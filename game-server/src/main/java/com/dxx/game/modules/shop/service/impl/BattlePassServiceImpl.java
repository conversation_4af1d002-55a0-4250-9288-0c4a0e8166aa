package com.dxx.game.modules.shop.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.gameconfig.ConfigEntity;
import com.dxx.game.config.entity.iap.BattlePassEntity;
import com.dxx.game.config.entity.iap.BattlePassRewardEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.ShopProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.reward.model.ResourceReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.shop.service.BattlePassService;
import com.dxx.game.modules.shop.support.ShopSupport;
import com.dxx.game.modules.user.service.UserService;
import com.google.api.client.util.Lists;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: lsc
 * @createDate: 2025/6/7
 * @description:
 */
@Service
public class BattlePassServiceImpl implements BattlePassService {

    @Resource
    private ShopDao shopDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RewardService rewardService;
    @Resource
    private ShopSupport shopSupport;
    @Resource
    private UserService userService;

    @DynamoDBTransactional
    @Override
    public Result<ShopProto.BattlePassGetInfoResponse> battlePassGetInfo(ShopProto.BattlePassGetInfoRequest params) {
        shopSupport.checkBattlePass();
        ShopProto.BattlePassGetInfoResponse.Builder response = ShopProto.BattlePassGetInfoResponse.newBuilder();
        response.setBattlePass(shopSupport.buildBattlePassDto());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopProto.BattlePassRewardResponse> battlePassReward(ShopProto.BattlePassRewardRequest params) {
        List<Integer> battlePassRewardIdList = params.getBattlePassRewardIdListList();
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        Shop.IAPBattlePassModel battlePassModel = iapModel.getBattlePassModel();
        if (battlePassModel.getId() <= 0) {
            return Result.Error(ErrorCode.BATTLE_PASS_NOT_OPEN);
        }
        BattlePassEntity battlePassEntity = gameConfigManager.getIAPConfig().getBattlePassEntity(battlePassModel.getId());
        int groupId = battlePassEntity.getGroupId();
        List<Integer> battlePassRewardId = battlePassModel.getBattlePassRewardId();
        List<Integer> freeRewardId = battlePassModel.getFreeRewardId();
        List<List<Integer>> rewardConfig = new ArrayList<>();
        for (int rewardId : battlePassRewardIdList) {
            // 判断是否同期
            BattlePassRewardEntity battlePassRewardEntity = gameConfigManager.getIAPConfig().getBattlePassRewardEntity(rewardId);
            if (battlePassRewardEntity.getGroupId() != groupId) {
                return Result.Error(ErrorCode.BATTLE_PASS_WRONG_GROUP);
            }
            // 判断类型
            if (battlePassRewardEntity.getType() != 1 && battlePassRewardEntity.getType() != 2) {
                return Result.Error(ErrorCode.BATTLE_PASS_REWARD_TYPE_WRONG);
            }
            // 判断是否已领取过
            if (freeRewardId.contains(rewardId)) {
                if (battlePassModel.getBuy() != 1) {
                    return Result.Error(ErrorCode.BATTLE_PASS_REWARD_GET);
                }
                if (battlePassRewardId.contains(rewardId)) {
                    return Result.Error(ErrorCode.BATTLE_PASS_REWARD_GET);
                }
            }
            // 判断积分是否足够
            if (battlePassRewardEntity.getScore() > battlePassModel.getScore()) {
                return Result.Error(ErrorCode.BATTLE_SCORE_NOT_ENOUGH);
            }
            // 封装奖励
            if (!freeRewardId.contains(rewardId)) {
                rewardConfig.addAll(battlePassRewardEntity.getFreeReward());
            }
            if (battlePassModel.getBuy() == 1 && !battlePassRewardId.contains(rewardId)) {
                rewardConfig.addAll(battlePassRewardEntity.getBattlePassReward());
            }
        }
//        rewardService.combineRewards(rewardConfig);
        // 先记录领取信息
        freeRewardId.addAll(battlePassRewardIdList);
        if (battlePassModel.getBuy() == 1) {
            battlePassRewardId.addAll(battlePassRewardIdList);
        }
        shopDao.updateIap(shop);
        // 后发放奖励
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        // 返回信息
        ShopProto.BattlePassRewardResponse.Builder builder = ShopProto.BattlePassRewardResponse.newBuilder();
        builder.setCommonData(commonData);
        builder.setBattlePassDto(shopSupport.buildBattlePassDto());
        return Result.Success(builder.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopProto.BattlePassChangeScoreResponse> battlePassChangeScore(ShopProto.BattlePassChangeScoreRequest params) {
        long now = DateUtils.getUnixTime();
        int addScore = params.getAddScore();
        ConfigEntity configEntity = gameConfigManager.getGameConfigConfig().getConfigEntity(802);
        int changeDiamond = (int) (addScore * Double.parseDouble(configEntity.getValue()));
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        Shop.IAPBattlePassModel battlePassModel = iapModel.getBattlePassModel();
        if (battlePassModel.getId() <= 0) {
            return Result.Error(ErrorCode.BATTLE_PASS_NOT_OPEN);
        }
        BattlePassEntity battlePassEntity = gameConfigManager.getIAPConfig().getBattlePassEntity(battlePassModel.getId());
        if (now < battlePassEntity.getBuyTime()) {
            return Result.Error(ErrorCode.BATTLE_PASS_NOT_BUY_TIME);
        }
        // 判断钻石是否足够
        User user = userService.getUser(userId);
        if (user.getDiamonds() < changeDiamond) {
            return Result.Error(ErrorCode.DIAMON_IS_NOT_ENOUGH);
        }
        // 判断积分是否已达上限
        int topScore = gameConfigManager.getBattlePassTopScoreMap().get(battlePassEntity.getGroupId());
        if (battlePassModel.getScore() >= topScore) {
            return Result.Error(ErrorCode.BATTLE_SCORE_TOP);
        }

        // 扣除钻石
        ResourceReward costDiamonds = ResourceReward.valueOf(RewardResourceType.DIAMONDS, -changeDiamond);
        RewardResultSet rewardResultSet = rewardService.executeReward(userId, costDiamonds);
        int score = battlePassModel.getScore();
        battlePassModel.setScore(score + addScore);
        shopDao.updateIap(shop);
        ShopProto.BattlePassChangeScoreResponse.Builder builder = ShopProto.BattlePassChangeScoreResponse.newBuilder();
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        builder.setCommonData(commonData);
        builder.setBattlePassDto(shopSupport.buildBattlePassDto());
        return Result.Success(builder.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopProto.BattlePassFinalRewardResponse> battlePassFinalReward(ShopProto.BattlePassFinalRewardRequest params) {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        Shop.IAPBattlePassModel battlePassModel = iap.getBattlePassModel();
        if (battlePassModel.getId() <= 0) {
            return Result.Error(ErrorCode.BATTLE_PASS_NOT_OPEN);
        }
        // 判断是否有剩余次数可以领取

        BattlePassEntity battlePassEntity = gameConfigManager.getIAPConfig().getBattlePassEntity(battlePassModel.getId());
        int topScore = gameConfigManager.getBattlePassTopScoreMap().get(battlePassEntity.getGroupId());
        BattlePassRewardEntity loopEntity = gameConfigManager.getBattlePassLoopRewardMap().get(battlePassEntity.getGroupId());
        int loopScore = loopEntity.getScore();
        int score = battlePassModel.getScore();
        if (score < topScore + loopScore) {
            return Result.Error(ErrorCode.BATTLE_SCORE_NOT_ENOUGH);
        }
        int loopCount = (score - topScore) / loopScore;
        int finalRewardTimes = battlePassEntity.getFinalRewardTimes();
        loopCount = loopCount < finalRewardTimes ? loopCount : finalRewardTimes;
        int finalRewardCount = battlePassModel.getFinalRewardCount();
        if (loopCount <= finalRewardCount) {
            return Result.Error(ErrorCode.BATTLE_SCORE_NOT_ENOUGH);
        }
        // 记录次数
        battlePassModel.setFinalRewardCount(finalRewardCount + 1);
        shopDao.updateIap(shop);
        // 发放奖励
        List<List<Integer>> rewardConfig = Lists.newArrayList();
        rewardConfig.addAll(loopEntity.getFreeReward());
        if (battlePassModel.getBuy() == 1) {
            rewardConfig.addAll(loopEntity.getBattlePassReward());
        }
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        ShopProto.BattlePassFinalRewardResponse.Builder builder = ShopProto.BattlePassFinalRewardResponse.newBuilder();
        builder.setCommonData(commonData);
        builder.setBattlePassDto(shopSupport.buildBattlePassDto());
        return Result.Success(builder.build());
    }
}

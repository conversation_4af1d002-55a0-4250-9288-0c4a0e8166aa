
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildStyle {
    public Guild_guildStyle(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Style = _buf.get("Style").getAsInt();
        AtlasID = _buf.get("AtlasID").getAsInt();
        Icon = _buf.get("Icon").getAsString();
    }

    public static Guild_guildStyle deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildStyle(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * style类型：1,2<br/>1：背景<br/>2：Icon
     */
    public final int Style;
    /**
     * 图标
     */
    public final int AtlasID;
    /**
     * 图标
     */
    public final String Icon;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Style:" + Style + ","
        + "AtlasID:" + AtlasID + ","
        + "Icon:" + Icon + ","
        + "}";
    }
}


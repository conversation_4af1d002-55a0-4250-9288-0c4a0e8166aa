package com.dxx.game.common.channel.apple.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.common.channel.apple.config.AppleConfig;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.modules.pay.service.RefundProcessingService;
import com.google.api.client.util.Base64;
import com.google.api.client.util.Charsets;
import com.google.common.primitives.Bytes;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.NoUniqueBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.ECPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/9 8:48 下午
 */
@Slf4j
@Service
public class AppleService {

    @Autowired
    private ChannelConfig channelConfig;
    @Resource
    private RefundProcessingService refundProcessingService;

    /**
     * 校验内购信息
     * @param urlIdx
     * @param retry
     * @param postData
     * @return
     */
    public Map<String, Object> checkOrderState(int urlIdx, boolean retry, Map<String, Object> postData,
                                               Set<String> whiteDeviceIds, String deviceId, long userId) {

        try {
            String url =  channelConfig.getAppleConfig().getPay().getVerifyReceiptURLs().get(urlIdx);
            String respData = OkHttpClientUtil.postJson(url, null, postData, String.class);
            Map<String, Object> respDataMap = JSON.parseObject(respData, new TypeReference<Map<String, Object>>(){});
            int status = (int) respDataMap.get("status");
            if (status == 21007) {
                log.info("ios pay request to sandBox");
                // 判断是否在白名单内
                if (StringUtils.isEmpty(deviceId) || !whiteDeviceIds.contains(deviceId)) {
                    log.error("iosPurchaseError deviceId not on whitelist, userId:{}, deviceId:{}",
                            userId, deviceId);
                    return null;
                }
                // 重新发送沙箱环境
                return checkOrderState(0, retry, postData, whiteDeviceIds, deviceId, userId);
            }

            return respDataMap;
        } catch (Exception e) {
            log.error("ios iosReceiptDataWitness retry, errorMsg:{}", e.getMessage());
            if (!retry) {
                retry = true;
                return checkOrderState(urlIdx, retry, postData, whiteDeviceIds, deviceId, userId);
            } else {
                return null;
            }
        }
    }

    /**
     * 验证账号信息
     * @param verification
     * @return
     */
    public boolean verifyLogin(String accountId, String verification) {
        AppleConfig appleConfig = channelConfig.getAppleConfig();
        if (!appleConfig.getAccount().isVerify()) {
            return true;
        }

        if (StringUtils.isEmpty(verification)) {
            log.error("verifyLogin[apple] failed, verification is empty");
            return false;
        }

        JSONObject jsonObject = JSON.parseObject(verification);
        if (!jsonObject.containsKey("verification")) {
            log.error("verifyLogin[apple] failed, verification is empty");
            return false;
        }

        try {
            JSONObject verifyObj = jsonObject.getJSONObject("verification");

            // 获取校验参数
            String teamPlayerId = jsonObject.getString("teamPlayerId");
            String playerId = jsonObject.getString("playerId");
            String bundleId = channelConfig.getAppleConfig().getAccount().getBundleId();
            long timestamp = verifyObj.getLongValue("timestamp");
            String salt = verifyObj.getString("salt");
            String sign = verifyObj.getString("signature");

            int methodTag = verifyObj.getIntValue("methodTag");
            if (methodTag == 2) {
                teamPlayerId = playerId;
            }

            if (DateUtils.getUnixTime() - timestamp >= appleConfig.getAccount().getSignatureTimeoutInSecond()) {
                // 时间戳超时
                log.error("verifyLogin[apple] failed, time out [{}] ", appleConfig.getAccount().getSignatureTimeoutInSecond());
                return false;
            }

            // 加载publicKey
            String publicKeyUrl = verifyObj.getString("publicKeyURL");
            InputStream inputStream = OkHttpClientUtil.getInputStream(publicKeyUrl);

            CertificateFactory cf = CertificateFactory.getInstance("x.509");
            X509Certificate cer = (X509Certificate) cf.generateCertificate(inputStream);
            cer.checkValidity();
            PublicKey publicKey = cer.getPublicKey();
            inputStream.close();

            byte[] content = this.concatContent(teamPlayerId, bundleId, timestamp, salt);
            byte[] signBytes = Base64.decodeBase64(sign);

            Signature signature = Signature.getInstance(cer.getSigAlgName());
            signature .initVerify(publicKey);
            signature .update(content);

            boolean flag = signature.verify(signBytes);
            if (!flag) {
                log.error("verifyLogin[apple] failed sign error");
                return false;
            } else if (!accountId.equals(playerId)) {
                log.error("verifyLogin[apple] accountId invalid accountId:{}, playerId:{}", accountId, playerId);
                return false;
            }

            return flag;

        } catch (Exception e) {
            log.error("verifyLogin error [apple]:", e);
            return true;
        }

    }

    private byte[] concatContent(String playerId, String bundleId, long timestamp, String salt)
            throws IOException {
        return Bytes.concat(playerId.getBytes(Charsets.UTF_8), bundleId.getBytes(Charsets.UTF_8), toBigEndian(timestamp) ,
                Base64.decodeBase64(salt));
    }

    private static byte[] toBigEndian(long value){
        byte[] buffer = new byte[8];
        for (int i = 0; i < 8; i++) {
            buffer[7 - i] = (byte)(value & 0xff);
            value = value >> 8;
        }
        return buffer;
    }

    /**
     * 根据订单号查询数据
     */
    public List<Map<String, String>> queryOrderById(String orderId) {

        List<Map<String, String>> result = new ArrayList<>();

        try {
            String token = this.makeApiToken();

            Map<String, String> headerParameter = new HashMap<>();
            headerParameter.put("Authorization", "Bearer " + token);
            String url = "https://api.storekit.itunes.apple.com/inApps/v1/lookup/" + orderId;
            String respData = OkHttpClientUtil.get(url, headerParameter, null, String.class);

            if (StringUtils.isEmpty(respData)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(respData);
            int status = jsonObject.getIntValue("status");
            if (status != 0) {
                // 订单号无效
                return null;
            }
            JSONArray jsonArray = jsonObject.getJSONArray("signedTransactions");
            for (int i = 0; i < jsonArray.size(); i ++) {
                String transactions = jsonArray.getString(i);
                SignedJWT signedJWT = SignedJWT.parse(transactions);
                JWTClaimsSet jwtClaimsSet = signedJWT.getJWTClaimsSet();
                Map<String, String> data = new HashMap<>();
                String productId = jwtClaimsSet.getStringClaim("productId");
                String transactionId = jwtClaimsSet.getStringClaim("transactionId");
                data.put("productId", productId);
                data.put("transactionId", transactionId);

                result.add(data);
            }
            return result;

        } catch (Exception e) {
            return result;
        }
    }


    /**
     * 接收通知
     * @param params
     * @return
     */
    public Object notification(JSONObject params) {
        String body = params.getString("signedPayload");
        if (StringUtils.isEmpty(body)) {
            return null;
        }
        log.info("notification params:{}", params);
        String signedPayload = new String(Base64.decodeBase64(body.split("\\.")[0]));
        JSONObject jsonObject = JSONObject.parseObject(signedPayload);

        Jws<Claims> payLoadObj = this.verifyJWT(jsonObject.getJSONArray("x5c").get(0).toString(), body);
        if (payLoadObj == null) {
            log.error("notification verifyJWT(signedPayload) is null, body:{}", body);
            return null;
        }
//        log.info("notification payLoadObj:{}", payLoadObj);

        Claims payLoadMap = payLoadObj.getBody();
        String notificationType = payLoadMap.get("notificationType").toString();

        if (notificationType.equals("TEST")) {
            return new HashMap<>();
        }

        Map<String,Object> dataMap = payLoadMap.get("data",HashMap.class);
        String env = dataMap.get("environment").toString();

        String signedTransactionInfo = new String(com.google.api.client.util.Base64.decodeBase64(dataMap.get("signedTransactionInfo").toString().split("\\.")[0]));
        JSONObject signedTransactionInfoJson = JSONObject.parseObject(signedTransactionInfo);

        Jws<Claims> signedTransactionInfoObj = this.verifyJWT(signedTransactionInfoJson.getJSONArray("x5c").get(0).toString(), dataMap.get("signedTransactionInfo").toString());
        if (signedTransactionInfoObj == null) {
            log.error("notification verifyJWT(signedTransactionInfo) is null, signedTransactionInfo:{}", signedTransactionInfo);
            return null;
        }

        String transactionId = signedTransactionInfoObj.getBody().get("transactionId").toString();
        String productId = signedTransactionInfoObj.getBody().get("productId").toString();
        String originalTransactionId = signedTransactionInfoObj.getBody().get("originalTransactionId").toString();
        if (notificationType.equals("REFUND")) {
            // 表示 App Store 已成功退还消耗性应用内购买、非消耗性应用内购买、自动续订订阅或非续订订阅的交易。
            log.info("notification type = REFUND, transactionId:{}, originalTransactionId:{}, productId:{}", transactionId, originalTransactionId, productId);
            refundProcessingService.revokeItemsAfterRefund(transactionId);
        } else if (notificationType.equals("CONSUMPTION_REQUEST")) {
            // 表示客户针对应用内购买的消耗品发起了退款请求，并且 App Store 正在要求您提供消耗数据。有关详细信息
            log.info("notification type = CONSUMPTION_REQUEST, transactionId:{}, originalTransactionId:{}, productId:{}", transactionId, originalTransactionId, productId);
            this.consumptionRequest(originalTransactionId, env);
        }

        return new HashMap<>();
    }

    private void consumptionRequest(String originalTransactionId, String env) {
        try {

            String token = this.makeApiToken();
            Map<String, String> headerParameter = new HashMap<>();
            headerParameter.put("Authorization", "Bearer " + token);
            headerParameter.put("Content-Type", "application/json");

            // https://developer.apple.com/documentation/appstoreserverapi/consumptionrequest?changes=_7
            Map<String, Object> postData = new HashMap<>();
            postData.put("accountTenure", "1");     // 注册天数
            postData.put("appAccountToken", "");    // apple下单可以传uuid
            postData.put("consumptionStatus", 0);  // 消耗状态 0：不确定；1：没消耗；2：部分消耗；3：完全消耗
            postData.put("customerConsented", true); // 用户是否同意提供消费数据 true：同意
            postData.put("deliveryStatus", 0);      // 是否完成购买
            postData.put("lifetimeDollarsPurchased", 0);     // 总充值金额
            postData.put("lifetimeDollarsRefunded", 0);      // 所有退款的总金额
            postData.put("platform", 1);                     // 1苹果平台
            postData.put("playTime", 0);
            postData.put("sampleContentProvided", false);       // 购买内容的免费样品或免费试用
            postData.put("userStatus", 1);

            String url = "https://api.storekit.itunes.apple.com/inApps/v1/transactions/consumption/" + originalTransactionId;
            if (env.equals("Sandbox")) {
                url = "https://api.storekit-sandbox.itunes.apple.com/inApps/v1/transactions/consumption/" + originalTransactionId;
            }



            OkHttpClientUtil.putJson(url, headerParameter, postData, new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("consumption failed, e:{}", e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (response.code() != 202) {
                        log.error("consumption failed, code:{}, msg:{}", response.code(), response.body().string());
                    }
                }
            });
        } catch (Exception e) {
        }
    }

    private Jws<Claims> verifyJWT(String x5c, String jws) {
        try {
            X509Certificate cert = this.getCert(x5c);
            if (!cert.getSubjectDN().getName().contains("Apple Inc")){
                log.error("not apple cert . name = {}", cert.getIssuerX500Principal().getName());
                return null;
            }

            return Jwts.parserBuilder().setSigningKey(cert.getPublicKey()).build().parseClaimsJws(jws);
        } catch (Exception e) {
            log.error("jws verify error, e", e);
            return null;
        }
    }

    private X509Certificate getCert(String x5c) throws CertificateException {
        String stripped = x5c.replaceAll("-----BEGIN (.*)-----", "");
        stripped = stripped.replaceAll("-----END (.*)----", "");
        stripped = stripped.replaceAll("\r\n", "");
        stripped = stripped.replaceAll("\n", "");
        stripped.trim();
        byte[] keyBytes = Base64.decodeBase64(stripped);
        CertificateFactory fact = CertificateFactory.getInstance("X.509");
        return (X509Certificate) fact.generateCertificate(new ByteArrayInputStream(keyBytes));
    }

    private String makeApiToken() throws Exception {

        AppleConfig appleConfig = channelConfig.getAppleConfig();
        List<Map<String, String>> result = new ArrayList<>();

        JWSHeader.Builder jwsHeaderBuilder = new JWSHeader.Builder(JWSAlgorithm.ES256);
        jwsHeaderBuilder.keyID(appleConfig.getOrder().getKid());
        jwsHeaderBuilder.type(JOSEObjectType.JWT);
        JWSHeader jwsHeader = jwsHeaderBuilder.build();

        Map<String, Object> payLoad = new HashMap<>();
        payLoad.put("iss", appleConfig.getOrder().getIss());
        payLoad.put("iat", DateUtils.getUnixTime());
        payLoad.put("exp", DateUtils.getUnixTime() + 1800);
        payLoad.put("aud", "appstoreconnect-v1");
        payLoad.put("nonce", UUID.randomUUID().toString());
        payLoad.put("bid", channelConfig.getAppleConfig().getPackageName());

        String keyStr = appleConfig.getOrder().getPrivateKey();
        byte[] p8der = keyStr.getBytes(StandardCharsets.UTF_8);
        PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(new org.apache.commons.codec.binary.Base64().decode(p8der));
        PrivateKey appleKey = KeyFactory.getInstance("EC").generatePrivate(priPKCS8);

        JWSSigner signer = new ECDSASigner((ECPrivateKey) appleKey);
        JWSObject jwsObject = new JWSObject(jwsHeader, new Payload(payLoad));
        jwsObject.sign(signer);

        return jwsObject.serialize();
    }

    public void testNotification() {
        try {
            System.out.println("sfasdfsdf");
            String token = this.makeApiToken();
            Map<String, String> headerParameter = new HashMap<>();
            headerParameter.put("Authorization", "Bearer " + token);
            String url = "https://api.storekit-sandbox.itunes.apple.com/inApps/v1/notifications/test";
//            OkHttpClientUtil.postJson(url, String.class);
            String respData = OkHttpClientUtil.postJson(url, headerParameter, null, String.class);
//            System.out.println(respData);
        } catch (Exception e) {

        }
    }
}

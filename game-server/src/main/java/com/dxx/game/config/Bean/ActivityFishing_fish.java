
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFishing_fish {
    public ActivityFishing_fish(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        nameId = _buf.get("nameId").getAsString();
        atlas = _buf.get("atlas").getAsInt();
        icon = _buf.get("icon").getAsString();
        fishType = _buf.get("fishType").getAsInt();
        type = _buf.get("type").getAsInt();
        quality = _buf.get("quality").getAsInt();
        number = _buf.get("number").getAsInt();
        weight = _buf.get("weight").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("weightFloat").getAsJsonArray(); weightFloat = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  weightFloat.add(_v0); }   }
        initialDamage = _buf.get("initialDamage").getAsInt();
        strength = _buf.get("strength").getAsInt();
        speed = _buf.get("speed").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("actionList").getAsJsonArray(); actionList = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  actionList.add(_v0); }   }
        attributes = _buf.get("attributes").getAsString();
        skillBuild = _buf.get("skillBuild").getAsInt();
    }

    public static ActivityFishing_fish deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFishing_fish(_buf);
    }

    /**
     * ID
     */
    public final int id;
    /**
     * 名称id
     */
    public final String nameId;
    /**
     * 图集
     */
    public final int atlas;
    /**
     * icon
     */
    public final String icon;
    /**
     * 鱼群id
     */
    public final int fishType;
    /**
     * 1-普通<br/>2-稀有<br/>3-鱼王
     */
    public final int type;
    /**
     * 品质
     */
    public final int quality;
    /**
     * 对应区域里有多少条
     */
    public final int number;
    /**
     * 重量基准（g)
     */
    public final int weight;
    /**
     * 重量浮动百分比（除100）
     */
    public final java.util.List<Integer> weightFloat;
    /**
     * 初始张力
     */
    public final int initialDamage;
    /**
     * 力量（*100）
     */
    public final int strength;
    /**
     * 速度（*100）
     */
    public final int speed;
    /**
     * 动作库
     */
    public final java.util.List<Integer> actionList;
    /**
     * 属性加成
     */
    public final String attributes;
    /**
     * 获得技能
     */
    public final int skillBuild;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "nameId:" + nameId + ","
        + "atlas:" + atlas + ","
        + "icon:" + icon + ","
        + "fishType:" + fishType + ","
        + "type:" + type + ","
        + "quality:" + quality + ","
        + "number:" + number + ","
        + "weight:" + weight + ","
        + "weightFloat:" + weightFloat + ","
        + "initialDamage:" + initialDamage + ","
        + "strength:" + strength + ","
        + "speed:" + speed + ","
        + "actionList:" + actionList + ","
        + "attributes:" + attributes + ","
        + "skillBuild:" + skillBuild + ","
        + "}";
    }
}


package com.dxx.game.consts;

public interface ErrorCode {
	
	int SUCCESS = 0;	// 正常返回结果
	int FAIL = 1;    // 失败

	/////////////////////////////////////////// 正数表示数据层逻辑状态 //////////////////////////////////////////////
	/** 用户 **/
	int REQUEST_DATA_DUPLICATE = 100;		// 请求数据重复(transId 冲突)
	int APP_VERSION_TOO_LOW = 101;			// APP版本号过低
	int USER_LOGIN_FAILED	= 102; 			// 登录失败
	int PARAMS_ERROR = 103;					// 参数不合法
	int USER_ACCESS_TOKEN_ERROR = 104;		// 用户令牌校验失败
	int USER_LONGIN_REPEAT = 105;			// 已在其他设备登录
	int REQUEST_METHOD_NOT_FOUND = 106; 	// 请求方法未找到
	int COIN_IS_NOT_ENOUGH = 107;			// 金币不足
	int DIAMON_IS_NOT_ENOUGH = 108;			// 钻石不足
	int EQUIP_IS_NOT_ENOUGH = 109;			// 装备数量不足
	int ITEM_IS_NOT_ENOUGH = 110;			// 道具数量不足
	int BOX_KEY_IS_NOT_ENOUGH = 111;		// 宝箱钥匙数量不足
	int SERVER_STOP = 112;					// 停服中
	int USER_BE_SEALED = 113;				// 已被限制登录-封号
	int REWARD_RECEIVED = 114;				// 奖励已领取
	int USER_LEVEL_NOT_ENOUGH = 115;	    // 等级不足
	int EQUIP_IS_NOT_FOUND = 116;			// 装备不存在
	int BATTLE_CHEAT = 117;					// 结算奖励不合法疑似作弊
	int COUNT_NOT_ENOUGH = 118;				// 次数不足
	int VERIFY_LOGIN_FAILED = 120;			// 登录信息验证失败
	int BATTLE_CHEAT_DATA = 121;			// 战斗数据校验不通过-作弊
	int USER_NICKNAME_HAS_EN = 122;         // 昵称包含英文字符
	int USER_NICKNAME_HAS_FAN = 123;        // 昵称包含繁体
	int USER_NICKNAME_HAS_LEGAL = 124;      // 昵称包含非法字符
	int USER_NICKNAME_LEN_ERROR = 125;		// 昵称长度不合法
	int USER_NICKNAME_NOT_LEGAL = 126;		// 昵称不合法有屏蔽词
	int USER_NICKNAME_REPEATED = 127;		// 昵称重复
	int USER_AVATAR_NOT_EXIST = 128;		// 头像不存在
	int USER_AVATAR_FRAME_NOT_EXIST = 129;	// 头像框不存在
	int USER_CHECK_NICKNAME_ERROR = 130;	// 昵称检测异常
	int CHAT_CONTENT_CHECK_ERROR = 131;		// 聊天内容检测异常
	int LIFE_IS_NOT_ENOUGH = 132;			// 体力不足
	int TICKET_IS_NOT_ENOUGH = 133;			// 门票不足
	int COUNT_GT_ZERO = 134;					// 消耗小于0
	int LOCK_OCCUPY = 135;						// 锁占用
	int LOGIN_LANGUAGE_IS_NULL = 136;			// 语言标识为空
	int VITALITY_IS_NOT_ENOUGH = 137;			// 体力不足


	/** 英雄 */
	int HERO_NOT_EXIST = 200;                // 英雄不存在
	int HERO_LEVEL_MAX = 201;                // 英雄已升到最大等级
	int HERO_QUALITY_MAX = 202;             // 英雄已到最高品质
	int HERO_LEVEL_LIMIT_TOP = 203;          // 英雄等级已达共享等级允许最大值
	int HERO_ADVANCE_MAX = 204;              // 英雄已升到最大等级
	int HERO_STAR_MAX = 205;                 // 英雄已升到最大星级
	int HERO_COND_NOT_ENOUGH = 206;          // 条件不足
	int HERO_BOOK_SCORE_NOT_ENOUGH = 207;    // 图鉴积分不足
	int HERO_SYS_EQUIP_MUST_ADVANCE = 208;   // 未升阶
	int HERO_SYS_EQUIP_ADVANCE_REPEAT = 209; // 重复升阶
	int HERO_EXIST = 210;                    // 英雄已存在

	/** 装备 */
	int EQUIP_LEVEL_MAX = 300;               // 装备星级已达上限
	int EQUIP_ROW_ID_REPEATED = 301;         // 装备RowID重复
	int EQUIP_IS_WEAR = 302;                 // 装备已穿戴

	/** 商店 */
	int INTEGRAL_SHOP_CANT_INIT = 400;       // 未初始化
	int INTEGRAL_SHOP_GOODS_NOT_EXIST = 401; // 商品不存在
	int INTEGRAL_SHOP_REFRESH_NUM_MAX = 402; // 刷新次数已达上限
	int INTEGRAL_SHOP_GOODS_BUY_COUNT = 403; // 超过购买次数

	/** 活动 */
	int ACTIVITY_SIGN_IN_IS_DONE = 500;      // 今日已签到
	int ACTIVITY_NOT_OPEN = 501;             // 活动未开放
	int ACTIVITY_BUY_LIMIT = 502;            // 超过购买次数
	int ACTIVITY_COND_NOT_ENOUGH = 503;      // 条件不足

	/** 直购 */
	int PURCHASE_PRODUCTID_NULL = 600;       // 未找到产品
	int PURCHASE_COUNT_FULL = 601;           // 物品购买次数已用尽
	int BATTLE_PASS_NOT_OPEN = 602;          // 通行证没有开启
	int BATTLE_PASS_WRONG_GROUP = 603;       // 通行证奖励组错误
	int BATTLE_PASS_REWARD_GET = 604;        // 通行证奖励已领取
	int BATTLE_SCORE_NOT_ENOUGH = 605;       // 通行证积分不足
	int BATTLE_SCORE_TOP = 606;              // 通行证积分到顶
	int BATTLE_PASS_REWARD_TYPE_WRONG = 607; // 通行证奖励类型错误
	int VIP_NO_REWARD = 608;                 // 没有VIP等级礼包可以领取
	int LEVELFUND_NOT_BUY = 609;             // 基金未购买
	int LEVELFUND_REWARDED = 610;            // 基金奖励已领取
	int LEVELFUND_TYPE_WRONG = 611;          // 基金类型错误
	int LEVELFUND_NOT_FINISH = 612;          // 基金条件未完成
	int FIRSTREWARD_NOT_RECHARGE = 613;      // 首充礼包不可领取
	int FIRSTREWARD_REWARDED = 614;          // 首充礼包已领取
	int BATTLE_PASS_NOT_BUY_TIME = 615;      // 通行证积分未到时间
	int GACHA_COUNT_MAX = 616;               // 抽卡上限
	int FIRST_PAY_GIFT_TIME_PASS = 617;      // 首冲礼包重复购买

	/** 内购 */
	int IAP_VERIFY_SERVER_UNAVAILABLE = 700; // 与IAP服务器进行通信失败
	int IAP_VERIFY_PRODUCT_ID_INVALID = 701; // ProductId和校验结果的ProductId不一致
	int IAP_VERIFY_FAILED = 702;             // IAP校验失败
	int IAP_ORDER_EXIST = 703;               // 订单重复
	int IAP_MONTHCARD_NOT_BUY = 704;         // 月卡未购买
	int IAP_MONTHCARD_REWARD_TODAY = 705;    // 今日月卡已领取
	int IAP_PURCHASE_COUNT_FULL = 706;       // 超出限购次数
	int IAP_UNIFIED_ORDER_FAILED = 707;      // 内购统一下单失败

	/** 钓鱼 */
	int FISHING_NOT_CAST_ROD = 800;          // 未抛竿

	/** 翻牌子 */
	int FLIP_GRID_UN_SHOW = 900;             // 格子未展示
	int FLIP_SHOW_GRID_TYPE_ERROR = 901;     // 展示格子类型错误
	int FLIP_REWARD_GRID_TYPE_ERROR = 902;   // 奖励格子类型错误
	int FLIP_CLUE_GRID_TYPE_ERROR = 903;     // 线索格子类型错误
	int FLIP_BOMB_GRID_TYPE_ERROR = 904;     // 炸弹格子类型错误

	/** 任务 */
	int TASK_ALREADY_RECEIVE = 1000;         // 任务奖励已领取
	int TASK_NOT_FINISH = 1001;              // 任务未完成
	int TASK_NOT_FOUND = 1002;               // 任务未找到
	int TASK_ACTIVE_NOT_ENOUGH = 1003;       // 任务活跃度不足

	/** 潜水 */
	int DIVE_INDEX_ERROR = 1100;             // 潜水坐标不合法
	int DIVE_GRID_TYPE_ERROR = 1101;         // 潜水格子类型不合法
	int DIVE_GRID_UN_LIGHT = 1102;           // 潜水格子未点亮

	// ----------------------------- 公会错误码 ----------------------------------- //
	int GUILD_JOINED = 300001;						// 公会-已加入公会
	int GUILD_NAME_LENGTH_ERROR = 300002;			// 公会-名称长度错误
	int GUILD_INFO_LENGTH_ERROR = 300003;			// 公会-简介长度错误
	int GUILD_NAME_REPEAT = 300004;	        		// 公会-名称重复
	int GUILD_NAME_ILLEGAL = 300005;				// 公会-名称不合法(包含敏感词)
	int GUILD_INFO_ILLEGAL = 300006;	            // 公会-简介不合法(包含敏感词)
	int GUILD_NOT_EXIST = 300007;					// 公会-不存在
	int GUILD_POSITION_ERROR = 300008;				// 公会-职位权限不足
	int GUILD_MAX_MEMBER_ERROR = 300009;			// 公会-人数已满
	int GUILD_APPLY_CONDITION_ERROR = 300010;		// 公会-申请加入条件未满足
	int GUILD_USER_APPLY_COUNT_MAX = 300011;		// 公会-用户公会申请数量已达上限
	int GUILD_APPLIED = 300012;						// 公会-此公会已申请
	int GUILD_APPLY_COUNT_MAX = 300013;				// 公会-公会申请人数已大上限
	int GUILD_USER_JOIN_OTHER = 300014;				// 公会-玩家已加入其他公会
	int GUILD_HAS_BEEN_DISSOLVED = 300015;			// 公会-已解散
	int GUILD_LANGUAGE_ERROR = 300016;				// 公会-语言条件未达到
	int GUILD_TRANSFER_PRESIDENT_FIRST = 300017;	// 公会-先转让会长(会长请求离开接口时用)
	int GUILD_POSITION_COUNT_ERROR = 300018;	    // 公会-副会长职位上限
	int GUILD_NOTICE_LENGTH_ERROR = 300019;	        // 公会-公告长度错误
	int GUILD_NOTICE_ILLEGAL = 300020;				// 公会-公告有敏感词
	int GUILD_POSITION_MANAGER_COUNT_ERROR = 300021;	// 公会-管理员职位上限
	int GUILD_AUTO_JOIN_NOT_FOUND = 300022;	            // 公会-暂无可以自动加入的公会
	int GUILD_JOIN_FIRST = 300023;	                    // 公会-请先加入
	int GUILD_DONATION_NOT_EXIST = 300024;				// 捐赠数据不存在
	int GUILD_DONATION_EXPIRED = 300025;				// 捐赠数据已过期
	int GUILD_DONATED = 300026;							// 已捐赠
	int GUILD_DONATION_ITEM_MAX = 300027;				// 捐赠道具已满
	int GUILD_DONATION_COUNT_NOT_ENOUGH = 300028;		// 捐赠道具次数不足
	int GUILD_USER_NOT_EXIST = 300029;					// 工会玩家不存在

	// ----------------------------- IM-错误码 ----------------------------------- //
	int IM_PARAMS_ERROR = 310001;					    // 参数不合法
	int IM_USER_NOT_LOGIN = 310002;                     // 用户未登录

	int IM_CHAT_CONTENT_LENGTH_ERROR = 320001;					// 聊天-内容过长
	int IM_CHAT_GUILD_NOT_JOIN = 320002;						// 聊天-未加入公会
	int IM_CHAT_FREQUENCY_TOO_FAST = 320003;                   // 聊天-频率过快
	int IM_CHAT_BANNED = 320004;								// 已被禁言
	int IM_CHAT_IN_BLACK_LIST = 320005;						// 在黑名单中
	int IM_CHAT_CHAPTER_LIMIT = 320006;						// 聊天章节限制不满足
	int IM_CHAT_NOT_OPEN = 320007;								// 聊天未开启
	int IM_CHAT_BLACK_LIST_ERROR = 320008;                     // 黑名单超长
	int IM_CHAT_CONTENT_CHECK_ERROR = 320009;					// 聊天长度不合法

	//////////////////////////////////////////// 负数表示通信或内部逻辑错误 //////////////////////////////////////////////
	int FORMAT_ERROR = -1;				// 通信二进制格式错误
	int BUILD_REQUEST_FAILED = -2;		// 构造request参数失败
	int SERVER_SYSTEM_ERROR = -3;		// 服务器系统异常
	int BUILD_RESPONSE_FAILED = -4; 	// 构造响应信息失败
	int QUERY_USER_DATA_ERROR = -5;		// 用户数据查询失败
	int EXEC_REWARD_ERROR = -6;			// 调用发奖励方法异常
	int MSG_INTERNAL_ARG_FAILED = -7;	// 内部参数错误
	int DELETE_DATA_FAILED = -8;		// 删除数据失败
	int INSERT_DATA_FAILED = -9;		// 插入数据失败
	int UPDATE_DATA_FAILED = -10;		// 更新数据失败
	int CONFIG_NOT_EXIST = -11;			// 配置表错误，配置项不存在
	int SYSTEM_MAINTAIN = -13;			// 系统维护中请稍后重试
	int OPERATION_IS_TOO_FREQUENT = -14; // 操作过于频繁
	int DYNAMODB_ERROR = -15;			 // dynamodb 数据库异常
}

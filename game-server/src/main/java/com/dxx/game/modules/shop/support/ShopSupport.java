package com.dxx.game.modules.shop.support;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.iap.BattlePassEntity;
import com.dxx.game.config.entity.iap.BattlePassRewardEntity;
import com.dxx.game.config.entity.iap.GiftPacksEntity;
import com.dxx.game.config.entity.iap.MonthCardEntity;
import com.dxx.game.config.entity.integralshop.GoodsEntity;
import com.dxx.game.consts.MailSourceType;
import com.dxx.game.consts.VipPermission;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.ShopProto;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.user.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @author: lsc
 * @createDate: 2025/6/7
 * @description:
 */
@Slf4j
@Component
public class ShopSupport {

    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private ShopDao shopDao;
    @Resource
    private UserService userService;
    @Resource
    private MailService mailService;


    public CommonProto.IAPDto buildUserIAPInfo() {
        long now = DateUtils.getUnixTime();
        // 获取月卡信息
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        Map<Integer, Shop.IAPMonthCardModel> monthCardMap = iap.getMonthCardMap();
        Map<Integer, CommonProto.IAPMonthCardDto> monthCardDtoMap = new HashMap<>();
        for (Map.Entry<Integer, Shop.IAPMonthCardModel> entry : monthCardMap.entrySet()) {
            int monthCardId = entry.getKey();
            Shop.IAPMonthCardModel value = entry.getValue();
            CommonProto.IAPMonthCardDto.Builder monthCardBuilder = CommonProto.IAPMonthCardDto.newBuilder();
            monthCardBuilder.setConfigId(monthCardId);
            monthCardBuilder.setLastCount(value.getLastCount());
            monthCardBuilder.setLastRewardTime(value.getLastRewardTime());
            monthCardBuilder.setCanReward(DateUtils.isSameDay(now, value.getRealLastRewardTime()) || value.getLastCount() <= 0 ? 0 : 1);
            monthCardBuilder.setNextRewardTime(DateUtils.getSystemResetTimeByTime(now));
            monthCardDtoMap.put(monthCardId, monthCardBuilder.build());
        }
        // 获取通行证信息
        CommonProto.IAPBattlePassDto battlePassDtoBuilder = buildBattlePassDto();

        // 获取基金信息

        int totalRecharge = iap.getTotalRecharge();
        int firstRechargeReward = iap.getFirstRechargeReward();

        CommonProto.IAPDto.Builder builder = CommonProto.IAPDto.newBuilder();
        builder.setGiftPack(this.buildIAPGiftPackDto(shop));
        builder.putAllMonthCardMap(monthCardDtoMap);
        builder.setBattlePassInfo(battlePassDtoBuilder);
        builder.setLevelFund(this.buildLevelFund(shop));
        builder.setTotalRecharge(totalRecharge);
        builder.setFirstRechargeReward(firstRechargeReward == 1);
        builder.putAllBuyOpenServerGiftId(iap.getOpenServerGiftTime());
        builder.putAllChapterGiftTime(iap.getChapterGiftTime());
        return builder.build();
    }

    public void checkDataWhenShopGetInfo(long userId) {
        Shop shop = shopDao.getByUserId(userId);
        this.checkIntegralShop(shop);
        this.checkBattlePass();
        this.checkMonthCard();
    }


    //////////////////////////////// 积分商城 ///////////////////////////////
    public void checkIntegralShop(Shop shop) {
        long time = DateUtils.getUnixTime();

        boolean isUpdate = false;
        Map<Integer, Shop.IntegralShopModel> integralShopModels = shop.getIntegralShops();
        if (integralShopModels == null) {
            integralShopModels = new HashMap<>();
            shop.setIntegralShops(integralShopModels);
            isUpdate = true;
        }

        int chapterId = userInfoRedisDao.getChapterId(shop.getUserId());
        // 判断是否解锁初始化
        for (Integer shopId : gameConfigManager.getShopGoodMap().keySet()) {

            // 商店入口判断
            int requireChapterId = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getLevelRequirements();
            if (integralShopModels.get(shopId) == null && chapterId >= requireChapterId) {
                integralShopModels.put(shopId, createIntegralShopModel(shopId, chapterId));
                continue;
            }

            // 跨天判断
            if (integralShopModels.get(shopId) != null && !DateUtils.isSameDay(integralShopModels.get(shopId).getRefreshTime(), time)) {
                integralShopModels.put(shopId, createIntegralShopModel(shopId, chapterId));
                isUpdate = true;
            }
        }

        if (isUpdate) {
            shop.setIntegralShops(integralShopModels);
            shopDao.updateIntegralShop(shop);
        }
    }

    private Shop.IntegralShopModel createIntegralShopModel(int shopId, int chapterId) {
        List<Integer> list = getIntegralShopGoods(shopId, chapterId).stream().map(GoodsEntity::getID).collect(Collectors.toList());

        Shop.IntegralShopModel integralShopModel = new Shop.IntegralShopModel();
        integralShopModel.setShopId(shopId);
        integralShopModel.setBuyList(new ArrayList<>());
        integralShopModel.setGoodsList(list);
        integralShopModel.setRefreshTime(DateUtils.getUnixTime());
        integralShopModel.setRefreshNum(0);

        List<Integer> refreshCost = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getRefreshCost();

        int rNum = refreshCost == null ? 0 : refreshCost.size();
        int add = (int) userService.vipPermissionValueCheck(RequestContext.getUserId(), VipPermission.PERMISSION_15);
        integralShopModel.setMaxNum(rNum + add);

        return integralShopModel;
    }

    public List<GoodsEntity> getIntegralShopGoods(int shopId, int chapterId) {
        List<GoodsEntity> shopGoodList = new ArrayList<>();

        Map<Integer, List<GoodsEntity>> groupGoodList = new HashMap<>();
        gameConfigManager.getShopGoodMap().get(shopId).forEach((goodId, entity) -> {
            groupGoodList.computeIfAbsent(entity.getGroupId(), k -> new ArrayList<>()).add(entity);
        });

        groupGoodList.forEach((groupId, list) -> {
            List<Integer> weights = new ArrayList<>();
            list.forEach(goodsEntity -> {
                weights.add(goodsEntity.getWeightInGroup());
            });

            int index = RandomUtil.randomProbIndexWithSeed(new Random(), weights);
            GoodsEntity entity = list.get(index);

            if (chapterId >= entity.getLevelRequirements()) {
                shopGoodList.add(entity);
            }
        });

        return shopGoodList;
    }

    public ShopProto.IntegralShopDto buildIntegralShopDto(Shop.IntegralShopModel model) {
        ShopProto.IntegralShopDto.Builder builder = ShopProto.IntegralShopDto.newBuilder();
        builder.setShopConfigId(model.getShopId());
        builder.addAllGoodsConfigId(model.getGoodsList());
        builder.addAllBuyConfigId(model.getBuyList());
        builder.setRound(model.getRefreshNum());
        builder.setMaxRound(model.getMaxNum());
        builder.setResetTimestamp(DateUtils.getSystemResetTime());
        return builder.build();
    }

    ///////////////////////////// battlepass //////////////////////////
    public void checkBattlePass() {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        Shop.IAPBattlePassModel battlePassModel = iapModel.getBattlePassModel();

        ConcurrentSkipListMap<Integer, BattlePassEntity> battlePass = gameConfigManager.getIAPConfig().getBattlePass();
        BattlePassEntity battlePassEntity = null;
        long now = DateUtils.getUnixTime();
        boolean isUpdate = false;
        for (BattlePassEntity entity : battlePass.values()) {
            if (entity.getStartTime() > now || entity.getEndTime() < now) {
                continue;
            }
            battlePassEntity = entity;
        }
        if (battlePassEntity == null) {
            if (battlePassModel.getId() > 0) {
                backBattlePassReward(userId, battlePassModel);

                battlePassModel.setId(0);
                battlePassModel.setBuy(0);
                battlePassModel.setScore(0);
                battlePassModel.getBattlePassRewardId().clear();
                battlePassModel.getFreeRewardId().clear();
                battlePassModel.setFinalRewardCount(0);

                isUpdate = true;
            }
        } else {
            if (battlePassEntity.getId() != battlePassModel.getId()) {
                if (battlePassModel.getId() > 0) {
                    backBattlePassReward(userId, battlePassModel);
                }

                battlePassModel.setId(battlePassEntity.getId());
                battlePassModel.setBuy(0);
                battlePassModel.setScore(0);
                battlePassModel.getBattlePassRewardId().clear();
                battlePassModel.getFreeRewardId().clear();
                battlePassModel.setFinalRewardCount(0);

                isUpdate = true;
            }
        }
        if (isUpdate) {
            shopDao.updateIap(shop);
        }
    }

    private void backBattlePassReward(long userId, Shop.IAPBattlePassModel battlePassModel) {
        BattlePassEntity battlePassEntity = gameConfigManager.getIAPConfig().getBattlePassEntity(battlePassModel.getId());
        int groupId = battlePassEntity.getGroupId();

        List<BattlePassRewardEntity> battlePassRewardEntityList = gameConfigManager.getIAPConfig().getBattlePassReward().values().stream().filter(v -> v.getGroupId() == groupId).filter(v -> v.getType() != 3).collect(Collectors.toList());

        List<Integer> battlePassRewardId = battlePassModel.getBattlePassRewardId();
        List<Integer> freeRewardId = battlePassModel.getFreeRewardId();

        List<List<Integer>> rewardConfig = new ArrayList<>();
        for (BattlePassRewardEntity rewardEntity : battlePassRewardEntityList) {
            // 判断积分是否足够
            if (rewardEntity.getScore() > battlePassModel.getScore()) {
                break;
            }

            // 封装奖励
            if (!freeRewardId.contains(rewardEntity.getId())) {
                rewardConfig.addAll(rewardEntity.getFreeReward());
            }
            if (battlePassModel.getBuy() == 1 && !battlePassRewardId.contains(rewardEntity.getId())) {
                rewardConfig.addAll(rewardEntity.getBattlePassReward());
            }
        }

        // 最终奖励
        int topScore = gameConfigManager.getBattlePassTopScoreMap().get(groupId);
        BattlePassRewardEntity loopEntity = gameConfigManager.getBattlePassLoopRewardMap().get(groupId);
        int loopScore = loopEntity.getScore();
        int score = battlePassModel.getScore();
        if (score >= topScore + loopScore) {
            int loopCount = (score - topScore) / loopScore;
            int finalRewardTimes = battlePassEntity.getFinalRewardTimes();
            loopCount = Math.min(loopCount, finalRewardTimes);
            int finalRewardCount = battlePassModel.getFinalRewardCount();
            if (loopCount > finalRewardCount) {
                int count = loopCount - finalRewardCount;

                for (int i = 0; i < count; i++) {
                    rewardConfig.addAll(loopEntity.getFreeReward());
                    if (battlePassModel.getBuy() == 1) {
                        rewardConfig.addAll(loopEntity.getBattlePassReward());
                    }
                }
            }
        }

        if (!rewardConfig.isEmpty()) {
            String tid = gameConfigManager.getMailTempId(4501, 4506);
            String mailUniqueId = "battle_pass_" + battlePassEntity.getSeasonID();
            mailService.createMail(userId, tid, mailUniqueId, rewardConfig, MailSourceType.BATTLE_PASS, battlePassEntity.getId());
        }
    }

    public CommonProto.IAPBattlePassDto buildBattlePassDto() {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();

        Shop.IAPBattlePassModel battlePassModel = iap.getBattlePassModel();
        CommonProto.IAPBattlePassDto.Builder battlePassDtoBuilder = CommonProto.IAPBattlePassDto.newBuilder();
        if (battlePassModel.getId() <= 0) {
            return battlePassDtoBuilder.build();
        }
        int battlePassId = battlePassModel.getId();
        int battlePassScore = battlePassModel.getScore();
        battlePassDtoBuilder.setBattlePassId(battlePassId);
        battlePassDtoBuilder.setScore(battlePassScore);
        battlePassDtoBuilder.addAllFreeRewardIdList(battlePassModel.getFreeRewardId());
        battlePassDtoBuilder.addAllBattlePassRewardIdList(battlePassModel.getBattlePassRewardId());
        BattlePassEntity battlePassEntity = gameConfigManager.getIAPConfig().getBattlePassEntity(battlePassModel.getId());
        int finalRewardTimes = battlePassEntity.getFinalRewardTimes();
        int topScore = gameConfigManager.getBattlePassTopScoreMap().get(battlePassEntity.getGroupId());
        BattlePassRewardEntity loopEntity = gameConfigManager.getBattlePassLoopRewardMap().get(battlePassEntity.getGroupId());
        int loopScore = loopEntity.getScore();
        if (battlePassScore < topScore) {
            battlePassDtoBuilder.setCanRewardFinalCount(0);
        } else {
            int finalTimes = (battlePassScore - topScore) / loopScore;
            if (finalTimes > finalRewardTimes) {
                finalTimes = finalRewardTimes;
            }
            battlePassDtoBuilder.setCanRewardFinalCount(finalTimes - battlePassModel.getFinalRewardCount());
        }
        battlePassDtoBuilder.setRewardFinalCount(battlePassModel.getFinalRewardCount());
        battlePassDtoBuilder.setBuy(battlePassModel.getBuy());
        return battlePassDtoBuilder.build();
    }


    ///////////////////////////// 月卡 /////////////////////////////
    public void checkMonthCard() {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        IAPConfig iapConfig = gameConfigManager.getIAPConfig();
        ConcurrentSkipListMap<Integer, MonthCardEntity> monthCardEntityMap = iapConfig.getMonthCard();
        AtomicBoolean needUpdate = new AtomicBoolean(false);
        iap.getMonthCardMap().forEach((key, data) -> {
            MonthCardEntity monthCardEntity = iapConfig.getMonthCard().get(key);
            log.debug("month_card_mail start uid:{} config:{}  curTime:{}------>", userId, monthCardEntity.getId(), DateUtils.getUnixTime() * 1000);
            if (data.getLastCount() <= 0) {
                return;
            }
            if (DateUtils.isSameDay(DateUtils.getUnixTime(), data.getLastRewardTime())) {
                return;
            }
            if (data.getLastRewardTime() <= 0) {
                return;
            }
            int rewardTimes = DateUtils.getDayLast(data.getLastRewardTime(), DateUtils.getUnixTime() - DateUtils.SECONDS_PRE_DAY);

            if (rewardTimes > data.getLastCount()) {
                rewardTimes = data.getLastCount();
            }

            if (rewardTimes <= 0) {
                return;
            }

            String postID = null;
            if (monthCardEntity.getId() == 301) {
                postID = gameConfigManager.getMailTempId(4504, 4509);
            } else if (monthCardEntity.getId() == 302) {
                postID = gameConfigManager.getMailTempId(4503, 4508);
            } else if (monthCardEntity.getId() == 304) {
                postID = gameConfigManager.getMailTempId(4502, 4507);
            }
            if (postID == null) {
                return;
            }

            data.setLastRewardTime(DateUtils.getUnixTime() - DateUtils.SECONDS_PRE_DAY);
            data.setLastCount(data.getLastCount() - rewardTimes);
            needUpdate.set(true);

            List<List<Integer>> rewardConfig = monthCardEntity.getProductsPerDay().stream().map(ArrayList::new).collect(Collectors.toList());
            int finalRewardRate = rewardTimes;
            rewardConfig.forEach(temp -> temp.set(temp.size() - 1, temp.get(temp.size() - 1) * finalRewardRate));
            mailService.createMailAsync(userId, postID, new HashMap<>(), rewardConfig);
            log.debug("month_card_mail end uid:{} config:{} rewardTimes:{} postID:{}------>", userId, monthCardEntity.getId(), rewardTimes, postID);
        });
        if (needUpdate.get()) {
            shopDao.updateIap(shop);
        }
    }


    /////////////////////////// 成长基金 /////////////////////////////
    public CommonProto.LevelFundDto buildLevelFund(Shop shop) {
        Shop.IAPModel iapModel = shop.getIap();
        Map<Integer, List<Integer>> levelFundRewardMap = iapModel.getLevelFundRewardMap();
        Set<Integer> buyLevelFundGroupId = levelFundRewardMap.keySet();
        Map<Integer, CommonProto.IntegerArray> levelFundRewardDto = new HashMap<>();
        for (Map.Entry<Integer, List<Integer>> entry : levelFundRewardMap.entrySet()) {
            int key = entry.getKey();
            List<Integer> value = entry.getValue();
            CommonProto.IntegerArray.Builder arrayBuilder = CommonProto.IntegerArray.newBuilder();
            arrayBuilder.addAllIntegerArray(value);
            levelFundRewardDto.put(key, arrayBuilder.build());
        }

        CommonProto.LevelFundDto.Builder builder = CommonProto.LevelFundDto.newBuilder();
        builder.putAllLevelFundReward(levelFundRewardDto);
        builder.addAllBuyLevelFundGroupId(buyLevelFundGroupId);
        return builder.build();
    }

    /////////////////////// 日周月礼包 //////////////////////
    public Map<Integer, Integer> calPacksBuyCount() {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        ConcurrentSkipListMap<Integer, GiftPacksEntity> giftPacks = gameConfigManager.getIAPConfig().getGiftPacks();
        Map<Integer, Shop.IAPPacksModel> packsMap = iapModel.getPacksMap();
        long now = DateUtils.getUnixTime();
        Map<Integer, Integer> dataMap = new HashMap<>();
        boolean isUpdate = false;
        for (GiftPacksEntity giftPacksEntity : giftPacks.values()) {
            int id = giftPacksEntity.getId();
            if (!packsMap.containsKey(id)) {
                continue;
            }
            long resetTime = 0;
            Shop.IAPPacksModel iapPacksModel = packsMap.get(id);
            long lastBuyTime = iapPacksModel.getLastBuyTime();
            switch (giftPacksEntity.getPackType()) {
                case 1:
                    resetTime = DateUtils.getSystemResetTimeByTime(lastBuyTime);
                    break;
                case 2:
                    resetTime = DateUtils.getSystemWeekResetTimeByTime(lastBuyTime);
                    break;
                case 3:
                    resetTime = DateUtils.getSystemMonthResetTimeByTime(lastBuyTime);
                    break;
                default:
                    break;
            }
            if (resetTime <= 0) {
                continue;
            }
            if (resetTime <= now) {
                packsMap.remove(id);
                continue;
            }
            dataMap.put(id, iapPacksModel.getBuyCount());
            isUpdate = true;
        }
        if (isUpdate) {
            shopDao.updateIap(shop);
        }
        return dataMap;
    }

    public CommonProto.IAPGiftPackDto buildIAPGiftPackDto(Shop shop) {
        Map<Integer, Integer> packsBuyCountMap = calPacksBuyCount();
        long now = DateUtils.getUnixTime();
        long packsResetTimeDay = DateUtils.getSystemResetTimeByTime(now);
        long packsResetTimeWeek = DateUtils.getSystemWeekResetTimeByTime(now);
        long packsResetTimeMonth = DateUtils.getSystemMonthResetTimeByTime(now);

        CommonProto.IAPGiftPackDto.Builder builder = CommonProto.IAPGiftPackDto.newBuilder();
        builder.putAllPacksBuyCount(packsBuyCountMap);
        builder.setPacksResetTimeDay(packsResetTimeDay);
        builder.setPacksResetTimeWeek(packsResetTimeWeek);
        builder.setPacksResetTimeMonth(packsResetTimeMonth);
        return builder.build();
    }



    //////////////////// VIP //////////////////////////
    public List<Integer> vipGetRewardIdList() {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        return iap.getRewardVipLevelSet();
    }
}

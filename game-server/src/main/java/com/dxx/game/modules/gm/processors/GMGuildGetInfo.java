package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.GUILD_GET_INFO)
public class GMGuildGetInfo extends AbstractGMProcessor {

    private static final byte SEARCH_GUILD_BY_GUILD_ID = 1;
    private static final byte SEARCH_GUILD_BY_GUILD_NAME = 2;

    @Resource
    private GuildDao guildDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private int searchType;
        private String searchValue;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class GMResponse {
        private List<Map<String, Object>> guilds;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);

        int searchType = request.getSearchType();
        String searchValue = request.getSearchValue();
        if(StringUtils.isEmpty(searchValue)) {
            return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "searchValue is blank");
        }

        Guild guild = null;
        List<Guild> guilds = new ArrayList<>();
        switch (searchType) {
            case SEARCH_GUILD_BY_GUILD_ID:
                if(!isNumber(searchValue)) {
                    return this.error(GMErrorCode.ERROR_CODE_GUILD_NOT_EXIST, "guild id not exist, id:" + searchValue);
                }

                long searchGuildId = Long.parseLong(searchValue);
                guild = guildDao.getByGuildId(searchGuildId);

                break;
            case SEARCH_GUILD_BY_GUILD_NAME:
                guild = guildDao.getByName(searchValue);
                if (guild != null) {
                    long guildId = Long.parseLong(guild.getPK().split("#")[1]);
                    guild = guildDao.getByGuildId(guildId);
                }
                break;
            default:
                log.error("getGuildInfo error, no searchType. params:{}", params);
                return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "params not exist searchType");
        }

        if (guild != null) {
            guilds.add(guild);
        }
        if(guilds.isEmpty()) {
            log.error("getGuildInfo error, guild not exist. params:{}", params);
            return this.error(GMErrorCode.ERROR_CODE_GUILD_NOT_EXIST, "guild not exist");
        }

        List<Map<String, Object>> guildInfoList = guilds.stream().map(this::buildGuildInfo).collect(Collectors.toList());
        return GMResponse.builder().guilds(guildInfoList).build();
    }

    private Map<String, Object> buildGuildInfo(Guild guild) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> guildInfo = new ArrayList<>();
        guildInfo.add(this.buildKVDM("guildId", guild.getGuildId(), "公会ID", false));
        guildInfo.add(this.buildKVDM("guildName", guild.getGuildName(), "公会名称", true));
        guildInfo.add(this.buildKVDM("guildLevel", guild.getGuildLevel(), "公会等级", false));
        guildInfo.add(this.buildKVDM("guildExp", guild.getGuildExp() + "", "公会经验", false));
        guildInfo.add(this.buildKVDM("guildCreateTime", DateUtils.stampToDate(guild.getGuildCreateTime()), "公会创建时间", false));
        guildInfo.add(this.buildKVDM("guildMembersCount", guild.getGuildMembersCount(), "公会成员数量", false));
        result.put("guildInfo", guildInfo);
        return result;
    }
}

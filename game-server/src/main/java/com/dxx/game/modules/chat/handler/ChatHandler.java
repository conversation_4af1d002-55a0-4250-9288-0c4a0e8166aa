package com.dxx.game.modules.chat.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.ChatProto.*;
import com.dxx.game.modules.chat.service.ChatService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class ChatHandler {
	
	@Autowired
	private ChatService chatService;


	@ApiMethod(command = MsgReqCommand.ChatShowItemRequest, name = "聊天-展示物品", skipRedisLock = true)
	public Result<ChatShowItemResponse> chatGuildShowItem(Message msg) {
		ChatShowItemRequest params = (ChatShowItemRequest)msg;
		return chatService.chatShowItemAction(params);
	}

}
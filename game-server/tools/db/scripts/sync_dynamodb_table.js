const fs = require("fs");
const path = require("path");
let AWS = require('aws-sdk');
const colors = require('colors');

var arguments = process.argv.splice(2);
let env = "dev";
if (arguments.length > 0) {
	env = arguments[0];
}


// if (env != "dev" && env != "test" && env != "prod") {
// 	console.log("env error... please enter dev or test or prod");
// 	process.exit();
// }

// if (env == 'dev') {
// 	process.exit();
// }

// dynamodb 表 json 目录
const dynamodbTableConfigPath = path.join(__dirname, "../dynamodb");
// java 配置文件目录
const javaResourcesPath = path.join(__dirname, "../../../src/main/resources/" + env);

if (!fs.existsSync(javaResourcesPath)) {
	console.log(("javaResourcesPath error :" + javaResourcesPath).red);
	process.exit();
}

// 读取java 配置文件
const properties = fs.readFileSync(javaResourcesPath + "/application.properties", 'utf-8');
// dynamodb 配置文件目录
const dynamodbProperties = javaResourcesPath + "/application-dynamodb.properties";


const DYNAMODB_ENDPOINT = getPropertiesValueByKey(properties, "aws.dynamodb.endpoint.url");
if (!DYNAMODB_ENDPOINT) {
	console.log(("dynamodb endpoint is null, javaResourcesPath:" + javaResourcesPath).red);
	process.exit();
}

const GAME_NAME = getPropertiesValueByKey(properties, "spring.application.name").replace(/\s/g, "");

console.log(("do sync dynamodb tables [game = " + GAME_NAME + "] [env = " + env + "]").green);

const SPRING_APPLICATION_ENV = getPropertiesValueByKey(properties, "spring.application.env").replace(/\s/g, "");
// AWS 配置
const ACCESS_KEY_ID = getPropertiesValueByKey(properties, "aws.credentials.accessKey").replace(/\s/g, "");
const SECRET_ACCESS_KEY = getPropertiesValueByKey(properties, "aws.credentials.secretKey").replace(/\s/g, "");
const REGION = getPropertiesValueByKey(properties, "aws.region.static").replace(/\s/g, "");
const AWS_CREDENTIALS_PROFILE = getPropertiesValueByKey(properties, "aws.credentials.profile").replace(/\s/g, "");



let ddb;
if ((ACCESS_KEY_ID == "" || SECRET_ACCESS_KEY == "")) {
	if (AWS_CREDENTIALS_PROFILE == "" && !isDevelop()) {
		console.log(("AWS_CREDENTIALS_PROFILE is null").red);
		process.exit();
	}

	var credentials = new AWS.SharedIniFileCredentials({profile: AWS_CREDENTIALS_PROFILE});
	AWS.config.credentials = credentials;
	AWS.config.region = REGION;
	AWS.config.endpoint = DYNAMODB_ENDPOINT;
	ddb = new AWS.DynamoDB();
} else {
	AWS.config.update({
		accessKeyId: ACCESS_KEY_ID,
		secretAccessKey: SECRET_ACCESS_KEY,
		region: REGION,
		endpoint: DYNAMODB_ENDPOINT
	});
	ddb = new AWS.DynamoDB();
}



doSync();


// 同步方法
async function doSync() {
	let existTableNames = [];
	const tableNames = await listTables({});
	// console.log(tableNames);
	if (tableNames.hasOwnProperty("TableNames")) {
		existTableNames = tableNames['TableNames'];
	}

	const files = fs.readdirSync(dynamodbTableConfigPath);
	const tables = {};
	for (var i = 0; i < files.length; i ++) {
		const file = files[i];
		let tableJson = JSON.parse(fs.readFileSync(dynamodbTableConfigPath + "/" + file, 'utf-8'));

		const dataModels = tableJson['DataModel'];
		for (let j = 0; j < dataModels.length; j ++) {
			const dataModel = dataModels[j];
			var tableName = dataModel['TableName'];
			if (tableName.startsWith(GAME_NAME + "-")) {
				tableName = tableName.split(GAME_NAME + "-")[1];
			}
			let replaceTableName = env + "-" + GAME_NAME + "-" + tableName;
			// console.log(replaceTableName);
			if (SPRING_APPLICATION_ENV == "dev" || isDevelop()) {
				// 本地开发环境不用替换表名
				replaceTableName = GAME_NAME + "-" + tableName;

			}

			// 新表
			if (existTableNames.indexOf(replaceTableName) < 0) {
				await syncToRemote(dataModel, replaceTableName);
				console.log(("sync table success: tableName [" + tableName + "] to [" + replaceTableName + "] ...").green);
			} else {
				// 是否有更新
				// const tableDetails = await describeTable(replaceTableName);
				// const isUpdate = await syncUpdateToRemote(tableDetails, dataModel);
				// if (isUpdate) {
				// 	console.log("\x1B[32m update table success: tableName [" + tableName + "] to [" + replaceTableName + "] ...\x1B[0m");
				// }
			}

			tables[tableName] = replaceTableName;
		}

	}

	await syncPropertiesFile(tables);
}


// 查看表信息
async function describeTable(tableName) {
	return new Promise(function (resolve, reject) {
		ddb.describeTable({TableName: tableName}, function (err, data) {
			if (err) {
				reject(err);
			} else {
				resolve(data);
			}
		});
	});
}


// 查询所有表
async function listTables(params) {
	return new Promise(function (resolve, reject) {
		ddb.listTables(params, function (err, data) {
			if (err) {
				reject(err);
			} else {
				resolve(data);
			}
		});
	});
}

// 同步到服务器
async function syncToRemote(dataModel, replaceTableName) {


	var BillingMode = dataModel['BillingMode'];
	BillingMode = 'PAY_PER_REQUEST';

	var ProvisionedThroughput = {};
	let params = {
		TableName: replaceTableName,
		BillingMode: BillingMode
	};

	if (BillingMode == 'PROVISIONED') {
		ProvisionedThroughput['ReadCapacityUnits'] = 5;
		ProvisionedThroughput['WriteCapacityUnits'] = 5;

		params['ProvisionedThroughput'] = ProvisionedThroughput;
	}


	let KeySchema = [];
	let AttributeDefinitions = [];
	let GlobalSecondaryIndexes = [];
	let LocalSecondaryIndexes = [];


	// 表字段, 主键, 排序键
	if (dataModel.hasOwnProperty("KeyAttributes")) {
		for (let key in dataModel['KeyAttributes']) {
			const keyAttribute = dataModel['KeyAttributes'][key];

			if (key == "PartitionKey") {
				KeySchema.push({
					AttributeName: keyAttribute["AttributeName"],
					KeyType: "HASH"
				});
			} else {
				KeySchema.push({
					AttributeName: keyAttribute["AttributeName"],
					KeyType: "RANGE"
				});
			}

			AttributeDefinitions.push(keyAttribute);
		}
	}

	if (dataModel.hasOwnProperty("NonKeyAttributes")) {
		dataModel["NonKeyAttributes"].forEach((value, index) => {
			AttributeDefinitions.push(value);
		});
	}


	// 全局索引
	if (dataModel.hasOwnProperty("GlobalSecondaryIndexes")) {
		dataModel['GlobalSecondaryIndexes'].forEach((value, index) => {
			var obj = {};
			obj['IndexName'] = value['IndexName'];
			obj['Projection'] = value['Projection'];
			obj['KeySchema'] = [];

			for (var key in value['KeyAttributes']) {
				var keyAttribute = value['KeyAttributes'][key];
				if (key == "PartitionKey") {
					obj['KeySchema'].push({
						AttributeName: keyAttribute["AttributeName"],
						KeyType: "HASH"
					});
				} else {
					obj['KeySchema'].push({
						AttributeName: keyAttribute["AttributeName"],
						KeyType: "RANGE"
					});
				}
			}

			if (BillingMode == 'PROVISIONED') {
				obj['ProvisionedThroughput'] = ProvisionedThroughput;
			}

			GlobalSecondaryIndexes.push(obj);
		});
	}

	// 本地索引
	if (dataModel.hasOwnProperty("LocalSecondaryIndexes")) {
		dataModel['LocalSecondaryIndexes'].forEach((value, index) => {
			var obj = {};
			obj['IndexName'] = value['IndexName'];
			obj['Projection'] = value['Projection'];
			obj['KeySchema'] = [];

			for (var key in value['KeyAttributes']) {
				var keyAttribute = value['KeyAttributes'][key];
				if (key == "PartitionKey") {
					obj['KeySchema'].push({
						AttributeName: keyAttribute["AttributeName"],
						KeyType: "HASH"
					});
				} else {
					obj['KeySchema'].push({
						AttributeName: keyAttribute["AttributeName"],
						KeyType: "RANGE"
					});
				}
			}

			if (BillingMode == 'PROVISIONED') {
				obj['ProvisionedThroughput'] = ProvisionedThroughput;
			}
			LocalSecondaryIndexes.push(obj);
		});
	}


	params['KeySchema'] = KeySchema;


	if (AttributeDefinitions.length > 0) {
		params['AttributeDefinitions'] = AttributeDefinitions;
	}

	if (GlobalSecondaryIndexes.length > 0) {
		params['GlobalSecondaryIndexes'] = GlobalSecondaryIndexes;
	}

	if (LocalSecondaryIndexes.length > 0) {
		params['LocalSecondaryIndexes'] = LocalSecondaryIndexes;
	}


	return new Promise(function (resolve, reject) {
		ddb.createTable(params, function (err, data) {
			if (err) {
				reject(err);
			} else {
				resolve(data);
			}
		});
	});
}

// 同步更新到服务器(只能增加表字段/(新增/删除全局二级索引)， 如果增加了排序键请用控制台操作)
async function syncUpdateToRemote(tableDetails, dataModel) {
	// console.log(JSON.stringify(tableDetails));

	const tableName = tableDetails['Table']['TableName'];
	const oldDefinitions = tableDetails['Table']['AttributeDefinitions'];
	let oldGlobalSecondaryIndexes = [];
	let oldIndexNames = [];
	let addDefinitions = [];

	if (tableDetails['Table'].hasOwnProperty("GlobalSecondaryIndexes")) {
		oldGlobalSecondaryIndexes = tableDetails['Table']['GlobalSecondaryIndexes'];
	}

	// 待更新的值
	let AttributeDefinitions = [];
	let GlobalSecondaryIndexes = [];
	let oldDefinitionsMap = {};
	let oldGlobalSecondaryIndexesMap = {};

	oldDefinitions.forEach((value, index) => {
		oldDefinitionsMap[value['AttributeName']] = value['AttributeType'];
	});

	oldGlobalSecondaryIndexes.forEach((value, index) => {
		oldIndexNames.push(value['IndexName']);
		oldGlobalSecondaryIndexesMap[value['IndexName']] = value;
	});


	// 表字段, 主键, 排序键
	if (dataModel.hasOwnProperty("KeyAttributes")) {
		for (let key in dataModel['KeyAttributes']) {
			const keyAttribute = dataModel['KeyAttributes'][key];

			const AttributeName = keyAttribute['AttributeName'];

			if (!oldDefinitionsMap.hasOwnProperty(AttributeName)) {
				AttributeDefinitions.push(keyAttribute);

				if (addDefinitions.indexOf(AttributeName) < 0) {
					addDefinitions.push(AttributeName);
				}
			}
		}
	}

	if (dataModel.hasOwnProperty("NonKeyAttributes")) {
		dataModel["NonKeyAttributes"].forEach((value, index) => {

			const AttributeName = value['AttributeName'];

			if (!oldDefinitionsMap.hasOwnProperty(AttributeName)) {
				AttributeDefinitions.push(value);

				if (addDefinitions.indexOf(AttributeName) < 0) {
					addDefinitions.push(AttributeName);
				}
			}
		});
	}

	let newIndexNames = [];
	var BillingMode = dataModel['BillingMode'];

	// 全局索引
	if (dataModel.hasOwnProperty("GlobalSecondaryIndexes")) {
		dataModel['GlobalSecondaryIndexes'].forEach((value, index) => {
			const IndexName = value['IndexName'];
			newIndexNames.push(IndexName);
			if (!oldGlobalSecondaryIndexesMap.hasOwnProperty(IndexName)) {
				// 新增索引
				var obj = {};
				obj['IndexName'] = value['IndexName'];
				obj['Projection'] = value['Projection'];
				obj['KeySchema'] = [];

				for (var key in value['KeyAttributes']) {
					var keyAttribute = value['KeyAttributes'][key];
					if (key == "PartitionKey") {
						obj['KeySchema'].push({
							AttributeName: keyAttribute["AttributeName"],
							KeyType: "HASH"
						});
					} else {
						obj['KeySchema'].push({
							AttributeName: keyAttribute["AttributeName"],
							KeyType: "RANGE"
						});
					}

					if (addDefinitions.indexOf(keyAttribute["AttributeName"]) < 0) {
						AttributeDefinitions.push(keyAttribute);
					}
				}

				if (BillingMode == 'PROVISIONED') {
					obj['ProvisionedThroughput'] = {
						ReadCapacityUnits:5,
						WriteCapacityUnits:5
					};
				}
				GlobalSecondaryIndexes.push(obj);
			}
		});
	}

	let delIndexNames = [];
	oldIndexNames.forEach((value, index) => {
		if (newIndexNames.indexOf(value) < 0) {
			delIndexNames.push(value);
		}
	});

	let isUpdate = false;
	if (AttributeDefinitions.length > 0 || GlobalSecondaryIndexes.length > 0 || delIndexNames.length > 0) {
		isUpdate = true;
	}

	if (isUpdate) {
		let params = {
			TableName: tableName
		};

		if (AttributeDefinitions.length > 0) {
			params['AttributeDefinitions'] = AttributeDefinitions;
		}

		for (let i = 0; i < GlobalSecondaryIndexes.length; i ++) {
			params['GlobalSecondaryIndexUpdates'] = [];
			params['GlobalSecondaryIndexUpdates'].push({Create : GlobalSecondaryIndexes[i]});

			await updateDynamodbTableAsync(params);
		}

		for (let i = 0; i < delIndexNames.length; i ++) {
			params['GlobalSecondaryIndexUpdates'] = [];
			params['GlobalSecondaryIndexUpdates'].push({Delete : {IndexName: delIndexNames[i]}});

			await updateDynamodbTableAsync(params);
		}

		return true;
	}
	return false;

}

// 写入java dynamod.properties 配置文件
async function syncPropertiesFile(tables) {
	let content = "### dynamodb 表名 对应环境的真实表名\r\n\r\n";

	for (let key in tables) {

		content += key + "=" + tables[key] + "\r\n";
	}

	var fd = fs.openSync(dynamodbProperties,'w');
	fs.writeSync(fd, content, 0, "utf-8");
	fs.closeSync(fd);
}

async function updateDynamodbTableAsync(params) {
	// console.log(JSON.stringify(params));

	return new Promise(function (resolve, reject) {
		ddb.updateTable(params, function (err, data) {
			if (err) {
				reject(err);
			} else {
				resolve(data);
			}
		});
	});
}


function getPropertiesValueByKey(properties, key) {
	var lines = properties.split("\n");
	for (var i = 0; i < lines.length; i ++) {
		var line = lines[i];
		var array = line.split("=");
		if (array[0] == key) {
			return array[1].replace("\r", "");
		}
	}
	return "";
}

function isDevelop() {
	return !(DYNAMODB_ENDPOINT.indexOf("localhost") < 0 && DYNAMODB_ENDPOINT.indexOf("127.0.0.1") < 0);
}
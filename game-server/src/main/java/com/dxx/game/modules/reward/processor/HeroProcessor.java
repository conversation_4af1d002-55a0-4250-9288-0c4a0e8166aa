package com.dxx.game.modules.reward.processor;

import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.gamemember.MemberEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.consts.TaskType;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.HeroDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.hero.data.HeroRewardData;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.HeroReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.model.TaskProcess;
import com.dxx.game.modules.task.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:09
 */
@Slf4j
@Component
public class HeroProcessor implements RewardProcessor {

    @Autowired
    private HeroService heroService;
    @Autowired
    private EquipService equipService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private HeroDao heroDao;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private SevenDayTaskService sevenDayTaskService;


    @Override
    public RewardType getType() {
        return RewardType.HERO;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.NONE;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        int addCount = reward.getCount();
        if (addCount < 0) {
            HeroReward heroReward=(HeroReward)reward;
            int rowId = heroReward.getRowId();
            Hero hero = heroDao.getByRowId(userId, rowId);
            if(hero==null) {
                // 删除英雄由具体业务处理
                resultCode = ErrorCode.HERO_NOT_EXIST;
            }

        } else {
            // 配置表ID是否存在
            MemberEntity memberEntity = gameConfigManager.getGameMemberConfig().getMemberEntity(reward.getConfigId());
            if (memberEntity == null) {
                log.error("hero config id not exist:{}", reward.getConfigId());
                resultCode = ErrorCode.CONFIG_NOT_EXIST;
            }
        }

        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
//        List<Hero> heros = heroService.getAllHeros(userId);
//        List<Integer> heroConfigIds = heros.stream().map(Hero::getHeroId).collect(Collectors.toList());

        HeroReward heroReward = (HeroReward)rewardAction.getReward();

        HeroRewardData heroRewardData = new HeroRewardData();

//        RewardResult<List<Hero>> result = new RewardResult<List<Hero>>(this.getType());
        RewardResult<HeroRewardData> result = new RewardResult<>(this.getType());
        if(heroReward.getCount() > 0) {
            List<Hero> addHeros = new ArrayList<>();
            for (int i = 0; i < heroReward.getCount(); i ++) {
//                if(heroConfigIds.contains(heroReward.getConfigId())) {
                if(userExtend.getHeroRecords().contains(heroReward.getConfigId())) {
                    // 转碎片
                    MemberEntity memberEntity = gameConfigManager.getGameMemberConfig().getMemberEntity(heroReward.getConfigId());

                    List<Integer> rewards = new ArrayList<>();
                    rewards.add(memberEntity.getHeroFragmentId());
                    rewards.add(memberEntity.getHeroFragmentNumber());

                    RewardResultSet rewardResultSet = rewardService.executeReward(userId, rewards);
                    if(rewardResultSet.isSuccess()) {
                        heroRewardData.setRewardDtoList(rewardResultSet.getRewards());
                        heroRewardData.setItems(rewardResultSet.getItems());

                        result.setActualCount(memberEntity.getHeroFragmentNumber());
                    }
                }
                else {
                    Hero hero = heroService.createHero(userId, heroReward.getConfigId(), heroReward.getLevel(),
                            heroReward.getStar(), heroReward.getQuality(),heroReward.getRowId());
                    if (hero == null) {
                        // 插入数据失败
                        log.error("hero : create failed. userId: {}, heroId:{}", userId, heroReward.getConfigId());
                        return null;
                    }
                    addHeros.add(hero);
                    if (!userExtend.getHeroRecords().contains(heroReward.getConfigId())) {
                        userExtend.getHeroRecords().add(heroReward.getConfigId());
                    }

                    TaskProcess achieveProcess = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_COUNT_DIFF_HERO, userExtend.getHeroRecords().size());
                    taskService.updateTask(userId, achieveProcess);

                    // 新手7日活动任务
                    SevenDayTaskProcess sevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_COUNT_DIFF_HERO, userExtend.getHeroRecords().size());
                    sevenDayTaskService.updateTask(userId, sevenDayTaskProcess);

                    heroRewardData.setHeroList(addHeros);

                    result.setActualCount(1);
                }

                result.setCurrent(heroRewardData);
            }
        }
        userExtendDao.update(userExtend);
        return result;
    }
}

package com.dxx.game.common.aws.translate;

import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.translate.TranslateClient;
import software.amazon.awssdk.services.translate.model.TranslateResponse;
import software.amazon.awssdk.services.translate.model.TranslateTextRequest;
import software.amazon.awssdk.services.translate.model.TranslateTextResponse;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/17
 * @description:
 */
@Service
public class TranslateService {

    @Resource
    private TranslateClient translateClient;

    public String translate(String text, String source, String target) {
        TranslateTextRequest request = TranslateTextRequest.builder()
                .text(text)
                .sourceLanguageCode(source)
                .targetLanguageCode(target).build();

        TranslateTextResponse response = translateClient.translateText(request);
        return response.translatedText();
    }
}

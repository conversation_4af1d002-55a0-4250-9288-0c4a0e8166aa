
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Box_output {
    public Box_output(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("num").getAsJsonArray(); num = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  num.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("dropQuality").getAsJsonArray(); dropQuality = new java.util.ArrayList<java.util.List<Integer>>(_json0_.size()); for(JsonElement _e0 : _json0_) { java.util.List<Integer> _v0;  { com.google.gson.JsonArray _json1_ = _e0.getAsJsonArray(); _v0 = new java.util.ArrayList<Integer>(_json1_.size()); for(JsonElement _e1 : _json1_) { int _v1;  _v1 = _e1.getAsInt();  _v0.add(_v1); }   }  dropQuality.add(_v0); }   }
        definiteDropQuality = _buf.get("definiteDropQuality").getAsInt();
    }

    public static Box_output deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Box_output(_buf);
    }

    /**
     * 编号
     */
    public final int Id;
    /**
     * 序列数
     */
    public final java.util.List<Integer> num;
    /**
     * 掉落宝箱品质(宝箱id，数量，权重)
     */
    public final java.util.List<java.util.List<Integer>> dropQuality;
    /**
     * 特殊掉落
     */
    public final int definiteDropQuality;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "num:" + num + ","
        + "dropQuality:" + dropQuality + ","
        + "definiteDropQuality:" + definiteDropQuality + ","
        + "}";
    }
}


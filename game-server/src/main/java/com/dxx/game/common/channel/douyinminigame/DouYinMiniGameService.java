package com.dxx.game.common.channel.douyinminigame;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.douyinminigame.model.DouYinMiniGameConfig;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.iap.IAPEntity;
import com.dxx.game.config.entity.iap.PurchaseEntity;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @author: lsc
 * @createDate: 2024/7/15
 * @description:
 */
@Slf4j
@Service
public class DouYinMiniGameService implements ChannelService {
    private static final ChannelID CHANNEL_ID = ChannelID.DouYinMiniGame;

    private static final String REDIS_TOKEN_KEY = "douyin_mini_game_token";

    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private RedisService redisService;
    @Resource
    private AndroidCallBackService androidCallBackService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserDao userDao;

    public final static int PAY_CODE_SUCCESS = 0;
    public final static int PAY_CODE_USER_NOT_EXIST = -1;
    public final static int PAY_CODE_BALANCE_ERROR = -2;
    public final static int PAY_CODE_REWARD_ERROR = -3;
    public final static int PAY_CODE_USER_IS_TEST_SERVER = -4;
    public final static int PAY_CODE_AMOUNT_ERROR = -5;


    // code换取openid
    public Map<String, Object> code2Session(JSONObject params) {
        String code = params.getString("code");
        DouYinMiniGameConfig douYinMiniGameConfig = channelConfig.getDouYinMiniGameConfig();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(douYinMiniGameConfig.getLoginUrl());
        stringBuilder.append("?appid=").append(douYinMiniGameConfig.getAppId());
        stringBuilder.append("&secret=").append(douYinMiniGameConfig.getAppSecret());
        stringBuilder.append("&code=").append(code);


        String response = OkHttpClientUtil.get(stringBuilder.toString(), String.class);
        JSONObject respObj = JSONObject.parseObject(response);
        int error = respObj.getIntValue("error");

        Map<String, Object> result = new HashMap<>();
        if (error != 0) {
            log.error("code2Session failed, params:{}, resp:{}", params, response);
            result.put("errCode", error);
            result.put("errMsg", respObj.getString("errmsg"));
            return result;
        }
        String openId = respObj.getString("openid");
        String unionid = respObj.getString("unionid");
        result.put("errCode", 0);
        result.put("openId", openId);
        result.put("unionId", unionid);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("openId", openId);
        jsonObject.put("sessionKey", respObj.getString("session_key"));
        result.put("verification", CryptUtil.encode(jsonObject.toJSONString()));       // 登录验证信息, 掉用游戏服登录接口是回传
        return result;
    }

    @Override
    public Object payCb(FullHttpRequest request) {
        HttpMethod method = request.method();
        if (method.equals(HttpMethod.GET)) {
            Map<String, String> requestParams = HttpRequester.getParams(request);
            log.info("douyin_pay_cb, method:{}, requestParams:{}", method, requestParams);
            if (!this.checkPayCbSign(requestParams)) {
                log.error("checkPayCbSign failed, params:{}", requestParams);
                return "";
            }
            return requestParams.get("echostr");
        } else {
            String body = request.content().toString(StandardCharsets.UTF_8);
            JSONObject requestParams = JSONObject.parseObject(body);

            Map<String, String> postParams = new HashMap<>();
            Set<Map.Entry<String, Object>> entrySet = requestParams.entrySet();
            for (Map.Entry<String, Object> entry : entrySet) {
                postParams.put(entry.getKey(), entry.getValue().toString());
            }
            log.info("douyin_pay_cb, method:{}, requestParams:{}, postParams:{}", method, requestParams, postParams);
            if (!this.checkPayCbSign(postParams)) {
                return this.payResultFail();
            }
            String msg = postParams.get("msg");
            JSONObject jsonObject = JSONObject.parseObject(msg);
            DouYinMiniGameConfig douYinMiniGameConfig = channelConfig.getDouYinMiniGameConfig();
            if (!jsonObject.getString("appid").equals(douYinMiniGameConfig.getAppId())) {
                log.error("douyin_pay_cb_error, appid error, params:{}, appid:{}", postParams, douYinMiniGameConfig.getAppId());
                return this.payResultFail();
            }
            String cpOrderNo = jsonObject.getString("cp_orderno");
            String cpExtra = jsonObject.getString("cp_extra");
            String orderNoChannel = jsonObject.getString("order_no_channel");
            int amountCoin = jsonObject.getIntValue("amount_coin");
            PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(cpExtra);
            long userId = payBackParamsModel.getUserId();
            // 将请求转发到测试服
            if (gameConfigManager.isProd() && userId > 90000000) {
                String url = channelConfig.getGameUrls().get(0) + "/payCb/douyinminigame";
                String resp = OkHttpClientUtil.postJsonWithOutHeader(url, requestParams, String.class);
                log.info("the user is test server request to test server, params:{}, resp:{}", postParams, resp);
                return this.payResultSuccess();
            }
            int code = payCbForGame(orderNoChannel, cpOrderNo, payBackParamsModel, amountCoin);
            if (code != 0) {
                log.error("douyin_pay_cb_error, payCbForGame != 0, resultCode:{}, params:{}, appid:{}", code, postParams, douYinMiniGameConfig.getAppId());
                return this.payResultFail();
            }
            return this.payResultSuccess();
        }
    }

    private boolean checkPayCbSign(Map<String, String> params) {
        try {
            List<String> sortedString = new ArrayList<>();
            sortedString.add(channelConfig.getDouYinMiniGameConfig().getPayCbToken());
            sortedString.add(params.get("timestamp"));
            sortedString.add(params.get("nonce"));
            sortedString.add(params.get("msg"));
            Collections.sort(sortedString);
            //拼接字符串
            String joinStr = String.join("", sortedString);
            //用sha1算法进行加密
            MessageDigest md = MessageDigest.getInstance("SHA1");;
            //获取密文  （完成摘要计算）
            byte[] b = md.digest(joinStr.getBytes());
            //将密文转换成十六进制字符串（小写）
            String s = Hex.encodeHexString(b).toLowerCase();
            String signature = params.get("signature");

            return s.equals(signature);
        } catch (Exception e) {
            log.error("checkPayCbSignError, e:", e);
            return false;
        }
    }

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        String data = CryptUtil.decode(verification);
        JSONObject jsonObject = JSONObject.parseObject(data);
        String openId = jsonObject.getString("openId");
        if (StringUtils.isEmpty(openId) || !accountId.equals(openId)) {
            return false;
        }
        return true;
    }

    public String getAccessToken() {
        String accessToken = redisService.get(REDIS_TOKEN_KEY);
        if (StringUtils.isEmpty(accessToken)) {
            int loopCnt = 0;
            do {
                accessToken = this.reqAccessToken();
                loopCnt++;
            } while (loopCnt != 3 && StringUtils.isEmpty(accessToken));
        }
        return accessToken;
    }

    public String reqAccessToken() {
        try {
            DouYinMiniGameConfig douYinMiniGameConfig = channelConfig.getDouYinMiniGameConfig();
            String url = douYinMiniGameConfig.getTokenUrl();

            Map<String, Object> postData = new HashMap<>();
            postData.put("appid", douYinMiniGameConfig.getAppId());
            postData.put("grant_type", "client_credential");
            postData.put("secret", douYinMiniGameConfig.getAppSecret());

            String response = OkHttpClientUtil.postJsonWithOutHeader(url, postData, String.class);

            JSONObject respObj = JSONObject.parseObject(response);
            int err_no = respObj.getIntValue("err_no");
            if (err_no != 0) {
                log.error("reqAccessToken failed, resp:{}", response);
                return null;
            }

            JSONObject data = respObj.getJSONObject("data");

            String token = data.getString("access_token");
            int expiresIn = data.getIntValue("expires_in");

            // redis 保存token
            redisService.set(REDIS_TOKEN_KEY, token);
            // 过期时间 - 60秒
            redisService.expireKey(REDIS_TOKEN_KEY, expiresIn - 60, TimeUnit.SECONDS);
            log.info("reqAccessToken, access_token:{}, ", response);
            return token;

        } catch (Exception e) {
            log.error("reqAccessToken error", e);
            return null;
        }
    }

    public int payCbForGame(String orderId, String cpOrderId, PayBackParamsModel payBackParamsModel, int amountCoin) {

        boolean orderState = this.queryPayStateCanDispatchOrder(cpOrderId);
        if (!orderState) {
            return PAY_CODE_SUCCESS;
        }

        PurchaseEntity purchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(Integer.parseInt(payBackParamsModel.getProductId()));
        if (purchaseEntity == null) {
            log.error("payCbForGame productId not exist, productId:{}", payBackParamsModel.getProductId());
            return PAY_CODE_REWARD_ERROR;
        }
        IAPEntity iapEntity = gameConfigManager.getIAPConfig().getIAPEntity(purchaseEntity.getIAPID());
        if (iapEntity == null) {
            log.error("payCbForGame iapId not exist, iapId:{}", purchaseEntity.getIAPID());
            return PAY_CODE_REWARD_ERROR;
        }
        int amount = (int)iapEntity.getCNprice();
        long userId = payBackParamsModel.getUserId();

        User user = userDao.getByUserId(userId);
        if (user == null) {
            log.error("pay_cb_user_is_null, payBackParamsModel:{}", payBackParamsModel);
            return PAY_CODE_USER_NOT_EXIST;
        }

        // 检测金额是否合法
        boolean isWhiteUser = channelConfig.isPayWhiteList(userId);
        if (!isWhiteUser && amount * 10 != amountCoin) {
            return PAY_CODE_AMOUNT_ERROR;
        }

        boolean flag = this.deductGameCurrency(user.getAccountId(), user.getOs(), amountCoin, cpOrderId, iapEntity.getIapName());
        if (!flag) {
            return PAY_CODE_BALANCE_ERROR;
        }

        PayCbVo payCbVo = new PayCbVo();
        payCbVo.setUserId(payBackParamsModel.getUserId());
        payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
        payCbVo.setOrderId(orderId);
        payCbVo.setCpOrderId(cpOrderId);
        payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
        payCbVo.setProductId(payBackParamsModel.getProductId());
        payCbVo.setChannelId(CHANNEL_ID.getId());
        payCbVo.setAmount(amount * 100);

        int code = androidCallBackService.doDeliverGoods(payCbVo);
        if (code != AndroidPayCbErrorCode.SUCCESS) {
            log.error("payCb doDeliverGoods failed ,body:{}", payCbVo);
            return PAY_CODE_REWARD_ERROR;
        }
        return PAY_CODE_SUCCESS;
    }

    private boolean deductGameCurrency(String openId, String pf, int amount, String cpOrderId, String productName) {
        if (StringUtils.isEmpty(pf)) {
            pf = "android";
        }

        DouYinMiniGameConfig douYinMiniGameConfig = channelConfig.getDouYinMiniGameConfig();
        Map<String, Object> postData = new HashMap<>();
        postData.put("openid", openId);
        postData.put("appid", douYinMiniGameConfig.getAppId());
        postData.put("ts", DateUtils.getUnixTime());
        postData.put("zone_id", "1");
        postData.put("pf", pf.toLowerCase());
        postData.put("amt", amount);           // 扣除游戏币数量 price*10
        postData.put("bill_no", cpOrderId);        // 自定义订单号
        postData.put("pay_item", productName);       // 道具名称
        postData.put("access_token", this.getAccessToken());


        String sign = this.makePaySign(postData);
        postData.put("mp_sig", sign);

        String response = attemptPayment(douYinMiniGameConfig, postData);

        log.info("deductGameCurrency result, postData:{}, response:{}", postData, response);
        // Log success or error
        if (StringUtils.isEmpty(response)) {
            log.error("game_pay_failed: No response received, postData:{}", postData);
            return false;
        }

        JSONObject respObj = JSONObject.parseObject(response);
        int errcode = respObj.getIntValue("errcode");
        if (errcode != 0) {
            log.error("game_pay_error: {}, postData:{}", errcode, postData);
            return false;
        }

        return true;
    }

    private String attemptPayment(DouYinMiniGameConfig config, Map<String, Object> postData) {
        postData.put("mp_sign", this.makePaySign(postData));

        String response = OkHttpClientUtil.postJsonWithOutHeader(config.getPayUrl(), postData, String.class);

        if (!StringUtils.isEmpty(response)) {
            JSONObject respObj = JSONObject.parseObject(response);
            int errcode = respObj.getIntValue("errcode");

            if (errcode == 90017) { // If token expired, retry
                log.error("Token expired, retrying payment. postData:{}", postData);
                postData.remove("mp_sign");
                postData.put("access_token", this.reqAccessToken());
                postData.put("mp_sig", this.makePaySign(postData));
                response = OkHttpClientUtil.postJsonWithOutHeader(config.getPayUrl(), postData, String.class);
            }
        }

        return response;
    }

    private String makePaySign(Map<String, Object> params) {
        String str = this.sortMap(params);
        str = str + "&org_loc=/api/apps/game/wallet/game_pay&method=POST";
        return CryptUtil.getHmacSha256(str, channelConfig.getDouYinMiniGameConfig().getPaySecret().getBytes(StandardCharsets.UTF_8));
    }

    private String sortMap(Map<String, Object> params) {
        // 参数排序
        Map<String, Object> sortMap = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        sortMap.putAll(params);

        StringBuilder sb = new StringBuilder();
        Iterator<String> iterator = sortMap.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            String value = String.valueOf(sortMap.get(key));
            sb.append(key).append("=").append(value);
            if (iterator.hasNext()) {
                sb.append("&");
            }
        }
        return sb.toString();
    }

    private boolean queryPayStateCanDispatchOrder(String cpOrderId) {
        DouYinMiniGameConfig douYinMiniGameConfig = channelConfig.getDouYinMiniGameConfig();

        JSONObject responseObj = this.queryPaymentStatus(douYinMiniGameConfig.getPayStateUrl(), this.getAccessToken(), cpOrderId);
        if (responseObj == null) {
            return false;
        }
        int errno = responseObj.getIntValue("err_no");
        if (errno != 0) {
            String errorMsg = responseObj.getString("message");
            // token 失效重新请求一次
            if (errno == -15100 && errorMsg.equals("bad access token")) {
                responseObj = queryPaymentStatus(douYinMiniGameConfig.getPayStateUrl(), this.reqAccessToken(), cpOrderId);
            }
        }
        if (responseObj == null) {
            return false;
        }
        if (errno != 0) {
            return false;
        }
        log.error("douyin_pay_cb_query_state, cpOrderId:{}, resp:{}", cpOrderId, responseObj);
        JSONObject data = responseObj.getJSONObject("data");
        if (data.getString("status").equals("success")) {
            return true;
        }
        log.error("douyin_pay_cb_error, payStateError, cpOrderId:{}, resp:{}", cpOrderId, responseObj);
        return false;
    }

    private JSONObject queryPaymentStatus(String baseUrl, String accessToken, String cpOrderId) {
        String url = new StringBuilder(baseUrl)
                .append("?access_token=").append(accessToken)
                .append("&orderno=").append(cpOrderId)
                .toString();
        String response = OkHttpClientUtil.get(url, String.class);
        log.info("Douyin payment status query for CP Order ID: {}, response: {}", cpOrderId, response);
        if (StringUtils.isEmpty(response)) {
            return null; // 如果响应为空，返回null
        }
        return JSONObject.parseObject(response); // 解析并返回JSON对象
    }

    private Map<String, String> payResultFail() {
        Map<String, String> result = new HashMap<>();
        result.put("status", "fail");
        return result;
    }

    private Map<String, String> payResultSuccess() {
        Map<String, String> result = new HashMap<>();
        result.put("status", "success");
        return result;
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class GuildTech_GuildTechModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.GuildTech_GuildTech> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.GuildTech_GuildTech> _dataList;
    
    public GuildTech_GuildTechModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.GuildTech_GuildTech>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.GuildTech_GuildTech>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.GuildTech_GuildTech _v;
            _v = com.dxx.game.config.Bean.GuildTech_GuildTech.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.GuildTech_GuildTech> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.GuildTech_GuildTech> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.GuildTech_GuildTech get(int key) { return _dataMap.get(key); }

}

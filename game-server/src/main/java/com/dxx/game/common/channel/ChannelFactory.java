package com.dxx.game.common.channel;

import com.dxx.game.common.channel.alipay.AliPayService;
import com.dxx.game.common.channel.bilibili.BiliService;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.consts.LoginChannelID;
import com.dxx.game.common.channel.douyin.DouYinService;
import com.dxx.game.common.channel.dxx.DxxService;
import com.dxx.game.common.channel.habby.HabbyService;
import com.dxx.game.common.channel.harmony.HarmonyService;
import com.dxx.game.common.channel.honor.HonorService;
import com.dxx.game.common.channel.huawei.HuaWeiService;
import com.dxx.game.common.channel.kuaishou.KuaiShouService;
import com.dxx.game.common.channel.oppo.OppoService;
import com.dxx.game.common.channel.ssjj.SsjjService;
import com.dxx.game.common.channel.uc.UCService;
import com.dxx.game.common.channel.vivo.VivoService;
import com.dxx.game.common.channel.wechat.WeChatService;
import com.dxx.game.common.channel.wechatminigame.WeChatMiniGameJsApiService;
import com.dxx.game.common.channel.wechatminigame.WeChatMiniGameService;
import com.dxx.game.common.channel.xiaomi.XiaoMiService;
import com.dxx.game.common.channel.yyb.YybService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/4/26 17:12
 */
@Component
public class ChannelFactory {

    @Autowired
    private AliPayService aliPayService;
    @Autowired
    private OppoService oppoService;
    @Autowired
    private XiaoMiService xiaoMiService;
    @Autowired
    private VivoService vivoService;
    @Autowired
    private UCService ucService;
    @Autowired
    private YybService yybService;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private KuaiShouService kuaiShouService;
    @Autowired
    private SsjjService ssjjService;
    @Autowired
    private BiliService biliService;
    @Autowired
    private HuaWeiService huaWeiService;
    @Autowired
    private HabbyService habbyService;
    @Autowired
    private DxxService dxxService;
    @Resource
    private DouYinService douYinService;
    @Resource
    private WeChatMiniGameService weChatMiniGameService;
    @Resource
    private WeChatMiniGameJsApiService weChatMiniGameJsApiService;
    @Resource
    private HonorService honorService;
    @Resource
    private HarmonyService harmonyService;

    private static ChannelFactory factory;

    @PostConstruct
    private void init() {
        factory = this;
    }

    public static ChannelService getService(String uri) {
        if (uri.startsWith("/payCb/aliPay")) {
            return factory.aliPayService;
        } else if (uri.startsWith("/payCb/oppo")) {
            return factory.oppoService;
        } else if (uri.startsWith("/payCb/xiaomi")) {
            return factory.xiaoMiService;
        } else if (uri.startsWith("/payCb/vivo")) {
            return factory.vivoService;
        } else if (uri.startsWith("/payCb/uc")) {
            return factory.ucService;
        } else if (uri.startsWith("/payCb/yyb") || uri.startsWith("/payCb/yybSandBox")) {
            return factory.yybService;
        } else if (uri.startsWith("/payCb/weChat")) {
            return factory.weChatService;
        } else if (uri.startsWith("/payCb/kuaiShou")) {
            return factory.kuaiShouService;
        } else if (uri.startsWith("/payCb/ssjj")) {
            return factory.ssjjService;
        } else if (uri.startsWith("/payCb/bili")) {
            return factory.biliService;
        } else if (uri.startsWith("/payCb/douYin")) {
            return factory.douYinService;
        } else if (uri.startsWith("/payCb/" + ChannelID.WeChatMiniGame.getName())) {
            return factory.weChatMiniGameService;
        } else if (uri.startsWith("/payCb/" + ChannelID.WeChatJS.getName())) {
            return factory.weChatMiniGameJsApiService;
        } else if (uri.startsWith("/payCb/honor")) {
            return factory.honorService;
        } else if (uri.startsWith("/payCb/harmony")) {
            return factory.harmonyService;
        }
        return null;
    }

    public static ChannelService getService(int channelId) {
        LoginChannelID loginChannelID = LoginChannelID.valueOf(channelId);
        if (loginChannelID == null) {
            return null;
        }
        if (loginChannelID == LoginChannelID.UNITY) {
            return factory.dxxService;
        } else if (loginChannelID == LoginChannelID.APPLE) {
            return factory.habbyService;
        } else if (loginChannelID == LoginChannelID.HUAWEI) {
            return factory.huaWeiService;
        } else if (loginChannelID == LoginChannelID.YYB) {
            return factory.yybService;
        } else if (loginChannelID == LoginChannelID.XiaoMi) {
            return factory.xiaoMiService;
        } else if (loginChannelID == LoginChannelID.Oppo) {
            return factory.oppoService;
        } else if (loginChannelID == LoginChannelID.Vivo) {
            return factory.vivoService;
        } else if (loginChannelID == LoginChannelID.Ssjj) {
            return factory.ssjjService;
        } else if (loginChannelID == LoginChannelID.UC) {
            return factory.ucService;
        } else if (loginChannelID == LoginChannelID.Bili) {
            return factory.biliService;
        } else if (loginChannelID == LoginChannelID.DOUYIN) {
            return factory.douYinService;
        } else if (loginChannelID == LoginChannelID.WECHAT_MINI_GAME) {
            return factory.weChatMiniGameService;
        } else if (loginChannelID == LoginChannelID.HONOR) {
            return factory.honorService;
        } else if (loginChannelID == LoginChannelID.HARMONY) {
            return factory.harmonyService;
        } else {
            return factory.habbyService;
        }
    }
}

package com.dxx.game.modules.reward.model;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
public class GuildExpReward implements Reward {
    /**
     * 奖励类型   {@link RewardType}
     */
    private RewardType type = RewardType.GUILD_EXP;

    /**
     * 奖励子类型   {@link RewardResourceType}
     */
    private RewardResourceType resourceType = RewardResourceType.NONE;

    /**
     * 数量
     */
    private int count = 0;

    /**
     * 配置表ID
     */
    private int configId = 0;

    private int exp = 0;
    private int level = 0;

    public GuildExpReward() {

    }

    public GuildExpReward(int configId, int count) {
        this.configId = configId;
        this.count = count;
    }

    public static GuildExpReward valueOf(int configId, int count) {
        return new GuildExpReward(configId, count);
    }


    @Override
    public RewardType getType() {
        return this.type;
    }

    @Override
    public RewardResourceType getResourceType() {
        return this.resourceType;
    }

    @Override
    public int getCount() {
        return this.count;
    }

    @Override
    public Reward increase(int incrCount) {
        this.count += incrCount;
        return this;
    }

    @Override
    public int getConfigId() {
        return this.configId;
    }

    @Override
    public Reward union(Reward reward) {
        if (match(reward)) {
            this.count += reward.getCount();
        }
        return this;
    }

    @Override
    public boolean match(Reward reward) {
        if (!(reward instanceof GuildExpReward)) {
            return false;
        }
        return this.configId == reward.getConfigId();
    }

    public void setExp(int exp) {
        this.exp = exp;
    }
    public int getExp() {
        return this.exp;
    }
    public void setLevel(int level) {
        this.level = level;
    }
    public int getLevel() {
        return this.level;
    }
}

package com.dxx.game.modules.battle;

import com.dxx.game.dto.BattleProto;

public interface BattleService {

    /**
     * 章节战斗
     */
    BattleProto.RChapterCombatResp chapterCombat(BattleProto.RChapterCombatReq req);

    /**
     * 战力
     */
    BattleProto.RpcPowerResp power(BattleProto.RpcPowerReq request);

    /**
     * 工会boss
     */
    BattleProto.RGuildBossCombatResp guildBossCombat(BattleProto.RGuildBossCombatReq request);
}

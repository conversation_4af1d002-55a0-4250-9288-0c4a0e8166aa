package com.dxx.game.modules.reward.model;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * 基本奖励 金币，钻石 等等...
 * <AUTHOR>
 * @date 2019-12-13 20:34
 */
public class ResourceReward implements Reward {
	
	/**
	 * 奖励类型   {@link RewardType}
	 */
	private RewardType type = RewardType.RESOURCE;
	
	/**
	 * 奖励子类型   {@link RewardResourceType}
	 */
	private RewardResourceType resourceType = RewardResourceType.NONE;
	
	/**
	 * 数量
	 */
	private int count = 0;
	
	public ResourceReward() {
		
	}
	
	public ResourceReward(RewardResourceType resourceType, int count) {
		this.resourceType = resourceType;
		this.count = count;
	}
	
	public static ResourceReward valueOf(RewardResourceType resourceType, int count) {
		return new ResourceReward(resourceType, count);
	}
	
	@Override
	public RewardType getType() {
		return this.type;
	}

	@Override
	public RewardResourceType getResourceType() {
		return resourceType;
	}

	@Override
	public int getCount() {
		return count;
	}

	@Override
	public Reward increase(int incrCount) {
		this.count += incrCount;
		return this;
	}

	@Override
	public int getConfigId() {
		return this.resourceType.getValue();
	}

	@Override
	public Reward union(Reward reward) {
		if (match(reward)) {
			this.count += reward.getCount();
		}
		return this;
	}

	@Override
	public boolean match(Reward reward) {
		if (reward == null) {
			return false;
		}
		return this.type == reward.getType() && this.resourceType == reward.getResourceType();
	}

}

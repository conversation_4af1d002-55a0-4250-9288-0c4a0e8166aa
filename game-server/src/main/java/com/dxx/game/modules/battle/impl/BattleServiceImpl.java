package com.dxx.game.modules.battle.impl;

import com.dxx.game.dto.BattleProto;
import com.dxx.game.dto.BattleServiceGrpc;
import com.dxx.game.modules.battle.BattleService;
import io.grpc.Channel;
import io.grpc.ManagedChannelBuilder;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;


@Service
public class BattleServiceImpl implements BattleService {

    @Value("${battleService}")
    String url;

    BattleServiceGrpc.BattleServiceFutureStub battleService;

    @PostConstruct
    public void init() {
        String[] address = url.split(":");
        String host = address[0];
        int port = Integer.parseInt(address[1]);
        Channel channel = null;
        if (host.endsWith(".com")) {
            channel = ManagedChannelBuilder.forAddress(host, port)
                    .useTransportSecurity()
                    .idleTimeout(1, TimeUnit.MINUTES)
                    .build();
        } else {
            channel = ManagedChannelBuilder.forAddress(host, port)
                    .usePlaintext()
                    .idleTimeout(1, TimeUnit.MINUTES)
                    .build();
        }
        battleService = BattleServiceGrpc.newFutureStub(channel);
    }

    @SneakyThrows
    @Override
    public BattleProto.RChapterCombatResp chapterCombat(BattleProto.RChapterCombatReq req) {
        return battleService.handleCombat(req).get(60, TimeUnit.SECONDS);
    }


    @SneakyThrows
    @Override
    public BattleProto.RpcPowerResp power(BattleProto.RpcPowerReq request) {
        return battleService.handlePower(request).get(3, TimeUnit.SECONDS);
    }

    @SneakyThrows
    @Override
    public BattleProto.RGuildBossCombatResp guildBossCombat(BattleProto.RGuildBossCombatReq request) {
        return battleService.handleGuildBoss(request).get(3, TimeUnit.SECONDS);
    }

}

package com.dxx.game.common.aws.dynamodb;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.capacity.ConsumedCapacityFactory;
import com.dxx.game.common.aws.dynamodb.capacity.Operation;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.aws.dynamodb.model.mapper.DynamoDBMapperRegistry;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.server.statistics.StatisticsContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.CommonUtils;
import com.dxx.game.config.GameConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.*;
import software.amazon.awssdk.enhanced.dynamodb.mapper.BeanTableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.*;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.*;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.Serializable;
import java.lang.invoke.*;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2022/5/12 5:42 下午
 */
@Slf4j
@Repository
@SuppressWarnings("unchecked")
public class BaseDynamoDBDao<T extends DynamoDBBaseModel> {

    @Resource
    protected DynamoDbEnhancedClient dynamoDbEnhancedClient;
    @Resource
    protected DynamoDbClient dynamoDbClient;

    protected Class<T> tClass;

    protected DynamoDbTable<T> mappedTable;


    protected String tableName;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private ConsumedCapacityFactory consumedCapacityFactory;

    private final Map<MyFunction<T,Object>, BiConsumer<T,Object>> functionCacheMap = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {

        if (!this.getClass().getGenericSuperclass().getTypeName().equals("java.lang.Object")) {
            this.tClass = (Class<T>)((ParameterizedType)getClass().getGenericSuperclass()).getActualTypeArguments()[0];

            this.tableName = DynamoDBMapperRegistry.getRealTableName(AnnotationUtils.findAnnotation(this.tClass, DynamoDBTableName.class).value());

            BeanTableSchema<T> tableSchema = TableSchema.fromBean(tClass);
            this.mappedTable = dynamoDbEnhancedClient.table(this.tableName, tableSchema);
            DynamoDBTransactionAspectSupport.addTableInfo(this.tClass, this.mappedTable, tableSchema);

        }
    }

    /**
     * 根据主键获取数据
     * @param partitionKey
     * @return
     */
    public T getItem(Object partitionKey) {
        T obj = DynamoDBCacheManager.get(this.tClass, partitionKey);
        if (obj == null) {
            Key key = this.buildKey(partitionKey);
            GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder().key(key).
                    returnConsumedCapacity(ReturnConsumedCapacity.INDEXES).consistentRead(true).build();
            GetItemEnhancedResponse<T> r = mappedTable.getItemWithResponse(getItemEnhancedRequest);
            StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(getItemEnhancedRequest, r.consumedCapacity()));
            obj = r.attributes();
            if (obj != null) {
                DynamoDBCacheManager.put(obj);
            }
        }
        return obj;
    }

    public T getItemWithoutCache(Object partitionKey) {
        Key key = this.buildKey(partitionKey);
        GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder()
                .key(key)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .consistentRead(true).build();
        GetItemEnhancedResponse<T> r = mappedTable.getItemWithResponse(getItemEnhancedRequest);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(getItemEnhancedRequest, r.consumedCapacity()));
        return r.attributes();
    }

    /**
     * 根据主键和排序键获取数据
     * @param partitionKey
     * @param sortKey
     * @return
     */
    public T getItem(Object partitionKey, Object sortKey) {
        String cacheKey = partitionKey.toString() + "_" + sortKey.toString();
        T obj = DynamoDBCacheManager.get(this.tClass, cacheKey);
        if (obj == null) {
            Key key = this.buildKey(partitionKey, sortKey);
            GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder()
                    .key(key)
                    .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                    .consistentRead(true)
                    .build();
            var r = mappedTable.getItemWithResponse(getItemEnhancedRequest);
            StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(getItemEnhancedRequest, r.consumedCapacity()));
            obj = r.attributes();
            if (obj != null) {
                DynamoDBCacheManager.put(obj);
            }
        }
        return obj;
    }

    /**
     * 插入数据
     * @param data
     */
    public void insert(T data) {
        DynamoDBTransactionAspectSupport.addPutItem(data);
        DynamoDBCacheManager.put(data);
    }

    /**
     * 立即插入
     * @param data
     */
    public void insertNow(T data) {
        PutItemEnhancedRequest<T> request = PutItemEnhancedRequest.builder(this.tClass)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .item(data)
                .build();
        var key = this.mappedTable.keyFrom(data);
        var r = this.mappedTable.putItemWithResponse(request);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(Operation.PUT, key, r.consumedCapacity()));
    }


    /**
     * 立刻插入数据并放入缓存
     * @param data
     */
    public void insertNowCache(T data) {
        insertNow(data);
        DynamoDBCacheManager.put(data);
    }
    /**
     * 更新数据
     * @param data
     */
    public void update(T data) {
        DynamoDBTransactionAspectSupport.addUpdateItem(data, false);
    }

    public void updateNow(T data) {
        UpdateItemEnhancedRequest<T> request = UpdateItemEnhancedRequest.builder(this.tClass)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .item(data)
                .build();
        var key = this.mappedTable.keyFrom(data);
        var r = this.mappedTable.updateItemWithResponse(request);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(Operation.UPDATE, key, r.consumedCapacity()));
    }

    /**
     * 更新一个或多个属性
     * @param data
     * @param dataFunc
     */
    public void updateProperty(T data, MyFunction<T,Object>... dataFunc) {
        T update = DynamoDBCacheManager.getUpdateObj(data);
        for (MyFunction<T, Object> myFunction : dataFunc) {
            BiConsumer<T,Object> bc = this.functionCacheMap.computeIfAbsent(myFunction, k -> {
                try {
                    return resolve(data.getClass(),k);
                } catch (Throwable e) {
                    log.error("Resolve function error", e);
                    throw new RuntimeException(e);
                }
            });
            var r = myFunction.apply(data);
            bc.accept(update, r);
        }
        updateIgnoreNulls(update);
    }


    public BiConsumer<T,Object> resolve(Class<?>clazz, MyFunction<T,Object>getterLambda) throws Throwable{
        var nM = getterLambda.getClass().getDeclaredMethod("writeReplace");
        nM.setAccessible(true);
        SerializedLambda sl = (SerializedLambda)nM.invoke(getterLambda);
        String name =sl.getImplMethodName();
        String fieldName = "";
        if(name.startsWith("get")){
            fieldName = name.substring(3);
        }else if(name.startsWith("is")){
            fieldName = name.substring(2);
        }else{
            throw new RuntimeException("fieldName must Be is or get"+name);
        }
        Method m = clazz.getMethod(name);
        MethodHandles.Lookup lookup = MethodHandles.lookup();
        var setter =lookup.findVirtual(clazz,"set"+fieldName, MethodType.methodType(void.class,m.getReturnType()));
        MethodType instantiated = setter.type().wrap().changeReturnType(void.class);
        CallSite site = LambdaMetafactory.metafactory(lookup,
                "accept",
                MethodType.methodType(BiConsumer.class),
                instantiated.erase(),
                setter,
                instantiated);
        return  (BiConsumer) site.getTarget().invokeExact();
    }


    @FunctionalInterface
    public interface  MyFunction<T,R> extends Function<T,R>, Serializable {

    }

    /**
     * 更新数据 - 忽略null值
     * @param data
     */
    public void updateIgnoreNulls(T data) {
        DynamoDBTransactionAspectSupport.addUpdateItem(data, true);
    }


    public void updateIgnoreNulls(T data, Consumer<T> dataFunc) {
        var key = data.getIdentity();
        var isPut = CommonUtils.isContain(DynamoDBTransactionAspectSupport.getPutItems(), key);
        var isDelete = CommonUtils.isContain(DynamoDBTransactionAspectSupport.getDeleteItems(), key);
        if (isDelete || isPut) {
            return;
        }
        T update = DynamoDBCacheManager.getUpdateObj(data);
        dataFunc.accept(update);
        DynamoDBTransactionAspectSupport.addUpdateItem(update, true);
    }

    public void updateNowIgnoreNulls(T data) {
        UpdateItemEnhancedRequest<T> request = UpdateItemEnhancedRequest.builder(this.tClass)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .item(data)
                .ignoreNulls(true)
                .build();
        var key = this.mappedTable.keyFrom(data);
        var r = this.mappedTable.updateItemWithResponse(request);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(Operation.UPDATE, key, r.consumedCapacity()));
    }

//
//    public void updateNowWithCondition(T data, Expression expression) {
//        UpdateItemEnhancedRequest<T> updateRequest
//                = UpdateItemEnhancedRequest.builder(this.tClass).item(data).ignoreNulls(true).conditionExpression(expression).build();
//        this.mappedTable.updateItem(updateRequest);
//    }


    /**
     * 删除数据
     * @param data
     */
    public void delete(T data) {
        DynamoDBTransactionAspectSupport.addDeleteItem(data);
    }

    /**
     * 立即删除
     * @param data
     */
    public void deleteNow(T data) {

        var key = this.mappedTable.keyFrom(data);
        DeleteItemEnhancedRequest request = DeleteItemEnhancedRequest.builder()
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .key(key)
                .build();

        var r = this.mappedTable.deleteItemWithResponse(request);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(Operation.DELETE, key, r.consumedCapacity()));
    }

    /**
     * 条件 更新数据
     * @param key
     * @param updateValues
     */
    public void updateAttributes(Map<String, AttributeValue> key, Map<String, AttributeValueUpdate> updateValues) {
        UpdateItemRequest request = UpdateItemRequest.builder()
                .tableName(this.tableName)
                .key(key)
                .attributeUpdates(updateValues)
                .build();
        DynamoDBTransactionAspectSupport.addUpdateItemRequest(request);
    }

    /**
     * 立即更新数据
     * @param key
     * @param updateValues
     */
    public void updateAttributesNow(Map<String, AttributeValue> key, Map<String, AttributeValueUpdate> updateValues) {
        UpdateItemRequest request = UpdateItemRequest.builder()
                .tableName(this.tableName)
                .key(key)
                .attributeUpdates(updateValues)
                .build();
        dynamoDbClient.updateItem(request);
    }

    /**
     * 查询指定字段的数据
     * @param key
     * @param attributes
     * @return
     */
    public Map<String,AttributeValue> getAttributes(Map<String, AttributeValue> key, String... attributes) {
        GetItemRequest getItemRequest = GetItemRequest.builder()
                .tableName(tableName)
                .key(key)
                .consistentRead(true)
                .attributesToGet(attributes).build();
        return dynamoDbClient.getItem(getItemRequest).item();
    }

//    public void test(long userId1, long userId2) {
//        Map<String, KeysAndAttributes> requestItems = new HashMap<>();
//        Map<String, AttributeValue> key1 = new HashMap<>();
//        key1.put("userId", ConvertUtil.buildAttributeValue(userId1));
//        Map<String, AttributeValue> key2 = new HashMap<>();
//        key2.put("userId", ConvertUtil.buildAttributeValue(userId2));
//
//        Map<String, KeysAndAttributes> keysAndAttributesMap = new HashMap<>();
//        keysAndAttributesMap.put(tableName, KeysAndAttributes.builder().keys(key1, key2).attributesToGet("chapterId").consistentRead(true).build());
//        BatchGetItemRequest batchGetItemRequest = BatchGetItemRequest.builder().requestItems(keysAndAttributesMap).build();
//        BatchGetItemResponse batchGetItemResponse = dynamoDbClient.batchGetItem(batchGetItemRequest);
//        System.out.println(batchGetItemResponse);
//    }



    public Map<Long, T> getByPartitionKey(List<Long> partitionKeys) {
        Map<Long,T> result = new HashMap<>();
        List<T> datas = batchGetItem(partitionKeys);
        for (T data : datas) {
            result.put((Long) data.getUniqueKey(), data);
        }
        return result;
    }


    /**
     * 根据主键获取所有数据
     * @param partitionKey
     * @return
     */
    public List<T> getAll(Object partitionKey) {
        Key key = this.buildKey(partitionKey);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .consistentRead(true)
                .build();
        PageIterable<T> pageIterable = mappedTable.query(queryEnhancedRequest);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(queryEnhancedRequest, "keyEqualTo", key, this.getAllItemsCostFromQueryResults(pageIterable)));
        return this.getAllItemsFromQueryResults(pageIterable);
    }

    public List<T> getLimit(Object partitionKey, int limit) {
        Key key = this.buildKey(partitionKey);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .limit(limit)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .scanIndexForward(false)
                .build();
        PageIterable<T> pageIterable = mappedTable.query(queryEnhancedRequest);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(queryEnhancedRequest, "keyEqualTo", key, this.getAllItemsCostFromQueryResults(pageIterable)));
        return this.getAllItemsFromQueryResults(pageIterable);
    }

    public <E> List<T> batchGetItem(List<E> partitionKeys) {
        List<T> result = new ArrayList<>();
        List<List<E>> splitKeys = CollectionUtils.splitList(partitionKeys, 100);

        for (List<E> keys : splitKeys) {
            ReadBatch.Builder<T> readBatchBuilder = ReadBatch.builder(this.tClass).mappedTableResource(this.mappedTable);
            for (Object key : keys) {
                GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder()
                        .key(this.buildKey(key))
                        .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                        .consistentRead(true).build();
                readBatchBuilder.addGetItem(getItemEnhancedRequest);
            }
            ReadBatch readBatch = readBatchBuilder.build();
            BatchGetItemEnhancedRequest batchGetItemEnhancedRequest = BatchGetItemEnhancedRequest.builder()
                    .addReadBatch(readBatch)
                    .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                    .build();
            BatchGetResultPageIterable batchResults = dynamoDbEnhancedClient.batchGetItem(batchGetItemEnhancedRequest);
            result.addAll(this.getAllItemsFromBatchGetResults(batchResults));

            StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(batchGetItemEnhancedRequest, batchResults));
        }

        return result;
    }

    public <E> List<T> batchGetItemWithSortKeys(List<List<Object>> keys) {
        List<T> result = new ArrayList<>();

        List<List<List<Object>>> splitKeys = CollectionUtils.splitMatrixList(keys, 100);
        for (List<List<Object>> splitData : splitKeys) {
            ReadBatch.Builder<T> readBatchBuilder = ReadBatch.builder(this.tClass).mappedTableResource(this.mappedTable);
            for (List<Object> key : splitData) {
                GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder()
                        .key(this.buildKey(key.get(0), key.get(1)))
                        .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                        .consistentRead(true)
                        .build();
                readBatchBuilder.addGetItem(getItemEnhancedRequest);
            }
            ReadBatch readBatch = readBatchBuilder.build();
            BatchGetItemEnhancedRequest batchGetItemEnhancedRequest = BatchGetItemEnhancedRequest.builder()
                    .addReadBatch(readBatch)
                    .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                    .build();
            BatchGetResultPageIterable batchResults = dynamoDbEnhancedClient.batchGetItem(batchGetItemEnhancedRequest);
            result.addAll(this.getAllItemsFromBatchGetResults(batchResults));

            StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(batchGetItemEnhancedRequest, batchResults));
        }
        return result;
    }


    /**
     * 批量获取数据
     * @param partitionKey
     * @param sortKeys
     * @return
     */
    public <E> List<T> batchGetItem(Object partitionKey, List<E> sortKeys) {

        // 最多返回100条数据
        List<List<E>> splitSortKeys = CollectionUtils.splitList(sortKeys, 100);

        List<T> result = new ArrayList<>();
        for (List<E> keys : splitSortKeys) {
            ReadBatch.Builder<T> readBatchBuilder = ReadBatch.builder(this.tClass).mappedTableResource(this.mappedTable);

            for (Object obj : keys) {
                GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder()
                        .key(this.buildKey(partitionKey, obj))
                        .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                        .consistentRead(true).build();
                readBatchBuilder.addGetItem(getItemEnhancedRequest);
            }
            ReadBatch readBatch = readBatchBuilder.build();

            BatchGetItemEnhancedRequest batchGetItemEnhancedRequest = BatchGetItemEnhancedRequest.builder()
                    .addReadBatch(readBatch)
                    .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                    .build();
            BatchGetResultPageIterable batchResults = dynamoDbEnhancedClient.batchGetItem(batchGetItemEnhancedRequest);
            result.addAll(this.getAllItemsFromBatchGetResults(batchResults));

            StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(batchGetItemEnhancedRequest, batchResults));
        }
        return result;
    }

    public List<Map<String, AttributeValue>> batchGetItemsByKeys(List<Map<String, AttributeValue>> keys, Map<String, KeysAndAttributes> fields) {
        List<Map<String, AttributeValue>> result = new ArrayList<>();
        List<List<Map<String, AttributeValue>>> splitKeys = CollectionUtils.splitList(keys, 100);
        for (List<Map<String, AttributeValue>> splitKey : splitKeys) {
            BatchGetItemRequest batchGetItemRequest = BatchGetItemRequest.builder()
                    .requestItems(fields)
                    .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                    .build();
            BatchGetItemResponse batchGetItemResponse = dynamoDbClient.batchGetItem(batchGetItemRequest);
            for (Map.Entry<String, List<Map<String, AttributeValue>>> entry : batchGetItemResponse.responses().entrySet()) {
                result.addAll(entry.getValue());
            }

            StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(batchGetItemRequest, batchGetItemResponse));
        }
        return result;
    }

    /**
     * 查询数据
     * @param partitionKey
     * @param expression
     * @return
     */
    public List<T> query(Object partitionKey, Expression expression) {
        Key key = this.buildKey(partitionKey);
        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .filterExpression(expression)
                .consistentRead(true)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .build();
        PageIterable<T> pageIterable = mappedTable.query(queryEnhancedRequest);
        StatisticsContext.putDdbConsumedCapacity(consumedCapacityFactory.create(queryEnhancedRequest, "keyEqualTo", key,  this.getAllItemsCostFromQueryResults(pageIterable)));
        return this.getAllItemsFromQueryResults(pageIterable);
    }

    /**
     * 查询一条数据
     * @param partitionKey
     * @param expression
     * @return
     */
    public T queryOne(Object partitionKey, Expression expression) {
        List<T> result = this.query(partitionKey, expression);
        if (result.isEmpty()) {
            return null;
        }
        return result.get(0);
    }

    /**
     * 查询一条数据
     * 首先从缓存查询， 缓存未查到从ddb查询
     * @param partitionKey
     * @param expression
     * @param cacheKey
     * @return
     */
    public T queryOne(Object partitionKey, Expression expression, String cacheKey) {
        T obj = DynamoDBCacheManager.get(cacheKey);
        if (obj == null) {
            List<T> result = this.query(partitionKey, expression);
            if (result.isEmpty()) {
                return null;
            }
            obj = result.get(0);
            DynamoDBCacheManager.put(cacheKey, obj);
        }
        return obj;
    }

    /**
     * 构建Key
     * @param partitionKey
     * @return
     */
    public Key buildKey(Object partitionKey) {
        Key.Builder keyBuilder = Key.builder();
        if (partitionKey instanceof Long) {
            keyBuilder.partitionValue((Long) partitionKey);
        } else if (partitionKey instanceof Integer) {
            keyBuilder.partitionValue((Integer) partitionKey);
        } else if (partitionKey instanceof String) {
            keyBuilder.partitionValue(partitionKey.toString());
        }
        return keyBuilder.build();
    }

    /**
     * 构建key 主键和排序键
     * @param partitionKey
     * @param sortKey
     * @return
     */
    public Key buildKey(Object partitionKey, Object sortKey) {
        Key.Builder keyBuilder = Key.builder();
        if (partitionKey instanceof Long) {
            keyBuilder.partitionValue((Long) partitionKey);
        } else if (partitionKey instanceof Integer) {
            keyBuilder.partitionValue((Integer) partitionKey);
        } else if (partitionKey instanceof String) {
            keyBuilder.partitionValue(partitionKey.toString());
        }

        if (sortKey instanceof Long) {
            keyBuilder.sortValue((Long) sortKey);
        } else if (sortKey instanceof Integer) {
            keyBuilder.sortValue((Integer) sortKey);
        } else if (sortKey instanceof String) {
            keyBuilder.sortValue(sortKey.toString());
        }

        return keyBuilder.build();
    }

    /**
     * 查询条件的key
     * @param pkName
     * @param pkValue
     * @return
     */
    public Map<String, AttributeValue> getKey(String pkName, Object pkValue) {
        HashMap<String, AttributeValue> itemKey = new HashMap<String,AttributeValue>();
        itemKey.put(pkName, DynamoDBConvertUtil.buildAttributeValue(pkValue));
        return itemKey;
    }

    /**
     * 查询条件的key
     * @param pkName
     * @param pkValue
     * @param skName
     * @param skValue
     * @return
     */
    public Map<String, AttributeValue> getKey(String pkName, Object pkValue, String skName, Object skValue) {
        HashMap<String, AttributeValue> itemKey = new HashMap<String,AttributeValue>();
        itemKey.put(pkName, DynamoDBConvertUtil.buildAttributeValue(pkValue));
        itemKey.put(skName, DynamoDBConvertUtil.buildAttributeValue(skValue));
        return itemKey;
    }

    /**
     * 默认key pkName = userId
     * @param pkValue
     * @return
     */
    public Map<String, AttributeValue> getKey(Object pkValue) {
        return this.getKey("userId", pkValue);
    }

    /**
     * 默认key pkName = userId, skName = rowId
     * @param pkValue
     * @param skValue
     * @return
     */
    public Map<String, AttributeValue> getKey(Object pkValue, Object skValue) {
        return this.getKey("userId", pkValue, "rowId", skValue);
    }

    /**
     * 构建 AttributeValueUpdate
     */
    public AttributeValueUpdate buildAttributeValueUpdate(Object value) {
        return AttributeValueUpdate.builder()
                .value(DynamoDBConvertUtil.buildAttributeValue(value))
                .action(AttributeAction.PUT)
                .build();
    }

    public AttributeValueUpdate buildAttributeValueUpdate(AttributeValue attributeValue) {
        return AttributeValueUpdate.builder()
                .value(attributeValue)
                .action(AttributeAction.PUT)
                .build();
    }

//    public <E> List<T> scanItems() {
//        ScanEnhancedRequest scanEnhancedRequest = ScanEnhancedRequest.builder().build();
//        return mappedTable.scan(scanEnhancedRequest).items().stream().collect(Collectors.toList());
//    }


    // 获取所有query的结果集
    protected List<T> getAllItemsFromQueryResults(PageIterable<T> pageIterable) {
        List<T> result = new ArrayList<>();
        pageIterable.forEach(page -> {
            result.addAll(page.items());
        });
        return result;
    }

    // 获取所有batchGetItem结果集
    private List<T> getAllItemsFromBatchGetResults(BatchGetResultPageIterable batchGetResultPageIterable) {
        List<T> result = new ArrayList<>();
        batchGetResultPageIterable.forEach(page -> {
            result.addAll(page.resultsForTable(this.mappedTable));
        });
        return result;
    }

    private List<ConsumedCapacity> getAllItemsCostFromQueryResults(PageIterable<T> pageIterable) {
        List<ConsumedCapacity> result = new ArrayList<>();
        pageIterable.forEach(page -> {
            result.add(page.consumedCapacity());
        });
        return result;
    }

    public List<ConsumedCapacity> getAllItemsCostFromQueryResults(SdkIterable<Page<T>> pageIterable) {
        List<ConsumedCapacity> result = new ArrayList<>();
        pageIterable.forEach(page -> {
            result.add(page.consumedCapacity());
        });
        return result;
    }
}

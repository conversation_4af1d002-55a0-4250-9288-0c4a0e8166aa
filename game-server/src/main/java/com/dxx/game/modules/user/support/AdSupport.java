package com.dxx.game.modules.user.support;

import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.gameconfig.AdInfoEntity;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dto.CommonProto;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/6/11
 * @description:
 */
@Slf4j
@Component
public class AdSupport {

    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private GameConfigManager gameConfigManager;

    public boolean costAd(long userId, int adId) {
        Map<Integer, UserExtend.AdInfo> adData = getAdInfos(userId);
        if (!adData.containsKey(adId)) {
            return false;
        }
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        long now = DateUtils.getUnixTime();
        // TODO 判断是否有永久免广告卡, 如果有免广告月卡则不用检测lastAdTs

        long lastAdTS = userExtend.getLastAdTs() == null ? 0L : userExtend.getLastAdTs();
        // 默认每次广告5秒钟
        int adMinLimit = 5;
        long duration = DateUtils.getUnixTime() - lastAdTS;
        if (adMinLimit > duration) {
            log.error("ad too fast, userId:{}, adId:{}, duration:{}, limit:{}", userId, adId, duration, adMinLimit);
            return false;
        }

        AdInfoEntity adInfoEntity = gameConfigManager.getGameConfigConfig().getAdInfoEntity(adId);
        UserExtend.AdInfo adInfo = adData.get(adId);
        adInfo.setCount(0);
        if (adInfo.getCount() >= adInfoEntity.getCount()) {
            log.error("ad count not enough, userId:{}, adId:{}, count:{}", userId, adId, adInfo.getCount());
            return false;
        }
        // 判断cd时间
        if (adInfo.getCdTS() > 0 && adInfo.getCdTS() > now + 15) {
            // 防止数据不一致 加15s的容错
            log.error("ad cd not enough, userId:{}, adId:{}, cdTS:{}", userId, adId, adInfo.getCdTS());
            return false;
        }
        adInfo.setCount(adInfo.getCount() + 1);

        // cd时间
        if (adInfoEntity.getCD() > 0) {
            adInfo.setCdTS(now + adInfoEntity.getCD() * 60L);
        }

        userExtend.setLastAdTs(0L);
        userExtendDao.updateAdInfo(userExtend);

        ResponseContext.addFillCommon("adInfo_" + adId, builder -> builder.addAdInfo(
                buildAdInfoDto(adInfo)
        ));

        return true;
    }

    public List<CommonProto.AdInfoDto> getAdInfoDtos(long userId) {
        List<CommonProto.AdInfoDto> adDtoList = Lists.newArrayList();
        Map<Integer, UserExtend.AdInfo> adData = getAdInfos(userId);
        adData.forEach((adId, adInfo) -> {
            adDtoList.add(this.buildAdInfoDto(adInfo));
        });

        return adDtoList;
    }

    public Map<Integer, UserExtend.AdInfo> getAdInfos(long userId) {
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        Map<Integer, UserExtend.AdInfo> adInfos = userExtend.getAdInfos();
        if (CollectionUtils.isNullOrEmpty(adInfos)) {
            adInfos = new HashMap<>();
        }

        boolean isUpdate = false;
        // 是否有未初始化的
        for (Map.Entry<Integer, AdInfoEntity> entry : gameConfigManager.getGameConfigConfig().getAdInfo().entrySet()) {
            if (!adInfos.containsKey(entry.getKey())) {
                UserExtend.AdInfo adInfo = new UserExtend.AdInfo();
                adInfo.setAdId(entry.getKey());
                adInfo.setCount(0);
                adInfo.setRTS(0);
                adInfo.setCdTS(0);
                adInfos.put(entry.getKey(), adInfo);
                isUpdate = true;
            }
        }

        long nowTimestamp = DateUtils.getUnixTime();
        for (Map.Entry<Integer, UserExtend.AdInfo> entry : adInfos.entrySet()) {
            if (nowTimestamp >= entry.getValue().getRTS()) {
                AdInfoEntity adInfoEntity = gameConfigManager.getGameConfigConfig().getAdInfoEntity(entry.getKey());
                entry.getValue().setCount(0);
                if (adInfoEntity.getRefresh() == 1) {
                    // 每天刷新
                    entry.getValue().setRTS(DateUtils.getSystemResetTime());
                } else if (adInfoEntity.getRefresh() == 2) {
                    // 每周刷新
                    entry.getValue().setRTS(DateUtils.getSystemWeeklyResetTime());
                } else if (adInfoEntity.getRefresh() == 3) {
                    // 每月刷新
                    entry.getValue().setRTS(DateUtils.getSystemMonthResetTime());
                }
                isUpdate = true;
            }
        }

        if (isUpdate) {
            userExtend.setAdInfos(adInfos);
            userExtendDao.updateAdInfo(userExtend);
        }
        return adInfos;
    }

    private CommonProto.AdInfoDto buildAdInfoDto(UserExtend.AdInfo adInfo) {
        AdInfoEntity adInfoEntity = gameConfigManager.getGameConfigConfig().getAdInfoEntity(adInfo.getAdId());
        return CommonProto.AdInfoDto.newBuilder()
                .setAdId(adInfo.getAdId())
                .setLeftCount(adInfoEntity.getCount() - adInfo.getCount())
                .setMaxCount(adInfoEntity.getCount())
                .setRTS(adInfo.getRTS())
                .setCdTS(adInfo.getCdTS())
                .build();
    }
}

package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.util.RequestIdUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.ProtobufUtils;
import com.dxx.game.dao.dynamodb.model.LogGmReward;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.LogGmRewardDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.SEND_AWARD_V2)
public class GMSendAwardV2 extends AbstractGMProcessor {
    @Resource
    private LogGmRewardDao logGmRewardDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private RewardService rewardService;
    @Resource
    private UserDao userDao;
    @Resource
    private PayService payService;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long userId;
        private int type;
        private Map<String, List<List<Integer>>> rewardsMap;
        private Map<String, String> preOrderMap;
    }


    @Override
    protected Object execute(JSONObject params) {

        GMRequest request = params.toJavaObject(GMRequest.class);
        if (request.getRewardsMap() == null || request.getRewardsMap().isEmpty()) {
            return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "Missing required parameter: rewardsMap");
        }

        long userId = request.getUserId();
        int type = request.getType();

        Map<String, List<List<Integer>>> rewardsMap = request.getRewardsMap();

        // 获取用户的奖励记录
        Map<String, LogGmReward> existingRewards = logGmRewardDao.getByUserIdAndUniqueIds(userId, rewardsMap.keySet());
        if (existingRewards.size() == rewardsMap.keySet().size()) {
            return this.error(GMErrorCode.ERROR_CODE_RECEIVED, "All awards have already been received");
        }

        // 预订单映射
        Map<String, String> preOrderMap = request.getPreOrderMap();
        // 初始化处理相关数据
        List<String> newUniqueIds = new ArrayList<>();
        Map<String, Long> preOrderIdMapping = new HashMap<>();
        List<List<Integer>> rewardConfigurations = new ArrayList<>();
        // 遍历 itemMap，提取奖励配置
        for (String uniqueId : rewardsMap.keySet()) {
            // 跳过已存在的奖励
            if (existingRewards.containsKey(uniqueId)) {
                continue;
            }

            // 收集未处理的 uniqueId
            newUniqueIds.add(uniqueId);

            // 处理预订单
            if (preOrderMap != null && preOrderMap.containsKey(uniqueId)) {
                preOrderIdMapping.put(uniqueId, Long.parseLong(preOrderMap.get(uniqueId)));
            }

            rewardConfigurations.addAll(rewardsMap.get(uniqueId));
        }

        // 如果没有新的奖励配置，则返回
        if (rewardConfigurations.isEmpty()) {
            return this.error(GMErrorCode.ERROR_CODE_RECEIVED, "No new awards to process");
        }
        // 解析奖励
        List<Reward> rewards = rewardService.parseRewards(rewardConfigurations);

        // 获取用户信息
        User user = userDao.getByUserId(userId);
        if (user == null) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "User does not exist");
        }

        RequestContext.setCommand((short) type);
        RequestContext.setUserId(userId);
        RequestContext.setUser(user);

        CommonProto.CommonParams.Builder commonParams = CommonProto.CommonParams.newBuilder();
        commonParams.setVersion(user.getClientNetVersion());
        RequestContext.setCommonParams(commonParams.buildPartial());

        // 加锁，防止并发请求
        if (!redisLock.lock()) {
            return this.error(GMErrorCode.ERROR_CODE_DEFAULT, "Requests are too frequent");
        }

        // 执行奖励发放
        RewardResultSet rewardResult = rewardService.executeRewards(userId, rewards);
        if (rewardResult.isFailed()) {
            log.error("Failed to send award, code: {}", rewardResult.getResultCode());
            return this.error(rewardResult.getResultCode(), "System error");
        }

        for (Map.Entry<String, Long> entry : preOrderIdMapping.entrySet()) {
            String uniqueId = entry.getKey();
            long preOrderId = entry.getValue();
            payService.iapSupplement(userId, uniqueId, preOrderId, 0, "");
        }

        for (String uniqueId : newUniqueIds) {
            LogGmReward logGmReward = new LogGmReward();
            logGmReward.setUserId(userId);
            logGmReward.setUniqueId(uniqueId);
            logGmReward.setTimestamp(DateUtils.getUnixTime());
            logGmRewardDao.update(logGmReward);
        }

        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResult);

        CommonProto.CommonData.Builder commonDataBuilder = commonData.toBuilder();
        RequestIdUtil.fillTransId(commonDataBuilder);
        commonData = commonDataBuilder.build();

        return this.success(ProtobufUtils.messageToBase64String(commonData));

    }
}

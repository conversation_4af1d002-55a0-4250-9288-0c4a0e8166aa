package com.dxx.game.common.channel.wechat.model;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/24 16:47
 */
@Data
public class WeChatPayConfig {

    // 是否启用
    private boolean open;
    // appId
    private String appId;
    // 商户ID
    private String mchId;
    // 证书序列号 -- 生成方式 ( openssl x509 -in apiclient_cert.pem -noout -serial )
    private String serialNo;
    // 获取平台证书列表URL
    private String certificatesUrl;
    // 私钥字符串
    private String privateKey;
    // api v3 密钥
    private String apiV3Key;
    // 下单地址
    private String doOrderUrl;

    // 获取token地址
    private String reqAccessTokenUrl;
    // 刷新token地址
    private String refreshTokenUrl;
    // 获取用户信息地址
    private String reqUserInfoUrl;
    private String refundUrl;

    // 包名对应的appId
    private Map<String, String> appIds;
    // app secret
    private Map<String, String> appSecrets;

    private Map<String, String> outTradeNoType;
}

package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.USER_CHANGE_DATA)
public class GMUserUpdateData extends AbstractGMProcessor {

    @Resource
    private UserDao userDao;
    @Resource
    private UserExtendDao userExtendDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long userId;
        private Map<String, String> data;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        long userId = request.getUserId();
        User user = userDao.getItemWithoutCache(userId);
        if (user == null) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
        }
        RequestContext.setUserId(userId);
        CommonHelper.buildCommonParams(user);

        Map<String, String> dataObj = request.getData();
        for (Map.Entry<String, String> entry : dataObj.entrySet()) {
            switch (entry.getKey()) {
                case "level":
                    user.setLevel(Short.parseShort(entry.getValue()));
                    userDao.updateUserLevel(user);
                    break;
                case "systemMask":
                    user.setSystemMask(entry.getValue());
                    userDao.updateSystemMask(user);
                    break;
                case "chapterId":
                    UserExtend userExtend = userExtendDao.getByUserId(userId);
                    userExtend.setChapterId(Integer.parseInt(entry.getValue()));
                    userExtendDao.update(userExtend);
                    break;
                case "chatBannedTM":
                    user.setChatBannedTM(Long.parseLong(entry.getValue()));
                    userDao.updateChatBannedTM(user);
                    break;
                default:
                    return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "params error");
            }
        }

        return this.success();
    }
}

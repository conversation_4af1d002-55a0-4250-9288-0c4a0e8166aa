package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.USER_CHECK)
public class <PERSON><PERSON>serCheck extends AbstractGMProcessor {

    @Resource
    private UserDao userDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private int searchType;
        private String searchValue;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        int searchType = request.getSearchType();
        String searchValue = request.getSearchValue();
        List<User> users = new ArrayList<>();
        User user = null;
        if (searchType == 1) {
            if (!isNumber(searchValue)) {
                return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
            }
            user = userDao.getByUserId(Long.parseLong(searchValue));
            if (user == null) {
                return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
            }
            users.add(user);
        }
        else if (searchType == 3) {
            users = userDao.getUsersByAccountId(searchValue);
        }
//        else if (searchType == 2) {
//            users = userDao.getByUsersByDeviceId(searchValue);
//        }

        if (users.isEmpty()) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
        }
        return this.success();
    }
}

package com.dxx.game.common.redis;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Request;
import com.dxx.game.consts.MsgReqCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

@XRayEnabled
@Slf4j
@Component
public class RedisLock {

    private static final Logger logger = LoggerFactory.getLogger(RedisLock.class);

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 默认过期时间 单位秒
     */
    private static final int DEFAULT_EXPIRE_MILLISECONDS = 5000;
    private static final int DEFAULT_SLEEP_MILLISECONDS = 5;

    private static final int MAX_EXPIRE_MILLISECONDS = 6000;

    /**
     * 尝试次数
     */
    private static final int TRY_COUNT = 600;

    /**
     * lock锁的key前缀
     */
    private static final String LOCK_KEY_PRE = "api_lock:";

    /**
     * 释放锁的lua脚本
     */
    private final static DefaultRedisScript<Long> UNLOCK_LUA_SCRIPT = new DefaultRedisScript<>(
            "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return -1 end"
            , Long.class
    );

    /**
     * 加锁
     * @return
     */
    public boolean lock() {
        String key = null;
        // accountId > deviceId > userId
        if (RequestContext.getAccountId() != null) {
            key = RequestContext.getAccountId();
        } else if (RequestContext.getCommonParams() != null && !StringUtils.isEmpty(RequestContext.getCommonParams().getAccountId())) {
            key = RequestContext.getCommonParams().getAccountId();
        } else if (RequestContext.getDeviceId() != null) {
            key = RequestContext.getDeviceId();
        } else if (RequestContext.getCommonParams() != null && !StringUtils.isEmpty(RequestContext.getCommonParams().getDeviceId())) {
            key = RequestContext.getCommonParams().getDeviceId();
        } else if (RequestContext.getUserId() != null) {
            key = String.valueOf(RequestContext.getUserId());
        }
        if (StringUtils.isEmpty(key)) {
            return true;
        }

        String clientId = UUID.randomUUID().toString();
        RedisLockContext.setClientId(clientId);
        RedisLockContext.setLockKey(key);
        return this.lock(key, clientId);
    }

    /**
     * 加锁
     * @param key
     * @param value
     * @return
     */
    public boolean lock(String key, String value) {
        return this.lock(key, value, TRY_COUNT, DEFAULT_SLEEP_MILLISECONDS, DEFAULT_EXPIRE_MILLISECONDS);
    }

    public boolean lock(String key, String value, int tryCount, int sleepMilliSeconds, int expireMilliSeconds) {
        try {
            while (tryCount > 0) {
                if (this.setNX(key, value, expireMilliSeconds)) {
                    RedisLockContext.putLockData(key, value);
                    return true;
                }
                Thread.sleep(sleepMilliSeconds);
                tryCount --;
            }
            return false;
        } catch (Exception e) {
            logger.error("lock error :{}", e.getMessage());
            return false;
        }
    }


    /**
     * 不重试加锁
     * @param key
     * @param value
     * @return
     */
    public boolean lockWithOutRetry(String key, String value) {
        boolean result = this.setNX(key, value);
        if (result) {
            RedisLockContext.putLockData(key, value);
        }
        return result;
    }

    /**
     * 逻辑执行完之后在 DynamoDBTransactionAspect 中会自动解锁
     */
    public boolean lockWithOutRetry(String key, String value, long expire) {
        boolean result = this.setNX(key, value, expire);
        if (result) {
            RedisLockContext.putLockData(key, value);
        }
        return result;
    }

    /**
     * 不需要自动解锁
     * @param key
     * @param value
     * @param expire
     * @return
     */
    public boolean tryLockOnce(String key, String value, long expire) {
        return this.setNX(key, value, expire);
    }

    /**
     * 释放锁
     */
    public void unlock() {
        if (RedisLockContext.getClientId() == null || RedisLockContext.getLockKey() == null) {
            return;
        }
        this.unlock(RedisLockContext.getLockKey(), RedisLockContext.getClientId());
    }

    /**
     * 在事务提交之后释放锁
     */
    public void unLockAfterTransaction() {
        if (RedisLockContext.getLockData() != null) {
            RedisLockContext.getLockData().forEach(this::unlock);
        }
    }

    /**
     * 释放锁
     * @param key
     * @param value
     */
    public void unlock(String key, String value) {
        String lockKey = LOCK_KEY_PRE + key;
        redisTemplate.execute(UNLOCK_LUA_SCRIPT, Collections.singletonList(lockKey), value);
    }

    public boolean setNX(final String key, final String value) {
        return this.setNX(key, value, DEFAULT_EXPIRE_MILLISECONDS);
    }

    public boolean setNX(final String key, final String value, long expireMillisSeconds) {
        try {
            String lockKey = LOCK_KEY_PRE + key;
            return redisTemplate.opsForValue().setIfAbsent(lockKey, value, expireMillisSeconds, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            logger.error("set nx failed, key:{}, error: {}", key, e.getMessage());
            return false;
        }
    }

}















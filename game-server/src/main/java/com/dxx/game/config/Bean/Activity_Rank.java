
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Activity_Rank {
    public Activity_Rank(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        GroupType = _buf.get("GroupType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Rank").getAsJsonArray(); Rank = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Rank.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        Style = _buf.get("Style").getAsInt();
    }

    public static Activity_Rank deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Activity_Rank(_buf);
    }

    /**
     * id
     */
    public final int Id;
    /**
     * 活动组类型
     */
    public final int GroupType;
    /**
     * 排名区间
     */
    public final java.util.List<Integer> Rank;
    /**
     * 奖励
     */
    public final java.util.List<String> Reward;
    /**
     * 展示风格（1：正常展示、2：展示为RankStar+）
     */
    public final int Style;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "GroupType:" + GroupType + ","
        + "Rank:" + Rank + ","
        + "Reward:" + Reward + ","
        + "Style:" + Style + ","
        + "}";
    }
}


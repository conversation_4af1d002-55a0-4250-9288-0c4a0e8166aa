package com.dxx.game.modules.im.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @author: lsc
 * @createDate: 2025/5/30
 * @description:
 */
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLoginResponse extends CallbackResponse {
    private int code;
    private String message;
    private long userId;
    private String loginDeviceId;
    private String serverIMGroupId;
    private String guildIMGroupId;
    private String crossServerIMGroupId;
    private String globalIMGroupId;
}

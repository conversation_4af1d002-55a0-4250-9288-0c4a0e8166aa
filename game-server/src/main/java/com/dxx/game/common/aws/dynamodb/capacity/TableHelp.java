package com.dxx.game.common.aws.dynamodb.capacity;

import com.dxx.game.common.aws.dynamodb.model.DynamoDBModelKeyInfo;
import com.dxx.game.common.aws.dynamodb.model.mapper.DynamoDBMapperRegistry;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2024/11/11
 * @description:
 */
@Slf4j
@Repository
public class TableHelp {

    private static Map<String, TableConfig> tableMap = new HashMap<>();
    private static List<TableConfig> tableList = new ArrayList<>();

    @PostConstruct
    private void init() {
        try {
            for (Map.Entry<String, DynamoDBModelKeyInfo> entry : DynamoDBMapperRegistry.getModelKeyInfoMap().entrySet()) {
                DynamoDBModelKeyInfo value = entry.getValue();
                TableConfig tableConfig = new TableConfig();
                tableConfig.tableName = value.getTableName();
                tableConfig.pkName = value.getPkFieldName();
                if (!StringUtils.isEmpty(value.getSkFieldName())) {
                    tableConfig.skName = value.getSkFieldName();
                }
                tableList.add(tableConfig);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static TableConfig getTableConfig(String name) {
        var config = tableMap.get(name);
        if (config != null) {
            return config;
        }
        for (var table : tableList) {
            if (name.equals(table.tableName)) {
                tableMap.put(table.tableName, table);
                config = table;
                break;
            }
        }
        return config;
    }

    @ToString
    public static class TableConfig {
        public String tableName;
        public String pkName;
        public String skName;
    }
}

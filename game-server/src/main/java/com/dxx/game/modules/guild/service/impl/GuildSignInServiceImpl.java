package com.dxx.game.modules.guild.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBWriteType;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guild.GuildSignInEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildMessage;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildMessageDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.guild.consts.GuildTaskType;
import com.dxx.game.modules.guild.model.GuildTaskProcess;
import com.dxx.game.modules.guild.service.GuildSignInService;
import com.dxx.game.modules.guild.service.GuildTaskService;
import com.dxx.game.modules.guild.support.GuildFeaturesSupport;
import com.dxx.game.modules.guild.support.GuildSupport;
import com.dxx.game.modules.message.service.MessageService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dxx.game.dto.GuildProto.*;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@Slf4j
@Service
public class GuildSignInServiceImpl implements GuildSignInService {

    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RewardService rewardService;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildFeaturesSupport guildFeaturesSupport;
    @Resource
    private GuildTaskService guildTaskService;
    @Resource
    private GuildSupport guildSupport;
    @Resource
    private GuildMessageDao guildMessageDao;
    @Resource
    private UserService userService;
    @Resource
    private MessageService messageService;


    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildSignInResponse> signInAction(GuildSignInRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        int limit = gameConfigManager.getGuildConfig().getGuildSignIn().size();
        if (guildUser.getSignInCnt() >= limit) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int key = guildUser.getSignInCnt() + 1;
        if (key > limit) {
            key = limit;
        }
        GuildSignInEntity guildSignInEntity = gameConfigManager.getGuildConfig().getGuildSignInEntity(key);
        int needItemId = guildSignInEntity.getNeedItemId();
        int needItemCount = guildSignInEntity.getNeedItemCount();
        RewardResultSet costResultSet = null;
        if (needItemId > 0) {
            List<Integer> costConfig = Lists.newArrayList(needItemId, -needItemCount);
            costResultSet = rewardService.executeReward(userId, costConfig);
            if (costResultSet.isFailed()) {
                return Result.Error(costResultSet.getResultCode());
            }
        }

        List<List<Integer>> rewards = new ArrayList<>();
        rewards.addAll(guildSignInEntity.getReward());
        rewards.addAll(guildSignInEntity.getOtherReward());
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        // 更新数据
        guildUser.setSignInCnt(guildUser.getSignInCnt() + 1);
        guildUserDao.updateSignIn(guildUser);

        // 完成公会任务
        GuildTaskProcess guildTaskProcess = GuildTaskProcess.valueOf(GuildTaskType.SIGN_IN, 1);
        guildTaskService.updateTask(userId, guildTaskProcess);

        User user = userService.getUser(userId);
        // 保存捐献记录
        long msgId = messageService.generateGuildMsgId(guildUser.getGuildId());
        String nickName = Optional.ofNullable(user.getNickName()).orElse("");
        JSONObject recordObj = new JSONObject();
        recordObj.put("userId", user.getUserId());
        recordObj.put("nickName", nickName);
        recordObj.put("itemId", needItemId);
        recordObj.put("timestamp", DateUtils.getUnixTime());
        String messageContent = recordObj.toJSONString();
        guildMessageDao.addGuildSignInRecord(guildUser.getGuildId(), msgId, messageContent);

        GuildUpdateInfoDto guildUpdateInfoDto = guildFeaturesSupport.buildGuildUpdateInfo(guildUser.getGuildId());

        GuildSignInResponse.Builder response = GuildSignInResponse.newBuilder();
        List<GuildTaskDto> taskDtos = guildTaskService.getGuildTaskListAfterOperation(guildUser);
        if (taskDtos != null) {
            response.addAllTasks(taskDtos);
        }
        response.setCommonData(CommonHelper.buildCommonData(costResultSet, rewardResultSet));
        response.setSignInDto(this.buildSignInDto(guildUser));
        response.setUserDailyActive(guildUser.getDailyActive());
        response.setUserWeeklyActive(guildUser.getWeeklyActive());
        response.setGuildUpdateInfo(guildUpdateInfoDto);
        response.setSignInRecord(messageContent);
        return Result.Success(response.build());
    }

    @Override
    public void refreshData(GuildUser guildUser) {
        if (guildUser.getDailyTM() != null && guildUser.getDailyTM() >= DateUtils.getUnixTime()) {
            return;
        }
        guildUser.setSignInCnt(0);
        guildUserDao.updateSignIn(guildUser);
    }

    @Override
    public GuilSignInDto buildSignInDto(GuildUser guildUser) {
        int limit = gameConfigManager.getGuildConfig().getGuildSignIn().size();
        int key = guildUser.getSignInCnt() + 1;
        if (key > limit) {
            key = limit;
        }
        GuildSignInEntity guildSignInEntity = gameConfigManager.getGuildConfig().getGuildSignInEntity(key);
        GuilSignInDto.Builder result = GuilSignInDto.newBuilder();
        result.setCount(guildUser.getSignInCnt());
        result.setLimit(limit);
        result.setNeedItemId(guildSignInEntity.getNeedItemId());
        result.setNeedItemCount(guildSignInEntity.getNeedItemCount());
        result.addAllRewards(CommonHelper.buildRewardDtoList(guildSignInEntity.getReward()));
        return result.build();
    }

    @Override
    public List<String> getSignInRecords(Guild guild) {
        List<String> result = new ArrayList<>();
        List<GuildMessage> guildMessages = guildMessageDao.querySignInRecords(guild.getGuildId(), 20);
        int size = guildMessages.size();
        for (int i = size - 1; i >= 0; i --) {
            result.add(guildMessages.get(i).getMessageContent());
        }
        return result;
    }
}











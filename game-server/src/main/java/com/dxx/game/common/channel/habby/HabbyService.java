package com.dxx.game.common.channel.habby;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.modules.gm.service.HabbyApiService;
import io.netty.handler.codec.http.FullHttpRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * @authoer: lsc
 * @createDate: 2022/6/28
 * @description:
 */
@Slf4j
@Service
public class HabbyService implements ChannelService {

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private HabbyApiService habbyIDService;
    @Resource
    private GameConfigManager gameConfigManager;

    @Override
    public Object payCb(FullHttpRequest request) {
        return null;
    }

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        if (!channelConfig.getHabbyConfig().isHabbyVerifyLogin()) {
            return true;
        }
        String decodeUserId = this.analysisUserId(verification);
        if (!accountId.equals(decodeUserId)) {
            return false;
        }
        return true;
    }

    private String analysisUserId(String verification) {
        try {
            String key = channelConfig.getHabbyConfig().getHabbyVerifyLoginKey();
            IvParameterSpec iv = new IvParameterSpec(key.getBytes("UTF-8"));
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] original = cipher.doFinal(Base64.decodeBase64(verification));
            String data =  new String(original);
            JSONObject jsonObject = JSONObject.parseObject(data);
            if (jsonObject.containsKey("newUserFrom")) {
                RequestContext.setNewUserFrom(jsonObject.getString("newUserFrom"));
            }
            return jsonObject.getString("userId");
        } catch (Exception ex) {
            log.error("habby login failed, verification:{}, e:", verification, ex);
            return null;
        }
    }

//    public JSONObject bindHabbyId(String accountPK, HabbyAuthResult authResult, Map<String, String> bindParams) {
//        String gameId = gameConfigManager.getMainConfig().getHabbyBindApiConfig().getGame_id();
//        Map<String, String> header = new HashMap<>();
//        header.put("Content-Type", "application/json");
//        header.put("Authorization", "Bearer " + authResult.getData().getAccessToken());
//        header.put("App-Language", bindParams.get("language"));
//        JSONObject postJson = new JSONObject();
//
//        postJson.put("tgaDeviceId", bindParams.get("tgaDeviceId"));
//        postJson.put("tgaDistinctId", bindParams.get("tgaDistinctId"));
//        postJson.put("gameId", gameId);
//        postJson.put("gameAccountId", accountPK);
//
//        String response = OkHttpClientUtil.postJson(gameConfigManager.getHabbyApiUrl() + "/bind", header, postJson);
//        return JSONObject.parseObject(response);
//    }

    public int unbindHabbyId(String accountPK, String habbyId) {
//        String gameId = gameConfigManager.getMainConfig().getHabbyBindApiConfig().getGame_id();
//        Map<String, String> header = new HashMap<>();
//        header.put("Content-Type", "application/json");
//        Map<String, String> param = new HashMap<>();
//        param.put("gameId", gameId);
//        param.put("gameAccountId", accountPK);
//        param.put("timestamp", DateUtils.getUnixTime() + "");
//        param.put("habbyId", habbyId);
//        param.put("sign", habbyStoreService.genSign(param, habbyStoreService.getHabbyStoreSecretKey()));
//        String response = OkHttpClientUtil.postJson(gameConfigManager.getHabbyApiUrl() + "/unbind", header, param);
//        var obj = JSONObject.parseObject(response);
//        var code = obj.getIntValue("code");
//        if (code > 0) {
//            log.error("habby unbind failed, response:{}", response);
//        }
//        return code;
        return ErrorCode.FAIL;
    }


    public static void main(String[] args) {
        String data = "Gp9/A1rqIT1bp1c2/dPG6z4qJTQZU9Ab1UxzHMvc6XboXWALs1kfMVM1JaI5L77cwQNgnhXjdUZ9ZixjpFjoqykZxDjQJ2kOezITTBAM0ls=";
        HabbyService service = new HabbyService();
        System.out.println(service.analysisUserId(data));
    }
}

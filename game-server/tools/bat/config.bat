@echo off
REM 获取当前脚本所在目录
set WORKSPACE=%~dp0
set WORKSPACE=%WORKSPACE:~0,-1%

REM 调用 gen.bat 脚本
call %WORKSPACE%\..\config\luban\Server\gen.bat

REM 同步 output_code 文件到目标目录
xcopy /E /Y /Q %WORKSPACE%\..\config\luban\Server\output_code\* %WORKSPACE%\..\..\src\main\java\com\dxx\game\config\
for /R %WORKSPACE%\..\config\luban\Server\output_code\ %%F in (*) do del "%%F"

REM 同步 output_json 文件到目标目录
xcopy /E /Y /Q %WORKSPACE%\..\config\luban\Server\output_json\* %WORKSPACE%\..\..\gameconf\
for /R %WORKSPACE%\..\config\luban\Server\output_json\ %%F in (*) do del "%%F"

echo 完成！
pause

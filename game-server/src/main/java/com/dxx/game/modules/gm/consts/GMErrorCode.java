package com.dxx.game.modules.gm.consts;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
public interface GMErrorCode {

    // 成功
    int SUCCESS = 0;
    // 其他异常
    int ERROR_CODE_DEFAULT = -1;
    // 参数错误
    int ERROR_CODE_PARAMS_ERROR = 100;
    // 系统异常
    int ERROR_CODE_SYSTEM_ERROR = 101;
    // 用户不存在
    int ERROR_CODE_USER_NOT_EXIST = 102;
    // 奖励已领取
    int ERROR_CODE_RECEIVED = 103;
    // 没有奖励
    int ERROR_CODE_NO_REWARD = 104;
    // 公会不存在
    int ERROR_CODE_GUILD_NOT_EXIST = 105;
}

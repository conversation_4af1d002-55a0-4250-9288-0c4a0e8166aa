package com.dxx.game.common.channel.wechatminigame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.wechat.WeChatService;
import com.dxx.game.common.channel.wechat.model.WeChatCertificateVO;
import com.dxx.game.common.channel.wechatminigame.model.WeChatJsApiUnifiedOrderResult;
import com.dxx.game.common.channel.wechat.model.WeChatNotifyResourceVO;
import com.dxx.game.common.channel.wechat.model.WeChatPayNotifyVO;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameJsApiConfig;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.DateUtils;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: lsc
 * @createDate: 2023/8/21
 * @description:
 */
@Slf4j
@DependsOn("okHttpClient")
@Component
public class WeChatMiniGameJsApiService implements ChannelService {

    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private WeChatService weChatService;

    private WeChatMiniGameJsApiConfig weChatMiniGameJsApiConfig;

    private static final ChannelID CHANNEL_ID = ChannelID.WeChatJS;

    // 私钥
    private PrivateKey privateKey = null;

    // 请求证书列表URL
    private HttpUrl certificatesUrl = null;
    // 下单URL
    private HttpUrl doOrderUrl = null;

    private final ConcurrentHashMap<String, X509Certificate> certificateMap = new ConcurrentHashMap<>();

    private static final String REDIS_TOKEN_KEY = "wechat_mini_game_js_api_token";

    @Resource
    private RedisService redisService;
    @Resource
    private AndroidCallBackService androidCallBackService;

    @PostConstruct
    private void init() {
        try {
            weChatMiniGameJsApiConfig = channelConfig.getWeChatMiniGameJsApiConfig();
            if (weChatMiniGameJsApiConfig == null || !weChatMiniGameJsApiConfig.isOpen()) {
                return;
            }

            certificatesUrl = HttpUrl.parse(channelConfig.getWeChatPayConfig().getCertificatesUrl());
            doOrderUrl = HttpUrl.parse(weChatMiniGameJsApiConfig.getDoOrderUrl());

            initPrivateKey();

            refreshCertificates();
        } catch (Exception e) {
            log.error("init weChat failed", e);
            System.exit(0);
        }
    }

    /**
     * 下单接口  https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_1.shtml
     */
    public WeChatJsApiUnifiedOrderResult unifiedOrder(long userId, String weChatJSOpenId, String description, String attach, int amount) {
        String appId = weChatMiniGameJsApiConfig.getAppId();
        String mchId = weChatMiniGameJsApiConfig.getMchId();
        boolean isWhiteUser = channelConfig.isPayWhiteList(userId);
        if (isWhiteUser) {
            // 测试账号下单固定1分钱
            amount = 1;
        }
//        appId = channelConfig.getWeChatMiniGameConfig().getAppId();
//        mchId = channelConfig.getWeChatMiniGameConfig().getOfferId();

        String notifyUrl = channelConfig.getNotifyUrl(CHANNEL_ID);
        String cpOrderId = channelConfig.getGameName() + "-" + PaymentUtils.createCpOrderId();
        if (cpOrderId.length() > 32) {
            cpOrderId = cpOrderId.substring(0, 32);
        }

        Map<String, Object> postBody = new HashMap<>();
        postBody.put("appid", appId);
        postBody.put("mchid", mchId);
        postBody.put("description", description);
        postBody.put("out_trade_no", cpOrderId);
        postBody.put("attach", attach);
        postBody.put("notify_url", notifyUrl);
        Map<String, Object> amountObj = new HashMap<>();
        amountObj.put("total", amount);
        postBody.put("amount", amountObj);
        Map<String, Object> payerObj = new HashMap<>();
        payerObj.put("openid", weChatJSOpenId);
        postBody.put("payer", payerObj);

        try {
            String respData = this.doRequest("POST", this.doOrderUrl, postBody);
            if (StringUtils.isEmpty(respData)) {
                return null;
            }
            JSONObject jsonObject = JSON.parseObject(respData);
            String prepayId = jsonObject.getString("prepay_id");
            WeChatJsApiUnifiedOrderResult result = new WeChatJsApiUnifiedOrderResult();
            result.setPrePayId(prepayId);
            return result;
        } catch (Exception e) {
            log.error("doOrder error", e);
            return null;
        }
    }

    /**
     * 跳转到 JSAPI 微信支付页面
     */
    public Map<String, String> jsapiPay(FullHttpRequest fullRequest) throws Exception {
        Map<String, String> params = HttpRequester.getParams(fullRequest);
        Map<String, String> result = new HashMap<>();
        if (params.isEmpty()) {
            log.error("jsapiPay params is empty");
            result.put("code", "-1");
            result.put("msg", "订单已失效, 请重新下单");
            return result;
        }

        log.info("jsapiPay params:{}", params);

        String code = params.get("code");
        String state = params.get("state");
        JSONObject stateJson = JSON.parseObject(state);
        long userId = stateJson.getLongValue("userId");
        String attach = stateJson.getString("attach");
        String productId = stateJson.getString("productId");
        String env = stateJson.getString("env");

        String reqAccessTokenUrl = weChatMiniGameJsApiConfig.getReqAccessTokenUrl();
        String appId = weChatMiniGameJsApiConfig.getAppId();
        String appSecret = weChatMiniGameJsApiConfig.getAppSecret();
        reqAccessTokenUrl = String.format(reqAccessTokenUrl, appId, appSecret, code);
        ReqOpenIdResult reqOpenIdResult = this.reqOpenId(reqAccessTokenUrl, 0);
        if (reqOpenIdResult.getErrcode() != 0) {
            log.error("reqOpenId failed reqOpenIdResult:{}", reqOpenIdResult);
            if (reqOpenIdResult.getErrcode() == 40163 || reqOpenIdResult.getErrcode() == 40029) {
                result.put("code", "-1");
                result.put("msg", "订单已失效, 请重新下单");
                return result;
            }
            return null;
        }

        String openId = reqOpenIdResult.getOpenId();

        int amount = androidCallBackService.getIapAmount(productId);


        String desc = androidCallBackService.getIapName(productId);
        WeChatJsApiUnifiedOrderResult unifiedOrderResult = this.unifiedOrder(userId, openId, desc, attach, amount * 100);
        String preOrderId = unifiedOrderResult.getPrePayId();

        // 商品描述
        String productDesc = URLEncoder.encode(desc, "UTF-8");
        int productPrice = amount;

        long timeStamp = DateUtils.getUnixTime();
        String nonceStr = UUID.randomUUID().toString();
        String packageStr = "prepay_id=" + preOrderId;
        String signType = "RSA";
        String message = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageStr + "\n";
        String paySign = buildSign(message.getBytes(StandardCharsets.UTF_8));
        paySign = URLEncoder.encode(paySign, "UTF-8");
//        log.info("message: {}, paySign: {}", message, paySign);
        String gameName = URLEncoder.encode(channelConfig.getGameNameCn(), "UTF-8");

        String[] payStaticUrls = weChatMiniGameJsApiConfig.getPayStaticUrl();
        String payStaticUrl;
        if (channelConfig.isTest()) {
            payStaticUrl = payStaticUrls[0];
        } else {
            payStaticUrl = payStaticUrls[1];
        }


        PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(attach);
        long cpPreOrderId = payBackParamsModel.getPreOrderId();
        payStaticUrl = String.format(payStaticUrl, gameName, productPrice, productDesc, appId, timeStamp, nonceStr, packageStr, signType, paySign, cpPreOrderId, env, userId, productId);
        log.info("jsapiPay payStaticUrl: {}", payStaticUrl);

        result.put("code", "0");
        result.put("url", payStaticUrl);
        return result;
    }

    private String reqUnifiedOrder(String openId, long userId, String attach, String productId, String notifyHost) {
        JSONObject params = new JSONObject();
        params.put("channelId", CHANNEL_ID.getId());
        params.put("wxOfficialAccountOpenId", openId);
        params.put("userId", userId);
        params.put("attach", attach);
        params.put("productId", productId);

        String unifiedOrderUrl = notifyHost + "/pay/preOrder";
        JSONObject resp = OkHttpClientUtil.postJson(unifiedOrderUrl, null, params, JSONObject.class);
        log.info("reqUnifiedOrder params = {}, resp = {}", params, resp);
        return resp.getJSONObject("data").getJSONObject("weChatJSOrderDto").getString("prePayId");
    }

    // 初始化私钥
    private void initPrivateKey() {
        try {
            byte[] keyBytes = Base64.getMimeDecoder().decode(weChatMiniGameJsApiConfig.getPrivateKey());
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void refreshCertificates() throws Exception {
        String respData = this.doRequest("GET", this.certificatesUrl, null);
        JSONObject jsonObject = JSON.parseObject(respData);
        List<WeChatCertificateVO> chatCertificateVOList = JSON.parseArray(jsonObject.getString("data"), WeChatCertificateVO.class);

        for (WeChatCertificateVO weChatCertificateVO : chatCertificateVOList) {
            String publicKey = this.decryptResponseBody(weChatCertificateVO.getEncrypt_certificate().getAssociated_data(),
                    weChatCertificateVO.getEncrypt_certificate().getNonce(), weChatCertificateVO.getEncrypt_certificate().getCiphertext());

            CertificateFactory certificateFactory = CertificateFactory.getInstance("X509");

            // 获取证书
            ByteArrayInputStream inputStream = new ByteArrayInputStream(publicKey.getBytes(StandardCharsets.UTF_8));
            X509Certificate x509Certificate = null;
            x509Certificate = (X509Certificate) certificateFactory.generateCertificate(inputStream);
            certificateMap.put(weChatCertificateVO.getSerial_no(), x509Certificate);
        }
    }

    private String buildToken(String method, HttpUrl httpUrl, String body) throws Exception {
        String nonceStr = UUID.randomUUID().toString();
        long timestamp = DateUtils.getUnixTime();
        String message = this.buildMessage(method, httpUrl, timestamp, nonceStr, body);

        String signature = this.buildSign(message.getBytes(StandardCharsets.UTF_8));
        String token = "WECHATPAY2-SHA256-RSA2048 mchid=\"" + weChatMiniGameJsApiConfig.getMchId() + "\","
                + "nonce_str=\"" + nonceStr + "\","
                + "timestamp=\"" + timestamp + "\","
                + "serial_no=\"" + weChatMiniGameJsApiConfig.getSerialNo() + "\","
                + "signature=\"" + signature + "\"";
        return token;
    }

    // sign
    private String buildSign(byte[] message) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message);
        return Base64.getEncoder().encodeToString(signature.sign());
    }

    private String buildMessage(String method, HttpUrl httpUrl, long timestamp, String nonceStr, String body) {
        String canonicalUrl = httpUrl.encodedPath();
        if (httpUrl.encodedQuery() != null) {
            canonicalUrl += "?" + httpUrl.encodedQuery();
        }

        return method + "\n" + canonicalUrl + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";
    }

    private String doRequest(String method, HttpUrl httpUrl, Map<String, Object> params) throws Exception {
        String body = "";
        if (params != null) {
            body = JSON.toJSONString(params);
        }

        String token = this.buildToken(method, httpUrl, body);
        Map<String, String> headerParameter = new HashMap<>();
        headerParameter.put("Content-Type", "application/json;charset=UTF-8");
        headerParameter.put("Accept", "application/json");
        headerParameter.put("Authorization", token);

        if (method.equals("GET")) {
            return OkHttpClientUtil.get(httpUrl.toString(), headerParameter, null, String.class);
        } else {
            return OkHttpClientUtil.postJson(httpUrl.toString(), headerParameter, params, String.class);
        }
    }

    private String decryptResponseBody(String associatedData, String nonce, String ciphertext) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        SecretKeySpec key = new SecretKeySpec(weChatMiniGameJsApiConfig.getApiV3Key().getBytes(StandardCharsets.UTF_8), "AES");
        GCMParameterSpec spec = new GCMParameterSpec(128, nonce.getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.DECRYPT_MODE, key, spec);
        cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));



        return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)));

    }


    @Override
    public Object payCb(FullHttpRequest request) {
        String body = request.content().toString(StandardCharsets.UTF_8);
        try {
            PayCbVo payCbVo = this.checkOrderState(request, body);
            if (payCbVo == null) {
                log.error("checkOrderState failed, body:{}", body);
                return this.error("ERROR", "checkOrderState failed");
            }

            int code = androidCallBackService.doDeliverGoods(payCbVo);
            if (code != AndroidPayCbErrorCode.SUCCESS) {
                log.error("doDeliverGoods failed, body:{}", body);
                return this.error("ERROR", "code = " + code);
            }

            return this.success();

        } catch (Exception e) {
            log.error("payCb failed", e);
            return this.error("EXCEPTION", "payCb failed");
        }
    }

    private PayCbVo checkOrderState(FullHttpRequest request, String body) throws Exception {
        if (verifiedSign(request, body)) {
            WeChatPayNotifyVO payNotifyVO = JSONObject.parseObject(body, WeChatPayNotifyVO.class);
            if (!payNotifyVO.getEvent_type().equals("TRANSACTION.SUCCESS")) {
                log.error("event_type error:{}", body);
                return null;
            }

            // 通知资源数据
            WeChatPayNotifyVO.Resource resource = payNotifyVO.getResource();
            // 解密后的数据
            String notifyResourceStr = this.decryptResponseBody(resource.getAssociated_data(), resource.getNonce(), resource.getCiphertext());

            WeChatNotifyResourceVO notifyResourceVO = JSONObject.parseObject(notifyResourceStr, WeChatNotifyResourceVO.class);
            if (!notifyResourceVO.getTrade_state().equals("SUCCESS")) {
                log.error("trade_state error:{}", notifyResourceVO);
                return null;
            }


            String attach = notifyResourceVO.getAttach();
            PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(attach);

            PayCbVo payCbVo = new PayCbVo();
            payCbVo.setUserId(payBackParamsModel.getUserId());
            payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
            payCbVo.setOrderId(notifyResourceVO.getTransaction_id());
            payCbVo.setCpOrderId(notifyResourceVO.getOut_trade_no());
            payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
            payCbVo.setProductId(payBackParamsModel.getProductId());
            payCbVo.setChannelId(CHANNEL_ID.getId());
            payCbVo.setAmount(notifyResourceVO.getAmount().getTotal());
            payCbVo.setExtra(notifyResourceVO.getAmount().getPayer_total());

            return payCbVo;
        } else {
            return null;
        }
    }

    /**
     * 验证微信回调签名
     */
    private boolean verifiedSign(FullHttpRequest request, String body) throws Exception {
        //微信返回的证书序列号
        String serialNo = request.headers().get("Wechatpay-Serial");
        //微信返回的随机字符串
        String nonceStr = request.headers().get("Wechatpay-Nonce");
        //微信返回的时间戳
        String timestamp = request.headers().get("Wechatpay-Timestamp");
        //微信返回的签名
        String weChatSign = request.headers().get("Wechatpay-Signature");

        if (serialNo == null) {
            return false;
        }

        //组装签名字符串
        String signStr = Stream.of(timestamp, nonceStr, body)
                .collect(Collectors.joining("\n", "", "\n"));

        if (certificateMap.isEmpty() || !certificateMap.containsKey(serialNo)) {
            this.refreshCertificates();
        }

        X509Certificate certificate = certificateMap.get(serialNo);
        if (certificate == null) {
            log.error("certificate not exist serialNo:{}", serialNo);
            return false;
        }

        //SHA256withRSA签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initVerify(certificate);
        signature.update(signStr.getBytes());
        //返回验签结果
        return signature.verify(Base64.getDecoder().decode(weChatSign));
    }

    private ReqOpenIdResult reqOpenId(String url, int retryCount) {
        String response = OkHttpClientUtil.get(url, String.class);
        JSONObject respObj = JSONObject.parseObject(response);
        int errCode = respObj.getIntValue("errcode");

        ReqOpenIdResult reqOpenIdResult = new ReqOpenIdResult();
        reqOpenIdResult.setErrcode(errCode);
        if (errCode == 0) {
            reqOpenIdResult.setOpenId(respObj.getString("openid"));
            return reqOpenIdResult;
        }

        if (errCode == -1 && retryCount < 3) {
            log.error("reqOpenId failed, retryCount:{}, resp:{}", retryCount, response);
            return reqOpenId(url, retryCount + 1);
        }

        reqOpenIdResult.setErrcode(errCode);
        reqOpenIdResult.setErrorMsg(respObj.getString("errmsg"));
        log.error("reqOpenId failed, code:{}, resp:{}", errCode, response);
        return reqOpenIdResult;
    }

    // 成功返回
    private Map<String, String> success() {
        Map<String, String> result = new HashMap<>();
        result.put("code", "SUCCESS");
        result.put("message", "");
        return result;
    }

    // 失败返回
    private Map<String, String> error(String code, String msg) {
        Map<String, String> result = new HashMap<>();
        result.put("code", code);
        result.put("message", msg);
        return result;
    }

    @Data
    private static class ReqOpenIdResult {
        private int errcode;
        private String openId;
        private String errorMsg;
    }


    public Map<String, Object> checkOrderExist(JSONObject params) {
        long userId = params.getLongValue("userId");
        long preOrderId = params.getLongValue("preOrderId");

        return androidCallBackService.queryOrder(userId, preOrderId);
    }
}

package com.dxx.game.modules.im.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/11
 * @description:
 */
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserActiveTimeResponse extends CallbackResponse {
    private int code;
    private String message;
    private Map<Long, Long> data;
}

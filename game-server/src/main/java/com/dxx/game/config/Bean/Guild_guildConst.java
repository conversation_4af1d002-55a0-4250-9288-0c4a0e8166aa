
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildConst {
    public Guild_guildConst(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Notes = _buf.get("Notes").getAsString();
        TypeInt = _buf.get("TypeInt").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("TypeIntArray").getAsJsonArray(); TypeIntArray = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  TypeIntArray.add(_v0); }   }
    }

    public static Guild_guildConst deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildConst(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * 备注
     */
    public final String Notes;
    /**
     * 属性值
     */
    public final int TypeInt;
    /**
     * 属性值-int数组
     */
    public final java.util.List<Integer> TypeIntArray;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Notes:" + Notes + ","
        + "TypeInt:" + TypeInt + ","
        + "TypeIntArray:" + TypeIntArray + ","
        + "}";
    }
}


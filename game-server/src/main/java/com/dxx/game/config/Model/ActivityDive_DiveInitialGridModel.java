
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ActivityDive_DiveInitialGridModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid> _dataList;
    
    public ActivityDive_DiveInitialGridModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid _v;
            _v = com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ActivityDive_DiveInitialGrid get(int key) { return _dataMap.get(key); }

}

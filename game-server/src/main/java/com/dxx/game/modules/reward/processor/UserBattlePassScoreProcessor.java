package com.dxx.game.modules.reward.processor;

import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.model.UserBattlePassScoreReward;
import com.dxx.game.modules.reward.result.RewardResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description : TODO
 * <AUTHOR> wzy
 * @Date : 2022/6/22 20:03
 **/
@Component
public class UserBattlePassScoreProcessor implements RewardProcessor {

    private static final Logger logger = LoggerFactory.getLogger(UserBattlePassScoreProcessor.class);
    @Autowired
    private ShopDao shopDao;


    @Override
    public RewardType getType() {
        return RewardType.RESOURCE;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.BATTLE_PASS_SCORE;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        if (iapModel == null) {
            resultCode = ErrorCode.QUERY_USER_DATA_ERROR;
        } else {
            if (reward.getCount() <= 0) {
                logger.error("add user battle pass score count error ,value : {}", reward.getCount());
                resultCode = ErrorCode.PARAMS_ERROR;
            }
        }
        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        int addScore = rewardAction.getReward().getCount();
        Shop.IAPBattlePassModel battlePassModel = iapModel.getBattlePassModel();
        int battlePassModelScore = battlePassModel.getScore();
        battlePassModel.setScore(battlePassModelScore + addScore);
        shopDao.updateIap(shop);
        RewardResult<Object> result = new RewardResult<>(this.getType(), this.getRewardResourceType());
        UserBattlePassScoreReward userBattlePassScoreReward = UserBattlePassScoreReward.valueOf(battlePassModel.getScore());
        result.setActualCount(addScore);
        result.setCurrent(userBattlePassScoreReward);
        return result;
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Guild_guildLanguageModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildLanguage> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.Guild_guildLanguage> _dataList;
    
    public Guild_guildLanguageModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildLanguage>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Guild_guildLanguage>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Guild_guildLanguage _v;
            _v = com.dxx.game.config.Bean.Guild_guildLanguage.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Guild_guildLanguage> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.Guild_guildLanguage> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Guild_guildLanguage get(int key) { return _dataMap.get(key); }

}

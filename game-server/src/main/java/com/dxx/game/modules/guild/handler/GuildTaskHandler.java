package com.dxx.game.modules.guild.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.modules.guild.service.GuildTaskService;
import com.google.protobuf.Message;
import com.dxx.game.dto.GuildProto.*;
import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@ApiHandler
public class GuildTaskHandler {

    @Resource
    private GuildTaskService guildTaskService;

    @ApiMethod(command = MsgReqCommand.GuildTaskRewardRequest, name = "公会-任务-领取奖励")
    public Result<GuildTaskRewardResponse> taskReward(Message msg) {
        GuildTaskRewardRequest params = (GuildTaskRewardRequest)msg;
        return guildTaskService.taskRewardAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildTaskRefreshRequest, name = "公会-任务-刷新")
    public Result<GuildTaskRefreshResponse> refreshTask(Message msg) {
        GuildTaskRefreshRequest params = (GuildTaskRefreshRequest)msg;
        return guildTaskService.refreshTaskAction(params);
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class MainLevelReward_AFKreward {
    public MainLevelReward_AFKreward(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        RewardLevel = _buf.get("RewardLevel").getAsInt();
        RequiredLevel = _buf.get("RequiredLevel").getAsInt();
        HangGold = _buf.get("HangGold").getAsInt();
        HangDust = _buf.get("HangDust").getAsInt();
        HangHeroExp = _buf.get("HangHeroExp").getAsInt();
        HangDiamond = _buf.get("HangDiamond").getAsInt();
        HangRewards = _buf.get("HangRewards").getAsInt();
    }

    public static MainLevelReward_AFKreward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.MainLevelReward_AFKreward(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 奖励等级
     */
    public final int RewardLevel;
    /**
     * 要求的战役章节
     */
    public final int RequiredLevel;
    /**
     * 挂机金币奖励
     */
    public final int HangGold;
    /**
     * 挂机粉尘奖励
     */
    public final int HangDust;
    /**
     * 挂机英雄经验奖励
     */
    public final int HangHeroExp;
    /**
     * 挂机钻石奖励
     */
    public final int HangDiamond;
    /**
     * 挂机奖品奖励
     */
    public final int HangRewards;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "RewardLevel:" + RewardLevel + ","
        + "RequiredLevel:" + RequiredLevel + ","
        + "HangGold:" + HangGold + ","
        + "HangDust:" + HangDust + ","
        + "HangHeroExp:" + HangHeroExp + ","
        + "HangDiamond:" + HangDiamond + ","
        + "HangRewards:" + HangRewards + ","
        + "}";
    }
}


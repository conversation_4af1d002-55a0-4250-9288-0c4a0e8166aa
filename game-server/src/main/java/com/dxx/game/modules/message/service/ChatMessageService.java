package com.dxx.game.modules.message.service;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildMessageDao;
import com.dxx.game.dto.IMProto;
import com.dxx.game.modules.im.service.IMService;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Optional;

/**
 * @authoer: lsc
 * @createDate: 2023/4/8
 * @description:
 */
@Slf4j
@Service
public class ChatMessageService {

    @Resource
    private MessageService messageService;
    @Resource
    private GuildMessageDao guildMessageDao;
    @Resource
    private IMService imService;

    // 聊天展示道具
    public void publicChatShowItem(User user, String groupId, GuildUser guildUser, int itemType, Message itemProto) {

        JSONObject itemJsonObj = null;
        try {
            String jsonString = JsonFormat.printer().print(itemProto);
            itemJsonObj = JSONObject.parseObject(jsonString);

        } catch (Exception e) {
            log.error("publishGuildShowItem parseItemProto error: ", e);
        }

        if (itemJsonObj == null) {
            return;
        }

        long msgId = imService.createMsgId(groupId);
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("userId", user.getUserId());
        jsonObject.put("nickName", Optional.ofNullable(user.getNickName()).orElse(""));
        jsonObject.put("avatar", Optional.ofNullable(user.getAvatar()).orElse(0));
        jsonObject.put("avatarFrame", Optional.ofNullable(user.getAvatarFrame()).orElse(0));
        jsonObject.put("itemType", itemType);
        jsonObject.put("itemProto", itemJsonObj.toJSONString());
        jsonObject.put("timestamp", DateUtils.getUnixTime());
        jsonObject.put("groupId", groupId);

        jsonObject.put("guildPosition", 0);
        jsonObject.put("guildId", 0);

        if (guildUser != null) {
            jsonObject.put("guildPosition", guildUser.getPosition());
            jsonObject.put("guildId", guildUser.getGuildId());
        }

        String messageContent = jsonObject.toJSONString();
        imService.sendGroupMsg(IMProto.MessageType.CHAT_SHOW_ITEM, groupId, messageContent, true);

    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class PlayerAvatar_PlayerName {
    public PlayerAvatar_PlayerName(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        Name = _buf.get("Name").getAsString();
    }

    public static PlayerAvatar_PlayerName deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.PlayerAvatar_PlayerName(_buf);
    }

    /**
     * Id
     */
    public final int Id;
    /**
     * Name
     */
    public final String Name;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "Name:" + Name + ","
        + "}";
    }
}


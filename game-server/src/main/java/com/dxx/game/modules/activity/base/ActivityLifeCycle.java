package com.dxx.game.modules.activity.base;

import com.dxx.game.dao.dynamodb.model.activity.ActivityBase;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.google.protobuf.Message;

/**
 * @author: lsc
 * @createDate: 2025/6/3
 * @description:
 */
public interface ActivityLifeCycle<M extends ActivityBase, D extends Message> {

    ActivityMetaData<M, D> buildMetaData();

    /**
     * 初始化活动数据，调用时无需判断 model 是否为空
     */
    void initializeModel(long userId, M model);

    /**
     * 创建dto数据
     */
    D buildDto(M model);

    /**
     * 获取model
     */
    @SuppressWarnings("unchecked")
    default M fetchModel(long userId) {
        return ActivityLifeCycleSupport.getInstance().getModel(
                userId,
                (Class<? extends ActivityLifeCycle<M, D>>) this.getClass(),
                false
        );
    }

    /**
     * 获取model
     * @param fromActivity  是否从ActivityGetList中获取
     */
    @SuppressWarnings("unchecked")
    default M fetchModel(long userId, boolean fromActivity) {
        return ActivityLifeCycleSupport.getInstance().getModel(
                userId,
                (Class<? extends ActivityLifeCycle<M, D>>) this.getClass(),
                fromActivity
        );
    }

    /**
     * 更新model
     */
    @SuppressWarnings("unchecked")
    default void updateModel(M model) {
        ActivityLifeCycleSupport.getInstance().updateModel(
                model,
                (Class<? extends ActivityLifeCycle<M, D>>) this.getClass()
        );
    }

    /**
     * 活动结束
     * 如果有剩余道具转换成金币/钻石的需求
     */
    void onActivityEnd(M model);
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFishing_fishingBase {
    public ActivityFishing_fishingBase(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        FishBaitItem = _buf.get("FishBaitItem").getAsInt();
        FishPointItem = _buf.get("FishPointItem").getAsInt();
        FishLineItem = _buf.get("FishLineItem").getAsInt();
        FishBaitPrice = _buf.get("FishBaitPrice").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("BuyLimit").getAsJsonArray(); BuyLimit = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BuyLimit.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("FishFailRevive").getAsJsonArray(); FishFailRevive = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  FishFailRevive.add(_v0); }   }
        ReviveStrenth = _buf.get("ReviveStrenth").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("FishUp").getAsJsonArray(); FishUp = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  FishUp.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("FishUseMulti").getAsJsonArray(); FishUseMulti = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  FishUseMulti.add(_v0); }   }
        DistanceDefault = _buf.get("DistanceDefault").getAsInt();
        DistanceFail = _buf.get("DistanceFail").getAsInt();
        Point = _buf.get("Point").getAsInt();
        DefaultFishBait = _buf.get("DefaultFishBait").getAsInt();
        GalleryReward = _buf.get("GalleryReward").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("MailItems").getAsJsonArray(); MailItems = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailItems.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("MailItemsId").getAsJsonArray(); MailItemsId = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailItemsId.add(_v0); }   }
        FishType = _buf.get("FishType").getAsInt();
        Path = _buf.get("Path").getAsInt();
        Bait = _buf.get("Bait").getAsInt();
        FishRod = _buf.get("FishRod").getAsInt();
    }

    public static ActivityFishing_fishingBase deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFishing_fishingBase(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 鱼饵道具
     */
    public final int FishBaitItem;
    /**
     * 钓鱼积分道具
     */
    public final int FishPointItem;
    /**
     * 断了的线道具
     */
    public final int FishLineItem;
    /**
     * 鱼饵售价，钻石
     */
    public final int FishBaitPrice;
    /**
     * 根据钻石余量设定单次购买抽奖券上限<br/>最小数,最大数,上限|...
     */
    public final java.util.List<String> BuyLimit;
    /**
     * 失败后复活所需钻石<br/>第几次复活，复活所需钻石数量
     */
    public final java.util.List<String> FishFailRevive;
    /**
     * 复活增加力量
     */
    public final int ReviveStrenth;
    /**
     * 抛竿时，力度良好和力度完美，增加的稀有鱼概率
     */
    public final java.util.List<Integer> FishUp;
    /**
     * 可使用多个鱼饵<br/>鱼饵最小值,鱼饵最大值,最多可一次使用的鱼饵数量|
     */
    public final java.util.List<String> FishUseMulti;
    /**
     * 鱼的初始距离
     */
    public final int DistanceDefault;
    /**
     * 鱼的失败距离
     */
    public final int DistanceFail;
    /**
     * 积分兑重量比例
     */
    public final int Point;
    /**
     * 初始赠送鱼饵个数
     */
    public final int DefaultFishBait;
    /**
     * 场景区域id
     */
    public final int GalleryReward;
    /**
     * 活动结束时，以下这些道具兑换为金币<br/>道具ID，金币数量|
     */
    public final java.util.List<String> MailItems;
    /**
     * 道具转为金币邮件模板ID<br/>测试服|正式服
     */
    public final java.util.List<String> MailItemsId;
    /**
     * 鱼群id
     */
    public final int FishType;
    /**
     * 地图资源
     */
    public final int Path;
    /**
     * 初始鱼饵数量
     */
    public final int Bait;
    /**
     * 初始鱼竿
     */
    public final int FishRod;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "FishBaitItem:" + FishBaitItem + ","
        + "FishPointItem:" + FishPointItem + ","
        + "FishLineItem:" + FishLineItem + ","
        + "FishBaitPrice:" + FishBaitPrice + ","
        + "BuyLimit:" + BuyLimit + ","
        + "FishFailRevive:" + FishFailRevive + ","
        + "ReviveStrenth:" + ReviveStrenth + ","
        + "FishUp:" + FishUp + ","
        + "FishUseMulti:" + FishUseMulti + ","
        + "DistanceDefault:" + DistanceDefault + ","
        + "DistanceFail:" + DistanceFail + ","
        + "Point:" + Point + ","
        + "DefaultFishBait:" + DefaultFishBait + ","
        + "GalleryReward:" + GalleryReward + ","
        + "MailItems:" + MailItems + ","
        + "MailItemsId:" + MailItemsId + ","
        + "FishType:" + FishType + ","
        + "Path:" + Path + ","
        + "Bait:" + Bait + ","
        + "FishRod:" + FishRod + ","
        + "}";
    }
}


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: fishing.proto

package com.dxx.game.dto;

public final class FishingProto {
  private FishingProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FishingOnOpenRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingOnOpenRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=11401 钓鱼活动-打开界面调用
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingOnOpenRequest}
   */
  public static final class FishingOnOpenRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingOnOpenRequest)
      FishingOnOpenRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingOnOpenRequest.newBuilder() to construct.
    private FishingOnOpenRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingOnOpenRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingOnOpenRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingOnOpenRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingOnOpenRequest.class, com.dxx.game.dto.FishingProto.FishingOnOpenRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingOnOpenRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingOnOpenRequest other = (com.dxx.game.dto.FishingProto.FishingOnOpenRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingOnOpenRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11401 钓鱼活动-打开界面调用
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingOnOpenRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingOnOpenRequest)
        com.dxx.game.dto.FishingProto.FishingOnOpenRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingOnOpenRequest.class, com.dxx.game.dto.FishingProto.FishingOnOpenRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingOnOpenRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingOnOpenRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingOnOpenRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingOnOpenRequest build() {
        com.dxx.game.dto.FishingProto.FishingOnOpenRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingOnOpenRequest buildPartial() {
        com.dxx.game.dto.FishingProto.FishingOnOpenRequest result = new com.dxx.game.dto.FishingProto.FishingOnOpenRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingOnOpenRequest) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingOnOpenRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingOnOpenRequest other) {
        if (other == com.dxx.game.dto.FishingProto.FishingOnOpenRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingOnOpenRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingOnOpenRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingOnOpenRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingOnOpenRequest)
    private static final com.dxx.game.dto.FishingProto.FishingOnOpenRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingOnOpenRequest();
    }

    public static com.dxx.game.dto.FishingProto.FishingOnOpenRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingOnOpenRequest>
        PARSER = new com.google.protobuf.AbstractParser<FishingOnOpenRequest>() {
      @java.lang.Override
      public FishingOnOpenRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingOnOpenRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingOnOpenRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingOnOpenRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingOnOpenRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingOnOpenResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingOnOpenResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>.Proto.Fishing.FishingDto fish = 3;</code>
     * @return Whether the fish field is set.
     */
    boolean hasFish();
    /**
     * <code>.Proto.Fishing.FishingDto fish = 3;</code>
     * @return The fish.
     */
    com.dxx.game.dto.FishingProto.FishingDto getFish();
    /**
     * <code>.Proto.Fishing.FishingDto fish = 3;</code>
     */
    com.dxx.game.dto.FishingProto.FishingDtoOrBuilder getFishOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=11402
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingOnOpenResponse}
   */
  public static final class FishingOnOpenResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingOnOpenResponse)
      FishingOnOpenResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingOnOpenResponse.newBuilder() to construct.
    private FishingOnOpenResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingOnOpenResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingOnOpenResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingOnOpenResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.dxx.game.dto.FishingProto.FishingDto.Builder subBuilder = null;
              if (fish_ != null) {
                subBuilder = fish_.toBuilder();
              }
              fish_ = input.readMessage(com.dxx.game.dto.FishingProto.FishingDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(fish_);
                fish_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingOnOpenResponse.class, com.dxx.game.dto.FishingProto.FishingOnOpenResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int FISH_FIELD_NUMBER = 3;
    private com.dxx.game.dto.FishingProto.FishingDto fish_;
    /**
     * <code>.Proto.Fishing.FishingDto fish = 3;</code>
     * @return Whether the fish field is set.
     */
    @java.lang.Override
    public boolean hasFish() {
      return fish_ != null;
    }
    /**
     * <code>.Proto.Fishing.FishingDto fish = 3;</code>
     * @return The fish.
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingDto getFish() {
      return fish_ == null ? com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance() : fish_;
    }
    /**
     * <code>.Proto.Fishing.FishingDto fish = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingDtoOrBuilder getFishOrBuilder() {
      return getFish();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (fish_ != null) {
        output.writeMessage(3, getFish());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (fish_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getFish());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingOnOpenResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingOnOpenResponse other = (com.dxx.game.dto.FishingProto.FishingOnOpenResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasFish() != other.hasFish()) return false;
      if (hasFish()) {
        if (!getFish()
            .equals(other.getFish())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasFish()) {
        hash = (37 * hash) + FISH_FIELD_NUMBER;
        hash = (53 * hash) + getFish().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingOnOpenResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11402
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingOnOpenResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingOnOpenResponse)
        com.dxx.game.dto.FishingProto.FishingOnOpenResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingOnOpenResponse.class, com.dxx.game.dto.FishingProto.FishingOnOpenResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingOnOpenResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (fishBuilder_ == null) {
          fish_ = null;
        } else {
          fish_ = null;
          fishBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingOnOpenResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingOnOpenResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingOnOpenResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingOnOpenResponse build() {
        com.dxx.game.dto.FishingProto.FishingOnOpenResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingOnOpenResponse buildPartial() {
        com.dxx.game.dto.FishingProto.FishingOnOpenResponse result = new com.dxx.game.dto.FishingProto.FishingOnOpenResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (fishBuilder_ == null) {
          result.fish_ = fish_;
        } else {
          result.fish_ = fishBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingOnOpenResponse) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingOnOpenResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingOnOpenResponse other) {
        if (other == com.dxx.game.dto.FishingProto.FishingOnOpenResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasFish()) {
          mergeFish(other.getFish());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingOnOpenResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingOnOpenResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.FishingProto.FishingDto fish_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishingDto, com.dxx.game.dto.FishingProto.FishingDto.Builder, com.dxx.game.dto.FishingProto.FishingDtoOrBuilder> fishBuilder_;
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       * @return Whether the fish field is set.
       */
      public boolean hasFish() {
        return fishBuilder_ != null || fish_ != null;
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       * @return The fish.
       */
      public com.dxx.game.dto.FishingProto.FishingDto getFish() {
        if (fishBuilder_ == null) {
          return fish_ == null ? com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance() : fish_;
        } else {
          return fishBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      public Builder setFish(com.dxx.game.dto.FishingProto.FishingDto value) {
        if (fishBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fish_ = value;
          onChanged();
        } else {
          fishBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      public Builder setFish(
          com.dxx.game.dto.FishingProto.FishingDto.Builder builderForValue) {
        if (fishBuilder_ == null) {
          fish_ = builderForValue.build();
          onChanged();
        } else {
          fishBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      public Builder mergeFish(com.dxx.game.dto.FishingProto.FishingDto value) {
        if (fishBuilder_ == null) {
          if (fish_ != null) {
            fish_ =
              com.dxx.game.dto.FishingProto.FishingDto.newBuilder(fish_).mergeFrom(value).buildPartial();
          } else {
            fish_ = value;
          }
          onChanged();
        } else {
          fishBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      public Builder clearFish() {
        if (fishBuilder_ == null) {
          fish_ = null;
          onChanged();
        } else {
          fish_ = null;
          fishBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishingDto.Builder getFishBuilder() {
        
        onChanged();
        return getFishFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishingDtoOrBuilder getFishOrBuilder() {
        if (fishBuilder_ != null) {
          return fishBuilder_.getMessageOrBuilder();
        } else {
          return fish_ == null ?
              com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance() : fish_;
        }
      }
      /**
       * <code>.Proto.Fishing.FishingDto fish = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishingDto, com.dxx.game.dto.FishingProto.FishingDto.Builder, com.dxx.game.dto.FishingProto.FishingDtoOrBuilder> 
          getFishFieldBuilder() {
        if (fishBuilder_ == null) {
          fishBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.FishingProto.FishingDto, com.dxx.game.dto.FishingProto.FishingDto.Builder, com.dxx.game.dto.FishingProto.FishingDtoOrBuilder>(
                  getFish(),
                  getParentForChildren(),
                  isClean());
          fish_ = null;
        }
        return fishBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingOnOpenResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingOnOpenResponse)
    private static final com.dxx.game.dto.FishingProto.FishingOnOpenResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingOnOpenResponse();
    }

    public static com.dxx.game.dto.FishingProto.FishingOnOpenResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingOnOpenResponse>
        PARSER = new com.google.protobuf.AbstractParser<FishingOnOpenResponse>() {
      @java.lang.Override
      public FishingOnOpenResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingOnOpenResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingOnOpenResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingOnOpenResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingOnOpenResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingCastRodRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingCastRodRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 消耗鱼饵数量
     * </pre>
     *
     * <code>uint32 baitNum = 2;</code>
     * @return The baitNum.
     */
    int getBaitNum();

    /**
     * <pre>
     * 0:Normal 1:Good 2:Perfect
     * </pre>
     *
     * <code>uint32 eval = 3;</code>
     * @return The eval.
     */
    int getEval();
  }
  /**
   * <pre>
   *CMD PackageId=11403 钓鱼活动-抛竿
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingCastRodRequest}
   */
  public static final class FishingCastRodRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingCastRodRequest)
      FishingCastRodRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingCastRodRequest.newBuilder() to construct.
    private FishingCastRodRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingCastRodRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingCastRodRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingCastRodRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              baitNum_ = input.readUInt32();
              break;
            }
            case 24: {

              eval_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingCastRodRequest.class, com.dxx.game.dto.FishingProto.FishingCastRodRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int BAITNUM_FIELD_NUMBER = 2;
    private int baitNum_;
    /**
     * <pre>
     * 消耗鱼饵数量
     * </pre>
     *
     * <code>uint32 baitNum = 2;</code>
     * @return The baitNum.
     */
    @java.lang.Override
    public int getBaitNum() {
      return baitNum_;
    }

    public static final int EVAL_FIELD_NUMBER = 3;
    private int eval_;
    /**
     * <pre>
     * 0:Normal 1:Good 2:Perfect
     * </pre>
     *
     * <code>uint32 eval = 3;</code>
     * @return The eval.
     */
    @java.lang.Override
    public int getEval() {
      return eval_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (baitNum_ != 0) {
        output.writeUInt32(2, baitNum_);
      }
      if (eval_ != 0) {
        output.writeUInt32(3, eval_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (baitNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, baitNum_);
      }
      if (eval_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, eval_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingCastRodRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingCastRodRequest other = (com.dxx.game.dto.FishingProto.FishingCastRodRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getBaitNum()
          != other.getBaitNum()) return false;
      if (getEval()
          != other.getEval()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + BAITNUM_FIELD_NUMBER;
      hash = (53 * hash) + getBaitNum();
      hash = (37 * hash) + EVAL_FIELD_NUMBER;
      hash = (53 * hash) + getEval();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingCastRodRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11403 钓鱼活动-抛竿
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingCastRodRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingCastRodRequest)
        com.dxx.game.dto.FishingProto.FishingCastRodRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingCastRodRequest.class, com.dxx.game.dto.FishingProto.FishingCastRodRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingCastRodRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        baitNum_ = 0;

        eval_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingCastRodRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingCastRodRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingCastRodRequest build() {
        com.dxx.game.dto.FishingProto.FishingCastRodRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingCastRodRequest buildPartial() {
        com.dxx.game.dto.FishingProto.FishingCastRodRequest result = new com.dxx.game.dto.FishingProto.FishingCastRodRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.baitNum_ = baitNum_;
        result.eval_ = eval_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingCastRodRequest) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingCastRodRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingCastRodRequest other) {
        if (other == com.dxx.game.dto.FishingProto.FishingCastRodRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getBaitNum() != 0) {
          setBaitNum(other.getBaitNum());
        }
        if (other.getEval() != 0) {
          setEval(other.getEval());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingCastRodRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingCastRodRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int baitNum_ ;
      /**
       * <pre>
       * 消耗鱼饵数量
       * </pre>
       *
       * <code>uint32 baitNum = 2;</code>
       * @return The baitNum.
       */
      @java.lang.Override
      public int getBaitNum() {
        return baitNum_;
      }
      /**
       * <pre>
       * 消耗鱼饵数量
       * </pre>
       *
       * <code>uint32 baitNum = 2;</code>
       * @param value The baitNum to set.
       * @return This builder for chaining.
       */
      public Builder setBaitNum(int value) {
        
        baitNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消耗鱼饵数量
       * </pre>
       *
       * <code>uint32 baitNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBaitNum() {
        
        baitNum_ = 0;
        onChanged();
        return this;
      }

      private int eval_ ;
      /**
       * <pre>
       * 0:Normal 1:Good 2:Perfect
       * </pre>
       *
       * <code>uint32 eval = 3;</code>
       * @return The eval.
       */
      @java.lang.Override
      public int getEval() {
        return eval_;
      }
      /**
       * <pre>
       * 0:Normal 1:Good 2:Perfect
       * </pre>
       *
       * <code>uint32 eval = 3;</code>
       * @param value The eval to set.
       * @return This builder for chaining.
       */
      public Builder setEval(int value) {
        
        eval_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0:Normal 1:Good 2:Perfect
       * </pre>
       *
       * <code>uint32 eval = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearEval() {
        
        eval_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingCastRodRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingCastRodRequest)
    private static final com.dxx.game.dto.FishingProto.FishingCastRodRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingCastRodRequest();
    }

    public static com.dxx.game.dto.FishingProto.FishingCastRodRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingCastRodRequest>
        PARSER = new com.google.protobuf.AbstractParser<FishingCastRodRequest>() {
      @java.lang.Override
      public FishingCastRodRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingCastRodRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingCastRodRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingCastRodRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingCastRodRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingCastRodResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingCastRodResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>repeated uint32 fishIds = 3;</code>
     * @return A list containing the fishIds.
     */
    java.util.List<java.lang.Integer> getFishIdsList();
    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>repeated uint32 fishIds = 3;</code>
     * @return The count of fishIds.
     */
    int getFishIdsCount();
    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>repeated uint32 fishIds = 3;</code>
     * @param index The index of the element to return.
     * @return The fishIds at the given index.
     */
    int getFishIds(int index);

    /**
     * <pre>
     * 下次复活需要钻石
     * </pre>
     *
     * <code>uint32 nextRebornDiamond = 4;</code>
     * @return The nextRebornDiamond.
     */
    int getNextRebornDiamond();
  }
  /**
   * <pre>
   *CMD PackageId=11404
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingCastRodResponse}
   */
  public static final class FishingCastRodResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingCastRodResponse)
      FishingCastRodResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingCastRodResponse.newBuilder() to construct.
    private FishingCastRodResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingCastRodResponse() {
      fishIds_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingCastRodResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingCastRodResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                fishIds_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              fishIds_.addInt(input.readUInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                fishIds_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                fishIds_.addInt(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 32: {

              nextRebornDiamond_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          fishIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingCastRodResponse.class, com.dxx.game.dto.FishingProto.FishingCastRodResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int FISHIDS_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.IntList fishIds_;
    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>repeated uint32 fishIds = 3;</code>
     * @return A list containing the fishIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getFishIdsList() {
      return fishIds_;
    }
    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>repeated uint32 fishIds = 3;</code>
     * @return The count of fishIds.
     */
    public int getFishIdsCount() {
      return fishIds_.size();
    }
    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>repeated uint32 fishIds = 3;</code>
     * @param index The index of the element to return.
     * @return The fishIds at the given index.
     */
    public int getFishIds(int index) {
      return fishIds_.getInt(index);
    }
    private int fishIdsMemoizedSerializedSize = -1;

    public static final int NEXTREBORNDIAMOND_FIELD_NUMBER = 4;
    private int nextRebornDiamond_;
    /**
     * <pre>
     * 下次复活需要钻石
     * </pre>
     *
     * <code>uint32 nextRebornDiamond = 4;</code>
     * @return The nextRebornDiamond.
     */
    @java.lang.Override
    public int getNextRebornDiamond() {
      return nextRebornDiamond_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (getFishIdsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(fishIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < fishIds_.size(); i++) {
        output.writeUInt32NoTag(fishIds_.getInt(i));
      }
      if (nextRebornDiamond_ != 0) {
        output.writeUInt32(4, nextRebornDiamond_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < fishIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(fishIds_.getInt(i));
        }
        size += dataSize;
        if (!getFishIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        fishIdsMemoizedSerializedSize = dataSize;
      }
      if (nextRebornDiamond_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, nextRebornDiamond_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingCastRodResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingCastRodResponse other = (com.dxx.game.dto.FishingProto.FishingCastRodResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getFishIdsList()
          .equals(other.getFishIdsList())) return false;
      if (getNextRebornDiamond()
          != other.getNextRebornDiamond()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (getFishIdsCount() > 0) {
        hash = (37 * hash) + FISHIDS_FIELD_NUMBER;
        hash = (53 * hash) + getFishIdsList().hashCode();
      }
      hash = (37 * hash) + NEXTREBORNDIAMOND_FIELD_NUMBER;
      hash = (53 * hash) + getNextRebornDiamond();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingCastRodResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11404
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingCastRodResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingCastRodResponse)
        com.dxx.game.dto.FishingProto.FishingCastRodResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingCastRodResponse.class, com.dxx.game.dto.FishingProto.FishingCastRodResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingCastRodResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        fishIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        nextRebornDiamond_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingCastRodResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingCastRodResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingCastRodResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingCastRodResponse build() {
        com.dxx.game.dto.FishingProto.FishingCastRodResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingCastRodResponse buildPartial() {
        com.dxx.game.dto.FishingProto.FishingCastRodResponse result = new com.dxx.game.dto.FishingProto.FishingCastRodResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          fishIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.fishIds_ = fishIds_;
        result.nextRebornDiamond_ = nextRebornDiamond_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingCastRodResponse) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingCastRodResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingCastRodResponse other) {
        if (other == com.dxx.game.dto.FishingProto.FishingCastRodResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (!other.fishIds_.isEmpty()) {
          if (fishIds_.isEmpty()) {
            fishIds_ = other.fishIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureFishIdsIsMutable();
            fishIds_.addAll(other.fishIds_);
          }
          onChanged();
        }
        if (other.getNextRebornDiamond() != 0) {
          setNextRebornDiamond(other.getNextRebornDiamond());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingCastRodResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingCastRodResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.google.protobuf.Internal.IntList fishIds_ = emptyIntList();
      private void ensureFishIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fishIds_ = mutableCopy(fishIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @return A list containing the fishIds.
       */
      public java.util.List<java.lang.Integer>
          getFishIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(fishIds_) : fishIds_;
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @return The count of fishIds.
       */
      public int getFishIdsCount() {
        return fishIds_.size();
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @param index The index of the element to return.
       * @return The fishIds at the given index.
       */
      public int getFishIds(int index) {
        return fishIds_.getInt(index);
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @param index The index to set the value at.
       * @param value The fishIds to set.
       * @return This builder for chaining.
       */
      public Builder setFishIds(
          int index, int value) {
        ensureFishIdsIsMutable();
        fishIds_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @param value The fishIds to add.
       * @return This builder for chaining.
       */
      public Builder addFishIds(int value) {
        ensureFishIdsIsMutable();
        fishIds_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @param values The fishIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllFishIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureFishIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fishIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>repeated uint32 fishIds = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishIds() {
        fishIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private int nextRebornDiamond_ ;
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 4;</code>
       * @return The nextRebornDiamond.
       */
      @java.lang.Override
      public int getNextRebornDiamond() {
        return nextRebornDiamond_;
      }
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 4;</code>
       * @param value The nextRebornDiamond to set.
       * @return This builder for chaining.
       */
      public Builder setNextRebornDiamond(int value) {
        
        nextRebornDiamond_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRebornDiamond() {
        
        nextRebornDiamond_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingCastRodResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingCastRodResponse)
    private static final com.dxx.game.dto.FishingProto.FishingCastRodResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingCastRodResponse();
    }

    public static com.dxx.game.dto.FishingProto.FishingCastRodResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingCastRodResponse>
        PARSER = new com.google.protobuf.AbstractParser<FishingCastRodResponse>() {
      @java.lang.Override
      public FishingCastRodResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingCastRodResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingCastRodResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingCastRodResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingCastRodResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingReelInRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingReelInRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 是否钓上来鱼 false=断线
     * </pre>
     *
     * <code>bool catch = 2;</code>
     * @return The catch.
     */
    boolean getCatch();
  }
  /**
   * <pre>
   *CMD PackageId=11405 钓鱼活动-收竿
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingReelInRequest}
   */
  public static final class FishingReelInRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingReelInRequest)
      FishingReelInRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingReelInRequest.newBuilder() to construct.
    private FishingReelInRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingReelInRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingReelInRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingReelInRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              catch_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingReelInRequest.class, com.dxx.game.dto.FishingProto.FishingReelInRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int CATCH_FIELD_NUMBER = 2;
    private boolean catch_;
    /**
     * <pre>
     * 是否钓上来鱼 false=断线
     * </pre>
     *
     * <code>bool catch = 2;</code>
     * @return The catch.
     */
    @java.lang.Override
    public boolean getCatch() {
      return catch_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (catch_ != false) {
        output.writeBool(2, catch_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (catch_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, catch_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingReelInRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingReelInRequest other = (com.dxx.game.dto.FishingProto.FishingReelInRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getCatch()
          != other.getCatch()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + CATCH_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getCatch());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingReelInRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11405 钓鱼活动-收竿
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingReelInRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingReelInRequest)
        com.dxx.game.dto.FishingProto.FishingReelInRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingReelInRequest.class, com.dxx.game.dto.FishingProto.FishingReelInRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingReelInRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        catch_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingReelInRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingReelInRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingReelInRequest build() {
        com.dxx.game.dto.FishingProto.FishingReelInRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingReelInRequest buildPartial() {
        com.dxx.game.dto.FishingProto.FishingReelInRequest result = new com.dxx.game.dto.FishingProto.FishingReelInRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.catch_ = catch_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingReelInRequest) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingReelInRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingReelInRequest other) {
        if (other == com.dxx.game.dto.FishingProto.FishingReelInRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getCatch() != false) {
          setCatch(other.getCatch());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingReelInRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingReelInRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private boolean catch_ ;
      /**
       * <pre>
       * 是否钓上来鱼 false=断线
       * </pre>
       *
       * <code>bool catch = 2;</code>
       * @return The catch.
       */
      @java.lang.Override
      public boolean getCatch() {
        return catch_;
      }
      /**
       * <pre>
       * 是否钓上来鱼 false=断线
       * </pre>
       *
       * <code>bool catch = 2;</code>
       * @param value The catch to set.
       * @return This builder for chaining.
       */
      public Builder setCatch(boolean value) {
        
        catch_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否钓上来鱼 false=断线
       * </pre>
       *
       * <code>bool catch = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCatch() {
        
        catch_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingReelInRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingReelInRequest)
    private static final com.dxx.game.dto.FishingProto.FishingReelInRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingReelInRequest();
    }

    public static com.dxx.game.dto.FishingProto.FishingReelInRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingReelInRequest>
        PARSER = new com.google.protobuf.AbstractParser<FishingReelInRequest>() {
      @java.lang.Override
      public FishingReelInRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingReelInRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingReelInRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingReelInRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingReelInRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingReelInResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingReelInResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    java.util.List<com.dxx.game.dto.FishingProto.FishDto> 
        getFishDtosList();
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    com.dxx.game.dto.FishingProto.FishDto getFishDtos(int index);
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    int getFishDtosCount();
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    java.util.List<? extends com.dxx.game.dto.FishingProto.FishDtoOrBuilder> 
        getFishDtosOrBuilderList();
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    com.dxx.game.dto.FishingProto.FishDtoOrBuilder getFishDtosOrBuilder(
        int index);

    /**
     * <pre>
     * 总重量
     * </pre>
     *
     * <code>uint32 weight = 4;</code>
     * @return The weight.
     */
    int getWeight();

    /**
     * <pre>
     * 钓鱼活动数
     * </pre>
     *
     * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
     * @return Whether the fishingDto field is set.
     */
    boolean hasFishingDto();
    /**
     * <pre>
     * 钓鱼活动数
     * </pre>
     *
     * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
     * @return The fishingDto.
     */
    com.dxx.game.dto.FishingProto.FishingDto getFishingDto();
    /**
     * <pre>
     * 钓鱼活动数
     * </pre>
     *
     * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
     */
    com.dxx.game.dto.FishingProto.FishingDtoOrBuilder getFishingDtoOrBuilder();

    /**
     * <pre>
     * 解锁的鱼竿
     * </pre>
     *
     * <code>uint32 unlockRod = 6;</code>
     * @return The unlockRod.
     */
    int getUnlockRod();
  }
  /**
   * <pre>
   *CMD PackageId=11406
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingReelInResponse}
   */
  public static final class FishingReelInResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingReelInResponse)
      FishingReelInResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingReelInResponse.newBuilder() to construct.
    private FishingReelInResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingReelInResponse() {
      fishDtos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingReelInResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingReelInResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                fishDtos_ = new java.util.ArrayList<com.dxx.game.dto.FishingProto.FishDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              fishDtos_.add(
                  input.readMessage(com.dxx.game.dto.FishingProto.FishDto.parser(), extensionRegistry));
              break;
            }
            case 32: {

              weight_ = input.readUInt32();
              break;
            }
            case 42: {
              com.dxx.game.dto.FishingProto.FishingDto.Builder subBuilder = null;
              if (fishingDto_ != null) {
                subBuilder = fishingDto_.toBuilder();
              }
              fishingDto_ = input.readMessage(com.dxx.game.dto.FishingProto.FishingDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(fishingDto_);
                fishingDto_ = subBuilder.buildPartial();
              }

              break;
            }
            case 48: {

              unlockRod_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          fishDtos_ = java.util.Collections.unmodifiableList(fishDtos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingReelInResponse.class, com.dxx.game.dto.FishingProto.FishingReelInResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int FISHDTOS_FIELD_NUMBER = 3;
    private java.util.List<com.dxx.game.dto.FishingProto.FishDto> fishDtos_;
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.FishingProto.FishDto> getFishDtosList() {
      return fishDtos_;
    }
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.FishingProto.FishDtoOrBuilder> 
        getFishDtosOrBuilderList() {
      return fishDtos_;
    }
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    @java.lang.Override
    public int getFishDtosCount() {
      return fishDtos_.size();
    }
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishDto getFishDtos(int index) {
      return fishDtos_.get(index);
    }
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishDtoOrBuilder getFishDtosOrBuilder(
        int index) {
      return fishDtos_.get(index);
    }

    public static final int WEIGHT_FIELD_NUMBER = 4;
    private int weight_;
    /**
     * <pre>
     * 总重量
     * </pre>
     *
     * <code>uint32 weight = 4;</code>
     * @return The weight.
     */
    @java.lang.Override
    public int getWeight() {
      return weight_;
    }

    public static final int FISHINGDTO_FIELD_NUMBER = 5;
    private com.dxx.game.dto.FishingProto.FishingDto fishingDto_;
    /**
     * <pre>
     * 钓鱼活动数
     * </pre>
     *
     * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
     * @return Whether the fishingDto field is set.
     */
    @java.lang.Override
    public boolean hasFishingDto() {
      return fishingDto_ != null;
    }
    /**
     * <pre>
     * 钓鱼活动数
     * </pre>
     *
     * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
     * @return The fishingDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingDto getFishingDto() {
      return fishingDto_ == null ? com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance() : fishingDto_;
    }
    /**
     * <pre>
     * 钓鱼活动数
     * </pre>
     *
     * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingDtoOrBuilder getFishingDtoOrBuilder() {
      return getFishingDto();
    }

    public static final int UNLOCKROD_FIELD_NUMBER = 6;
    private int unlockRod_;
    /**
     * <pre>
     * 解锁的鱼竿
     * </pre>
     *
     * <code>uint32 unlockRod = 6;</code>
     * @return The unlockRod.
     */
    @java.lang.Override
    public int getUnlockRod() {
      return unlockRod_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      for (int i = 0; i < fishDtos_.size(); i++) {
        output.writeMessage(3, fishDtos_.get(i));
      }
      if (weight_ != 0) {
        output.writeUInt32(4, weight_);
      }
      if (fishingDto_ != null) {
        output.writeMessage(5, getFishingDto());
      }
      if (unlockRod_ != 0) {
        output.writeUInt32(6, unlockRod_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      for (int i = 0; i < fishDtos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, fishDtos_.get(i));
      }
      if (weight_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, weight_);
      }
      if (fishingDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getFishingDto());
      }
      if (unlockRod_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, unlockRod_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingReelInResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingReelInResponse other = (com.dxx.game.dto.FishingProto.FishingReelInResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getFishDtosList()
          .equals(other.getFishDtosList())) return false;
      if (getWeight()
          != other.getWeight()) return false;
      if (hasFishingDto() != other.hasFishingDto()) return false;
      if (hasFishingDto()) {
        if (!getFishingDto()
            .equals(other.getFishingDto())) return false;
      }
      if (getUnlockRod()
          != other.getUnlockRod()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (getFishDtosCount() > 0) {
        hash = (37 * hash) + FISHDTOS_FIELD_NUMBER;
        hash = (53 * hash) + getFishDtosList().hashCode();
      }
      hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getWeight();
      if (hasFishingDto()) {
        hash = (37 * hash) + FISHINGDTO_FIELD_NUMBER;
        hash = (53 * hash) + getFishingDto().hashCode();
      }
      hash = (37 * hash) + UNLOCKROD_FIELD_NUMBER;
      hash = (53 * hash) + getUnlockRod();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingReelInResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingReelInResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11406
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingReelInResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingReelInResponse)
        com.dxx.game.dto.FishingProto.FishingReelInResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingReelInResponse.class, com.dxx.game.dto.FishingProto.FishingReelInResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingReelInResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFishDtosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (fishDtosBuilder_ == null) {
          fishDtos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fishDtosBuilder_.clear();
        }
        weight_ = 0;

        if (fishingDtoBuilder_ == null) {
          fishingDto_ = null;
        } else {
          fishingDto_ = null;
          fishingDtoBuilder_ = null;
        }
        unlockRod_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingReelInResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingReelInResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingReelInResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingReelInResponse build() {
        com.dxx.game.dto.FishingProto.FishingReelInResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingReelInResponse buildPartial() {
        com.dxx.game.dto.FishingProto.FishingReelInResponse result = new com.dxx.game.dto.FishingProto.FishingReelInResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (fishDtosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            fishDtos_ = java.util.Collections.unmodifiableList(fishDtos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fishDtos_ = fishDtos_;
        } else {
          result.fishDtos_ = fishDtosBuilder_.build();
        }
        result.weight_ = weight_;
        if (fishingDtoBuilder_ == null) {
          result.fishingDto_ = fishingDto_;
        } else {
          result.fishingDto_ = fishingDtoBuilder_.build();
        }
        result.unlockRod_ = unlockRod_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingReelInResponse) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingReelInResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingReelInResponse other) {
        if (other == com.dxx.game.dto.FishingProto.FishingReelInResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (fishDtosBuilder_ == null) {
          if (!other.fishDtos_.isEmpty()) {
            if (fishDtos_.isEmpty()) {
              fishDtos_ = other.fishDtos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFishDtosIsMutable();
              fishDtos_.addAll(other.fishDtos_);
            }
            onChanged();
          }
        } else {
          if (!other.fishDtos_.isEmpty()) {
            if (fishDtosBuilder_.isEmpty()) {
              fishDtosBuilder_.dispose();
              fishDtosBuilder_ = null;
              fishDtos_ = other.fishDtos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              fishDtosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFishDtosFieldBuilder() : null;
            } else {
              fishDtosBuilder_.addAllMessages(other.fishDtos_);
            }
          }
        }
        if (other.getWeight() != 0) {
          setWeight(other.getWeight());
        }
        if (other.hasFishingDto()) {
          mergeFishingDto(other.getFishingDto());
        }
        if (other.getUnlockRod() != 0) {
          setUnlockRod(other.getUnlockRod());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingReelInResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingReelInResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.FishingProto.FishDto> fishDtos_ =
        java.util.Collections.emptyList();
      private void ensureFishDtosIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fishDtos_ = new java.util.ArrayList<com.dxx.game.dto.FishingProto.FishDto>(fishDtos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishDto, com.dxx.game.dto.FishingProto.FishDto.Builder, com.dxx.game.dto.FishingProto.FishDtoOrBuilder> fishDtosBuilder_;

      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.FishingProto.FishDto> getFishDtosList() {
        if (fishDtosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fishDtos_);
        } else {
          return fishDtosBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public int getFishDtosCount() {
        if (fishDtosBuilder_ == null) {
          return fishDtos_.size();
        } else {
          return fishDtosBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishDto getFishDtos(int index) {
        if (fishDtosBuilder_ == null) {
          return fishDtos_.get(index);
        } else {
          return fishDtosBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder setFishDtos(
          int index, com.dxx.game.dto.FishingProto.FishDto value) {
        if (fishDtosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFishDtosIsMutable();
          fishDtos_.set(index, value);
          onChanged();
        } else {
          fishDtosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder setFishDtos(
          int index, com.dxx.game.dto.FishingProto.FishDto.Builder builderForValue) {
        if (fishDtosBuilder_ == null) {
          ensureFishDtosIsMutable();
          fishDtos_.set(index, builderForValue.build());
          onChanged();
        } else {
          fishDtosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder addFishDtos(com.dxx.game.dto.FishingProto.FishDto value) {
        if (fishDtosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFishDtosIsMutable();
          fishDtos_.add(value);
          onChanged();
        } else {
          fishDtosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder addFishDtos(
          int index, com.dxx.game.dto.FishingProto.FishDto value) {
        if (fishDtosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFishDtosIsMutable();
          fishDtos_.add(index, value);
          onChanged();
        } else {
          fishDtosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder addFishDtos(
          com.dxx.game.dto.FishingProto.FishDto.Builder builderForValue) {
        if (fishDtosBuilder_ == null) {
          ensureFishDtosIsMutable();
          fishDtos_.add(builderForValue.build());
          onChanged();
        } else {
          fishDtosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder addFishDtos(
          int index, com.dxx.game.dto.FishingProto.FishDto.Builder builderForValue) {
        if (fishDtosBuilder_ == null) {
          ensureFishDtosIsMutable();
          fishDtos_.add(index, builderForValue.build());
          onChanged();
        } else {
          fishDtosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder addAllFishDtos(
          java.lang.Iterable<? extends com.dxx.game.dto.FishingProto.FishDto> values) {
        if (fishDtosBuilder_ == null) {
          ensureFishDtosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fishDtos_);
          onChanged();
        } else {
          fishDtosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder clearFishDtos() {
        if (fishDtosBuilder_ == null) {
          fishDtos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fishDtosBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public Builder removeFishDtos(int index) {
        if (fishDtosBuilder_ == null) {
          ensureFishDtosIsMutable();
          fishDtos_.remove(index);
          onChanged();
        } else {
          fishDtosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishDto.Builder getFishDtosBuilder(
          int index) {
        return getFishDtosFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishDtoOrBuilder getFishDtosOrBuilder(
          int index) {
        if (fishDtosBuilder_ == null) {
          return fishDtos_.get(index);  } else {
          return fishDtosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.FishingProto.FishDtoOrBuilder> 
           getFishDtosOrBuilderList() {
        if (fishDtosBuilder_ != null) {
          return fishDtosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fishDtos_);
        }
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishDto.Builder addFishDtosBuilder() {
        return getFishDtosFieldBuilder().addBuilder(
            com.dxx.game.dto.FishingProto.FishDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public com.dxx.game.dto.FishingProto.FishDto.Builder addFishDtosBuilder(
          int index) {
        return getFishDtosFieldBuilder().addBuilder(
            index, com.dxx.game.dto.FishingProto.FishDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 鱼
       * </pre>
       *
       * <code>repeated .Proto.Fishing.FishDto fishDtos = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.FishingProto.FishDto.Builder> 
           getFishDtosBuilderList() {
        return getFishDtosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishDto, com.dxx.game.dto.FishingProto.FishDto.Builder, com.dxx.game.dto.FishingProto.FishDtoOrBuilder> 
          getFishDtosFieldBuilder() {
        if (fishDtosBuilder_ == null) {
          fishDtosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.FishingProto.FishDto, com.dxx.game.dto.FishingProto.FishDto.Builder, com.dxx.game.dto.FishingProto.FishDtoOrBuilder>(
                  fishDtos_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          fishDtos_ = null;
        }
        return fishDtosBuilder_;
      }

      private int weight_ ;
      /**
       * <pre>
       * 总重量
       * </pre>
       *
       * <code>uint32 weight = 4;</code>
       * @return The weight.
       */
      @java.lang.Override
      public int getWeight() {
        return weight_;
      }
      /**
       * <pre>
       * 总重量
       * </pre>
       *
       * <code>uint32 weight = 4;</code>
       * @param value The weight to set.
       * @return This builder for chaining.
       */
      public Builder setWeight(int value) {
        
        weight_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总重量
       * </pre>
       *
       * <code>uint32 weight = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeight() {
        
        weight_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.FishingProto.FishingDto fishingDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishingDto, com.dxx.game.dto.FishingProto.FishingDto.Builder, com.dxx.game.dto.FishingProto.FishingDtoOrBuilder> fishingDtoBuilder_;
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       * @return Whether the fishingDto field is set.
       */
      public boolean hasFishingDto() {
        return fishingDtoBuilder_ != null || fishingDto_ != null;
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       * @return The fishingDto.
       */
      public com.dxx.game.dto.FishingProto.FishingDto getFishingDto() {
        if (fishingDtoBuilder_ == null) {
          return fishingDto_ == null ? com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance() : fishingDto_;
        } else {
          return fishingDtoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      public Builder setFishingDto(com.dxx.game.dto.FishingProto.FishingDto value) {
        if (fishingDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fishingDto_ = value;
          onChanged();
        } else {
          fishingDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      public Builder setFishingDto(
          com.dxx.game.dto.FishingProto.FishingDto.Builder builderForValue) {
        if (fishingDtoBuilder_ == null) {
          fishingDto_ = builderForValue.build();
          onChanged();
        } else {
          fishingDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      public Builder mergeFishingDto(com.dxx.game.dto.FishingProto.FishingDto value) {
        if (fishingDtoBuilder_ == null) {
          if (fishingDto_ != null) {
            fishingDto_ =
              com.dxx.game.dto.FishingProto.FishingDto.newBuilder(fishingDto_).mergeFrom(value).buildPartial();
          } else {
            fishingDto_ = value;
          }
          onChanged();
        } else {
          fishingDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      public Builder clearFishingDto() {
        if (fishingDtoBuilder_ == null) {
          fishingDto_ = null;
          onChanged();
        } else {
          fishingDto_ = null;
          fishingDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      public com.dxx.game.dto.FishingProto.FishingDto.Builder getFishingDtoBuilder() {
        
        onChanged();
        return getFishingDtoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      public com.dxx.game.dto.FishingProto.FishingDtoOrBuilder getFishingDtoOrBuilder() {
        if (fishingDtoBuilder_ != null) {
          return fishingDtoBuilder_.getMessageOrBuilder();
        } else {
          return fishingDto_ == null ?
              com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance() : fishingDto_;
        }
      }
      /**
       * <pre>
       * 钓鱼活动数
       * </pre>
       *
       * <code>.Proto.Fishing.FishingDto FishingDto = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishingDto, com.dxx.game.dto.FishingProto.FishingDto.Builder, com.dxx.game.dto.FishingProto.FishingDtoOrBuilder> 
          getFishingDtoFieldBuilder() {
        if (fishingDtoBuilder_ == null) {
          fishingDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.FishingProto.FishingDto, com.dxx.game.dto.FishingProto.FishingDto.Builder, com.dxx.game.dto.FishingProto.FishingDtoOrBuilder>(
                  getFishingDto(),
                  getParentForChildren(),
                  isClean());
          fishingDto_ = null;
        }
        return fishingDtoBuilder_;
      }

      private int unlockRod_ ;
      /**
       * <pre>
       * 解锁的鱼竿
       * </pre>
       *
       * <code>uint32 unlockRod = 6;</code>
       * @return The unlockRod.
       */
      @java.lang.Override
      public int getUnlockRod() {
        return unlockRod_;
      }
      /**
       * <pre>
       * 解锁的鱼竿
       * </pre>
       *
       * <code>uint32 unlockRod = 6;</code>
       * @param value The unlockRod to set.
       * @return This builder for chaining.
       */
      public Builder setUnlockRod(int value) {
        
        unlockRod_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 解锁的鱼竿
       * </pre>
       *
       * <code>uint32 unlockRod = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnlockRod() {
        
        unlockRod_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingReelInResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingReelInResponse)
    private static final com.dxx.game.dto.FishingProto.FishingReelInResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingReelInResponse();
    }

    public static com.dxx.game.dto.FishingProto.FishingReelInResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingReelInResponse>
        PARSER = new com.google.protobuf.AbstractParser<FishingReelInResponse>() {
      @java.lang.Override
      public FishingReelInResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingReelInResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingReelInResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingReelInResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingReelInResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingBuyBaitRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingBuyBaitRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 购买数量
     * </pre>
     *
     * <code>uint32 buyNum = 2;</code>
     * @return The buyNum.
     */
    int getBuyNum();
  }
  /**
   * <pre>
   *CMD PackageId=11407 钓鱼活动-购买鱼饵
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingBuyBaitRequest}
   */
  public static final class FishingBuyBaitRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingBuyBaitRequest)
      FishingBuyBaitRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingBuyBaitRequest.newBuilder() to construct.
    private FishingBuyBaitRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingBuyBaitRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingBuyBaitRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingBuyBaitRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              buyNum_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.class, com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int BUYNUM_FIELD_NUMBER = 2;
    private int buyNum_;
    /**
     * <pre>
     * 购买数量
     * </pre>
     *
     * <code>uint32 buyNum = 2;</code>
     * @return The buyNum.
     */
    @java.lang.Override
    public int getBuyNum() {
      return buyNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (buyNum_ != 0) {
        output.writeUInt32(2, buyNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (buyNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, buyNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingBuyBaitRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingBuyBaitRequest other = (com.dxx.game.dto.FishingProto.FishingBuyBaitRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getBuyNum()
          != other.getBuyNum()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + BUYNUM_FIELD_NUMBER;
      hash = (53 * hash) + getBuyNum();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingBuyBaitRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11407 钓鱼活动-购买鱼饵
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingBuyBaitRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingBuyBaitRequest)
        com.dxx.game.dto.FishingProto.FishingBuyBaitRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.class, com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        buyNum_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingBuyBaitRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingBuyBaitRequest build() {
        com.dxx.game.dto.FishingProto.FishingBuyBaitRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingBuyBaitRequest buildPartial() {
        com.dxx.game.dto.FishingProto.FishingBuyBaitRequest result = new com.dxx.game.dto.FishingProto.FishingBuyBaitRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.buyNum_ = buyNum_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingBuyBaitRequest) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingBuyBaitRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingBuyBaitRequest other) {
        if (other == com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getBuyNum() != 0) {
          setBuyNum(other.getBuyNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingBuyBaitRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingBuyBaitRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int buyNum_ ;
      /**
       * <pre>
       * 购买数量
       * </pre>
       *
       * <code>uint32 buyNum = 2;</code>
       * @return The buyNum.
       */
      @java.lang.Override
      public int getBuyNum() {
        return buyNum_;
      }
      /**
       * <pre>
       * 购买数量
       * </pre>
       *
       * <code>uint32 buyNum = 2;</code>
       * @param value The buyNum to set.
       * @return This builder for chaining.
       */
      public Builder setBuyNum(int value) {
        
        buyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 购买数量
       * </pre>
       *
       * <code>uint32 buyNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyNum() {
        
        buyNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingBuyBaitRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingBuyBaitRequest)
    private static final com.dxx.game.dto.FishingProto.FishingBuyBaitRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingBuyBaitRequest();
    }

    public static com.dxx.game.dto.FishingProto.FishingBuyBaitRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingBuyBaitRequest>
        PARSER = new com.google.protobuf.AbstractParser<FishingBuyBaitRequest>() {
      @java.lang.Override
      public FishingBuyBaitRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingBuyBaitRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingBuyBaitRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingBuyBaitRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingBuyBaitRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingBuyBaitResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingBuyBaitResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=11408
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingBuyBaitResponse}
   */
  public static final class FishingBuyBaitResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingBuyBaitResponse)
      FishingBuyBaitResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingBuyBaitResponse.newBuilder() to construct.
    private FishingBuyBaitResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingBuyBaitResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingBuyBaitResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingBuyBaitResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.class, com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingBuyBaitResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingBuyBaitResponse other = (com.dxx.game.dto.FishingProto.FishingBuyBaitResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingBuyBaitResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11408
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingBuyBaitResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingBuyBaitResponse)
        com.dxx.game.dto.FishingProto.FishingBuyBaitResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.class, com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingBuyBaitResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingBuyBaitResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingBuyBaitResponse build() {
        com.dxx.game.dto.FishingProto.FishingBuyBaitResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingBuyBaitResponse buildPartial() {
        com.dxx.game.dto.FishingProto.FishingBuyBaitResponse result = new com.dxx.game.dto.FishingProto.FishingBuyBaitResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingBuyBaitResponse) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingBuyBaitResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingBuyBaitResponse other) {
        if (other == com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingBuyBaitResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingBuyBaitResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingBuyBaitResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingBuyBaitResponse)
    private static final com.dxx.game.dto.FishingProto.FishingBuyBaitResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingBuyBaitResponse();
    }

    public static com.dxx.game.dto.FishingProto.FishingBuyBaitResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingBuyBaitResponse>
        PARSER = new com.google.protobuf.AbstractParser<FishingBuyBaitResponse>() {
      @java.lang.Override
      public FishingBuyBaitResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingBuyBaitResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingBuyBaitResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingBuyBaitResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingBuyBaitResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingRebornRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingRebornRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 看广告
     * </pre>
     *
     * <code>bool isAd = 2;</code>
     * @return The isAd.
     */
    boolean getIsAd();
  }
  /**
   * <pre>
   *CMD PackageId=11409 钓鱼活动-复活
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingRebornRequest}
   */
  public static final class FishingRebornRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingRebornRequest)
      FishingRebornRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingRebornRequest.newBuilder() to construct.
    private FishingRebornRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingRebornRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingRebornRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingRebornRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              isAd_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingRebornRequest.class, com.dxx.game.dto.FishingProto.FishingRebornRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int ISAD_FIELD_NUMBER = 2;
    private boolean isAd_;
    /**
     * <pre>
     * 看广告
     * </pre>
     *
     * <code>bool isAd = 2;</code>
     * @return The isAd.
     */
    @java.lang.Override
    public boolean getIsAd() {
      return isAd_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (isAd_ != false) {
        output.writeBool(2, isAd_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (isAd_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isAd_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingRebornRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingRebornRequest other = (com.dxx.game.dto.FishingProto.FishingRebornRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getIsAd()
          != other.getIsAd()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ISAD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsAd());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingRebornRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11409 钓鱼活动-复活
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingRebornRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingRebornRequest)
        com.dxx.game.dto.FishingProto.FishingRebornRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingRebornRequest.class, com.dxx.game.dto.FishingProto.FishingRebornRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingRebornRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        isAd_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingRebornRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingRebornRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingRebornRequest build() {
        com.dxx.game.dto.FishingProto.FishingRebornRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingRebornRequest buildPartial() {
        com.dxx.game.dto.FishingProto.FishingRebornRequest result = new com.dxx.game.dto.FishingProto.FishingRebornRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.isAd_ = isAd_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingRebornRequest) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingRebornRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingRebornRequest other) {
        if (other == com.dxx.game.dto.FishingProto.FishingRebornRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getIsAd() != false) {
          setIsAd(other.getIsAd());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingRebornRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingRebornRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private boolean isAd_ ;
      /**
       * <pre>
       * 看广告
       * </pre>
       *
       * <code>bool isAd = 2;</code>
       * @return The isAd.
       */
      @java.lang.Override
      public boolean getIsAd() {
        return isAd_;
      }
      /**
       * <pre>
       * 看广告
       * </pre>
       *
       * <code>bool isAd = 2;</code>
       * @param value The isAd to set.
       * @return This builder for chaining.
       */
      public Builder setIsAd(boolean value) {
        
        isAd_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 看广告
       * </pre>
       *
       * <code>bool isAd = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAd() {
        
        isAd_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingRebornRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingRebornRequest)
    private static final com.dxx.game.dto.FishingProto.FishingRebornRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingRebornRequest();
    }

    public static com.dxx.game.dto.FishingProto.FishingRebornRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingRebornRequest>
        PARSER = new com.google.protobuf.AbstractParser<FishingRebornRequest>() {
      @java.lang.Override
      public FishingRebornRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingRebornRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingRebornRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingRebornRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingRebornRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingRebornResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingRebornResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 下次复活需要钻石
     * </pre>
     *
     * <code>uint32 nextRebornDiamond = 3;</code>
     * @return The nextRebornDiamond.
     */
    int getNextRebornDiamond();
  }
  /**
   * <pre>
   *CMD PackageId=11410
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingRebornResponse}
   */
  public static final class FishingRebornResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingRebornResponse)
      FishingRebornResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingRebornResponse.newBuilder() to construct.
    private FishingRebornResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingRebornResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingRebornResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingRebornResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              nextRebornDiamond_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingRebornResponse.class, com.dxx.game.dto.FishingProto.FishingRebornResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int NEXTREBORNDIAMOND_FIELD_NUMBER = 3;
    private int nextRebornDiamond_;
    /**
     * <pre>
     * 下次复活需要钻石
     * </pre>
     *
     * <code>uint32 nextRebornDiamond = 3;</code>
     * @return The nextRebornDiamond.
     */
    @java.lang.Override
    public int getNextRebornDiamond() {
      return nextRebornDiamond_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (nextRebornDiamond_ != 0) {
        output.writeUInt32(3, nextRebornDiamond_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (nextRebornDiamond_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, nextRebornDiamond_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingRebornResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingRebornResponse other = (com.dxx.game.dto.FishingProto.FishingRebornResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getNextRebornDiamond()
          != other.getNextRebornDiamond()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + NEXTREBORNDIAMOND_FIELD_NUMBER;
      hash = (53 * hash) + getNextRebornDiamond();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingRebornResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingRebornResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11410
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingRebornResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingRebornResponse)
        com.dxx.game.dto.FishingProto.FishingRebornResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingRebornResponse.class, com.dxx.game.dto.FishingProto.FishingRebornResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingRebornResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        nextRebornDiamond_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingRebornResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingRebornResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingRebornResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingRebornResponse build() {
        com.dxx.game.dto.FishingProto.FishingRebornResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingRebornResponse buildPartial() {
        com.dxx.game.dto.FishingProto.FishingRebornResponse result = new com.dxx.game.dto.FishingProto.FishingRebornResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.nextRebornDiamond_ = nextRebornDiamond_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingRebornResponse) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingRebornResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingRebornResponse other) {
        if (other == com.dxx.game.dto.FishingProto.FishingRebornResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getNextRebornDiamond() != 0) {
          setNextRebornDiamond(other.getNextRebornDiamond());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingRebornResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingRebornResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int nextRebornDiamond_ ;
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 3;</code>
       * @return The nextRebornDiamond.
       */
      @java.lang.Override
      public int getNextRebornDiamond() {
        return nextRebornDiamond_;
      }
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 3;</code>
       * @param value The nextRebornDiamond to set.
       * @return This builder for chaining.
       */
      public Builder setNextRebornDiamond(int value) {
        
        nextRebornDiamond_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRebornDiamond() {
        
        nextRebornDiamond_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingRebornResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingRebornResponse)
    private static final com.dxx.game.dto.FishingProto.FishingRebornResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingRebornResponse();
    }

    public static com.dxx.game.dto.FishingProto.FishingRebornResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingRebornResponse>
        PARSER = new com.google.protobuf.AbstractParser<FishingRebornResponse>() {
      @java.lang.Override
      public FishingRebornResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingRebornResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingRebornResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingRebornResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingRebornResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 累计已钓KG
     * </pre>
     *
     * <code>uint32 accWeight = 1;</code>
     * @return The accWeight.
     */
    int getAccWeight();

    /**
     * <pre>
     * 当前鱼竿
     * </pre>
     *
     * <code>uint32 curRod = 2;</code>
     * @return The curRod.
     */
    int getCurRod();

    /**
     * <pre>
     * 配置
     * </pre>
     *
     * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
     * @return Whether the configDto field is set.
     */
    boolean hasConfigDto();
    /**
     * <pre>
     * 配置
     * </pre>
     *
     * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
     * @return The configDto.
     */
    com.dxx.game.dto.FishingProto.FishingConfigDto getConfigDto();
    /**
     * <pre>
     * 配置
     * </pre>
     *
     * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
     */
    com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder getConfigDtoOrBuilder();

    /**
     * <pre>
     * 下次复活需要钻石
     * </pre>
     *
     * <code>uint32 nextRebornDiamond = 5;</code>
     * @return The nextRebornDiamond.
     */
    int getNextRebornDiamond();

    /**
     * <pre>
     * 是否已抛竿
     * </pre>
     *
     * <code>bool isThrow = 6;</code>
     * @return The isThrow.
     */
    boolean getIsThrow();
  }
  /**
   * <pre>
   * 钓鱼活动数据
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingDto}
   */
  public static final class FishingDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingDto)
      FishingDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingDto.newBuilder() to construct.
    private FishingDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingDto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              accWeight_ = input.readUInt32();
              break;
            }
            case 16: {

              curRod_ = input.readUInt32();
              break;
            }
            case 34: {
              com.dxx.game.dto.FishingProto.FishingConfigDto.Builder subBuilder = null;
              if (configDto_ != null) {
                subBuilder = configDto_.toBuilder();
              }
              configDto_ = input.readMessage(com.dxx.game.dto.FishingProto.FishingConfigDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(configDto_);
                configDto_ = subBuilder.buildPartial();
              }

              break;
            }
            case 40: {

              nextRebornDiamond_ = input.readUInt32();
              break;
            }
            case 48: {

              isThrow_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingDto.class, com.dxx.game.dto.FishingProto.FishingDto.Builder.class);
    }

    public static final int ACCWEIGHT_FIELD_NUMBER = 1;
    private int accWeight_;
    /**
     * <pre>
     * 累计已钓KG
     * </pre>
     *
     * <code>uint32 accWeight = 1;</code>
     * @return The accWeight.
     */
    @java.lang.Override
    public int getAccWeight() {
      return accWeight_;
    }

    public static final int CURROD_FIELD_NUMBER = 2;
    private int curRod_;
    /**
     * <pre>
     * 当前鱼竿
     * </pre>
     *
     * <code>uint32 curRod = 2;</code>
     * @return The curRod.
     */
    @java.lang.Override
    public int getCurRod() {
      return curRod_;
    }

    public static final int CONFIGDTO_FIELD_NUMBER = 4;
    private com.dxx.game.dto.FishingProto.FishingConfigDto configDto_;
    /**
     * <pre>
     * 配置
     * </pre>
     *
     * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
     * @return Whether the configDto field is set.
     */
    @java.lang.Override
    public boolean hasConfigDto() {
      return configDto_ != null;
    }
    /**
     * <pre>
     * 配置
     * </pre>
     *
     * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
     * @return The configDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingConfigDto getConfigDto() {
      return configDto_ == null ? com.dxx.game.dto.FishingProto.FishingConfigDto.getDefaultInstance() : configDto_;
    }
    /**
     * <pre>
     * 配置
     * </pre>
     *
     * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder getConfigDtoOrBuilder() {
      return getConfigDto();
    }

    public static final int NEXTREBORNDIAMOND_FIELD_NUMBER = 5;
    private int nextRebornDiamond_;
    /**
     * <pre>
     * 下次复活需要钻石
     * </pre>
     *
     * <code>uint32 nextRebornDiamond = 5;</code>
     * @return The nextRebornDiamond.
     */
    @java.lang.Override
    public int getNextRebornDiamond() {
      return nextRebornDiamond_;
    }

    public static final int ISTHROW_FIELD_NUMBER = 6;
    private boolean isThrow_;
    /**
     * <pre>
     * 是否已抛竿
     * </pre>
     *
     * <code>bool isThrow = 6;</code>
     * @return The isThrow.
     */
    @java.lang.Override
    public boolean getIsThrow() {
      return isThrow_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (accWeight_ != 0) {
        output.writeUInt32(1, accWeight_);
      }
      if (curRod_ != 0) {
        output.writeUInt32(2, curRod_);
      }
      if (configDto_ != null) {
        output.writeMessage(4, getConfigDto());
      }
      if (nextRebornDiamond_ != 0) {
        output.writeUInt32(5, nextRebornDiamond_);
      }
      if (isThrow_ != false) {
        output.writeBool(6, isThrow_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (accWeight_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, accWeight_);
      }
      if (curRod_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, curRod_);
      }
      if (configDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getConfigDto());
      }
      if (nextRebornDiamond_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, nextRebornDiamond_);
      }
      if (isThrow_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(6, isThrow_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingDto other = (com.dxx.game.dto.FishingProto.FishingDto) obj;

      if (getAccWeight()
          != other.getAccWeight()) return false;
      if (getCurRod()
          != other.getCurRod()) return false;
      if (hasConfigDto() != other.hasConfigDto()) return false;
      if (hasConfigDto()) {
        if (!getConfigDto()
            .equals(other.getConfigDto())) return false;
      }
      if (getNextRebornDiamond()
          != other.getNextRebornDiamond()) return false;
      if (getIsThrow()
          != other.getIsThrow()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ACCWEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getAccWeight();
      hash = (37 * hash) + CURROD_FIELD_NUMBER;
      hash = (53 * hash) + getCurRod();
      if (hasConfigDto()) {
        hash = (37 * hash) + CONFIGDTO_FIELD_NUMBER;
        hash = (53 * hash) + getConfigDto().hashCode();
      }
      hash = (37 * hash) + NEXTREBORNDIAMOND_FIELD_NUMBER;
      hash = (53 * hash) + getNextRebornDiamond();
      hash = (37 * hash) + ISTHROW_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsThrow());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 钓鱼活动数据
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingDto)
        com.dxx.game.dto.FishingProto.FishingDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingDto.class, com.dxx.game.dto.FishingProto.FishingDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        accWeight_ = 0;

        curRod_ = 0;

        if (configDtoBuilder_ == null) {
          configDto_ = null;
        } else {
          configDto_ = null;
          configDtoBuilder_ = null;
        }
        nextRebornDiamond_ = 0;

        isThrow_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingDto getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingDto build() {
        com.dxx.game.dto.FishingProto.FishingDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingDto buildPartial() {
        com.dxx.game.dto.FishingProto.FishingDto result = new com.dxx.game.dto.FishingProto.FishingDto(this);
        result.accWeight_ = accWeight_;
        result.curRod_ = curRod_;
        if (configDtoBuilder_ == null) {
          result.configDto_ = configDto_;
        } else {
          result.configDto_ = configDtoBuilder_.build();
        }
        result.nextRebornDiamond_ = nextRebornDiamond_;
        result.isThrow_ = isThrow_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingDto) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingDto other) {
        if (other == com.dxx.game.dto.FishingProto.FishingDto.getDefaultInstance()) return this;
        if (other.getAccWeight() != 0) {
          setAccWeight(other.getAccWeight());
        }
        if (other.getCurRod() != 0) {
          setCurRod(other.getCurRod());
        }
        if (other.hasConfigDto()) {
          mergeConfigDto(other.getConfigDto());
        }
        if (other.getNextRebornDiamond() != 0) {
          setNextRebornDiamond(other.getNextRebornDiamond());
        }
        if (other.getIsThrow() != false) {
          setIsThrow(other.getIsThrow());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int accWeight_ ;
      /**
       * <pre>
       * 累计已钓KG
       * </pre>
       *
       * <code>uint32 accWeight = 1;</code>
       * @return The accWeight.
       */
      @java.lang.Override
      public int getAccWeight() {
        return accWeight_;
      }
      /**
       * <pre>
       * 累计已钓KG
       * </pre>
       *
       * <code>uint32 accWeight = 1;</code>
       * @param value The accWeight to set.
       * @return This builder for chaining.
       */
      public Builder setAccWeight(int value) {
        
        accWeight_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 累计已钓KG
       * </pre>
       *
       * <code>uint32 accWeight = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccWeight() {
        
        accWeight_ = 0;
        onChanged();
        return this;
      }

      private int curRod_ ;
      /**
       * <pre>
       * 当前鱼竿
       * </pre>
       *
       * <code>uint32 curRod = 2;</code>
       * @return The curRod.
       */
      @java.lang.Override
      public int getCurRod() {
        return curRod_;
      }
      /**
       * <pre>
       * 当前鱼竿
       * </pre>
       *
       * <code>uint32 curRod = 2;</code>
       * @param value The curRod to set.
       * @return This builder for chaining.
       */
      public Builder setCurRod(int value) {
        
        curRod_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前鱼竿
       * </pre>
       *
       * <code>uint32 curRod = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurRod() {
        
        curRod_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.FishingProto.FishingConfigDto configDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishingConfigDto, com.dxx.game.dto.FishingProto.FishingConfigDto.Builder, com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder> configDtoBuilder_;
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       * @return Whether the configDto field is set.
       */
      public boolean hasConfigDto() {
        return configDtoBuilder_ != null || configDto_ != null;
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       * @return The configDto.
       */
      public com.dxx.game.dto.FishingProto.FishingConfigDto getConfigDto() {
        if (configDtoBuilder_ == null) {
          return configDto_ == null ? com.dxx.game.dto.FishingProto.FishingConfigDto.getDefaultInstance() : configDto_;
        } else {
          return configDtoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      public Builder setConfigDto(com.dxx.game.dto.FishingProto.FishingConfigDto value) {
        if (configDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          configDto_ = value;
          onChanged();
        } else {
          configDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      public Builder setConfigDto(
          com.dxx.game.dto.FishingProto.FishingConfigDto.Builder builderForValue) {
        if (configDtoBuilder_ == null) {
          configDto_ = builderForValue.build();
          onChanged();
        } else {
          configDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      public Builder mergeConfigDto(com.dxx.game.dto.FishingProto.FishingConfigDto value) {
        if (configDtoBuilder_ == null) {
          if (configDto_ != null) {
            configDto_ =
              com.dxx.game.dto.FishingProto.FishingConfigDto.newBuilder(configDto_).mergeFrom(value).buildPartial();
          } else {
            configDto_ = value;
          }
          onChanged();
        } else {
          configDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      public Builder clearConfigDto() {
        if (configDtoBuilder_ == null) {
          configDto_ = null;
          onChanged();
        } else {
          configDto_ = null;
          configDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      public com.dxx.game.dto.FishingProto.FishingConfigDto.Builder getConfigDtoBuilder() {
        
        onChanged();
        return getConfigDtoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      public com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder getConfigDtoOrBuilder() {
        if (configDtoBuilder_ != null) {
          return configDtoBuilder_.getMessageOrBuilder();
        } else {
          return configDto_ == null ?
              com.dxx.game.dto.FishingProto.FishingConfigDto.getDefaultInstance() : configDto_;
        }
      }
      /**
       * <pre>
       * 配置
       * </pre>
       *
       * <code>.Proto.Fishing.FishingConfigDto configDto = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.FishingProto.FishingConfigDto, com.dxx.game.dto.FishingProto.FishingConfigDto.Builder, com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder> 
          getConfigDtoFieldBuilder() {
        if (configDtoBuilder_ == null) {
          configDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.FishingProto.FishingConfigDto, com.dxx.game.dto.FishingProto.FishingConfigDto.Builder, com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder>(
                  getConfigDto(),
                  getParentForChildren(),
                  isClean());
          configDto_ = null;
        }
        return configDtoBuilder_;
      }

      private int nextRebornDiamond_ ;
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 5;</code>
       * @return The nextRebornDiamond.
       */
      @java.lang.Override
      public int getNextRebornDiamond() {
        return nextRebornDiamond_;
      }
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 5;</code>
       * @param value The nextRebornDiamond to set.
       * @return This builder for chaining.
       */
      public Builder setNextRebornDiamond(int value) {
        
        nextRebornDiamond_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次复活需要钻石
       * </pre>
       *
       * <code>uint32 nextRebornDiamond = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRebornDiamond() {
        
        nextRebornDiamond_ = 0;
        onChanged();
        return this;
      }

      private boolean isThrow_ ;
      /**
       * <pre>
       * 是否已抛竿
       * </pre>
       *
       * <code>bool isThrow = 6;</code>
       * @return The isThrow.
       */
      @java.lang.Override
      public boolean getIsThrow() {
        return isThrow_;
      }
      /**
       * <pre>
       * 是否已抛竿
       * </pre>
       *
       * <code>bool isThrow = 6;</code>
       * @param value The isThrow to set.
       * @return This builder for chaining.
       */
      public Builder setIsThrow(boolean value) {
        
        isThrow_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否已抛竿
       * </pre>
       *
       * <code>bool isThrow = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsThrow() {
        
        isThrow_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingDto)
    private static final com.dxx.game.dto.FishingProto.FishingDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingDto();
    }

    public static com.dxx.game.dto.FishingProto.FishingDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingDto>
        PARSER = new com.google.protobuf.AbstractParser<FishingDto>() {
      @java.lang.Override
      public FishingDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishingConfigDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishingConfigDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 鱼饵道具ID
     * </pre>
     *
     * <code>uint32 baitItemId = 1;</code>
     * @return The baitItemId.
     */
    int getBaitItemId();

    /**
     * <pre>
     * 积分道具ID
     * </pre>
     *
     * <code>uint32 pointItemId = 2;</code>
     * @return The pointItemId.
     */
    int getPointItemId();

    /**
     * <pre>
     * 断了的线道具ID
     * </pre>
     *
     * <code>uint32 lineItemId = 3;</code>
     * @return The lineItemId.
     */
    int getLineItemId();

    /**
     * <pre>
     * 鱼饵钻石售价
     * </pre>
     *
     * <code>uint32 baitPrice = 5;</code>
     * @return The baitPrice.
     */
    int getBaitPrice();
  }
  /**
   * <pre>
   * 钓鱼配置
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishingConfigDto}
   */
  public static final class FishingConfigDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishingConfigDto)
      FishingConfigDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishingConfigDto.newBuilder() to construct.
    private FishingConfigDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishingConfigDto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishingConfigDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishingConfigDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              baitItemId_ = input.readUInt32();
              break;
            }
            case 16: {

              pointItemId_ = input.readUInt32();
              break;
            }
            case 24: {

              lineItemId_ = input.readUInt32();
              break;
            }
            case 40: {

              baitPrice_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingConfigDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingConfigDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishingConfigDto.class, com.dxx.game.dto.FishingProto.FishingConfigDto.Builder.class);
    }

    public static final int BAITITEMID_FIELD_NUMBER = 1;
    private int baitItemId_;
    /**
     * <pre>
     * 鱼饵道具ID
     * </pre>
     *
     * <code>uint32 baitItemId = 1;</code>
     * @return The baitItemId.
     */
    @java.lang.Override
    public int getBaitItemId() {
      return baitItemId_;
    }

    public static final int POINTITEMID_FIELD_NUMBER = 2;
    private int pointItemId_;
    /**
     * <pre>
     * 积分道具ID
     * </pre>
     *
     * <code>uint32 pointItemId = 2;</code>
     * @return The pointItemId.
     */
    @java.lang.Override
    public int getPointItemId() {
      return pointItemId_;
    }

    public static final int LINEITEMID_FIELD_NUMBER = 3;
    private int lineItemId_;
    /**
     * <pre>
     * 断了的线道具ID
     * </pre>
     *
     * <code>uint32 lineItemId = 3;</code>
     * @return The lineItemId.
     */
    @java.lang.Override
    public int getLineItemId() {
      return lineItemId_;
    }

    public static final int BAITPRICE_FIELD_NUMBER = 5;
    private int baitPrice_;
    /**
     * <pre>
     * 鱼饵钻石售价
     * </pre>
     *
     * <code>uint32 baitPrice = 5;</code>
     * @return The baitPrice.
     */
    @java.lang.Override
    public int getBaitPrice() {
      return baitPrice_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (baitItemId_ != 0) {
        output.writeUInt32(1, baitItemId_);
      }
      if (pointItemId_ != 0) {
        output.writeUInt32(2, pointItemId_);
      }
      if (lineItemId_ != 0) {
        output.writeUInt32(3, lineItemId_);
      }
      if (baitPrice_ != 0) {
        output.writeUInt32(5, baitPrice_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (baitItemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, baitItemId_);
      }
      if (pointItemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, pointItemId_);
      }
      if (lineItemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, lineItemId_);
      }
      if (baitPrice_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, baitPrice_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishingConfigDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishingConfigDto other = (com.dxx.game.dto.FishingProto.FishingConfigDto) obj;

      if (getBaitItemId()
          != other.getBaitItemId()) return false;
      if (getPointItemId()
          != other.getPointItemId()) return false;
      if (getLineItemId()
          != other.getLineItemId()) return false;
      if (getBaitPrice()
          != other.getBaitPrice()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BAITITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getBaitItemId();
      hash = (37 * hash) + POINTITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getPointItemId();
      hash = (37 * hash) + LINEITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getLineItemId();
      hash = (37 * hash) + BAITPRICE_FIELD_NUMBER;
      hash = (53 * hash) + getBaitPrice();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishingConfigDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishingConfigDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 钓鱼配置
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishingConfigDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishingConfigDto)
        com.dxx.game.dto.FishingProto.FishingConfigDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingConfigDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingConfigDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishingConfigDto.class, com.dxx.game.dto.FishingProto.FishingConfigDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishingConfigDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        baitItemId_ = 0;

        pointItemId_ = 0;

        lineItemId_ = 0;

        baitPrice_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishingConfigDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingConfigDto getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishingConfigDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingConfigDto build() {
        com.dxx.game.dto.FishingProto.FishingConfigDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishingConfigDto buildPartial() {
        com.dxx.game.dto.FishingProto.FishingConfigDto result = new com.dxx.game.dto.FishingProto.FishingConfigDto(this);
        result.baitItemId_ = baitItemId_;
        result.pointItemId_ = pointItemId_;
        result.lineItemId_ = lineItemId_;
        result.baitPrice_ = baitPrice_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishingConfigDto) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishingConfigDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishingConfigDto other) {
        if (other == com.dxx.game.dto.FishingProto.FishingConfigDto.getDefaultInstance()) return this;
        if (other.getBaitItemId() != 0) {
          setBaitItemId(other.getBaitItemId());
        }
        if (other.getPointItemId() != 0) {
          setPointItemId(other.getPointItemId());
        }
        if (other.getLineItemId() != 0) {
          setLineItemId(other.getLineItemId());
        }
        if (other.getBaitPrice() != 0) {
          setBaitPrice(other.getBaitPrice());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishingConfigDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishingConfigDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int baitItemId_ ;
      /**
       * <pre>
       * 鱼饵道具ID
       * </pre>
       *
       * <code>uint32 baitItemId = 1;</code>
       * @return The baitItemId.
       */
      @java.lang.Override
      public int getBaitItemId() {
        return baitItemId_;
      }
      /**
       * <pre>
       * 鱼饵道具ID
       * </pre>
       *
       * <code>uint32 baitItemId = 1;</code>
       * @param value The baitItemId to set.
       * @return This builder for chaining.
       */
      public Builder setBaitItemId(int value) {
        
        baitItemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼饵道具ID
       * </pre>
       *
       * <code>uint32 baitItemId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBaitItemId() {
        
        baitItemId_ = 0;
        onChanged();
        return this;
      }

      private int pointItemId_ ;
      /**
       * <pre>
       * 积分道具ID
       * </pre>
       *
       * <code>uint32 pointItemId = 2;</code>
       * @return The pointItemId.
       */
      @java.lang.Override
      public int getPointItemId() {
        return pointItemId_;
      }
      /**
       * <pre>
       * 积分道具ID
       * </pre>
       *
       * <code>uint32 pointItemId = 2;</code>
       * @param value The pointItemId to set.
       * @return This builder for chaining.
       */
      public Builder setPointItemId(int value) {
        
        pointItemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 积分道具ID
       * </pre>
       *
       * <code>uint32 pointItemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPointItemId() {
        
        pointItemId_ = 0;
        onChanged();
        return this;
      }

      private int lineItemId_ ;
      /**
       * <pre>
       * 断了的线道具ID
       * </pre>
       *
       * <code>uint32 lineItemId = 3;</code>
       * @return The lineItemId.
       */
      @java.lang.Override
      public int getLineItemId() {
        return lineItemId_;
      }
      /**
       * <pre>
       * 断了的线道具ID
       * </pre>
       *
       * <code>uint32 lineItemId = 3;</code>
       * @param value The lineItemId to set.
       * @return This builder for chaining.
       */
      public Builder setLineItemId(int value) {
        
        lineItemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 断了的线道具ID
       * </pre>
       *
       * <code>uint32 lineItemId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineItemId() {
        
        lineItemId_ = 0;
        onChanged();
        return this;
      }

      private int baitPrice_ ;
      /**
       * <pre>
       * 鱼饵钻石售价
       * </pre>
       *
       * <code>uint32 baitPrice = 5;</code>
       * @return The baitPrice.
       */
      @java.lang.Override
      public int getBaitPrice() {
        return baitPrice_;
      }
      /**
       * <pre>
       * 鱼饵钻石售价
       * </pre>
       *
       * <code>uint32 baitPrice = 5;</code>
       * @param value The baitPrice to set.
       * @return This builder for chaining.
       */
      public Builder setBaitPrice(int value) {
        
        baitPrice_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼饵钻石售价
       * </pre>
       *
       * <code>uint32 baitPrice = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBaitPrice() {
        
        baitPrice_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishingConfigDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishingConfigDto)
    private static final com.dxx.game.dto.FishingProto.FishingConfigDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishingConfigDto();
    }

    public static com.dxx.game.dto.FishingProto.FishingConfigDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishingConfigDto>
        PARSER = new com.google.protobuf.AbstractParser<FishingConfigDto>() {
      @java.lang.Override
      public FishingConfigDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishingConfigDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishingConfigDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishingConfigDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishingConfigDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FishDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Fishing.FishDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>uint32 fishId = 1;</code>
     * @return The fishId.
     */
    int getFishId();

    /**
     * <pre>
     * 鱼重量
     * </pre>
     *
     * <code>uint32 weight = 2;</code>
     * @return The weight.
     */
    int getWeight();
  }
  /**
   * <pre>
   * 鱼
   * </pre>
   *
   * Protobuf type {@code Proto.Fishing.FishDto}
   */
  public static final class FishDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Fishing.FishDto)
      FishDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FishDto.newBuilder() to construct.
    private FishDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FishDto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FishDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FishDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              fishId_ = input.readUInt32();
              break;
            }
            case 16: {

              weight_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.FishingProto.FishDto.class, com.dxx.game.dto.FishingProto.FishDto.Builder.class);
    }

    public static final int FISHID_FIELD_NUMBER = 1;
    private int fishId_;
    /**
     * <pre>
     * 鱼ID
     * </pre>
     *
     * <code>uint32 fishId = 1;</code>
     * @return The fishId.
     */
    @java.lang.Override
    public int getFishId() {
      return fishId_;
    }

    public static final int WEIGHT_FIELD_NUMBER = 2;
    private int weight_;
    /**
     * <pre>
     * 鱼重量
     * </pre>
     *
     * <code>uint32 weight = 2;</code>
     * @return The weight.
     */
    @java.lang.Override
    public int getWeight() {
      return weight_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (fishId_ != 0) {
        output.writeUInt32(1, fishId_);
      }
      if (weight_ != 0) {
        output.writeUInt32(2, weight_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (fishId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, fishId_);
      }
      if (weight_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, weight_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.FishingProto.FishDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.FishingProto.FishDto other = (com.dxx.game.dto.FishingProto.FishDto) obj;

      if (getFishId()
          != other.getFishId()) return false;
      if (getWeight()
          != other.getWeight()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FISHID_FIELD_NUMBER;
      hash = (53 * hash) + getFishId();
      hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getWeight();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.FishingProto.FishDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.FishingProto.FishDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 鱼
     * </pre>
     *
     * Protobuf type {@code Proto.Fishing.FishDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Fishing.FishDto)
        com.dxx.game.dto.FishingProto.FishDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.FishingProto.FishDto.class, com.dxx.game.dto.FishingProto.FishDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.FishingProto.FishDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fishId_ = 0;

        weight_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.FishingProto.internal_static_Proto_Fishing_FishDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishDto getDefaultInstanceForType() {
        return com.dxx.game.dto.FishingProto.FishDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishDto build() {
        com.dxx.game.dto.FishingProto.FishDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.FishingProto.FishDto buildPartial() {
        com.dxx.game.dto.FishingProto.FishDto result = new com.dxx.game.dto.FishingProto.FishDto(this);
        result.fishId_ = fishId_;
        result.weight_ = weight_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.FishingProto.FishDto) {
          return mergeFrom((com.dxx.game.dto.FishingProto.FishDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.FishingProto.FishDto other) {
        if (other == com.dxx.game.dto.FishingProto.FishDto.getDefaultInstance()) return this;
        if (other.getFishId() != 0) {
          setFishId(other.getFishId());
        }
        if (other.getWeight() != 0) {
          setWeight(other.getWeight());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.FishingProto.FishDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.FishingProto.FishDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int fishId_ ;
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>uint32 fishId = 1;</code>
       * @return The fishId.
       */
      @java.lang.Override
      public int getFishId() {
        return fishId_;
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>uint32 fishId = 1;</code>
       * @param value The fishId to set.
       * @return This builder for chaining.
       */
      public Builder setFishId(int value) {
        
        fishId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼ID
       * </pre>
       *
       * <code>uint32 fishId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFishId() {
        
        fishId_ = 0;
        onChanged();
        return this;
      }

      private int weight_ ;
      /**
       * <pre>
       * 鱼重量
       * </pre>
       *
       * <code>uint32 weight = 2;</code>
       * @return The weight.
       */
      @java.lang.Override
      public int getWeight() {
        return weight_;
      }
      /**
       * <pre>
       * 鱼重量
       * </pre>
       *
       * <code>uint32 weight = 2;</code>
       * @param value The weight to set.
       * @return This builder for chaining.
       */
      public Builder setWeight(int value) {
        
        weight_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鱼重量
       * </pre>
       *
       * <code>uint32 weight = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeight() {
        
        weight_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Fishing.FishDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Fishing.FishDto)
    private static final com.dxx.game.dto.FishingProto.FishDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.FishingProto.FishDto();
    }

    public static com.dxx.game.dto.FishingProto.FishDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FishDto>
        PARSER = new com.google.protobuf.AbstractParser<FishDto>() {
      @java.lang.Override
      public FishDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FishDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FishDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FishDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.FishingProto.FishDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingOnOpenRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingOnOpenRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingOnOpenResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingOnOpenResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingCastRodRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingCastRodRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingCastRodResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingCastRodResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingReelInRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingReelInRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingReelInResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingReelInResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingBuyBaitRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingBuyBaitRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingBuyBaitResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingBuyBaitResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingRebornRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingRebornRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingRebornResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingRebornResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishingConfigDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishingConfigDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Fishing_FishDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Fishing_FishDto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rfishing.proto\022\rProto.Fishing\032\014common.p" +
      "roto\"H\n\024FishingOnOpenRequest\0220\n\014commonPa" +
      "rams\030\001 \001(\0132\032.Proto.Common.CommonParams\"|" +
      "\n\025FishingOnOpenResponse\022\014\n\004code\030\001 \001(\005\022,\n" +
      "\ncommonData\030\002 \001(\0132\030.Proto.Common.CommonD" +
      "ata\022\'\n\004fish\030\003 \001(\0132\031.Proto.Fishing.Fishin" +
      "gDto\"h\n\025FishingCastRodRequest\0220\n\014commonP" +
      "arams\030\001 \001(\0132\032.Proto.Common.CommonParams\022" +
      "\017\n\007baitNum\030\002 \001(\r\022\014\n\004eval\030\003 \001(\r\"\200\001\n\026Fishi" +
      "ngCastRodResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommo" +
      "nData\030\002 \001(\0132\030.Proto.Common.CommonData\022\017\n" +
      "\007fishIds\030\003 \003(\r\022\031\n\021nextRebornDiamond\030\004 \001(" +
      "\r\"W\n\024FishingReelInRequest\0220\n\014commonParam" +
      "s\030\001 \001(\0132\032.Proto.Common.CommonParams\022\r\n\005c" +
      "atch\030\002 \001(\010\"\317\001\n\025FishingReelInResponse\022\014\n\004" +
      "code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.C" +
      "ommon.CommonData\022(\n\010fishDtos\030\003 \003(\0132\026.Pro" +
      "to.Fishing.FishDto\022\016\n\006weight\030\004 \001(\r\022-\n\nFi" +
      "shingDto\030\005 \001(\0132\031.Proto.Fishing.FishingDt" +
      "o\022\021\n\tunlockRod\030\006 \001(\r\"Y\n\025FishingBuyBaitRe" +
      "quest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Comm" +
      "on.CommonParams\022\016\n\006buyNum\030\002 \001(\r\"T\n\026Fishi" +
      "ngBuyBaitResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommo" +
      "nData\030\002 \001(\0132\030.Proto.Common.CommonData\"V\n" +
      "\024FishingRebornRequest\0220\n\014commonParams\030\001 " +
      "\001(\0132\032.Proto.Common.CommonParams\022\014\n\004isAd\030" +
      "\002 \001(\010\"n\n\025FishingRebornResponse\022\014\n\004code\030\001" +
      " \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Common." +
      "CommonData\022\031\n\021nextRebornDiamond\030\003 \001(\r\"\217\001" +
      "\n\nFishingDto\022\021\n\taccWeight\030\001 \001(\r\022\016\n\006curRo" +
      "d\030\002 \001(\r\0222\n\tconfigDto\030\004 \001(\0132\037.Proto.Fishi" +
      "ng.FishingConfigDto\022\031\n\021nextRebornDiamond" +
      "\030\005 \001(\r\022\017\n\007isThrow\030\006 \001(\010\"b\n\020FishingConfig" +
      "Dto\022\022\n\nbaitItemId\030\001 \001(\r\022\023\n\013pointItemId\030\002" +
      " \001(\r\022\022\n\nlineItemId\030\003 \001(\r\022\021\n\tbaitPrice\030\005 " +
      "\001(\r\")\n\007FishDto\022\016\n\006fishId\030\001 \001(\r\022\016\n\006weight" +
      "\030\002 \001(\rB \n\020com.dxx.game.dtoB\014FishingProto" +
      "b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Fishing_FishingOnOpenRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Fishing_FishingOnOpenRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingOnOpenRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Fishing_FishingOnOpenResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Fishing_FishingOnOpenResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingOnOpenResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Fish", });
    internal_static_Proto_Fishing_FishingCastRodRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Fishing_FishingCastRodRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingCastRodRequest_descriptor,
        new java.lang.String[] { "CommonParams", "BaitNum", "Eval", });
    internal_static_Proto_Fishing_FishingCastRodResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Fishing_FishingCastRodResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingCastRodResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "FishIds", "NextRebornDiamond", });
    internal_static_Proto_Fishing_FishingReelInRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Fishing_FishingReelInRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingReelInRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Catch", });
    internal_static_Proto_Fishing_FishingReelInResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Fishing_FishingReelInResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingReelInResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "FishDtos", "Weight", "FishingDto", "UnlockRod", });
    internal_static_Proto_Fishing_FishingBuyBaitRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Fishing_FishingBuyBaitRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingBuyBaitRequest_descriptor,
        new java.lang.String[] { "CommonParams", "BuyNum", });
    internal_static_Proto_Fishing_FishingBuyBaitResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Fishing_FishingBuyBaitResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingBuyBaitResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Fishing_FishingRebornRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Fishing_FishingRebornRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingRebornRequest_descriptor,
        new java.lang.String[] { "CommonParams", "IsAd", });
    internal_static_Proto_Fishing_FishingRebornResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Fishing_FishingRebornResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingRebornResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "NextRebornDiamond", });
    internal_static_Proto_Fishing_FishingDto_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Fishing_FishingDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingDto_descriptor,
        new java.lang.String[] { "AccWeight", "CurRod", "ConfigDto", "NextRebornDiamond", "IsThrow", });
    internal_static_Proto_Fishing_FishingConfigDto_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_Proto_Fishing_FishingConfigDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishingConfigDto_descriptor,
        new java.lang.String[] { "BaitItemId", "PointItemId", "LineItemId", "BaitPrice", });
    internal_static_Proto_Fishing_FishDto_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_Proto_Fishing_FishDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Fishing_FishDto_descriptor,
        new java.lang.String[] { "FishId", "Weight", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

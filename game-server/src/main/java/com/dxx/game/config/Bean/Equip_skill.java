
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Equip_skill {
    public Equip_skill(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        descId = _buf.get("descId").getAsString();
        attributeType = _buf.get("attributeType").getAsInt();
        action1 = _buf.get("action1").getAsString();
        notes = _buf.get("notes").getAsString();
    }

    public static Equip_skill deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Equip_skill(_buf);
    }

    /**
     * 必须有的ID
     */
    public final int id;
    /**
     * 描述的多语言ID
     */
    public final String descId;
    /**
     * 作用效果类型
     */
    public final int attributeType;
    /**
     * 作用效果
     */
    public final String action1;
    /**
     * 策划用注释
     */
    public final String notes;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "descId:" + descId + ","
        + "attributeType:" + attributeType + ","
        + "action1:" + action1 + ","
        + "notes:" + notes + ","
        + "}";
    }
}



//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFishing_fishRod {
    public ActivityFishing_fishRod(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        nameId = _buf.get("nameId").getAsString();
        atlas = _buf.get("atlas").getAsInt();
        icon = _buf.get("icon").getAsString();
        type = _buf.get("type").getAsInt();
        hp = _buf.get("hp").getAsInt();
        hpRestore = _buf.get("hpRestore").getAsInt();
        strength = _buf.get("strength").getAsInt();
        speed = _buf.get("speed").getAsInt();
        tiresSpeed = _buf.get("tiresSpeed").getAsInt();
    }

    public static ActivityFishing_fishRod deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFishing_fishRod(_buf);
    }

    /**
     * ID
     */
    public final int id;
    /**
     * 名称
     */
    public final String nameId;
    /**
     * 图集
     */
    public final int atlas;
    /**
     * icon
     */
    public final String icon;
    /**
     * 品质
     */
    public final int type;
    /**
     * 血量（杆顺序）
     */
    public final int hp;
    /**
     * 回血速度<br/>每秒
     */
    public final int hpRestore;
    /**
     * 力量
     */
    public final int strength;
    /**
     * 速度
     */
    public final int speed;
    /**
     * 乏力竿速
     */
    public final int tiresSpeed;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "nameId:" + nameId + ","
        + "atlas:" + atlas + ","
        + "icon:" + icon + ","
        + "type:" + type + ","
        + "hp:" + hp + ","
        + "hpRestore:" + hpRestore + ","
        + "strength:" + strength + ","
        + "speed:" + speed + ","
        + "tiresSpeed:" + tiresSpeed + ","
        + "}";
    }
}


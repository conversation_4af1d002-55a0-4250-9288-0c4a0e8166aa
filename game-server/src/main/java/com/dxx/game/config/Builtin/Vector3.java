
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Builtin;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Vector3 {
    public Vector3(JsonObject _buf) { 
        x = _buf.get("x").getAsFloat();
        y = _buf.get("y").getAsFloat();
        z = _buf.get("z").getAsFloat();
    }

    public static Vector3 deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Builtin.Vector3(_buf);
    }

    public final float x;
    public final float y;
    public final float z;

    

    @Override
    public String toString() {
        return "{ "
        + "x:" + x + ","
        + "y:" + y + ","
        + "z:" + z + ","
        + "}";
    }
}


package com.dxx.game.modules.guild.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.GuildProto.*;
import com.google.protobuf.Message;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/18
 * @description:
 */
@ApiHandler
public class GuildBossHandler {

    @Resource
    private GuildBossService guildBossService;

    @ApiMethod(command = MsgReqCommand.GuildBossGetInfoRequest, name = "公会-BOSS-获取数据")
    public Result<GuildBossGetInfoResponse> getInfo(Message msg) {
        GuildBossGetInfoRequest params = (GuildBossGetInfoRequest)msg;
        return guildBossService.getInfoAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildBossStartRequest, name = "公会-BOSS-开始挑战")
    public Result<GuildBossStartResponse> bossStart(Message msg) {
        GuildBossStartRequest params = (GuildBossStartRequest)msg;
//        return guildBossService.bossStartAction(params);
        return null;
    }

    @ApiMethod(command = MsgReqCommand.GuildBossEndRequest, name = "公会-BOSS-战斗结算")
    public Result<GuildBossEndResponse> bossEnd(Message msg) {
        GuildBossEndRequest params = (GuildBossEndRequest)msg;
//        return guildBossService.bossEndAction(params);
        return null;
    }

    @ApiMethod(command = MsgReqCommand.GuildBossBuyCntRequest, name = "公会-BOSS-购买挑战次数")
    public Result<GuildBossBuyCntResponse> bossBuyCnt(Message msg) {
        GuildBossBuyCntRequest params = (GuildBossBuyCntRequest)msg;
        return guildBossService.bossBuyCntAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildBossTaskRewardRequest, name = "公会-BOSS-领取任务奖励")
    public Result<GuildBossTaskRewardResponse> bossTaskReward(Message msg) {
        GuildBossTaskRewardRequest params = (GuildBossTaskRewardRequest)msg;
        return guildBossService.bossTaskRewardAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildBossBoxRewardRequest, name = "公会-BOSS-领取击杀宝箱奖励")
    public Result<GuildBossBoxRewardResponse> bossBoxReward(Message msg) {
        GuildBossBoxRewardRequest params = (GuildBossBoxRewardRequest)msg;
//        return guildBossService.bossBoxRewardAction(params);
        return null;
    }

    @ApiMethod(command = MsgReqCommand.GuildBossGetRankListRequest, name = "公会-BOSS-获取排行榜")
    public Result<GuildBossGetRankListResponse> getRankList(Message msg) {
        GuildBossGetRankListRequest params = (GuildBossGetRankListRequest)msg;
        return guildBossService.bossGetRankListAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildBossBattleRequest, name = "公会-BOSS-新战斗")
    public Result<GuildBossBattleResponse> bossBattle(Message msg) {
        GuildBossBattleRequest params = (GuildBossBattleRequest)msg;
        return guildBossService.bossBattle(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildBossBattleGRankRequest, name = "公会-BOSS-获取排行榜")
    public Result<GuildBossBattleGRankResponse> gRankList(Message msg) {
        GuildBossBattleGRankRequest params = (GuildBossBattleGRankRequest)msg;
        return guildBossService.gRankList(params);
    }
}











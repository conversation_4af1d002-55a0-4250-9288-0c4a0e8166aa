package com.dxx.game.modules.activity.model;

import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/17 7:05 下午
 */
@Getter
public class SevenDayTaskProcess {

    private int taskType;
    private long count;
    private Map<Integer, Integer> equipLevel;
    private Map<Integer, Integer> equipQua;

    public static SevenDayTaskProcess valueOf(int taskType, long count) {
        SevenDayTaskProcess process = new SevenDayTaskProcess();
        process.taskType = taskType;
        process.count = count;
        return process;
    }

    public static SevenDayTaskProcess equipLevel(int taskType,  Map<Integer, Integer> equipLevel) {
        SevenDayTaskProcess process = new SevenDayTaskProcess();
        process.taskType = taskType;
        process.count = 0;
        process.equipLevel = equipLevel;
        return process;
    }

    public static SevenDayTaskProcess equipQua(int taskType, Map<Integer, Integer> equipQua) {
        SevenDayTaskProcess process = new SevenDayTaskProcess();
        process.taskType = taskType;
        process.count = 0;
        process.equipQua = equipQua;
        return process;
    }


}

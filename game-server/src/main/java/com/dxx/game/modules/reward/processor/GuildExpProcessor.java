package com.dxx.game.modules.reward.processor;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guild.GuildLevelEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.message.service.GuildMessageService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.GuildExpReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.Expression;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@Slf4j
@Component
public class GuildExpProcessor implements RewardProcessor {

    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserService userService;
    @Resource
    private LogService logService;
    @Resource
    private RedisLock redisLock;
    @Resource
    private GuildMessageService guildMessageService;

    @Override
    public RewardType getType() {
        return RewardType.GUILD_EXP;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.NONE;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        int addCount = reward.getCount();
        if (addCount <= 0) {
            resultCode = ErrorCode.PARAMS_ERROR;
            log.error("add guild exp count error, value : {}", addCount);
        } else {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser == null || guildUser.getGuildId() == 0) {
                resultCode = ErrorCode.GUILD_JOIN_FIRST;
                log.error("user not join guild, userId:{}", userId);
            } else {
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild == null) {
                    resultCode = ErrorCode.GUILD_NOT_EXIST;
                    log.error("guild not exist, guildId:{}", guildUser.getGuildId());
                }
            }
        }
        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }

        GuildExpReward reward = (GuildExpReward) rewardAction.getReward();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        // 防止并发更新, 加个redis锁 默认3秒超时, 超时之后直接更新
        String lockKey = "update_guild_exp_lock_" + guildUser.getGuildId();
        redisLock.lock(lockKey, String.valueOf(userId));
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());


        int addExp = reward.getCount();
        // 判断是否是自动升级的公会
        int isAuto = gameConfigManager.getGuildConfig().getGuildConstEntity(113).getTypeInt();
        if (isAuto == 1) {
            // 自动升级
            Expression updateCondition = Expression.builder().expression("#guildExp = :guildExp and #guildLevel = :guildLevel")
                    .putExpressionName("#guildExp", "guildExp")
                    .putExpressionName("#guildLevel", "guildLevel")
                    .putExpressionValue(":guildExp", DynamoDBConvertUtil.buildAttributeValue(guild.getGuildExp()))
                    .putExpressionValue(":guildLevel", DynamoDBConvertUtil.buildAttributeValue(guild.getGuildLevel())).build();

            int maxLevel = gameConfigManager.getGuildConfig().getGuildLevel().size();
            int fromLevel = guild.getGuildLevel();
            int toLevel = fromLevel;
            int toExp = guild.getGuildExp() + addExp;

            while (true) {
                if (toLevel >= maxLevel) {
                    break;
                }
                GuildLevelEntity guildLevelEntity = gameConfigManager.getGuildConfig().getGuildLevelEntity(toLevel);
                int needExp = guildLevelEntity.getExp();
                if (needExp > toExp) {
                    break;
                }
                toLevel ++;
                toExp -= needExp;
            }

            guild.setGuildLevel(toLevel);
            guild.setGuildExp(toExp);

            if (toLevel > fromLevel) {
                // 更新公会最大人数
                GuildLevelEntity guildLevelEntity = gameConfigManager.getGuildConfig().getGuildLevelEntity(toLevel);
                guild.setGuildMaxMembersCount(guildLevelEntity.getMaxMemberCount());
                guildDao.updateGuildMaxMembersCount(guild);
            }

            guild.addUpdateCondition(updateCondition);
            guildDao.updateGuildExp(guild);
            guildDao.updateGuildLevel(guild);
        } else {
            // 手动升级
            Expression updateGuildExpression = Expression.builder()
                    .expression("#guildExp = #guildExp + :guildExp")
                    .putExpressionName("#guildExp", "guildExp")
                    .putExpressionValue(":guildExp", DynamoDBConvertUtil.buildAttributeValue(reward.getCount()))
                    .build();

            guild.addUpdateExpression(updateGuildExpression);
            guild.setGuildExp(guild.getGuildExp() + addExp);
            
            guildDao.updateGuildExp(guild);
        }

        reward.setExp(guild.getGuildExp());
        reward.setLevel(guild.getGuildLevel());

        JSONObject logObj = new JSONObject();
        logObj.put("guildLevel", guild.getGuildLevel());
        logObj.put("guildExp", guild.getGuildExp());

        logService.sendBasic(userId, RequestContext.getCommand(), userService.getTransId(userId), logObj.toJSONString());

        // 推送公会经验，等级变化
        guildMessageService.pushGuildInfoModify(guild);
        RewardResult<GuildExpReward> result = new RewardResult<>(this.getType());
        result.setActualCount(reward.getCount());
        result.setCurrent(reward);
        return result;
    }
}

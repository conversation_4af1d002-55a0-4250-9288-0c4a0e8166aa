package com.dxx.game.modules.guild.handler;
import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.GuildProto.*;
import com.dxx.game.modules.guild.service.GuildDonationService;
import com.google.protobuf.Message;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/5/25
 * @description:
 */
@ApiHandler
public class GuildDonationHandler {
    @Resource
    private GuildDonationService guildDonationService;

    @ApiMethod(command = MsgReqCommand.GuildDonationReqItemRequest, name = "公会-捐赠-请求道具")
    public Result<GuildDonationReqItemResponse> reqItem(Message msg) {
        GuildDonationReqItemRequest params = (GuildDonationReqItemRequest)msg;
        return guildDonationService.reqItemAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildDonationSendItemRequest, name = "公会-捐赠-赠送道具")
    public Result<GuildDonationSendItemResponse> sendItem(Message msg) {
        GuildDonationSendItemRequest params = (GuildDonationSendItemRequest)msg;
        return guildDonationService.sendItemAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildDonationReceiveRequest, name = "公会-捐赠-领取道具")
    public Result<GuildDonationReceiveResponse> receiveItem(Message msg) {
        GuildDonationReceiveRequest params = (GuildDonationReceiveRequest)msg;
        return guildDonationService.receiveAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildDonationGetRecordsRequest, name = "公会-捐赠-获取记录", skipRedisLock = true)
    public Result<GuildDonationGetRecordsResponse> getRecords(Message msg) {
        GuildDonationGetRecordsRequest params = (GuildDonationGetRecordsRequest)msg;
        return guildDonationService.getRecordsAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildDonationGetOperationRecordsRequest, name = "公会-捐赠-获取受赠记录")
    public Result<GuildDonationGetOperationRecordsResponse> getOperationRecords(Message msg) {
        GuildDonationGetOperationRecordsRequest params = (GuildDonationGetOperationRecordsRequest)msg;
        return guildDonationService.getOperationRecordsAction(params);
    }
}

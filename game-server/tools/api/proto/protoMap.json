{"9001": {"proto": "DevelopLoginRequest", "desc": "开发测试用", "response": "DevelopLoginResponse"}, "9002": {"proto": "DevelopLoginResponse", "desc": "", "response": ""}, "9003": {"proto": "DevelopChangeResourceRequest", "desc": "开发测试修改资源", "response": "DevelopChangeResourceResponse"}, "9004": {"proto": "DevelopChangeResourceResponse", "desc": "", "response": ""}, "9005": {"proto": "DevelopToolsRequest", "desc": "测试-工具", "response": "DevelopToolsResponse"}, "9006": {"proto": "DevelopToolsResponse", "desc": "测试-工具", "response": ""}, "9999": {"proto": "Error<PERSON><PERSON>", "desc": "通用错误返回", "response": ""}, "10101": {"proto": "UserLoginRequest", "desc": "登录请求", "response": "UserLoginResponse"}, "10102": {"proto": "UserLoginResponse", "desc": "登录响应", "response": ""}, "10103": {"proto": "UserGetInfoRequest", "desc": "客户端数据不同步时刷新数据用接口", "response": "UserGetInfoResponse"}, "10104": {"proto": "UserGetInfoResponse", "desc": "", "response": ""}, "10105": {"proto": "UserHeartbeatRequest", "desc": "客户端心跳(每分钟)", "response": "UserHeartbeatResponse"}, "10106": {"proto": "UserHeartbeatResponse", "desc": "", "response": ""}, "10107": {"proto": "UserUpdateSystemMaskRequest", "desc": "更新systemMask", "response": "UserUpdateSystemMaskResponse"}, "10108": {"proto": "UserUpdateSystemMaskResponse", "desc": "", "response": ""}, "10109": {"proto": "UserUpdateGuideMaskRequest", "desc": "更新新手引导步骤guideMask", "response": "UserUpdateGuideMaskResponse"}, "10110": {"proto": "UserUpdateGuideMaskResponse", "desc": "", "response": ""}, "10111": {"proto": "UserCancelAccountRequest", "desc": "注销账号", "response": "UserCancelAccountResponse"}, "10112": {"proto": "UserCancelAccountResponse", "desc": "", "response": ""}, "10113": {"proto": "UserUpdateInfoRequest", "desc": "更新昵称,头像,头像框", "response": "UserUpdateInfoResponse"}, "10114": {"proto": "UserUpdateInfoResponse", "desc": "", "response": ""}, "10115": {"proto": "UserGetOtherPlayerInfoRequest", "desc": "获取其他玩家信息", "response": "UserGetOtherPlayerInfoResponse"}, "10116": {"proto": "UserGetOtherPlayerInfoResponse", "desc": "", "response": ""}, "10117": {"proto": "UserGetBattleReportRequest", "desc": "获得战报", "response": "UserGetBattleReportResponse"}, "10118": {"proto": "UserGetBattleReportResponse", "desc": "", "response": ""}, "10119": {"proto": "UserOpenModelRequest", "desc": "记录开启模块", "response": "UserOpenModelResponse"}, "10120": {"proto": "UserOpenModelResponse", "desc": "", "response": ""}, "10121": {"proto": "UserSetFormationByTypeRequest", "desc": "设置阵容", "response": "UserSetFormationByTypeResponse"}, "10122": {"proto": "UserSetFormationByTypeResponse", "desc": "", "response": ""}, "10123": {"proto": "UserGetFormationByTypeRequest", "desc": "获得阵容", "response": "UserGetFormationByTypeResponse"}, "10124": {"proto": "UserGetFormationByTypeResponse", "desc": "", "response": ""}, "10141": {"proto": "UserGetLastLoginRequest", "desc": "请求角色列表", "response": "UserGetLastLoginResponse"}, "10142": {"proto": "UserGetLastLoginResponse", "desc": "响应", "response": ""}, "10143": {"proto": "FindServerListRequest", "desc": "请求服务器列表", "response": "FindServerListResponse"}, "10144": {"proto": "FindServerListResponse", "desc": "响应", "response": ""}, "10201": {"proto": "ItemUseRequest", "desc": "道具-使用", "response": "ItemUseResponse"}, "10202": {"proto": "ItemUseResponse", "desc": "", "response": ""}, "10301": {"proto": "MissionGetInfoRequest", "desc": "关卡-获取数据", "response": "MissionGetInfoResponse"}, "10302": {"proto": "MissionGetInfoResponse", "desc": "", "response": ""}, "10303": {"proto": "MissionStartRequest", "desc": "关卡-开始战斗", "response": "MissionStartResponse"}, "10304": {"proto": "MissionStartResponse", "desc": "", "response": ""}, "10305": {"proto": "MissionEndRequest", "desc": "关卡-结算", "response": "MissionEndResponse"}, "10306": {"proto": "MissionEndResponse", "desc": "", "response": ""}, "10307": {"proto": "MissionGetHangUpItemsRequest", "desc": "关卡-获取挂机奖励道具", "response": "MissionGetHangUpItemsResponse"}, "10308": {"proto": "MissionGetHangUpItemsResponse", "desc": "", "response": ""}, "10309": {"proto": "MissionReceiveHangUpItemsRequest", "desc": "关卡-领取挂机奖励", "response": "MissionReceiveHangUpItemsResponse"}, "10310": {"proto": "MissionReceiveHangUpItemsResponse", "desc": "", "response": ""}, "10311": {"proto": "MissionQuickHangUpRequest", "desc": "关卡-快速挂机请求", "response": "MissionQuickHangUpResponse"}, "10312": {"proto": "MissionQuickHangUpResponse", "desc": "", "response": ""}, "10401": {"proto": "PayInAppPurchaseRequest", "desc": "内购请求", "response": "PayInAppPurchaseResponse"}, "10402": {"proto": "PayInAppPurchaseResponse", "desc": "内购响应", "response": ""}, "10403": {"proto": "PayPreOrderRequest", "desc": "内购-预下单", "response": "PayPreOrderResponse"}, "10404": {"proto": "PayPreOrderResponse", "desc": "", "response": ""}, "10405": {"proto": "PayOnUnityRequest", "desc": "内购-unity内测试", "response": "PayOnUnityResponse"}, "10406": {"proto": "PayOnUnityResponse", "desc": "", "response": ""}, "10501": {"proto": "TaskGetInfoRequest", "desc": "任务-获取数据", "response": "TaskGetInfoResponse"}, "10502": {"proto": "TaskGetInfoResponse", "desc": "", "response": ""}, "10503": {"proto": "TaskRewardDailyRequest", "desc": "任务-每日领取奖励", "response": "TaskRewardDailyResponse"}, "10504": {"proto": "TaskRewardDailyResponse", "desc": "", "response": ""}, "10505": {"proto": "TaskRewardAchieveRequest", "desc": "成就-领取奖励", "response": "TaskRewardAchieveResponse"}, "10506": {"proto": "TaskRewardAchieveResponse", "desc": "", "response": ""}, "10507": {"proto": "TaskActiveRewardRequest", "desc": "任务-领取活跃度奖励", "response": "TaskActiveRewardResponse"}, "10508": {"proto": "TaskActiveRewardResponse", "desc": "", "response": ""}, "10509": {"proto": "TaskActiveRewardAllRequest", "desc": "任务-领取全活跃度奖励", "response": "TaskActiveRewardAllResponse"}, "10510": {"proto": "TaskActiveRewardAllResponse", "desc": "", "response": ""}, "10601": {"proto": "ShopGetInfoRequest", "desc": "商店-请求数据", "response": "ShopGetInfoResponse"}, "10602": {"proto": "ShopGetInfoResponse", "desc": "", "response": ""}, "10603": {"proto": "ShopDoGachaRequest", "desc": "商店-抽卡", "response": "ShopDoGachaResponse"}, "10604": {"proto": "ShopDoGachaResponse", "desc": "", "response": ""}, "10605": {"proto": "ShopIntegralGetInfoRequest", "desc": "积分商店-获取积分商店数据", "response": "ShopIntegralGetInfoResponse"}, "10606": {"proto": "ShopIntegralGetInfoResponse", "desc": "", "response": ""}, "10607": {"proto": "ShopIntegralRefreshRequest", "desc": "积分商店-刷新道具", "response": "ShopIntegralRefreshResponse"}, "10608": {"proto": "ShopIntegralRefreshResponse", "desc": "", "response": ""}, "10609": {"proto": "ShopIntegralBuyItemRequest", "desc": "积分商店-购买道具", "response": "ShopIntegralBuyItemResponse"}, "10610": {"proto": "ShopIntegralBuyItemResponse", "desc": "", "response": ""}, "10611": {"proto": "ShopGacheWishRequest", "desc": "商店-抽卡-保存许愿列表", "response": "ShopGacheWishResponse"}, "10612": {"proto": "ShopGacheWishResponse", "desc": "", "response": ""}, "10613": {"proto": "ShopFreeIAPItemRequest", "desc": "商店-免费", "response": "ShopFreeIAPItemResponse"}, "10614": {"proto": "ShopFreeIAPItemResponse", "desc": "", "response": ""}, "10631": {"proto": "BattlePassGetInfoRequest", "desc": "battlepass-获取数据(打开面板请求)", "response": "BattlePassGetInfoResponse"}, "10632": {"proto": "BattlePassGetInfoResponse", "desc": "", "response": ""}, "10633": {"proto": "BattlePassRewardRequest", "desc": "battlepass-领取通行证奖励", "response": "BattlePassRewardResponse"}, "10634": {"proto": "BattlePassRewardResponse", "desc": "", "response": ""}, "10635": {"proto": "BattlePassChangeScoreRequest", "desc": "battlepass-兑换通行证积分", "response": "BattlePassChangeScoreResponse"}, "10636": {"proto": "BattlePassChangeScoreResponse", "desc": "", "response": ""}, "10637": {"proto": "BattlePassFinalRewardRequest", "desc": "battlepass-通行证领取最终奖励", "response": "BattlePassFinalRewardResponse"}, "10638": {"proto": "BattlePassFinalRewardResponse", "desc": "", "response": ""}, "10651": {"proto": "MonthCardGetRewardRequest", "desc": "月卡-领取月卡奖励", "response": "MonthCardGetRewardResponse"}, "10652": {"proto": "MonthCardGetRewardResponse", "desc": "", "response": ""}, "10661": {"proto": "VIPLevelRewardRequest", "desc": "vip-领取vip等级宝箱", "response": "VIPLevelRewardResponse"}, "10662": {"proto": "VIPLevelRewardResponse", "desc": "", "response": ""}, "10671": {"proto": "LevelFundGetInfoRequest", "desc": "基金-获取数据(打开面板请求)", "response": "LevelFundGetInfoResponse"}, "10672": {"proto": "LevelFundGetInfoResponse", "desc": "", "response": ""}, "10673": {"proto": "LevelFundRewardRequest", "desc": "基金-领取基金奖励", "response": "LevelFundRewardResponse"}, "10674": {"proto": "LevelFundRewardResponse", "desc": "", "response": ""}, "10675": {"proto": "FirstRechargeRewardRequest", "desc": "首充-领取首充奖励", "response": "FirstRechargeRewardResponse"}, "10676": {"proto": "FirstRechargeRewardResponse", "desc": "", "response": ""}, "10701": {"proto": "HeroUpgradeRequest", "desc": "英雄-升级", "response": "HeroUpgradeResponse"}, "10702": {"proto": "HeroUpgradeResponse", "desc": "", "response": ""}, "10703": {"proto": "HeroAdvanceRequest", "desc": "英雄-升阶", "response": "HeroAdvanceResponse"}, "10704": {"proto": "HeroAdvanceResponse", "desc": "", "response": ""}, "10705": {"proto": "HeroStarRequest", "desc": "英雄-升星", "response": "HeroStarResponse"}, "10706": {"proto": "HeroStarResponse", "desc": "", "response": ""}, "10707": {"proto": "HeroResetRequest", "desc": "英雄-重生", "response": "HeroResetResponse"}, "10708": {"proto": "HeroResetResponse", "desc": "", "response": ""}, "10709": {"proto": "HeroBookScoreRequest", "desc": "英雄-图鉴积分", "response": "HeroBookScoreResponse"}, "10710": {"proto": "HeroBookScoreResponse", "desc": "", "response": ""}, "10711": {"proto": "HeroBookRewardRequest", "desc": "英雄-图鉴领奖", "response": "HeroBookRewardResponse"}, "10712": {"proto": "HeroBookRewardResponse", "desc": "", "response": ""}, "10713": {"proto": "HeroReplaceSkinRequest", "desc": "英雄-替换皮肤", "response": "HeroReplaceSkinResponse"}, "10714": {"proto": "HeroReplaceSkinResponse", "desc": "", "response": ""}, "10715": {"proto": "HeroBondLevelUpRequest", "desc": "英雄-羁绊升级", "response": "HeroBondLevelUpResponse"}, "10716": {"proto": "HeroBondLevelUpResponse", "desc": "", "response": ""}, "10717": {"proto": "HeroLosslessRequest", "desc": "英雄-无损换将", "response": "HeroLosslessResponse"}, "10718": {"proto": "HeroLosslessResponse", "desc": "", "response": ""}, "10801": {"proto": "EquipStrengthRequest", "desc": "装备-强化请求", "response": "EquipStrengthResponse"}, "10802": {"proto": "EquipStrengthResponse", "desc": "", "response": ""}, "10803": {"proto": "EquipComposeRequest", "desc": "装备-合成", "response": "EquipComposeResponse"}, "10804": {"proto": "EquipComposeResponse", "desc": "", "response": ""}, "10805": {"proto": "EquipUpgradeRequest", "desc": "装备-升级", "response": "EquipUpgradeResponse"}, "10806": {"proto": "EquipUpgradeResponse", "desc": "", "response": ""}, "10807": {"proto": "EquipDressRequest", "desc": "装备-穿戴", "response": "EquipDressResponse"}, "10808": {"proto": "EquipDressResponse", "desc": "", "response": ""}, "10809": {"proto": "EquipLevelResetRequest", "desc": "装备-等级重置", "response": "EquipLevelResetResponse"}, "10810": {"proto": "EquipLevelResetResponse", "desc": "", "response": ""}, "10811": {"proto": "EquipQualityDownRequest", "desc": "装备-降品", "response": "EquipQualityDownResponse"}, "10812": {"proto": "EquipQualityDownResponse", "desc": "", "response": ""}, "10813": {"proto": "EquipOffRequest", "desc": "装备-脱下", "response": "EquipOffResponse"}, "10814": {"proto": "EquipOffResponse", "desc": "", "response": ""}, "10815": {"proto": "EquipReplaceRequest", "desc": "装备-替换", "response": "EquipReplaceResponse"}, "10816": {"proto": "EquipReplaceResponse", "desc": "", "response": ""}, "10901": {"proto": "ActivityGetListRequest", "desc": "活动-获取所有活动数据(登录时调用,", "response": "ActivityGetListResponse"}, "10902": {"proto": "ActivityGetListResponse", "desc": "", "response": ""}, "10903": {"proto": "ActivityGetTaskRequest", "desc": "活动-根据活动id获取任务数据", "response": "ActivityGetTaskResponse"}, "10904": {"proto": "ActivityGetTaskResponse", "desc": "", "response": ""}, "10905": {"proto": "ActivityTaskRewardRequest", "desc": "活动-活动任务领奖", "response": "ActivityTaskRewardResponse"}, "10906": {"proto": "ActivityTaskRewardResponse", "desc": "", "response": ""}, "10907": {"proto": "ActivityGetShopRequest", "desc": "活动-根据活动id获取商店数据", "response": "ActivityGetShopResponse"}, "10908": {"proto": "ActivityGetShopResponse", "desc": "", "response": ""}, "10909": {"proto": "ActivityShopExchangeRequest", "desc": "活动-兑换", "response": "ActivityShopExchangeResponse"}, "10910": {"proto": "ActivityShopExchangeResponse", "desc": "", "response": ""}, "10911": {"proto": "ActivityGetRankRequest", "desc": "活动-排行榜", "response": "ActivityGetRankResponse"}, "10912": {"proto": "ActivityGetRankResponse", "desc": "", "response": ""}, "11101": {"proto": "SignInGetInfoRequest", "desc": "签到-获取数据", "response": "SignInGetInfoResponse"}, "11102": {"proto": "SignInGetInfoResponse", "desc": "", "response": ""}, "11103": {"proto": "SignInDoSignRequest", "desc": "签到-领取签到奖励", "response": "SignInDoSignResponse"}, "11104": {"proto": "SignInDoSignResponse", "desc": "", "response": ""}, "11301": {"proto": "SevenDayTaskGetInfoRequest", "desc": "新手7日任务活动-获取数据", "response": "SevenDayTaskGetInfoResponse"}, "11302": {"proto": "SevenDayTaskGetInfoResponse", "desc": "", "response": ""}, "11303": {"proto": "SevenDayTaskRewardRequest", "desc": "新手7日任务活动-领取任务奖励", "response": "SevenDayTaskRewardResponse"}, "11304": {"proto": "SevenDayTaskRewardResponse", "desc": "", "response": ""}, "11305": {"proto": "SevenDayTaskActiveRewardRequest", "desc": "新手7日任务活动-领取活跃度奖励", "response": "SevenDayTaskActiveRewardResponse"}, "11306": {"proto": "SevenDayTaskActiveRewardResponse", "desc": "", "response": ""}, "11401": {"proto": "FishingOnOpenRequest", "desc": "钓鱼活动-打开界面调用", "response": "FishingOnOpenResponse"}, "11402": {"proto": "FishingOnOpenResponse", "desc": "", "response": ""}, "11403": {"proto": "FishingCastRodRequest", "desc": "钓鱼活动-抛竿", "response": "FishingCastRodResponse"}, "11404": {"proto": "FishingCastRodResponse", "desc": "", "response": ""}, "11405": {"proto": "FishingReelInRequest", "desc": "钓鱼活动-收竿", "response": "FishingReelInResponse"}, "11406": {"proto": "FishingReelInResponse", "desc": "", "response": ""}, "11407": {"proto": "FishingBuyBaitRequest", "desc": "钓鱼活动-购买鱼饵", "response": "FishingBuyBaitResponse"}, "11408": {"proto": "FishingBuyBaitResponse", "desc": "", "response": ""}, "11409": {"proto": "FishingRebornRequest", "desc": "钓鱼活动-复活", "response": "FishingRebornResponse"}, "11410": {"proto": "FishingRebornResponse", "desc": "", "response": ""}, "11501": {"proto": "FlipOnOpenRequest", "desc": "翻牌子活动-打开界面调用", "response": "FlipOnOpenResponse"}, "11502": {"proto": "FlipOnOpenResponse", "desc": "", "response": ""}, "11503": {"proto": "FlipAccRewardRequest", "desc": "翻牌子活动-领取累计线索奖励", "response": "FlipAccRewardResponse"}, "11504": {"proto": "FlipAccRewardResponse", "desc": "", "response": ""}, "11505": {"proto": "FlipBuyStepRequest", "desc": "翻牌子活动-购买步数", "response": "FlipBuyStepResponse"}, "11506": {"proto": "FlipBuyStepResponse", "desc": "", "response": ""}, "11507": {"proto": "FlipShowGridRequest", "desc": "翻牌子活动-展示格子", "response": "FlipShowGridResponse"}, "11508": {"proto": "FlipShowGridResponse", "desc": "", "response": ""}, "11509": {"proto": "FlipRewardGridRequest", "desc": "翻牌子活动-领奖格子", "response": "FlipRewardGridResponse"}, "11510": {"proto": "FlipRewardGridResponse", "desc": "", "response": ""}, "11511": {"proto": "FlipClueGridRequest", "desc": "翻牌子活动-线索格子", "response": "FlipClueGridResponse"}, "11512": {"proto": "FlipClueGridResponse", "desc": "", "response": ""}, "11513": {"proto": "FlipBombGridRequest", "desc": "翻牌子活动-炸弹格子", "response": "FlipBombGridResponse"}, "11514": {"proto": "FlipBombGridResponse", "desc": "", "response": ""}, "11515": {"proto": "FlipMapFindSpecialRequest", "desc": "翻牌子活动-当前地图是否有特殊奖励未领取", "response": "FlipMapFindSpecialResponse"}, "11516": {"proto": "FlipMapFindSpecialResponse", "desc": "", "response": ""}, "11517": {"proto": "FlipAllAccRewardRequest", "desc": "翻牌子活动-一键领取累计线索奖励", "response": "FlipAllAccRewardResponse"}, "11518": {"proto": "FlipAllAccRewardResponse", "desc": "", "response": ""}, "11601": {"proto": "DiveOnOpenRequest", "desc": "潜水活动-打开界面调用", "response": "DiveOnOpenResponse"}, "11602": {"proto": "DiveOnOpenResponse", "desc": "", "response": ""}, "11603": {"proto": "DiveBuyItemRequest", "desc": "潜水活动-购买潜水材料", "response": "DiveBuyItemResponse"}, "11604": {"proto": "DiveBuyItemResponse", "desc": "", "response": ""}, "11605": {"proto": "DiveAccRewardRequest", "desc": "潜水活动-领取累计奖励", "response": "DiveAccRewardResponse"}, "11606": {"proto": "DiveAccRewardResponse", "desc": "", "response": ""}, "11607": {"proto": "DiveShineRequest", "desc": "潜水活动-使用电光水母", "response": "DiveShineResponse"}, "11608": {"proto": "DiveShineResponse", "desc": "", "response": ""}, "11609": {"proto": "DiveUsePropRequest", "desc": "潜水活动-使用手电筒", "response": "DiveUsePropResponse"}, "11610": {"proto": "DiveUsePropResponse", "desc": "", "response": ""}, "11611": {"proto": "DiveAllAccRewardRequest", "desc": "潜水活动-一键领取累计奖励", "response": "DiveAllAccRewardResponse"}, "11612": {"proto": "DiveAllAccRewardResponse", "desc": "", "response": ""}, "11701": {"proto": "PowerOnOpenRequest", "desc": "战力活动-打开界面调用", "response": "PowerOnOpenResponse"}, "11702": {"proto": "PowerOnOpenResponse", "desc": "", "response": ""}, "11703": {"proto": "PowerRewardRequest", "desc": "战力活动-领奖", "response": "PowerRewardResponse"}, "11704": {"proto": "PowerRewardResponse", "desc": "", "response": ""}, "30101": {"proto": "GuildGetInfoRequest", "desc": "公会-获取数据", "response": "GuildGetInfoResponse"}, "30102": {"proto": "GuildGetInfoResponse", "desc": "", "response": ""}, "30103": {"proto": "GuildCreateRequest", "desc": "公会-创建公会", "response": "GuildCreateResponse"}, "30104": {"proto": "GuildCreateResponse", "desc": "", "response": ""}, "30105": {"proto": "GuildSearchRequest", "desc": "公会-搜索", "response": "GuildSearchResponse"}, "30106": {"proto": "GuildSearchResponse", "desc": "", "response": ""}, "30107": {"proto": "GuildGetDetailRequest", "desc": "公会-查看详细信息", "response": "GuildGetDetailResponse"}, "30108": {"proto": "GuildGetDetailResponse", "desc": "", "response": ""}, "30109": {"proto": "GuildGetMemberListRequest", "desc": "公会-获取成员列表", "response": "GuildGetMemberListResponse"}, "30110": {"proto": "GuildGetMemberListResponse", "desc": "", "response": ""}, "30111": {"proto": "GuildModifyRequest", "desc": "公会-修改信息", "response": "GuildModifyResponse"}, "30112": {"proto": "GuildModifyResponse", "desc": "", "response": ""}, "30113": {"proto": "GuildDismissRequest", "desc": "公会-解散", "response": "GuildDismissResponse"}, "30114": {"proto": "GuildDismissResponse", "desc": "", "response": ""}, "30115": {"proto": "GuildApplyJoinRequest", "desc": "公会-申请加入", "response": "GuildApplyJoinResponse"}, "30116": {"proto": "GuildApplyJoinResponse", "desc": "", "response": ""}, "30117": {"proto": "GuildCancelApplyRequest", "desc": "公会-取消申请", "response": "GuildCancelApplyResponse"}, "30118": {"proto": "GuildCancelApplyResponse", "desc": "", "response": ""}, "30119": {"proto": "GuildAutoJoinRequest", "desc": "公会-自动加入", "response": "GuildAutoJoinResponse"}, "30120": {"proto": "GuildAutoJoinResponse", "desc": "", "response": ""}, "30121": {"proto": "GuildGetApplyListRequest", "desc": "公会-获取申请列表", "response": "GuildGetApplyListResponse"}, "30122": {"proto": "GuildGetApplyListResponse", "desc": "", "response": ""}, "30123": {"proto": "GuildAgreeJoinRequest", "desc": "公会-同意加入", "response": "GuildAgreeJoinResponse"}, "30124": {"proto": "GuildAgreeJoinResponse", "desc": "", "response": ""}, "30125": {"proto": "GuildRefuseJoinRequest", "desc": "公会-拒绝加入", "response": "GuildRefuseJoinResponse"}, "30126": {"proto": "GuildRefuseJoinResponse", "desc": "", "response": ""}, "30127": {"proto": "GuildKickOutRequest", "desc": "公会-踢人", "response": "GuildKickOutResponse"}, "30128": {"proto": "GuildKickOutResponse", "desc": "", "response": ""}, "30129": {"proto": "GuildLeaveRequest", "desc": "公会-离开", "response": "GuildLeaveResponse"}, "30130": {"proto": "GuildLeaveResponse", "desc": "", "response": ""}, "30131": {"proto": "GuildUpPositionRequest", "desc": "公会-修改成员职位", "response": "GuildUpPositionResponse"}, "30132": {"proto": "GuildUpPositionResponse", "desc": "", "response": ""}, "30133": {"proto": "GuildTransferPresidentRequest", "desc": "公会-转让会长", "response": "GuildTransferPresidentResponse"}, "30134": {"proto": "GuildTransferPresidentResponse", "desc": "", "response": ""}, "30135": {"proto": "GuildGetFeaturesInfoRequest", "desc": "公会-获取公会功能信息", "response": "GuildGetFeaturesInfoResponse"}, "30136": {"proto": "GuildGetFeaturesInfoResponse", "desc": "", "response": ""}, "30137": {"proto": "GuildLevelUpRequest", "desc": "公会-升级", "response": "GuildLevelUpResponse"}, "30138": {"proto": "GuildLevelUpResponse", "desc": "", "response": ""}, "30141": {"proto": "GuildSignInRequest", "desc": "公会-签到", "response": "GuildSignInResponse"}, "30142": {"proto": "GuildSignInResponse", "desc": "", "response": ""}, "30151": {"proto": "GuildShopBuyRequest", "desc": "公会-商店-购买", "response": "GuildShopBuyResponse"}, "30152": {"proto": "GuildShopBuyResponse", "desc": "", "response": ""}, "30153": {"proto": "GuildShopRefreshRequest", "desc": "公会-商店-刷新", "response": "GuildShopRefreshResponse"}, "30154": {"proto": "GuildShopRefreshResponse", "desc": "", "response": ""}, "30161": {"proto": "GuildTaskRewardRequest", "desc": "公会-任务-领取奖励", "response": "GuildTaskRewardResponse"}, "30162": {"proto": "GuildTaskRewardResponse", "desc": "", "response": ""}, "30163": {"proto": "GuildTaskRefreshRequest", "desc": "公会-任务-刷新", "response": "GuildTaskRefreshResponse"}, "30164": {"proto": "GuildTaskRefreshResponse", "desc": "", "response": ""}, "30171": {"proto": "GuildGetMessageRecordsRequest", "desc": "公会-获取消息记录", "response": "GuildGetMessageRecordsResponse"}, "30172": {"proto": "GuildGetMessageRecordsResponse", "desc": "", "response": ""}, "30201": {"proto": "GuildDonationReqItemRequest", "desc": "公会-捐赠-请求道具", "response": "GuildDonationReqItemResponse"}, "30202": {"proto": "GuildDonationReqItemResponse", "desc": "", "response": ""}, "30203": {"proto": "GuildDonationSendItemRequest", "desc": "公会-捐赠-赠送道具", "response": "GuildDonationSendItemResponse"}, "30204": {"proto": "GuildDonationSendItemResponse", "desc": "", "response": ""}, "30205": {"proto": "GuildDonationReceiveRequest", "desc": "公会-捐赠-领取道具", "response": "GuildDonationReceiveResponse"}, "30206": {"proto": "GuildDonationReceiveResponse", "desc": "", "response": ""}, "30207": {"proto": "GuildDonationGetRecordsRequest", "desc": "公会-捐赠-获取请求道具记录", "response": "GuildDonationGetRecordsResponse"}, "30208": {"proto": "GuildDonationGetRecordsResponse", "desc": "", "response": ""}, "30209": {"proto": "GuildDonationGetOperationRecordsRequest", "desc": "公会-捐赠-获取受赠记录", "response": "GuildDonationGetOperationRecordsResponse"}, "30210": {"proto": "GuildDonationGetOperationRecordsResponse", "desc": "", "response": ""}, "30211": {"proto": "GuildBossBattleRequest", "desc": "公会-BOSS战", "response": "GuildBossBattleResponse"}, "30212": {"proto": "GuildBossBattleResponse", "desc": "", "response": ""}, "30213": {"proto": "GuildBossBattleGRankRequest", "desc": "公会-BOSS战-公会排行榜", "response": "GuildBossBattleGRankResponse"}, "30214": {"proto": "GuildBossBattleGRankResponse", "desc": "", "response": ""}, "30501": {"proto": "GuildTechUpgradeRequest", "desc": "公会-科技-升级", "response": "GuildTechUpgradeResponse"}, "30502": {"proto": "GuildTechUpgradeResponse", "desc": "", "response": ""}, "31101": {"proto": "IMLoginRequest", "desc": "IM-登录", "response": "IMLoginResponse"}, "31102": {"proto": "IMLoginResponse", "desc": "", "response": ""}, "31103": {"proto": "IMJoinGroupRequest", "desc": "IM-加入分组", "response": "IMJoinGroupResponse"}, "31104": {"proto": "IMJoinGroupResponse", "desc": "", "response": ""}, "31105": {"proto": "IMQuitGroupRequest", "desc": "IM-退出分组", "response": "IMQuitGroupResponse"}, "31106": {"proto": "IMQuitGroupResponse", "desc": "", "response": ""}, "31107": {"proto": "IMHeartBeatRequest", "desc": "IM-心跳(每5秒请求一次)", "response": "IMHeartBeatResponse"}, "31108": {"proto": "IMHeartBeatResponse", "desc": "", "response": ""}, "31109": {"proto": "IMGroupChatRequest", "desc": "IM-聊天", "response": "IMGroupChatResponse"}, "31110": {"proto": "IMGroupChatResponse", "desc": "", "response": ""}, "31111": {"proto": "IMPrivateChatRequest", "desc": "IM-私聊", "response": "IMPrivateChatResponse"}, "31112": {"proto": "IMPrivateChatResponse", "desc": "", "response": ""}, "31113": {"proto": "IMGroupMessageRecordRequest", "desc": "IM-获取分组消息记录(聊天记录)", "response": "IMGroupMessageRecordResponse"}, "31114": {"proto": "IMGroupMessageRecordResponse", "desc": "", "response": ""}, "31115": {"proto": "IMPrivateListRequest", "desc": "IM-获取私聊列表", "response": "IMPrivateListResponse"}, "31116": {"proto": "IMPrivateListResponse", "desc": "", "response": ""}, "31117": {"proto": "IMPrivateChatRecordRequest", "desc": "IM-获取私聊记录", "response": "IMPrivateChatRecordResponse"}, "31118": {"proto": "IMPrivateChatRecordResponse", "desc": "", "response": ""}, "31119": {"proto": "IMGetBlackListRequest", "desc": "IM-黑名单-获取列表", "response": "IMGetBlackListResponse"}, "31120": {"proto": "IMGetBlackListResponse", "desc": "", "response": ""}, "31121": {"proto": "IMAddToBlackListRequest", "desc": "IM-黑名单-添加", "response": "IMAddToBlackListResponse"}, "31122": {"proto": "IMAddToBlackListResponse", "desc": "", "response": ""}, "31123": {"proto": "IMRemoveFromBlackListRequest", "desc": "IM-黑名单-移除", "response": "IMRemoveFromBlackListResponse"}, "31124": {"proto": "IMRemoveFromBlackListResponse", "desc": "", "response": ""}, "31125": {"proto": "IMChatTextTranslateRequest", "desc": "IM-聊天-翻译", "response": "IMChatTextTranslateResponse"}, "31126": {"proto": "IMChatTextTranslateResponse", "desc": "", "response": ""}, "31202": {"proto": "IMLoginRepeatMessage", "desc": "IM-重复登录", "response": ""}, "31204": {"proto": "IMReconnectMessage", "desc": "IM-告知客户端断开链接重新登录", "response": ""}, "31206": {"proto": "IMPushMessage", "desc": "IM-推送消息", "response": ""}, "31299": {"proto": "IMErrorMessage", "desc": "通用错误消息", "response": ""}, "32105": {"proto": "ChatShowItemRequest", "desc": "聊天-展示物品", "response": "ChatShowItemResponse"}, "32106": {"proto": "ChatShowItemResponse", "desc": "", "response": ""}}
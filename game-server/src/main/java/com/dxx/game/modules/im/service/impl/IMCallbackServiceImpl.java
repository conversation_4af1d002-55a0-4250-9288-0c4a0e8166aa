package com.dxx.game.modules.im.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.model.UserToken;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.IMProto;
import com.dxx.game.modules.chat.service.ChatService;
import com.dxx.game.modules.common.service.SensitiveWordsService;
import com.dxx.game.modules.im.service.IMService;
import com.dxx.game.modules.message.service.MessageService;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.user.model.UserInfoModel;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.im.IMGroupIdGenerator;
import com.dxx.game.modules.im.consts.GameApiCommand;
import com.dxx.game.modules.im.dto.*;
import com.dxx.game.modules.im.service.IMCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * @author: lsc
 * @createDate: 2025/3/20
 * @description:
 */
@Slf4j
@Service
public class IMCallbackServiceImpl implements IMCallbackService {

    @Resource
    private UserService userService;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private SensitiveWordsService sensitiveWordsService;
    @Resource
    private ChatService chatService;
    @Resource
    private MessageService messageService;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private UserDao userDao;
    @Resource
    private IMService imService;
    @Resource
    private ServerListService serverListService;

    @DynamoDBTransactional
    @Override
    public CallbackResponse callback(JSONObject params) {
        // 参数校验
        if (params == null || params.isEmpty()) {
            return null;
        }

        // 将参数转换为 CallbackRequest 对象
        CallbackRequest callbackRequest = params.toJavaObject(CallbackRequest.class);
        String command = callbackRequest.getCommand();

        // 校验命令是否为空
        if (StringUtils.isEmpty(command)) {
            return null;
        }

        // 根据命令类型处理不同逻辑
        switch (command) {
            case GameApiCommand.USER_LOGIN:
                return this.callbackLogin(params);
            case GameApiCommand.BEFORE_JOIN_GROUP:
                return this.callbackBeforeJoin(params);
            case GameApiCommand.BEFORE_GROUP_CHAT:
            case GameApiCommand.BEFORE_PRIVATE_CHAT:
                return this.callbackBeforeChat(params);
            case GameApiCommand.USER_ACTIVE_TIME:
                return this.getUserActiveTime(params);
            case GameApiCommand.USER_INFO:
                return this.getUserInfo(params);
            default:
                // 如果命令未匹配，返回 null 或者可以考虑返回一个默认响应
                return null;
        }
    }

    private UserLoginResponse callbackLogin(JSONObject params) {
        UserLoginRequest userLoginRequest = params.toJavaObject(UserLoginRequest.class);
        String accessToken = userLoginRequest.getAccessToken();
        UserToken userToken = userService.parseAccessToken(accessToken);
        if (userToken == null) {
            return UserLoginResponse.builder()
                    .code(ErrorCode.FAIL)
                    .message("accessToken is empty")
                    .build();
        }

        long userId = userToken.getUserId();
        int serverId = userToken.getServerId();
        String serverIMGroupId = imService.getUserServerGroupId(serverId);
        String guildIMGroupId = imService.getUserGuildGroupId(userId);
        String crossServerIMGroupId = imService.getUserCrossServerGroupId(serverId);
        String globalIMGroupId = imService.getUserGlobalGroupId(serverId);

        return UserLoginResponse.builder()
                .code(ErrorCode.SUCCESS)
                .userId(userToken.getUserId())
                .loginDeviceId(userToken.getLoginDeviceId())
                .serverIMGroupId(serverIMGroupId)
                .guildIMGroupId(guildIMGroupId)
                .crossServerIMGroupId(crossServerIMGroupId)
                .globalIMGroupId(globalIMGroupId)
                .build();
    }


    private BeforeChatResponse callbackBeforeChat(JSONObject params) {
        BeforeChatRequest beforeChatRequest = params.toJavaObject(BeforeChatRequest.class);

        MsgContent msgContent = beforeChatRequest.getMsgContent();
        if (msgContent == null) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.FAIL)
                    .message("msgContent is null")
                    .build();
        }

        String content = msgContent.getContent();
        if (StringUtils.isEmpty(content)) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.FAIL)
                    .message("content is null")
                    .build();
        }

        String senderUserId = beforeChatRequest.getSenderUserId();
        if (StringUtils.isEmpty(senderUserId)) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.FAIL)
                    .message("senderUserId is null")
                    .build();
        }

        long userId = Long.parseLong(senderUserId);
        User user = userService.getUser(userId);
        if (user.getChatBannedTM() != null && user.getChatBannedTM() > DateUtils.getUnixTime()) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.IM_CHAT_BANNED)
                    .message("chat banned")
                    .build();
        }

        // 默认5级解锁聊天
        if (user.getLevel() < 5) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.IM_CHAT_NOT_OPEN)
                    .message("level too low")
                    .build();
        }

        int maxLength = gameConfigManager.getGuildConfig().getGuildConstEntity(115).getTypeInt();
        int contentLength = content.getBytes(StandardCharsets.UTF_8).length;
        if (contentLength > maxLength) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.IM_CHAT_CONTENT_LENGTH_ERROR)
                    .message("content length error")
                    .build();
        }

        content = sensitiveWordsService.replaceWords(content);
        if (StringUtils.isEmpty(content)) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.CHAT_CONTENT_CHECK_ERROR)
                    .message("content check error")
                    .build();
        }
        // 验证聊天频率
        if (chatService.isChatFrequencyTooFast(userId)) {
            return BeforeChatResponse.builder()
                    .code(ErrorCode.IM_CHAT_FREQUENCY_TOO_FAST)
                    .message("chat frequency too fast")
                    .build();
        }

        // 保存下次可聊天频率
        chatService.saveChatFrequency(userId);

        String language = null;
        String customData = msgContent.getCustomData();
        JSONObject customDataJson = null;
        if (!StringUtils.isEmpty(customData)) {
            customDataJson = JSONObject.parseObject(customData);
            language = customDataJson.getString("language");
        }

        String groupId = beforeChatRequest.getGroupId();
        long msgId = 0;
        int messageType = 0;

        JSONObject jsonObject = new JSONObject(true);
        putUserInfo(jsonObject, user);

        // 群组聊天
        if (!StringUtils.isEmpty(groupId)) {
            var groupType = IMGroupIdGenerator.getGroupType(groupId);
            if (groupType == null) {
                return BeforeChatResponse.builder()
                        .code(ErrorCode.PARAMS_ERROR)
                        .message("unknown groupType")
                        .build();
            }

            msgId = imService.createMsgId(groupId);

            if (groupType == IMGroupIdGenerator.GroupType.GUILD) {
                // 是在公会发的消息
                GuildUser guildUser = guildUserDao.getByUserId(userId);
                if (guildUser == null || guildUser.getGuildId() == 0) {
                    return BeforeChatResponse.builder()
                            .code(ErrorCode.IM_CHAT_GUILD_NOT_JOIN)
                            .message("guildUser not found")
                            .build();
                }
                jsonObject.put("userPosition", guildUser.getPosition());
                messageType = IMProto.MessageType.CHAT_GUILD.getNumber();
            } else if (groupType == IMGroupIdGenerator.GroupType.SERVER) {
                messageType = IMProto.MessageType.CHAT_WORLD_SERVER.getNumber();
            } else if (groupType == IMGroupIdGenerator.GroupType.CROSS_SERVER) {
                messageType = IMProto.MessageType.CHAT_CROSS_SERVER.getNumber();
            } else if (groupType == IMGroupIdGenerator.GroupType.GLOBAL) {
                messageType = IMProto.MessageType.CHAT_GLOBAL.getNumber();
            }
        } else if (!StringUtils.isEmpty(beforeChatRequest.getTargetUserId())) {
            // 私聊
            if (customDataJson == null || !customDataJson.containsKey("targetId")) {
                return BeforeChatResponse.builder()
                        .code(ErrorCode.PARAMS_ERROR)
                        .message("targetId is null")
                        .build();
            }
            long targetId = customDataJson.getLong("targetId");
            User targetUser = userService.getUser(targetId);
            if (targetUser == null) {
                return BeforeChatResponse.builder()
                        .code(ErrorCode.PARAMS_ERROR)
                        .message("target user is null")
                        .build();
            }
            messageType = IMProto.MessageType.CHAT_PRIVATE.getNumber();
        }

        jsonObject.put("msgId", msgId);
        jsonObject.put("chatContent", content);
        if (language != null) {
            jsonObject.put("language", language);
        }
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        return BeforeChatResponse.builder().code(0)
                .data(BeforeChatResponse.Data.builder()
                        .msgContent(MsgContent.builder()
                                .chatText(content)
                                .content(jsonObject.toJSONString())
                                .messageType(messageType)
                                .customData(customData)
                                .build()
                        )
                        .build()
                ).build();
    }

    /** 检测 group */
    private BeforeJoinGroupResponse callbackBeforeJoin(JSONObject params) {
        BeforeJoinGroupRequest beforeJoinGroupRequest = params.toJavaObject(BeforeJoinGroupRequest.class);
        long userId = Long.parseLong(beforeJoinGroupRequest.getUserId());
        List<String> groupIdList = beforeJoinGroupRequest.getGroupIdList();
        if (CollectionUtils.isNullOrEmpty(groupIdList)) {
            return BeforeJoinGroupResponse.builder()
                    .code(ErrorCode.FAIL)
                    .message("groupId is null")
                    .build();
        }

        User user = userService.getUser(userId);
        if (user == null) {
            return BeforeJoinGroupResponse.builder()
                    .code(ErrorCode.FAIL)
                    .message("user is null")
                    .build();
        }

        for (String groupId : groupIdList) {
            IMGroupIdGenerator.GroupType groupType = IMGroupIdGenerator.getGroupType(groupId);
            if (groupType.equals(IMGroupIdGenerator.GroupType.SERVER)) {
                long serverId = IMGroupIdGenerator.parseIdFromGroupId(groupId);
                if (user.getServerId() != serverId) {
                    return BeforeJoinGroupResponse.builder()
                            .code(ErrorCode.FAIL)
                            .message("serverId not match, user.serverId:" + user.getServerId() + ", request.serverId:" + serverId)
                            .build();
                }
            } else if (groupType.equals(IMGroupIdGenerator.GroupType.GUILD)) {
                long guildId = IMGroupIdGenerator.parseIdFromGroupId(groupId);
                GuildUser guildUser = guildUserDao.getByUserId(userId);
                long userGuildId = 0;
                if (guildUser != null) {
                    userGuildId = guildUser.getGuildId();
                }
                if (userGuildId != guildId || userGuildId == 0) {
                    return BeforeJoinGroupResponse.builder()
                            .code(ErrorCode.FAIL)
                            .message("guildId not match, guildId:" + userGuildId + ", request.guildId:" + guildId)
                            .build();
                }
            } else if (groupType.equals(IMGroupIdGenerator.GroupType.CROSS_SERVER)) {
                long crossId = IMGroupIdGenerator.parseIdFromGroupId(groupId);
                long crossServerId = IMGroupIdGenerator.getCrossServerId(user.getServerId());
                if (crossId != crossServerId) {
                    return BeforeJoinGroupResponse.builder()
                            .code(ErrorCode.FAIL)
                            .message("crossId not match, crossId:" + crossServerId + ", request.crossId:" + crossId)
                            .build();
                }
            } else if (groupType.equals(IMGroupIdGenerator.GroupType.GLOBAL)) {
                long globalId = IMGroupIdGenerator.parseIdFromGroupId(groupId);
                long zoneId = serverListService.getZoneIdByServerId(user.getServerId());
                if (globalId != zoneId) {
                    return BeforeJoinGroupResponse.builder()
                            .code(ErrorCode.FAIL)
                            .message("globalId not match, globalId:" + zoneId + ", request.globalId:" + globalId)
                            .build();
                }
            }
        }

        return BeforeJoinGroupResponse.builder()
                .code(ErrorCode.SUCCESS)
                .message("")
                .build();
    }

    private UserActiveTimeResponse getUserActiveTime(JSONObject params) {
        UserActiveTimeRequest request = params.toJavaObject(UserActiveTimeRequest.class);
        if (CollectionUtils.isNullOrEmpty(request.getUserIds())) {
            return UserActiveTimeResponse.builder().code(ErrorCode.FAIL).build();
        }

        Map<Long, Long> data = userInfoRedisDao.getActiveTM(request.getUserIds());
        return UserActiveTimeResponse.builder().code(0).data(data).build();
    }

    private UserInfoResponse getUserInfo(JSONObject params) {
        UserInfoRequest request = params.toJavaObject(UserInfoRequest.class);
        Map<Long, String> userInfoMap = new HashMap<>();
        if (!CollectionUtils.isNullOrEmpty(request.getUserIds())) {
            Map<Long, UserInfoModel> userMap = userDao.queryUserInfo(request.getUserIds(), false);
            for (Map.Entry<Long, UserInfoModel> entry : userMap.entrySet()) {
                userInfoMap.put(entry.getKey(), this.getUserCustomData(entry.getKey(), entry.getValue()));
            }
        }
        return UserInfoResponse.builder().code(0).userInfoMap(userInfoMap).build();
    }

    private String getUserCustomData(long userId, UserInfoModel user) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", userId);
        jsonObject.put("nickName", Optional.ofNullable(user.getNickName()).orElse(""));
        jsonObject.put("avatar", Optional.of(user.getAvatar()).orElse(0));
        jsonObject.put("avatarFrame", Optional.of(user.getAvatarFrame()).orElse(0));
        return jsonObject.toJSONString();
    }

    private void putUserInfo(JSONObject jsonObject, User user) {
        jsonObject.put("userId", user.getUserId());
        jsonObject.put("nickName", Optional.ofNullable(user.getNickName()).orElse(""));
        jsonObject.put("avatar", Optional.ofNullable(user.getAvatar()).orElse(0));
        jsonObject.put("avatarFrame", Optional.ofNullable(user.getAvatarFrame()).orElse(0));
    }



}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Builtin;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Vector4 {
    public Vector4(JsonObject _buf) { 
        x = _buf.get("x").getAsFloat();
        y = _buf.get("y").getAsFloat();
        z = _buf.get("z").getAsFloat();
        w = _buf.get("w").getAsFloat();
    }

    public static Vector4 deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Builtin.Vector4(_buf);
    }

    public final float x;
    public final float y;
    public final float z;
    public final float w;

    

    @Override
    public String toString() {
        return "{ "
        + "x:" + x + ","
        + "y:" + y + ","
        + "z:" + z + ","
        + "w:" + w + ","
        + "}";
    }
}


@echo off

REM 调用上一级目录中的 proto2java.bat 脚本
call ..\proto\proto2java.bat


rem @echo Proto file generator...
rem @echo off

rem :: proto文件所在目录（不需要修改）
rem set cur_path=..\proto\proto\

rem :: 类生成目录
rem set java_out_path=..\..\src\main\java\

rem :: 文件修改时间
rem node ../proto/filetime.js %cur_path%

rem setlocal enabledelayedexpansion

rem for %%i in (%cur_path%*.proto) do (

rem 	rem echo %%i

rem 	node ./proto/checkmodify.js %%~ni

rem 	if !errorlevel! EQU 1 (
rem 		echo %%i
rem 		protoc.exe --java_out=%java_out_path% --proto_path=%cur_path% %%i
rem 	) 
rem )

rem node ..\node_modules\protobufjs\bin\pbjs -t json ..\proto\proto\*.proto > proto.json
rem move proto.json ..\api\proto\
rem node ..\proto/createMsgId.js %cur_path%

rem @echo done!

rem pause 

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFishing_fishMove {
    public ActivityFishing_fishMove(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("strongTime").getAsJsonArray(); strongTime = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  strongTime.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("struggleTime").getAsJsonArray(); struggleTime = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  struggleTime.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("tireTime").getAsJsonArray(); tireTime = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  tireTime.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("action").getAsJsonArray(); action = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  action.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("actionTime").getAsJsonArray(); actionTime = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  actionTime.add(_v0); }   }
    }

    public static ActivityFishing_fishMove deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFishing_fishMove(_buf);
    }

    /**
     * ID
     */
    public final int id;
    /**
     * 有力时间
     */
    public final java.util.List<Integer> strongTime;
    /**
     * 挣扎时间
     */
    public final java.util.List<Integer> struggleTime;
    /**
     * 乏力时间
     */
    public final java.util.List<Integer> tireTime;
    /**
     * 动作序列<br/>1有力 2挣扎 3乏力<br/>序列完成后循环
     */
    public final java.util.List<Integer> action;
    /**
     * 动作序列<br/>对应的时间
     */
    public final java.util.List<Integer> actionTime;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "strongTime:" + strongTime + ","
        + "struggleTime:" + struggleTime + ","
        + "tireTime:" + tireTime + ","
        + "action:" + action + ","
        + "actionTime:" + actionTime + ","
        + "}";
    }
}


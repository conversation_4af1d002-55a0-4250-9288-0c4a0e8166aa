{"nested": {"Proto": {"nested": {"Activity": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "ActivityProto"}, "nested": {"ActivityGetListRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "ActivityGetListResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "activityInfos": {"rule": "repeated", "type": "ActivityInfoDto", "id": 3}, "refTime": {"type": "uint64", "id": 4}, "taskMap": {"keyType": "uint32", "type": "TaskListDto", "id": 5}, "shopMap": {"keyType": "uint32", "type": "ShopListDto", "id": 6}, "sevenDayTaskDto": {"type": "Proto.SevenDayTask.SevenDayDto", "id": 7}, "signInData": {"type": "Proto.SignIn.SignInData", "id": 8}, "fishingDto": {"type": "Proto.Fishing.FishingDto", "id": 9}, "flipDto": {"type": "Proto.Flip.FlipDto", "id": 10}, "diveDto": {"type": "Proto.Dive.DiveDto", "id": 11}, "consumeDtos": {"rule": "repeated", "type": "ConsumeDto", "id": 12}, "powerDto": {"type": "Proto.Power.PowerDto", "id": 13}}}, "ActivityGetTaskRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ActivityGetTaskResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "taskMap": {"keyType": "uint32", "type": "TaskListDto", "id": 3}}}, "ActivityTaskRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"type": "uint32", "id": 2}, "taskId": {"type": "uint32", "id": 3}}}, "ActivityTaskRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "dtos": {"rule": "repeated", "type": "Proto.Common.ActivityTaskDto", "id": 3}}}, "ActivityGetShopRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ActivityGetShopResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "shopMap": {"keyType": "uint32", "type": "ShopListDto", "id": 3}}}, "ActivityShopExchangeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"type": "uint32", "id": 2}, "shopId": {"type": "uint32", "id": 3}}}, "ActivityShopExchangeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "shop": {"type": "Proto.Common.ActivityShopDto", "id": 3}}}, "ActivityGetRankRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"type": "uint32", "id": 2}, "page": {"type": "uint32", "id": 3}}}, "ActivityGetRankResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "rank": {"rule": "repeated", "type": "Proto.Common.RankDto", "id": 3}, "ownerRank": {"type": "uint32", "id": 4}, "ownerScore": {"type": "uint64", "id": 5}}}, "ActivityInfoDto": {"fields": {"activityId": {"type": "uint32", "id": 1}, "startTime": {"type": "uint64", "id": 2}, "endTime": {"type": "uint64", "id": 3}}}, "ConsumeDto": {"fields": {"activityId": {"type": "uint32", "id": 1}, "completeCount": {"type": "uint32", "id": 2}, "finishList": {"rule": "repeated", "type": "uint32", "id": 3}, "round": {"type": "uint32", "id": 4}}}, "TaskListDto": {"fields": {"tasks": {"rule": "repeated", "type": "Proto.Common.ActivityTaskDto", "id": 1}}}, "ShopListDto": {"fields": {"shop": {"rule": "repeated", "type": "Proto.Common.ActivityShopDto", "id": 1}}}}}, "Common": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "CommonProto"}, "nested": {"CommonParams": {"fields": {"accountId": {"type": "string", "id": 1}, "version": {"type": "uint32", "id": 2}, "deviceId": {"type": "string", "id": 3}, "accessToken": {"type": "string", "id": 4}, "transId": {"type": "uint64", "id": 5}, "serverId": {"type": "uint32", "id": 6}, "languageMark": {"type": "string", "id": 7}}}, "UserLevel": {"fields": {"level": {"type": "uint32", "id": 1}, "exp": {"type": "uint32", "id": 2}}}, "UserCurrency": {"fields": {"coins": {"type": "int64", "id": 1}, "diamonds": {"type": "int64", "id": 2}}}, "RewardDto": {"fields": {"type": {"type": "uint32", "id": 1}, "configId": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}}}, "ItemDto": {"fields": {"rowId": {"type": "uint64", "id": 1}, "itemId": {"type": "uint32", "id": 2}, "count": {"type": "int64", "id": 3}}}, "UpdateUserCurrency": {"fields": {"isChange": {"type": "bool", "id": 1}, "userCurrency": {"type": "UserCurrency", "id": 2}}}, "UpdateUserLevel": {"fields": {"isChange": {"type": "bool", "id": 1}, "userLevel": {"type": "UserLevel", "id": 2}, "levelUpReward": {"rule": "repeated", "type": "<PERSON><PERSON><PERSON><PERSON>", "id": 3}}}, "UpdateTransId": {"fields": {"isChange": {"type": "bool", "id": 1}, "transId": {"type": "uint64", "id": 2}}}, "AdInfoDto": {"fields": {"adId": {"type": "uint32", "id": 1}, "leftCount": {"type": "int32", "id": 2}, "maxCount": {"type": "uint32", "id": 3}, "cdTS": {"type": "uint64", "id": 4}, "rTS": {"type": "uint64", "id": 5}}}, "UserInfoDto": {"fields": {"userId": {"type": "uint64", "id": 1}, "nickName": {"type": "string", "id": 2}, "avatar": {"type": "uint32", "id": 3}, "avatarFrame": {"type": "uint32", "id": 4}, "level": {"type": "uint32", "id": 5}, "power": {"type": "uint64", "id": 6}}}, "UserVitality": {"fields": {"value": {"type": "int32", "id": 1}, "maxValue": {"type": "int32", "id": 2}, "ts": {"type": "int64", "id": 3}, "buyCount": {"type": "int32", "id": 4}}}, "UpdateUserVitality": {"fields": {"isChange": {"type": "bool", "id": 1}, "vitality": {"type": "UserVitality", "id": 2}}}, "CommonData": {"fields": {"updateTransId": {"type": "UpdateTransId", "id": 1}, "updateUserCurrency": {"type": "UpdateUserCurrency", "id": 2}, "reward": {"rule": "repeated", "type": "<PERSON><PERSON><PERSON><PERSON>", "id": 3}, "updateUserLevel": {"type": "UpdateUserLevel", "id": 4}, "taskRedPoint": {"type": "bool", "id": 5}, "equipment": {"rule": "repeated", "type": "EquipmentDto", "id": 6}, "items": {"rule": "repeated", "type": "ItemDto", "id": 7}, "guildTaskRedPoint": {"type": "bool", "id": 8}, "deleteEquipRowIds": {"rule": "repeated", "type": "uint64", "id": 9}, "heros": {"rule": "repeated", "type": "HeroDto", "id": 10}, "updateUserVipLevel": {"type": "UpdateUserVipLevel", "id": 11}, "updateTasks": {"rule": "repeated", "type": "TaskDto", "id": 12}, "sevenDayTaskRedPoint": {"type": "bool", "id": 13}, "sevenDayTaskDto": {"rule": "repeated", "type": "SevenDayTaskDto", "id": 14}, "battlePassScore": {"type": "uint32", "id": 15}, "activityTasks": {"rule": "repeated", "type": "ActivityTaskDto", "id": 16}, "formations": {"keyType": "uint32", "type": "Proto.Common.LongArray", "id": 17}, "updateUserVitality": {"type": "UpdateUserVitality", "id": 18}, "adInfo": {"rule": "repeated", "type": "AdInfoDto", "id": 19}}}, "ErrorMsg": {"fields": {"code": {"type": "int32", "id": 1}, "msg": {"type": "string", "id": 2}}}, "TowerRankDto": {"fields": {"userId": {"type": "int64", "id": 1}, "nickName": {"type": "string", "id": 2}, "avatar": {"type": "int32", "id": 3}, "avatarFrame": {"type": "int32", "id": 4}, "power": {"type": "int64", "id": 5}, "tower": {"type": "int32", "id": 6}}}, "RankDto": {"fields": {"userId": {"type": "int64", "id": 1}, "nickName": {"type": "string", "id": 2}, "avatar": {"type": "int32", "id": 3}, "avatarFrame": {"type": "int32", "id": 4}, "power": {"type": "int64", "id": 5}, "score": {"type": "int64", "id": 6}}}, "CombatUnitDto": {"fields": {"rowId": {"type": "int64", "id": 1}, "instanceID": {"type": "int32", "id": 2}, "hp": {"type": "int64", "id": 3}, "energy": {"type": "int64", "id": 4}, "hpPercent": {"type": "uint32", "id": 5}, "rechargePercent": {"type": "uint32", "id": 6}}}, "IAPDto": {"fields": {"giftPack": {"type": "IAPGiftPackDto", "id": 1}, "monthCardMap": {"keyType": "uint32", "type": "IAPMonthCardDto", "id": 2}, "battlePassInfo": {"type": "IAPBattlePassDto", "id": 3}, "firstRechargeReward": {"type": "bool", "id": 4}, "totalRecharge": {"type": "uint32", "id": 5}, "buyOpenServerGiftId": {"keyType": "uint32", "type": "uint64", "id": 6}, "chapterGiftTime": {"keyType": "uint32", "type": "uint64", "id": 7}, "levelFund": {"type": "LevelFundDto", "id": 8}}}, "LevelFundDto": {"fields": {"buyLevelFundGroupId": {"rule": "repeated", "type": "uint32", "id": 1}, "levelFundReward": {"keyType": "uint32", "type": "IntegerArray", "id": 2}}}, "IAPGiftPackDto": {"fields": {"packsBuyCount": {"keyType": "uint32", "type": "uint32", "id": 1}, "packsResetTimeDay": {"type": "uint64", "id": 2}, "packsResetTimeWeek": {"type": "uint64", "id": 3}, "packsResetTimeMonth": {"type": "uint64", "id": 4}}}, "IAPMonthCardDto": {"fields": {"configId": {"type": "uint32", "id": 1}, "lastCount": {"type": "uint32", "id": 2}, "lastRewardTime": {"type": "uint64", "id": 3}, "canReward": {"type": "uint32", "id": 4}, "nextRewardTime": {"type": "uint64", "id": 5}}}, "IAPBattlePassDto": {"fields": {"battlePassId": {"type": "uint32", "id": 1}, "buy": {"type": "uint32", "id": 2}, "score": {"type": "uint32", "id": 3}, "freeRewardIdList": {"rule": "repeated", "type": "uint32", "id": 4}, "battlePassRewardIdList": {"rule": "repeated", "type": "uint32", "id": 5}, "canRewardFinalCount": {"type": "uint32", "id": 6}, "rewardFinalCount": {"type": "uint32", "id": 7}}}, "UpdateUserVipLevel": {"fields": {"isChange": {"type": "bool", "id": 1}, "userVipLevel": {"type": "UserVipLevel", "id": 2}}}, "UserVipLevel": {"fields": {"vipLevel": {"type": "uint32", "id": 1}, "vipExp": {"type": "uint32", "id": 2}, "rewardId": {"rule": "repeated", "type": "uint32", "id": 3}}}, "IntegerArray": {"fields": {"integerArray": {"rule": "repeated", "type": "int32", "id": 1}}}, "Uint32List": {"fields": {"values": {"rule": "repeated", "type": "uint32", "id": 1}}}, "HeroDto": {"fields": {"rowId": {"type": "uint64", "id": 1}, "heroId": {"type": "uint32", "id": 2}, "level": {"type": "uint32", "id": 3}, "exp": {"type": "uint32", "id": 4}, "star": {"type": "uint32", "id": 5}, "quality": {"type": "uint32", "id": 6}, "advance": {"type": "uint32", "id": 7}, "skinConfigId": {"type": "uint32", "id": 8}}}, "EquipmentDto": {"fields": {"rowId": {"type": "uint64", "id": 1}, "equipId": {"type": "uint32", "id": 2}, "level": {"type": "uint32", "id": 3}, "exp": {"type": "uint32", "id": 4}, "quality": {"type": "uint32", "id": 5}, "heroRowId": {"type": "uint64", "id": 6}}}, "EquipmentStatsDto": {"fields": {"rowId": {"type": "uint64", "id": 1}, "equipId": {"type": "uint32", "id": 2}, "attrs": {"rule": "repeated", "type": "string", "id": 3}}}, "BattleUserDto": {"fields": {"userId": {"type": "int64", "id": 1}, "serverId": {"type": "int32", "id": 2}, "nickName": {"type": "string", "id": 3}, "level": {"type": "int32", "id": 4}, "avatar": {"type": "int32", "id": 5}, "avatarFrame": {"type": "int32", "id": 6}, "power": {"type": "int64", "id": 7}, "unit": {"type": "BattleUnitDto", "id": 10}}}, "BattleUnitDto": {"fields": {"userId": {"type": "int64", "id": 1}, "equips": {"rule": "repeated", "type": "Proto.Common.EquipmentDto", "id": 2}, "formations": {"keyType": "int32", "type": "FormationDto", "id": 3}, "guildTechLv": {"type": "int32", "id": 4}, "skinConfigIds": {"rule": "repeated", "type": "int32", "id": 5}, "vipLevel": {"type": "uint32", "id": 6}, "heroBookScoreCounts": {"rule": "repeated", "type": "Proto.Common.DIntInt", "id": 7}, "heroBondCounts": {"rule": "repeated", "type": "Proto.Common.DIntInt", "id": 8}}}, "FormationDto": {"fields": {"heroes": {"rule": "repeated", "type": "Proto.Common.HeroDto", "id": 1}}}, "PVPRecordDto": {"fields": {"ownerUser": {"type": "BattleUserDto", "id": 1}, "otherUser": {"type": "BattleUserDto", "id": 2}, "result": {"type": "int32", "id": 3}, "seed": {"type": "int32", "id": 4}, "startUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 5}, "endUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 6}, "reportRowId": {"type": "int64", "id": 7}, "time": {"type": "int64", "id": 8}}}, "ActivityTaskDto": {"fields": {"id": {"type": "uint32", "id": 1}, "activityId": {"type": "uint32", "id": 2}, "process": {"type": "uint64", "id": 3}, "isReceive": {"type": "bool", "id": 4}}}, "ActivityShopDto": {"fields": {"id": {"type": "uint32", "id": 1}, "activityId": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}}}, "RewardDtoListDto": {"fields": {"rewardDtos": {"rule": "repeated", "type": "<PERSON><PERSON><PERSON><PERSON>", "id": 1}}}, "DIntInt": {"fields": {"key": {"type": "int32", "id": 1}, "val": {"type": "int32", "id": 2}}}, "DLongInt": {"fields": {"key": {"type": "int64", "id": 1}, "val": {"type": "int32", "id": 2}}}, "TaskDto": {"fields": {"id": {"type": "uint32", "id": 1}, "process": {"type": "uint64", "id": 2}, "isFinish": {"type": "bool", "id": 3}, "isReceive": {"type": "bool", "id": 4}, "taskType": {"type": "uint32", "id": 5}}}, "SevenDayTaskDto": {"fields": {"id": {"type": "uint32", "id": 1}, "process": {"type": "uint64", "id": 2}, "isFinish": {"type": "bool", "id": 3}, "isReceive": {"type": "bool", "id": 4}}}, "LongArray": {"fields": {"longArray": {"rule": "repeated", "type": "int64", "id": 1}}}, "BattleUnitCacheDto": {"fields": {"userId": {"type": "int64", "id": 1}, "formations": {"keyType": "int32", "type": "LongArray", "id": 2}, "equips": {"rule": "repeated", "type": "Proto.Common.EquipmentDto", "id": 3}, "heroes": {"rule": "repeated", "type": "Proto.Common.HeroDto", "id": 4}, "guildTechLv": {"type": "int32", "id": 5}, "skinConfigIds": {"rule": "repeated", "type": "int32", "id": 6}, "vipLevel": {"type": "uint32", "id": 7}, "heroBookScoreCounts": {"rule": "repeated", "type": "Proto.Common.DIntInt", "id": 8}, "heroBondCounts": {"rule": "repeated", "type": "Proto.Common.DIntInt", "id": 9}}}}}, "SignIn": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "SignInProto"}, "nested": {"SignInGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "SignInGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "signInData": {"type": "SignInData", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "SignInDoSignRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "SignInDoSignResponse": {"fields": {"code": {"type": "int32", "id": 1}, "signInData": {"type": "SignInData", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "SignInData": {"fields": {"isCanSignIn": {"type": "bool", "id": 1}, "timestamp": {"type": "uint64", "id": 2}, "log": {"type": "uint32", "id": 3}, "rewardDtoList": {"rule": "repeated", "type": "Proto.Common.RewardDtoListDto", "id": 4}, "configId": {"type": "uint32", "id": 5}}}}}, "Fishing": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "FishingProto"}, "nested": {"FishingOnOpenRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "FishingOnOpenResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "fish": {"type": "FishingDto", "id": 3}}}, "FishingCastRodRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "baitNum": {"type": "uint32", "id": 2}, "eval": {"type": "uint32", "id": 3}}}, "FishingCastRodResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "fishIds": {"rule": "repeated", "type": "uint32", "id": 3}, "nextRebornDiamond": {"type": "uint32", "id": 4}}}, "FishingReelInRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "catch": {"type": "bool", "id": 2}}}, "FishingReelInResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "fishDtos": {"rule": "repeated", "type": "FishDto", "id": 3}, "weight": {"type": "uint32", "id": 4}, "FishingDto": {"type": "FishingDto", "id": 5}, "unlockRod": {"type": "uint32", "id": 6}}}, "FishingBuyBaitRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "buyNum": {"type": "uint32", "id": 2}}}, "FishingBuyBaitResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "FishingRebornRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "isAd": {"type": "bool", "id": 2}}}, "FishingRebornResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "nextRebornDiamond": {"type": "uint32", "id": 3}}}, "FishingDto": {"fields": {"accWeight": {"type": "uint32", "id": 1}, "curRod": {"type": "uint32", "id": 2}, "configDto": {"type": "FishingConfigDto", "id": 4}, "nextRebornDiamond": {"type": "uint32", "id": 5}, "isThrow": {"type": "bool", "id": 6}}}, "FishingConfigDto": {"fields": {"baitItemId": {"type": "uint32", "id": 1}, "pointItemId": {"type": "uint32", "id": 2}, "lineItemId": {"type": "uint32", "id": 3}, "baitPrice": {"type": "uint32", "id": 5}}}, "FishDto": {"fields": {"fishId": {"type": "uint32", "id": 1}, "weight": {"type": "uint32", "id": 2}}}}}, "Flip": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "FlipProto"}, "nested": {"FlipOnOpenRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "FlipOnOpenResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "flipDto": {"type": "FlipDto", "id": 3}}}, "FlipAccRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "need": {"type": "uint32", "id": 2}}}, "FlipAccRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "clueNum": {"type": "uint64", "id": 3}, "accRewards": {"rule": "repeated", "type": "AccClueRewardDto", "id": 4}}}, "FlipBuyStepRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "buyNum": {"type": "uint32", "id": 2}}}, "FlipBuyStepResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "FlipShowGridRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "isItem": {"type": "bool", "id": 2}, "index": {"type": "uint32", "id": 3}}}, "FlipShowGridResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "showGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 3}, "mapGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 4}, "accSteps": {"type": "uint32", "id": 5}}}, "FlipRewardGridRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "index": {"type": "uint32", "id": 2}}}, "FlipRewardGridResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "showGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 3}, "activeGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 4}, "mapGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 5}, "impasseShowGrid": {"type": "FlipGridDto", "id": 6}, "accSteps": {"type": "uint32", "id": 7}}}, "FlipClueGridRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "index": {"type": "uint32", "id": 2}}}, "FlipClueGridResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "clueNum": {"type": "uint32", "id": 3}, "accRewards": {"rule": "repeated", "type": "AccClueRewardDto", "id": 4}, "mapGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 5}, "hasSpecialRewards": {"type": "bool", "id": 6}, "accSteps": {"type": "uint32", "id": 7}, "oldMapGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 8}}}, "FlipBombGridRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "index": {"type": "uint32", "id": 2}}}, "FlipBombGridResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "bombGrids": {"rule": "repeated", "type": "FlipBombGridDto", "id": 3}, "rewardGrids": {"rule": "repeated", "type": "FlipBombRewardGridDto", "id": 4}, "allShowGrids": {"rule": "repeated", "type": "FlipBombAllShowGridDto", "id": 5}, "stoneGrids": {"rule": "repeated", "type": "FlipBombStoneGridDto", "id": 6}, "collectGrids": {"rule": "repeated", "type": "FlipBombCollectGridDto", "id": 7}, "mapGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 8}, "impasseShowGrid": {"type": "FlipGridDto", "id": 9}, "accSteps": {"type": "uint32", "id": 10}}}, "FlipMapFindSpecialRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "FlipMapFindSpecialResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "hasSpecialRewards": {"type": "bool", "id": 3}}}, "FlipAllAccRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "FlipAllAccRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "needs": {"rule": "repeated", "type": "uint32", "id": 3}, "clueNum": {"type": "uint64", "id": 4}, "accRewards": {"rule": "repeated", "type": "AccClueRewardDto", "id": 5}}}, "FlipBombGridDto": {"fields": {"bombSource": {"type": "FlipGridDto", "id": 1}, "showGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 2}}}, "FlipBombRewardGridDto": {"fields": {"rewardSource": {"type": "FlipGridDto", "id": 1}, "rewardDto": {"type": "Proto.Common.Reward<PERSON>to", "id": 2}}}, "FlipBombAllShowGridDto": {"fields": {"showSource": {"type": "FlipGridDto", "id": 1}, "showGrids": {"rule": "repeated", "type": "FlipGridDto", "id": 2}}}, "FlipBombStoneGridDto": {"fields": {"source": {"type": "FlipGridDto", "id": 1}, "self": {"type": "FlipGridDto", "id": 2}, "rewardDto": {"type": "Proto.Common.Reward<PERSON>to", "id": 3}}}, "FlipBombCollectGridDto": {"fields": {"collectSource": {"type": "FlipGridDto", "id": 1}, "effects": {"rule": "repeated", "type": "FlipCollectEffectDto", "id": 2}}}, "FlipCollectEffectDto": {"fields": {"stoneTarger": {"type": "FlipGridDto", "id": 1}, "rewardDto": {"type": "Proto.Common.Reward<PERSON>to", "id": 2}}}, "FlipDto": {"fields": {"activityId": {"type": "uint32", "id": 1}, "clueNum": {"type": "uint32", "id": 4}, "serverClueNum": {"type": "uint32", "id": 5}, "accRewards": {"rule": "repeated", "type": "AccClueRewardDto", "id": 6}, "grids": {"rule": "repeated", "type": "FlipGridDto", "id": 8}, "config": {"type": "FlipConfigDto", "id": 9}}}, "AccClueRewardDto": {"fields": {"need": {"type": "uint32", "id": 1}, "rewardDto": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 2}, "state": {"type": "uint32", "id": 3}}}, "FlipGridDto": {"fields": {"index": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "status": {"type": "uint32", "id": 3}, "rewardDto": {"type": "Proto.Common.Reward<PERSON>to", "id": 4}}}, "FlipConfigDto": {"fields": {"stepItemId": {"type": "uint32", "id": 1}, "clueItemId": {"type": "uint32", "id": 2}, "stepPrice": {"type": "uint32", "id": 3}, "stones": {"rule": "repeated", "type": "uint32", "id": 4}, "allShowItemId": {"type": "uint32", "id": 5}}}}}, "Dive": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "DiveProto"}, "nested": {"DiveOnOpenRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "DiveOnOpenResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "diveDto": {"type": "DiveDto", "id": 3}}}, "DiveBuyItemRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "buyCount": {"type": "uint32", "id": 2}, "butItemId": {"type": "uint32", "id": 3}}}, "DiveBuyItemResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "DiveAccRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "accType": {"type": "uint32", "id": 2}, "need": {"type": "uint32", "id": 3}}}, "DiveAccRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "diveDto": {"type": "DiveDto", "id": 3}}}, "DiveShineRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "depth": {"type": "uint32", "id": 2}, "index": {"type": "uint32", "id": 3}}}, "DiveShineResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "shineGrid": {"type": "DiveGridDto", "id": 3}, "sharkGrid": {"type": "DiveGridDto", "id": 4}, "varecGrid": {"type": "DiveGridDto", "id": 5}, "diveDto": {"type": "DiveDto", "id": 6}, "rewardDto": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 7}}}, "DiveUsePropRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "depth": {"type": "uint32", "id": 2}, "index": {"type": "uint32", "id": 3}, "itemType": {"type": "uint32", "id": 4}}}, "DiveUsePropResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "shineGrid": {"rule": "repeated", "type": "DiveGridDto", "id": 3}, "sharkGrid": {"rule": "repeated", "type": "DiveGridDto", "id": 4}, "varecGrid": {"rule": "repeated", "type": "DiveGridDto", "id": 5}, "diveDto": {"type": "DiveDto", "id": 6}, "rewardDto": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 7}}}, "DiveAllAccRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "DiveAllAccRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "needs": {"rule": "repeated", "type": "uint32", "id": 3}, "dto": {"type": "DiveDto", "id": 4}}}, "DiveDto": {"fields": {"activityId": {"type": "uint32", "id": 1}, "startTimestamp": {"type": "uint64", "id": 2}, "endTimestamp": {"type": "uint64", "id": 3}, "configDto": {"type": "DiveConfigDto", "id": 4}, "userDepth": {"type": "uint32", "id": 5}, "userAccRewards": {"rule": "repeated", "type": "DiveAccRewardDto", "id": 6}, "guildDepth": {"type": "uint32", "id": 7}, "guildAccRewards": {"rule": "repeated", "type": "DiveAccRewardDto", "id": 8}, "lineDtos": {"rule": "repeated", "type": "DiveLineDto", "id": 9}}}, "DiveConfigDto": {"fields": {"diveItemId": {"type": "uint32", "id": 1}, "divePrice": {"type": "uint32", "id": 2}, "divePropA": {"type": "uint32", "id": 3}, "divePropB": {"type": "uint32", "id": 4}, "divePrice1": {"type": "uint32", "id": 5}, "divePrice2": {"type": "uint32", "id": 6}, "exchangeItem": {"rule": "repeated", "type": "uint32", "id": 7}}}, "DiveAccRewardDto": {"fields": {"need": {"type": "uint32", "id": 1}, "rewardDto": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 2}, "state": {"type": "uint32", "id": 3}}}, "DiveLineDto": {"fields": {"depth": {"type": "uint32", "id": 1}, "gridDtos": {"rule": "repeated", "type": "DiveGridDto", "id": 2}}}, "DiveGridDto": {"fields": {"type": {"type": "int32", "id": 1}, "shark": {"type": "uint32", "id": 2}, "varecRewardNum": {"type": "uint32", "id": 3}, "ice": {"type": "uint32", "id": 4}, "rewardDto": {"type": "Proto.Common.Reward<PERSON>to", "id": 5}, "depth": {"type": "uint32", "id": 6}, "index": {"type": "uint32", "id": 7}, "light": {"type": "uint32", "id": 8}, "varecClick": {"type": "bool", "id": 9}, "rwardNum": {"type": "uint32", "id": 10}}}}}, "SevenDayTask": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "SevenDayTaskProto"}, "nested": {"SevenDayTaskGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "SevenDayTaskGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "sevenDayDto": {"type": "SevenDayDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "SevenDayTaskRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "taskId": {"type": "uint32", "id": 2}}}, "SevenDayTaskRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "active": {"type": "uint32", "id": 3}, "updateTaskDto": {"rule": "repeated", "type": "Proto.Common.SevenDayTaskDto", "id": 4}}}, "SevenDayTaskActiveRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "configId": {"type": "uint32", "id": 2}, "selectIdx": {"type": "uint32", "id": 3}}}, "SevenDayTaskActiveRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "activeLog": {"type": "uint64", "id": 3}, "updateTaskDto": {"rule": "repeated", "type": "Proto.Common.SevenDayTaskDto", "id": 4}}}, "SevenDayDto": {"fields": {"days": {"type": "uint32", "id": 1}, "taskEndTimestamp": {"type": "uint64", "id": 2}, "endTimestamp": {"type": "uint64", "id": 3}, "activeLog": {"type": "uint64", "id": 4}, "active": {"type": "uint32", "id": 5}, "tasks": {"rule": "repeated", "type": "Proto.Common.SevenDayTaskDto", "id": 6}}}}}, "Power": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "PowerProto"}, "nested": {"PowerOnOpenRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "PowerOnOpenResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "powerDto": {"type": "PowerDto", "id": 3}}}, "PowerRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "configId": {"type": "int32", "id": 2}}}, "PowerRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "PowerDto": {"fields": {"activityId": {"type": "int32", "id": 1}, "rewardId": {"rule": "repeated", "type": "int32", "id": 2}}}}}, "Battle": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "BattleProto"}, "nested": {"BattleService": {"methods": {"handleCombat": {"requestType": "RChapterCombatReq", "responseType": "RChapterCombatResp"}, "handlePower": {"requestType": "RpcPowerReq", "responseType": "RpcPowerResp"}, "handleGuildBoss": {"requestType": "RGuildBossCombatReq", "responseType": "RGuildBossCombatResp"}}}, "RChapterCombatReq": {"fields": {"chapterId": {"type": "int32", "id": 1}, "waveIndex": {"type": "int32", "id": 2}, "unit": {"type": "Proto.Common.BattleUnitDto", "id": 3}, "skills": {"rule": "repeated", "type": "int32", "id": 4}, "seed": {"type": "int32", "id": 5}, "units": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 6}}}, "RChapterCombatResp": {"fields": {"code": {"type": "int32", "id": 1}, "chapterId": {"type": "int32", "id": 2}, "waveIndex": {"type": "int32", "id": 3}, "result": {"type": "int32", "id": 4}, "seed": {"type": "int32", "id": 6}, "startUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 7}, "endUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 8}, "power": {"type": "int64", "id": 9}}}, "RGuildBossCombatReq": {"fields": {"bossId": {"type": "int32", "id": 1}, "unit": {"type": "Proto.Common.BattleUnitDto", "id": 2}, "seed": {"type": "int32", "id": 3}}}, "RGuildBossCombatResp": {"fields": {"code": {"type": "int32", "id": 1}, "seed": {"type": "int32", "id": 2}, "startUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 3}, "endUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 4}, "damage": {"type": "uint64", "id": 5}}}, "RpcPowerReq": {"fields": {"unit": {"type": "Proto.Common.BattleUnitDto", "id": 1}}}, "RpcPowerResp": {"fields": {"code": {"type": "int32", "id": 1}, "result": {"type": "int64", "id": 2}}}}}, "Chat": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "ChatProto"}, "nested": {"ChatShowItemRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "itemType": {"type": "uint32", "id": 2}, "rowId": {"type": "uint64", "id": 3}, "groupId": {"type": "string", "id": 4}}}, "ChatShowItemResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}}}, "Develop": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "DevelopProto"}, "nested": {"DevelopLoginRequest": {"fields": {"userId": {"type": "uint64", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "DevelopLoginResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonParams": {"type": "Proto.Common.CommonParams", "id": 2}, "userId": {"type": "int64", "id": 3}, "dbIdx": {"type": "int64", "id": 4}, "tableIdx": {"type": "int64", "id": 5}, "coins": {"type": "int64", "id": 6}, "diamonds": {"type": "int64", "id": 7}, "chapterId": {"type": "int32", "id": 8}, "loginType": {"type": "uint32", "id": 9}, "level": {"type": "uint32", "id": 10}, "exp": {"type": "uint32", "id": 11}, "extra": {"type": "string", "id": 12}, "missionId": {"type": "uint32", "id": 13}}}, "DevelopChangeResourceRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "resType": {"type": "int32", "id": 2}, "resNum": {"type": "int32", "id": 3}, "itemId": {"type": "int32", "id": 4}, "otherData": {"type": "string", "id": 5}}}, "DevelopChangeResourceResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "respData": {"type": "string", "id": 3}}}, "DevelopToolsRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "params": {"type": "string", "id": 2}}}, "DevelopToolsResponse": {"fields": {"code": {"type": "int32", "id": 1}, "respData": {"type": "string", "id": 2}}}}}, "Equip": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "EquipProto"}, "nested": {"EquipStrengthRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowId": {"type": "uint64", "id": 2}, "equipRowIds": {"rule": "repeated", "type": "uint64", "id": 3}, "useItems": {"keyType": "uint32", "type": "uint32", "id": 4}}}, "EquipStrengthResponse": {"fields": {"code": {"type": "int32", "id": 1}, "delEquipRowIds": {"rule": "repeated", "type": "uint64", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "EquipComposeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "composeData": {"rule": "repeated", "type": "EquipComposeData", "id": 2}}}, "EquipComposeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "delEquipRowId": {"rule": "repeated", "type": "int64", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "EquipUpgradeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowId": {"type": "uint64", "id": 2}, "count": {"type": "uint32", "id": 3}}}, "EquipUpgradeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "EquipDressRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}, "heroRowId": {"type": "uint64", "id": 3}}}, "EquipDressResponse": {"fields": {"code": {"type": "int32", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "EquipLevelResetRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}}}, "EquipLevelResetResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "EquipQualityDownRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}}}, "EquipQualityDownResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "EquipOffRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}, "heroRowId": {"type": "uint64", "id": 3}}}, "EquipOffResponse": {"fields": {"code": {"type": "int32", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "EquipReplaceRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowId": {"type": "uint64", "id": 2}, "heroRowId": {"type": "uint64", "id": 3}}}, "EquipReplaceResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "EquipComposeData": {"fields": {"mainRowId": {"type": "uint64", "id": 1}, "rowIds": {"rule": "repeated", "type": "uint64", "id": 2}}}}}, "Guild": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "GuildProto"}, "nested": {"GuildGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "isJoined": {"type": "bool", "id": 2}, "guildDetailInfoDto": {"type": "GuildDetailInfoDto", "id": 3}, "guildFeaturesDto": {"type": "GuildFeaturesDto", "id": 4}, "isLevelUp": {"type": "bool", "id": 5}, "beKickedOutDto": {"type": "BeKickedOutDto", "id": 6}, "commonData": {"type": "Proto.Common.CommonData", "id": 7}, "donationReward": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 8}}}, "GuildCreateRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guildName": {"type": "string", "id": 2}, "guildIntro": {"type": "string", "id": 3}, "guildIcon": {"type": "uint32", "id": 4}, "guildIconBg": {"type": "uint32", "id": 5}, "applyType": {"type": "uint32", "id": 6}, "applyCondition": {"type": "uint32", "id": 7}, "language": {"type": "uint32", "id": 8}, "guildNotice": {"type": "string", "id": 9}}}, "GuildCreateResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildDetailInfoDto": {"type": "GuildDetailInfoDto", "id": 3}, "guildFeaturesDto": {"type": "GuildFeaturesDto", "id": 4}}}, "GuildSearchRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "type": {"type": "uint32", "id": 2}, "value": {"type": "string", "id": 3}, "isOnlyJoinable": {"type": "bool", "id": 4}, "excludeGuildIds": {"rule": "repeated", "type": "uint64", "id": 5}, "pageIndex": {"type": "uint32", "id": 6}}}, "GuildSearchResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildInfoDtos": {"rule": "repeated", "type": "GuildInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildGetDetailRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guildId": {"type": "uint64", "id": 2}}}, "GuildGetDetailResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildDetailInfoDto": {"type": "GuildDetailInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildGetMemberListRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guildId": {"type": "uint64", "id": 2}}}, "GuildGetMemberListResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildMemberInfoDtos": {"rule": "repeated", "type": "GuildMemberInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildModifyRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guildName": {"type": "string", "id": 2}, "guildIntro": {"type": "string", "id": 3}, "guildIcon": {"type": "uint32", "id": 4}, "guildIconBg": {"type": "uint32", "id": 5}, "applyType": {"type": "uint32", "id": 6}, "applyCondition": {"type": "uint32", "id": 7}, "isModifyGuildIntro": {"type": "bool", "id": 8}, "language": {"type": "uint32", "id": 9}, "guildNotice": {"type": "string", "id": 10}, "isModifyGuildNotice": {"type": "bool", "id": 11}}}, "GuildModifyResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildInfoDto": {"type": "GuildInfoDto", "id": 3}}}, "GuildDismissRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildDismissResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "GuildApplyJoinRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guildId": {"type": "uint64", "id": 2}, "language": {"type": "uint32", "id": 3}}}, "GuildApplyJoinResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildDetailInfoDto": {"type": "GuildDetailInfoDto", "id": 2}, "guildFeaturesDto": {"type": "GuildFeaturesDto", "id": 3}, "commonData": {"type": "Proto.Common.CommonData", "id": 4}}}, "GuildCancelApplyRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guildId": {"type": "uint64", "id": 2}}}, "GuildCancelApplyResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "GuildAutoJoinRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildAutoJoinResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildDetailInfoDto": {"type": "GuildDetailInfoDto", "id": 2}, "guildFeaturesDto": {"type": "GuildFeaturesDto", "id": 3}, "commonData": {"type": "Proto.Common.CommonData", "id": 4}}}, "GuildGetApplyListRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildGetApplyListResponse": {"fields": {"code": {"type": "int32", "id": 1}, "applyList": {"rule": "repeated", "type": "GuildMemberInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildAgreeJoinRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "userIds": {"rule": "repeated", "type": "uint64", "id": 2}}}, "GuildAgreeJoinResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildMemberInfoDto": {"rule": "repeated", "type": "GuildMemberInfoDto", "id": 2}, "joinOtherGuildUserIds": {"rule": "repeated", "type": "uint64", "id": 3}, "applyCount": {"type": "uint32", "id": 4}, "commonData": {"type": "Proto.Common.CommonData", "id": 5}}}, "GuildRefuseJoinRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "userIds": {"rule": "repeated", "type": "uint64", "id": 2}}}, "GuildRefuseJoinResponse": {"fields": {"code": {"type": "int32", "id": 1}, "applyCount": {"type": "uint32", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildKickOutRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "userId": {"type": "uint64", "id": 2}}}, "GuildKickOutResponse": {"fields": {"code": {"type": "int32", "id": 1}, "members": {"type": "uint32", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildLeaveRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildLeaveResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "GuildUpPositionRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "userId": {"type": "uint64", "id": 2}, "position": {"type": "uint32", "id": 3}}}, "GuildUpPositionResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildMemberInfoDto": {"type": "GuildMemberInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildTransferPresidentRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "userId": {"type": "uint64", "id": 2}}}, "GuildTransferPresidentResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildMemberInfoDtos": {"rule": "repeated", "type": "GuildMemberInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildGetFeaturesInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildGetFeaturesInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guildFeaturesDto": {"type": "GuildFeaturesDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildLevelUpRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildLevelUpResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildUpdateInfo": {"type": "GuildUpdateInfoDto", "id": 3}, "tasks": {"rule": "repeated", "type": "GuildTaskDto", "id": 4}, "dailyShop": {"rule": "repeated", "type": "GuildShopDto", "id": 5}, "weeklyShop": {"rule": "repeated", "type": "GuildShopDto", "id": 6}}}, "GuildSignInRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildSignInResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "signInDto": {"type": "GuilSignInDto", "id": 3}, "userDailyActive": {"type": "uint32", "id": 4}, "userWeeklyActive": {"type": "uint32", "id": 5}, "guildUpdateInfo": {"type": "GuildUpdateInfoDto", "id": 6}, "tasks": {"rule": "repeated", "type": "GuildTaskDto", "id": 7}, "signInRecord": {"type": "string", "id": 8}}}, "GuildShopBuyRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "type": {"type": "uint32", "id": 2}, "shopId": {"type": "uint32", "id": 3}}}, "GuildShopBuyResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildShopDto": {"type": "GuildShopDto", "id": 3}, "tasks": {"rule": "repeated", "type": "GuildTaskDto", "id": 4}}}, "GuildShopRefreshRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "GuildShopRefreshResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "type": {"type": "uint32", "id": 3}, "shopDto": {"rule": "repeated", "type": "GuildShopDto", "id": 4}}}, "GuildTaskRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "taskId": {"type": "uint32", "id": 2}}}, "GuildTaskRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "updateTaskDto": {"type": "GuildTaskDto", "id": 3}, "deleteTaskDtoId": {"type": "uint32", "id": 4}, "tasks": {"rule": "repeated", "type": "GuildTaskDto", "id": 5}, "userDailyActive": {"type": "uint32", "id": 6}, "userWeeklyActive": {"type": "uint32", "id": 7}, "guildUpdateInfo": {"type": "GuildUpdateInfoDto", "id": 8}}}, "GuildTaskRefreshRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "taskId": {"type": "uint32", "id": 2}}}, "GuildTaskRefreshResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildTask": {"type": "GuildTaskDto", "id": 3}, "taskRefreshCount": {"type": "uint32", "id": 4}, "taskRefreshCost": {"type": "uint32", "id": 5}}}, "GuildGetMessageRecordsRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "msgId": {"type": "uint64", "id": 2}, "pageIndex": {"type": "uint32", "id": 3}}}, "GuildGetMessageRecordsResponse": {"fields": {"code": {"type": "int32", "id": 1}, "messageRecords": {"rule": "repeated", "type": "GuildPushMessageDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildDonationReqItemRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "itemId": {"type": "uint32", "id": 2}}}, "GuildDonationReqItemResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildDonationDto": {"type": "GuildDonationDto", "id": 3}}}, "GuildDonationSendItemRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "msgId": {"type": "uint64", "id": 2}}}, "GuildDonationSendItemResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "guildDonationDto": {"type": "GuildDonationDto", "id": 3}}}, "GuildDonationReceiveRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "msgId": {"type": "uint64", "id": 2}}}, "GuildDonationReceiveResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "receiveCount": {"type": "uint32", "id": 3}}}, "GuildDonationGetRecordsRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "msgId": {"type": "uint64", "id": 2}, "pageIndex": {"type": "uint32", "id": 3}}}, "GuildDonationGetRecordsResponse": {"fields": {"code": {"type": "int32", "id": 1}, "records": {"rule": "repeated", "type": "string", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildDonationGetOperationRecordsRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "msgId": {"type": "uint64", "id": 2}, "pageIndex": {"type": "uint32", "id": 3}}}, "GuildDonationGetOperationRecordsResponse": {"fields": {"code": {"type": "int32", "id": 1}, "records": {"rule": "repeated", "type": "string", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "GuildBossBattleRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "GuildBossBattleResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "challengeCnt": {"type": "uint32", "id": 3}, "damage": {"type": "uint64", "id": 4}, "totalDamage": {"type": "uint64", "id": 5}, "seed": {"type": "int32", "id": 6}, "startUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 7}, "endUnits": {"rule": "repeated", "type": "Proto.Common.CombatUnitDto", "id": 8}}}, "GuildBossBattleGRankRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildBossBattleGRankResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "dtos": {"rule": "repeated", "type": "GuildSimpleDto", "id": 3}}}, "GuildTechUpgradeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "GuildTechUpgradeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "lv": {"type": "int32", "id": 3}}}, "GuildInfoDto": {"fields": {"guildId": {"type": "uint64", "id": 1}, "guildName": {"type": "string", "id": 2}, "guildIntro": {"type": "string", "id": 3}, "guildIcon": {"type": "uint64", "id": 4}, "guildIconBg": {"type": "uint64", "id": 5}, "members": {"type": "uint32", "id": 6}, "maxMembers": {"type": "uint32", "id": 7}, "active": {"type": "uint32", "id": 8}, "level": {"type": "uint32", "id": 9}, "applyType": {"type": "uint32", "id": 10}, "applyCondition": {"type": "uint32", "id": 11}, "isApply": {"type": "bool", "id": 12}, "language": {"type": "uint32", "id": 13}, "guildNotice": {"type": "string", "id": 14}, "guildPresidentUserId": {"type": "uint64", "id": 15}, "guildPresidentNickName": {"type": "string", "id": 16}, "exp": {"type": "uint32", "id": 17}, "totalPower": {"type": "uint64", "id": 18}, "imGroupId": {"type": "string", "id": 19}}}, "GuildSimpleDto": {"fields": {"guildId": {"type": "uint64", "id": 1}, "guildName": {"type": "string", "id": 2}, "avatar": {"type": "uint32", "id": 3}, "avatarFrame": {"type": "uint32", "id": 4}, "power": {"type": "uint64", "id": 5}, "damage": {"type": "uint64", "id": 6}, "level": {"type": "uint32", "id": 7}}}, "GuildMemberInfoDto": {"fields": {"userId": {"type": "uint64", "id": 1}, "nickName": {"type": "string", "id": 2}, "avatar": {"type": "uint32", "id": 3}, "avatarFrame": {"type": "uint32", "id": 4}, "level": {"type": "uint32", "id": 5}, "activeTime": {"type": "uint64", "id": 6}, "position": {"type": "uint32", "id": 7}, "chapterId": {"type": "uint32", "id": 8}, "atk": {"type": "uint32", "id": 9}, "hp": {"type": "uint32", "id": 10}, "battlePower": {"type": "uint64", "id": 11}, "applyTime": {"type": "uint64", "id": 12}, "joinTime": {"type": "uint64", "id": 13}, "dailyActive": {"type": "uint32", "id": 14}, "weekActive": {"type": "uint32", "id": 15}, "heroConfigId": {"type": "uint32", "id": 16}, "skinItemConfigId": {"rule": "repeated", "type": "uint32", "id": 17}, "equipIds": {"rule": "repeated", "type": "uint32", "id": 18}, "isOnline": {"type": "bool", "id": 20}}}, "GuildDetailInfoDto": {"fields": {"guildInfoDto": {"type": "GuildInfoDto", "id": 1}, "guildMemberInfoDtos": {"rule": "repeated", "type": "GuildMemberInfoDto", "id": 2}}}, "GuildTaskDto": {"fields": {"taskId": {"type": "uint32", "id": 1}, "progress": {"type": "uint32", "id": 2}, "need": {"type": "uint32", "id": 3}, "rewards": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 4}, "isFinish": {"type": "bool", "id": 5}, "isReceive": {"type": "bool", "id": 6}, "languageId": {"type": "string", "id": 7}}}, "GuildShopDto": {"fields": {"shopId": {"type": "uint32", "id": 1}, "position": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}, "limit": {"type": "uint32", "id": 4}, "needItemId": {"type": "uint32", "id": 5}, "needItemCount": {"type": "uint32", "id": 6}, "rewards": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 7}, "discount": {"type": "uint32", "id": 8}, "freeCnt": {"type": "uint32", "id": 9}}}, "GuilSignInDto": {"fields": {"count": {"type": "uint32", "id": 1}, "limit": {"type": "uint32", "id": 2}, "needItemId": {"type": "uint32", "id": 3}, "needItemCount": {"type": "uint32", "id": 4}, "diamonds": {"type": "uint32", "id": 5}, "rewards": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 6}}}, "GuildFeaturesDto": {"fields": {"tasks": {"rule": "repeated", "type": "GuildTaskDto", "id": 1}, "dailyShop": {"rule": "repeated", "type": "GuildShopDto", "id": 2}, "weeklyShop": {"rule": "repeated", "type": "GuildShopDto", "id": 3}, "signInDto": {"type": "GuilSignInDto", "id": 4}, "dailyRefreshTime": {"type": "uint64", "id": 5}, "weeklyRefreshTime": {"type": "uint64", "id": 6}, "guildBossInfo": {"type": "GuildBossInfoDto", "id": 7}, "taskRefreshCount": {"type": "uint32", "id": 8}, "maxTaskRefreshCount": {"type": "uint32", "id": 9}, "taskRefreshCost": {"type": "uint32", "id": 10}, "signInRecords": {"rule": "repeated", "type": "string", "id": 11}, "donationDto": {"type": "GuildDonationDto", "id": 12}, "applyCount": {"type": "uint32", "id": 13}}}, "GuildUpdateInfoDto": {"fields": {"active": {"type": "uint32", "id": 1}, "exp": {"type": "uint32", "id": 2}, "level": {"type": "uint32", "id": 3}, "maxMembers": {"type": "uint32", "id": 4}}}, "BeKickedOutDto": {"fields": {"guildId": {"type": "uint64", "id": 1}, "guildName": {"type": "string", "id": 2}, "fromUserId": {"type": "uint64", "id": 3}, "fromUserNickName": {"type": "string", "id": 4}, "fromUserPosition": {"type": "uint32", "id": 5}}}, "GuildPushMessageDto": {"fields": {"messageType": {"type": "uint32", "id": 1}, "messageContent": {"type": "string", "id": 2}}}, "GuildBossInfoDto": {"fields": {"challengeCnt": {"type": "uint32", "id": 1}, "buyCntByDiamonds": {"type": "uint32", "id": 2}, "maxBuyCntByDiamonds": {"type": "uint32", "id": 3}, "buyCntCostByDiamonds": {"type": "uint32", "id": 4}, "buyCntByCoins": {"type": "uint32", "id": 5}, "maxBuyCntByCoins": {"type": "uint32", "id": 6}, "buyCntCostByCoins": {"type": "uint32", "id": 7}, "challengeCntRecoverySeconds": {"type": "uint32", "id": 8}, "challengeCntRecoveryPerTime": {"type": "uint32", "id": 9}, "nextChallengeCntRecoveryTime": {"type": "uint64", "id": 10}, "maxRecoveryCnt": {"type": "uint32", "id": 11}, "totalPersonalDamage": {"type": "uint64", "id": 12}, "totalGuildDamage": {"type": "uint64", "id": 13}, "killBossCnt": {"type": "uint32", "id": 14}, "bossRefreshTimestamp": {"type": "uint64", "id": 15}, "dailyRefreshTimestamp": {"type": "uint64", "id": 16}, "bossConfig": {"rule": "repeated", "type": "GuildBossConfigDto", "id": 17}, "bossTask": {"rule": "repeated", "type": "GuildBossTaskDto", "id": 18}, "killBox": {"rule": "repeated", "type": "GuildBossKillBoxDto", "id": 19}, "challengeRecords": {"rule": "repeated", "type": "string", "id": 20}}}, "GuildBossConfigDto": {"fields": {"bossId": {"type": "uint32", "id": 1}, "maxHp": {"type": "uint64", "id": 2}, "nowHp": {"type": "uint64", "id": 3}}}, "GuildBossTaskDto": {"fields": {"taskId": {"type": "uint32", "id": 1}, "need": {"type": "uint64", "id": 2}, "progress": {"type": "uint64", "id": 3}, "rewards": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 4}, "isFinish": {"type": "bool", "id": 5}, "isReceive": {"type": "bool", "id": 6}, "languageId": {"type": "uint32", "id": 7}}}, "GuildBossKillBoxDto": {"fields": {"boxId": {"type": "uint32", "id": 1}, "need": {"type": "uint32", "id": 2}, "progress": {"type": "uint32", "id": 3}, "rewards": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 4}, "isFinish": {"type": "bool", "id": 5}, "isReceive": {"type": "bool", "id": 6}}}, "GuildBossRankDto": {"fields": {"guildMemberInfo": {"type": "GuildMemberInfoDto", "id": 1}, "damage": {"type": "uint64", "id": 2}, "rank": {"type": "uint32", "id": 3}}}, "GuildDonationDto": {"fields": {"requestItemTimestamp": {"type": "uint64", "id": 1}, "donationItemCount": {"type": "uint32", "id": 2}, "donationMaxItemCount": {"type": "uint32", "id": 3}}}}}, "Hero": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "HeroProto"}, "nested": {"HeroUpgradeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroRowId": {"type": "uint64", "id": 2}, "count": {"type": "uint32", "id": 3}}}, "HeroUpgradeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroAdvanceRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroRowId": {"type": "uint64", "id": 2}}}, "HeroAdvanceResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroStarRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroRowId": {"type": "uint64", "id": 2}}}, "HeroStarResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroResetRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroRowId": {"type": "uint64", "id": 2}}}, "HeroResetResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroBookScoreRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroRowId": {"type": "uint64", "id": 2}}}, "HeroBookScoreResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroBookRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rewardId": {"type": "int32", "id": 2}}}, "HeroBookRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroReplaceSkinRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroRowId": {"type": "uint64", "id": 2}, "skinConfigId": {"type": "uint32", "id": 3}}}, "HeroReplaceSkinResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroBondLevelUpRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "heroBondConfigId": {"type": "int32", "id": 2}, "heroRowIds": {"rule": "repeated", "type": "uint64", "id": 3}}}, "HeroBondLevelUpResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "HeroLosslessRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "fType": {"type": "int32", "id": 2}, "fIndex": {"type": "int32", "id": 3}, "heroRowId": {"type": "int64", "id": 4}}}, "HeroLosslessResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}}}, "Item": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "ItemProto"}, "nested": {"ItemUseRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "rowId": {"type": "uint64", "id": 2}, "count": {"type": "uint32", "id": 3}, "index": {"type": "uint32", "id": 4}}}, "ItemUseResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}}}, "Mission": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "MissionProto"}, "nested": {"MissionGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "MissionGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "mainMission": {"type": "MainMission", "id": 2}}}, "MissionStartRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "startDto": {"type": "MissionStartDto", "id": 2}}}, "MissionStartResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "MissionEndRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "endDto": {"type": "MissionEndDto", "id": 2}}}, "MissionEndResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "mainMission": {"type": "MainMission", "id": 3}}}, "MissionGetHangUpItemsRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "MissionGetHangUpItemsResponse": {"fields": {"code": {"type": "int32", "id": 1}, "reward": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 2}}}, "MissionReceiveHangUpItemsRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "MissionReceiveHangUpItemsResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "MissionQuickHangUpRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "MissionQuickHangUpResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "MissionStartDto": {"fields": {"missionType": {"type": "MissionType", "id": 1}, "missionId": {"type": "uint32", "id": 2}}}, "MissionEndDto": {"fields": {"missionType": {"type": "MissionType", "id": 1}, "missionId": {"type": "uint32", "id": 2}, "startTransId": {"type": "uint64", "id": 3}, "isWin": {"type": "bool", "id": 4}, "rewards": {"rule": "repeated", "type": "Proto.Common.Reward<PERSON>to", "id": 5}, "extar": {"type": "string", "id": 6}, "damage": {"type": "uint64", "id": 7}}}, "MainMission": {"fields": {"missionId": {"type": "uint32", "id": 1}, "wave": {"type": "int32", "id": 2}, "claimedRewardId": {"type": "int32", "id": 3}, "enterCount": {"type": "int32", "id": 4}}}, "MissionType": {"values": {"MAIN": 0}}}}, "Pay": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "PayProto"}, "nested": {"PayInAppPurchaseRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "channelId": {"type": "uint32", "id": 2}, "purchaseId": {"type": "uint32", "id": 3}, "receiptData": {"type": "string", "id": 4}, "extraInfo": {"type": "string", "id": 5}, "preOrderId": {"type": "uint64", "id": 6}, "currency": {"type": "string", "id": 7}}}, "PayInAppPurchaseResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "IAPTransID": {"type": "string", "id": 3}, "rechargeId": {"type": "uint32", "id": 4}, "rechargeIds": {"keyType": "uint32", "type": "uint32", "id": 5}, "iapInfo": {"type": "Proto.Common.IAPDto", "id": 6}}}, "PayPreOrderRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "purchaseId": {"type": "uint32", "id": 2}, "extraInfo": {"type": "string", "id": 3}, "preOrderId": {"type": "uint64", "id": 4}, "channelId": {"type": "uint32", "id": 5}, "androidPackageName": {"type": "string", "id": 6}, "kuaiShouChannelID": {"type": "string", "id": 7}, "yybType": {"type": "uint32", "id": 8}, "yybOpenId": {"type": "string", "id": 9}, "yybOpenkey": {"type": "string", "id": 10}, "yybPf": {"type": "string", "id": 11}, "yybPfKey": {"type": "string", "id": 12}, "douyinRiskControlInfo": {"type": "string", "id": 13}}}, "PayPreOrderResponse": {"fields": {"code": {"type": "int32", "id": 1}, "preOrderId": {"type": "uint64", "id": 2}, "notifyUrl": {"type": "string", "id": 3}, "weChatOrderDto": {"type": "WeChatOrderDto", "id": 4}, "aliOrderData": {"type": "string", "id": 5}, "ucOrderDto": {"type": "UcOrderDto", "id": 6}, "biliOrderDto": {"type": "BiliOrderDto", "id": 7}, "passBackParams": {"type": "string", "id": 8}, "kusiShouOrderDto": {"type": "KuaiShouOrderDto", "id": 9}, "cpOrderId": {"type": "string", "id": 10}, "amount": {"type": "uint32", "id": 11}, "vivoOrderDto": {"type": "VivoOrderDto", "id": 12}, "yybOrderDto": {"type": "YybOrderDto", "id": 13}, "douYinOrderDto": {"type": "DouYinOrderDto", "id": 14}, "weChatMiniGameOrderDto": {"type": "WeChatMiniGameOrderDto", "id": 15}, "commonData": {"type": "Proto.Common.CommonData", "id": 16}}}, "PayOnUnityRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "purchaseId": {"type": "uint32", "id": 2}, "extraInfo": {"type": "string", "id": 3}}}, "PayOnUnityResponse": {"fields": {"code": {"type": "int32", "id": 1}, "payInAppPurchaseResponse": {"type": "PayInAppPurchaseResponse", "id": 2}}}, "WeChatOrderDto": {"fields": {"prePayId": {"type": "string", "id": 1}, "nonceStr": {"type": "string", "id": 2}, "timestamp": {"type": "uint64", "id": 3}, "sign": {"type": "string", "id": 4}}}, "UcOrderDto": {"fields": {"callbackInfo": {"type": "string", "id": 1}, "amount": {"type": "string", "id": 2}, "notifyUrl": {"type": "string", "id": 3}, "cpOrderId": {"type": "string", "id": 4}, "accountId": {"type": "string", "id": 5}, "signType": {"type": "string", "id": 6}, "sign": {"type": "string", "id": 7}}}, "BiliOrderDto": {"fields": {"uid": {"type": "string", "id": 1}, "role": {"type": "string", "id": 2}, "notifyUrl": {"type": "string", "id": 3}, "outTradeNo": {"type": "string", "id": 4}, "totalFee": {"type": "uint32", "id": 5}, "extensionInfo": {"type": "string", "id": 6}, "orderSign": {"type": "string", "id": 7}, "gameMoney": {"type": "uint32", "id": 8}, "subject": {"type": "string", "id": 9}, "body": {"type": "string", "id": 10}}}, "KuaiShouOrderDto": {"fields": {"channelId": {"type": "string", "id": 1}, "userIp": {"type": "string", "id": 2}, "appId": {"type": "string", "id": 3}, "productId": {"type": "string", "id": 4}, "productName": {"type": "string", "id": 5}, "productDesc": {"type": "string", "id": 6}, "productNum": {"type": "uint32", "id": 7}, "price": {"type": "uint32", "id": 8}, "serverId": {"type": "string", "id": 9}, "serverName": {"type": "string", "id": 10}, "roleId": {"type": "string", "id": 11}, "roleName": {"type": "string", "id": 12}, "roleLevel": {"type": "string", "id": 13}, "orderId": {"type": "string", "id": 14}, "payNotifyUrl": {"type": "string", "id": 15}, "extension": {"type": "string", "id": 16}, "coinName": {"type": "string", "id": 17}, "sign": {"type": "string", "id": 18}}}, "VivoOrderDto": {"fields": {"appId": {"type": "string", "id": 1}, "cpOrderNumber": {"type": "string", "id": 2}, "productName": {"type": "string", "id": 3}, "productDesc": {"type": "string", "id": 4}, "orderAmount": {"type": "string", "id": 5}, "vivoSignature": {"type": "string", "id": 6}, "extuid": {"type": "string", "id": 7}, "notifyUrl": {"type": "string", "id": 8}, "expireTime": {"type": "string", "id": 9}, "level": {"type": "string", "id": 10}, "vip": {"type": "string", "id": 11}, "balance": {"type": "string", "id": 12}, "party": {"type": "string", "id": 13}, "roleId": {"type": "string", "id": 14}, "roleName": {"type": "string", "id": 15}, "serverName": {"type": "string", "id": 16}, "extInfo": {"type": "string", "id": 17}}}, "YybOrderDto": {"fields": {"zoneId": {"type": "string", "id": 1}, "goodsTokenUrl": {"type": "string", "id": 2}}}, "DouYinOrderDto": {"fields": {"code": {"type": "int32", "id": 1}, "sdkParam": {"type": "string", "id": 2}, "message": {"type": "string", "id": 3}}}, "WeChatMiniGameOrderDto": {"fields": {"signData": {"type": "string", "id": 1}, "paySig": {"type": "string", "id": 2}, "signature": {"type": "string", "id": 3}}}}}, "ServerList": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "ServerListProto"}, "nested": {"UserGetLastLoginRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "UserGetLastLoginResponse": {"fields": {"code": {"type": "int32", "id": 1}, "roleList": {"rule": "repeated", "type": "RoleDetailDto", "id": 2}, "serverList": {"keyType": "uint32", "type": "ZoneInfoDto", "id": 3}, "commonData": {"type": "Proto.Common.CommonData", "id": 4}}}, "FindServerListRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "groupId": {"type": "uint32", "id": 2}}}, "FindServerListResponse": {"fields": {"code": {"type": "int32", "id": 1}, "serverInfoDto": {"type": "ZoneInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "RoleDetailDto": {"fields": {"nickName": {"type": "string", "id": 1}, "groupId": {"type": "uint32", "id": 2}, "avatar": {"type": "uint32", "id": 3}, "avatarFrame": {"type": "uint32", "id": 4}, "serverId": {"type": "uint32", "id": 5}, "power": {"type": "uint64", "id": 6}, "lastLoginPass": {"type": "uint64", "id": 7}, "userId": {"type": "uint64", "id": 8}}}, "ZoneInfoDto": {"fields": {"maxServer": {"type": "uint32", "id": 1}, "serverList": {"keyType": "uint32", "type": "ServerGroupDto", "id": 2}}}, "ServerGroupDto": {"fields": {"group": {"type": "uint32", "id": 1}, "startServer": {"type": "uint32", "id": 2}, "endServer": {"type": "uint32", "id": 3}, "serverInfoDto": {"rule": "repeated", "type": "ServerInfoDto", "id": 4}}}, "ServerInfoDto": {"fields": {"serverId": {"type": "uint32", "id": 1}, "status": {"type": "uint64", "id": 2}}}}}, "Task": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "TaskProto"}, "nested": {"TaskGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "TaskGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "tasks": {"type": "Tasks", "id": 2}, "dailyTaskActive": {"type": "uint32", "id": 3}, "weeklyTaskActive": {"type": "uint32", "id": 4}, "dailyTaskResetTime": {"type": "uint64", "id": 5}, "dailyTaskRewardLog": {"type": "uint64", "id": 6}, "weeklyTaskRewardLog": {"type": "uint64", "id": 7}}}, "TaskRewardDailyRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "TaskRewardDailyResponse": {"fields": {"code": {"type": "int32", "id": 1}, "activeDaily": {"type": "uint32", "id": 2}, "activeWeekly": {"type": "uint32", "id": 3}, "commonData": {"type": "Proto.Common.CommonData", "id": 4}, "updateTaskDto": {"type": "Proto.Common.TaskDto", "id": 5}, "tasks": {"type": "Tasks", "id": 6}}}, "TaskRewardAchieveRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "TaskRewardAchieveResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "updateTaskDto": {"type": "Proto.Common.TaskDto", "id": 3}, "deleteTaskDtoId": {"type": "uint32", "id": 4}, "tasks": {"type": "Tasks", "id": 5}}}, "TaskActiveRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "type": {"type": "uint32", "id": 2}, "id": {"type": "uint32", "id": 3}}}, "TaskActiveRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "type": {"type": "uint32", "id": 2}, "rewardLog": {"type": "uint64", "id": 3}, "commonData": {"type": "Proto.Common.CommonData", "id": 4}, "tasks": {"type": "Tasks", "id": 5}}}, "TaskActiveRewardAllRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "TaskActiveRewardAllResponse": {"fields": {"code": {"type": "int32", "id": 1}, "type": {"type": "uint32", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}, "tasks": {"type": "Tasks", "id": 4}, "rewardLog": {"type": "uint64", "id": 5}}}, "Tasks": {"fields": {"dailyTask": {"rule": "repeated", "type": "Proto.Common.TaskDto", "id": 1}, "achievements": {"rule": "repeated", "type": "Proto.Common.TaskDto", "id": 2}}}}}, "User": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "UserProto"}, "nested": {"UserLoginRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "channelId": {"type": "uint32", "id": 2}, "accountId2": {"type": "string", "id": 3}, "verification": {"type": "string", "id": 4}}}, "UserLoginResponse": {"fields": {"code": {"type": "int32", "id": 1}, "accessToken": {"type": "string", "id": 2}, "timestamp": {"type": "uint64", "id": 3}, "userId": {"type": "uint64", "id": 4}, "transId": {"type": "uint64", "id": 5}, "userCurrency": {"type": "Proto.Common.UserCurrency", "id": 6}, "systemMask": {"type": "string", "id": 7}, "userLevel": {"type": "Proto.Common.UserLevel", "id": 8}, "guideMask": {"type": "uint64", "id": 9}, "registerTimestamp": {"type": "uint64", "id": 10}, "items": {"rule": "repeated", "type": "Proto.Common.ItemDto", "id": 11}, "equipments": {"rule": "repeated", "type": "Proto.Common.EquipmentDto", "id": 12}, "userInfoDto": {"type": "Proto.Common.UserInfoDto", "id": 13}, "heros": {"rule": "repeated", "type": "Proto.Common.HeroDto", "id": 14}, "userVipLevel": {"type": "Proto.Common.UserVipLevel", "id": 15}, "openModelIdList": {"rule": "repeated", "type": "uint32", "id": 16}, "chapterRecordInfo": {"type": "string", "id": 17}, "timeZone": {"type": "string", "id": 18}, "serverId": {"type": "uint32", "id": 19}, "openServerTime": {"type": "uint64", "id": 20}, "openServerResetTime": {"type": "uint64", "id": 21}, "ServerIMGroupId": {"type": "string", "id": 22}, "guildServerIMGroupId": {"type": "string", "id": 23}, "crossServerIMGroupId": {"type": "string", "id": 24}, "globalIMGroupId": {"type": "string", "id": 25}, "mainMission": {"type": "Proto.Mission.MainMission", "id": 26}, "adInfo": {"rule": "repeated", "type": "Proto.Common.AdInfoDto", "id": 27}}}, "UserGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "UserGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "userCurrency": {"type": "Proto.Common.UserCurrency", "id": 3}, "userLevel": {"type": "Proto.Common.UserLevel", "id": 4}, "mainMission": {"type": "Proto.Mission.MainMission", "id": 5}, "items": {"rule": "repeated", "type": "Proto.Common.ItemDto", "id": 6}, "equipments": {"rule": "repeated", "type": "Proto.Common.EquipmentDto", "id": 7}, "transId": {"type": "uint64", "id": 8}}}, "UserHeartbeatRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "UserHeartbeatResponse": {"fields": {"code": {"type": "int32", "id": 1}, "serverTimestamp": {"type": "uint64", "id": 2}}}, "UserUpdateSystemMaskRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "position": {"type": "uint32", "id": 2}, "value": {"type": "uint32", "id": 3}}}, "UserUpdateSystemMaskResponse": {"fields": {"code": {"type": "int32", "id": 1}, "systemMask": {"type": "string", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "UserUpdateGuideMaskRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "guideMask": {"type": "uint64", "id": 2}}}, "UserUpdateGuideMaskResponse": {"fields": {"code": {"type": "int32", "id": 1}, "guideMask": {"type": "uint64", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "UserCancelAccountRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "token": {"type": "string", "id": 2}}}, "UserCancelAccountResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "UserUpdateInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "nickName": {"type": "string", "id": 2}, "avatar": {"type": "uint32", "id": 3}, "avatarFrame": {"type": "uint32", "id": 4}}}, "UserUpdateInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "userInfoDto": {"type": "Proto.Common.UserInfoDto", "id": 3}}}, "UserGetOtherPlayerInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "otherUserIds": {"rule": "repeated", "type": "uint64", "id": 2}}}, "UserGetOtherPlayerInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "playerInfos": {"rule": "repeated", "type": "PlayerInfoDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "UserGetBattleReportRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "reportId": {"type": "uint64", "id": 2}}}, "UserGetBattleReportResponse": {"fields": {"code": {"type": "int32", "id": 1}, "record": {"type": "Proto.Common.PVPRecordDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "PlayerInfoDto": {"fields": {"nickName": {"type": "string", "id": 1}, "userId": {"type": "uint64", "id": 2}, "avatar": {"type": "uint32", "id": 3}, "avatarFrame": {"type": "uint32", "id": 4}, "lastLoginTimestamp": {"type": "uint64", "id": 5}, "guildName": {"type": "string", "id": 6}, "guildId": {"type": "string", "id": 7}, "chapterId": {"type": "int32", "id": 8}, "waveIndex": {"type": "int32", "id": 9}, "power": {"type": "int32", "id": 10}, "guildIcon": {"type": "int32", "id": 15}, "guildIconBg": {"type": "int32", "id": 16}}}, "UserOpenModelRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "modelIds": {"rule": "repeated", "type": "uint32", "id": 2}}}, "UserOpenModelResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "UserSetFormationByTypeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "formationType": {"type": "uint32", "id": 2}, "formation": {"type": "Proto.Common.LongArray", "id": 3}}}, "UserSetFormationByTypeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "UserGetFormationByTypeRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "formationType": {"type": "uint32", "id": 2}}}, "UserGetFormationByTypeResponse": {"fields": {"code": {"type": "int32", "id": 1}, "formationType": {"type": "uint32", "id": 2}, "formation": {"type": "Proto.Common.LongArray", "id": 3}, "commonData": {"type": "Proto.Common.CommonData", "id": 4}}}}}}}, "IM": {"nested": {"Guild": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "IMProto"}, "nested": {"IMLoginRequest": {"fields": {"accessToken": {"type": "string", "id": 1}}}, "IMLoginResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMJoinGroupRequest": {"fields": {"groupId": {"rule": "repeated", "type": "string", "id": 1}}}, "IMJoinGroupResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMQuitGroupRequest": {"fields": {"groupId": {"rule": "repeated", "type": "string", "id": 1}}}, "IMQuitGroupResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMHeartBeatRequest": {"fields": {}}, "IMHeartBeatResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMGroupChatRequest": {"fields": {"groupId": {"type": "string", "id": 1}, "content": {"type": "string", "id": 2}, "language": {"type": "string", "id": 3}}}, "IMGroupChatResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMPrivateChatRequest": {"fields": {"targetId": {"type": "string", "id": 1}, "content": {"type": "string", "id": 2}, "language": {"type": "string", "id": 3}}}, "IMPrivateChatResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMGroupMessageRecordRequest": {"fields": {"groupId": {"type": "string", "id": 1}, "msgSeq": {"type": "uint64", "id": 2}, "msgCount": {"type": "uint32", "id": 3}}}, "IMGroupMessageRecordResponse": {"fields": {"code": {"type": "int32", "id": 1}, "messageRecord": {"rule": "repeated", "type": "IMMessageRecord", "id": 2}}}, "IMPrivateListRequest": {"fields": {"limit": {"type": "uint32", "id": 1}, "lastEvaluatedKey": {"type": "string", "id": 2}}}, "IMPrivateListResponse": {"fields": {"code": {"type": "int32", "id": 1}, "privateUser": {"rule": "repeated", "type": "IMPrivateUser", "id": 2}, "lastEvaluatedKey": {"type": "string", "id": 3}}}, "IMPrivateChatRecordRequest": {"fields": {"targetId": {"type": "string", "id": 1}, "msgSeq": {"type": "uint64", "id": 2}, "msgCount": {"type": "uint32", "id": 3}}}, "IMPrivateChatRecordResponse": {"fields": {"code": {"type": "int32", "id": 1}, "messageRecord": {"rule": "repeated", "type": "IMMessageRecord", "id": 2}}}, "IMGetBlackListRequest": {"fields": {}}, "IMGetBlackListResponse": {"fields": {"code": {"type": "int32", "id": 1}, "blackUser": {"rule": "repeated", "type": "IMBlackUser", "id": 2}}}, "IMAddToBlackListRequest": {"fields": {"targetId": {"rule": "repeated", "type": "string", "id": 1}}}, "IMAddToBlackListResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMRemoveFromBlackListRequest": {"fields": {"targetId": {"rule": "repeated", "type": "string", "id": 1}}}, "IMRemoveFromBlackListResponse": {"fields": {"code": {"type": "int32", "id": 1}}}, "IMChatTextTranslateRequest": {"fields": {"groupId": {"type": "string", "id": 1}, "privateTargetId": {"type": "string", "id": 2}, "msgSeq": {"type": "uint64", "id": 3}, "targetLanguage": {"type": "string", "id": 4}}}, "IMChatTextTranslateResponse": {"fields": {"code": {"type": "int32", "id": 1}, "translateMsg": {"type": "string", "id": 2}}}, "IMLoginRepeatMessage": {"fields": {}}, "IMReconnectMessage": {"fields": {}}, "IMPushMessage": {"fields": {"messageType": {"type": "uint32", "id": 1}, "messageContent": {"type": "string", "id": 2}}}, "IMErrorMessage": {"fields": {"code": {"type": "int32", "id": 1}, "msg": {"type": "string", "id": 2}}}, "IMMessageContent": {"fields": {"content": {"type": "string", "id": 1}, "messageType": {"type": "uint32", "id": 2}, "chatText": {"type": "string", "id": 3}, "customData": {"type": "string", "id": 4}}}, "IMMessageRecord": {"fields": {"messageContent": {"type": "IMMessageContent", "id": 1}, "msgSender": {"type": "string", "id": 2}, "msgSeq": {"type": "uint64", "id": 3}, "createTime": {"type": "uint64", "id": 4}}}, "IMPrivateUser": {"fields": {"msgSeq": {"type": "uint64", "id": 1}, "lastMsg": {"type": "string", "id": 2}, "lastMsgTime": {"type": "uint64", "id": 3}, "senderId": {"type": "string", "id": 4}, "targetId": {"type": "string", "id": 5}, "userInfo": {"type": "string", "id": 6}, "onlineTime": {"type": "uint64", "id": 7}}}, "IMBlackUser": {"fields": {"userInfo": {"type": "string", "id": 1}, "addTimestamp": {"type": "uint64", "id": 2}}}, "MessageType": {"values": {"DEFAULT": 0, "GUILD_JOIN_SUCCESS": 101, "GUILD_USER_JOIN": 102, "GUILD_USER_LEAVE": 103, "GUILD_POSITION_CHANGE": 104, "GUILD_BE_KICKED_OUT": 105, "GUILD_USER_BE_KICKED_OUT": 106, "GUILD_INFO_MODIFY": 107, "GUILD_DONATION": 108, "GUILD_DONATION_CHANGE_ITEM_COUNT": 109, "GUILD_DONATION_DELETE": 110, "GUILD_APPLY_JOIN": 111, "GUILD_TRANSFER_PRESIDENT": 113, "GUILD_CHECK_TRANSFER_PRESIDENT": 114, "CHAT_GUILD": 201, "CHAT_WORLD_SERVER": 202, "CHAT_PRIVATE": 203, "CHAT_CROSS_SERVER": 204, "CHAT_GLOBAL": 205, "CHAT_SHOW_ITEM": 206, "SYSTEM_PUSH": 9527}}}}}}, "Shop": {"nested": {"Arena": {"options": {"java_package": "com.dxx.game.dto", "java_outer_classname": "ShopProto"}, "nested": {"ShopGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "ShopGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "integralShops": {"rule": "repeated", "type": "IntegralShopDto", "id": 3}, "refreshTime": {"type": "uint64", "id": 4}, "rechargeIds": {"keyType": "uint32", "type": "uint32", "id": 5}, "shopGachaDataDtos": {"rule": "repeated", "type": "ShopGachaDataDto", "id": 6}, "wishData": {"keyType": "uint32", "type": "Proto.Common.Uint32List", "id": 7}, "iapInfo": {"type": "Proto.Common.IAPDto", "id": 8}}}, "ShopDoGachaRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "gachaId": {"type": "uint32", "id": 2}, "costType": {"type": "uint32", "id": 3}, "gachaType": {"type": "uint32", "id": 4}}}, "ShopDoGachaResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "gachaCount": {"type": "uint32", "id": 3}, "gachaFreeCount": {"type": "uint32", "id": 4}}}, "ShopIntegralGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "ShopIntegralGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "integralShops": {"rule": "repeated", "type": "IntegralShopDto", "id": 3}}}, "ShopIntegralRefreshRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "shopConfigId": {"type": "int32", "id": 2}}}, "ShopIntegralRefreshResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "integralShop": {"type": "IntegralShopDto", "id": 3}}}, "ShopIntegralBuyItemRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "shopConfigId": {"type": "int32", "id": 2}, "goodsConfigId": {"type": "int32", "id": 3}}}, "ShopIntegralBuyItemResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "shopConfigId": {"type": "int32", "id": 3}, "goodsConfigId": {"type": "int32", "id": 4}}}, "ShopGacheWishRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "gachaId": {"type": "uint32", "id": 2}, "heroIds": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ShopGacheWishResponse": {"fields": {"code": {"type": "int32", "id": 1}, "wishData": {"keyType": "uint32", "type": "Proto.Common.Uint32List", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "ShopFreeIAPItemRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "purchaseId": {"type": "uint32", "id": 2}, "extraInfo": {"type": "string", "id": 3}, "extraType": {"type": "uint32", "id": 4}}}, "ShopFreeIAPItemResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "rechargeIds": {"keyType": "uint32", "type": "uint32", "id": 3}, "iapInfo": {"type": "Proto.Common.IAPDto", "id": 4}}}, "BattlePassGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "BattlePassGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "battlePass": {"type": "Proto.Common.IAPBattlePassDto", "id": 2}}}, "BattlePassRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "battlePassRewardIdList": {"rule": "repeated", "type": "uint32", "id": 2}}}, "BattlePassRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "battlePassDto": {"type": "Proto.Common.IAPBattlePassDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "BattlePassChangeScoreRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "addScore": {"type": "uint32", "id": 2}}}, "BattlePassChangeScoreResponse": {"fields": {"code": {"type": "int32", "id": 1}, "battlePassDto": {"type": "Proto.Common.IAPBattlePassDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "BattlePassFinalRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "BattlePassFinalRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "battlePassDto": {"type": "Proto.Common.IAPBattlePassDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "MonthCardGetRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "monthCardId": {"type": "uint32", "id": 2}}}, "MonthCardGetRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "iapInfo": {"type": "Proto.Common.IAPDto", "id": 2}, "commonData": {"type": "Proto.Common.CommonData", "id": 3}}}, "VIPLevelRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "level": {"type": "uint32", "id": 2}}}, "VIPLevelRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}}}, "LevelFundGetInfoRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "LevelFundGetInfoResponse": {"fields": {"code": {"type": "int32", "id": 1}, "levelFund": {"type": "Proto.Common.LevelFundDto", "id": 2}}}, "LevelFundRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}, "levelFundRewardId": {"type": "int32", "id": 2}}}, "LevelFundRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "levelFund": {"type": "Proto.Common.LevelFundDto", "id": 3}}}, "FirstRechargeRewardRequest": {"fields": {"commonParams": {"type": "Proto.Common.CommonParams", "id": 1}}}, "FirstRechargeRewardResponse": {"fields": {"code": {"type": "int32", "id": 1}, "commonData": {"type": "Proto.Common.CommonData", "id": 2}, "firstRechargeReward": {"type": "bool", "id": 3}, "totalRecharge": {"type": "uint32", "id": 4}}}, "ShopGachaDataDto": {"fields": {"id": {"type": "uint32", "id": 1}, "openType": {"type": "uint32", "id": 2}, "openTimestamp": {"type": "uint64", "id": 3}, "endTimestamp": {"type": "uint64", "id": 4}, "gachaCount": {"type": "uint32", "id": 5}, "gachaFreeCount": {"type": "uint32", "id": 6}, "refreshTimestamp": {"type": "uint64", "id": 7}}}, "IntegralShopItemDto": {"fields": {"configId": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "reward": {"type": "Proto.Common.Reward<PERSON>to", "id": 3}, "priceType": {"type": "uint32", "id": 4}, "price": {"type": "uint32", "id": 5}, "discount": {"type": "uint32", "id": 6}, "sort": {"type": "uint32", "id": 7}}}, "IntegralShopDto": {"fields": {"shopConfigId": {"type": "uint32", "id": 1}, "goodsConfigId": {"rule": "repeated", "type": "uint32", "id": 2}, "buyConfigId": {"rule": "repeated", "type": "uint32", "id": 3}, "round": {"type": "uint32", "id": 4}, "resetTimestamp": {"type": "uint64", "id": 5}, "maxRound": {"type": "uint32", "id": 6}}}}}}}}}
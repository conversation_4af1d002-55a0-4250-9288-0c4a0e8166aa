var express = require('express');
var path = require('path');
var favicon = require('serve-favicon');
var session = require('express-session');
var cookieParser = require('cookie-parser');

//初始化express
var viewsPath = path.join(__dirname, '../views');
var publicPath = path.join(__dirname, '../public');
var faviconPath = path.join(__dirname, '../public/favicon.ico');
var server = express();

server.set('views', viewsPath);
server.set('view engine', 'ejs');
server.use(favicon(faviconPath));
server.use(express.static(publicPath));
server.use(cookieParser());

server.use(express.json({ limit: '5mb' }));

// 对于 URL 编码的请求体
server.use(express.urlencoded({ limit: '5mb', extended: false }));

server.use(session({
	secret: 'dxx_game',
	cookie: { maxAge: 24 * 60 * 60 * 1000 },
	resave: true,
	saveUninitialized: true
}));

//记录访问IP
// server.all('*', function(req, res, next) {
//     var url = req.url;
//     console.log('IP：' + req.connection.remoteAddress + ' url:  ' + url);
//     next();
// });
var checkKey = "1fa387ab7880a717";
var allowIp = ["127.0.0.1", "************",  "************", "*************", "*************", "************", "*************", "*************", "*************", "**************", "**************", "**************"];

//记录访问IP
server.all('*', function(req, res, next) {

	var queryKey = req.query.key;
	if (queryKey) {
		req.session.queryKey = queryKey;
	} else {
		queryKey = req.session.queryKey;
	}

	// 健康检测不处理
	if (req.url.indexOf("health_check") >= 0 || queryKey === checkKey) {
		next();
		return;
	}


	var ip = req.headers["x-forwarded-for"];
	if (!ip) {
		ip = req.ip;
	}

	ip = ip.split(",")[0];


	if (allowIp.indexOf(ip) < 0) {
		var allow = false;
		if (ip.indexOf("103.158.82.") >= 0 || ip.indexOf("148.153.97") >= 0) {
			allow = true;
		}
		// for (var i = 0; i< allowIp.length; i ++) {
		// 	if (ip.indexOf("103.158.") >= 0 || ip.indexOf("103.251.237") >= 0 || ip.indexOf("103.134.") >= 0 || ip.indexOf("103.104.171") >= 0) {
		// 		allow = true;
		// 		break;
		// 	}
		// }
		if (allow) {
			next();
		} else {
			res.status(404).send("404");
		}
	} else {
		next();
	}



	//    next();
});

// 模块注册
var api = require('../routes/api.js');
server.use('/', api);


// 监听端口
server.listen(10013, '0.0.0.0');
console.log('server listening front on 127.0.0.1:10013');

module.exports = server;















package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.modules.activity.service.SignInService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;
import com.dxx.game.dto.SignInProto.*;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:01
 */
@ApiHandler
public class SignInHandler {
    @Autowired
    private SignInService signInService;

    @ApiMethod(command = MsgReqCommand.SignInGetInfoRequest, name = "签到-获取数据")
    public Result<SignInGetInfoResponse> getInfo(Message msg) {
        SignInGetInfoRequest params = (SignInGetInfoRequest)msg;
        return signInService.getInfoAction(params);
    }

    @ApiMethod(command = MsgReqCommand.SignInDoSignRequest, name = "签到-领取签到奖励")
    public Result<SignInDoSignResponse> doSign(Message msg) {
        SignInDoSignRequest params = (SignInDoSignRequest)msg;
        return signInService.doSignAction(params);
    }
}

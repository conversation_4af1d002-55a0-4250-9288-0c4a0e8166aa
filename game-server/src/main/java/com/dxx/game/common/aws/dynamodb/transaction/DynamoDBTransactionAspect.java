package com.dxx.game.common.aws.dynamodb.transaction;

import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.capacity.ConsumedCapacityInfo;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBatchWriteData;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.aws.kinesis.KinesisService;
import com.dxx.game.common.aws.kinesis.KinesiseCacheManager;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisLockContext;
import com.dxx.game.common.redis.RedisPublishMessageContext;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.consts.ServerType;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ServerContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.statistics.StatisticsContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteResult;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.WriteBatch;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.*;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/10/16 14:54
 */
@Slf4j
@Aspect
@Component
public class DynamoDBTransactionAspect {



    @Resource
    protected DynamoDbEnhancedClient dynamoDbEnhancedClient;
    @Resource
    protected DynamoDbClient dynamoDbClient;
    @Resource
    private RedisService redisService;
    @Resource
    private RedisLock redisLock;

    @Pointcut(value = "@annotation(com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional)")
    public void pointCut() {
    }

    @Before(value = "pointCut() && @annotation(dynamoDBTransactional)")
    public void doBefore(JoinPoint joinPoint, DynamoDBTransactional dynamoDBTransactional) {
        if (!DynamoDBTransactionAspectSupport.isOpen()) {
            // 记录事务是否开启
            DynamoDBTransactionAspectSupport.setLocalTransaction(joinPoint.getSignature());
        }
    }

    @SuppressWarnings("unchecked")
    @AfterReturning(returning = "result", value = "pointCut() && @annotation(dynamoDBTransactional)")
    public void doAfterReturning(JoinPoint joinPoint, Object result, DynamoDBTransactional dynamoDBTransactional) throws InterruptedException {
        if (DynamoDBTransactionAspectSupport.isLocalTransaction(joinPoint.getSignature())) {
            try {
                if (DynamoDBTransactionAspectSupport.isRollBack()) {
                    return;
                }
                // Result 返回错误码 不处理事务
                if (result instanceof Result) {
                    if (((Result<?>) result).getCode() != 0) {
                        return;
                    }
                }

//                boolean open = dynamoDBTransactional.open();
                DynamoDBWriteType writeType = dynamoDBTransactional.value();
                if (writeType == DynamoDBWriteType.TRANSACTION_ENHANCED) {       // 增强型客户端
                    Map<Integer, TransactWriteItemsEnhancedRequest.Builder> requests = DynamoDBTransactionAspectSupport.getTransactWriteItemsEnhancedRequests();
                    if (requests != null && !requests.isEmpty()) {
                        requests.forEach((k, v) -> {
                            var req = v.returnConsumedCapacity(ReturnConsumedCapacity.INDEXES).build();
                            var r = dynamoDbEnhancedClient.transactWriteItemsWithResponse(v.build());
                            StatisticsContext.putDdbConsumedCapacity(new ConsumedCapacityInfo(req.transactWriteItems(), r.consumedCapacity()));
                        });
                    }
                }
                if (writeType == DynamoDBWriteType.TRANSACTION) {                   // 普通客户端
                    List<TransactWriteItemsRequest> transactWriteItemsRequests = DynamoDBTransactionAspectSupport.getTransactionWriteRequests();
                    for (TransactWriteItemsRequest request : transactWriteItemsRequests) {
                        var req = request.toBuilder().returnConsumedCapacity(ReturnConsumedCapacity.INDEXES).build();
                        var r = dynamoDbClient.transactWriteItems(req);

                        StatisticsContext.putDdbConsumedCapacity(new ConsumedCapacityInfo(req.transactItems(), r.consumedCapacity()));
                    }

                } else if (writeType == DynamoDBWriteType.BATCH) {                  // 批量操作
                    // 提交数据修改
                    Collection<Map<String, DynamoDBBatchWriteData>> writeBatch = DynamoDBTransactionAspectSupport.getWriteBatch();
                    if (writeBatch != null) {
                        for (Map<String, DynamoDBBatchWriteData> data : writeBatch) {

                            List<WriteBatch> writeBatches = new ArrayList<>();
                            data.forEach((k, v) -> {
                                writeBatches.add(v.getWriteBatchBuilder().build());
                            });

                            BatchWriteItemEnhancedRequest request = BatchWriteItemEnhancedRequest.builder()
                                    .writeBatches(writeBatches)
                                    .build();
                            BatchWriteResult batchWriteResult = dynamoDbEnhancedClient.batchWriteItem(request);

                            data.forEach((k, v) -> {
                                // 失败的数据重试一次
                                batchWriteResult.unprocessedPutItemsForTable(v.getMappedTable()).forEach(item -> {
                                    v.getMappedTable().putItem(item);
                                    log.error("batchWriteItem unprocessed, use putItem:{}", item);
                                });
                                batchWriteResult.unprocessedDeleteItemsForTable(v.getMappedTable()).forEach(key -> {
                                    v.getMappedTable().deleteItem(key);
                                    log.error("batchDeleteItem unprocessed, use deleteItem:{}", key);
                                });
                            });
                        }
                    }
                    // 更新数据
                    DynamoDBTransactionAspectSupport.executeUpdateItems();
                }

                // 条件更新数据
//                if (DynamoDBTransactionAspectSupport.getUpdateItemRequest() != null) {
//                    for (UpdateItemRequest updateItemRequest : DynamoDBTransactionAspectSupport.getUpdateItemRequest()) {
//                        dynamoDbClient.updateItem(updateItemRequest);
//                    }
//                }
                KinesisService.sendResourceLog(KinesiseCacheManager.getResourceLogChache());
                // redis 推送消息
                List<Pair<String, String>> publishMessage = RedisPublishMessageContext.getPublishMessage();
                if (publishMessage != null) {
                    for (Pair<String, String> msg : publishMessage) {
                        redisService.publishMessage(msg.getLeft(), msg.getRight());
                    }
                }

                DynamoDBTransactionSyncManager.doAfterCommit();
//                throw TransactionCanceledException.builder().cancellationReasons(CancellationReason.builder().code("TransactionConflict").build()).build();
            } catch (Exception e) {
                log.error("dynamodbError - putItems:{}, updateItems:{}, deleteItems:{}, updateConditions:{} - dynamodbException:",
                        DynamoDBTransactionAspectSupport.getPutItems(),
                        DynamoDBTransactionAspectSupport.getUpdateItems(),
                        DynamoDBTransactionAspectSupport.getDeleteItems(),
                        DynamoDBTransactionAspectSupport.getUpdateConditions()
                        , e);
                int code = -15;
                if (e instanceof TransactionCanceledException) {
                    // 事务冲突 返回请求重复错误码。 客户端自动重试请求
                    TransactionCanceledException transactionCanceledException = (TransactionCanceledException) e;
                    for (CancellationReason cancellationReason : transactionCanceledException.cancellationReasons()) {
                        if ("TransactionConflict".equals(cancellationReason.code()) || "ConditionalCheckFailed".equals(cancellationReason.code())) {
                            code = getResultCode();
                            break;
                        }
                    }
                } else if (e instanceof TransactionConflictException) {
                    code = getResultCode();
                }
                if (result instanceof Result) {
                    ((Result<?>) result).setCode(code);
                } else if (result instanceof Map) {
                    ((Map<String, Object>) result).clear();
                    ((Map<String, Object>) result).put("code", code);
                    ((Map<String, Object>) result).put("msg", e.getMessage());
                }
            } finally {
                this.clear();

            }
        }
    }

    private int getResultCode() {
        int code = -14;
        try {
            if (RequestContext.getUserId() != null) {
                // 事务冲突 重试3次
                String key = "transaction_canceled:" + RequestContext.getUserId();
                long retryCount = redisService.incrBy(key);
                if (retryCount <= 3) {
                    redisService.expireKey(key, 3, TimeUnit.SECONDS);
                    code = 100;
                    log.error("TransactionCanceledRetry, time:{} userId:{}, command:{}, retryCount:{}", System.currentTimeMillis(), RequestContext.getUserId(), RequestContext.getCommand(), retryCount);
                    Thread.sleep(5);
                } else {
                    redisService.deleteKey(key);
                }
            }
        } catch (Exception e) {
            log.error("getResultCodeError, e:", e);
        }

        return code;
    }


    @AfterThrowing(value = "pointCut()")
    public void doAfterThrowing() {
        DynamoDBTransactionAspectSupport.setRollBack();
        this.clear();
    }

    private void clear() {
        DynamoDBTransactionAspectSupport.clear();
        DynamoDBCacheManager.clear();
        KinesiseCacheManager.clear();
        // 释放在业务逻辑中加的锁
        redisLock.unLockAfterTransaction();
        RedisPublishMessageContext.clear();

        if (ServerContext.getServerType() != null && ServerContext.getServerType() == ServerType.CRON_SERVER) {
            RedisLockContext.clear();
        }
    }
}

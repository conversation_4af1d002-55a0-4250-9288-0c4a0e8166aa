var express = require('express');
var router = express.Router();
var fs = require('fs');
var path = require('path');
var ProtoBufRoot = require("protobufjs").Root;
var ByteBuffer = require('../libs/ByteBuffer.js');
var request = require("request");
var crypto = require("crypto");
var rp = require('request-promise');

var errorMsg = {};


router.get("/", function(req, res) {

	var userId = 0;
	var type = 0;
	if (req.query.login) {
		userId = req.query.userId;
		req.session.userId = userId;

		renderDev(req, res, null);
	} else if (req.session.userId) {
		userId = req.session.userId;
	}

	if (userId == 0) {
		renderDev(req, res, null);
	} else {
		var params = {
			"userId": userId,
			"type": type
		};
		postGame(9001, req.session.selectServer, JSON.stringify(params), res, function(result) {
			if (result.code == 0) {
				req.session.commonParams = result.commonParams;
			}

			

			renderDev(req, res, result);
		});
	}


});

router.get("/login", function(req, res) {
	var params = {
		"userId": req.query.userId,
		"type":req.query.type
	};
	req.session.selectServer = req.query.serverIdx;
	postGame(9001, req.query.serverIdx, JSON.stringify(params), res, function(result) {
		if (result.code == 0) {
			req.session.commonParams = result.commonParams;

			req.session.userId = result.userId;
		}

		res.send(JSON.stringify(result));
	});
});

router.get("/addResource", function(req, res) {

	var params = {
		"commonParams": req.session.commonParams,
		"resType":req.query.resType,
		"resNum":req.query.resNum,
		"itemId":req.query.itemId,
		"otherData": req.query.otherData
	};
	req.session.selectServer = req.query.serverIdx;

	postGame(9003, req.query.serverIdx, JSON.stringify(params), res, function(result) {
		res.send(JSON.stringify(result));
	});

});

router.post("/addResource", function(req, res) {

	var params = {
		"commonParams": req.session.commonParams,
		"resType":req.body.resType,
		"resNum":req.body.resNum,
		"itemId":req.body.itemId,
		"otherData": req.body.otherData
	};
	req.session.selectServer = req.body.serverIdx;

	postGame(9003, req.body.serverIdx, JSON.stringify(params), res, function(result) {
		res.send(JSON.stringify(result));
	});

});


router.get("/tools", function(req, res) {

	var params = {
		"commonParams": req.session.commonParams,
		"params":JSON.stringify(req.query.params)
	};
	req.session.selectServer = req.query.serverIdx;

	postGame(9005, req.query.serverIdx, JSON.stringify(params), res, function(result) {
		res.send(JSON.stringify(result));
	});

});

router.post("/tools", function(req, res) {
	var params = {
		"commonParams": req.session.commonParams,
		"params":JSON.stringify(
		{
			'type':req.body['params[type]'],
			'content':req.body['params[content]'],
		})
	};
	req.session.selectServer = req.body.serverIdx;

	postGame(9005, req.body.serverIdx, JSON.stringify(params), res, function(result) {
		res.send(JSON.stringify(result));
	});

});

router.get('/statistics/query', async function(req, res) {
	var serverIdx = req.query.serverIdx;
	var date = req.query.date;
	req.session.selectServer = req.query.serverIdx;

	var serverJsonPath = path.join(__dirname, '../config/servers.json');
	var serverJson = JSON.parse(fs.readFileSync(serverJsonPath, 'utf-8'));
	var serverInfo = serverJson[serverIdx];

	var url = serverInfo['url'] + "statistics/query?date=" + date;

	try {
		
		var responseBody = await rp({method:'GET', uri:url});
    	res.send(responseBody);
	} catch (e) {
		console.log(e);
		res.send("-1");
	}
	
});

router.get("/api", function(req, res) {

	var obj = getProtoMapJsonObject();
	var data = obj[req.query.cmd];

	var reqProto = "";
	var resProto = "";
	if (data) {
		reqProto = data['proto'];
		resProto = data['response'];
	}

	// 参数说明
	var jsonParamsPath = path.join(__dirname, '../proto/protoParams.json');
	var jsonParamsObj = JSON.parse(fs.readFileSync(jsonParamsPath, 'utf-8'));

	var reqParams = jsonParamsObj[reqProto];
	var resParams = jsonParamsObj[resProto];


	reqParams = fillCustomTypes(reqParams, jsonParamsObj);
	resParams = fillCustomTypes(resParams, jsonParamsObj);

	var renderData = {
        cmd: req.query.cmd,
        data: req.query.data,
        serverIdx: req.session.selectServer,
        api:req.query.name,
        reqParams:JSON.stringify(reqParams),
        resParams:JSON.stringify(resParams),
        transId: 0
    };


    // 查询transId
	var userId = req.query.userId;
	var transId = 0;
	if (userId) {
		var params = {
			"commonParams": req.session.commonParams,
			"params": JSON.stringify({
	            type:6,
	            userId:userId
	        })
		};
		postGame(9005, req.session.selectServer, JSON.stringify(params), res, function(result) {
			renderData["transId"] = result.respData;
			res.render('wiki/api', renderData);
		});
	} else {
		res.render('wiki/api', renderData);	
	}

	// req.session.selectServer = req.query.serverIdx;

	

	

});

router.get("/saveServerIdx", function(req, res) {
	req.session.selectServer = req.query.serverIdx;

	var result = {code:0};
	res.send(JSON.stringify(result));
});


router.get('/doApi', function(req, res) {
	if (req.query.serverIdx == "") {
		req.query.serverIdx = 0;
	}
	req.session.selectServer = req.query.serverIdx;
	postGame(req.query.cmd, req.query.serverIdx, req.query.params, res);
 });

router.get("/mazeRoomUnFinish",function (req,res) {
	var params = {
		"commonParams": req.session.commonParams,
		"resType":req.query.resType,
		"resNum":req.query.xPoint,
		"itemId":req.query.yPoint
	};
	req.session.selectServer = req.query.serverIdx;
	postGame(9003,req.query.serverIdx, JSON.stringify(params), res, function(result){
		res.send(JSON.stringify(result));
	});
});


function postGame(cmd, serverIdx, params, res, callback) {
	var protoObj = getProtoObj();
	var obj = getProtoMapJsonObject();
	var data = obj[cmd];

	
	if (!data) {
		res.send("-1");
		return;
	}

	var requestProtoJson = getProtoStructure(protoObj, obj[cmd]['proto']);
	if (requestProtoJson == null) {
		res.send("-2");
		return;
	}

	var protoBufJsonObj = ProtoBufRoot.fromJSON(protoObj);

	// 根据响应的cmd 查找proto
	var requestProtoObj = protoBufJsonObj.lookupType(obj[cmd]['proto']);

	var message = requestProtoObj.create(JSON.parse(params));
	var encodeMessage = requestProtoObj.encode(message).finish();
	var decodeMessage = requestProtoObj.decode(encodeMessage);
	
	
	var serverJsonPath = path.join(__dirname, '../config/servers.json');
	var serverJson = JSON.parse(fs.readFileSync(serverJsonPath, 'utf-8'));
	var serverInfo = serverJson[serverIdx];

	var url = serverInfo['url'];
	var key = serverInfo['key'];
	var version = serverInfo['version'];

	// console.log("url:" + url + " serverIdx:"+serverIdx);
	var timestamp = Date.parse(new Date()) / 1000;
	var timeBuffer = Buffer.from(timestamp + "");
	var keyBuffer = Buffer.from(key);

	// 发送包
	var buff = new ByteBuffer();
	buff.ushort(cmd);
	buff.uint32(encodeMessage.length);
	buff.byteArray(encodeMessage, encodeMessage.length);
	// buff.buff(encodeMessage)
	var packet = buff.pack(); 

	var bodyBuffer = packet;
	const totalBuffer = Buffer.concat([keyBuffer, timeBuffer, bodyBuffer]);
	var bytes = new Int8Array(totalBuffer);

	let hash = crypto.createHash('sha256').update(bytes, 'utf8').digest('hex');

	request({
	    url: url,
	    method: "POST",
	    headers: {
	        "DxxTime": timestamp,
	        "DxxVersion": version,
	        "Connection": "close",
	        "Content-Type": "application/octet-stream",
	        "DxxCheck":hash
	    },
	    body: packet,
	    encoding:null
	}, function(error, response, body) {
	    if (error != null) {
	    	res.send(error);	
	    } else {
	    	try {
	    		
	    		// var bodyBuff = new ByteBuffer(body);
		    	// var responseCmd = bodyBuff.ushort().unpack()[0];
		    	// var responseBuff = bodyBuff.buff().unpack()[1];
		    	var responseCmd = body.readUInt16LE();
		    	var responseBuff = body.slice(4, body.length);
				// console.log("responseCmd:" + responseCmd);

		    	var responseProtoObj = protoBufJsonObj.lookupType(obj[responseCmd]['proto']);
				var decodeMessage = responseProtoObj.decode(responseBuff);
				var responseProtoJson = getProtoStructure(protoObj, obj[responseCmd]['proto']);

				// console.log(decodeMessage);

				if (callback != null) {
		    		callback(formateResult(responseProtoJson, decodeMessage));
		    	} else {
		    		res.send(JSON.stringify(formateResult(responseProtoJson, decodeMessage)));	
		    	}
		    	
	    	} catch (e) {
	    		console.log(e);
	    		res.send("-1");
	    	}
	    }
	}); 
}

function formateResult(requestProtoJson, decodeMessage) {

	var msgJson = decodeMessage.toJSON();

	// console.log(msgJson);
	var result = {};
	if (msgJson['code'] != 0) {
		getErrorCode();
		result['code'] = msgJson['code'];
		result['errMsg'] = errorMsg[msgJson['code']];
	}

	for (var key in requestProtoJson) {
		if (msgJson.hasOwnProperty(key)) {
			result[key] = msgJson[key];
		} else {
			result[key] = requestProtoJson[key];
		}
	}

	return result;
}

function renderDev(req, res, loginData) {

	// 错误码
	var errorCode = getErrorCode();
	// 服务器列表
	var serverJsonPath = path.join(__dirname, '../config/servers.json');
	var serverJson = JSON.parse(fs.readFileSync(serverJsonPath, 'utf-8'));

	// api接口数据
	var filePath = path.join(__dirname, '../proto/protoMap.json');
	var fileContent = fs.readFileSync(filePath, 'utf-8').toString();
	var obj = JSON.parse(fileContent);


	var itemJsonPath = path.join(__dirname, '../config/Item.json');
	var itemContent = fs.readFileSync(itemJsonPath, 'utf-8').toString();
	var itemObj = JSON.parse(itemContent);

	var selectServer = req.session.selectServer;
	var version = 9999;
	if (typeof(selectServer) != "undefined") {
        version = serverJson[selectServer]['version']
    }

	var protoObj = getProtoObj();

	var protoJson = {};
	for (var key in obj) {
		if (obj[key]['response'] != "") {
			var modCmd = parseInt(key / 100);

			if (!protoJson.hasOwnProperty(modCmd)) {
				protoJson[modCmd] = {};
				protoJson[modCmd]['count'] = 1;
			} else {
				protoJson[modCmd]['count'] ++;
			}
			protoJson[modCmd][key] = obj[key];

			var requestProtoJson = getProtoStructure(protoObj, obj[key]['proto']);

			if (requestProtoJson != null) {

				if (requestProtoJson.hasOwnProperty('commonParams') && req.session.commonParams) {
					requestProtoJson = meargeCommparams(requestProtoJson, req.session.commonParams);
				}

				if (requestProtoJson.hasOwnProperty('commonParams')) {
					requestProtoJson['commonParams']['version'] = version;
				}

				protoJson[modCmd][key]['data'] = JSON.stringify(requestProtoJson);
			} else {
				protoJson[modCmd][key]['data'] = "";
			}
		}
	}

	var renderData = {};
	renderData['serverJson'] = serverJson;
	renderData['protoJson'] = protoJson;
	renderData['itemObj'] = itemObj;
	renderData['errorCode'] = errorCode;
	if (loginData != null) {
		req.session.userId = loginData.userId;
		renderData['userId'] = loginData.userId;
		renderData['commonParams'] = JSON.stringify(loginData.commonParams);
		renderData['dbIdx'] = loginData.dbIdx;
		renderData['tableIdx'] = loginData.tableIdx;
		renderData['coins'] = loginData.coins;
		renderData['diamonds'] = loginData.diamonds;
		renderData['chapterId'] = loginData.chapterId;
		renderData['missionId'] = loginData.missionId;
		renderData['level'] = loginData.level;
		renderData['exp'] = loginData.exp;
		renderData['extra'] = loginData.extra;
		renderData['missionRound'] = loginData.missionRound;
		renderData['lifeValue'] = loginData.lifeValue;
	}
	renderData['selectServer'] = selectServer;
	res.render('wiki/dev', renderData);
}

function meargeCommparams(jsonObj, loginObj) {
	for (var key in jsonObj['commonParams']) {
		if (loginObj.hasOwnProperty(key)) {
			jsonObj['commonParams'][key] = loginObj[key];
		}
	}
	return jsonObj;
}

function getErrorCode() {
	var codeFile = path.join(__dirname, '../config/ErrorCode.java');
	var content = fs.readFileSync(codeFile, 'utf-8');
	var lines = content.split("\n");
	var errorCode = [];
	for (var i = 0; i < lines.length; i ++) {
		var line = lines[i];
		var code = null;
		if (line.indexOf("=") >= 0) {
			var data = line.split("=");
			code = data[data.length - 1].split(";")[0];
		}

		if (line.indexOf("//") >= 0 && code != null) {
			var data = line.split("//");
			errorCode.push({"code": code, "msg": data[data.length - 1]});

			errorMsg[parseInt(code)] = data[data.length - 1];
		}
		
	}
	return errorCode;
}


var baseType = ["string", "int", "bool", "double", "float", "byte"];

// 是否是基础数据类型
function isBaseType(type) {
	var result = false;
	for (var key in baseType) {
		if (type.indexOf(baseType[key]) >= 0) {
			result = true;
			break;
		}
	}
	return result;
}

function getProtoObj() {
	var protoPath = path.join(__dirname, '../proto/proto.json');
	var protoContent = fs.readFileSync(protoPath, 'utf-8').toString();
	var protoObj = JSON.parse(protoContent);
	return protoObj;
}

function getProtoStructure(protoObj, protoName) {
	
	var findObj = null;
	
	// proto 3.4的写法
	for (var key in protoObj['nested']) {

		for (var subKey in protoObj['nested'][key]['nested']) {
			for (var protoKey in protoObj['nested'][key]['nested'][subKey]['nested']) {
				if (protoKey == protoName) {
					findObj = protoObj['nested'][key]['nested'][subKey]['nested'][protoKey];
					break;
				}
			}
		}
	}

	if (findObj == null) return null;
	findObj = findObj['fields'];
	var result = {};
	for (var key in findObj) {
		var data = findObj[key];

		if (data.hasOwnProperty('keyType')) {
			// map格式
			result[key] = {};
		} else if (data.hasOwnProperty('rule')) {
			// list
			result[key] = [];
		} else {
			if (isBaseType(data['type'])) {
				if (data['type'] == "string") {
					result[key] = "";
				} else if (data['type'] == "bool") {
					result[key] = false;
				} else {
					result[key] = 0;
				}
			} else {
				var typeObj = data['type'].split(".");
				typeObj = typeObj[typeObj.length - 1];
				result[key] = getProtoStructure(protoObj, typeObj);
			}
		}
	}
	return result;
}

function getProtoMapJsonObject() {
	var filePath = path.join(__dirname, '../proto/protoMap.json');
	var fileContent = fs.readFileSync(filePath, 'utf-8').toString();
	var obj = JSON.parse(fileContent);
	return obj;
}

function fillCustomTypes(params, paramsJsonObj) {
	for (var i = 0; i < params.length; i ++) {
		if (params[i]['type'] == "") {
			params[i]['rule'] = "unknown";
			params[i]['type'] = "unknown";
			params[i]['desc'] = "unknown";
			params[i]['custom'] = "unknown";
			params[i]['isCustom'] = false;
			continue;
		}
		var typeName = params[i]['type'].split(".");
		var strType = typeName[typeName.length - 1];
		if (params[i]['isCustom'] && paramsJsonObj.hasOwnProperty(strType)) {
			params[i]['custom'] = paramsJsonObj[strType];

			for (var j = 0; j < params[i]['custom'].length; j ++) {
				if (params[i]['custom'][j]['type'] == "") {
					params[i]['custom'][j]['rule'] = "unknown";
					params[i]['custom'][j]['type'] = "unknown";
					params[i]['custom'][j]['desc'] = "unknown";
				}
			}
		}
	}
	// console.log(JSON.stringify(params))
	return params;
}

router.get("/syncItemConfig", async function(req, res) {

	var ids = req.query.ids;
	var result = {};
	for (var i = 0; i < ids.length; i ++) {
		var fileId = ids[i];
		var url = "http://config.gorilla10.com/export/downLoad?fileId="+fileId+"&key=a5f0a9b3c9e1c7e0";
		var responseBody = await rp({method:'POST', uri:url});
		var itemObj = JSON.parse(responseBody);

		var itemsObj = itemObj['Item'];
		for (var key in itemsObj) {
			result[key] = {};
			result[key]['note'] = itemsObj[key]['note'];
			result[key]['itemType'] = itemsObj[key]['itemType'];

		}
	}

	var configPath = path.join(__dirname, "../config/Item.json");
	
	var jsonContent = JSON.stringify(result);
	var fd = fs.openSync(configPath,'w');
	fs.writeSync(fd, jsonContent, 0, "utf-8");
	fs.closeSync(fd);

	res.send("0");

});

router.get("/health_check", function(req, res) {
	res.send("0");
});

module.exports = router;





















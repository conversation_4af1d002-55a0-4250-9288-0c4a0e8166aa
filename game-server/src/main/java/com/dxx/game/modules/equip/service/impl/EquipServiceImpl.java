package com.dxx.game.modules.equip.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.equip.EquipComposeEntity;
import com.dxx.game.config.entity.equip.EquipEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.repository.EquipDao;
import com.dxx.game.dao.dynamodb.repository.HeroDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto.CommonData;
import com.dxx.game.dto.CommonProto.EquipmentDto;
import com.dxx.game.dto.EquipProto.*;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.equip.support.EquipSupport;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.reward.model.EquipReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.service.TaskService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EquipServiceImpl implements EquipService {

    @Autowired
    private EquipSupport equipSupport;
    @Autowired
    private EquipDao equipDao;
    @Autowired
    private HeroDao heroDao;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private UserService userService;
    @Autowired
    private LogService logService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private SevenDayTaskService sevenDayTaskService;
    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private UserInfoRedisDao userInfoRedisDao;


    @DynamoDBTransactional
    @Override
    public Result<EquipComposeResponse> equipCompose(EquipComposeRequest params) {
        Long userId = RequestContext.getUserId();
        List<EquipComposeData> composeDataList = params.getComposeDataList();
        if (composeDataList.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        List<Long> rowIds = new ArrayList<>();
        List<Long> materialRowIds = new ArrayList<>();
        List<Long> mainRowIds = new ArrayList<>();
        for (EquipComposeData composeData : composeDataList) {
            if (composeData.getMainRowId() <= 0) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            rowIds.add(composeData.getMainRowId());
            List<Long> rowIdsList = composeData.getRowIdsList();
            for (long rowId : rowIdsList) {
                if (rowId <= 0) {
                    return Result.Error(ErrorCode.PARAMS_ERROR);
                }
                if (materialRowIds.contains(rowId)) {
                    return Result.Error(ErrorCode.PARAMS_ERROR);
                }
                rowIds.add(rowId);
                materialRowIds.add(rowId);
            }
            mainRowIds.add(composeData.getMainRowId());
        }
        rowIds = rowIds.stream().distinct().collect(Collectors.toList());
        List<Equip> equipList = equipDao.getListByRowIds(userId, rowIds);
        Map<Long, Equip> equipMap = equipList.stream().collect(Collectors.toMap(Equip::getRowId, equip -> equip));
        Map<Long, EquipmentDto> equipmentDtos = new HashMap<>();
        List<List<Integer>> rewardConfig = new ArrayList<>();
        List<Hero> heroes = new ArrayList<>();

        // 计算合成是否合法
        for (EquipComposeData composeData : composeDataList) {
            long mainRowId = composeData.getMainRowId();
            List<Long> rowIdsList = composeData.getRowIdsList();
            Equip mainEquip = equipMap.get(mainRowId);
            EquipEntity mainEquipEntity = gameConfigManager.getEquipConfig().getEquipEntity(mainEquip.getEquipId());
            EquipComposeEntity equipComposeEntity = gameConfigManager.getEquipConfig().getEquipComposeEntity(mainEquipEntity.getQuality());
            if (equipComposeEntity == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            if (equipComposeEntity.getComposeTo() <= 0) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            // 检查数量
            if (equipComposeEntity.getComposeNeed3() != rowIdsList.size()) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            for (long rowId : rowIdsList) {
                Equip equip = equipMap.get(rowId);
                // 检查品质
                int q = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId()).getQuality();
                if (q != equipComposeEntity.getComposeNeed2()) {
                    return Result.Error(ErrorCode.PARAMS_ERROR);
                }
                EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
                // 检查部位
                if (equipComposeEntity.getComposeNeed1() > 0) {
                    if (equipEntity.getType() != mainEquipEntity.getType()) {
                        return Result.Error(ErrorCode.PARAMS_ERROR);
                    }
                }
                // 检查ID
                if (equipComposeEntity.getComposeNeed1() == 2) {
                    if (equipEntity.getId() != mainEquipEntity.getId()) {
                        return Result.Error(ErrorCode.PARAMS_ERROR);
                    }
                }
            }
            // 执行合成逻辑
            int supId = mainEquip.getEquipId() / 100;
            Map<Integer, EquipEntity> qualityMap = gameConfigManager.getEquipQualityMap().get(supId);
            if (qualityMap == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            EquipEntity equipEntity = qualityMap.get(equipComposeEntity.getComposeTo());
            if (equipEntity == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            // 计算材料返还
            for (long rowId : rowIdsList) {
                Equip equip = equipMap.get(rowId);
//                Map<Integer, Integer> levelReturnMap = calEquipLevelReturn(equip);
//                for (Map.Entry<Integer, Integer> entry : levelReturnMap.entrySet()) {
//                    Integer itemId = entry.getKey();
//                    Integer itemCount = entry.getValue();
//                    if (itemCount <= 0) {
//                        continue;
//                    }
//                    List<Integer> temp = new ArrayList<>();
//                    temp.add(itemId);
//                    temp.add(itemCount);
//                    rewardConfig.add(temp);
//                }equipCfgEntity
                // 装备
                if (equip.getHeroRowId() != 0) {
                    EquipEntity equipCfgEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
                    var hero = getHeroByCache(userId, equip.getHeroRowId(), heroes);
                    hero.getEquips().remove(equipCfgEntity.getType(), equip);
                }
                // 删除材料
                equipDao.delete(equip);
            }
            // 主装备升阶
            mainEquip.setEquipId(equipEntity.getId());
            if (mainEquip.getHeroRowId() != 0) {
                EquipEntity equipCfgEntity = gameConfigManager.getEquipConfig().getEquipEntity(mainEquip.getEquipId());
                var hero = getHeroByCache(userId, mainEquip.getHeroRowId(), heroes);
                hero.getEquips().put(equipCfgEntity.getType(), mainEquip);
            }

            equipDao.update(mainEquip);

            for (Hero hero : heroes) {
                heroDao.update(hero);
            }

            EquipmentDto equipmentDto = CommonHelper.buildEquipmentDto(mainEquip);
            equipmentDtos.put(mainEquip.getRowId(), equipmentDto);
        }

        RewardResultSet rewardResultSet = null;
        if (!rewardConfig.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
        }

//        battleRedisDao.updateWearEquip(userId, getAllDressEquipDataList(userExtend));
        battleRedisDao.removeData(userId);

        //任务
//        TaskProcess taskProcess = TaskProcess.valueOf(TaskType.DAILY, TaskType.DAILY_EQUIP_COMPOSE, 1);
//        taskService.updateTask(userId, taskProcess);


        // 封装消息
        CommonData commonData = null;
        if (rewardResultSet != null) {
            commonData = CommonHelper.buildCommonData(rewardResultSet);
        } else {
            commonData = CommonHelper.buildCommonData();
        }
        commonData = commonData.toBuilder().addAllEquipment(equipmentDtos.values()).build();
        EquipComposeResponse.Builder response = EquipComposeResponse.newBuilder();
        response.setCommonData(commonData);
        response.addAllDelEquipRowId(materialRowIds);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<EquipDressResponse> equipDress(EquipDressRequest params) {
        long userId = RequestContext.getUserId();
        // 装备位置是否正确
        if (params.getRowIdsList().size() > gameConfigManager.getEquipConfig().getEquipType().size()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<Long> rowIds = new ArrayList<>();
        for (Long rowId : params.getRowIdsList()) {
            if (rowId > 0) {
                rowIds.add(rowId);
            }
        }

        // 重复
        if (CollectionUtils.isRepeat(rowIds)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Hero hero = heroDao.getByRowId(userId, params.getHeroRowId());
        if (hero == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<EquipmentDto> dtos = new ArrayList<>();
        List<Equip> equips = equipDao.getListByRowIds(userId, params.getRowIdsList());
        for (Equip equip : equips) {
            if (equip.getHeroRowId() != 0) {
                return Result.Error(ErrorCode.EQUIP_IS_WEAR);
            }

            EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
            if (equipEntity == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            if(hero.getEquips().get(equipEntity.getType()) != null){
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            equipSupport.upEquipment(params.getHeroRowId(), equip);
            hero.getEquips().put(equipEntity.getType(), equip);
            equipDao.update(equip);
            dtos.add(CommonHelper.buildEquipmentDto(equip));
        }

        heroDao.update(hero);
        battleRedisDao.removeData(userId);

        EquipDressResponse.Builder response = EquipDressResponse.newBuilder();
        response.addAllRowIds(params.getRowIdsList());
        response.setCommonData(CommonHelper.buildCommonData().toBuilder().addAllEquipment(dtos));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<EquipOffResponse> equipOff(EquipOffRequest params) {
        long userId = RequestContext.getUserId();

        if (params.getRowIdsList().size() > gameConfigManager.getEquipConfig().getEquipType().size()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        List<Long> rowIds = new ArrayList<>();
        for (Long rowId : params.getRowIdsList()) {
            if (rowId > 0) {
                rowIds.add(rowId);
            }
        }
        // 重复
        if (CollectionUtils.isRepeat(rowIds)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }


        List<EquipmentDto> dtos = new ArrayList<>();
        List<Equip> equips = equipDao.getListByRowIds(userId, params.getRowIdsList());

        Hero hero = heroDao.getByRowId(userId, params.getHeroRowId());
        if (hero == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        for (Equip equip : equips) {
            EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());

            if (hero.getEquips().get(equipEntity.getType()) == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            if (equip.getHeroRowId() != params.getHeroRowId()) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            if (!hero.getEquips().get(equipEntity.getType()).getRowId().equals(equip.getRowId())) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            equipSupport.downEquipment(params.getHeroRowId(), equip);
            hero.getEquips().remove(equipEntity.getType());
            equipDao.update(equip);
            dtos.add(CommonHelper.buildEquipmentDto(equip));
        }

        heroDao.update(hero);
        battleRedisDao.removeData(userId);

        EquipOffResponse.Builder response = EquipOffResponse.newBuilder();
        response.addAllRowIds(params.getRowIdsList());
        response.setCommonData(CommonHelper.buildCommonData().toBuilder().addAllEquipment(dtos));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<EquipQualityDownResponse> qualityDown(EquipQualityDownRequest params) {
        long userId = RequestContext.getUserId();

        List<Long> rowIds = params.getRowIdsList();
        if (rowIds.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (CollectionUtils.isRepeat(rowIds)) {
            log.error("equip quality down equip rowIds repeated, userId:{}, rowIds:{} ", userId, rowIds);
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        List<Equip> equips = equipDao.getListByRowIds(userId, rowIds);
        if (equips.size() < rowIds.size()) {
            log.error("EQUIP_IS_NOT_FOUND, userId:{}, ids : {}", userId, rowIds);
            return Result.Error(ErrorCode.EQUIP_IS_NOT_FOUND);
        }

        Map<Integer, Integer> addItems = new HashMap<>();
        List<EquipmentDto> equipmentDtos = new ArrayList<>();
        List<Equip> logDeleteEquips = CollectionUtils.deepCopyList(equips);
        List<Equip> logReceiveEquips = new ArrayList<>(equips.size());
        List<Hero> heroes = new ArrayList<>();

        for (Equip equip : equips) {
            int equipId = equip.getEquipId();
            // 根据配置判断此装备是否可以降品
            EquipEntity equipCfgEntity = gameConfigManager.getEquipConfig().getEquipEntity(equipId);
            int equipQualityUpId = equipCfgEntity.getQuality();
            EquipComposeEntity equipQualityCfgEntity = gameConfigManager.getEquipConfig().getEquipComposeEntity(equipQualityUpId);
            if (equipQualityCfgEntity.getReduceTo() == 0) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            // 品质下降后的装备ID
            int downNum = equipQualityUpId - equipQualityCfgEntity.getReduceTo();
            int downEquipId = equipId - downNum;

            // 降品返还通用材料
            int costNum = equipQualityCfgEntity.getReduceNeed1();
            if (costNum <= 0) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            int costId = 2000000 + 99 * 10000 + equipCfgEntity.getType() * 100 + equipQualityCfgEntity.getReduceNeed2();
            addItems.put(costId, costNum);

            equip.setEquipId(downEquipId);

            if (equip.getHeroRowId() != 0) {
                var hero = getHeroByCache(userId, equip.getHeroRowId(), heroes);
                hero.getEquips().put(equipCfgEntity.getType(), equip);
            }

            equipDao.update(equip);
            equipmentDtos.add(CommonHelper.buildEquipmentDto(equip));
            logReceiveEquips.add(equip);
        }

        for (Hero hero : heroes) {
            heroDao.update(hero);
        }

        // 给道具
        List<Reward> rewards = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : addItems.entrySet()) {
            rewards.add(EquipReward.valueOf(entry.getKey(), entry.getValue()));
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

//        battleRedisDao.updateWearEquip(userId, getAllDressEquipDataList(userExtendDao.getByUserId(userId)));
        battleRedisDao.removeData(userId);

//        todo test
//        发送日志
        long transId = userService.getTransId(userId);
        // 删除日志
        logService.sendEquips(userId, RequestContext.getCommand(), transId, logDeleteEquips, false);
        // 更新获得日志
        logService.sendEquips(userId, RequestContext.getCommand(), transId, logReceiveEquips, true);

        EquipQualityDownResponse.Builder response = EquipQualityDownResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet).toBuilder().addAllEquipment(equipmentDtos).build());

        return Result.Success(response.build());
    }

    public Hero getHeroByCache(long userId, long heroRowId, List<Hero> heroes) {
        var find = heroes.stream().filter(v -> v.getRowId().equals(heroRowId)).findAny();
        if (find.isEmpty()) {
            var hero = heroDao.getByRowId(userId, heroRowId);
            heroes.add(hero);
            return hero;
        }else{
            return find.get();
        }
    }


    @DynamoDBTransactional
    @Override
    public Result<EquipReplaceResponse> equipReplace(EquipReplaceRequest params) {
        long userId = RequestContext.getUserId();
        long rowId = params.getRowId();
        long heroRowId = params.getHeroRowId();

        Equip equip = equipDao.getByRowId(userId, rowId);
        if (equip == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Hero hero = heroDao.getByRowId(userId, params.getHeroRowId());
        if (hero == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int part = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId()).getType();

        var onEquip = hero.getEquips().get(part);
        if (onEquip != null && onEquip.getRowId().equals(equip.getRowId())) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<EquipmentDto> dtos = new ArrayList<>();
        if (onEquip != null) {
            equipSupport.downEquipment(heroRowId, onEquip);
            dtos.add(CommonHelper.buildEquipmentDto(onEquip));
            equipDao.update(onEquip);
        }

        equipSupport.upEquipment(heroRowId, equip);
        dtos.add(CommonHelper.buildEquipmentDto(equip));
        hero.getEquips().put(part, equip);

        equipDao.update(equip);
        heroDao.update(hero);

        EquipReplaceResponse.Builder response = EquipReplaceResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData().toBuilder().addAllEquipment(dtos));
        return Result.Success(response.build());
    }

    @Override
    public Equip createEquip(long userId, int configId, int level) {
        EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(configId);
        if (equipEntity == null) {
            log.error("equip config is null, configId: {}", configId);
            return null;
        }

        Equip equip = new Equip();
        equip.setRowId(commonService.generateId(userId));
        equip.setUserId(userId);
        equip.setEquipId(configId);
        equip.setLevel(1);
        equip.setExp(0);
        equip.setHeroRowId(0L);
//        equip.setQuality(equipEntity.getQuality());
        equipDao.insert(equip);
        return equip;
    }

    @Override
    public List<Equip> getAllEquip(long userId) {
        return equipDao.getAllByUserId(userId);
    }

    @Override
    public List<EquipmentDto> getAllEquipDto(long userId, List<Equip> equips) {
        List<EquipmentDto> result = new ArrayList<>(equips.size());

        for (Equip equip : equips) {
            result.add(CommonHelper.buildEquipmentDto(equip));
        }

        return result;
    }

    @Override
    public List<Long> getAllEquipIds(List<Equip> equips) {
        List<Long> result = new ArrayList<>();

        for (Equip equip : equips) {
            if (equip.getHeroRowId() != null && equip.getHeroRowId() != 0) {
                result.add(equip.getRowId());
            }
        }

        return result;
    }

    @Override
    public List<EquipmentDto> getAllEquips(long userId) {
        List<Equip> equips = equipDao.getAllByUserId(userId);
        List<EquipmentDto> result = new ArrayList<>(equips.size());

        for (Equip equip : equips) {
            result.add(CommonHelper.buildEquipmentDto(equip));
        }

        return result;
    }

    @Override
    public List<EquipmentDto> getAllEquips(List<Equip> equips) {
        List<EquipmentDto> result = new ArrayList<>();

        equips.forEach(v -> {
           if (v.getHeroRowId() != 0){
               result.add(CommonHelper.buildEquipmentDto(v));
           }
        });

        return result;
    }

//    private Map<Integer, Integer> calEquipLevelReturn(Equip equip) {
//        Map<Integer, Integer> dataMap = new HashMap<>();
//        ConcurrentHashMap<Integer, Map<Integer, Map<Integer, Integer>>> equipLevelResourceReturnMap = gameConfigManager.getEquipLevelResourceReturnMap();
//        EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
//        if (!equipLevelResourceReturnMap.containsKey(equipEntity.getType())) {
//            return dataMap;
//        }
//        Map<Integer, Map<Integer, Integer>> typeMap = equipLevelResourceReturnMap.get(equipEntity.getType());
//        if (!typeMap.containsKey(equip.getLevel())) {
//            return dataMap;
//        }
//        Map<Integer, Integer> returnMap = typeMap.get(equip.getLevel());
//        dataMap.putAll(returnMap);
//        return dataMap;
//    }
}

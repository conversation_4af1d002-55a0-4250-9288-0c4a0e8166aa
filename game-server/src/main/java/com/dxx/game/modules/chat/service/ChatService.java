package com.dxx.game.modules.chat.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.ChatProto.*;

public interface ChatService {


    /**
     * 聊天-展示道具-公会
     * @param params
     * @return
     */
    Result<ChatShowItemResponse> chatShowItemAction(ChatShowItemRequest params);

    boolean isChatFrequencyTooFast(long userId);

    void saveChatFrequency(long userId);


}

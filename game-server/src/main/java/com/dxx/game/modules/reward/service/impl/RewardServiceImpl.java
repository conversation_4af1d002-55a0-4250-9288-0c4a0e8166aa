package com.dxx.game.modules.reward.service.impl;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.Symbol;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.item.ItemEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.modules.item.service.ItemService;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.action.RewardActionSet;
import com.dxx.game.modules.reward.action.SimlpleRewardAction;
import com.dxx.game.modules.reward.model.*;
import com.dxx.game.modules.reward.processor.RewardProcessor;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.service.TaskService;
import com.dxx.game.modules.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 奖励接口实现类
 *
 * <AUTHOR>
 * @date 2019-12-13 16:47
 */
@Service
public class RewardServiceImpl implements RewardService {

    private static final Logger logger = LoggerFactory.getLogger(RewardServiceImpl.class);

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private UserService userService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private LogService logService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private TaskService taskService;

    /**
     * 处理器集合
     */
    private static final ConcurrentMap<RewardType, RewardProcessor> REWARD_PROCESSORS = new ConcurrentHashMap<RewardType, RewardProcessor>();
    /**
     * 资源处理器集合
     */
    private static final ConcurrentMap<RewardResourceType, RewardProcessor> REWARD_REOURCE_PROCESSORS = new ConcurrentHashMap<RewardResourceType, RewardProcessor>();

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        RewardAction action = new SimlpleRewardAction();
        RewardProcessor processor = getProcessor(reward);
        if (processor == null) {
            logger.error("RewardProcessor is null rewardType: {}, command:{}", reward.getType().getValue(), RequestContext.getCommand());
            action.setResultCode(ErrorCode.EXEC_REWARD_ERROR);
        } else {
            action = processor.tryReward(userId, reward);
        }
        return action;
    }


    @Override
    public RewardActionSet tryRewards(long userId, List<Integer> reward) {
        List<Integer> copyList = CollectionUtils.copyS(reward);

        List<Reward> rewards = this.parseReward(copyList);
        if (rewards.isEmpty()) {
            logger.error("rewards is empty, userId:{}, config:{}", userId, copyList);
            RewardActionSet rewardActionSet = new RewardActionSet();
            rewardActionSet.setResultCode(ErrorCode.EXEC_REWARD_ERROR);
            return rewardActionSet;
        } else {
            return tryRewards(userId, rewards);
        }
    }

    @Override
    public RewardActionSet tryRewards(long userId, Collection<Reward> rewards) {
        RewardActionSet rewardActionSet = new RewardActionSet();
        if (rewards.isEmpty()) {
            logger.error("rewards is emtpy, userId:{}, command:{}", userId, RequestContext.getCommand());
            rewardActionSet.setResultCode(ErrorCode.EXEC_REWARD_ERROR);
        }
        for (Reward reward : rewards) {
            RewardAction action = this.tryReward(userId, reward);
            rewardActionSet.addAction(action);
            if (action.isFailed()) {
                break;
            }
        }
        return rewardActionSet;
    }

    @Override
    public RewardResultSet executeReward(long userId, List<Integer> rewardConfig) {
        List<Integer> copyList = CollectionUtils.copyS(rewardConfig);

        List<Reward> rewards = this.parseReward(copyList);
        if (rewards.isEmpty()) {
            logger.error("rewards is empty, userId:{}, config:{}", userId, copyList);
            return RewardResultSet.valueOf(ErrorCode.EXEC_REWARD_ERROR);
        }

        return this.executeRewards(userId, rewards);
    }

    @Override
    public RewardResultSet executeReward(long userId, Reward reward) {
        RewardAction rewardAction = this.tryReward(userId, reward);
        if (rewardAction.isFailed()) {
            return RewardResultSet.valueOf(rewardAction.getResultCode());
        }
        return this.executeReward(userId, rewardAction);
    }


    @Override
    public RewardResultSet executeReward(long userId, RewardAction rewardAction) {
        RewardResultSet resultSet = new RewardResultSet();
        if (rewardAction == null || rewardAction.isFailed()) {
            resultSet.setResultCode(ErrorCode.EXEC_REWARD_ERROR);
            return resultSet;
        } else {
            this.doExecuteReward(userId, rewardAction, resultSet);
        }

        // 发送日志事件
        logService.sendLog(resultSet, userId, RequestContext.getCommand(), userService.getTransId(userId));
        resultSet.buildResponseData();
        taskService.updateTask(userId, resultSet);
        return resultSet;
    }

    @Override
    public RewardResultSet executeRewards(long userId, List<List<Integer>> rewardConfig) {
        List<List<Integer>> copyList = CollectionUtils.copyM(rewardConfig);

        List<Reward> rewards = this.parseRewards(copyList);
        if (rewards.isEmpty()) {
            logger.error("rewards is empty, userId:{}, config:{}", userId, copyList);
            return RewardResultSet.valueOf(ErrorCode.EXEC_REWARD_ERROR);
        }
        return this.executeRewards(userId, rewards);

    }

    @Override
    public RewardResultSet executeRewards(long userId, Collection<Reward> rewards) {
        RewardActionSet rewardActionSet = this.tryRewards(userId, rewards);
        if (rewardActionSet.isFailed()) {
            return RewardResultSet.valueOf(rewardActionSet.getResultCode());
        }
        return this.executeRewards(userId, rewardActionSet);
    }

    @Override
    public RewardResultSet executeRewards(long userId, RewardActionSet rewardActionSet) {
        RewardResultSet resultSet = new RewardResultSet();
        if (rewardActionSet.getActionList().isEmpty() || rewardActionSet.isFailed()) {
            resultSet.setResultCode(ErrorCode.EXEC_REWARD_ERROR);
            return resultSet;
        }

        for (RewardAction rewardAction : rewardActionSet.getActionList()) {
            this.doExecuteReward(userId, rewardAction, resultSet);
            if (resultSet.isFailed()) {
                return resultSet;
            }
        }
        // 发送日志事件
        logService.sendLog(resultSet, userId, RequestContext.getCommand(), userService.getTransId(userId));
        resultSet.buildResponseData();
        taskService.updateTask(userId, resultSet);
        return resultSet;
    }

    @Override
    public RewardResultSet executeCosts(long userId, List<List<Integer>> costConfig) {
        List<List<Integer>> costList = new ArrayList<>();

        for (List<Integer> tmp : costConfig) {
            costList.add(getCostList(tmp));
        }

        return this.executeRewards(userId, costList);
    }

    @Override
    public RewardResultSet executeCost(long userId, List<Integer> costConfig) {
        return this.executeReward(userId, getCostList(costConfig));
    }

    @Override
    public List<Integer> getCostList(List<Integer> costConfig) {
        List<Integer> copyList = CollectionUtils.copyS(costConfig);

        checkRewardConfig(copyList);

        List<Integer> cost = new ArrayList<>();
        cost.add(copyList.get(0));
        cost.add(copyList.get(1));
        cost.add(-Math.abs(copyList.get(2)));

        return cost;
    }

    @Override
    public List<Reward> parseRewards(List<List<Integer>> rewardsConfig) {
        return parseRewards(rewardsConfig, true);
    }

    @Override
    public List<Reward> parseRewards(List<List<Integer>> rewardsConfig, boolean combine) {
        List<List<Integer>> copyList = CollectionUtils.copyM(rewardsConfig);

        List<Reward> result = new ArrayList<Reward>(copyList.size());
        for (List<Integer> config : copyList) {

            // 配置小于3 自动判断类型
            checkRewardConfig(config);
            if (config.get(2) == 0) {
                continue;
            }
            List<Reward> reward = this.parseReward(config);
            if (!reward.isEmpty()) {
                result.addAll(reward);
            }
        }

        if (combine) {
            this.combineRewards(result);
        }

        return result;
    }

    @Override
    public List<Reward> parseReward(List<Integer> rewardConfig) {
        List<Integer> copyList = CollectionUtils.copyS(rewardConfig);

        checkRewardConfig(copyList);

        List<Reward> result = new ArrayList<Reward>();
        Reward reward = null;
        List<Reward> rewards = null;
        RewardType rewardType = RewardType.valueOf(copyList.get(0));
        int itemId = copyList.get(1);
        int count = copyList.get(2);

        ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(itemId);

        switch (rewardType) {
            case RESOURCE: {
                reward = new ResourceReward(RewardResourceType.valueOf(itemId), count);
                break;
            }
            case GUILD_SHOP_COIN:
            case ITEM_OPTIONAL_GIFT:
            case ITEM_HUANG_UP_TIME_GIFT:
            case ITEM_HERO_FRAGMENT:
            case ITEM: {
                reward = ItemReward.valueOf(itemId, count);
                break;
            }
            case EQUIP: {
                if (copyList.size() > 3) {
                    // 等级
                    reward = EquipReward.valueOf(itemId, count, copyList.get(3));
                } else {
                    reward = EquipReward.valueOf(itemId, count);
                }

                break;
            }
            case HERO: {
                int realHeroId = itemEntity.getItemTypeParam().get(0).get(0);
                int quality = gameConfigManager.getGameMemberConfig().getMemberEntity(realHeroId).getInitialQuality();

                if (copyList.size() >= 4) {
                    int rowId = copyList.get(3);
                    reward = HeroReward.valueOf(realHeroId, count, quality, rowId);
                } else {
                    reward = HeroReward.valueOf(realHeroId, count, quality);
                }
                break;
            }
            case ITEM_RAND_GIFT:
                int isAutoUse = itemEntity.getAutoUse();
                if (isAutoUse == 0) {
                    reward = ItemReward.valueOf(itemId, count);
                } else {
                    rewards = itemService.randGiftReward(itemId, count);
                }
                break;
            case GUILD_EXP: {
                reward = GuildExpReward.valueOf(itemId, count);
                break;
            }
            case GUILD_ACTIVE: {
                reward = GuildActiveReward.valueOf(itemId, count);
                break;
            }
            default:
				logger.error("parseRewards failed, rewardType not found : {}, command:{}", rewardType, RequestContext.getCommand());
				break;
        }
        if (reward == null && rewards == null) {
            logger.error("parseReward failed..., userId:{}, rewardType:{}, configId : {}, count :{}, command:{}", RequestContext.getUserId(), copyList.get(0), rewardConfig.get(1), rewardConfig.get(2), RequestContext.getCommand());
            return result;
        }

        if (reward != null) {
            result.add(reward);
        }
        if (rewards != null && !rewards.isEmpty()) {
            result.addAll(rewards);
        }
        return result;
    }

    /**
     * 奖励类型
     *
     * @param config
     */
    public List<Integer> checkRewardConfig(List<Integer> config) {
        if (config.size() < 3) {
            int itemId = config.get(0);
            int count = config.get(1);
            int type = gameConfigManager.getItemConfig().getItemEntity(itemId).getItemType();
            config.clear();
            config.add(type);
            config.add(itemId);
            config.add(count);
        }
        return config;
    }

    @Override
    public List<List<Integer>> combineRewards(List<List<Integer>> data) {
        List<List<Integer>> rewards = CollectionUtils.copyM(data);
        // 合并奖励
        List<List<Integer>> results = new ArrayList<>(rewards.size());
        boolean matched = false;
        for (int i = 0, length = rewards.size(); i < length; i++) {
            matched = false;

            List<Integer> reward1 = this.checkRewardConfig(rewards.get(i));
            if (reward1.get(0) == -1) {
                continue;
            }

            for (int j = 0, jLength = results.size(); j < jLength; j++) {
                List<Integer> reward2 = this.checkRewardConfig(results.get(j));
                if (reward1.get(1).equals(reward2.get(1))) {
                    results.get(j).set(2, reward1.get(2) + reward2.get(2));
                    matched = true;
                    break;
                }
            }

            if (!matched) {
                results.add(reward1);
            }
        }

        rewards.clear();
        rewards.addAll(results);
        return rewards;
    }

    /**
     * 合并奖励
     *
     * @param rewards
     * @return
     */
    public void combineRewards(final Collection<Reward> rewards) {
        if (rewards == null || rewards.isEmpty()) {
            return;
        }

        List<Reward> results = new ArrayList<>(rewards.size());
        boolean matched = false;
        for (Reward reward : rewards) {
            matched = false;
            for (Reward rw : results) {
                if (rw.match(reward)) {
                    rw.union(reward);
                    matched = true;
                    break;
                }
            }
            if (!matched) {
                results.add(reward);
            }
        }

        rewards.clear();
        rewards.addAll(results);
    }

    /**
     * 执行奖励入库操作
     *
     * @param userId
     * @param rewardAction
     * @param resultSet
     */
    private void doExecuteReward(long userId, RewardAction rewardAction, RewardResultSet resultSet) {
        RewardProcessor processor = getProcessor(rewardAction.getReward());
        RewardResult<?> rewardResult = processor.executeReward(userId, rewardAction);
        if (rewardResult == null) {
            resultSet.setResultCode(ErrorCode.UPDATE_DATA_FAILED);
        } else {
            resultSet.addRewardResult(rewardResult);
        }
    }

    /**
     * 执行奖励入库操作 批量插入数据库
     *
     * @param userId
     * @param rewardType
     * @param rewardActionList
     * @param rewardResultSet
     */
    private void doExecuteRewardsBatch(long userId, RewardType rewardType, List<RewardAction> rewardActionList, RewardResultSet rewardResultSet) {
        RewardProcessor processor = REWARD_PROCESSORS.get(rewardType);
        RewardResult<?> rewardResult = processor.executeRewards(userId, rewardActionList);
        if (rewardResult == null) {
            rewardResultSet.setResultCode(ErrorCode.UPDATE_DATA_FAILED);
        } else {
            rewardResultSet.addRewardResult(rewardResult);
        }
    }


    /**
     * 奖励处理器
     */
    private RewardProcessor getProcessor(Reward reward) {
        RewardProcessor processor = null;
        if (reward.getType() == RewardType.RESOURCE) {
            processor = REWARD_REOURCE_PROCESSORS.get(reward.getResourceType());
        } else {
            processor = REWARD_PROCESSORS.get(reward.getType());
        }

        if (processor == null) {
            logger.error("没有对应 [{}, {}] 类型的奖励处理器！", reward.getType(), reward.getResourceType());
        }
        return processor;
    }

    @Override
    public List<List<Integer>> stringToRewardList(String str) {
        List<List<Integer>> rewardConfig = new ArrayList<>();
        String[] split = str.split(Symbol.SHUXIAN);
        for (String s : split) {
            String[] split1 = s.split(Symbol.DOUHAO);
            List<Integer> data = new ArrayList<>();
            for (String s1 : split1) {
                data.add(Integer.parseInt(s1));
            }
            rewardConfig.add(data);
        }
        return rewardConfig;
    }

    @PostConstruct
    private void init() {
        Map<String, RewardProcessor> processorMap = this.applicationContext.getBeansOfType(RewardProcessor.class);
        for (RewardProcessor processor : processorMap.values()) {
            RewardProcessor existsProcessor = null;
            if (processor.getType() == RewardType.RESOURCE) {
                existsProcessor = REWARD_REOURCE_PROCESSORS.put(processor.getRewardResourceType(), processor);
            } else {
                existsProcessor = REWARD_PROCESSORS.put(processor.getType(), processor);
            }

            if (existsProcessor != null) {
                logger.error("存在重复的奖励处理器 : {}", processor.getType());
            }
        }
    }

}

























package com.dxx.game.modules.activity.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.activity.Flip;
import com.dxx.game.dto.FlipProto;

public interface FlipService {
    Result<FlipProto.FlipOnOpenResponse> onOpen(FlipProto.FlipOnOpenRequest params);

    Result<FlipProto.FlipAccRewardResponse> accReward(FlipProto.FlipAccRewardRequest params);

    Result<FlipProto.FlipBuyStepResponse> buyStep(FlipProto.FlipBuyStepRequest params);

    Result<FlipProto.FlipShowGridResponse> showGrid(FlipProto.FlipShowGridRequest params);

    Result<FlipProto.FlipRewardGridResponse> rewardGrid(FlipProto.FlipRewardGridRequest params);

    Result<FlipProto.FlipClueGridResponse> clueGrid(FlipProto.FlipClueGridRequest params);

    Result<FlipProto.FlipBombGridResponse> bombGrid(FlipProto.FlipBombGridRequest params);

    Result<FlipProto.FlipMapFindSpecialResponse> hasSpecila(FlipProto.FlipMapFindSpecialRequest params);

    Result<FlipProto.FlipAllAccRewardResponse> allAccReward(FlipProto.FlipAllAccRewardRequest params);

    void onAddClue(long userId, int itemId, int count, boolean b);
}

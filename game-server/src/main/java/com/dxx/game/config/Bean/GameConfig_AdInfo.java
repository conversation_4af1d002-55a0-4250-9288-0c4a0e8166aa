
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class GameConfig_AdInfo {
    public GameConfig_AdInfo(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Notes = _buf.get("Notes").getAsString();
        Count = _buf.get("Count").getAsInt();
        Refresh = _buf.get("Refresh").getAsInt();
        CD = _buf.get("CD").getAsInt();
    }

    public static GameConfig_AdInfo deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.GameConfig_AdInfo(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 备注
     */
    public final String Notes;
    /**
     * 次数
     */
    public final int Count;
    /**
     * 刷新时间(单位分钟)
     */
    public final int Refresh;
    /**
     * cd时间单位分钟
     */
    public final int CD;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Notes:" + Notes + ","
        + "Count:" + Count + ","
        + "Refresh:" + Refresh + ","
        + "CD:" + CD + ","
        + "}";
    }
}


package com.dxx.game.common.aws.xray;

import com.amazonaws.xray.AWSXRay;
import com.amazonaws.xray.AWSXRayRecorder;
import com.amazonaws.xray.entities.Segment;
import com.amazonaws.xray.entities.TraceHeader;
import com.amazonaws.xray.entities.TraceHeader.SampleDecision;
import com.amazonaws.xray.entities.TraceID;
import com.amazonaws.xray.strategy.sampling.SamplingResponse;
import com.amazonaws.xray.strategy.sampling.SamplingStrategy;
import com.dxx.game.common.log.DxxLogConsoleAppender;
import com.dxx.game.common.utils.IpUtils;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpHeaders;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class XRayFilter {

    private boolean enabled = false;

    private final double DEFAULT_SAMPLING_RATE = 1.0; // 默认全采样
    private double samplingRate = DEFAULT_SAMPLING_RATE;


    public XRayFilter() {
        // 如果没有设置 AWS_XRAY_ENABLED，则默认为 true
        String awsXrayEnabled = System.getenv("AWS_XRAY_ENABLED");
        if (awsXrayEnabled != null) {
            enabled = Boolean.parseBoolean(awsXrayEnabled);
        }


        // 设置采样率
        String awsXraySamplingRate = System.getenv("AWS_XRAY_SAMPLING_RATE");
        if (awsXraySamplingRate != null) {
            try {
                samplingRate = Double.parseDouble(awsXraySamplingRate);
                if (samplingRate < 0.0 || samplingRate > 1.0) {
                    log.error("Invalid sampling rate. It should be a decimal number between 0 and 1.");
                    samplingRate = DEFAULT_SAMPLING_RATE;
                }
            } catch (NumberFormatException e) {
                log.error("Invalid sampling rate format. It should be a decimal number between 0 and 1.", e);
            }
        }
    }


    public Segment preFilter(ChannelHandlerContext ctx, FullHttpRequest request) {
        if (!enabled) {
            return null;
        }

        AWSXRayRecorder recorder = AWSXRay.getGlobalRecorder();

        Optional<TraceHeader> incomingHeader = getTraceHeader(request);

//        incomingHeader.ifPresent(traceHeader -> log.info("Incoming trace header received: {}", traceHeader));

        SamplingResponse samplingResponse = fromSamplingStrategy(request);

        SampleDecision sampleDecision = incomingHeader.map(TraceHeader::getSampled).orElseGet(() -> getSampleDecision(samplingResponse));

        // 使用环境变量控制采样率
        if (Math.random() > samplingRate) {
            sampleDecision = SampleDecision.NOT_SAMPLED;
        } else if (SampleDecision.REQUESTED.equals(sampleDecision) || SampleDecision.UNKNOWN.equals(sampleDecision)) {
            sampleDecision = getSampleDecision(samplingResponse);
        }

        TraceID traceId = null;
        String parentId = null;
        if (incomingHeader.isPresent()) {
            TraceHeader header = incomingHeader.get();
            traceId = header.getRootTraceId();
            parentId = header.getParentId();
        }

        final Segment created;
        if (SampleDecision.SAMPLED.equals(sampleDecision)) {
            String segmentName = getSegmentName(request);
            created = traceId != null
                    ? recorder.beginSegment(segmentName, traceId, parentId)
                    : recorder.beginSegment(segmentName);
            if (samplingResponse.getRuleName().isPresent()) {
                created.setRuleName(samplingResponse.getRuleName().get());
            }
        } else {
            String segmentName = getSegmentName(request);
            SamplingStrategy samplingStrategy = recorder.getSamplingStrategy();
            if (samplingStrategy.isForcedSamplingSupported()) {
                created = traceId != null
                        ? recorder.beginSegment(segmentName, traceId, parentId)
                        : recorder.beginSegment(segmentName);
                created.setSampled(false);
            } else {
               log.info("Creating Dummy Segment");
                created = traceId != null ? recorder.beginNoOpSegment(traceId) : recorder.beginNoOpSegment();
            }
        }

        Map<String, Object> requestAttributes = new HashMap<>();
        requestAttributes.put("url", request.uri());
        requestAttributes.put("method", request.method().name());

        HttpHeaders headers = request.headers();
        if (headers.contains("User-Agent")) {
            requestAttributes.put("user_agent", headers.get("User-Agent"));
        }

        if (headers.contains("X-Forwarded-For")) {
            requestAttributes.put("client_ip", headers.get("X-Forwarded-For"));
            requestAttributes.put("x_forwarded_for", true);
        } else {
            requestAttributes.put("client_ip", IpUtils.getRemoteIp(ctx, request));
        }

        created.putHttp("request", requestAttributes);

        return created;
    }


    public void postFilter(Segment segment) {
        if (!enabled) {
            return;
        }
        if (null != segment) {
            Map<String, Object> responseAttributes = new HashMap<>();

            String responseCodeStr = MDC.get(DxxLogConsoleAppender.RESP_CODE);
            int commandResponseCode = responseCodeStr == null ? 0 : Integer.parseInt(responseCodeStr);
            responseAttributes.put("command_response_code", commandResponseCode);


//            String requestBodyLenStr = MDC.get(DxxLogConsoleAppender.REQUEST_BODY_LEN);
//            Optional<Integer> contentLength = requestBodyLenStr == null ? Optional.empty() : Optional.of(Integer.parseInt(requestBodyLenStr));
//            contentLength.ifPresent(integer -> responseAttributes.put("content_length", integer));

            segment.putHttp("response", responseAttributes);
            AWSXRayRecorder recorder = AWSXRay.getGlobalRecorder();
            recorder.endSegment();
        }
    }

    private Optional<TraceHeader> getTraceHeader(FullHttpRequest request) {
        String headerValue = request.headers().get(TraceHeader.HEADER_KEY);
        if (headerValue != null) {
            return Optional.of(TraceHeader.fromString(headerValue));
        }
        return Optional.empty();
    }

    private String getSegmentName(FullHttpRequest request) {
        return "Game-Server";
    }

    private SamplingResponse fromSamplingStrategy(FullHttpRequest request) {
        return new SamplingResponse(true, "all");
    }

    private SampleDecision getSampleDecision(SamplingResponse samplingResponse) {
        return samplingResponse.isSampled() ? SampleDecision.SAMPLED : SampleDecision.NOT_SAMPLED;
    }

}

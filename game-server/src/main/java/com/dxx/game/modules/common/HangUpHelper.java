package com.dxx.game.modules.common;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.CardPermission;
import com.dxx.game.consts.VipPermission;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO
 */
@Component
public class HangUpHelper {
    @Autowired
    private CommonService commonService;
    @Autowired
    private DropService dropService;
    @Autowired
    private GameConfigManager gameConfigManager;
//    @Autowired
//    private VIPService vipService;
//    @Autowired
//    private RelicService relicService;
    @Autowired
    private UserService userService;

    /**
     * 计算挂机奖励资源
     */
    public List<Integer> calcHangUpItems(int seconds, int count, int type, int itemId) {
        long userId = RequestContext.getUserId();

        if (count == 0) {
            return null;
        }
        int diff = calcHangUpItemCount(seconds, type);
        if (diff <= 0) {
            return null;
        }

//        int amount = Math.round(diff * count);
        long amount = (long) diff * count;

        if(itemId == 1) {
            // vip权限检查
            double per = 0;
            double vpv = userService.vipPermissionValueCheck(userId, VipPermission.PERMISSION_3);
            if(vpv > 0) {
                per = per + vpv;
            }

            Double cpv = userService.cardPermissionValueCheck(userId, CardPermission.PERMISSION_1);
            if(cpv != null && cpv > 0) {
                per = per + cpv;
            }

            if(per > 0) {
                amount = (long) Math.floor(amount * (1 + per));
            }
        }

        if(amount >= Integer.MAX_VALUE) {
            return null;
        }

        //TODO
//        if (itemId == 1) {
//            float vip = vipService.vipCoinsAdd();
//            float relic = relicService.getRelicCoinsAdd() / 10000F;
//
//            float add = vip + relic;
//
//            amount = Math.round(amount * add);
//        } else if (itemId == 3) {
//            float vip = vipService.vipDustAdd();
//            float relic = relicService.getRelicDustAdd() / 10000F;
//
//            float add = vip + relic;
//
//            amount = Math.round(amount * add);
//        } else if (itemId == 4) {
//            float vip = vipService.vipHeroExpAdd();
//            float relic = relicService.getRelicHeroExpAdd() / 10000F;
//
//            float add = vip + relic;
//
//            amount = Math.round(amount * add);
//        }

        int itemType = gameConfigManager.getItemConfig().getItemEntity(itemId).getItemType();
        List<Integer> result = new ArrayList<>(3);
        result.add(itemType);
        result.add(itemId);
        result.add((int) amount);
        return result;
    }

    public int calcHangUpItemCount(int seconds, int type) {
        int diffSeconds = commonService.getGameConfigIntValue(type);
        return (int) Math.floor((float) seconds / diffSeconds);
    }
}

const fs = require("fs");
const path = require("path");

var arguments = process.argv.splice(2);
let env = "test";
if (arguments.length > 0) {
	env = arguments[0];
}

// 读取配置文件
const conifg = fs.readFileSync(path.join(__dirname, "../../config.json"), 'utf-8');
const conifgObj = JSON.parse(conifg);

// 游戏名
var game = conifgObj["game"];


const javaResourcesPath = path.join(__dirname, "../../../src/main/resources/" + env);
const properties = fs.readFileSync(javaResourcesPath + "/application.properties", 'utf-8');
const javaDaoPath = path.join(__dirname, "../../../src/main/java/com/dxx/game/dao/dynamodb/model");
const applicationEnv = getPropertiesValueByKey(properties, "spring.application.env");
const applicationName = getPropertiesValueByKey(properties, "spring.application.name");

if (applicationName != game) {
	console.log("spring.application.name name error");
	process.exit(1);
}



var result = "";

var tableNames = [];


traverseDirectory(javaDaoPath);



function traverseDirectory(dir) {
	const files = fs.readdirSync(dir); // 读取目录下的所有文件和子目录
    files.forEach(file => {
        const filePath = path.join(dir, file); // 获取当前文件的完整路径
        if (fs.statSync(filePath).isDirectory()) { // 判断是否为子目录
            traverseDirectory(filePath); // 如果是子目录，则递归遍历该目录
        } else if (path.extname(filePath) === '.java') { // 判断是否为Java文件
            var content = fs.readFileSync(filePath, 'utf-8');
			var lines = content.split("\n");
			for (var j = 0; j < lines.length; j ++) {
				var line = lines[j];
				if (line.indexOf("@DynamoDBTableName") >= 0) {
					var tableName = line.split("(\"")[1].replace("\")", "").trim();

					if (tableNames.includes(tableName)) {
						continue;
					}

					tableNames.push(tableName);

					// result += tableName + "=" + env + tableName + "\r\n";

					result += tableName + "=";

					if (applicationEnv != "dev") {
						result += env + "-";
					}

					result += game + "-" + tableName + "\r\n";

				}
			}
        }
    });
}


var fd = fs.openSync(javaResourcesPath + "/application-dynamodb.properties",'w');
fs.writeSync(fd, result, 0, "utf-8");
fs.closeSync(fd);


console.log(result);


function getPropertiesValueByKey(properties, key) {
	var lines = properties.split("\n");
	for (var i = 0; i < lines.length; i ++) {
		var line = lines[i];
		var array = line.split("=");
		if (array[0] == key) {
			return array[1].replace("\r", "");
		}
	}
	return "";
}

// console.log(files);
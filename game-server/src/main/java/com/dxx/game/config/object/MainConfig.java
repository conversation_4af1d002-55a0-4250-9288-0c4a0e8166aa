package com.dxx.game.config.object;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.config.entity.AppVersion;
import com.dxx.game.config.entity.ForceUpdate;
import com.dxx.game.config.entity.GmServerApi;
import com.dxx.game.config.entity.ServerMaintain;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/26 15:37
 */
@Component
@Getter
public class MainConfig {

    /** 版本配置 **/
    private List<AppVersion> appVersions = new ArrayList<>();
    /** 针对设备进行版本强更提示 **/
    private ForceUpdate forceUpdate;
    /** 服务器状态配置 **/
    private ServerMaintain serverMaintainConfig;

    // api 请求日志白名单
    private List<Long> apiLogWhiteUserIds;
    // 神装的ID前缀
    private List<Integer> divineEquipIds;

    private Map<String, String> sensitiveWords;

    /** 邮件服务器配置*/
    private EmailApiConfig emailApiConfig;

//    private GmServerApi gmServerApi;

    /** oneStore api 配置 **/
//    private OneStoreApiConfig oneStoreApiConfig;
    /** google api 配置 **/

    private GorillaEmailApiConfig gorillaEmailApiConfig;

    public void setUp(String strMainJson) {
        JSONObject mainObj = JSONObject.parseObject(strMainJson);
        appVersions = JSONObject.parseArray(mainObj.getString("api_version"), AppVersion.class);
        forceUpdate = JSON.parseObject(mainObj.getString("force_update"), ForceUpdate.class);
        serverMaintainConfig = JSONObject.parseObject(mainObj.getString("server_maintain"), ServerMaintain.class);
        apiLogWhiteUserIds = JSONObject.parseArray(mainObj.getString("api_log_white_list"), Long.class);
        divineEquipIds = JSONObject.parseArray(mainObj.getString("divine_equip_ids"), Integer.class);

//        gmServerApi = JSONObject.parseObject(mainObj.getString("gm_server_api"), GmServerApi.class);

        sensitiveWords = mainObj.getObject("sensitive_words", new TypeReference<Map<String, String>>(){});

        emailApiConfig = JSONObject.parseObject(mainObj.getString("email_api"),EmailApiConfig.class);


        Map<String, GorillaEmailApiConfig.GorillaEmailApi> gorillaEmailApiMap = JSONObject.parseObject(mainObj.getString("gorilla_email_api"),
                        new TypeReference<Map<String, GorillaEmailApiConfig.GorillaEmailApi>>() {});
        gorillaEmailApiConfig = new GorillaEmailApiConfig();
        gorillaEmailApiConfig.setGorillaEmailApiMap(gorillaEmailApiMap);

//        oneStoreApiConfig = JSONObject.parseObject(mainObj.getString("onestore_api"), OneStoreApiConfig.class);
//        googleApiConfig = JSONObject.parseObject(mainObj.getString("google_api"), GoogleApiConfig.class);
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildEvent {
    public Guild_guildEvent(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        name = _buf.get("name").getAsInt();
        eventLanguage = _buf.get("eventLanguage").getAsInt();
        eventAtlas = _buf.get("eventAtlas").getAsString();
        eventShow = _buf.get("eventShow").getAsString();
        eventSort = _buf.get("eventSort").getAsInt();
    }

    public static Guild_guildEvent deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildEvent(_buf);
    }

    /**
     * Id
     */
    public final int id;
    /**
     * 活动名称多语言
     */
    public final int name;
    /**
     * 描述文本
     */
    public final int eventLanguage;
    /**
     * 图集
     */
    public final String eventAtlas;
    /**
     * 入口图片
     */
    public final String eventShow;
    /**
     * 排序
     */
    public final int eventSort;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "name:" + name + ","
        + "eventLanguage:" + eventLanguage + ","
        + "eventAtlas:" + eventAtlas + ","
        + "eventShow:" + eventShow + ","
        + "eventSort:" + eventSort + ","
        + "}";
    }
}


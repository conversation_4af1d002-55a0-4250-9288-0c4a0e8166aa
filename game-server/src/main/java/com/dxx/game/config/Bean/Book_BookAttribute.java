
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Book_BookAttribute {
    public Book_BookAttribute(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        CharacterId = _buf.get("CharacterId").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Attribute").getAsJsonArray(); Attribute = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Attribute.add(_v0); }   }
    }

    public static Book_BookAttribute deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Book_BookAttribute(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 角色id
     */
    public final int CharacterId;
    /**
     * 属性加成(激活次数,属性|)
     */
    public final java.util.List<String> Attribute;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "CharacterId:" + CharacterId + ","
        + "Attribute:" + Attribute + ","
        + "}";
    }
}


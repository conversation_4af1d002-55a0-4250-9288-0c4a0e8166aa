package com.dxx.game.modules.reward.model;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * 装备
 * <AUTHOR>
 * @date 2019-12-13 20:42
 */
public class EquipReward implements Reward {

    /**
     * 奖励类型   {@link RewardType}
     */
    private RewardType type = RewardType.EQUIP;

    /**
     * 奖励子类型   {@link RewardResourceType}
     */
    private RewardResourceType resourceType = RewardResourceType.NONE;

    /**
     * 数量
     */
    private int count = 0;

    /**
     * 配置表ID
     */
    private int configId = 0;
    /**
     * 等级
     */
    private int level = 0;

    /**
     * 唯一ID
     */
    private int rowId=0;

    public EquipReward() {

    }

    public EquipReward(int configId, int count, int level) {
        this.configId = configId;
        this.count = count;
        this.level = level;
    }

    public EquipReward(int configId, int count, int level, int rowId) {
        this.configId=configId;
        this.count=count;
        this.level=level;
        this.rowId=rowId;
    }

    public static EquipReward valueOf(int configId,int count,int level,int rowId) {
        return new EquipReward(configId,count,level,rowId);
    }

    public static EquipReward valueOf(int configId, int count, int level) {
        return new EquipReward(configId, count, level);
    }

    public static EquipReward valueOf(int configId, int count) {
        return new EquipReward(configId, count, 0);
    }

    @Override
    public RewardType getType() {
        return this.type;
    }

    @Override
    public RewardResourceType getResourceType() {
        return this.resourceType;
    }

    @Override
    public int getCount() {
        return this.count;
    }

    public int getLevel() {
        return level;
    }

    public int getRowId() {
        return rowId;
    }

    @Override
    public Reward increase(int incrCount) {
        this.count += incrCount;
        return this;
    }

    @Override
    public int getConfigId() {
        return this.configId;
    }

    @Override
    public Reward union(Reward reward) {
        if (match(reward)) {
            this.count += reward.getCount();
        }
        return this;
    }

    @Override
    public boolean match(Reward reward) {
        return false;
//		if (!(reward instanceof EquipReward)) {
//			return false;
//		}
//		return this.configId == reward.getConfigId();
    }




}



















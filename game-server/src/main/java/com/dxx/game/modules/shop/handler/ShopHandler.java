package com.dxx.game.modules.shop.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.PayProto;
import com.dxx.game.dto.ShopProto.*;
import com.dxx.game.modules.shop.service.ShopService;
import com.google.protobuf.Message;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class ShopHandler {
	
	@Resource
	private ShopService shopService;

	@ApiMethod(command = MsgReqCommand.ShopGetInfoRequest, name = "shop-获取信息")
	public Result<ShopGetInfoResponse> getInfo(Message msg) {
		ShopGetInfoRequest params = (ShopGetInfoRequest)msg;
		return shopService.getInfoAction(params);
	}


	@ApiMethod(command = MsgReqCommand.ShopFreeIAPItemRequest, name = "shop-免费")
	public Result<ShopFreeIAPItemResponse> free(Message msg) {
		ShopFreeIAPItemRequest params = (ShopFreeIAPItemRequest)msg;
		return shopService.free(params);
	}

	@ApiMethod(command = MsgReqCommand.ShopDoGachaRequest, name = "shop-抽卡", skipIdempotent = false)
	public Result<ShopDoGachaResponse> doGacha(Message msg) {
		ShopDoGachaRequest params = (ShopDoGachaRequest)msg;
		return shopService.doGachaAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ShopIntegralGetInfoRequest, name = "积分商店-获取数据")
	public Result<ShopIntegralGetInfoResponse> integralGetInfo(Message msg) {
		ShopIntegralGetInfoRequest params = (ShopIntegralGetInfoRequest)msg;
		return shopService.integralGetInfoAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ShopIntegralRefreshRequest, name = "积分商店-刷新道具")
	public Result<ShopIntegralRefreshResponse> refreshIntegral(Message msg) {
		ShopIntegralRefreshRequest params = (ShopIntegralRefreshRequest)msg;
		return shopService.refreshIntegralAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ShopIntegralBuyItemRequest, name = "积分商店-购买道具")
	public Result<ShopIntegralBuyItemResponse> integralBuyItem(Message msg) {
		ShopIntegralBuyItemRequest params = (ShopIntegralBuyItemRequest)msg;
		return shopService.integralBuyItemAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ShopGacheWishRequest, name = "商店-抽卡-保存许愿列表")
	public Result<ShopGacheWishResponse> gachaWish(Message msg) {
		ShopGacheWishRequest params = (ShopGacheWishRequest)msg;
		return shopService.gachaWishAction(params);
	}


	@ApiMethod(command = MsgReqCommand.MonthCardGetRewardRequest, name = "月卡-领取奖励")
	public Result<MonthCardGetRewardResponse> monthCardGetReward(Message msg) {
		MonthCardGetRewardRequest params = (MonthCardGetRewardRequest) msg;
		return shopService.monthCardGetReward(params);
	}



	@ApiMethod(command = MsgReqCommand.VIPLevelRewardRequest, name = "VIP-领取VIP等级奖励")
	public Result<VIPLevelRewardResponse> vipLevelReward(Message msg) {
		VIPLevelRewardRequest params = (VIPLevelRewardRequest) msg;
		return shopService.vipLevelReward(params);
	}



	@ApiMethod(command = MsgReqCommand.FirstRechargeRewardRequest, name = "领取首充礼包")
	public Result<FirstRechargeRewardResponse> firstRechargeReward(Message msg) {
		FirstRechargeRewardRequest params = (FirstRechargeRewardRequest) msg;
		return shopService.firstRechargeReward(params);
	}
}
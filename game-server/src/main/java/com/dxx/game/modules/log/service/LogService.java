package com.dxx.game.modules.log.service;

import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.modules.reward.result.RewardResultSet;

import java.util.List;

/**
 * 日志接口
 * <AUTHOR>
 * @date 2019-12-14 15:03
 */
public interface LogService {

	/**
	 * 流水日志
	 * @param userId
	 * @param command
	 * @param transId
	 * @param extra
	 */
	public void sendBasic(long userId, short command, long transId, String extra);
	public void sendBasic(long userId, short command, long transId, int customType, String extra);
	/**
	 * 发送金币日志
	 * @param amount
	 * @param totalAmount
	 */
	void sendCoin(long userId, short command, long transId, int amount, long totalAmount);

	/**
	 * 发送钻石日志
	 * @param amount
	 * @param totalAmount
	 */
	void sendDiamond(long userId, short command, long transId, int amount, long totalAmount);

	void sendHeros(long userId, short command, long transId, List<Hero> heros, boolean isReceive);

    /**
	 * 发送道具日志
	 * @param itemId
	 * @param itemNum
	 * @param totalNum
	 */
    void sendItem(long userId, short command, long transId, int itemId, int itemNum, long totalNum);
    public void sendItems(long userId, short command, long transId, List<Item> items);

	/**
	 * 发送装备日志
	 * @param equipId
	 * @param equipNum
	 */
	public void sendEquip(long userId, short command, long transId, long equipRowId, int equipId, int equipNum, int level);
	public void sendEquips(long userId, short command, long transId, List<Equip> equips, boolean isReceive);

	/**
	 * 发送日志
	 * @param userId
	 * @param command
	 * @param transId
	 */
	public void sendLog(RewardResultSet rewardResultSet, long userId, short command, long transId);
}





















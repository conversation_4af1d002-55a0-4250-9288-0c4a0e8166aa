package com.dxx.game.modules.user.service;

import com.dxx.game.dao.dynamodb.model.Account;
import com.dxx.game.dto.UserProto;

/**
 * @author: lsc
 * @createDate: 2025/5/22
 * @description:
 */
public interface AccountService {

    /**
     * 验证登录信息
     * @param channelId
     * @param accountId
     * @param verification
     * @return
     */
    boolean verifyLogin(int channelId, String accountId, String verification);

    /**
     * 登录
     * @param loginRequest
     * @return
     */
    Account accountLogin(UserProto.UserLoginRequest loginRequest);

    /**
     * 获取账号数据
     * @param accountId
     * @param accountId2
     * @param deviceId
     * @return
     */
    Account getAccount(String accountId, String accountId2, String deviceId);

    /**
     * 重置账号
     * @param account
     */
    void resetAccount(Account account);
}

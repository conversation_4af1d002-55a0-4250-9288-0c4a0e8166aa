package com.dxx.game.modules.equip.support;

import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.Equip;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class EquipSupport {

    /**
     * 英雄卸下装备
     * @param heroRowId 英雄唯一id
     * @param equip     装备实体
     */
    public void downEquipment(long heroRowId, Equip equip) {
        equip.setHeroRowId(0L);
    }

    /**
     * 英雄戴上装备
     *
     * @param heroRowId 英雄唯一id
     * @param equip     装备实体
     * @return
     */
    public void upEquipment(long heroRowId, Equip equip) {
        equip.setHeroRowId(heroRowId);
    }
}

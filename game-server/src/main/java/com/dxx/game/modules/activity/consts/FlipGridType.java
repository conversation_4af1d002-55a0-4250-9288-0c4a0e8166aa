package com.dxx.game.modules.activity.consts;


import com.dxx.game.config.entity.eventflipcard.FlipBaseEntity;
import com.dxx.game.config.entity.eventflipcard.FlipMapEntity;

/**
 * 翻牌格子
 *
 * <AUTHOR>
 * @date 2023/7/17 16:28
 */
public enum FlipGridType {
    eNormalGrid1(1, "红色普通格子") {
        @Override
        public boolean isNormalGrid() {
            return true;
        }

        @Override
        public Integer getItemId(FlipBaseEntity flipBaseEntity) {
            return flipBaseEntity.getFlipStones().get(0);
        }
    },
    eNormalGrid2(2, "蓝色普通格子") {
        @Override
        public boolean isNormalGrid() {
            return true;
        }

        @Override
        public Integer getItemId(FlipBaseEntity flipBaseEntity) {
            return flipBaseEntity.getFlipStones().get(1);
        }
    },
    eNormalGrid3(3, "绿色普通格子") {
        @Override
        public boolean isNormalGrid() {
            return true;
        }

        @Override
        public Integer getItemId(FlipBaseEntity flipBaseEntity) {
            return flipBaseEntity.getFlipStones().get(2);
        }
    },
    eNormalGrid4(4, "紫色普通格子") {
        @Override
        public boolean isNormalGrid() {
            return true;
        }

        @Override
        public Integer getItemId(FlipBaseEntity flipBaseEntity) {
            return flipBaseEntity.getFlipStones().get(3);
        }
    },
    eClue(5, "线索格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return 1;
        }

        @Override
        public Integer getItemId(FlipBaseEntity flipBaseEntity) {
            return flipBaseEntity.getClueItem();
        }

        @Override
        public boolean isClueGrid() {
            return true;
        }
    },
    eSpecial(6, "特殊奖励格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getSpecial();
        }

        @Override
        public boolean isSpecialGrid() {
            return true;
        }
    },
    eStep(7, "步数格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getStep();
        }

        @Override
        public boolean isStepGrid() {
            return true;
        }
    },
    eBomb(8, "炸弹格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getBomb();
        }

        @Override
        public boolean isBombGrid() {
            return true;
        }
    },
    eBombH(9, "横向炸弹格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getBombH();
        }

        @Override
        public boolean isBombGrid() {
            return true;
        }
    },
    eBombV(10, "纵向炸弹格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getBombV();
        }

        @Override
        public boolean isBombGrid() {
            return true;
        }
    },
    eShow(11, "全开格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getShow();
        }

        @Override
        public boolean isAllShowGrid() {
            return true;
        }
    },
    ePartShow(12, "部分开（5个）格子") {
        @Override
        public int getCfgNum(FlipMapEntity flipMapEntity) {
            return flipMapEntity.getPartShow();
        }

        @Override
        public boolean isPartShowGrid() {
            return true;
        }
    },
    eCollectGrid1(13, "收集红色普通格子") {
        @Override
        public boolean isCollectGrid() {
            return true;
        }

        @Override
        public FlipGridType getCollectType() {
            return FlipGridType.eNormalGrid1;
        }
    },
    eCollectGrid2(14, "收集蓝色普通格子") {
        @Override
        public boolean isCollectGrid() {
            return true;
        }

        @Override
        public FlipGridType getCollectType() {
            return FlipGridType.eNormalGrid2;
        }
    },
    eCollectGrid3(15, "收集绿色普通格子") {
        @Override
        public boolean isCollectGrid() {
            return true;
        }

        @Override
        public FlipGridType getCollectType() {
            return FlipGridType.eNormalGrid3;
        }
    },
    eCollectGrid4(16, "收集紫色普通格子") {
        @Override
        public boolean isCollectGrid() {
            return true;
        }

        @Override
        public FlipGridType getCollectType() {
            return FlipGridType.eNormalGrid4;
        }
    },
    ;

    private int type;
    private String name;

    FlipGridType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public int getCfgNum(FlipMapEntity flipMapEntity) {
        return 0;
    }

    public boolean isNormalGrid() {
        return false;
    }

    public boolean isAllShowGrid() {
        return false;
    }

    public boolean isPartShowGrid() {
        return false;
    }

    public boolean isStepGrid() {
        return false;
    }

    public boolean isSpecialGrid() {
        return false;
    }

    public boolean isClueGrid() {
        return false;
    }

    public boolean isBombGrid() {
        return false;
    }

    public boolean isCollectGrid() {
        return false;
    }

    public FlipGridType getCollectType() {
        return null;
    }

    public Integer getItemId(FlipBaseEntity flipBaseEntity) {
        return null;
    }

    public static FlipGridType getFlipGridByType(int type) {
        for (FlipGridType grid : values()) {
            if (grid.getType() == type)
                return grid;
        }

        return null;
    }

}

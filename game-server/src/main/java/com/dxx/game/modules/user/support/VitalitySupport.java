package com.dxx.game.modules.user.support;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.user.service.UserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: lsc
 * @createDate: 2025/6/11
 * @description:
 */
@Component
public class VitalitySupport {

    @Resource
    private UserService userService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private LogService logService;
    @Resource
    private UserDao userDao;

    public void checkVitality(long userId) {
        User user = userService.getUser(userId);
        int maxVitality = this.getMaxVitality(userId);
        if (user.getVitality() >= maxVitality) {
            return;
        }
        int lifeSeconds = Integer.parseInt(gameConfigManager.getGameConfigConfig().getConfigEntity(2002).getValue());
        int addCount = (int) ((DateUtils.getUnixTime() - user.getVitalityTimeStamp()) / lifeSeconds);
        if (addCount <= 0) {
            return;
        }
        int vitality = user.getVitality() + addCount;
        if (vitality > maxVitality) {
            vitality = maxVitality;
            user.setVitalityTimeStamp(DateUtils.getUnixTime());
            addCount = maxVitality - user.getVitality();
        } else {
            user.setVitalityTimeStamp(user.getVitalityTimeStamp() + (long) addCount * lifeSeconds);
        }
        user.setVitality(vitality);
        this.buildFillCommon(user);
        // 记录日志
        if (addCount > 0) {
            long transId = userService.getTransId(user.getUserId());
            logService.sendItem(userId, RequestContext.getCommand(), transId,
                    RewardResourceType.VITALITY.getValue(), addCount, user.getVitality());
        }
    }

    public int getMaxVitality(long userId) {
        return Integer.parseInt(gameConfigManager.getGameConfigConfig().getConfigEntity(2001).getValue());
    }

    public boolean costVitality(long userId, int costValue) {
        User user = userService.getUser(userId);
        if (user.getUserId() != userId) {
            user = userDao.getByUserId(userId);
        }
        this.checkVitality(userId);
        if (user.getVitality() < costValue) {
            return false;
        }

        user.setVitality(user.getVitality() - costValue);
        userDao.updateVitality(user);

        this.buildFillCommon(user);

        // 记录日志
        logService.sendItem(userId, RequestContext.getCommand(),
                userService.getTransId(userId), RewardResourceType.VITALITY.getValue(), -costValue, user.getVitality());
        return true;
    }

    public void buildFillCommon(User user) {
        ResponseContext.addFillCommon("vitality", builder -> {
            builder.setUpdateUserVitality(CommonHelper.buildUpdateUserVitality(user));
        });
    }

}

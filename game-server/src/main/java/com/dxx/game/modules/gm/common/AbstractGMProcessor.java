package com.dxx.game.modules.gm.common;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.modules.gm.annotation.GMCommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
public abstract class AbstractGMProcessor implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(AbstractGMProcessor.class);
    /**
     * 模板方法：执行GM总入口
     *
     * @param params
     * @return
     */
    public Map<String, Object> executeProcessor(JSONObject params) {
        Object data = execute(params);
        if (data instanceof Map) {
            return (Map<String, Object>) data;
        }
        return this.success(data);
    }


    /**
     * 构建成功响应
     */
    public Map<String, Object> success(Object data) {
        return buildResponse(ErrorCode.SUCCESS, "success", data);
    }

    public Map<String, Object> success() {
        return buildResponse(ErrorCode.SUCCESS, "success", null);
    }

    /**
     * 构建错误响应
     */
    public Map<String, Object> error(int code, String msg) {
        return buildResponse(code, msg, null);
    }

    /**
     * 通用响应构建方法
     */
    private Map<String, Object> buildResponse(int code, String msg, Object data) {
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("code", code);
        result.put("msg", msg);
        if (data != null) {
            result.put("data", data);
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() {
        GMCommand gmCmdAnn = this.getClass().getAnnotation(GMCommand.class);
        if (null == gmCmdAnn) {
            throw new RuntimeException("There is no GmCmd annotation on the subtype [" + this.getClass() + "] of BaseGmCommand.class");
        }
        int gmCmd = gmCmdAnn.value();
        if (gmCmd <= 0) {
            throw new RuntimeException("Empty GmCmd annotation value on the subtype [" + this.getClass().getName() + "] of BaseGmCommand.class");
        }
        if (GMManager.getInstance().getBaseGMFunction(gmCmd) != null) {
            throw new RuntimeException("Duplicated GmCmd [" + gmCmd + "]");
        }
        GMManager.getInstance().registerBean(gmCmd, this);
        logger.info("found GMCommand handler [command:{}, handlerClass:{}", gmCmd, this.getClass().getSimpleName());
    }

    /**
     * 执行命令
     *
     * @param params 执行需要的参数
     */
    protected abstract Object execute(JSONObject params);

    private final String NUMBER_PATTERN = "[0-9]+";
    private final Pattern numberPattern = Pattern.compile(NUMBER_PATTERN);

    protected boolean isNumber(String str) {
        return numberPattern.matcher(str).matches();
    }

    protected Map<String, Object> buildKVDM(String key, Object value, String desc, boolean modify) {
        Map<String, Object> data = new HashMap<>();
        data.put("key", key);
        data.put("value", value);
        if (value == null) {
            data.put("value", "");
        }
        data.put("desc", desc);
        data.put("modify", modify);
        return data;
    }
}

package com.dxx.game.consts;

public enum RewardResourceType {
	
	/**
     * 空类型
     */
    NONE(0),
	/**
	 * 金币
	 */
	COINS(1),
	/**
	 * 钻石
	 */
	DIAMONDS(2),

    /**
     * 玩家经验
     */
    EXP(3),

    /**
     * VIP经验
     */
    VIP_EXP(7),

    /**
     * 通行证积分
     */
    BATTLE_PASS_SCORE(8),

    /**
     * 主线体力
     */
    VITALITY(32),
    ;

    /**
     * 类型值
     */
    private final int value;

    private RewardResourceType(int value) {
        this.value = value;
    }
    
    /**
     * 类型值
     *
     * @return int
     */
    public int getValue() {
        return value;
    }
    
    /**
     * 生成 RewardType
     *
     * @param value 类型值
     * @return RewardType
     */
    public static RewardResourceType valueOf(int value) {
        for (RewardResourceType type : RewardResourceType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
}

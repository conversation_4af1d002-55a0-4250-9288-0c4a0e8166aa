package com.dxx.game.common.channel.honor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.honor.model.HonorConfig;
import com.dxx.game.common.channel.huawei.model.HuaWeiConfig;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.utils.CryptUtil;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.Charsets;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Security;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @author: lsc
 * @createDate: 2023/10/26
 * @description:
 */
@Slf4j
@DependsOn("okHttpClient")
@Service
public class HonorService implements ChannelService {

    private static final ChannelID CHANNEL_ID = ChannelID.Honor;

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private AndroidCallBackService androidCallBackService;
    @Autowired
    private RedisService redisService;
    private static final String REDIS_TOKEN_KEY = "honor_access_token";
    private static final String REDIS_ORDER_DATA = "honor_order_data";

    @PostConstruct
    private void init() {
        Security.addProvider(new BouncyCastleProvider());
    }

    // 验证登录信息
    @Override
    public boolean verifyLogin(String accountId, String verification) {
        if (!channelConfig.getHonorConfig().isVerifyLogin()) {
            return true;
        }
        JSONObject jsonObject = JSONObject.parseObject(verification);
        String unionToken = jsonObject.getString("token");

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("token", unionToken);

        Map<String, String> customerHeader = new HashMap<>();
        customerHeader.put("x-union-token", unionToken);
        Map<String, String> requestHeader = getHeaders(customerHeader);

        JSONObject json = new JSONObject(requestBody);
        String bodyStr = json.toString();
        String signStr = String.format("{\"x-ra-timestamp\":\"%s\",\"x-ra-traceid\":\"%s\"}%s", requestHeader.get("x-ra-timestamp"), requestHeader.get("x-ra-traceid"), bodyStr);
        String sign = hmacSha256Sign(signStr);
        requestHeader.put("x-sign-value", sign);

        String response = OkHttpClientUtil.postJson(channelConfig.getHonorConfig().getLoginUrl(), requestHeader, json, String.class);
        JSONObject respObj = JSON.parseObject(response);
        int code = respObj.getIntValue("errorCode");
        if (code != 0) {
            log.error("honor login failed accountId:{}, verification:{}, resp:{}", accountId, verification, respObj);
            return false;
        }
        String openId = respObj.getJSONObject("data").getString("openId");
        if (StringUtils.isEmpty(openId) || !accountId.equals(openId)) {
            return false;
        }
        return true;
    }

    // 登录-获取用户ID
//    public Map<String, Object> login(JSONObject params) {
//        Map<String, Object> result = new HashMap<>();
//        if (!params.containsKey("unionToken")) {
//            result.put("code", -1);
//            result.put("msg", "params error");
//            return result;
//        }
//
//        String unionToken = params.getString("unionToken");
//        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("token", unionToken);
//
//        Map<String, String> customerHeader = new HashMap<>();
//        customerHeader.put("x-union-token", unionToken);
//        Map<String, String> requestHeader = getHeaders(customerHeader);
//
//        JSONObject json = new JSONObject(requestBody);
//        String bodyStr = json.toString();
//        String signStr = String.format("{\"x-ra-timestamp\":\"%s\",\"x-ra-traceid\":\"%s\"}%s", requestHeader.get("x-ra-timestamp"), requestHeader.get("x-ra-traceid"), bodyStr);
//        String sign = hmacSha256Sign(signStr);
//        requestHeader.put("x-sign-value", sign);
//
//        String response = OkHttpClientUtil.postJson(channelConfig.getHonorConfig().getLoginUrl(), requestHeader, json, String.class);
//        JSONObject respObj = JSON.parseObject(response);
//        int code = respObj.getIntValue("errorCode");
//        if (code != 0) {
//            result.put("code", code);
//            result.put("msg", respObj.getString("errorMessage"));
//            return result;
//        }
//
//        JSONObject userInfo = respObj.getJSONObject("data");
//        result.put("code", 0);
//        result.put("userInfo", userInfo);
//
//        JSONObject verificationObj = new JSONObject();
//        verificationObj.put("openId", userInfo.getString("openId"));
//        result.put("verification", CryptUtil.encode(verificationObj.toJSONString()));
//        return result;
//    }

    // 保存订单信息
    public boolean unifiedOrder(long userId, String attach, String cpOrderId) {
        String redisKey = this.getRedisOrderDataKey(userId, cpOrderId);
        redisService.set(redisKey, attach);
        redisService.expireKey(redisKey, 10800, TimeUnit.SECONDS);
        return true;
    }

    // 支付通知回调
    @Override
    public Object payCb(FullHttpRequest request) {
        String body = request.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
        try {
            JSONObject params = JSONObject.parseObject(body);
            PayCbVo payCbVo = this.checkOrderState(request, params);
            if (payCbVo == null) {
                log.error("checkOrderState failed, body:{}", body);
                return this.iapError(1001, "checkOrderState failed");
            }

            String env = params.getString("env");
            payCbVo.setSandBox(false);
            if (env.equals("sandbox")) {
                payCbVo.setSandBox(true);
            }

            // 处理发货
            String purchaseToken = params.getJSONObject("data").getString("purchaseToken");
            return this.doDeliverGoods(payCbVo, purchaseToken, params);
        } catch (Exception e) {
            log.error("payCb failed", e);
            return this.iapError(1000, "payCb failed");
        }
    }

    // 补单
    public Object supplementaryOrder(JSONObject params) {
        log.info("honor supplementaryOrder params:{}", params);
        try {
            JSONArray purchaseList = params.getJSONArray("purchaseList");
            JSONArray sigList = params.getJSONArray("sigList");

            for (int i = 0; i < purchaseList.size(); i ++) {
                String purchaseInfo = purchaseList.getString(i);
                String sign = sigList.getString(i);
                boolean signFlag = this.verifiedSign(purchaseInfo.getBytes(StandardCharsets.UTF_8), Base64.decodeBase64(sign.getBytes(StandardCharsets.UTF_8)), "SHA256withRSA/PSS");
                if (!signFlag) {
                    log.error("honor check sign failed, purchaseInfo:{}", purchaseInfo) ;
                    continue;
                }

                JSONObject purchaseInfoObj = JSONObject.parseObject(purchaseInfo);

                String appId = purchaseInfoObj.getString("appId");
                if (!appId.equals(channelConfig.getHonorConfig().getAppId())) {
                    log.error("honor appId error purchaseInfo:{}", purchaseInfo);
                    continue;
                }
                int purchaseState = purchaseInfoObj.getIntValue("purchaseState");
                if (purchaseState != 0) {
                    log.error("honor purchaseState error purchaseInfo:{}", purchaseInfo);
                    continue;
                }
                String purchaseToken = purchaseInfoObj.getString("purchaseToken");
                PayCbVo payCbVo = this.verifyPurchaseToken(purchaseToken);
                if (payCbVo == null) {
                    log.error("honor verifyPurchaseToken error purchaseInfo:{}", purchaseInfo);
                    continue;
                }
                this.doDeliverGoods(payCbVo, purchaseToken, purchaseInfo);
            }

        } catch (Exception e) {
            log.error("honor supplementaryOrder error params:{}, e:", params, e);
        }
        return this.iapSuccess();
    }

    private Object doDeliverGoods(PayCbVo payCbVo, String purchaseToken, Object params) {
        int code = androidCallBackService.doDeliverGoods(payCbVo);
        if (code != AndroidPayCbErrorCode.SUCCESS) {
            log.error("doDeliverGoods failed, body:{}", params);
            return this.iapError(1001, "doDeliverGoods failed code = " + code);
        }

        // 通知商品消耗
        this.confirmPurchase(purchaseToken, payCbVo.getCpOrderId());
        return this.iapSuccess();
    }

    // 校验订单状态
    private PayCbVo checkOrderState(FullHttpRequest request, JSONObject params) throws Exception {
        String eventType = params.getString("eventType");
        if (!eventType.equals("order success")) {
            log.error("eventType error:{}", params);
            return null;
        }
        String data = params.getString("data");
        String signature = request.headers().get("signature");

        boolean signFlag = this.verifiedSign(data.getBytes(StandardCharsets.UTF_8), Base64.decodeBase64(signature.getBytes(StandardCharsets.UTF_8)), "SHA256withRSA");
        if (!signFlag) {
            log.error("honor check sign failed, params:{}", params) ;
            return null;
        }

        // verifyToken 验证
        JSONObject dataObj = JSONObject.parseObject(data);
        String purchaseToken = dataObj.getString("purchaseToken");
        return this.verifyPurchaseToken(purchaseToken);
    }

    private PayCbVo verifyPurchaseToken(String purchaseToken) {
        // verifyToken 验证
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        requestHeader.put("access-token", this.getAccessToken());
        requestHeader.put("x-iap-appid", channelConfig.getHonorConfig().getAppId());
        requestHeader.put("purchaseToken", purchaseToken);
        String verifyPurchaseTokenResponse = OkHttpClientUtil.postJson(channelConfig.getHonorConfig().getVerifyPurchaseTokenUrl(), requestHeader, String.class);
        JSONObject verifyPurchaseTokenObj = JSONObject.parseObject(verifyPurchaseTokenResponse);
        int code = verifyPurchaseTokenObj.getIntValue("code");
        if (code != 0) {
            log.error("verifyPurchaseTokenError, purchaseToken:{}, resp:{}", purchaseToken, verifyPurchaseTokenResponse);
            return null;
        }
        JSONObject purchaseProductInfo = verifyPurchaseTokenObj.getJSONObject("data").getJSONObject("purchaseProductInfo");

        // 验证appId
        String appId = purchaseProductInfo.getString("appId");
        if (!appId.equals(channelConfig.getHonorConfig().getAppId())) {
            log.error("appIdError, purchaseToken:{}, purchaseProductInfo:{}", purchaseToken, purchaseProductInfo);
            return null;
        }

        // 订单交易状态。0：已购买 1：已退款 2：付款失败 3：退款失败 4：未支付 5: 退款中
        int purchaseState = purchaseProductInfo.getIntValue("purchaseState");
        if (purchaseState != 0) {
            log.error("purchaseStateError, purchaseToken:{}, resp:{}", purchaseToken, purchaseProductInfo);
            return null;
        }

        // developerPayload 透传 userId||extraInfo|preOrderId
        String developerPayload = purchaseProductInfo.getString("developerPayload");
        String orderId = purchaseProductInfo.getString("orderId");
        String cpOrderId = purchaseProductInfo.getString("bizOrderNo");
        String productId = purchaseProductInfo.getString("productId");
        if (StringUtils.isEmpty(developerPayload)) {
            log.error("honor developerPayload is empty, purchaseProductInfo:{} ", purchaseProductInfo);
            return null;
        }
        BigDecimal bigDecimal = new BigDecimal(purchaseProductInfo.getString("price"));
        int amount = bigDecimal.multiply(new BigDecimal("100")).intValue();

        long userId = 0;
        String extraInfo = "";
        long preOrderId = 0;
//        developerPayload = "10001782||23423422323|1699245286000";
        String[] developerPayLoadArr = developerPayload.split("\\|");
        if (developerPayLoadArr.length > 1) {
            userId = Long.parseLong(developerPayLoadArr[0]);
            extraInfo = developerPayLoadArr[2];
            preOrderId =  Long.parseLong(developerPayLoadArr[3]);
        } else {
            userId = Long.parseLong(developerPayload);
            String redisKey = this.getRedisOrderDataKey(userId, cpOrderId);
            String attach = redisService.get(redisKey);
            if (!StringUtils.isEmpty(attach)) {
                PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(attach);
                if (!productId.equals(payBackParamsModel.getProductId())) {
                    log.error("honor productError, purchaseProductInfo:{}, payBackParamsModel:{}", purchaseProductInfo, payBackParamsModel);
                    return null;
                }
                extraInfo = payBackParamsModel.getExtraInfo();
                preOrderId = payBackParamsModel.getPreOrderId();
            }
        }

        PayCbVo payCbVo = new PayCbVo();
        payCbVo.setUserId(userId);
        payCbVo.setExtraInfo(extraInfo);
        payCbVo.setOrderId(orderId);
        payCbVo.setCpOrderId(cpOrderId);
        payCbVo.setPreOrderId(preOrderId);
        payCbVo.setProductId(productId);
        payCbVo.setChannelId(CHANNEL_ID.getId());
        payCbVo.setAmount(amount);
        return payCbVo;
    }

    // 支付通知sign校验
    private boolean verifiedSign(byte[] data, byte[] sign, String algorithm) throws Exception {
        // 转换公钥材料
        // 实例化密钥工厂
        String publicKey = channelConfig.getHonorConfig().getPublicKey();
        byte[] pub_key_bytes = Base64.decodeBase64(publicKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        // 初始化公钥
        // 密钥材料转换
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(pub_key_bytes);
        // 产生公钥
        PublicKey pubKey = keyFactory.generatePublic(x509KeySpec);


        // 实例化Signature
        Signature signature = Signature.getInstance(algorithm);
        // 初始化Signature
        signature.initVerify(pubKey);
        // 更新
        signature.update(data);
        // 验证
        return signature.verify(sign);
    }

    // 登录请求的header
    private Map<String, String> getHeaders(Map<String, String> customerHeader) {
        Map<String, String> header = new HashMap<>();
        if(null != customerHeader) {
            header.putAll(customerHeader);
        }
        header.put("Content-Type", "application/json");
        header.put("x-app-id", channelConfig.getHonorConfig().getAppId());
        header.put("x-sign-type", "HmacSha256");
        String traceId = UUID.randomUUID().toString().replace("-", "");
        header.put("x-ra-traceid", traceId);
        String time = String.valueOf(new Date().getTime());
        header.put("x-ra-timestamp", time);
        return header;
    }

    /**
     * HmacSHA256签名
     *
     * @param data 需要签名的数据
     */
    private String hmacSha256Sign(String data) {
        if (null != data) {
            try {
                Mac hmac = Mac.getInstance("HmacSHA256");
                SecretKeySpec secret_key = new SecretKeySpec(channelConfig.getHonorConfig().getSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
                hmac.init(secret_key);
                byte[] bytes = hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
                return bytesToHex(bytes);
            } catch (Throwable var6) {
                return null;
            }
        } else {
            return null;
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder hex = new StringBuilder();
        for (byte b : bytes) {
            hex.append(String.format("%02x", b));
        }
        return hex.toString();
    }


    private String getAccessToken() {
        String accessToken = redisService.get(REDIS_TOKEN_KEY);
        if (StringUtils.isEmpty(accessToken)) {
            int loopCnt = 0;
            do {
                accessToken = this.reqAccessToken();
                loopCnt++;
            } while (loopCnt != 3 && StringUtils.isEmpty(accessToken));
        }
        return accessToken;
    }

    // 获取access_token
    private String reqAccessToken() {
        try {
            HonorConfig honorConfig = channelConfig.getHonorConfig();
            String grantType = "client_credentials";

            Map<String, String> params = new HashMap<>();
            params.put("grant_type", grantType);
            params.put("client_secret", URLEncoder.encode(honorConfig.getSecret(), "UTF-8"));
            params.put("client_id", honorConfig.getAppId());


            String response = OkHttpClientUtil.postForm(honorConfig.getAccessTokenUrl(), params, String.class);

            JSONObject respObj = JSONObject.parseObject(response);
            String token = respObj.getString("access_token");
            int expiresIn = respObj.getIntValue("expires_in");

            // redis 保存token
            redisService.set(REDIS_TOKEN_KEY, token);
            // 过期时间 - 60秒
            redisService.expireKey(REDIS_TOKEN_KEY, expiresIn - 60, TimeUnit.SECONDS);

            return token;
        } catch (Exception e) {
            log.error("payCb getAccessToken failed, e:", e);
            return null;
        }
    }

    // 成功返回
    private Map<String, Object> iapSuccess() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "");
        return result;
    }

    // 失败返回
    private Map<String, Object> iapError(int code, String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", msg);
        return result;
    }

    private String getRedisOrderDataKey(long userId, String cpOrderId) {
        return REDIS_ORDER_DATA + "_" + userId + "_" + cpOrderId;
    }

    // 通知-商品消耗
    private void confirmPurchase(String purchaseToken, String cpOrderId) {
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        requestHeader.put("access-token", this.getAccessToken());
        requestHeader.put("x-iap-appid", channelConfig.getHonorConfig().getAppId());
        requestHeader.put("purchaseToken", purchaseToken);
        Map<String, String> postData = new HashMap<>();
        postData.put("purchaseToken", purchaseToken);
        if (StringUtils.isEmpty(cpOrderId)) {
            cpOrderId = PaymentUtils.createCpOrderId();
        }
        postData.put("developerChallenge", cpOrderId);

        String response = OkHttpClientUtil.postJson(channelConfig.getHonorConfig().getConsumeProductUrl(), requestHeader, postData, String.class);
        if (ResponseContext.getOkHttpStatusCode() != null && ResponseContext.getOkHttpStatusCode() != 200) {
            log.error("confirmPurchaseFailed, params:{}, resp:{}", requestHeader, response);
            return;
        }
        JSONObject respObj = JSONObject.parseObject(response);
        int code = respObj.getIntValue("code");
        if (code != 0) {
            log.error("confirmPurchaseFailed, params:{}, resp:{}", requestHeader, response);
        }
    }
}

package com.dxx.game.modules.rank.support;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dto.UserProto;
import com.dxx.game.modules.rank.model.UserRankExtraData;
import com.dxx.game.modules.user.model.UserInfoModel;
import com.dxx.game.modules.user.service.UserService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 排行榜插件
 * <AUTHOR>
 * @date 2023/8/5 10:20
 */
@Component
public class RankHelper {
    public static final long MAX_TIME = 3000000000L;

    private static RankHelper rankHelper;

    @Autowired
    private UserDao userDao;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisService redisService;

    @PostConstruct
    private void init() {
        rankHelper = this;
    }

    public static void commit(String key, long id, long score, int expireTime, TimeUnit timeUnit) {
        String number = score + "." + (MAX_TIME - DateUtils.getUnixTime());
        rankHelper.redisService.zAdd(key, String.valueOf(id), Double.parseDouble(number));
        rankHelper.redisService.expireKey(key, expireTime, timeUnit);
    }

    public static void commit(String key, long id, long score) {
        String number = score + "." + (MAX_TIME - DateUtils.getUnixTime());
        rankHelper.redisService.zAdd(key, String.valueOf(id), Double.parseDouble(number));
    }

    public static void inc(String key, long id, long score) {
        String number = score + "." + (MAX_TIME - DateUtils.getUnixTime());
        rankHelper.redisService.zIncrementScore(key, String.valueOf(id), Double.parseDouble(number));
        expireKey(key);
    }

    public static void inc(String key, long id, long score, int expireTime, TimeUnit timeUnit) {
        String number = score + "." + (MAX_TIME - DateUtils.getUnixTime());
        rankHelper.redisService.zIncrementScore(key, String.valueOf(id), Double.parseDouble(number));
        rankHelper.redisService.expireKey(key, expireTime, timeUnit);
    }

    public static Map<Long, Pair<Long, UserRankExtraData>> rankList(String key, int start, int end) {
        Map<Long, Long> userList = getRankInfo(key, start, end);
        if (userList.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, UserInfoModel> users = rankHelper.userDao.queryUserInfo(new ArrayList<>(userList.keySet()), false);
        if(users.isEmpty()){
            return Collections.emptyMap();
        }
        Map<Long, Pair<Long, UserRankExtraData>> list = new LinkedHashMap<>();
        userList.forEach((k, v) -> {
            UserInfoModel user = users.get(k);

            if(user != null) {
                list.put(k, Pair.of(v, UserRankExtraData.build(user)));
            }
        });

        return list;
    }

    public static Map<Long, Long> getRankInfo(String key, int start, int end) {
        Set<ZSetOperations.TypedTuple<String>> set = rankHelper.redisService.zReverseRangeWithScores(key, start, end);
        if (set == null || set.isEmpty()) {
            return Collections.emptyMap();
        }
        return set.stream().collect(Collectors.toMap(tuple -> Optional.ofNullable(tuple.getValue()).map(Long::parseLong).orElse(0L), tuple -> Optional.ofNullable(tuple.getScore()).map(Double::longValue).orElse(-1L), (o, n) -> o, LinkedHashMap::new));
    }

    public static Map<Long, Long> guildBossRankList(int serverId, long bossTime) {
        String key = getGuildBossKey(serverId, bossTime);
        Set<ZSetOperations.TypedTuple<String>> rankList = rankHelper.redisService.zReverseRangeWithScores(key, 0, Integer.MAX_VALUE);

        Map<Long, Long> list = new LinkedHashMap<>();
        rankList.forEach(v -> {
            String value = v.getValue();
            Double score = v.getScore();

            long uid = value != null ? Long.parseLong(value) : 0L;
            long finalScore = score == null ? -1 : score.longValue();

            list.put(uid, finalScore);
        });

        return list;
    }

    public static Pair<Long, Pair<Integer, UserProto.PlayerInfoDto>> getRankP(String key, int rank) {
        rank = rank - 1;
        Map<Long, Pair<Integer, UserProto.PlayerInfoDto>> list = rankListP(key, rank, rank);
        if(list.isEmpty()) {
            return null;
        }

        long userId = list.keySet().iterator().next();
        Pair<Integer, UserProto.PlayerInfoDto> info = list.get(userId);
        return Pair.of(userId, info);
    }


    public static Map<Long, Pair<Integer, UserProto.PlayerInfoDto>> rankListP(String key, int start, int end) {
        Set<ZSetOperations.TypedTuple<String>> rankList = rankHelper.redisService.zReverseRangeWithScores(key, start, end);

        Map<Long, Integer> userList = new LinkedHashMap<>();
        rankList.forEach(v -> {
            String value = v.getValue();
            Double score = v.getScore();

            long uid = value != null ? Long.parseLong(value) : 0L;
            int finalScore = score == null ? -1 : score.intValue();

            userList.put(uid, finalScore);
        });

        List<UserProto.PlayerInfoDto> users= rankHelper.userService.getPlayerInfos(new ArrayList<>(userList.keySet()));
        Map<Long, UserProto.PlayerInfoDto> userMap = users.stream().collect(Collectors.toMap(UserProto.PlayerInfoDto::getUserId, v -> v));

        Map<Long, Pair<Integer, UserProto.PlayerInfoDto>> list = new LinkedHashMap<>();
        userList.forEach((k, v) -> {
            UserProto.PlayerInfoDto player = userMap.get(k);

            if(player != null) {
                list.put(k, Pair.of(v, player));
            }
        });

        return list;
    }

    public static Map<Long, ThreeTuple<Integer, UserProto.PlayerInfoDto, UserRankExtraData>> rankListT(String key, int start, int end) {
        Set<ZSetOperations.TypedTuple<String>> rankList = rankHelper.redisService.zReverseRangeWithScores(key, start, end);

        Map<Long, Integer> userList = new LinkedHashMap<>();
        rankList.forEach(v -> {
            String value = v.getValue();
            Double score = v.getScore();

            long uid = value != null ? Long.parseLong(value) : 0L;
            int finalScore = score == null ? -1 : score.intValue();

            userList.put(uid, finalScore);
        });

        List<UserProto.PlayerInfoDto> users= rankHelper.userService.getPlayerInfos(new ArrayList<>(userList.keySet()));
        Map<Long, UserProto.PlayerInfoDto> userMap = users.stream().collect(Collectors.toMap(UserProto.PlayerInfoDto::getUserId, v -> v));

        Map<Long, ThreeTuple<Integer, UserProto.PlayerInfoDto, UserRankExtraData>> list = new LinkedHashMap<>();
        userList.forEach((k, v) -> {
            UserProto.PlayerInfoDto user = userMap.get(k);

            if(user != null) {
                ThreeTuple<Integer, UserProto.PlayerInfoDto, UserRankExtraData> t = new ThreeTuple<>(v, user, UserRankExtraData.build(user));
                list.put(k, t);
            }
        });

        return list;
    }

    public static Map<Long, Integer> rankListSimple(String key, int start, int end) {
        Set<ZSetOperations.TypedTuple<String>> rankList = rankHelper.redisService.zReverseRangeWithScores(key, start, end);

        Map<Long, Integer> userList = new LinkedHashMap<>();
        rankList.forEach(v -> {
            String value = v.getValue();
            Double score = v.getScore();

            long uid = value != null ? Long.parseLong(value) : 0L;
            int finalScore = score == null ? -1 : score.intValue();

            userList.put(uid, finalScore);
        });

        return userList;
    }

    public static long getScore(String key, long id) {
        return rankHelper.redisService.zScore(key, String.valueOf(id));
    }

    public static int getRank(String key, long id) {
        Long rank = rankHelper.redisService.zRank(key, String.valueOf(id));
        return rank != null ? rank.intValue() + 1 : 0;
    }

    public static int getRankCount(String key) {
        return rankHelper.redisService.zCard(key).intValue();
    }

    public static void removeRank(String key, long id) {
        rankHelper.redisService.zRem(key, String.valueOf(id));
    }

    private static void expireKey(String key) {
        rankHelper.redisService.expireKey(key, DateUtils.SECONDS_30_DAY, TimeUnit.SECONDS);
    }

    public static String getPowerRankKey(int serverId) {
        return "r:pow" + ":" + serverId;
    }
    public static String getTowerRankKey(int serverId) {
        return "r:tower" + ":" + serverId;
    }
    public static String getGuildBossKey(int serverId, long bossTime) {
        return "r:gb" + ":" + serverId + ":" + bossTime;
    }
    public static String getHistoryPowerRankKey(int serverId) {
        return "r:hpow" + ":" + serverId;
    }
    public static String getPersonalBossRankKey(int serverId, long time) {
        return "r:pb" + ":" + serverId + ":" + time;
    }
}

package com.dxx.game.cron.event;

import com.dxx.game.common.config.game.event.RefreshConfigEvent;
import com.dxx.game.common.server.consts.ServerType;
import com.dxx.game.common.server.context.ServerContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * @author: lsc
 * @createDate: 2024/4/13
 * @description:
 */
@Component
public class RedisMessageReceiver {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void onMessage(String message) {
        if (ServerContext.getServerType() != null && ServerContext.getServerType() == ServerType.CRON_SERVER) {
            if (message.equals("reload_config")) {
                applicationEventPublisher.publishEvent(new RefreshConfigEvent(this));
            }
        }
    }
}

package com.dxx.game.modules.log.service.impl;

import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.kinesis.KinesiseCacheManager;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.LogResource;
import com.dxx.game.dao.dynamodb.repository.LogResourceDao;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.hero.data.HeroRewardData;
import com.dxx.game.modules.reward.model.GuildActiveReward;
import com.dxx.game.modules.reward.model.GuildExpReward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.modules.log.service.LogService;

import java.util.ArrayList;
import java.util.List;

/**
 * 日志接口实现类
 * <AUTHOR>
 * @date 2019-12-14 15:04
 */
@Service
@Slf4j
public class LogServiceImpl implements LogService {

	@Autowired
	private CommonService commonService;
	@Autowired
	private LogResourceDao logResourceDao;

	@Override
	public void sendBasic(long userId, short command, long transId, String extra) {
		this.sendBasic(userId, command, transId, 0, extra);
	}

	@Override
	public void sendBasic(long userId, short command, long transId, int customType, String extra) {

		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		logResource.setCustomType(customType);
		if (!StringUtils.isEmpty(logResource.getExtra())) {
			logResource.setExtra(logResource.getExtra() + "|" + extra);
		} else {
			logResource.setExtra(extra);
		}
		logResource.setCommand(command);
		logResourceDao.insert(logResource);
	}

	@Override
	public void sendCoin(long userId, short command, long transId, int amount, long totalAmount) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		List<Long> logItemData = new ArrayList<>();
		logItemData.add((long) RewardResourceType.COINS.getValue());
		logItemData.add((long) amount);
		logItemData.add(totalAmount);
		logResource.getItems().add(logItemData);
		logResource.setExtra("");
		logResourceDao.insert(logResource);
		KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
				logItemData.get(1), logItemData.get(2), logResource.getLogTime());
	}

	@Override
	public void sendDiamond(long userId, short command, long transId, int amount, long totalAmount) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		List<Long> logItemData = new ArrayList<>();
		logItemData.add((long) RewardResourceType.DIAMONDS.getValue());
		logItemData.add((long) amount);
		logItemData.add(totalAmount);
		logResource.getItems().add(logItemData);
		logResourceDao.insert(logResource);
		KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
				logItemData.get(1), logItemData.get(2), logResource.getLogTime());
	}

	@Override
	public void sendHeros(long userId, short command, long transId, List<Hero> heros, boolean isReceive) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		for (Hero hero : heros) {
			List<Long> logItemData = new ArrayList<>();
			logItemData.add((long) hero.getHeroId());
			if (isReceive) {
				logItemData.add(1L);
				logItemData.add(1L);
			} else {
				logItemData.add(-1L);
				logItemData.add(0L);
			}
			logItemData.add((long) hero.getLevel());
			logItemData.add((long) hero.getRowId().intValue());
			logResource.getItems().add(logItemData);
			KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
					logItemData.get(1), logItemData.get(2), logResource.getLogTime());
		}
		logResourceDao.update(logResource);
	}

	@Override
	public void sendItem(long userId, short command, long transId, int itemId, int itemNum, long totalNum) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		List<Long> logItemData = new ArrayList<>();
		logItemData.add((long) itemId);
		logItemData.add((long) itemNum);
		logItemData.add(totalNum);
		logResource.getItems().add(logItemData);
		logResourceDao.insert(logResource);
		KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
				logItemData.get(1), logItemData.get(2), logResource.getLogTime());
	}

	@Override
	public void sendItems(long userId, short command, long transId, List<Item> items) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		for (Item item : items) {
			List<Long> logItemData = new ArrayList<>();
			logItemData.add((long) item.getItemId());
			logItemData.add((long) item.getNum());
			logItemData.add((long) item.getCount());
			logResource.getItems().add(logItemData);
			KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
					logItemData.get(1), logItemData.get(2), logResource.getLogTime());
		}
		logResourceDao.insert(logResource);
	}

	@Override
	public void sendEquip(long userId, short command, long transId, long equipRowId, int equipId, int equipNum, int level) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		List<Long> logItemData = new ArrayList<>();
		logItemData.add((long) equipId);
		logItemData.add((long) equipNum);
		if (equipNum < 0) {
			logItemData.add(0L);
		} else {
			logResource.getEquipRowIds().add(equipRowId);
			logItemData.add((long) equipNum);
		}
		logItemData.add((long) level);
		logResource.getItems().add(logItemData);

		logResourceDao.insert(logResource);
		KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
				logItemData.get(1), logItemData.get(2), logResource.getLogTime());
	}

	@Override
	public void sendEquips(long userId, short command, long transId, List<Equip> equips, boolean isReceive) {
		LogResource logResource = this.getLogResourceObj(userId, transId, command);
		for (Equip equip : equips) {
			List<Long> logItemData = new ArrayList<>();
			logItemData.add((long) equip.getEquipId());
			if (isReceive) {
				logItemData.add(1L);
				logItemData.add(1l);
				logResource.getEquipRowIds().add(equip.getRowId());
			} else {
				logItemData.add(-1L);
				logItemData.add(0l);
			}
			logItemData.add((long) equip.getLevel());

			logResource.getItems().add(logItemData);

			KinesiseCacheManager.putResourceLogChache(userId, command, transId, logItemData.get(0),
					logItemData.get(1), logItemData.get(2), logResource.getLogTime());
		}
		logResourceDao.insert(logResource);
	}

	@Override
	public void sendLog(RewardResultSet rewardResultSet, long userId, short command, long transId) {
		try {
			List<Item> items = new ArrayList<>();
			List<Hero> heroes = new ArrayList<>();
			for (RewardResult<?> result : rewardResultSet.getResults()) {
				switch (result.getRewardType()) {
					case RESOURCE:
						// 判断子类型
						if (result.getResourceType() == RewardResourceType.COINS) {
							this.sendCoin(userId, command, transId, result.getActualCount(), (Long) result.getCurrent());
						} else if (result.getResourceType() == RewardResourceType.DIAMONDS) {
							this.sendDiamond(userId, command, transId,result.getActualCount(), (Long) result.getCurrent());
						} else if (result.getResourceType() == RewardResourceType.VITALITY) {
							this.sendItem(userId, command, transId, result.getConfigId(), result.getActualCount(), (Integer) result.getCurrent());
						}
						break;
					case ITEM:
						Item item = (Item) result.getCurrent();
						item.setNum(result.getActualCount());
						items.add(item);
						break;
					case EQUIP:
						List<Equip> equips = (List<Equip>) result.getCurrent();
						if (result.getActualCount() > 0) {
							this.sendEquips(userId, command, transId, equips, true);
						} else {
							this.sendEquips(userId, command, transId, equips, false);
						}

						break;
					case GUILD_EXP:
						GuildExpReward guildExpReward = (GuildExpReward)result.getCurrent();
						this.sendItem(userId, command, transId, guildExpReward.getConfigId(), guildExpReward.getCount(), guildExpReward.getExp());
						break;
					case GUILD_ACTIVE:
						GuildActiveReward guildActiveReward = (GuildActiveReward)result.getCurrent();
						this.sendItem(userId, command, transId, guildActiveReward.getConfigId(), guildActiveReward.getCount(), guildActiveReward.getGuildActive());
						break;
					case HERO:
						HeroRewardData data = (HeroRewardData) result.getCurrent();
						heroes.addAll(data.getHeroList());
						break;
					default:
						log.error("sendLog failed undefined rewardType : {}", result.getRewardType());
						break;
				}
			}
			if (!items.isEmpty()) {
				this.sendItems(userId, command, transId, items);
			}
			if (!heroes.isEmpty()) {
				this.sendHeros(userId, command, transId, heroes, true);
			}

		} catch (Exception e) {
			log.error("sendLog e:", e);
			log.error("sendLog failed : {}", e.getMessage());
		}
	}


	private LogResource getLogResourceObj(long userId, long transId, short command) {
		LogResource logResource = DynamoDBCacheManager.getLogResourceCache();
		if (logResource == null) {
			logResource = new LogResource();
			logResource.setUserId(userId);
			logResource.setTransId(transId);
			logResource.setCommand(command);
			logResource.setLogTime(DateUtils.getUnixTime());
			logResource.setTtlTime(DateUtils.getUnixTime() + DateUtils.SECONDS_60_DAY);
			logResource.setItems(new ArrayList<>());
			logResource.setExtra("");
			logResource.setCustomType(0);
			logResource.setEquipRowIds(new ArrayList<>());
			logResource.setRequestId(RequestContext.getRequestId());
			DynamoDBCacheManager.setLogResourceCache(logResource);
		}
		return logResource;
	}
}
























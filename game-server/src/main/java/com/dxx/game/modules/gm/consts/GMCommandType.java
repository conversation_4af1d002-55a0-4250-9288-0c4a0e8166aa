package com.dxx.game.modules.gm.consts;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
public interface GMCommandType {

    // 查询玩家详情数据
    int USER_GET_INFO = 1001;
//    int USER_RECHARGE_LOG = 1002;       // 弃用
    // 修改用户数据
    int USER_CHANGE_DATA  = 1003;
    // 发放奖励
    int SEND_AWARD = 1004;
    // 内购补单
    int IAP_SUPPLEMENT = 1005;
    // 玩家清档
    int USER_RESET = 1006;
    // 交换存档
    int USER_SWAP = 1007;
    // 玩家账号 封号/解封
    int USER_SET_ACCOUNT_STATE = 1008;
    // 查询用户是否存在
    int USER_CHECK = 1009;
    // 根据商品ID查询玩家最后一笔所购买的道具
    int QUERY_USER_ORDER_REWARDS = 1010;
    // 查询ios订单
    int QUERY_IOS_ORDER_BY_ID = 1011;
    // 查询公会数据
    int GUILD_GET_INFO = 1015;
    // 查询公会成员数据
    int GUILD_MEMBER_GET_INFO = 1016;
    // 修改公会数据
    int GUILD_CHANGE_DATA  = 1017;
    // 发放奖励-2.0
    int SEND_AWARD_V2 = 1018;

    // 查询订单
    int QUERY_RECHARGE_ORDER = 2001;
    // 查询资源消耗记录
    int QUERY_RESOURCE_LOG = 2002;
    // 根据userIds 检测用户是否存在
    int QUERY_USER_SIMPLE  = 2003;

    int INTERNAL_QUERY = 3001;
}

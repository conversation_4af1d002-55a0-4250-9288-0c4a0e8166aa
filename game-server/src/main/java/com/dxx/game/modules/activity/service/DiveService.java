package com.dxx.game.modules.activity.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.activity.Dive;
import com.dxx.game.dto.DiveProto;

public interface DiveService {
    Result<DiveProto.DiveOnOpenResponse> onOpen(DiveProto.DiveOnOpenRequest params);

    Result<DiveProto.DiveBuyItemResponse> buyItem(DiveProto.DiveBuyItemRequest params);

    Result<DiveProto.DiveAccRewardResponse> accReward(DiveProto.DiveAccRewardRequest params);

    Result<DiveProto.DiveShineResponse> shine(DiveProto.DiveShineRequest params);

    Result<DiveProto.DiveUsePropResponse> shineItem(DiveProto.DiveUsePropRequest params);

    Result<DiveProto.DiveAllAccRewardResponse> allAcc(DiveProto.DiveAllAccRewardRequest params);
}

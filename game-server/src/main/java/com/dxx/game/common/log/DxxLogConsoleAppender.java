package com.dxx.game.common.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.pattern.ExtendedThrowableProxyConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.ConsoleAppender;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.server.context.RequestContext;
import lombok.SneakyThrows;
import org.slf4j.MDC;


/**
 * <AUTHOR>
 * @date 2021/9/15 10:00
 */
public class DxxLogConsoleAppender extends ConsoleAppender<ILoggingEvent> {

    public static final String COMMAND_NAME = "commandName";

    public static final String REQUEST_PATH = "reqPath";
    public static final String REQUEST_BODY = "reqBody";

    public static final String RESP_CODE = "respCode";
    public static final String RESP_DATA = "respData";

    public static final String REQUEST_TIME_IN_MS = "reqTimeInMS";
    public static final String LATENCY_IN_MS = "latencyInMS";

    public static final String TRACE_ID = "traceId";


    @SneakyThrows
    @Override
    protected void append(ILoggingEvent event) {
        if (!LogUtils.isStdOutput()) {
            super.append(event);
        } else {
            Level level = event.getLevel();
            int lineNumber = 0;
            StackTraceElement[] cda = event.getCallerData();
            if (cda != null && cda.length > 0) {
                lineNumber = cda[0].getLineNumber();
            }


            String[] logger = event.getLoggerName().split("\\.");
            String loggerName = logger[logger.length - 1];
            String message = event.getFormattedMessage();


            message = message.replaceAll("\r\n", "");
            message = message.replaceAll("\n", "");

            long userId = 0;
            if (RequestContext.getUserId() != null) {
                userId = RequestContext.getUserId();
            }
            short command = 0;
            if (RequestContext.getCommand() != null) {
                command = RequestContext.getCommand();
            }

            JSONObject logContent = new JSONObject(true);
            if (userId > 0 && command > 0) {
                logContent.put("userId", userId);
                logContent.put("command", command);
            }
            logContent.put("line", lineNumber);
            logContent.put("class", loggerName);
            logContent.put("message", message);
            logContent.put("logLevel", level.levelStr);

            put(TRACE_ID, logContent);
            put(COMMAND_NAME, logContent);
            put(REQUEST_PATH, logContent);
            put(REQUEST_BODY, logContent);
            putLong(RESP_CODE, logContent);
            put(RESP_DATA, logContent);
            putLong(REQUEST_TIME_IN_MS, logContent);
            putLong(LATENCY_IN_MS, logContent);

            if (level == Level.ERROR) {
                String exception = "";
                if (event.getThrowableProxy() != null) {
                    ExtendedThrowableProxyConverter throwableConverter = new ExtendedThrowableProxyConverter();
                    throwableConverter.start();
                    exception = throwableConverter.convert(event);
                    throwableConverter.stop();
                }
                if (!exception.isEmpty()) {
                    exception = exception.replaceAll("Caused by", "    at Caused by");
                    exception = exception.replaceAll("\\.\\.\\.", "    at .......");
                }

                logContent.put("exception", exception);
                JSONObject errorLog = new JSONObject();
                errorLog.put("error", logContent);
                System.err.println(errorLog.toJSONString());
            } else {
                System.out.println(logContent.toJSONString());
            }
        }

//            if (level == Level.ERROR) {
//                String exception = "";
//                if (event.getThrowableProxy() != null) {
//                    ExtendedThrowableProxyConverter throwableConverter = new ExtendedThrowableProxyConverter();
//                    throwableConverter.start();
//                    exception = throwableConverter.convert(event);
//
//                    throwableConverter.stop();
//                }
//                if (!exception.isEmpty()) {
//                    exception = exception.replaceAll("Caused by", "    at Caused by");
//                    exception = exception.replaceAll("\\.\\.\\.", "    at .......");
//                }
//
//                StringBuilder stringBuilder = new StringBuilder();
//                stringBuilder.append("    Error:");
//                if (userId > 0 && command > 0) {
//                    stringBuilder.append("userId=").append(userId).append(",")
//                            .append("command=").append(command).append(",");
//                }
//                stringBuilder.append("line=").append(lineNumber).append(",")
//                        .append("class=").append(loggerName).append(",")
//                        .append("message=").append(message).append(",")
//                        .append("exception=").append(exception);
//
//                String logStr = stringBuilder.toString();
//                logStr = logStr.replaceAll("\\t", "    ");
//
//                if (StringUtils.isEmpty(exception)) {
//                    System.err.println(logStr);
//                } else {
//                    System.err.print(logStr);
//                }
//            } else if (level == Level.INFO) {
//                StringBuilder stringBuilder = new StringBuilder();
//                if (userId > 0 && command > 0) {
//                    stringBuilder.append("userId=").append(userId).append(",")
//                            .append("command=").append(command).append(",");
//                }
//                stringBuilder.append("line=").append(lineNumber).append(",")
//                        .append("class=").append(loggerName).append(",")
//                        .append("message=").append(message);
//
//                System.out.println(stringBuilder.toString().trim());
//
//            } else if (level == Level.DEBUG) {
//                Map<String, Object> logInfo = new HashMap<>();
//                if (userId > 0 && command > 0) {
//                    logInfo.put("userId", userId);
//                    logInfo.put("command", command);
//                }
//                logInfo.put("line", lineNumber);
//                logInfo.put("class", loggerName);
//                logInfo.put("message", message);
//                System.out.println(JSONObject.toJSONString(logInfo).trim());
//            }
//        }
    }

    public static void put(String key, JSONObject logContent) {
        String value = MDC.get(key);
        if (value != null) {
            logContent.put(key, value);
        }
    }

    public static void putLong(String key, JSONObject logContent) {
        String value = MDC.get(key);
        if (value != null) {
            logContent.put(key, Long.parseLong(value));
        }
    }
}



package com.dxx.game.modules.reward.processor;

import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.level.LevelEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.model.UserExpReward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 主线体力处理器
 * <AUTHOR>
 * @date 2021/11/19 20:39
 */
@Slf4j
@Component
public class UserExpProcessor implements RewardProcessor {

    @Autowired
    private UserService userService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserDao userDao;
    @Autowired
    private RewardService rewardService;

    @Override
    public RewardType getType() {
        return RewardType.RESOURCE;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.EXP;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        User user = userService.getUser(userId);
        if (user == null) {
            resultCode = ErrorCode.QUERY_USER_DATA_ERROR;
        } else {
            if (reward.getCount() <= 0) {
                log.error("add user exp count error, value : {}", reward.getCount());
                resultCode = ErrorCode.PARAMS_ERROR;
            }
        }
        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }

        return null;

//        User user = userService.getUser(userId);
//        int maxLevel = gameConfigManager.getLevelConfig().getLevel().size();
//        int fromLevel = user.getLevel();
//        int toLevel = fromLevel;
//        int addExp = rewardAction.getReward().getCount();
//        int toExp = user.getExp() + addExp;
//        List<List<Integer>> levelUpRewards = new ArrayList<>();
//
//        while (true) {
//            if (toLevel >= maxLevel) {
//                break;
//            }
//
//            LevelEntity levelEntity = gameConfigManager.getLevelConfig().getLevelEntity(toLevel);
//
//            int needExp = levelEntity.getExp();
//            if (needExp > toExp) {
//                break;
//            }
//
//            toLevel ++;
//            toExp -= needExp;
//
//            if (!levelEntity.getRewards().isEmpty()) {
//                levelUpRewards.addAll(levelEntity.getRewards());
//            }
//        }
//
//        user.setLevel((short)toLevel);
//        user.setExp(toExp);
//        userDao.update(user);
//
//        // 升级奖励
//        if (!levelUpRewards.isEmpty()) {
//            RewardResultSet rewardResultSet = rewardService.executeRewards(user.getUserId(), levelUpRewards);
//            if (rewardResultSet.isFailed()) {
//                log.error("levelUpRewards execute failed, userId:{}, fromLevel:{}, toLevel:{}", user.getUserId(), fromLevel, toLevel);
//                rewardAction.setResultCode(rewardResultSet.getResultCode());
//                return null;
//            }
//            ResponseContext.setLevelUpRewardResultSet(rewardResultSet);
//        }
//
//
//        UserExpReward userExpReward = UserExpReward.valueOf(toLevel, toExp);
//        RewardResult<UserExpReward> result = new RewardResult<UserExpReward>(this.getType(), this.getRewardResourceType());
//        result.setActualCount(addExp);
//        result.setCurrent(userExpReward);
//        return result;
    }
}

[{"ID": 101, "Day": 1, "TaskType": 12, "StatisticsType": 0, "Need": 3, "Describe": "9500101", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "1,500"], "Jump": 12, "UnlockNeed": 2}, {"ID": 102, "Day": 1, "TaskType": 12, "StatisticsType": 0, "Need": 5, "Describe": "9500102", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "1,1000"], "Jump": 12, "UnlockNeed": 2}, {"ID": 103, "Day": 1, "TaskType": 12, "StatisticsType": 0, "Need": 10, "Describe": "9500103", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "1,2000"], "Jump": 12, "UnlockNeed": 2}, {"ID": 104, "Day": 1, "TaskType": 1, "StatisticsType": 0, "Need": 3, "Describe": "9500104", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,500"], "Jump": 1, "UnlockNeed": 3}, {"ID": 105, "Day": 1, "TaskType": 1, "StatisticsType": 0, "Need": 5, "Describe": "9500105", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,1000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 106, "Day": 1, "TaskType": 1, "StatisticsType": 0, "Need": 8, "Describe": "9500106", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,2000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 107, "Day": 1, "TaskType": 8, "StatisticsType": 0, "Need": 2, "Describe": "9500107", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,4"], "Jump": 5, "UnlockNeed": 15}, {"ID": 108, "Day": 1, "TaskType": 8, "StatisticsType": 0, "Need": 3, "Describe": "9500108", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,6"], "Jump": 5, "UnlockNeed": 15}, {"ID": 201, "Day": 2, "TaskType": 1, "StatisticsType": 0, "Need": 10, "Describe": "9500201", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,4000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 202, "Day": 2, "TaskType": 1, "StatisticsType": 0, "Need": 12, "Describe": "9500202", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,6000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 203, "Day": 2, "TaskType": 17, "StatisticsType": 0, "Need": 20, "Describe": "9500203", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "400,20"], "Jump": 2, "UnlockNeed": 73}, {"ID": 204, "Day": 2, "TaskType": 17, "StatisticsType": 0, "Need": 50, "Describe": "9500204", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "400,30"], "Jump": 2, "UnlockNeed": 73}, {"ID": 205, "Day": 2, "TaskType": 17, "StatisticsType": 0, "Need": 100, "Describe": "9500205", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "400,50"], "Jump": 2, "UnlockNeed": 73}, {"ID": 206, "Day": 2, "TaskType": 13, "StatisticsType": 0, "Need": 8000, "Describe": "9500206", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "51,1"], "Jump": 2, "UnlockNeed": 5}, {"ID": 207, "Day": 2, "TaskType": 13, "StatisticsType": 0, "Need": 10000, "Describe": "9500207", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "51,1"], "Jump": 2, "UnlockNeed": 5}, {"ID": 208, "Day": 2, "TaskType": 13, "StatisticsType": 0, "Need": 12000, "Describe": "9500208", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "51,1"], "Jump": 2, "UnlockNeed": 5}, {"ID": 301, "Day": 3, "TaskType": 1, "StatisticsType": 0, "Need": 14, "Describe": "9500301", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,8000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 302, "Day": 3, "TaskType": 1, "StatisticsType": 0, "Need": 16, "Describe": "9500302", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,10000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 303, "Day": 3, "TaskType": 19, "StatisticsType": 1, "Need": 5, "Describe": "9500303", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "52,1"], "Jump": 8, "UnlockNeed": 51}, {"ID": 304, "Day": 3, "TaskType": 19, "StatisticsType": 1, "Need": 10, "Describe": "9500304", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "52,1"], "Jump": 8, "UnlockNeed": 51}, {"ID": 305, "Day": 3, "TaskType": 19, "StatisticsType": 1, "Need": 20, "Describe": "9500305", "ProgressType": 1, "Active": 1, "Reward": ["6001,1", "52,1"], "Jump": 8, "UnlockNeed": 51}, {"ID": 306, "Day": 3, "TaskType": 8, "StatisticsType": 0, "Need": 4, "Describe": "9500306", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,4"], "Jump": 5, "UnlockNeed": 15}, {"ID": 307, "Day": 3, "TaskType": 8, "StatisticsType": 0, "Need": 5, "Describe": "9500307", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,6"], "Jump": 5, "UnlockNeed": 15}, {"ID": 308, "Day": 3, "TaskType": 8, "StatisticsType": 0, "Need": 6, "Describe": "9500308", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1004,2"], "Jump": 5, "UnlockNeed": 15}, {"ID": 401, "Day": 4, "TaskType": 1, "StatisticsType": 0, "Need": 18, "Describe": "9500401", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,12000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 402, "Day": 4, "TaskType": 1, "StatisticsType": 0, "Need": 19, "Describe": "9500402", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,15000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 403, "Day": 4, "TaskType": 23, "StatisticsType": 0, "Need": 2, "Describe": "9500403", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "400,30"], "Jump": 2, "UnlockNeed": 2}, {"ID": 404, "Day": 4, "TaskType": 23, "StatisticsType": 0, "Need": 3, "Describe": "9500404", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "400,50"], "Jump": 2, "UnlockNeed": 2}, {"ID": 405, "Day": 4, "TaskType": 23, "StatisticsType": 0, "Need": 4, "Describe": "9500405", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "400,80"], "Jump": 2, "UnlockNeed": 2}, {"ID": 406, "Day": 4, "TaskType": 22, "StatisticsType": 1, "Need": 1, "Describe": "9500406", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "405,10"], "Jump": 14, "UnlockNeed": 70}, {"ID": 407, "Day": 4, "TaskType": 22, "StatisticsType": 1, "Need": 2, "Describe": "9500407", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "405,20"], "Jump": 14, "UnlockNeed": 70}, {"ID": 408, "Day": 4, "TaskType": 22, "StatisticsType": 1, "Need": 3, "Describe": "9500408", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "405,30"], "Jump": 14, "UnlockNeed": 70}, {"ID": 501, "Day": 5, "TaskType": 1, "StatisticsType": 0, "Need": 20, "Describe": "9500501", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,20000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 502, "Day": 5, "TaskType": 1, "StatisticsType": 0, "Need": 21, "Describe": "9500502", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,25000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 503, "Day": 5, "TaskType": 10, "StatisticsType": 1, "Need": 5, "Describe": "9500503", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "2,100"], "Jump": 9, "UnlockNeed": 53}, {"ID": 504, "Day": 5, "TaskType": 10, "StatisticsType": 1, "Need": 10, "Describe": "9500504", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "2,200"], "Jump": 9, "UnlockNeed": 53}, {"ID": 505, "Day": 5, "TaskType": 10, "StatisticsType": 1, "Need": 15, "Describe": "9500505", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "2,300"], "Jump": 9, "UnlockNeed": 53}, {"ID": 506, "Day": 5, "TaskType": 8, "StatisticsType": 0, "Need": 7, "Describe": "9500506", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,4"], "Jump": 5, "UnlockNeed": 15}, {"ID": 507, "Day": 5, "TaskType": 8, "StatisticsType": 0, "Need": 8, "Describe": "9500507", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,6"], "Jump": 5, "UnlockNeed": 15}, {"ID": 508, "Day": 5, "TaskType": 8, "StatisticsType": 0, "Need": 9, "Describe": "9500508", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1004,2"], "Jump": 5, "UnlockNeed": 15}, {"ID": 601, "Day": 6, "TaskType": 1, "StatisticsType": 0, "Need": 22, "Describe": "9500601", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,30000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 602, "Day": 6, "TaskType": 1, "StatisticsType": 0, "Need": 23, "Describe": "9500602", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,35000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 603, "Day": 6, "TaskType": 14, "StatisticsType": 1, "Need": 1, "Describe": "9500603", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "405,10"], "Jump": 11, "UnlockNeed": 17}, {"ID": 604, "Day": 6, "TaskType": 14, "StatisticsType": 1, "Need": 2, "Describe": "9500604", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "405,20"], "Jump": 11, "UnlockNeed": 17}, {"ID": 605, "Day": 6, "TaskType": 14, "StatisticsType": 1, "Need": 3, "Describe": "9500605", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "405,30"], "Jump": 11, "UnlockNeed": 17}, {"ID": 606, "Day": 6, "TaskType": 20, "StatisticsType": 0, "Need": 2, "Describe": "9500606", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "53,1"], "Jump": 13, "UnlockNeed": 17}, {"ID": 607, "Day": 6, "TaskType": 20, "StatisticsType": 0, "Need": 3, "Describe": "9500607", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "53,1"], "Jump": 13, "UnlockNeed": 17}, {"ID": 608, "Day": 6, "TaskType": 20, "StatisticsType": 0, "Need": 5, "Describe": "9500608", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "53,2"], "Jump": 13, "UnlockNeed": 17}, {"ID": 701, "Day": 7, "TaskType": 1, "StatisticsType": 0, "Need": 24, "Describe": "9500701", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,40000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 702, "Day": 7, "TaskType": 1, "StatisticsType": 0, "Need": 25, "Describe": "9500702", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1,50000"], "Jump": 1, "UnlockNeed": 3}, {"ID": 703, "Day": 7, "TaskType": 8, "StatisticsType": 0, "Need": 10, "Describe": "9500703", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1003,8"], "Jump": 5, "UnlockNeed": 15}, {"ID": 704, "Day": 7, "TaskType": 8, "StatisticsType": 0, "Need": 11, "Describe": "9500704", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1004,2"], "Jump": 5, "UnlockNeed": 15}, {"ID": 705, "Day": 7, "TaskType": 23, "StatisticsType": 0, "Need": 5, "Describe": "9500705", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1025,1"], "Jump": 2, "UnlockNeed": 2}, {"ID": 706, "Day": 7, "TaskType": 23, "StatisticsType": 0, "Need": 6, "Describe": "9500706", "ProgressType": 0, "Active": 2, "Reward": ["6001,2", "1030,1"], "Jump": 2, "UnlockNeed": 2}, {"ID": 707, "Day": 7, "TaskType": 13, "StatisticsType": 0, "Need": 30000, "Describe": "9500707", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1004,10"], "Jump": 2, "UnlockNeed": 5}, {"ID": 708, "Day": 7, "TaskType": 13, "StatisticsType": 0, "Need": 40000, "Describe": "9500708", "ProgressType": 1, "Active": 2, "Reward": ["6001,2", "1004,6"], "Jump": 2, "UnlockNeed": 5}]
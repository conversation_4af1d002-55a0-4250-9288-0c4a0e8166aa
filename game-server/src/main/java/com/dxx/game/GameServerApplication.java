package com.dxx.game;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBBaseModelScan;
import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTransactionScan;
import com.dxx.game.common.server.context.ApplicationContextProvider;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.netty.NettyServerListener;

import com.dxx.game.common.trans.annotation.TransScan;
import com.dxx.game.cron.CronServerApplication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.transaction.jta.JtaAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.event.ContextClosedEvent;

import java.util.Arrays;

import jakarta.annotation.Resource;


@SpringBootApplication(exclude = {JtaAutoConfiguration.class})
@DynamoDBBaseModelScan(basePackages = "com.dxx.game.dao.dynamodb.model", dynamodbTableConfig = "application-dynamodb.properties")
@DynamoDBTransactionScan(basePackages = "com.dxx.game.modules.**.service.impl", methodReturnTypeClasses = Result.class)
@TransScan(handlerPackages = "com.dxx.game.modules.**.handler", methodReturnTypeClasses = Result.class)
@ComponentScan(basePackages = {"com.dxx.game"}, excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = {CronServerApplication.class})
})
public class GameServerApplication implements ApplicationRunner, ApplicationListener<ContextClosedEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GameServerApplication.class);

    @Resource
    private NettyServerListener nettyServerListener;

    public static void main(String[] args) {
        try {
            SpringApplication springApplication = new SpringApplication(GameServerApplication.class);
            // 关闭spring启动日志
            springApplication.setBannerMode(Banner.Mode.OFF);
            springApplication.setLogStartupInfo(false);
            springApplication.setAllowCircularReferences(true);
            springApplication.run(args);
        } catch (Exception e) {
            LOGGER.error("GameServerApplicationError, e:", e);
            System.exit(-1);
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        LOGGER.info("ApplicationArguments:{}", Arrays.asList(args.getSourceArgs()));
        nettyServerListener.start(args);
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        nettyServerListener.close();
    }
}














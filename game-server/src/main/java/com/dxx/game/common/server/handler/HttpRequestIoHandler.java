package com.dxx.game.common.server.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.xray.entities.Segment;
import com.dxx.game.common.aws.xray.XRayFilter;
import com.dxx.game.common.server.consts.HttpProtocolValues;
import com.dxx.game.common.server.dispatcher.ApiDispatcher;
import com.dxx.game.common.server.dispatcher.LogicDispatcher;
import com.dxx.game.common.utils.ThreadPoolUtils;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.handler.codec.http.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.Charsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;

import static com.dxx.game.common.log.DxxLogConsoleAppender.*;

@Slf4j
public class HttpRequestIoHandler extends ChannelInboundHandlerAdapter {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final String FAVICON_ICO = "/favicon.ico";

    private final XRayFilter rayFilter = new XRayFilter();

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (!(msg instanceof HttpRequest)) {
            return;
        }

        FullHttpRequest request = (FullHttpRequest) msg;

        Segment segment = null;
        try {
            segment = rayFilter.preFilter(ctx, request);
        } catch (Throwable e) {
            logger.error("x-ray preFilter error", e);
        }

        try {
            long currentTimeMillis = System.currentTimeMillis();
            request.headers().set("request-time", currentTimeMillis);

            MDC.put(REQUEST_TIME_IN_MS, String.valueOf(currentTimeMillis));

            String traceId = request.headers().get(TRACE_ID);
            if (traceId == null) {
                traceId = UUID.randomUUID().toString().split("-")[4];
                request.headers().set(TRACE_ID, traceId);
                MDC.put(TRACE_ID, traceId);
            }

            String uri = request.uri();

            if (uri.equals(FAVICON_ICO)) return;
            if (uri.contains("/health_check")) {
                FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1,
                        HttpResponseStatus.OK);
                response.headers().set(HttpHeaderNames.CONTENT_TYPE, HttpProtocolValues.HTTP_CONTENT_TYPE_JSON);
                HttpResponser.doSend(ctx, request, response);
                return;
            }

            HttpHeaders headers = request.headers();

            HttpMethod method = request.method();
            if (method.equals(HttpMethod.GET)){
                handleGetContent(ctx, request, uri, headers);
            } else if (method.equals(HttpMethod.POST) || method.equals(HttpMethod.PUT)){
                handlePostContent(ctx, request, headers );
            } else if (method.equals(HttpMethod.OPTIONS)) {
                FullHttpResponse response = new DefaultFullHttpResponse(
                        HttpVersion.HTTP_1_1,
                        HttpResponseStatus.OK,
                        Unpooled.wrappedBuffer("OK".getBytes())
                );
                response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
                response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS, "*");
                response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS, "*");
                response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
                ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
            }
        } catch (Exception e) {
            if (segment != null) {
                segment.addException(e);
            }
            throw e;
        } finally {
            request.release();

            try {
                if (segment != null) {
                    rayFilter.postFilter(segment);
                }
            } catch (Throwable e) {
                logger.error("x-ray postFilter error", e);
            }

            MDC.remove(COMMAND_NAME);
            MDC.remove(REQUEST_PATH);
            MDC.remove(REQUEST_BODY);
            MDC.remove(RESP_CODE);
            MDC.remove(RESP_DATA);
            MDC.remove(REQUEST_TIME_IN_MS);
            MDC.remove(LATENCY_IN_MS);
            MDC.remove(TRACE_ID);
        }

    }

    //处理GET请求
    private void handleGetContent(ChannelHandlerContext ctx, FullHttpRequest fullRequest, String uri, HttpHeaders headers) {
        ApiDispatcher.dispatch(ctx, fullRequest, headers, null);
    }

    //处理POST请求
    private void handlePostContent(ChannelHandlerContext ctx, FullHttpRequest fullRequest, HttpHeaders headers) throws Exception{
        String contentType = getContentType(headers);
        switch (contentType) {
            case "application/json": {
                String jsonStr = fullRequest.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
                JSONObject obj = JSON.parseObject(jsonStr);
                ApiDispatcher.dispatch(ctx, fullRequest, headers, obj);
                break;
            }
            case "application/x-www-form-urlencoded":
            case "multipart/form-data": {
                ApiDispatcher.dispatch(ctx, fullRequest, headers, null);
                break;
            }
            case "application/octet-stream":  //binary
                LogicDispatcher.dispatch(ctx, fullRequest, headers);
                break;
            default:
                FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1,
                        HttpResponseStatus.FORBIDDEN);
                HttpResponser.doSend(ctx, fullRequest, response);
                break;
        }
    }

    private String getContentType(HttpHeaders headers) {
        if (!headers.contains("Content-Type")) {
            return "";
        }
        String typeStr = headers.get("Content-Type").toString();
        String[] list = typeStr.split(";");
        return list[0];
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("exceptionCaught e:", cause);
        ctx.close();
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

}

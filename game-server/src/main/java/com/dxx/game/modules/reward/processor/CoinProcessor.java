package com.dxx.game.modules.reward.processor;

import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.user.service.UserService;
import software.amazon.awssdk.enhanced.dynamodb.Expression;

/**
 * 金币处理器
 * <AUTHOR>
 * @date 2019-12-13 15:55
 */
@Component
public class CoinProcessor implements RewardProcessor {
	
	private static final Logger logger = LoggerFactory.getLogger(CoinProcessor.class);
	
	@Autowired
	private UserService userService;
	@Autowired
	private UserDao userDao;
	
	@Override
	public RewardType getType() {
		return RewardType.RESOURCE;
	}
	
	@Override
	public RewardResourceType getRewardResourceType() {
		return RewardResourceType.COINS;
	}

	@Override
	public RewardAction tryReward(long userId, Reward reward) {
		int resultCode = ErrorCode.SUCCESS;
		
		User user = userService.getUser(userId);
		if (user == null) {
			resultCode = ErrorCode.QUERY_USER_DATA_ERROR;
		} else {
			long coinResult = user.getCoins() + reward.getCount();
			if (coinResult < 0) {
				logger.error("coins not enough, userId:{}, realCount:{}, needCount:{}", userId, user.getCoins(), reward.getCount());
				resultCode = ErrorCode.COIN_IS_NOT_ENOUGH;
			}
		}
		return simpleRewardAction(reward, resultCode);
	}

	@Override
	public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
		if (rewardAction.isFailed()) {
			return null;
		}
		
		User user = userService.getUser(userId);
		int addCount = rewardAction.getReward().getCount();
		long current = user.getCoins() + addCount;
		
		if (current < 0) {
			logger.error("user coins error : userId : {}, coins: {}, addCount : {}", userId, user.getCoins(), addCount);
			return null;
		}

		Expression expression = Expression.builder().expression("#coins = :coins")
				.putExpressionName("#coins", "coins")
				.putExpressionValue(":coins", DynamoDBConvertUtil.buildAttributeValue(user.getCoins()))
				.build();
		user.addUpdateCondition(expression);
		user.setCoins(current);
		userDao.updateCoins(user);

		RewardResult<Long> result = new RewardResult<>(this.getType(), this.getRewardResourceType());
		result.setActualCount(addCount);
		result.setCurrent(current);
		
		return result;
	}
}



















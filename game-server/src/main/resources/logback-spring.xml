<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 日志根目录-->
    <springProperty scope="context" name="LOG_HOME" source="logging.path" defaultValue="/var/log/framework/gameserver"/>

    <!-- 日志级别 -->
    <springProperty scope="context" name="LOG_ROOT_LEVEL" source="logging.level.root" defaultValue="INFO"/>

    <!-- 标识这个"STDOUT" 将会添加到这个logger -->
    <springProperty scope="context" name="STDOUT" source="log.stdout" defaultValue="CONSOLE"/>

    <!-- 日志文件名称-->
    <property name="LOG_PREFIX" value="dxx" />

    <!-- 日志文件编码-->
    <property name="LOG_CHARSET" value="UTF-8" />

    <!-- 日志文件路径+日期-->
    <property name="LOG_DIR" value="${LOG_HOME}/%d{yyyyMMdd}" />

    <!-- 对日志进行格式化 -->
    <property name="LOG_MSG" value="[%d{yyyyMMdd HH:mm:ss.SSS}] | [%level] | [%thread] | [%logger{36}] | [%line] | --> %msg|%n"/>

    <!-- 文件大小，默认20MB -->
    <property name="MAX_FILE_SIZE" value="20MB" />

    <!-- 配置日志的滚动时间 ，表示只保留最近 30 天的日志 -->
    <property name="MAX_HISTORY" value="30"/>

    <!-- 输出到控制台 -->
    <appender name="CONSOLE" class="com.dxx.game.common.log.DxxLogConsoleAppender">
        <encoder>
            <charset>${LOG_CHARSET}</charset>
            <pattern>${LOG_MSG}</pattern>
        </encoder>
    </appender>

    <!-- 定义 INFO 日志的输出方式 -->
    <appender name="FILE_INFO" class="com.dxx.game.common.log.DxxLogRollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <File>${LOG_HOME}/info_${LOG_PREFIX}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_DIR}/info_${LOG_PREFIX}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder>
            <charset>${LOG_CHARSET}</charset>
            <pattern>${LOG_MSG}</pattern>
        </encoder>
    </appender>

    <!-- 定义 ERROR 日志的输出方式 -->
    <appender name="FILE_ERROR" class="com.dxx.game.common.log.DxxLogRollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <File>${LOG_HOME}/err_${LOG_PREFIX}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_DIR}/err_${LOG_PREFIX}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder>
            <charset>${LOG_CHARSET}</charset>
            <pattern>${LOG_MSG}</pattern>
        </encoder>
    </appender>

    <!-- 定义 DEBUG 日志的输出方式 -->
    <appender name="FILE_DEBUG" class="com.dxx.game.common.log.DxxLogRollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <File>${LOG_HOME}/debug_${LOG_PREFIX}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_DIR}/debug_${LOG_PREFIX}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder>
            <charset>${LOG_CHARSET}</charset>
            <pattern>${LOG_MSG}</pattern>
        </encoder>
    </appender>

    <!-- 定义数据日志的输出方式 -->
    <appender name="cheatLogAppender" class="com.dxx.game.common.log.DxxLogRollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <File>${LOG_HOME}/cheat_${LOG_PREFIX}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_DIR}/data_${LOG_PREFIX}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder>
            <charset>${LOG_CHARSET}</charset>
            <pattern>${LOG_MSG}</pattern>
        </encoder>
    </appender>

    <!-- 定义 SQL 日志的输出方式 -->
    <appender name="sqlDataAppender" class="com.dxx.game.common.log.DxxLogRollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
        <File>${LOG_HOME}/sql_${LOG_PREFIX}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_DIR}/sql_${LOG_PREFIX}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <MaxHistory>${MAX_HISTORY}</MaxHistory>
        </rollingPolicy>
        <encoder>
            <charset>${LOG_CHARSET}</charset>
            <pattern>${LOG_MSG}</pattern>
        </encoder>
    </appender>

    <!-- 不同的业务逻辑日志打印到指定文件夹 -->
    <logger name="CHEAT_LOG" additivity="false" level="INFO">
        <appender-ref ref="cheatLogAppender"/>
        <appender-ref ref="${STDOUT}"/>
    </logger>
    <logger name="ShardingSphere-SQL" additivity="false" level="INFO">
        <appender-ref ref="sqlDataAppender"/>
        <appender-ref ref="${STDOUT}"/>
    </logger>
    <logger name="org.apache.activemq.ActiveMQSession" additivity="false" level="ERROR">
        <appender-ref ref="sqlDataAppender"/>
        <appender-ref ref="${STDOUT}"/>
    </logger>
    <logger name="org.apache.activemq.transport.AbstractInactivityMonitor" level="ERROR" />

    <!-- ${LOG_ROOT_LEVEL} 日志级别 -->
    <root level="${LOG_ROOT_LEVEL}">
        <appender-ref ref="${STDOUT}"/>
        <appender-ref ref="FILE_INFO"/>
        <appender-ref ref="FILE_ERROR"/>
        <appender-ref ref="FILE_DEBUG"/>
    </root>

</configuration>

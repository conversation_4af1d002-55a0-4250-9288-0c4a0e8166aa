
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Reward_reward {
    public Reward_reward(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("rewards").getAsJsonArray(); rewards = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  rewards.add(_v0); }   }
    }

    public static Reward_reward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Reward_reward(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 奖励<br/>item表ID,数量|item表id,数量
     */
    public final java.util.List<String> rewards;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "rewards:" + rewards + ","
        + "}";
    }
}


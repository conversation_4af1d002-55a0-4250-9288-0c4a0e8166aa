package com.dxx.game.common.config.game;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.regex.Pattern;

import jakarta.annotation.PostConstruct;

import com.dxx.game.common.config.game.annotation.FieldMeta;
import com.dxx.game.common.config.game.converter.FieldConverter;
import com.dxx.game.common.server.context.ApplicationContextProvider;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.common.config.game.annotation.Resource;
import com.dxx.game.common.config.game.entity.BaseEntity;
import com.dxx.game.config.GameConfigManager;
import com.google.common.base.Preconditions;

@Slf4j
@Component
public class GameConfiguration<T> {

    @Autowired
    private ApplicationContext ac;
    @Autowired
    private GameConfigLoader gameConfigLoader;

    private Pattern pattern = Pattern.compile("[0-9]*");

    protected final String packageName = "com.dxx.game.config.entity.";

    @PostConstruct
    private void init() {
        this.loadConfig(false);
    }

    /**
     * 重新加载配置文件
     *
     * @param isLocal 是否加载本地文件
     */
    public void reload(boolean isLocal) {
        this.loadConfig(isLocal);
    }

    private void loadConfig(boolean isLocal) {
        try {
            if (getClass().isAnnotationPresent(Resource.class)) {
                Resource source = Preconditions.checkNotNull(getClass().getAnnotation(Resource.class));
                String sourceName = source.name() + "." + source.suffix();
                String content = "";
                if (isLocal) {
                    content = gameConfigLoader.loadConfigFromLocal(sourceName);
                } else {
                    content = gameConfigLoader.loadConfig(sourceName);
                }

                setUpConfig(content, source);
                gameConfigLoader.addClass(sourceName, this.getClass());
            }
        } catch (Exception e) {
            log.error("load config failed, e:", e);
            throw new RuntimeException("load config failed");

        }
    }

    @SuppressWarnings("unchecked")
    private void setUpConfig(String jsonStr, Resource source) {
        Map<String, Object> configMap = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {
        });

        // 总的map数据
        Map<String, Map<Object, BaseEntity>> configData = new HashMap<>();
        for (Entry<String, Object> entry : configMap.entrySet()) {

            try {
                Map<Object, Object> sheet = (Map<Object, Object>) entry.getValue();

                // 反射对应的实体类
                Class<?> classType = Class.forName(this.packageName + source.name().toLowerCase() + "." + ucFirst(entry.getKey()) + "Entity");

                // 每个key下的数据字典
                ConcurrentSkipListMap<Object, BaseEntity> sheetConfigData = new ConcurrentSkipListMap<Object, BaseEntity>();

                // 装载实体类
                for (Entry<Object, Object> config : sheet.entrySet()) {
                    Object cls = classType.newInstance();
                    Map<String, Object> value = null;
                    value = (Map<String, Object>) config.getValue();

                    Map<Integer, FieldDescriptor> modelMeta = modelMeta(classType);

                    // 给实体类字段赋值
                    for (FieldDescriptor fd : modelMeta.values()) {

                        Field field = fd.getField();
                        if (value.get(field.getName()) == null) {
                            continue;
                        }
                        field.set(cls, fd.getConverter().convert(field, value.get(field.getName())));
                    }

                    // 存入map数据字典
                    if (pattern.matcher(config.getKey().toString()).matches()) {
                        sheetConfigData.put(Integer.parseInt(config.getKey().toString()), (BaseEntity) cls);
                    } else {
                        sheetConfigData.put(config.getKey().toString(), (BaseEntity) cls);
                    }

                }
                // 存入总字典
                configData.put(entry.getKey().toLowerCase(), sheetConfigData);
            } catch (Exception e) {
                log.error("config setup error: {} [{}, {}]", source.name(), entry.getKey(), entry.getValue());
                if (!(e instanceof ClassNotFoundException)) {
                    throw new RuntimeException("load config failed," + e.getClass().getName() + "|" + e.getMessage());
                }

            }
        }


        // 装载到对应的config object Map中
        for (Field f : this.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            try {
                if (f.getGenericType().getTypeName().equals("org.slf4j.Logger")) {
                    continue;
                }
                f.set(this, configData.get(f.getName().toLowerCase()));
            } catch (Exception e) {
                log.error("setUpConfig, e:",e);
            }
        }
    }

    /**
     * 封装实体类
     *
     * @param cls
     * @return
     */
    public Map<Integer, FieldDescriptor> modelMeta(Class<?> cls) {
        Map<Integer, FieldDescriptor> modelMeta = new HashMap<>();

        for (Field field : cls.getDeclaredFields()) {
            FieldMeta anno = field.getAnnotation(FieldMeta.class);
            if (anno != null) {

                field.setAccessible(true);
                int index = anno.index();

                FieldConverter converter = ac.getBean(anno.converter());
                boolean required = anno.required();
                FieldDescriptor fieldDescriptor = new FieldDescriptor(index, field, converter, required);
                modelMeta.put(index, fieldDescriptor);
            }
        }
        return modelMeta;
    }

    /**
     * 首字母转换成大写
     *
     * @param str
     * @return
     */
    public static String ucFirst(String str) {
        char[] charArray = str.toCharArray();
        if (charArray[0] >= 'a' && charArray[0] <= 'z') {
            charArray[0] -= 32;
        }
        return String.valueOf(charArray);
    }

}








































































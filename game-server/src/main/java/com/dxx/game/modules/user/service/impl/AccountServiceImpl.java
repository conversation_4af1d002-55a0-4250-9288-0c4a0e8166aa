package com.dxx.game.modules.user.service.impl;

import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.channel.ChannelFactory;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.habby.HabbyService;
import com.dxx.game.common.error.GameError;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.AccountStatus;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.Account;
import com.dxx.game.dao.dynamodb.repository.AccountDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dto.UserProto;
import com.dxx.game.modules.user.service.AccountService;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Expression;

import java.util.Objects;
import java.util.UUID;

/**
 * @author: lsc
 * @createDate: 2025/5/22
 * @description:
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {

    @Resource
    private AccountDao accountDao;
    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserDao userDao;
    @Resource
    private HabbyService habbyService;


    @Override
    public boolean verifyLogin(int channelId, String accountId, String verification) {
        if (!channelConfig.isVerifyLogin()) {
            log.warn("verifyLogin is not open, channelId:[{}], accountId:[{}], verification:[{}]", channelId, accountId, verification);
            return true;
        }
        ChannelService channelService = ChannelFactory.getService(channelId);
        if (channelService == null) {
            log.error("channelService is null, channelId:[{}], accountId:[{}], verification:[{}]", channelId, accountId, verification);
            return false;
        }
        // unity登录不需要验证verification是否为空
        if (channelId != 0 && StringUtils.isEmpty(verification)) {
            log.error("verifyLogin failed, channelId:[{}], accountId:[{}], verification:[{}]", channelId, accountId, verification);
            return false;
        }
        boolean flag = channelService.verifyLogin(accountId, verification);
        if (!flag) {
            log.error("verifyLogin failed, channelId:[{}], accountId:[{}], verification:[{}]", channelId, accountId, verification);
        }
        return flag;
    }

    @Override
    public Account accountLogin(UserProto.UserLoginRequest loginRequest) {
        Account account = getAccount(loginRequest.getCommonParams().getAccountId(), "", loginRequest.getCommonParams().getDeviceId());
        if (account == null) {
            // 国内-验证登录
            if (gameConfigManager.isCn() && !verifyLogin(loginRequest.getChannelId(), loginRequest.getCommonParams().getAccountId(), loginRequest.getVerification())) {
                throw new GameError(ErrorCode.VERIFY_LOGIN_FAILED);
            }
            // TODO 因为一致性问题account可能会取到null
            account = this.creatAccount(loginRequest.getCommonParams().getAccountId(), "", loginRequest.getCommonParams().getDeviceId(), loginRequest.getChannelId());
        } else {
            // 虽然存在 account，但是还是需要去各个渠道验证下
            if (gameConfigManager.isCn() && !verifyLogin(loginRequest.getChannelId(), account.getAccountId(), loginRequest.getVerification())) {
                throw new GameError(ErrorCode.VERIFY_LOGIN_FAILED);
            }
        }

        //最终一致性
        return account;
    }

    private Account creatAccount(String accountId, String accountId2, String deviceId, int channelId) {
        var account = new Account();
        account.setPK(UUID.randomUUID().toString());
        if (StringUtils.isNotEmpty(accountId)) {
            account.setAccountId(accountId);
        }
        if (StringUtils.isNotEmpty(accountId2)) {
            account.setAccountId2(accountId2);
        }
        if (StringUtils.isNotEmpty(deviceId)) {
            account.setDeviceId(deviceId);
        }
        account.setChannelId(channelId);
        account.setLastLoginUserId(0L);
        account.setServerUserIdMap(Maps.newHashMap());
        accountDao.insert(account);
        return account;
    }

    @Override
    public Account getAccount(String accountId, String accountId2, String deviceId) {
        //根据accountId查询
        Account account = null;
        if (StringUtils.isNotEmpty(accountId)) {
            account = accountDao.getByAccountId(accountId);
        }
        if (account == null && StringUtils.isNotEmpty(deviceId)) {
            account = accountDao.getByDeviceId(deviceId);
        }
        if (account != null) {
            account = accountDao.getByAccountKey(account.getPK());
            if (StringUtils.isNotEmpty(accountId)) {
                if (StringUtils.isEmpty(account.getAccountId())) {
                    // 绑定账号
                    account.setAccountId(accountId);
                    accountDao.updateAccountId(account);
                } else if (!Objects.equals(accountId, account.getAccountId())) {
                    //设备和账号冲突，建新号,旧帐号解绑
                    unboundDevice(account);
                    return null;
                }
            }

            if (StringUtils.isNotEmpty(accountId2)) {
                if (StringUtils.isEmpty(account.getAccountId2())) {
                    // 绑定账号
                    account.setAccountId2(accountId2);
                    accountDao.updateAccountId2(account);
                }
            }
        }
        if (account != null && StringUtils.isNotEmpty(deviceId)) {
            if (!Objects.equals(deviceId, account.getDeviceId())) {
                //老帐号解绑
                var oldAccounts = accountDao.getAllAccountByDeviceId(deviceId);
                for (Account oldAccount : oldAccounts) {
                    unboundDevice(oldAccount);
                }
                account.setDeviceId(deviceId);
                accountDao.updateDeviceId(account);

            }
        }
        return account;
    }

    @Override
    public void resetAccount(Account account) {
        account.setAccountId(account.getAccountId() + "_unbound");
        account.setAccountId2(account.getAccountId2() + "_unbound");
        account.setDeviceId(account.getDeviceId() + "_unbound");
//        if (!StringUtils.isEmpty(account.getHabbyId())) {
//            //todo 是否需要？
//            if (habbyService.unbindHabbyId(account.getPK(), account.getHabbyId()) == 0) {
//                account.setHabbyId(null);
//            }
//        }
        accountDao.update(account);
        if (account.getLastLoginUserId() != null && account.getLastLoginUserId() > 0) {
            var user = userDao.getByUserId(account.getLastLoginUserId());
            if (user == null) {
                return;
            }
            user.setAccountStatus(AccountStatus.DELETE);
            userDao.updateAccount(user);

        }
    }

    private void unboundDevice(Account account) {
        Expression expression = Expression.builder().expression("#deviceId = :deviceId")
                .putExpressionName("#deviceId", "deviceId")
                .putExpressionValue(":deviceId", DynamoDBConvertUtil.buildAttributeValue(account.getDeviceId()))
                .build();
        account.addUpdateCondition(expression);
        account.setDeviceId(account.getDeviceId() + "_unbound");
        accountDao.updateDeviceId(account);
    }
}

//package com.dxx.game.common.config.game.dirtywords;
//
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.TypeReference;
//import com.dxx.game.common.config.game.GameConfigLoader;
//import com.dxx.game.consts.ErrorCode;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import jakarta.annotation.PostConstruct;
//import java.io.UnsupportedEncodingException;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * 屏蔽词
// *
// * <AUTHOR>
// * @date 2021/1/27 15:18
// */
//@Component
//@Slf4j
//public class DirtyWordsService {
//    // 屏蔽词
//    private Map<String, DirtyWordsEntity> dirtyWordsEntityMap = new HashMap<>();
//    // 特殊词汇 - 比如国家领导人姓名
//    private ConcurrentHashMap<String, List<String>> specialWordsMap = new ConcurrentHashMap<>();
//
//    @Autowired
//    private GameConfigLoader gameConfigLoader;
//
//    private static com.dxx.game.common.config.game.dirtywords.DirtyWordsService dirtyWordsService;
//
//    @SuppressWarnings("unused")
//    @PostConstruct
//    private void init() {
//        dirtyWordsService = this;
//        reload();
//    }
//
//    public static void reload() {
//        try {
//            String content = dirtyWordsService.gameConfigLoader.loadConfig("dirtywords.json");
//
//            Map<String, String> configMap = JSONObject.parseObject(content, new TypeReference<Map<String, String>>() {
//            });
//            configMap.forEach((key, value) -> {
//                if (key.equals("normal")) {
//                    Map<String, String> normalMap = JSONObject.parseObject(value, new TypeReference<Map<String, String>>() {
//                    });
//                    normalMap.forEach((nkey, nValue) -> {
//                        dirtyWordsService.dirtyWordsEntityMap.put(nkey, JSONObject.parseObject(nValue, DirtyWordsEntity.class));
//                    });
//
//                } else {
//                    dirtyWordsService.specialWordsMap.clear();
//                    List<String> specialList = JSONObject.parseObject(value, new TypeReference<List<String>>() {
//                    });
//                    for (String specialStr : specialList) {
//                        String firstChar = "";
//                        for (int i = 0; i < specialStr.length(); i++) {
//                            String specialChar = specialStr.substring(i, i + 1);
//                            if (i == 0) {
//                                firstChar = specialChar;
//                                if (!dirtyWordsService.specialWordsMap.containsKey(specialChar)) {
//                                    dirtyWordsService.specialWordsMap.put(specialChar, new ArrayList<>());
//                                }
//                            }
//                            dirtyWordsService.specialWordsMap.get(firstChar).add(specialChar);
//                        }
//                    }
//                }
//            });
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    /**
//     * 检验是否有屏蔽词
//     *
//     * @param words
//     * @return
//     */
//    public static boolean isLegal(String words) {
//        words = words.replaceAll(" ", "");
//        for (int i = 0, length = words.length(); i < length; i++) {
//            String charWord = words.substring(i, 1 + i);
//
//            DirtyWordsEntity dirtyWordsEntity = dirtyWordsService.dirtyWordsEntityMap.get(charWord);
//
//            if (dirtyWordsEntity == null) {
//                continue;
//            }
//
//            Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]");
//            Matcher matcher = pattern.matcher(charWord);
//            if (matcher.find()) {
//                // 中文
//                String lastWords = words.substring(i, words.length());
//                lastWords = lastWords.replaceAll("[^\u4e00-\u9fa5]", "");
//
//                int lastWordsLen = lastWords.length();
//                int loopCnt = (0xFFFFFFFF >>> (32 - lastWordsLen));
//
//                for (int j = 1; j <= loopCnt; j++) {
//                    StringBuilder comWords = new StringBuilder();
//                    for (int k = 0; k < lastWordsLen; k++) {
//                        if ((j << (31 - k)) >> 31 == -1) {
//                            comWords.append(lastWords.substring(k, k + 1));
//                        }
//                    }
//
//                    int comWordsLength = comWords.toString().length();
//                    if (comWordsLength < dirtyWordsEntity.getMinLen() || comWordsLength > dirtyWordsEntity.getMaxLen()) {
//                        continue;
//                    }
//
//                    if (dirtyWordsEntity.getList().contains(comWords.toString())) {
//                        log.error("find_dirty_words, words:{}, filters:{}", words, comWords.toString());
//                        return true;
//                    }
//                }
//
//            } else {
//                // 其他
//                for (int j = 0; j < dirtyWordsEntity.getMaxLen(); j++) {
//                    int len = 1 + j + i;
//                    if (len > words.length()) {
//                        break;
//                    }
//                    String str = words.substring(i, 1 + j + i);
//                    if (dirtyWordsEntity.getList().contains(str)) {
//                        i += j;
//                        log.error("find_dirty_words, words:{}, filters:{}", words, str);
//                        return true;
//                    }
//                }
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 过滤屏蔽词， 将屏蔽词转换成**
//     *
//     * @param words
//     * @return
//     */
//    public static String filterDirtyWords(String words) {
//        for (int i = 0, length = words.length(); i < length; i++) {
//            String charWord = words.substring(i, 1 + i);
//            if (charWord.equals("*") || charWord.equals(" ")) {
//                continue;
//            }
//            DirtyWordsEntity dirtyWordsEntity = dirtyWordsService.dirtyWordsEntityMap.get(charWord);
//            if (dirtyWordsEntity == null) {
//                continue;
//            }
//
//            if (dirtyWordsService.specialWordsMap.containsKey(charWord)) {
//                // 特殊处理 -- 比如国家领导人姓名
//                List<String> specialList = dirtyWordsService.specialWordsMap.get(charWord);
//                List<Integer> findIndexList = new ArrayList<>();
//                for (String str : specialList) {
//                    int findIndex = words.indexOf(str);
//                    if (findIndex >= 0) {
//                        findIndexList.add(findIndex);
//                    }
//                }
//                if (findIndexList.size() >= 3) {
//                    for (String str : specialList) {
//                        words = words.replaceAll(str, "*");
//                    }
//                }
//            } else {
//                // 其他
//                for (int j = 0; j < dirtyWordsEntity.getMaxLen(); j++) {
//                    int len = 1 + j + i;
//                    if (len > words.length()) {
//                        break;
//                    }
//                    String str = words.substring(i, 1 + j + i);
//                    String findStr = str;
//                    int strLen = str.length();
//                    str = str.replaceAll(" ", "");
//                    if (dirtyWordsEntity.getList().contains(str)) {
//                        String replaceStr = "";
//                        for (int k = 0; k < strLen; k++) {
//                            replaceStr += "*";
//                        }
//                        words = words.replace(findStr, replaceStr);
//                        i += j;
//                        break;
//                    }
//                }
//            }
//        }
//
//        return words;
//
//    }
//
//    /**
//     * 判断字符串是否只包含简体中文和数字
//     *
//     * @param str
//     * @return
//     */
//    public static int checkSimChineseAndNum(String str) {
//        // 判断字符串是否只包含中文和数字
//        Pattern pattern = Pattern.compile("[\u4e00-\u9fa50-9]+");
//        Matcher matcher = pattern.matcher(str);
//        if (!matcher.matches()) {
//            Pattern enPattern = Pattern.compile("[a-zA-Z]+");
//            Matcher matcherEn = enPattern.matcher(str);
//            if (matcherEn.find()) {
//                return ErrorCode.USER_NICKNAME_HAS_EN;
//            } else {
//                return ErrorCode.USER_NICKNAME_HAS_LEGAL;
//            }
//        }
//        // 判断字符串是否包含繁体字
//        String simEncode = "GB2312";
//        try {
//            if (!str.equals(new String(str.getBytes(simEncode), simEncode))) {
//                return ErrorCode.USER_NICKNAME_HAS_FAN;
//            }
//        } catch (UnsupportedEncodingException e) {
//            return ErrorCode.USER_NICKNAME_HAS_FAN;
//        }
//        return 0;
//    }
//}

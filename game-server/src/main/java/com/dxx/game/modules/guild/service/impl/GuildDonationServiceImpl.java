package com.dxx.game.modules.guild.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guild.GuildDonationEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildMessage;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildMessageDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.guild.service.GuildDonationService;
import com.dxx.game.dto.GuildProto.*;
import com.dxx.game.modules.guild.support.GuildSupport;
import com.dxx.game.modules.message.service.GuildMessageService;
import com.dxx.game.modules.message.service.MessageService;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @authoer: lsc
 * @createDate: 2023/5/25
 * @description:
 */
@Service
public class GuildDonationServiceImpl implements GuildDonationService {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GuildSupport guildSupport;
    @Resource
    private GuildDao guildDao;
    @Resource
    private UserService userService;
    @Resource
    private GuildMessageService guildMessageService;
    @Resource
    private GuildMessageDao guildMessageDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private RewardService rewardService;
    @Resource
    private MessageService messageService;

    @DynamoDBTransactional
    @Override
    public Result<GuildDonationReqItemResponse> reqItemAction(GuildDonationReqItemRequest params) {
        long userId = RequestContext.getUserId();
        int itemId = params.getItemId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        // 判断间隔(10s容错)
        if (guildUser.getReqItemTM() != null && guildUser.getReqItemTM() > DateUtils.getUnixTime() + 10) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        // 判断条件
        GuildDonationEntity guildDonationEntity = gameConfigManager.getGuildConfig().getGuildDonationEntity(itemId);
        if (guildDonationEntity == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (!guildDonationEntity.getGuildLevel().contains(guild.getGuildLevel())) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        int chapterId = userService.getChapterId(userId);
        if (guildDonationEntity.getChapterId().get(0) > chapterId || chapterId > guildDonationEntity.getChapterId().get(1)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int seconds = gameConfigManager.getGuildConfig().getGuildConst().get(133).getTypeInt();
        User user = userService.getUser(userId);
        GuildMessage.RequestItemModel model = new GuildMessage.RequestItemModel();
        long msgId = messageService.generateGuildMsgId(guild.getGuildId());
        model.setMsgId(msgId);
        model.setUserId(userId);
        model.setNickName(Optional.ofNullable(user.getNickName()).orElse(""));
        model.setItemId(itemId);
        model.setCount(0);
        model.setMaxCount(guildDonationEntity.getCount());
        model.setExpiredTM(DateUtils.getUnixTime() + seconds);
        model.setTimestamp(DateUtils.getUnixTime());

        // 更新数据
        int cdSeconds = gameConfigManager.getGuildConfig().getGuildConst().get(132).getTypeInt();
        guildUser.setReqItemTM(DateUtils.getUnixTime() + cdSeconds);
        guildUserDao.updateDonation(guildUser);

        // 推送消息
        guildMessageService.publishDonation(guildUser.getGuildId(), userId, model);

        GuildDonationReqItemResponse.Builder response = GuildDonationReqItemResponse.newBuilder();
        response.setGuildDonationDto(this.buildGuildDonationDto(guildUser));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildDonationSendItemResponse> sendItemAction(GuildDonationSendItemRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }



        long msgId = params.getMsgId();
        long guildId = guildUser.getGuildId();
        String lockKey = this.getLockKey(guildId, msgId);
        if (!redisLock.lock(lockKey, String.valueOf(userId))) {
            // 加锁失败，返回请求重复，让客户端重新请求
            return Result.Error(ErrorCode.REQUEST_DATA_DUPLICATE);
        }
        GuildMessage guildMessage = guildMessageDao.getGuildMessageByKey(guildId, msgId);
        if (guildMessage == null || guildMessage.getRequestItem() == null) {
            return Result.Error(ErrorCode.GUILD_DONATION_NOT_EXIST);
        }
        // 捐赠消息已过期
        if (DateUtils.getUnixTime() - 10 >= guildMessage.getRequestItem().getExpiredTM()) {
            return Result.Error(ErrorCode.GUILD_DONATION_EXPIRED);
        }

        // 判断是不是自己发布的
        GuildMessage.RequestItemModel requestItemModel = guildMessage.getRequestItem();
        if (requestItemModel.getUserId() == userId) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (requestItemModel.getDonorUserId() != null && requestItemModel.getDonorUserId().contains(userId)) {
            return Result.Error(ErrorCode.GUILD_DONATED);
        }

        // 判断是否已满
        if (requestItemModel.getCount() >= requestItemModel.getMaxCount()) {
            return Result.Error(ErrorCode.GUILD_DONATION_ITEM_MAX);
        }

        // 扣除道具
        RewardResultSet costResultSet = rewardService.executeReward(userId, Lists.newArrayList(requestItemModel.getItemId(), -1));
        if (costResultSet.isFailed()) {
            return Result.Error(costResultSet.getResultCode());
        }
        // 是否给其他奖励
        RewardResultSet rewardResultSet = null;
        GuildDonationEntity guildDonationEntity = gameConfigManager.getGuildConfig().getGuildDonationEntity(requestItemModel.getItemId());
        if (guildDonationEntity.getReward() != null && !guildDonationEntity.getReward().isEmpty()) {
            // 筛选公会货币最大数量
            List<List<Integer>> rewards = guildDonationEntity.getReward();
            int maxCnt = gameConfigManager.getGuildConfig().getGuildConst().get(134).getTypeInt();

            int donationItemCount = Optional.ofNullable(guildUser.getDonationItemCount()).orElse(0);
            // 1000  990 + 20;
            for (List<Integer> reward : rewards) {
                if (reward.get(0) == 400000001) {
                    if (donationItemCount + reward.get(1) >= maxCnt) {
                        reward.set(1, maxCnt - donationItemCount);
                    }
                    if (reward.get(1) <= 0) {
                        rewards.remove(reward);
                    }
                    // 更新数量
                    guildUser.setDonationItemCount(donationItemCount + reward.get(1));
                }
            }

            if (!rewards.isEmpty()) {
                rewardResultSet = rewardService.executeRewards(userId, rewards);
            }
        }

        if (requestItemModel.getDonorUserId() == null) {
            requestItemModel.setDonorUserId(new ArrayList<>());
        }

        // 更新数据
        requestItemModel.getDonorUserId().add(userId);
        requestItemModel.setCount(requestItemModel.getCount() + 1);
        // 数量上限将消息设置为过期
        if (requestItemModel.getCount() >= requestItemModel.getMaxCount()) {
            requestItemModel.setExpiredTM(DateUtils.getUnixTime());
        }
        guildMessage.setRequestItem(requestItemModel);
        guildMessageDao.update(guildMessage);

        guildUserDao.updateDonation(guildUser);

        // 推送捐赠道具数量变化
        guildMessageService.publishDonationChangeCount(guildId, requestItemModel.getMsgId(), requestItemModel.getCount());

        // 记录日志
        User user = userService.getUser(userId);
        String nickName = Optional.ofNullable(user.getNickName()).orElse("");
        long msgId1 = messageService.generateGuildMsgId(guildId);
        long msgId2 = messageService.generateGuildMsgId(guildId);
        guildMessageDao.addDonationSendRecord(guildUser.getGuildId(), msgId1, msgId2,
                userId, nickName, guildMessage.getRequestItem().getUserId(), guildMessage.getRequestItem().getNickName(),
                guildMessage.getRequestItem().getItemId(), 1);

        GuildDonationSendItemResponse.Builder response = GuildDonationSendItemResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet, rewardResultSet));
        response.setGuildDonationDto(this.buildGuildDonationDto(guildUser));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildDonationGetRecordsResponse> getRecordsAction(GuildDonationGetRecordsRequest params) {
        long userId = RequestContext.getUserId();
        long msgId = params.getMsgId();
        int limit = 20;
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        GuildDonationGetRecordsResponse.Builder response = GuildDonationGetRecordsResponse.newBuilder();
        List<GuildMessage> guildMessageList = guildMessageDao.queryDonationsReqItems(guildUser.getGuildId(), msgId, limit);
        for (GuildMessage guildMessage : guildMessageList) {
            if (guildMessage.getRequestItem() != null) {
                // 消息已过期
                if (DateUtils.getUnixTime() >= guildMessage.getRequestItem().getExpiredTM()) {
                    continue;
                }
                // 捐赠数据
                if (guildMessage.getRequestItem().getDonorUserId() != null && guildMessage.getRequestItem().getDonorUserId().contains(userId)) {
                    guildMessage.getRequestItem().setDonated(true);
                }

                response.addRecords(guildMessage.getRequestItem().toJsonString());
            }
        }
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildDonationReceiveResponse> receiveAction(GuildDonationReceiveRequest params) {
        long userId = RequestContext.getUserId();
        long msgId = params.getMsgId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        String lockKey = this.getLockKey(guildUser.getGuildId(), msgId);
        if (!redisLock.lock(lockKey, String.valueOf(userId))) {
            // 加锁失败，返回请求重复，让客户端重新请求
            return Result.Error(ErrorCode.REQUEST_DATA_DUPLICATE);
        }
        GuildMessage guildMessage = guildMessageDao.getGuildMessageByKey(guildUser.getGuildId(), msgId);
        if (guildMessage == null || guildMessage.getRequestItem() == null) {
            return Result.Error(ErrorCode.GUILD_DONATION_EXPIRED);
        }
        if (guildMessage.getUserId() != userId) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        int itemId = guildMessage.getRequestItem().getItemId();
        int receiveCount = guildMessage.getRequestItem().getReceiveCnt();
        int count = guildMessage.getRequestItem().getCount();
        int addCnt = count - receiveCount;
        if (addCnt <= 0) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }
        // 发奖励
        RewardResultSet rewardResultSet = rewardService.executeReward(userId, Lists.newArrayList(itemId, addCnt));
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }
        // 更新领取数量
        guildMessage.getRequestItem().setReceiveCnt(guildMessage.getRequestItem().getReceiveCnt() + addCnt);
        guildMessageDao.update(guildMessage);

        // 全部领取之后移除数据
        if (guildMessage.getRequestItem().getReceiveCnt() >= guildMessage.getRequestItem().getMaxCount()) {
            guildUserDao.updateDonation(guildUser);
        }


        GuildDonationReceiveResponse.Builder response = GuildDonationReceiveResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setReceiveCount(guildMessage.getRequestItem().getReceiveCnt());
        return Result.Success(response.build());
    }

    /**
     * {"itemId":6102,"fromNickName":"","fromUserId":10001734,"msgId":333,"type":2,"itemCount":1,"timestamp":1685326021}
     * {"itemId":6102,"toUserNickName":"t332","msgId":332,"type":1,"toUserId":10001733,"itemCount":1,"timestamp":1685326021}
     * [type:类型(1赠送给别人,2别人赠送给我),toUserNickName:赠送的昵称，toUserId:赠送的用户ID,fromNickName:赠送给我的昵称,fromUserId:赠送给我的用户ID,itemId:道具id,itemCount:道具数量,timestamp:时间戳,msgId:消息ID]
     * @param params
     * @return
     */
    @DynamoDBTransactional
    @Override
    public Result<GuildDonationGetOperationRecordsResponse> getOperationRecordsAction(GuildDonationGetOperationRecordsRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        long msgId = params.getMsgId();
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        GuildDonationGetOperationRecordsResponse.Builder response = GuildDonationGetOperationRecordsResponse.newBuilder();

        int pageIndex = params.getPageIndex();
        int maxPage = gameConfigManager.getGuildConfig().getGuildConstEntity(137).getTypeInt();
        if (maxPage >= pageIndex) {
            int limit = gameConfigManager.getGuildConfig().getGuildConstEntity(136).getTypeInt();
            List<GuildMessage> guildMessageList = guildMessageDao.queryDonationOperations(guildUser.getGuildId(), userId, msgId, limit);
            for (GuildMessage guildMessage : guildMessageList) {
                response.addRecords(guildMessage.getMessageContent());
            }
        }

        return Result.Success(response.build());
    }

    @Override
    public GuildDonationDto buildGuildDonationDto(GuildUser guildUser) {
        int donationItemCount = Optional.ofNullable(guildUser.getDonationItemCount()).orElse(0);
        GuildDonationDto.Builder result = GuildDonationDto.newBuilder();
        result.setRequestItemTimestamp(guildUser.getReqItemTM());
        int maxCnt = gameConfigManager.getGuildConfig().getGuildConst().get(134).getTypeInt();
        int lastCnt = Math.min(donationItemCount, maxCnt);
        result.setDonationItemCount(lastCnt);
        result.setDonationMaxItemCount(maxCnt);
        return result.build();
    }

    @Override
    public void removeDonationRequestItems(long guildId, long userId) {
        List<GuildMessage> guildMessages = guildMessageDao.getSelfDonationReqItems(guildId, userId);
        List<Long> msgIds = new ArrayList<>(guildMessages.size());
        for (GuildMessage guildMessage : guildMessages) {
            guildMessageDao.delete(guildMessage);
            msgIds.add(guildMessage.getMsgId());
        }

        guildMessageService.publishDonationDelete(guildId, msgIds);
    }

    @Override
    public List<List<Integer>> getReceiveItems(long guildId, long userId) {
        List<GuildMessage> guildMessageList = guildMessageDao.getSelfDonationReqItems(guildId, userId);
        List<List<Integer>> itemsConfig = null;

        if (guildMessageList != null && !guildMessageList.isEmpty()) {
            itemsConfig = new ArrayList<>();
            for (GuildMessage guildMessage : guildMessageList) {
                boolean isAdd = false;
                if (guildMessage.getRequestItem() == null) {
                    continue;
                }

                if (guildMessage.getRequestItem().getCount() > guildMessage.getRequestItem().getReceiveCnt()) {
                    // 添加道具
                    String lockKey = this.getLockKey(guildId, guildMessage.getMsgId());
                    if (redisLock.lockWithOutRetry(lockKey, String.valueOf(userId))) {
                        int itemId = guildMessage.getRequestItem().getItemId();
                        int addCnt = guildMessage.getRequestItem().getCount() - guildMessage.getRequestItem().getReceiveCnt();
                        itemsConfig.add(Lists.newArrayList(itemId, addCnt));
                        guildMessage.getRequestItem().setReceiveCnt(guildMessage.getRequestItem().getCount());
                        guildMessageDao.update(guildMessage);
                        isAdd = true;
                    }
                }
                // 移除数据
                if (DateUtils.getUnixTime() >= guildMessage.getRequestItem().getExpiredTM()) {
                    RequestContext.setCommand(MsgReqCommand.GuildDonationReceiveRequest);
                    guildMessageDao.delete(guildMessage);

                    if (!isAdd) {
                        int addCnt = guildMessage.getRequestItem().getCount() - guildMessage.getRequestItem().getReceiveCnt();
                        if (addCnt > 0) {
                            int itemId = guildMessage.getRequestItem().getItemId();
                            itemsConfig.add(Lists.newArrayList(itemId, addCnt));
                        }
                    }
                }
            }
        }
        return itemsConfig;
    }

    private String getLockKey(long guildId, long msgId) {
        return "donation_send_item:" + guildId + ":" + msgId;
    }
}

package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.user.service.UserService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.QUERY_USER_ORDER_REWARDS)
public class GMQueryUserOrderRewards extends AbstractGMProcessor {

    @Resource
    private UserService userService;
    @Resource
    private PayService payService;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long userId;
        private String productId;
    }


    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        long userId = request.getUserId();
        String productId = request.getProductId();
        User user = userService.getUser(userId);
        CommonProto.CommonParams.Builder commonParams = CommonProto.CommonParams.newBuilder();
        commonParams.setVersion(user.getClientNetVersion());
        RequestContext.setCommonParams(commonParams.buildPartial());
        Map<String, Object> result = payService.getRechargeOrderItems(userId, Integer.parseInt(productId));
        if (result == null) {
            return this.error(GMErrorCode.ERROR_CODE_NO_REWARD, "没有奖励");
        }

        return this.success(result);
    }
}

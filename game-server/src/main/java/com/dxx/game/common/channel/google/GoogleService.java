package com.dxx.game.common.channel.google;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.google.config.GoogleConfig;
import com.dxx.game.common.utils.DateUtils;
import com.google.api.client.auth.oauth2.BearerToken;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpExecuteInterceptor;
import com.google.api.client.http.HttpRequest;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.api.services.games.Games;
import com.google.api.services.games.model.ApplicationVerifyResponse;
import com.google.api.services.games.model.Player;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jose4j.jwe.JsonWebEncryption;
import org.jose4j.jws.JsonWebSignature;
import org.jose4j.jwx.JsonWebStructure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Properties;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/5/9 8:48 下午
 */
@Slf4j
@Component
public class GoogleService {

    @Autowired
    private ChannelConfig channelConfig;

    private PrivateKey privateKey = null;
    private HttpTransport transport;
    private GoogleCredential googleCredential;
    private AndroidPublisher publisher;


    @PostConstruct
    public void init() throws GeneralSecurityException, IOException {
        initPrivateKey();
        if (this.privateKey == null) {
            log.error("google privateKey init failed, retry");
            initPrivateKey();
        }

        // 设置 google 请求代理 NetHttpTransport.defaultProxy()
        // Properties systemProperties = System.getProperties();
        // systemProperties.setProperty("https.proxyHost", proxyAddress);
        // systemProperties.setProperty("https.proxyPort", proxyPort);


        this.transport = GoogleNetHttpTransport.newTrustedTransport();;
        this.googleCredential = new GoogleCredential.Builder()
                .setTransport(transport)
                .setJsonFactory(JacksonFactory.getDefaultInstance())
                .setServiceAccountId(channelConfig.getGoogleConfig().getPay().getClientEmail())
                .setServiceAccountScopes(AndroidPublisherScopes.all())
                .setServiceAccountPrivateKey(this.privateKey).build();
        this.publisher = new AndroidPublisher.
                Builder(transport, JacksonFactory.getDefaultInstance(), googleCredential)
                .setApplicationName(channelConfig.getGoogleConfig().getPay().getApplicationName())
                .build();
    }

    /**
     * 初始化私钥
     */
    private void initPrivateKey() {
        try {
            String privateKeyString = channelConfig.getGoogleConfig().getPay().getPrivateKey();
            byte[] keyBytes = Base64.getMimeDecoder().decode(privateKeyString);
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            this.privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 校验订单状态
     * @param packageName
     * @param productId
     * @param purchaseToken
     * @return
     */
    public ProductPurchase checkOrderState(String packageName, String productId, String orderId, String purchaseToken) {
        ProductPurchase purchase = null;
        try {
            AndroidPublisher.Purchases.Products products = this.publisher.purchases().products();
            AndroidPublisher.Purchases.Products.Get product = products.get(packageName, productId, purchaseToken);
            purchase = product.execute();
        } catch (Exception e) {
            log.error("google checkOrderState error msg:{}, packageName:{}, productId:{}, orderId:{}, purchaseToken:{}" , e.getMessage(), packageName, productId, orderId, purchaseToken);
        }
        return purchase;
    }

    /**
     * 验证账号信息
     * @param accountId
     * @param verification
     * @return
     */
    public boolean verifyLogin(String accountId, String verification) {
        GoogleConfig googleConfig = channelConfig.getGoogleConfig();
        if (!googleConfig.getAccount().isVerify()) {
            return true;
        }

        if (StringUtils.isEmpty(verification)) {
            log.error("verifyLogin[google] failed, verification is empty");
            return false;
        }

        JSONObject jsonObject = JSON.parseObject(verification);
        if (!jsonObject.containsKey("verification")) {
            log.error("verifyLogin[google] failed, verification is empty");
            return false;
        }

        try {
            JSONObject verifyObj = jsonObject.getJSONObject("verification");
            String authCode = verifyObj.getString("authCode");

            GoogleTokenResponse tokenResponse =
                    new GoogleAuthorizationCodeTokenRequest(
                            new NetHttpTransport(),
                            JacksonFactory.getDefaultInstance(),
                            "https://www.googleapis.com/oauth2/v4/token",
                            googleConfig.getAccount().getClientId(),
                            googleConfig.getAccount().getClientSecret(),
                            authCode,
                            "")
                            .execute();
//
//            //{"access_token":"*******************************************************************************************************************************************************************","expires_in":3598,"refresh_token":"1//0eHnwviXceIAYCgYIARAAGA4SNwF-L9IrULJ6liprae-U12lhVs1Fj6776b_AG5H4nvq81vcvZDvXzsDFq5U1s5J3qvGGgq9Akso","scope":"https://www.googleapis.com/auth/games_lite","token_type":"Bearer"}
//
//            String accessToken = tokenResponse.getAccessToken();
//            long expiresIn = tokenResponse.getExpiresInSeconds();
//            String refreshToken = tokenResponse.getRefreshToken();


//            String accessToken = "*******************************************************************************************************************************************************************";
//            String refreshToken = "1//0eHnwviXceIAYCgYIARAAGA4SNwF-L9IrULJ6liprae-U12lhVs1Fj6776b_AG5H4nvq81vcvZDvXzsDFq5U1s5J3qvGGgq9Akso";
//            long expiresIn = 3598;

//            System.out.println(tokenResponse);
//            System.out.println(tokenResponse.getRefreshToken());
//            System.out.println(tokenResponse.getExpiresInSeconds());
//            System.out.println(tokenResponse.getAccessToken());

//            TokenResponse tokenResponse = new TokenResponse();
//            tokenResponse.setAccessToken(accessToken);
//            tokenResponse.setRefreshToken(refreshToken);
//            tokenResponse.setExpiresInSeconds(expiresIn);
//            tokenResponse.setScope("https://www.googleapis.com/auth/games_lite");
//            tokenResponse.setTokenType("Bearer");

            Credential credential = new Credential
                    .Builder(BearerToken.authorizationHeaderAccessMethod())
                    .setJsonFactory(JacksonFactory.getDefaultInstance())
                    .setTransport(this.transport)
                    .setTokenServerEncodedUrl("https://www.googleapis.com/oauth2/v4/token")
                    .setClientAuthentication(new HttpExecuteInterceptor() {
                        @Override
                        public void intercept(HttpRequest request)
                                throws IOException {
                        }
                    })
                    .build()
                    .setFromTokenResponse(tokenResponse);

//            Games gamesAPI = new Games(this.transport, JacksonFactory.getDefaultInstance(), credential);

            Games.Builder gamesBuilder = new Games.Builder(this.transport, JacksonFactory.getDefaultInstance(), credential);
            gamesBuilder.setApplicationName(googleConfig.getPay().getApplicationName());
            Games gamesAPI = gamesBuilder.build();

            ApplicationVerifyResponse resp = gamesAPI.applications().verify
                    (googleConfig.getAccount().getApplicationId()).execute();

            boolean flag = resp.getPlayerId().equals(accountId);
            if (!flag) {
                log.error("verifyLogin[google] failed, authCode invalid, accountId:{}, exchangeAccountId:{}", accountId, resp.getPlayerId());
            }

            return flag;
        } catch (Exception e) {
            log.error("verifyLogin error [google]:{}", e.getMessage());
            return true;
        }
    }

    public void integrityApi(long userId, String nonce, String token) {
        try {
            if (StringUtils.isEmpty(token)) {
                log.error("integrityApi error, token is empty, userId:{}", userId);
                return;
            }

            String base64OfEncodedDecryptionKey = channelConfig.getGoogleConfig().getIntegrityDecryptionKey();
            byte[] decryptionKeyBytes = Base64.getDecoder().decode(base64OfEncodedDecryptionKey);

            SecretKey decryptionKey =
                    new SecretKeySpec(decryptionKeyBytes,"AES");

            String base64OfEncodedVerificationKey = channelConfig.getGoogleConfig().getIntegrityVerificationKey();
            byte[] encodedVerificationKey = Base64.getDecoder().decode(base64OfEncodedVerificationKey);
            PublicKey verificationKey =
                    KeyFactory.getInstance("EC")
                            .generatePublic(new X509EncodedKeySpec(encodedVerificationKey));

            JsonWebEncryption jwe = (JsonWebEncryption) JsonWebStructure.fromCompactSerialization(token);
            jwe.setKey(decryptionKey);

            String compactJws = jwe.getPayload();
            JsonWebSignature jws = (JsonWebSignature) JsonWebStructure.fromCompactSerialization(compactJws);
            jws.setKey(verificationKey);

            String payload = jws.getPayload();

            if (StringUtils.isEmpty(payload)) {
                log.error("integrityApi error, payload is empty, userId:{}", userId);
                return;
            }

            // 验证requestDetails
            JSONObject jsonObj = JSONObject.parseObject(payload);
            JSONObject requestDetails = jsonObj.getJSONObject("requestDetails");
            String requestPackageName = requestDetails.getString("requestPackageName");
            if (!requestPackageName.equals(channelConfig.getGoogleConfig().getPackageName())) {
                log.error("integrityApi error, packageName invalid, userId:{}, token:{}, payload:{}", userId, token, payload);
                return;
            }
//            String requestNonce = requestDetails.getString("nonce");
//            if (!requestNonce.equals(nonce)) {
//                log.error("integrityApi error, nonce invalid, userId:{}, token:{}, payload:{}", userId, token, payload);
//                return;
//            }
            long tm = requestDetails.getLongValue("timestampMillis") / 1000;
            if (DateUtils.getUnixTime() - tm > DateUtils.SECONDS_12_HOUR) {
                log.error("integrityApi error, timestamp invalid, userId:{}, token:{}, payload:{}", userId, token, payload);
                return;
            }

            // 验证 appIntegrity 应用完整性字段
            JSONObject appIntegrity = jsonObj.getJSONObject("appIntegrity");
            String appRecognitionVerdict = appIntegrity.getString("appRecognitionVerdict");
            // PLAY_RECOGNIZED = 应用和证书与 Google Play 分发的版本相符。
            // UNRECOGNIZED_VERSION = 证书或软件包名称与 Google Play 记录不符。
            // UNEVALUATED = 未评估应用完整性。未满足必要条件，例如设备不够可信。
            if (!appRecognitionVerdict.equals("PLAY_RECOGNIZED")) {
                log.error("integrityApi error, appRecognitionVerdict invalid, userId:{}, token:{}, payload:{}", userId, token, payload);
                return;
            }

            JSONObject deviceIntegrity = jsonObj.getJSONObject("deviceIntegrity");
            String deviceRecognitionVerdict = deviceIntegrity.getString("deviceRecognitionVerdict");
            // MEETS_DEVICE_INTEGRITY = 应用正在由 Google Play 服务提供支持的 Android 设备上运行。设备通过了系统完整性检查，并且满足 Android 兼容性要求。
            //无标签（空白值） = 应用正在有攻击迹象（如 API 挂接）或系统被侵迹象（如取得 root 权限）的设备上运行，或者应用未在实体设备（如未通过 Google Play 完整性检查的模拟器）上运行。
            if (deviceRecognitionVerdict == null || !deviceRecognitionVerdict.contains("MEETS_DEVICE_INTEGRITY")) {
                log.error("integrityApi error, deviceRecognitionVerdict invalid, userId:{}, token:{}, payload:{}", userId, token, payload);
                return;
            }

            JSONObject accountDetails = jsonObj.getJSONObject("accountDetails");
            String appLicensingVerdict = accountDetails.getString("appLicensingVerdict");
            // LICENSED = 用户拥有应用使用权。换句话说，用户在 Google Play 上安装或购买了您的应用。
            // UNLICENSED = 用户没有应用使用权。例如，当用户旁加载了您的应用，或未从 Google Play 获取您的应用时，就会发生这种情况。
            // UNEVALUATED = 设备不够可信。/ 设备上安装的应用是 Google Play 未知的版本。/ 用户未登录 Google Play。
            if (!StringUtils.isEmpty(appLicensingVerdict) && !appLicensingVerdict.equals("LICENSED")) {
                log.error("integrityApi error, licensingVerdict invalid, userId:{}, token:{}, payload:{}", userId, token, payload);
                return;
            }

        } catch (Exception e) {
            log.error("integrityApi failed userId:{}, e:", userId, e);
        }
    }

    public String makeNonce() {
        String str = UUID.randomUUID().toString() + System.currentTimeMillis();
        return new String(Base64.getEncoder().encode(str.getBytes(StandardCharsets.UTF_8)));
    }

}

package com.dxx.game.modules.hero.data;

import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dto.CommonProto;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/2 16:49
 */
public class HeroRewardData {
    List<Hero> heroList = new ArrayList<>();

    List<CommonProto.RewardDto> rewardDtoList = new ArrayList<>();

    private List<CommonProto.ItemDto> items = new ArrayList<>();

    public List<Hero> getHeroList() {
        return heroList;
    }

    public void setHeroList(List<Hero> heroList) {
        this.heroList = heroList;
    }

    public List<CommonProto.RewardDto> getRewardDtoList() {
        return rewardDtoList;
    }

    public void setRewardDtoList(List<CommonProto.RewardDto> rewardDtoList) {
        this.rewardDtoList = rewardDtoList;
    }

    public List<CommonProto.ItemDto> getItems() {
        return items;
    }

    public void setItems(List<CommonProto.ItemDto> items) {
        this.items = items;
    }
}

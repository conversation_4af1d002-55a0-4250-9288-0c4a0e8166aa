package com.dxx.game.modules.mission.support;

import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.equip.EquipEntity;
import com.dxx.game.consts.BattleType;
import com.dxx.game.dao.dynamodb.model.LogResource;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.LogResourceDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.MissionProto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/8 18:32
 */
@Slf4j
@Component
public class MissionSupport {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private LogResourceDao logResourceDao;

    // 因为战斗都用的一个接口 所以重新定义command
    public short getMissionLogCommand(MissionProto.MissionType missionType) {
        return (short) (100 + missionType.getNumber());
    }

    // 是否扣除了体力
    public boolean isCostVitality(long userId, long transId, MissionProto.MissionType missionType) {
        LogResource logResource = logResourceDao.getByTransId(userId, transId);
        short missionCommand = getMissionLogCommand(missionType);
        if (logResource == null || logResource.getCommand() != missionCommand || logResource.getCustomType() != missionType.getNumber()) {
            return false;
        }

        logResource.setCustomType(-1);
        logResourceDao.update(logResource);
        return true;
    }

    public MissionProto.MainMission buildMainMission(UserExtend userExtend) {
        MissionProto.MainMission.Builder mainMission = MissionProto.MainMission.newBuilder();
        mainMission.setMissionId(userExtend.getChapterId());
        return mainMission.build();
    }

    /**
     * 战斗结算的奖励是否有效
     */
    private boolean isBattleRewardValid(long userId, int chapterId, BattleType battleType,
                                        Map<Integer, Integer> itemsLimit, Map<Integer, Integer> equipLimit,
                                        List<CommonProto.RewardDto> rewardDtos)  {
        Map<Integer, Integer> itemsNumMap = new HashMap<>();
        Map<Integer, Integer> copyEquipLimit = new HashMap<>();

        int maxEquipNum = 0;

        for (Map.Entry<Integer, Integer> entry : equipLimit.entrySet()) {
            copyEquipLimit.put(entry.getKey(), entry.getValue());
            maxEquipNum += entry.getValue();
        }


        int equipNum = 0;
        for (CommonProto.RewardDto rewardDto : rewardDtos) {
            int itemId = rewardDto.getConfigId();
            int itemNum = rewardDto.getCount();
            int itemType = rewardDto.getType();

            if (itemType == 3) {
                // 装备
                equipNum += itemNum;

                // 验证装备品质对应的数量
                EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquipEntity(itemId);
                if (equipEntity == null) {
                    return false;
                }

                int num = copyEquipLimit.getOrDefault(equipEntity.getQuality(), 0);
                if (itemNum > num) {
                    log.error("battle cheat reward equips error, userId:{}, chapterId:{}, equipId:{}, receive_value:{}, real_value:{}",
                            userId, chapterId, rewardDto.getConfigId(), rewardDto.getCount(), num);
                    return false;
                }

                copyEquipLimit.put(equipEntity.getQuality(), num - itemNum);
            } else {
                // 货币,卷轴
                if (itemId >= 2000101 && itemId <= 2000110) {
                    itemId = 2000100;
                }

                if (!itemsLimit.containsKey(itemId)) {
                    log.error("battle cheat reward cheat items not allowed, userId:{}, chapterId:{}, itemId:{}, receive_value:{}",
                            userId, chapterId, itemId, itemNum);
                    return false;
                }
                int num = itemsNumMap.getOrDefault(itemId, 0) + itemNum;
                itemsNumMap.put(itemId, num);
            }
        }

        if (equipNum > maxEquipNum) {
            // 装备上限
            log.error("battle cheat reward cheat equips error, userId:{}, chapterId:{}, equipId:{}, receive_value:{}, real_value:{}",
                    userId, chapterId, 3000000, equipNum, maxEquipNum);
            return false;
        } else {
            // 验证道具数量
            for (Map.Entry<Integer, Integer> entry : itemsNumMap.entrySet()) {
                if (entry.getValue() > itemsLimit.get(entry.getKey())) {
                    log.error("battle cheat reward cheat items error, userId:{}, chapterId:{}, itemId:{}, receive_value:{}, real_value:{}",
                            userId, chapterId, entry.getKey(), entry.getValue(), itemsLimit.get(entry.getKey()));
                    return false;
                }
            }
        }

        return true;
    }
}

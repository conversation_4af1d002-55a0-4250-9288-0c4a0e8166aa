#!/bin/bash

echo "Proto file generator..."

# 获取当前脚本所在的绝对路径
WORKSPACE="$(cd "$(dirname "$0")" && pwd)"

# Proto 文件目录
cur_path="${WORKSPACE}/proto/"

# Java 类生成目录
java_out_path="${WORKSPACE}/../../src/main/java/"

# echo "Java output path: $java_out_path"

# 检查文件修改时间
node "${WORKSPACE}/filetime.js" "$cur_path"
if [[ $? -ne 0 ]]; then
    echo "Error: Failed to check file modification times."
    exit 1
fi

# 遍历目录下所有 .proto 文件
for file in "${cur_path}"*.proto; do
    # 提取文件名（不带路径）
    filename="${file##*/}"
    # 去掉文件扩展名
    filename="${filename%.*}"

    # 检查文件是否被修改
    node "${WORKSPACE}/checkmodify.js" "$filename"
    if [[ $? -eq 1 ]]; then
        # echo "Processing modified proto file: $file"

        # 生成 Java 类
        "${WORKSPACE}/tool/protoc" \
            --java_out="${java_out_path}" \
            --plugin=protoc-gen-grpc-java="${WORKSPACE}/tool/protoc-gen-grpc-java-osx-aarch_64.exe" \
            --grpc-java_out="${java_out_path}" \
            --proto_path="${cur_path}" \
            "$file"

        if [[ $? -ne 0 ]]; then
            echo "Error: Failed to generate Java classes for $filename.proto"
            exit 1
        fi

        echo "Generated Java classes for $filename.proto"
    fi
done

# 将所有 .proto 文件转换为 JSON 格式
node "${WORKSPACE}/../node_modules/protobufjs/bin/pbjs" -t json "${cur_path}"*.proto > proto.json
if [[ $? -ne 0 ]]; then
    echo "Error: Failed to convert proto files to JSON."
    exit 1
fi

# 移动生成的 JSON 文件到目标目录
mv proto.json "${WORKSPACE}/../api/proto/"
if [[ $? -ne 0 ]]; then
    echo "Error: Failed to move proto.json to target directory."
    exit 1
fi

# 创建消息 ID
node "${WORKSPACE}/createMsgId.js" "$cur_path"
if [[ $? -ne 0 ]]; then
    echo "Error: Failed to create message IDs."
    exit 1
fi

echo "Proto file generation completed successfully!"

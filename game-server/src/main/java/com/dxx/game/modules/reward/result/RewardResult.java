package com.dxx.game.modules.reward.result;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * 奖励结果
 * <AUTHOR>
 * @date 2019-12-13 16:13
 */
public class RewardResult<T> {

	/**
	 * 奖励类型值
	 */
	private RewardType rewardType = RewardType.RESOURCE;
	
	/**
	 * 资源奖励类型
	 */
	private RewardResourceType resourceType = RewardResourceType.NONE;
	
	/**
	 * 实际变化的值
	 */
	private int actualCount = 0;

	/**
	 * 获得的道具ID
	 */
	private int configId;



	/**
	 * 执行操作后的当前值
	 */
	private T current;
	
	public RewardResult() {}
	
	public RewardResult(RewardType rewardType) {
		this.rewardType = rewardType;
	}
	
	public RewardResult(RewardType rewardType, RewardResourceType resourceType) {
		this.rewardType = rewardType;
		this.resourceType = resourceType;
	}
	
	public RewardType getRewardType() {
		return this.rewardType;
	}
	
	public RewardResourceType getResourceType() {
		return this.resourceType;
	}
	
	public int getActualCount() {
		return actualCount;
	}
	
	public void setActualCount(int actualCount) {
		this.actualCount = actualCount;
	}
	
	public T getCurrent() {
		return this.current;
	}
	
	public void setCurrent(T current) {
		this.current = current;
	}

	public int getConfigId() {
		return configId;
	}

	public void setConfigId(int configId) {
		this.configId = configId;
	}
}
 

























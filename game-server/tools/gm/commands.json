{"1": "邮件", "2": "GM后台扣除资源", "1003": "GM后台修改", "9001": "开发测试用", "9003": "开发测试修改资源", "9005": "测试-工具", "9999": "通用错误返回", "10101": "登录请求", "10103": "客户端数据不同步时刷新数据用接口", "10105": "客户端心跳(每分钟)", "10107": "更新systemMask", "10109": "更新新手引导步骤guideMask", "10111": "注销账号", "10113": "更新昵称,头像,头像框", "10115": "获取其他玩家信息", "10117": "获得战报", "10119": "记录开启模块", "10121": "设置阵容", "10123": "获得阵容", "10141": "请求角色列表", "10143": "请求服务器列表", "10201": "道具-使用", "10301": "关卡-获取数据", "10303": "关卡-开始战斗", "10305": "关卡-结算", "10307": "关卡-获取挂机奖励道具", "10309": "关卡-领取挂机奖励", "10311": "关卡-快速挂机请求", "10401": "内购请求", "10403": "内购-预下单", "10405": "内购-unity内测试", "10501": "任务-获取数据", "10503": "任务-每日领取奖励", "10505": "成就-领取奖励", "10507": "任务-领取活跃度奖励", "10509": "任务-领取全活跃度奖励", "10601": "商店-请求数据", "10603": "商店-抽卡", "10605": "积分商店-获取积分商店数据", "10607": "积分商店-刷新道具", "10609": "积分商店-购买道具", "10611": "商店-抽卡-保存许愿列表", "10613": "商店-免费", "10631": "battlepass-获取数据(打开面板请求)", "10633": "battlepass-领取通行证奖励", "10635": "battlepass-兑换通行证积分", "10637": "battlepass-通行证领取最终奖励", "10651": "月卡-领取月卡奖励", "10661": "vip-领取vip等级宝箱", "10671": "基金-获取数据(打开面板请求)", "10673": "基金-领取基金奖励", "10675": "首充-领取首充奖励", "10701": "英雄-升级", "10703": "英雄-升阶", "10705": "英雄-升星", "10707": "英雄-重生", "10709": "英雄-图鉴积分", "10711": "英雄-图鉴领奖", "10713": "英雄-替换皮肤", "10715": "英雄-羁绊升级", "10717": "英雄-无损换将", "10801": "装备-强化请求", "10803": "装备-合成", "10805": "装备-升级", "10807": "装备-穿戴", "10809": "装备-等级重置", "10811": "装备-降品", "10813": "装备-脱下", "10815": "装备-替换", "10901": "活动-获取所有活动数据(登录时调用,", "10903": "活动-根据活动id获取任务数据", "10905": "活动-活动任务领奖", "10907": "活动-根据活动id获取商店数据", "10909": "活动-兑换", "10911": "活动-排行榜", "11101": "签到-获取数据", "11103": "签到-领取签到奖励", "11301": "新手7日任务活动-获取数据", "11303": "新手7日任务活动-领取任务奖励", "11305": "新手7日任务活动-领取活跃度奖励", "11401": "钓鱼活动-打开界面调用", "11403": "钓鱼活动-抛竿", "11405": "钓鱼活动-收竿", "11407": "钓鱼活动-购买鱼饵", "11409": "钓鱼活动-复活", "11501": "翻牌子活动-打开界面调用", "11503": "翻牌子活动-领取累计线索奖励", "11505": "翻牌子活动-购买步数", "11507": "翻牌子活动-展示格子", "11509": "翻牌子活动-领奖格子", "11511": "翻牌子活动-线索格子", "11513": "翻牌子活动-炸弹格子", "11515": "翻牌子活动-当前地图是否有特殊奖励未领取", "11517": "翻牌子活动-一键领取累计线索奖励", "11601": "潜水活动-打开界面调用", "11603": "潜水活动-购买潜水材料", "11605": "潜水活动-领取累计奖励", "11607": "潜水活动-使用电光水母", "11609": "潜水活动-使用手电筒", "11611": "潜水活动-一键领取累计奖励", "11701": "战力活动-打开界面调用", "11703": "战力活动-领奖", "30101": "公会-获取数据", "30103": "公会-创建公会", "30105": "公会-搜索", "30107": "公会-查看详细信息", "30109": "公会-获取成员列表", "30111": "公会-修改信息", "30113": "公会-解散", "30115": "公会-申请加入", "30117": "公会-取消申请", "30119": "公会-自动加入", "30121": "公会-获取申请列表", "30123": "公会-同意加入", "30125": "公会-拒绝加入", "30127": "公会-踢人", "30129": "公会-离开", "30131": "公会-修改成员职位", "30133": "公会-转让会长", "30135": "公会-获取公会功能信息", "30137": "公会-升级", "30141": "公会-签到", "30151": "公会-商店-购买", "30153": "公会-商店-刷新", "30161": "公会-任务-领取奖励", "30163": "公会-任务-刷新", "30171": "公会-获取消息记录", "30201": "公会-捐赠-请求道具", "30203": "公会-捐赠-赠送道具", "30205": "公会-捐赠-领取道具", "30207": "公会-捐赠-获取请求道具记录", "30209": "公会-捐赠-获取受赠记录", "30211": "公会-BOSS战", "30213": "公会-BOSS战-公会排行榜", "30501": "公会-科技-升级", "31101": "IM-登录", "31103": "IM-加入分组", "31105": "IM-退出分组", "31107": "IM-心跳(每5秒请求一次)", "31109": "IM-聊天", "31111": "IM-私聊", "31113": "IM-获取分组消息记录(聊天记录)", "31115": "IM-获取私聊列表", "31117": "IM-获取私聊记录", "31119": "IM-黑名单-获取列表", "31121": "IM-黑名单-添加", "31123": "IM-黑名单-移除", "31125": "IM-聊天-翻译", "31299": "通用错误消息", "32105": "聊天-展示物品"}
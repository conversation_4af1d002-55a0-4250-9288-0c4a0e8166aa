@echo Proto file generator...
@echo off

:: proto文件所在目录（不需要修改）
set cur_path=.\proto\
:: 类生成目录
set java_out_path=..\..\src\main\java\

:: 文件修改时间
node ./filetime.js %cur_path%

setlocal enabledelayedexpansion

for %%i in (%cur_path%*.proto) do (

	rem echo %%i

	node ./checkmodify.js %%~ni

	if !errorlevel! EQU 1 (
		echo %%i
		.\tool\protoc.exe --java_out=%java_out_path% --proto_path=%cur_path% %%i
	) 
)

node ..\node_modules\protobufjs\bin\pbjs -t json .\proto\*.proto > proto.json
move proto.json ..\api\proto\ 
node ./createMsgId.js %cur_path%

@echo done!

pause 
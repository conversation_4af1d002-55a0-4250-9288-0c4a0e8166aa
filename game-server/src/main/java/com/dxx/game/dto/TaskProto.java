// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: task.proto

package com.dxx.game.dto;

public final class TaskProto {
  private TaskProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TaskGetInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskGetInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10501 任务-获取数据
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskGetInfoRequest}
   */
  public static final class TaskGetInfoRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskGetInfoRequest)
      TaskGetInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskGetInfoRequest.newBuilder() to construct.
    private TaskGetInfoRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskGetInfoRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskGetInfoRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskGetInfoRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskGetInfoRequest.class, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskGetInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskGetInfoRequest other = (com.dxx.game.dto.TaskProto.TaskGetInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskGetInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10501 任务-获取数据
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskGetInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskGetInfoRequest)
        com.dxx.game.dto.TaskProto.TaskGetInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskGetInfoRequest.class, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskGetInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskGetInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoRequest build() {
        com.dxx.game.dto.TaskProto.TaskGetInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskGetInfoRequest result = new com.dxx.game.dto.TaskProto.TaskGetInfoRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskGetInfoRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskGetInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskGetInfoRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskGetInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskGetInfoRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskGetInfoRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskGetInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskGetInfoRequest)
    private static final com.dxx.game.dto.TaskProto.TaskGetInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskGetInfoRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskGetInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskGetInfoRequest>() {
      @java.lang.Override
      public TaskGetInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskGetInfoRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskGetInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskGetInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskGetInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskGetInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskGetInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 任务列表
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 2;</code>
     * @return Whether the tasks field is set.
     */
    boolean hasTasks();
    /**
     * <pre>
     * 任务列表
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 2;</code>
     * @return The tasks.
     */
    com.dxx.game.dto.TaskProto.Tasks getTasks();
    /**
     * <pre>
     * 任务列表
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 2;</code>
     */
    com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder();

    /**
     * <pre>
     * 日常活跃度
     * </pre>
     *
     * <code>uint32 dailyTaskActive = 3;</code>
     * @return The dailyTaskActive.
     */
    int getDailyTaskActive();

    /**
     * <pre>
     * 周常活跃度
     * </pre>
     *
     * <code>uint32 weeklyTaskActive = 4;</code>
     * @return The weeklyTaskActive.
     */
    int getWeeklyTaskActive();

    /**
     * <pre>
     * 日常任务刷新时间戳
     * </pre>
     *
     * <code>uint64 dailyTaskResetTime = 5;</code>
     * @return The dailyTaskResetTime.
     */
    long getDailyTaskResetTime();

    /**
     * <pre>
     * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>uint64 dailyTaskRewardLog = 6;</code>
     * @return The dailyTaskRewardLog.
     */
    long getDailyTaskRewardLog();

    /**
     * <pre>
     * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>uint64 weeklyTaskRewardLog = 7;</code>
     * @return The weeklyTaskRewardLog.
     */
    long getWeeklyTaskRewardLog();
  }
  /**
   * <pre>
   *CMD PackageId=10502 
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskGetInfoResponse}
   */
  public static final class TaskGetInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskGetInfoResponse)
      TaskGetInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskGetInfoResponse.newBuilder() to construct.
    private TaskGetInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskGetInfoResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskGetInfoResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskGetInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.TaskProto.Tasks.Builder subBuilder = null;
              if (tasks_ != null) {
                subBuilder = tasks_.toBuilder();
              }
              tasks_ = input.readMessage(com.dxx.game.dto.TaskProto.Tasks.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(tasks_);
                tasks_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              dailyTaskActive_ = input.readUInt32();
              break;
            }
            case 32: {

              weeklyTaskActive_ = input.readUInt32();
              break;
            }
            case 40: {

              dailyTaskResetTime_ = input.readUInt64();
              break;
            }
            case 48: {

              dailyTaskRewardLog_ = input.readUInt64();
              break;
            }
            case 56: {

              weeklyTaskRewardLog_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskGetInfoResponse.class, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int TASKS_FIELD_NUMBER = 2;
    private com.dxx.game.dto.TaskProto.Tasks tasks_;
    /**
     * <pre>
     * 任务列表
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 2;</code>
     * @return Whether the tasks field is set.
     */
    @java.lang.Override
    public boolean hasTasks() {
      return tasks_ != null;
    }
    /**
     * <pre>
     * 任务列表
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 2;</code>
     * @return The tasks.
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.Tasks getTasks() {
      return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
    }
    /**
     * <pre>
     * 任务列表
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
      return getTasks();
    }

    public static final int DAILYTASKACTIVE_FIELD_NUMBER = 3;
    private int dailyTaskActive_;
    /**
     * <pre>
     * 日常活跃度
     * </pre>
     *
     * <code>uint32 dailyTaskActive = 3;</code>
     * @return The dailyTaskActive.
     */
    @java.lang.Override
    public int getDailyTaskActive() {
      return dailyTaskActive_;
    }

    public static final int WEEKLYTASKACTIVE_FIELD_NUMBER = 4;
    private int weeklyTaskActive_;
    /**
     * <pre>
     * 周常活跃度
     * </pre>
     *
     * <code>uint32 weeklyTaskActive = 4;</code>
     * @return The weeklyTaskActive.
     */
    @java.lang.Override
    public int getWeeklyTaskActive() {
      return weeklyTaskActive_;
    }

    public static final int DAILYTASKRESETTIME_FIELD_NUMBER = 5;
    private long dailyTaskResetTime_;
    /**
     * <pre>
     * 日常任务刷新时间戳
     * </pre>
     *
     * <code>uint64 dailyTaskResetTime = 5;</code>
     * @return The dailyTaskResetTime.
     */
    @java.lang.Override
    public long getDailyTaskResetTime() {
      return dailyTaskResetTime_;
    }

    public static final int DAILYTASKREWARDLOG_FIELD_NUMBER = 6;
    private long dailyTaskRewardLog_;
    /**
     * <pre>
     * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>uint64 dailyTaskRewardLog = 6;</code>
     * @return The dailyTaskRewardLog.
     */
    @java.lang.Override
    public long getDailyTaskRewardLog() {
      return dailyTaskRewardLog_;
    }

    public static final int WEEKLYTASKREWARDLOG_FIELD_NUMBER = 7;
    private long weeklyTaskRewardLog_;
    /**
     * <pre>
     * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>uint64 weeklyTaskRewardLog = 7;</code>
     * @return The weeklyTaskRewardLog.
     */
    @java.lang.Override
    public long getWeeklyTaskRewardLog() {
      return weeklyTaskRewardLog_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (tasks_ != null) {
        output.writeMessage(2, getTasks());
      }
      if (dailyTaskActive_ != 0) {
        output.writeUInt32(3, dailyTaskActive_);
      }
      if (weeklyTaskActive_ != 0) {
        output.writeUInt32(4, weeklyTaskActive_);
      }
      if (dailyTaskResetTime_ != 0L) {
        output.writeUInt64(5, dailyTaskResetTime_);
      }
      if (dailyTaskRewardLog_ != 0L) {
        output.writeUInt64(6, dailyTaskRewardLog_);
      }
      if (weeklyTaskRewardLog_ != 0L) {
        output.writeUInt64(7, weeklyTaskRewardLog_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (tasks_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTasks());
      }
      if (dailyTaskActive_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, dailyTaskActive_);
      }
      if (weeklyTaskActive_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, weeklyTaskActive_);
      }
      if (dailyTaskResetTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, dailyTaskResetTime_);
      }
      if (dailyTaskRewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, dailyTaskRewardLog_);
      }
      if (weeklyTaskRewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, weeklyTaskRewardLog_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskGetInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskGetInfoResponse other = (com.dxx.game.dto.TaskProto.TaskGetInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasTasks() != other.hasTasks()) return false;
      if (hasTasks()) {
        if (!getTasks()
            .equals(other.getTasks())) return false;
      }
      if (getDailyTaskActive()
          != other.getDailyTaskActive()) return false;
      if (getWeeklyTaskActive()
          != other.getWeeklyTaskActive()) return false;
      if (getDailyTaskResetTime()
          != other.getDailyTaskResetTime()) return false;
      if (getDailyTaskRewardLog()
          != other.getDailyTaskRewardLog()) return false;
      if (getWeeklyTaskRewardLog()
          != other.getWeeklyTaskRewardLog()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasTasks()) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasks().hashCode();
      }
      hash = (37 * hash) + DAILYTASKACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getDailyTaskActive();
      hash = (37 * hash) + WEEKLYTASKACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getWeeklyTaskActive();
      hash = (37 * hash) + DAILYTASKRESETTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDailyTaskResetTime());
      hash = (37 * hash) + DAILYTASKREWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDailyTaskRewardLog());
      hash = (37 * hash) + WEEKLYTASKREWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWeeklyTaskRewardLog());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskGetInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10502 
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskGetInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskGetInfoResponse)
        com.dxx.game.dto.TaskProto.TaskGetInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskGetInfoResponse.class, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskGetInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (tasksBuilder_ == null) {
          tasks_ = null;
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }
        dailyTaskActive_ = 0;

        weeklyTaskActive_ = 0;

        dailyTaskResetTime_ = 0L;

        dailyTaskRewardLog_ = 0L;

        weeklyTaskRewardLog_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskGetInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoResponse build() {
        com.dxx.game.dto.TaskProto.TaskGetInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskGetInfoResponse result = new com.dxx.game.dto.TaskProto.TaskGetInfoResponse(this);
        result.code_ = code_;
        if (tasksBuilder_ == null) {
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
        result.dailyTaskActive_ = dailyTaskActive_;
        result.weeklyTaskActive_ = weeklyTaskActive_;
        result.dailyTaskResetTime_ = dailyTaskResetTime_;
        result.dailyTaskRewardLog_ = dailyTaskRewardLog_;
        result.weeklyTaskRewardLog_ = weeklyTaskRewardLog_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskGetInfoResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskGetInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskGetInfoResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskGetInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasTasks()) {
          mergeTasks(other.getTasks());
        }
        if (other.getDailyTaskActive() != 0) {
          setDailyTaskActive(other.getDailyTaskActive());
        }
        if (other.getWeeklyTaskActive() != 0) {
          setWeeklyTaskActive(other.getWeeklyTaskActive());
        }
        if (other.getDailyTaskResetTime() != 0L) {
          setDailyTaskResetTime(other.getDailyTaskResetTime());
        }
        if (other.getDailyTaskRewardLog() != 0L) {
          setDailyTaskRewardLog(other.getDailyTaskRewardLog());
        }
        if (other.getWeeklyTaskRewardLog() != 0L) {
          setWeeklyTaskRewardLog(other.getWeeklyTaskRewardLog());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskGetInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskGetInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.TaskProto.Tasks tasks_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> tasksBuilder_;
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       * @return Whether the tasks field is set.
       */
      public boolean hasTasks() {
        return tasksBuilder_ != null || tasks_ != null;
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       * @return The tasks.
       */
      public com.dxx.game.dto.TaskProto.Tasks getTasks() {
        if (tasksBuilder_ == null) {
          return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        } else {
          return tasksBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      public Builder setTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          tasks_ = value;
          onChanged();
        } else {
          tasksBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      public Builder setTasks(
          com.dxx.game.dto.TaskProto.Tasks.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          tasks_ = builderForValue.build();
          onChanged();
        } else {
          tasksBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      public Builder mergeTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (tasks_ != null) {
            tasks_ =
              com.dxx.game.dto.TaskProto.Tasks.newBuilder(tasks_).mergeFrom(value).buildPartial();
          } else {
            tasks_ = value;
          }
          onChanged();
        } else {
          tasksBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = null;
          onChanged();
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      public com.dxx.game.dto.TaskProto.Tasks.Builder getTasksBuilder() {
        
        onChanged();
        return getTasksFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilder();
        } else {
          return tasks_ == null ?
              com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        }
      }
      /**
       * <pre>
       * 任务列表
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder>(
                  getTasks(),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }

      private int dailyTaskActive_ ;
      /**
       * <pre>
       * 日常活跃度
       * </pre>
       *
       * <code>uint32 dailyTaskActive = 3;</code>
       * @return The dailyTaskActive.
       */
      @java.lang.Override
      public int getDailyTaskActive() {
        return dailyTaskActive_;
      }
      /**
       * <pre>
       * 日常活跃度
       * </pre>
       *
       * <code>uint32 dailyTaskActive = 3;</code>
       * @param value The dailyTaskActive to set.
       * @return This builder for chaining.
       */
      public Builder setDailyTaskActive(int value) {
        
        dailyTaskActive_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日常活跃度
       * </pre>
       *
       * <code>uint32 dailyTaskActive = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyTaskActive() {
        
        dailyTaskActive_ = 0;
        onChanged();
        return this;
      }

      private int weeklyTaskActive_ ;
      /**
       * <pre>
       * 周常活跃度
       * </pre>
       *
       * <code>uint32 weeklyTaskActive = 4;</code>
       * @return The weeklyTaskActive.
       */
      @java.lang.Override
      public int getWeeklyTaskActive() {
        return weeklyTaskActive_;
      }
      /**
       * <pre>
       * 周常活跃度
       * </pre>
       *
       * <code>uint32 weeklyTaskActive = 4;</code>
       * @param value The weeklyTaskActive to set.
       * @return This builder for chaining.
       */
      public Builder setWeeklyTaskActive(int value) {
        
        weeklyTaskActive_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 周常活跃度
       * </pre>
       *
       * <code>uint32 weeklyTaskActive = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeeklyTaskActive() {
        
        weeklyTaskActive_ = 0;
        onChanged();
        return this;
      }

      private long dailyTaskResetTime_ ;
      /**
       * <pre>
       * 日常任务刷新时间戳
       * </pre>
       *
       * <code>uint64 dailyTaskResetTime = 5;</code>
       * @return The dailyTaskResetTime.
       */
      @java.lang.Override
      public long getDailyTaskResetTime() {
        return dailyTaskResetTime_;
      }
      /**
       * <pre>
       * 日常任务刷新时间戳
       * </pre>
       *
       * <code>uint64 dailyTaskResetTime = 5;</code>
       * @param value The dailyTaskResetTime to set.
       * @return This builder for chaining.
       */
      public Builder setDailyTaskResetTime(long value) {
        
        dailyTaskResetTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日常任务刷新时间戳
       * </pre>
       *
       * <code>uint64 dailyTaskResetTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyTaskResetTime() {
        
        dailyTaskResetTime_ = 0L;
        onChanged();
        return this;
      }

      private long dailyTaskRewardLog_ ;
      /**
       * <pre>
       * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>uint64 dailyTaskRewardLog = 6;</code>
       * @return The dailyTaskRewardLog.
       */
      @java.lang.Override
      public long getDailyTaskRewardLog() {
        return dailyTaskRewardLog_;
      }
      /**
       * <pre>
       * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>uint64 dailyTaskRewardLog = 6;</code>
       * @param value The dailyTaskRewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setDailyTaskRewardLog(long value) {
        
        dailyTaskRewardLog_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>uint64 dailyTaskRewardLog = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyTaskRewardLog() {
        
        dailyTaskRewardLog_ = 0L;
        onChanged();
        return this;
      }

      private long weeklyTaskRewardLog_ ;
      /**
       * <pre>
       * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>uint64 weeklyTaskRewardLog = 7;</code>
       * @return The weeklyTaskRewardLog.
       */
      @java.lang.Override
      public long getWeeklyTaskRewardLog() {
        return weeklyTaskRewardLog_;
      }
      /**
       * <pre>
       * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>uint64 weeklyTaskRewardLog = 7;</code>
       * @param value The weeklyTaskRewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setWeeklyTaskRewardLog(long value) {
        
        weeklyTaskRewardLog_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>uint64 weeklyTaskRewardLog = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeeklyTaskRewardLog() {
        
        weeklyTaskRewardLog_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskGetInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskGetInfoResponse)
    private static final com.dxx.game.dto.TaskProto.TaskGetInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskGetInfoResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskGetInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskGetInfoResponse>() {
      @java.lang.Override
      public TaskGetInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskGetInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskGetInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskGetInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskGetInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardDailyRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardDailyRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 任务列表返回的ID
     * </pre>
     *
     * <code>uint32 id = 2;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * <pre>
   *CMD PackageId=10503 任务-每日领取奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardDailyRequest}
   */
  public static final class TaskRewardDailyRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardDailyRequest)
      TaskRewardDailyRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskRewardDailyRequest.newBuilder() to construct.
    private TaskRewardDailyRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardDailyRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskRewardDailyRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskRewardDailyRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              id_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.class, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int ID_FIELD_NUMBER = 2;
    private int id_;
    /**
     * <pre>
     * 任务列表返回的ID
     * </pre>
     *
     * <code>uint32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (id_ != 0) {
        output.writeUInt32(2, id_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardDailyRequest other = (com.dxx.game.dto.TaskProto.TaskRewardDailyRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getId()
          != other.getId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardDailyRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10503 任务-每日领取奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardDailyRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardDailyRequest)
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.class, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        id_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest build() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequest result = new com.dxx.game.dto.TaskProto.TaskRewardDailyRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.id_ = id_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardDailyRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardDailyRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskRewardDailyRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int id_ ;
      /**
       * <pre>
       * 任务列表返回的ID
       * </pre>
       *
       * <code>uint32 id = 2;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       * 任务列表返回的ID
       * </pre>
       *
       * <code>uint32 id = 2;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务列表返回的ID
       * </pre>
       *
       * <code>uint32 id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardDailyRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardDailyRequest)
    private static final com.dxx.game.dto.TaskProto.TaskRewardDailyRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardDailyRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardDailyRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardDailyRequest>() {
      @java.lang.Override
      public TaskRewardDailyRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskRewardDailyRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskRewardDailyRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardDailyRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardDailyResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardDailyResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 活跃度-日
     * </pre>
     *
     * <code>uint32 activeDaily = 2;</code>
     * @return The activeDaily.
     */
    int getActiveDaily();

    /**
     * <pre>
     * 活跃度-周
     * </pre>
     *
     * <code>uint32 activeWeekly = 3;</code>
     * @return The activeWeekly.
     */
    int getActiveWeekly();

    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
     * @return Whether the updateTaskDto field is set.
     */
    boolean hasUpdateTaskDto();
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
     * @return The updateTaskDto.
     */
    com.dxx.game.dto.CommonProto.TaskDto getUpdateTaskDto();
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
     */
    com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getUpdateTaskDtoOrBuilder();

    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 6;</code>
     * @return Whether the tasks field is set.
     */
    boolean hasTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 6;</code>
     * @return The tasks.
     */
    com.dxx.game.dto.TaskProto.Tasks getTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 6;</code>
     */
    com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10504
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardDailyResponse}
   */
  public static final class TaskRewardDailyResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardDailyResponse)
      TaskRewardDailyResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskRewardDailyResponse.newBuilder() to construct.
    private TaskRewardDailyResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardDailyResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskRewardDailyResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskRewardDailyResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 16: {

              activeDaily_ = input.readUInt32();
              break;
            }
            case 24: {

              activeWeekly_ = input.readUInt32();
              break;
            }
            case 34: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 42: {
              com.dxx.game.dto.CommonProto.TaskDto.Builder subBuilder = null;
              if (updateTaskDto_ != null) {
                subBuilder = updateTaskDto_.toBuilder();
              }
              updateTaskDto_ = input.readMessage(com.dxx.game.dto.CommonProto.TaskDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(updateTaskDto_);
                updateTaskDto_ = subBuilder.buildPartial();
              }

              break;
            }
            case 50: {
              com.dxx.game.dto.TaskProto.Tasks.Builder subBuilder = null;
              if (tasks_ != null) {
                subBuilder = tasks_.toBuilder();
              }
              tasks_ = input.readMessage(com.dxx.game.dto.TaskProto.Tasks.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(tasks_);
                tasks_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.class, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ACTIVEDAILY_FIELD_NUMBER = 2;
    private int activeDaily_;
    /**
     * <pre>
     * 活跃度-日
     * </pre>
     *
     * <code>uint32 activeDaily = 2;</code>
     * @return The activeDaily.
     */
    @java.lang.Override
    public int getActiveDaily() {
      return activeDaily_;
    }

    public static final int ACTIVEWEEKLY_FIELD_NUMBER = 3;
    private int activeWeekly_;
    /**
     * <pre>
     * 活跃度-周
     * </pre>
     *
     * <code>uint32 activeWeekly = 3;</code>
     * @return The activeWeekly.
     */
    @java.lang.Override
    public int getActiveWeekly() {
      return activeWeekly_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int UPDATETASKDTO_FIELD_NUMBER = 5;
    private com.dxx.game.dto.CommonProto.TaskDto updateTaskDto_;
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
     * @return Whether the updateTaskDto field is set.
     */
    @java.lang.Override
    public boolean hasUpdateTaskDto() {
      return updateTaskDto_ != null;
    }
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
     * @return The updateTaskDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDto getUpdateTaskDto() {
      return updateTaskDto_ == null ? com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance() : updateTaskDto_;
    }
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getUpdateTaskDtoOrBuilder() {
      return getUpdateTaskDto();
    }

    public static final int TASKS_FIELD_NUMBER = 6;
    private com.dxx.game.dto.TaskProto.Tasks tasks_;
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 6;</code>
     * @return Whether the tasks field is set.
     */
    @java.lang.Override
    public boolean hasTasks() {
      return tasks_ != null;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 6;</code>
     * @return The tasks.
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.Tasks getTasks() {
      return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
      return getTasks();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (activeDaily_ != 0) {
        output.writeUInt32(2, activeDaily_);
      }
      if (activeWeekly_ != 0) {
        output.writeUInt32(3, activeWeekly_);
      }
      if (commonData_ != null) {
        output.writeMessage(4, getCommonData());
      }
      if (updateTaskDto_ != null) {
        output.writeMessage(5, getUpdateTaskDto());
      }
      if (tasks_ != null) {
        output.writeMessage(6, getTasks());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (activeDaily_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, activeDaily_);
      }
      if (activeWeekly_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, activeWeekly_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCommonData());
      }
      if (updateTaskDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getUpdateTaskDto());
      }
      if (tasks_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getTasks());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardDailyResponse other = (com.dxx.game.dto.TaskProto.TaskRewardDailyResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getActiveDaily()
          != other.getActiveDaily()) return false;
      if (getActiveWeekly()
          != other.getActiveWeekly()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasUpdateTaskDto() != other.hasUpdateTaskDto()) return false;
      if (hasUpdateTaskDto()) {
        if (!getUpdateTaskDto()
            .equals(other.getUpdateTaskDto())) return false;
      }
      if (hasTasks() != other.hasTasks()) return false;
      if (hasTasks()) {
        if (!getTasks()
            .equals(other.getTasks())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + ACTIVEDAILY_FIELD_NUMBER;
      hash = (53 * hash) + getActiveDaily();
      hash = (37 * hash) + ACTIVEWEEKLY_FIELD_NUMBER;
      hash = (53 * hash) + getActiveWeekly();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasUpdateTaskDto()) {
        hash = (37 * hash) + UPDATETASKDTO_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateTaskDto().hashCode();
      }
      if (hasTasks()) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasks().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardDailyResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10504
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardDailyResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardDailyResponse)
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.class, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        activeDaily_ = 0;

        activeWeekly_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = null;
        } else {
          updateTaskDto_ = null;
          updateTaskDtoBuilder_ = null;
        }
        if (tasksBuilder_ == null) {
          tasks_ = null;
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse build() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponse result = new com.dxx.game.dto.TaskProto.TaskRewardDailyResponse(this);
        result.code_ = code_;
        result.activeDaily_ = activeDaily_;
        result.activeWeekly_ = activeWeekly_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (updateTaskDtoBuilder_ == null) {
          result.updateTaskDto_ = updateTaskDto_;
        } else {
          result.updateTaskDto_ = updateTaskDtoBuilder_.build();
        }
        if (tasksBuilder_ == null) {
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardDailyResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardDailyResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getActiveDaily() != 0) {
          setActiveDaily(other.getActiveDaily());
        }
        if (other.getActiveWeekly() != 0) {
          setActiveWeekly(other.getActiveWeekly());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasUpdateTaskDto()) {
          mergeUpdateTaskDto(other.getUpdateTaskDto());
        }
        if (other.hasTasks()) {
          mergeTasks(other.getTasks());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskRewardDailyResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private int activeDaily_ ;
      /**
       * <pre>
       * 活跃度-日
       * </pre>
       *
       * <code>uint32 activeDaily = 2;</code>
       * @return The activeDaily.
       */
      @java.lang.Override
      public int getActiveDaily() {
        return activeDaily_;
      }
      /**
       * <pre>
       * 活跃度-日
       * </pre>
       *
       * <code>uint32 activeDaily = 2;</code>
       * @param value The activeDaily to set.
       * @return This builder for chaining.
       */
      public Builder setActiveDaily(int value) {
        
        activeDaily_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度-日
       * </pre>
       *
       * <code>uint32 activeDaily = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveDaily() {
        
        activeDaily_ = 0;
        onChanged();
        return this;
      }

      private int activeWeekly_ ;
      /**
       * <pre>
       * 活跃度-周
       * </pre>
       *
       * <code>uint32 activeWeekly = 3;</code>
       * @return The activeWeekly.
       */
      @java.lang.Override
      public int getActiveWeekly() {
        return activeWeekly_;
      }
      /**
       * <pre>
       * 活跃度-周
       * </pre>
       *
       * <code>uint32 activeWeekly = 3;</code>
       * @param value The activeWeekly to set.
       * @return This builder for chaining.
       */
      public Builder setActiveWeekly(int value) {
        
        activeWeekly_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度-周
       * </pre>
       *
       * <code>uint32 activeWeekly = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveWeekly() {
        
        activeWeekly_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.CommonProto.TaskDto updateTaskDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> updateTaskDtoBuilder_;
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       * @return Whether the updateTaskDto field is set.
       */
      public boolean hasUpdateTaskDto() {
        return updateTaskDtoBuilder_ != null || updateTaskDto_ != null;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       * @return The updateTaskDto.
       */
      public com.dxx.game.dto.CommonProto.TaskDto getUpdateTaskDto() {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_ == null ? com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance() : updateTaskDto_;
        } else {
          return updateTaskDtoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      public Builder setUpdateTaskDto(com.dxx.game.dto.CommonProto.TaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          updateTaskDto_ = value;
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      public Builder setUpdateTaskDto(
          com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = builderForValue.build();
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      public Builder mergeUpdateTaskDto(com.dxx.game.dto.CommonProto.TaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (updateTaskDto_ != null) {
            updateTaskDto_ =
              com.dxx.game.dto.CommonProto.TaskDto.newBuilder(updateTaskDto_).mergeFrom(value).buildPartial();
          } else {
            updateTaskDto_ = value;
          }
          onChanged();
        } else {
          updateTaskDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      public Builder clearUpdateTaskDto() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = null;
          onChanged();
        } else {
          updateTaskDto_ = null;
          updateTaskDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder getUpdateTaskDtoBuilder() {
        
        onChanged();
        return getUpdateTaskDtoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getUpdateTaskDtoOrBuilder() {
        if (updateTaskDtoBuilder_ != null) {
          return updateTaskDtoBuilder_.getMessageOrBuilder();
        } else {
          return updateTaskDto_ == null ?
              com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance() : updateTaskDto_;
        }
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
          getUpdateTaskDtoFieldBuilder() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder>(
                  getUpdateTaskDto(),
                  getParentForChildren(),
                  isClean());
          updateTaskDto_ = null;
        }
        return updateTaskDtoBuilder_;
      }

      private com.dxx.game.dto.TaskProto.Tasks tasks_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> tasksBuilder_;
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       * @return Whether the tasks field is set.
       */
      public boolean hasTasks() {
        return tasksBuilder_ != null || tasks_ != null;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       * @return The tasks.
       */
      public com.dxx.game.dto.TaskProto.Tasks getTasks() {
        if (tasksBuilder_ == null) {
          return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        } else {
          return tasksBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      public Builder setTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          tasks_ = value;
          onChanged();
        } else {
          tasksBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      public Builder setTasks(
          com.dxx.game.dto.TaskProto.Tasks.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          tasks_ = builderForValue.build();
          onChanged();
        } else {
          tasksBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      public Builder mergeTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (tasks_ != null) {
            tasks_ =
              com.dxx.game.dto.TaskProto.Tasks.newBuilder(tasks_).mergeFrom(value).buildPartial();
          } else {
            tasks_ = value;
          }
          onChanged();
        } else {
          tasksBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = null;
          onChanged();
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      public com.dxx.game.dto.TaskProto.Tasks.Builder getTasksBuilder() {
        
        onChanged();
        return getTasksFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilder();
        } else {
          return tasks_ == null ?
              com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder>(
                  getTasks(),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardDailyResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardDailyResponse)
    private static final com.dxx.game.dto.TaskProto.TaskRewardDailyResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardDailyResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardDailyResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardDailyResponse>() {
      @java.lang.Override
      public TaskRewardDailyResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskRewardDailyResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskRewardDailyResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardDailyResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardAchieveRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardAchieveRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 任务列表返回的ID
     * </pre>
     *
     * <code>uint32 id = 2;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * <pre>
   *CMD PackageId=10505 成就-领取奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardAchieveRequest}
   */
  public static final class TaskRewardAchieveRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardAchieveRequest)
      TaskRewardAchieveRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskRewardAchieveRequest.newBuilder() to construct.
    private TaskRewardAchieveRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardAchieveRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskRewardAchieveRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskRewardAchieveRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              id_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int ID_FIELD_NUMBER = 2;
    private int id_;
    /**
     * <pre>
     * 任务列表返回的ID
     * </pre>
     *
     * <code>uint32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (id_ != 0) {
        output.writeUInt32(2, id_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest other = (com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getId()
          != other.getId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10505 成就-领取奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardAchieveRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardAchieveRequest)
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        id_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest build() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest result = new com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.id_ = id_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int id_ ;
      /**
       * <pre>
       * 任务列表返回的ID
       * </pre>
       *
       * <code>uint32 id = 2;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       * 任务列表返回的ID
       * </pre>
       *
       * <code>uint32 id = 2;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务列表返回的ID
       * </pre>
       *
       * <code>uint32 id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardAchieveRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardAchieveRequest)
    private static final com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardAchieveRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardAchieveRequest>() {
      @java.lang.Override
      public TaskRewardAchieveRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskRewardAchieveRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskRewardAchieveRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardAchieveRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardAchieveResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardAchieveResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
     * @return Whether the updateTaskDto field is set.
     */
    boolean hasUpdateTaskDto();
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
     * @return The updateTaskDto.
     */
    com.dxx.game.dto.CommonProto.TaskDto getUpdateTaskDto();
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
     */
    com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getUpdateTaskDtoOrBuilder();

    /**
     * <pre>
     * 删除的任务ID
     * </pre>
     *
     * <code>uint32 deleteTaskDtoId = 4;</code>
     * @return The deleteTaskDtoId.
     */
    int getDeleteTaskDtoId();

    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return Whether the tasks field is set.
     */
    boolean hasTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return The tasks.
     */
    com.dxx.game.dto.TaskProto.Tasks getTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     */
    com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10506
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardAchieveResponse}
   */
  public static final class TaskRewardAchieveResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardAchieveResponse)
      TaskRewardAchieveResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskRewardAchieveResponse.newBuilder() to construct.
    private TaskRewardAchieveResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardAchieveResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskRewardAchieveResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskRewardAchieveResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.dxx.game.dto.CommonProto.TaskDto.Builder subBuilder = null;
              if (updateTaskDto_ != null) {
                subBuilder = updateTaskDto_.toBuilder();
              }
              updateTaskDto_ = input.readMessage(com.dxx.game.dto.CommonProto.TaskDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(updateTaskDto_);
                updateTaskDto_ = subBuilder.buildPartial();
              }

              break;
            }
            case 32: {

              deleteTaskDtoId_ = input.readUInt32();
              break;
            }
            case 42: {
              com.dxx.game.dto.TaskProto.Tasks.Builder subBuilder = null;
              if (tasks_ != null) {
                subBuilder = tasks_.toBuilder();
              }
              tasks_ = input.readMessage(com.dxx.game.dto.TaskProto.Tasks.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(tasks_);
                tasks_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int UPDATETASKDTO_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.TaskDto updateTaskDto_;
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
     * @return Whether the updateTaskDto field is set.
     */
    @java.lang.Override
    public boolean hasUpdateTaskDto() {
      return updateTaskDto_ != null;
    }
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
     * @return The updateTaskDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDto getUpdateTaskDto() {
      return updateTaskDto_ == null ? com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance() : updateTaskDto_;
    }
    /**
     * <pre>
     * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
     * </pre>
     *
     * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getUpdateTaskDtoOrBuilder() {
      return getUpdateTaskDto();
    }

    public static final int DELETETASKDTOID_FIELD_NUMBER = 4;
    private int deleteTaskDtoId_;
    /**
     * <pre>
     * 删除的任务ID
     * </pre>
     *
     * <code>uint32 deleteTaskDtoId = 4;</code>
     * @return The deleteTaskDtoId.
     */
    @java.lang.Override
    public int getDeleteTaskDtoId() {
      return deleteTaskDtoId_;
    }

    public static final int TASKS_FIELD_NUMBER = 5;
    private com.dxx.game.dto.TaskProto.Tasks tasks_;
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return Whether the tasks field is set.
     */
    @java.lang.Override
    public boolean hasTasks() {
      return tasks_ != null;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return The tasks.
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.Tasks getTasks() {
      return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
      return getTasks();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (updateTaskDto_ != null) {
        output.writeMessage(3, getUpdateTaskDto());
      }
      if (deleteTaskDtoId_ != 0) {
        output.writeUInt32(4, deleteTaskDtoId_);
      }
      if (tasks_ != null) {
        output.writeMessage(5, getTasks());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (updateTaskDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getUpdateTaskDto());
      }
      if (deleteTaskDtoId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, deleteTaskDtoId_);
      }
      if (tasks_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getTasks());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse other = (com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasUpdateTaskDto() != other.hasUpdateTaskDto()) return false;
      if (hasUpdateTaskDto()) {
        if (!getUpdateTaskDto()
            .equals(other.getUpdateTaskDto())) return false;
      }
      if (getDeleteTaskDtoId()
          != other.getDeleteTaskDtoId()) return false;
      if (hasTasks() != other.hasTasks()) return false;
      if (hasTasks()) {
        if (!getTasks()
            .equals(other.getTasks())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasUpdateTaskDto()) {
        hash = (37 * hash) + UPDATETASKDTO_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateTaskDto().hashCode();
      }
      hash = (37 * hash) + DELETETASKDTOID_FIELD_NUMBER;
      hash = (53 * hash) + getDeleteTaskDtoId();
      if (hasTasks()) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasks().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10506
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardAchieveResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardAchieveResponse)
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = null;
        } else {
          updateTaskDto_ = null;
          updateTaskDtoBuilder_ = null;
        }
        deleteTaskDtoId_ = 0;

        if (tasksBuilder_ == null) {
          tasks_ = null;
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse build() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse result = new com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (updateTaskDtoBuilder_ == null) {
          result.updateTaskDto_ = updateTaskDto_;
        } else {
          result.updateTaskDto_ = updateTaskDtoBuilder_.build();
        }
        result.deleteTaskDtoId_ = deleteTaskDtoId_;
        if (tasksBuilder_ == null) {
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasUpdateTaskDto()) {
          mergeUpdateTaskDto(other.getUpdateTaskDto());
        }
        if (other.getDeleteTaskDtoId() != 0) {
          setDeleteTaskDtoId(other.getDeleteTaskDtoId());
        }
        if (other.hasTasks()) {
          mergeTasks(other.getTasks());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.CommonProto.TaskDto updateTaskDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> updateTaskDtoBuilder_;
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       * @return Whether the updateTaskDto field is set.
       */
      public boolean hasUpdateTaskDto() {
        return updateTaskDtoBuilder_ != null || updateTaskDto_ != null;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       * @return The updateTaskDto.
       */
      public com.dxx.game.dto.CommonProto.TaskDto getUpdateTaskDto() {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_ == null ? com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance() : updateTaskDto_;
        } else {
          return updateTaskDtoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      public Builder setUpdateTaskDto(com.dxx.game.dto.CommonProto.TaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          updateTaskDto_ = value;
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      public Builder setUpdateTaskDto(
          com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = builderForValue.build();
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      public Builder mergeUpdateTaskDto(com.dxx.game.dto.CommonProto.TaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (updateTaskDto_ != null) {
            updateTaskDto_ =
              com.dxx.game.dto.CommonProto.TaskDto.newBuilder(updateTaskDto_).mergeFrom(value).buildPartial();
          } else {
            updateTaskDto_ = value;
          }
          onChanged();
        } else {
          updateTaskDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      public Builder clearUpdateTaskDto() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = null;
          onChanged();
        } else {
          updateTaskDto_ = null;
          updateTaskDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder getUpdateTaskDtoBuilder() {
        
        onChanged();
        return getUpdateTaskDtoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getUpdateTaskDtoOrBuilder() {
        if (updateTaskDtoBuilder_ != null) {
          return updateTaskDtoBuilder_.getMessageOrBuilder();
        } else {
          return updateTaskDto_ == null ?
              com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance() : updateTaskDto_;
        }
      }
      /**
       * <pre>
       * 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增
       * </pre>
       *
       * <code>.Proto.Common.TaskDto updateTaskDto = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
          getUpdateTaskDtoFieldBuilder() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder>(
                  getUpdateTaskDto(),
                  getParentForChildren(),
                  isClean());
          updateTaskDto_ = null;
        }
        return updateTaskDtoBuilder_;
      }

      private int deleteTaskDtoId_ ;
      /**
       * <pre>
       * 删除的任务ID
       * </pre>
       *
       * <code>uint32 deleteTaskDtoId = 4;</code>
       * @return The deleteTaskDtoId.
       */
      @java.lang.Override
      public int getDeleteTaskDtoId() {
        return deleteTaskDtoId_;
      }
      /**
       * <pre>
       * 删除的任务ID
       * </pre>
       *
       * <code>uint32 deleteTaskDtoId = 4;</code>
       * @param value The deleteTaskDtoId to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteTaskDtoId(int value) {
        
        deleteTaskDtoId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 删除的任务ID
       * </pre>
       *
       * <code>uint32 deleteTaskDtoId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteTaskDtoId() {
        
        deleteTaskDtoId_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.TaskProto.Tasks tasks_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> tasksBuilder_;
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       * @return Whether the tasks field is set.
       */
      public boolean hasTasks() {
        return tasksBuilder_ != null || tasks_ != null;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       * @return The tasks.
       */
      public com.dxx.game.dto.TaskProto.Tasks getTasks() {
        if (tasksBuilder_ == null) {
          return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        } else {
          return tasksBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder setTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          tasks_ = value;
          onChanged();
        } else {
          tasksBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder setTasks(
          com.dxx.game.dto.TaskProto.Tasks.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          tasks_ = builderForValue.build();
          onChanged();
        } else {
          tasksBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder mergeTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (tasks_ != null) {
            tasks_ =
              com.dxx.game.dto.TaskProto.Tasks.newBuilder(tasks_).mergeFrom(value).buildPartial();
          } else {
            tasks_ = value;
          }
          onChanged();
        } else {
          tasksBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = null;
          onChanged();
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public com.dxx.game.dto.TaskProto.Tasks.Builder getTasksBuilder() {
        
        onChanged();
        return getTasksFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilder();
        } else {
          return tasks_ == null ?
              com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder>(
                  getTasks(),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardAchieveResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardAchieveResponse)
    private static final com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardAchieveResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardAchieveResponse>() {
      @java.lang.Override
      public TaskRewardAchieveResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskRewardAchieveResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskRewardAchieveResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardAchieveResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActiveRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     * 对应的活跃度配置表ID
     * </pre>
     *
     * <code>uint32 id = 3;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * <pre>
   *CMD PackageId=10507 任务-领取活跃度奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskActiveRewardRequest}
   */
  public static final class TaskActiveRewardRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActiveRewardRequest)
      TaskActiveRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskActiveRewardRequest.newBuilder() to construct.
    private TaskActiveRewardRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskActiveRewardRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskActiveRewardRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskActiveRewardRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              type_ = input.readUInt32();
              break;
            }
            case 24: {

              id_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.class, com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int ID_FIELD_NUMBER = 3;
    private int id_;
    /**
     * <pre>
     * 对应的活跃度配置表ID
     * </pre>
     *
     * <code>uint32 id = 3;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (type_ != 0) {
        output.writeUInt32(2, type_);
      }
      if (id_ != 0) {
        output.writeUInt32(3, id_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, type_);
      }
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActiveRewardRequest other = (com.dxx.game.dto.TaskProto.TaskActiveRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getType()
          != other.getType()) return false;
      if (getId()
          != other.getId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActiveRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10507 任务-领取活跃度奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskActiveRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActiveRewardRequest)
        com.dxx.game.dto.TaskProto.TaskActiveRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.class, com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        type_ = 0;

        id_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardRequest build() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardRequest result = new com.dxx.game.dto.TaskProto.TaskActiveRewardRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.type_ = type_;
        result.id_ = id_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActiveRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActiveRewardRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskActiveRewardRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskActiveRewardRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int type_ ;
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int id_ ;
      /**
       * <pre>
       * 对应的活跃度配置表ID
       * </pre>
       *
       * <code>uint32 id = 3;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       * 对应的活跃度配置表ID
       * </pre>
       *
       * <code>uint32 id = 3;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 对应的活跃度配置表ID
       * </pre>
       *
       * <code>uint32 id = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActiveRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActiveRewardRequest)
    private static final com.dxx.game.dto.TaskProto.TaskActiveRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActiveRewardRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActiveRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskActiveRewardRequest>() {
      @java.lang.Override
      public TaskActiveRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskActiveRewardRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskActiveRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActiveRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActiveRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 rewardLog = 3;</code>
     * @return The rewardLog.
     */
    long getRewardLog();

    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return Whether the tasks field is set.
     */
    boolean hasTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return The tasks.
     */
    com.dxx.game.dto.TaskProto.Tasks getTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     */
    com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10508
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskActiveRewardResponse}
   */
  public static final class TaskActiveRewardResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActiveRewardResponse)
      TaskActiveRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskActiveRewardResponse.newBuilder() to construct.
    private TaskActiveRewardResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskActiveRewardResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskActiveRewardResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskActiveRewardResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 16: {

              type_ = input.readUInt32();
              break;
            }
            case 24: {

              rewardLog_ = input.readUInt64();
              break;
            }
            case 34: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 42: {
              com.dxx.game.dto.TaskProto.Tasks.Builder subBuilder = null;
              if (tasks_ != null) {
                subBuilder = tasks_.toBuilder();
              }
              tasks_ = input.readMessage(com.dxx.game.dto.TaskProto.Tasks.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(tasks_);
                tasks_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.class, com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int REWARDLOG_FIELD_NUMBER = 3;
    private long rewardLog_;
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 rewardLog = 3;</code>
     * @return The rewardLog.
     */
    @java.lang.Override
    public long getRewardLog() {
      return rewardLog_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int TASKS_FIELD_NUMBER = 5;
    private com.dxx.game.dto.TaskProto.Tasks tasks_;
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return Whether the tasks field is set.
     */
    @java.lang.Override
    public boolean hasTasks() {
      return tasks_ != null;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     * @return The tasks.
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.Tasks getTasks() {
      return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
      return getTasks();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (type_ != 0) {
        output.writeUInt32(2, type_);
      }
      if (rewardLog_ != 0L) {
        output.writeUInt64(3, rewardLog_);
      }
      if (commonData_ != null) {
        output.writeMessage(4, getCommonData());
      }
      if (tasks_ != null) {
        output.writeMessage(5, getTasks());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, type_);
      }
      if (rewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, rewardLog_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCommonData());
      }
      if (tasks_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getTasks());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActiveRewardResponse other = (com.dxx.game.dto.TaskProto.TaskActiveRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getType()
          != other.getType()) return false;
      if (getRewardLog()
          != other.getRewardLog()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasTasks() != other.hasTasks()) return false;
      if (hasTasks()) {
        if (!getTasks()
            .equals(other.getTasks())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + REWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRewardLog());
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasTasks()) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasks().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActiveRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10508
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskActiveRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActiveRewardResponse)
        com.dxx.game.dto.TaskProto.TaskActiveRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.class, com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        type_ = 0;

        rewardLog_ = 0L;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (tasksBuilder_ == null) {
          tasks_ = null;
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardResponse build() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardResponse result = new com.dxx.game.dto.TaskProto.TaskActiveRewardResponse(this);
        result.code_ = code_;
        result.type_ = type_;
        result.rewardLog_ = rewardLog_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (tasksBuilder_ == null) {
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActiveRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActiveRewardResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getRewardLog() != 0L) {
          setRewardLog(other.getRewardLog());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasTasks()) {
          mergeTasks(other.getTasks());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskActiveRewardResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskActiveRewardResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private long rewardLog_ ;
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 rewardLog = 3;</code>
       * @return The rewardLog.
       */
      @java.lang.Override
      public long getRewardLog() {
        return rewardLog_;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 rewardLog = 3;</code>
       * @param value The rewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setRewardLog(long value) {
        
        rewardLog_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 rewardLog = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardLog() {
        
        rewardLog_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.TaskProto.Tasks tasks_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> tasksBuilder_;
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       * @return Whether the tasks field is set.
       */
      public boolean hasTasks() {
        return tasksBuilder_ != null || tasks_ != null;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       * @return The tasks.
       */
      public com.dxx.game.dto.TaskProto.Tasks getTasks() {
        if (tasksBuilder_ == null) {
          return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        } else {
          return tasksBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder setTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          tasks_ = value;
          onChanged();
        } else {
          tasksBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder setTasks(
          com.dxx.game.dto.TaskProto.Tasks.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          tasks_ = builderForValue.build();
          onChanged();
        } else {
          tasksBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder mergeTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (tasks_ != null) {
            tasks_ =
              com.dxx.game.dto.TaskProto.Tasks.newBuilder(tasks_).mergeFrom(value).buildPartial();
          } else {
            tasks_ = value;
          }
          onChanged();
        } else {
          tasksBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = null;
          onChanged();
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public com.dxx.game.dto.TaskProto.Tasks.Builder getTasksBuilder() {
        
        onChanged();
        return getTasksFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilder();
        } else {
          return tasks_ == null ?
              com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder>(
                  getTasks(),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActiveRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActiveRewardResponse)
    private static final com.dxx.game.dto.TaskProto.TaskActiveRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActiveRewardResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActiveRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskActiveRewardResponse>() {
      @java.lang.Override
      public TaskActiveRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskActiveRewardResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskActiveRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActiveRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveRewardAllRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActiveRewardAllRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   *CMD PackageId=10509 任务-领取全活跃度奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskActiveRewardAllRequest}
   */
  public static final class TaskActiveRewardAllRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActiveRewardAllRequest)
      TaskActiveRewardAllRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskActiveRewardAllRequest.newBuilder() to construct.
    private TaskActiveRewardAllRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskActiveRewardAllRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskActiveRewardAllRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskActiveRewardAllRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              type_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (type_ != 0) {
        output.writeUInt32(2, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest other = (com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10509 任务-领取全活跃度奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskActiveRewardAllRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActiveRewardAllRequest)
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest build() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest result = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int type_ ;
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActiveRewardAllRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActiveRewardAllRequest)
    private static final com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActiveRewardAllRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskActiveRewardAllRequest>() {
      @java.lang.Override
      public TaskActiveRewardAllRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskActiveRewardAllRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskActiveRewardAllRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActiveRewardAllRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveRewardAllResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActiveRewardAllResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 4;</code>
     * @return Whether the tasks field is set.
     */
    boolean hasTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 4;</code>
     * @return The tasks.
     */
    com.dxx.game.dto.TaskProto.Tasks getTasks();
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 4;</code>
     */
    com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder();

    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 rewardLog = 5;</code>
     * @return The rewardLog.
     */
    long getRewardLog();
  }
  /**
   * <pre>
   *CMD PackageId=10510
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskActiveRewardAllResponse}
   */
  public static final class TaskActiveRewardAllResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActiveRewardAllResponse)
      TaskActiveRewardAllResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskActiveRewardAllResponse.newBuilder() to construct.
    private TaskActiveRewardAllResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskActiveRewardAllResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskActiveRewardAllResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TaskActiveRewardAllResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 16: {

              type_ = input.readUInt32();
              break;
            }
            case 26: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 34: {
              com.dxx.game.dto.TaskProto.Tasks.Builder subBuilder = null;
              if (tasks_ != null) {
                subBuilder = tasks_.toBuilder();
              }
              tasks_ = input.readMessage(com.dxx.game.dto.TaskProto.Tasks.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(tasks_);
                tasks_ = subBuilder.buildPartial();
              }

              break;
            }
            case 40: {

              rewardLog_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int TASKS_FIELD_NUMBER = 4;
    private com.dxx.game.dto.TaskProto.Tasks tasks_;
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 4;</code>
     * @return Whether the tasks field is set.
     */
    @java.lang.Override
    public boolean hasTasks() {
      return tasks_ != null;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 4;</code>
     * @return The tasks.
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.Tasks getTasks() {
      return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
    }
    /**
     * <pre>
     * 领取任务奖励触发其他任务状态变化
     * </pre>
     *
     * <code>.Proto.Task.Tasks tasks = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
      return getTasks();
    }

    public static final int REWARDLOG_FIELD_NUMBER = 5;
    private long rewardLog_;
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 rewardLog = 5;</code>
     * @return The rewardLog.
     */
    @java.lang.Override
    public long getRewardLog() {
      return rewardLog_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (type_ != 0) {
        output.writeUInt32(2, type_);
      }
      if (commonData_ != null) {
        output.writeMessage(3, getCommonData());
      }
      if (tasks_ != null) {
        output.writeMessage(4, getTasks());
      }
      if (rewardLog_ != 0L) {
        output.writeUInt64(5, rewardLog_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, type_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      if (tasks_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getTasks());
      }
      if (rewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, rewardLog_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse other = (com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getType()
          != other.getType()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasTasks() != other.hasTasks()) return false;
      if (hasTasks()) {
        if (!getTasks()
            .equals(other.getTasks())) return false;
      }
      if (getRewardLog()
          != other.getRewardLog()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasTasks()) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasks().hashCode();
      }
      hash = (37 * hash) + REWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRewardLog());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10510
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskActiveRewardAllResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActiveRewardAllResponse)
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        type_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (tasksBuilder_ == null) {
          tasks_ = null;
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }
        rewardLog_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse build() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse result = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse(this);
        result.code_ = code_;
        result.type_ = type_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (tasksBuilder_ == null) {
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
        result.rewardLog_ = rewardLog_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasTasks()) {
          mergeTasks(other.getTasks());
        }
        if (other.getRewardLog() != 0L) {
          setRewardLog(other.getRewardLog());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.TaskProto.Tasks tasks_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> tasksBuilder_;
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       * @return Whether the tasks field is set.
       */
      public boolean hasTasks() {
        return tasksBuilder_ != null || tasks_ != null;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       * @return The tasks.
       */
      public com.dxx.game.dto.TaskProto.Tasks getTasks() {
        if (tasksBuilder_ == null) {
          return tasks_ == null ? com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        } else {
          return tasksBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      public Builder setTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          tasks_ = value;
          onChanged();
        } else {
          tasksBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      public Builder setTasks(
          com.dxx.game.dto.TaskProto.Tasks.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          tasks_ = builderForValue.build();
          onChanged();
        } else {
          tasksBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      public Builder mergeTasks(com.dxx.game.dto.TaskProto.Tasks value) {
        if (tasksBuilder_ == null) {
          if (tasks_ != null) {
            tasks_ =
              com.dxx.game.dto.TaskProto.Tasks.newBuilder(tasks_).mergeFrom(value).buildPartial();
          } else {
            tasks_ = value;
          }
          onChanged();
        } else {
          tasksBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = null;
          onChanged();
        } else {
          tasks_ = null;
          tasksBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      public com.dxx.game.dto.TaskProto.Tasks.Builder getTasksBuilder() {
        
        onChanged();
        return getTasksFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      public com.dxx.game.dto.TaskProto.TasksOrBuilder getTasksOrBuilder() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilder();
        } else {
          return tasks_ == null ?
              com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance() : tasks_;
        }
      }
      /**
       * <pre>
       * 领取任务奖励触发其他任务状态变化
       * </pre>
       *
       * <code>.Proto.Task.Tasks tasks = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.TaskProto.Tasks, com.dxx.game.dto.TaskProto.Tasks.Builder, com.dxx.game.dto.TaskProto.TasksOrBuilder>(
                  getTasks(),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }

      private long rewardLog_ ;
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 rewardLog = 5;</code>
       * @return The rewardLog.
       */
      @java.lang.Override
      public long getRewardLog() {
        return rewardLog_;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 rewardLog = 5;</code>
       * @param value The rewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setRewardLog(long value) {
        
        rewardLog_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 rewardLog = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardLog() {
        
        rewardLog_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActiveRewardAllResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActiveRewardAllResponse)
    private static final com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActiveRewardAllResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskActiveRewardAllResponse>() {
      @java.lang.Override
      public TaskActiveRewardAllResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TaskActiveRewardAllResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TaskActiveRewardAllResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActiveRewardAllResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TasksOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.Tasks)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.TaskDto> 
        getDailyTaskList();
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    com.dxx.game.dto.CommonProto.TaskDto getDailyTask(int index);
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    int getDailyTaskCount();
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
        getDailyTaskOrBuilderList();
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getDailyTaskOrBuilder(
        int index);

    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.TaskDto> 
        getAchievementsList();
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    com.dxx.game.dto.CommonProto.TaskDto getAchievements(int index);
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    int getAchievementsCount();
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
        getAchievementsOrBuilderList();
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getAchievementsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code Proto.Task.Tasks}
   */
  public static final class Tasks extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Task.Tasks)
      TasksOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Tasks.newBuilder() to construct.
    private Tasks(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Tasks() {
      dailyTask_ = java.util.Collections.emptyList();
      achievements_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Tasks();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Tasks(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                dailyTask_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TaskDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              dailyTask_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.TaskDto.parser(), extensionRegistry));
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                achievements_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TaskDto>();
                mutable_bitField0_ |= 0x00000002;
              }
              achievements_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.TaskDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          dailyTask_ = java.util.Collections.unmodifiableList(dailyTask_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          achievements_ = java.util.Collections.unmodifiableList(achievements_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_Tasks_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_Tasks_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.Tasks.class, com.dxx.game.dto.TaskProto.Tasks.Builder.class);
    }

    public static final int DAILYTASK_FIELD_NUMBER = 1;
    private java.util.List<com.dxx.game.dto.CommonProto.TaskDto> dailyTask_;
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.TaskDto> getDailyTaskList() {
      return dailyTask_;
    }
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
        getDailyTaskOrBuilderList() {
      return dailyTask_;
    }
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    @java.lang.Override
    public int getDailyTaskCount() {
      return dailyTask_.size();
    }
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDto getDailyTask(int index) {
      return dailyTask_.get(index);
    }
    /**
     * <pre>
     * 日常
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getDailyTaskOrBuilder(
        int index) {
      return dailyTask_.get(index);
    }

    public static final int ACHIEVEMENTS_FIELD_NUMBER = 2;
    private java.util.List<com.dxx.game.dto.CommonProto.TaskDto> achievements_;
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.TaskDto> getAchievementsList() {
      return achievements_;
    }
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
        getAchievementsOrBuilderList() {
      return achievements_;
    }
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    @java.lang.Override
    public int getAchievementsCount() {
      return achievements_.size();
    }
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDto getAchievements(int index) {
      return achievements_.get(index);
    }
    /**
     * <pre>
     * 成就
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getAchievementsOrBuilder(
        int index) {
      return achievements_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < dailyTask_.size(); i++) {
        output.writeMessage(1, dailyTask_.get(i));
      }
      for (int i = 0; i < achievements_.size(); i++) {
        output.writeMessage(2, achievements_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < dailyTask_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, dailyTask_.get(i));
      }
      for (int i = 0; i < achievements_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, achievements_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.Tasks)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.Tasks other = (com.dxx.game.dto.TaskProto.Tasks) obj;

      if (!getDailyTaskList()
          .equals(other.getDailyTaskList())) return false;
      if (!getAchievementsList()
          .equals(other.getAchievementsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDailyTaskCount() > 0) {
        hash = (37 * hash) + DAILYTASK_FIELD_NUMBER;
        hash = (53 * hash) + getDailyTaskList().hashCode();
      }
      if (getAchievementsCount() > 0) {
        hash = (37 * hash) + ACHIEVEMENTS_FIELD_NUMBER;
        hash = (53 * hash) + getAchievementsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.Tasks parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.Tasks prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Task.Tasks}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.Tasks)
        com.dxx.game.dto.TaskProto.TasksOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_Tasks_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_Tasks_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.Tasks.class, com.dxx.game.dto.TaskProto.Tasks.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.Tasks.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDailyTaskFieldBuilder();
          getAchievementsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dailyTaskBuilder_ == null) {
          dailyTask_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dailyTaskBuilder_.clear();
        }
        if (achievementsBuilder_ == null) {
          achievements_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          achievementsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_Tasks_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.Tasks getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.Tasks build() {
        com.dxx.game.dto.TaskProto.Tasks result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.Tasks buildPartial() {
        com.dxx.game.dto.TaskProto.Tasks result = new com.dxx.game.dto.TaskProto.Tasks(this);
        int from_bitField0_ = bitField0_;
        if (dailyTaskBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            dailyTask_ = java.util.Collections.unmodifiableList(dailyTask_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.dailyTask_ = dailyTask_;
        } else {
          result.dailyTask_ = dailyTaskBuilder_.build();
        }
        if (achievementsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            achievements_ = java.util.Collections.unmodifiableList(achievements_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.achievements_ = achievements_;
        } else {
          result.achievements_ = achievementsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.Tasks) {
          return mergeFrom((com.dxx.game.dto.TaskProto.Tasks)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.Tasks other) {
        if (other == com.dxx.game.dto.TaskProto.Tasks.getDefaultInstance()) return this;
        if (dailyTaskBuilder_ == null) {
          if (!other.dailyTask_.isEmpty()) {
            if (dailyTask_.isEmpty()) {
              dailyTask_ = other.dailyTask_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureDailyTaskIsMutable();
              dailyTask_.addAll(other.dailyTask_);
            }
            onChanged();
          }
        } else {
          if (!other.dailyTask_.isEmpty()) {
            if (dailyTaskBuilder_.isEmpty()) {
              dailyTaskBuilder_.dispose();
              dailyTaskBuilder_ = null;
              dailyTask_ = other.dailyTask_;
              bitField0_ = (bitField0_ & ~0x00000001);
              dailyTaskBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDailyTaskFieldBuilder() : null;
            } else {
              dailyTaskBuilder_.addAllMessages(other.dailyTask_);
            }
          }
        }
        if (achievementsBuilder_ == null) {
          if (!other.achievements_.isEmpty()) {
            if (achievements_.isEmpty()) {
              achievements_ = other.achievements_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureAchievementsIsMutable();
              achievements_.addAll(other.achievements_);
            }
            onChanged();
          }
        } else {
          if (!other.achievements_.isEmpty()) {
            if (achievementsBuilder_.isEmpty()) {
              achievementsBuilder_.dispose();
              achievementsBuilder_ = null;
              achievements_ = other.achievements_;
              bitField0_ = (bitField0_ & ~0x00000002);
              achievementsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getAchievementsFieldBuilder() : null;
            } else {
              achievementsBuilder_.addAllMessages(other.achievements_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TaskProto.Tasks parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TaskProto.Tasks) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.dxx.game.dto.CommonProto.TaskDto> dailyTask_ =
        java.util.Collections.emptyList();
      private void ensureDailyTaskIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          dailyTask_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TaskDto>(dailyTask_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> dailyTaskBuilder_;

      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TaskDto> getDailyTaskList() {
        if (dailyTaskBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dailyTask_);
        } else {
          return dailyTaskBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public int getDailyTaskCount() {
        if (dailyTaskBuilder_ == null) {
          return dailyTask_.size();
        } else {
          return dailyTaskBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto getDailyTask(int index) {
        if (dailyTaskBuilder_ == null) {
          return dailyTask_.get(index);
        } else {
          return dailyTaskBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder setDailyTask(
          int index, com.dxx.game.dto.CommonProto.TaskDto value) {
        if (dailyTaskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDailyTaskIsMutable();
          dailyTask_.set(index, value);
          onChanged();
        } else {
          dailyTaskBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder setDailyTask(
          int index, com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (dailyTaskBuilder_ == null) {
          ensureDailyTaskIsMutable();
          dailyTask_.set(index, builderForValue.build());
          onChanged();
        } else {
          dailyTaskBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder addDailyTask(com.dxx.game.dto.CommonProto.TaskDto value) {
        if (dailyTaskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDailyTaskIsMutable();
          dailyTask_.add(value);
          onChanged();
        } else {
          dailyTaskBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder addDailyTask(
          int index, com.dxx.game.dto.CommonProto.TaskDto value) {
        if (dailyTaskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDailyTaskIsMutable();
          dailyTask_.add(index, value);
          onChanged();
        } else {
          dailyTaskBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder addDailyTask(
          com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (dailyTaskBuilder_ == null) {
          ensureDailyTaskIsMutable();
          dailyTask_.add(builderForValue.build());
          onChanged();
        } else {
          dailyTaskBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder addDailyTask(
          int index, com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (dailyTaskBuilder_ == null) {
          ensureDailyTaskIsMutable();
          dailyTask_.add(index, builderForValue.build());
          onChanged();
        } else {
          dailyTaskBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder addAllDailyTask(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.TaskDto> values) {
        if (dailyTaskBuilder_ == null) {
          ensureDailyTaskIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dailyTask_);
          onChanged();
        } else {
          dailyTaskBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder clearDailyTask() {
        if (dailyTaskBuilder_ == null) {
          dailyTask_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dailyTaskBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public Builder removeDailyTask(int index) {
        if (dailyTaskBuilder_ == null) {
          ensureDailyTaskIsMutable();
          dailyTask_.remove(index);
          onChanged();
        } else {
          dailyTaskBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder getDailyTaskBuilder(
          int index) {
        return getDailyTaskFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getDailyTaskOrBuilder(
          int index) {
        if (dailyTaskBuilder_ == null) {
          return dailyTask_.get(index);  } else {
          return dailyTaskBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
           getDailyTaskOrBuilderList() {
        if (dailyTaskBuilder_ != null) {
          return dailyTaskBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dailyTask_);
        }
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder addDailyTaskBuilder() {
        return getDailyTaskFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder addDailyTaskBuilder(
          int index) {
        return getDailyTaskFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 日常
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto dailyTask = 1;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TaskDto.Builder> 
           getDailyTaskBuilderList() {
        return getDailyTaskFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
          getDailyTaskFieldBuilder() {
        if (dailyTaskBuilder_ == null) {
          dailyTaskBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder>(
                  dailyTask_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          dailyTask_ = null;
        }
        return dailyTaskBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.TaskDto> achievements_ =
        java.util.Collections.emptyList();
      private void ensureAchievementsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          achievements_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TaskDto>(achievements_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> achievementsBuilder_;

      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TaskDto> getAchievementsList() {
        if (achievementsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(achievements_);
        } else {
          return achievementsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public int getAchievementsCount() {
        if (achievementsBuilder_ == null) {
          return achievements_.size();
        } else {
          return achievementsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto getAchievements(int index) {
        if (achievementsBuilder_ == null) {
          return achievements_.get(index);
        } else {
          return achievementsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder setAchievements(
          int index, com.dxx.game.dto.CommonProto.TaskDto value) {
        if (achievementsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAchievementsIsMutable();
          achievements_.set(index, value);
          onChanged();
        } else {
          achievementsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder setAchievements(
          int index, com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (achievementsBuilder_ == null) {
          ensureAchievementsIsMutable();
          achievements_.set(index, builderForValue.build());
          onChanged();
        } else {
          achievementsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder addAchievements(com.dxx.game.dto.CommonProto.TaskDto value) {
        if (achievementsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAchievementsIsMutable();
          achievements_.add(value);
          onChanged();
        } else {
          achievementsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder addAchievements(
          int index, com.dxx.game.dto.CommonProto.TaskDto value) {
        if (achievementsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAchievementsIsMutable();
          achievements_.add(index, value);
          onChanged();
        } else {
          achievementsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder addAchievements(
          com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (achievementsBuilder_ == null) {
          ensureAchievementsIsMutable();
          achievements_.add(builderForValue.build());
          onChanged();
        } else {
          achievementsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder addAchievements(
          int index, com.dxx.game.dto.CommonProto.TaskDto.Builder builderForValue) {
        if (achievementsBuilder_ == null) {
          ensureAchievementsIsMutable();
          achievements_.add(index, builderForValue.build());
          onChanged();
        } else {
          achievementsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder addAllAchievements(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.TaskDto> values) {
        if (achievementsBuilder_ == null) {
          ensureAchievementsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, achievements_);
          onChanged();
        } else {
          achievementsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder clearAchievements() {
        if (achievementsBuilder_ == null) {
          achievements_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          achievementsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public Builder removeAchievements(int index) {
        if (achievementsBuilder_ == null) {
          ensureAchievementsIsMutable();
          achievements_.remove(index);
          onChanged();
        } else {
          achievementsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder getAchievementsBuilder(
          int index) {
        return getAchievementsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDtoOrBuilder getAchievementsOrBuilder(
          int index) {
        if (achievementsBuilder_ == null) {
          return achievements_.get(index);  } else {
          return achievementsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
           getAchievementsOrBuilderList() {
        if (achievementsBuilder_ != null) {
          return achievementsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(achievements_);
        }
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder addAchievementsBuilder() {
        return getAchievementsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskDto.Builder addAchievementsBuilder(
          int index) {
        return getAchievementsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.TaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 成就
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskDto achievements = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TaskDto.Builder> 
           getAchievementsBuilderList() {
        return getAchievementsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder> 
          getAchievementsFieldBuilder() {
        if (achievementsBuilder_ == null) {
          achievementsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.TaskDto, com.dxx.game.dto.CommonProto.TaskDto.Builder, com.dxx.game.dto.CommonProto.TaskDtoOrBuilder>(
                  achievements_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          achievements_ = null;
        }
        return achievementsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Task.Tasks)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.Tasks)
    private static final com.dxx.game.dto.TaskProto.Tasks DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.Tasks();
    }

    public static com.dxx.game.dto.TaskProto.Tasks getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Tasks>
        PARSER = new com.google.protobuf.AbstractParser<Tasks>() {
      @java.lang.Override
      public Tasks parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Tasks(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Tasks> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Tasks> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.Tasks getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActiveRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskActiveRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActiveRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskActiveRewardResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_Tasks_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Task_Tasks_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\ntask.proto\022\nProto.Task\032\014common.proto\"F" +
      "\n\022TaskGetInfoRequest\0220\n\014commonParams\030\001 \001" +
      "(\0132\032.Proto.Common.CommonParams\"\315\001\n\023TaskG" +
      "etInfoResponse\022\014\n\004code\030\001 \001(\005\022 \n\005tasks\030\002 " +
      "\001(\0132\021.Proto.Task.Tasks\022\027\n\017dailyTaskActiv" +
      "e\030\003 \001(\r\022\030\n\020weeklyTaskActive\030\004 \001(\r\022\032\n\022dai" +
      "lyTaskResetTime\030\005 \001(\004\022\032\n\022dailyTaskReward" +
      "Log\030\006 \001(\004\022\033\n\023weeklyTaskRewardLog\030\007 \001(\004\"V" +
      "\n\026TaskRewardDailyRequest\0220\n\014commonParams" +
      "\030\001 \001(\0132\032.Proto.Common.CommonParams\022\n\n\002id" +
      "\030\002 \001(\r\"\320\001\n\027TaskRewardDailyResponse\022\014\n\004co" +
      "de\030\001 \001(\005\022\023\n\013activeDaily\030\002 \001(\r\022\024\n\014activeW" +
      "eekly\030\003 \001(\r\022,\n\ncommonData\030\004 \001(\0132\030.Proto." +
      "Common.CommonData\022,\n\rupdateTaskDto\030\005 \001(\013" +
      "2\025.Proto.Common.TaskDto\022 \n\005tasks\030\006 \001(\0132\021" +
      ".Proto.Task.Tasks\"X\n\030TaskRewardAchieveRe" +
      "quest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Comm" +
      "on.CommonParams\022\n\n\002id\030\002 \001(\r\"\300\001\n\031TaskRewa" +
      "rdAchieveResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommo" +
      "nData\030\002 \001(\0132\030.Proto.Common.CommonData\022,\n" +
      "\rupdateTaskDto\030\003 \001(\0132\025.Proto.Common.Task" +
      "Dto\022\027\n\017deleteTaskDtoId\030\004 \001(\r\022 \n\005tasks\030\005 " +
      "\001(\0132\021.Proto.Task.Tasks\"e\n\027TaskActiveRewa" +
      "rdRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto." +
      "Common.CommonParams\022\014\n\004type\030\002 \001(\r\022\n\n\002id\030" +
      "\003 \001(\r\"\231\001\n\030TaskActiveRewardResponse\022\014\n\004co" +
      "de\030\001 \001(\005\022\014\n\004type\030\002 \001(\r\022\021\n\trewardLog\030\003 \001(" +
      "\004\022,\n\ncommonData\030\004 \001(\0132\030.Proto.Common.Com" +
      "monData\022 \n\005tasks\030\005 \001(\0132\021.Proto.Task.Task" +
      "s\"\\\n\032TaskActiveRewardAllRequest\0220\n\014commo" +
      "nParams\030\001 \001(\0132\032.Proto.Common.CommonParam" +
      "s\022\014\n\004type\030\002 \001(\r\"\234\001\n\033TaskActiveRewardAllR" +
      "esponse\022\014\n\004code\030\001 \001(\005\022\014\n\004type\030\002 \001(\r\022,\n\nc" +
      "ommonData\030\003 \001(\0132\030.Proto.Common.CommonDat" +
      "a\022 \n\005tasks\030\004 \001(\0132\021.Proto.Task.Tasks\022\021\n\tr" +
      "ewardLog\030\005 \001(\004\"^\n\005Tasks\022(\n\tdailyTask\030\001 \003" +
      "(\0132\025.Proto.Common.TaskDto\022+\n\014achievement" +
      "s\030\002 \003(\0132\025.Proto.Common.TaskDtoB\035\n\020com.dx" +
      "x.game.dtoB\tTaskProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Task_TaskGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Task_TaskGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "Tasks", "DailyTaskActive", "WeeklyTaskActive", "DailyTaskResetTime", "DailyTaskRewardLog", "WeeklyTaskRewardLog", });
    internal_static_Proto_Task_TaskRewardDailyRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardDailyRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Id", });
    internal_static_Proto_Task_TaskRewardDailyResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardDailyResponse_descriptor,
        new java.lang.String[] { "Code", "ActiveDaily", "ActiveWeekly", "CommonData", "UpdateTaskDto", "Tasks", });
    internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Id", });
    internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "UpdateTaskDto", "DeleteTaskDtoId", "Tasks", });
    internal_static_Proto_Task_TaskActiveRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Task_TaskActiveRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskActiveRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Type", "Id", });
    internal_static_Proto_Task_TaskActiveRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Task_TaskActiveRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskActiveRewardResponse_descriptor,
        new java.lang.String[] { "Code", "Type", "RewardLog", "CommonData", "Tasks", });
    internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Type", });
    internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor,
        new java.lang.String[] { "Code", "Type", "CommonData", "Tasks", "RewardLog", });
    internal_static_Proto_Task_Tasks_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Task_Tasks_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Task_Tasks_descriptor,
        new java.lang.String[] { "DailyTask", "Achievements", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

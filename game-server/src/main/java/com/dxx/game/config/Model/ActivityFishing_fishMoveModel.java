
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ActivityFishing_fishMoveModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityFishing_fishMove> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.ActivityFishing_fishMove> _dataList;
    
    public ActivityFishing_fishMoveModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityFishing_fishMove>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ActivityFishing_fishMove>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ActivityFishing_fishMove _v;
            _v = com.dxx.game.config.Bean.ActivityFishing_fishMove.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityFishing_fishMove> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.ActivityFishing_fishMove> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ActivityFishing_fishMove get(int key) { return _dataMap.get(key); }

}

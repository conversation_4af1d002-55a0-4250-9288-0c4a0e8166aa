package com.dxx.game.modules.activity.consts;

import com.google.common.collect.Maps;

import java.util.TreeMap;

/**
 * 潜水格子类型
 * <AUTHOR>
 * @date 2023/9/1 16:55
 */
public enum EDiveGrid {
    /** 通路 */
    ePass(-1, 0, true, false),
    /** 气泡 */
    ePop(1, 0, false, true),
    /** 冰块 */
    eIce(2, 0, false, false),
    /** 连续奖励 */
    eContinuousRewards(3, 4, false, false),
    /** 海藻 */
    eVarec(4, 5, true, false),
    /** 海藻中心 */
    eVarecCenter(5, 5, true, false),
    /** 水母泡泡 */
    eDiveItemPop(6, 1, false, true),
    /** 手电筒泡泡 */
    ePropAPop(7, 2, false, true),
    /** 炸弹泡泡 */
    ePropBPop(8, 3, false, true),
    /** 贝壳A泡泡 */
    eShellAPop(9, 7, false, true),
    /** 贝壳B泡泡 */
    eShellBPop(10, 8, false, true),
    /** 特殊奖励1泡泡 */
    eSPReward1Pop(11, 9, false, true),
    ;

    private int type;

    private int speicalId;

    private boolean pass;

    private boolean pop;

    private EDiveGrid(int type, int speicalId, boolean pass, boolean pop) {
        this.type = type;
        this.speicalId = speicalId;
        this.pass = pass;
        this.pop = pop;
    }

    public int getType() {
        return type;
    }

    public int getSpeicalId() {
        return speicalId;
    }

    public boolean isPass() {
        return pass;
    }

    public boolean isPop() {
        return pop;
    }

    private static TreeMap<Integer, EDiveGrid> typeMap = Maps.newTreeMap();
    static {
       for (EDiveGrid eDiveGrid : values()) {
           typeMap.put(eDiveGrid.type, eDiveGrid);
       }
    }

    public static EDiveGrid getEnumByType(int type) {
        return typeMap.get(type);
    }

}

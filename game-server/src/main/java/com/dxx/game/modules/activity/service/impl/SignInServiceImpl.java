package com.dxx.game.modules.activity.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.signin.SignInEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.SignIn;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityDao;
import com.dxx.game.modules.activity.service.SignInService;
import com.dxx.game.dto.SignInProto.*;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/14 11:01
 */
@Service
public class SignInServiceImpl implements SignInService {

    @Resource
    private ActivityDao activityDao;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;
    @Resource
    private ActivityBaseDao.SignInDao signInDao;

    @DynamoDBTransactional
    @Override
    public Result<SignInGetInfoResponse> getInfoAction(SignInGetInfoRequest params) {
        long userId = RequestContext.getUserId();

        SignInGetInfoResponse.Builder response = SignInGetInfoResponse.newBuilder();
        response.setSignInData(this.getSignData(userId));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<SignInDoSignResponse> doSignAction(SignInDoSignRequest params) {
        long userId = RequestContext.getUserId();

        SignIn sign = signInDao.getByUserId(userId);
        if (sign == null || sign.getSignIn() == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }
        SignIn.SignInModel signInModel = sign.getSignIn();

        long systemResetTimestamp = DateUtils.getSystemResetTime();
        if (signInModel.getTimestamp() == systemResetTimestamp) {
            return Result.Error(ErrorCode.ACTIVITY_SIGN_IN_IS_DONE);
        }

        int log = signInModel.getLog();
        List<List<Integer>> rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay1();
        switch (log) {
            case 1:
                rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay2();
                break;
            case 2:
                rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay3();
                break;
            case 3:
                rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay4();
                break;
            case 4:
                rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay5();
                break;
            case 5:
                rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay6();
                break;
            case 6:
                rewardsConfig = gameConfigManager.getSignInConfig().getSignInEntity(signInModel.getId()).getDay7();
                break;
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardsConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        signInModel.setLog(signInModel.getLog() + 1);
        signInModel.setSignDays(signInModel.getSignDays() + 1);
        signInModel.setTimestamp(systemResetTimestamp);
        signInDao.update(sign);

        SignInDoSignResponse.Builder response = SignInDoSignResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setSignInData(this.buildSignInData(signInModel, false));
        return Result.Success(response.build());
    }

    @Override
    public SignInData getSignData(long userId) {
        Activity activity = activityDao.getByUserId(userId);
        SignIn signIn = activity.getSignIn();
        if (signIn == null) {
            signIn = new SignIn();
            signIn.setUserId(userId);
            activity.setSignIn(signIn);
        }

        SignIn.SignInModel signInModel = signIn.getSignIn();

        boolean isCanSign = false;
        long systemResetTimestamp = DateUtils.getSystemResetTime();
        if (signInModel == null) {
            signInModel = new SignIn.SignInModel();
            int signDays = 1;
            signInModel.setSignDays(0);
            SignInEntity signInEntity = this.getSignInConfig(signDays);
            signInModel.setId(signInEntity.getID());
            signInModel.setLog(0);
            signInModel.setTimestamp(0);
            isCanSign = true;

            signIn.setSignIn(signInModel);
            activity.setSignIn(signIn);
            signInDao.update(signIn);
        } else {
            // 判断是否跨天
            if (systemResetTimestamp != signInModel.getTimestamp()) {
                isCanSign = true;

                if (signInModel.getLog() >= 7) {
                    // 新的一轮
                    signInModel.setLog(0);
                    SignInEntity signInEntity = this.getSignInConfig(signInModel.getSignDays());
                    signInModel.setId(signInEntity.getID());
                }
                signInDao.update(signIn);
            }
        }

        return this.buildSignInData(signInModel, isCanSign);
    }

    private SignInEntity getSignInConfig(int days) {
        days += 1;
        Map<Integer, SignInEntity> signInEntityMap = gameConfigManager.getSignInConfig().getSignIn();
        for (Map.Entry<Integer, SignInEntity> entry : signInEntityMap.entrySet()) {
            if (days >= entry.getValue().getMinDays() && days <= entry.getValue().getMaxDays()) {
                return entry.getValue();
            }
        }
        return signInEntityMap.get(1);
    }

    private SignInData buildSignInData(SignIn.SignInModel signInModel, boolean isCanSign) {

        SignInData.Builder builder = SignInData.newBuilder();
        builder.setIsCanSignIn(isCanSign);
        if (!isCanSign) {
            builder.setTimestamp(DateUtils.getSystemResetTime());
        }
        builder.setLog(signInModel.getLog());
        builder.addAllRewardDtoList(gameConfigManager.getSignInRewardsDto().get(signInModel.getId()));
        builder.setConfigId(signInModel.getId());
        return builder.build();
    }

}

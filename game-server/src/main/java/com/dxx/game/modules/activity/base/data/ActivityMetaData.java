package com.dxx.game.modules.activity.base.data;

import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.error.GameError;
import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.ActivityBase;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dto.ActivityProto;
import com.dxx.game.modules.activity.base.ActivityLifeCycle;
import com.google.protobuf.Message;
import lombok.Getter;
import org.springframework.data.util.ProxyUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * @author: lsc
 * @createDate: 2025/6/3
 * @description:
 */
@Getter
public class ActivityMetaData<M extends ActivityBase, D extends Message> {
    private int activityType; // 活动类型
    private ActivityLifeCycle<M, D> activityLifeCycle;

    private Class<M> modelClazz; // 数据类
    private Class<D> dtoClazz; // DTO类
    private Field modelField; // 数据字段
    private Method dtoSetMethod; // DTO的set方法
    private ActivityBaseDao<M> dao; // DAO层

    private ActivityMetaData() {}

    public static <M extends ActivityBase, D extends Message> ActivityMetaData<M, D> valueOf(int activityType) {
        ActivityMetaData<M, D> metaData = new ActivityMetaData<>();
        metaData.activityType = activityType;
        return metaData;
    }

    public ActivityMetaData<M, D> withDao(ActivityBaseDao<M> dao) {
        this.dao = dao;
        return this;
    }

    public M createNewModel() {
        try {
            return modelClazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new GameError("Failed to create new model: " + e.getMessage());
        }
    }

    public void updateModel(Activity activity, M model) {
        try {
            if (activity != null) {
                modelField.set(activity, model);
            }
            this.dao.update(model);
            DynamoDBCacheManager.put(model);
        } catch (IllegalAccessException e) {
            throw new GameError("Failed to assign model: " + e.getMessage());
        }
    }

    public void deleteModel(Activity activity, M model) {
        try {
            if (activity != null) {
                modelField.set(activity, null);
            }
            this.dao.delete(model);
        } catch (IllegalAccessException e) {
            throw new GameError("Failed to assign model: " + e.getMessage());
        }
    }

    public void addDtoToResponse(ActivityProto.ActivityGetListResponse.Builder response, D dto) {
        try {
            dtoSetMethod.invoke(response, dto);
        } catch (Exception e) {
            throw new GameError("Failed to add DTO to response: " + e.getMessage());
        }
    }

    @SuppressWarnings("unchecked")
    public M extractModel(Activity activity) {
        try {
            return (M) modelField.get(activity);
        } catch (IllegalAccessException e) {
            throw new GameError("Failed to extract model: " + e.getMessage());
        }
    }

    @SuppressWarnings("unchecked")
    public void initialize(ActivityLifeCycle<?, ?> lifeCycle) {
        if (this.activityLifeCycle != null) {
            return;
        }
        this.activityLifeCycle = (ActivityLifeCycle<M, D>) lifeCycle;
        Type superClass = ProxyUtils.getUserClass(this.activityLifeCycle.getClass()).getGenericInterfaces()[1];
        modelClazz = (Class<M>) ((ParameterizedType) superClass).getActualTypeArguments()[0];
        dtoClazz = (Class<D>) ((ParameterizedType) superClass).getActualTypeArguments()[1];


        this.parseActivityModelField();
        this.parseDtoSetMethod();
    }

    private void parseActivityModelField() {
        for (Field field : Activity.class.getDeclaredFields()) {
            if (field.getType() == modelClazz) {
                if (this.modelField != null) {
                    throw new GameError("Multiple fields of type " + modelClazz.getSimpleName() + " found in Activity.");
                }
                this.modelField = field;
            }
        }
        if (modelField == null) {
            throw new GameError("Field of type " + modelClazz.getSimpleName() + " not found in Activity.");
        }
        modelField.setAccessible(true);
    }

    private void parseDtoSetMethod() {
        for (Method method : ActivityProto.ActivityGetListResponse.Builder.class.getMethods()) {
            if (method.getName().startsWith("set") && method.getParameterCount() == 1
                    && method.getParameterTypes()[0] == dtoClazz) {
                if (this.dtoSetMethod != null) {
                    throw new GameError("Multiple set methods for DTO class " + dtoClazz.getSimpleName() + " found.");
                }
                this.dtoSetMethod = method;
            }
        }
        if (dtoSetMethod == null) {
            throw new GameError("Set method for DTO class " + dtoClazz.getSimpleName() + " not found.");
        }
        dtoSetMethod.setAccessible(true);
    }
}


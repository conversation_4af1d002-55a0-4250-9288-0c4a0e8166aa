package com.dxx.game.consts;

import com.google.common.primitives.Ints;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum FunctionTypeEnum {

    FUNCTION_MAZE(17, 0,"迷宫"),
    FUNCTION_EXPEDITION(19, 0,"远征"),
    FUNCTION_CHAT(40, 0,"聊天"),

    FUNCTION_82(82, 10, "战役-战斗-4倍速"),
    FUNCTION_83(83, 14, "战役-战斗-跳过"),
    FUNCTION_85(85, 11, "挑战之塔-战斗-4倍速"),
    FUNCTION_86(86, 13, "挑战之塔-战斗-跳过"),
    FUNCTION_88(88, 12, "竞技场-战斗-4倍速"),
    FUNCTION_89(89, 8,  "竞技场-战斗-跳过"),

    UNCTION_ACTIVE(1001, 0,"活动"),
    FUNCTION_ARENA(1002, 0, "竞技场"),
    FUNCTION_TOWER(1003, 0,"爬塔"),
    FUNCTION_GUILD(1004, 0,"工会");


    private final int functionKey;
    private final int vipPower;
    private final String desc;

    private static Map<Integer, FunctionTypeEnum> values = new HashMap<>();

    static {
        FunctionTypeEnum[] values1 = values();
        for (FunctionTypeEnum type : values1) {
            values.put(type.getFunctionKey(), type);
        }
    }

    public static FunctionTypeEnum getEnumByKey(int type) {
        return values.get(type);
    }

    FunctionTypeEnum(int functionKey, int vipPower, String desc) {
        this.functionKey = functionKey;
        this.vipPower = vipPower;
        this.desc = desc;
    }

    public int parseConfigArgs(String configArgs) {
        return Ints.tryParse(configArgs);
    }



}

package com.dxx.game.modules.activity.base;

import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.ActivityBase;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityDao;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.google.protobuf.Message;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: lsc
 * @createDate: 2025/6/3
 * @description:
 */
@Slf4j
@Component
public class ActivityLifeCycleSupport {

    @Resource
    private ActivityDao activityDao;
    @Resource
    private ActivityRegistry activityRegistry;

    @Getter
    private static ActivityLifeCycleSupport instance;

    @PostConstruct
    private void init() {
        instance = this;
    }

    public <M extends ActivityBase, D extends Message> M getModel(long userId, Class<? extends ActivityLifeCycle<M, D>> clazz, boolean fromActivity) {
        M model = null;
        ActivityMetaData<M, D> metaData = activityRegistry.getActivityMetaData(clazz);

        Activity activity = null;
        if (fromActivity) {
            activity = activityDao.getByUserId(userId);
            model = metaData.extractModel(activity);
        } else {
            model = metaData.getDao().getByUserId(userId);
        }

        model = ActivityManager.getInstance().processActivity(userId, activity, model, metaData);

        return model;
    }

    public <M extends ActivityBase, D extends Message> void updateModel(M model, Class<? extends ActivityLifeCycle<M, D>> clazz) {
        ActivityMetaData<M, ?> metaData = activityRegistry.getActivityMetaData(clazz);
        metaData.getDao().updateIgnoreNulls(model);
    }
}


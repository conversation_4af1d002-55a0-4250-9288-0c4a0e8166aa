
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Activity_Shop {
    public Activity_Shop(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        NameId = _buf.get("NameId").getAsString();
        GroupType = _buf.get("GroupType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Cost").getAsJsonArray(); Cost = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Cost.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        MaxCount = _buf.get("MaxCount").getAsInt();
        type = _buf.get("type").getAsInt();
        IAPId = _buf.get("IAPId").getAsInt();
        style = _buf.get("style").getAsInt();
    }

    public static Activity_Shop deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Activity_Shop(_buf);
    }

    /**
     * 商店id
     */
    public final int Id;
    /**
     * 条目名称
     */
    public final String NameId;
    /**
     * 活动组类型
     */
    public final int GroupType;
    /**
     * 兑换消耗
     */
    public final java.util.List<String> Cost;
    /**
     * 兑换奖励
     */
    public final java.util.List<String> Reward;
    /**
     * 兑换次数上限
     */
    public final int MaxCount;
    /**
     * 类型（1兑换2付费购买）
     */
    public final int type;
    /**
     * IAPid
     */
    public final int IAPId;
    /**
     * 展示类型（1按钮显示货币2条目多兑一）
     */
    public final int style;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "NameId:" + NameId + ","
        + "GroupType:" + GroupType + ","
        + "Cost:" + Cost + ","
        + "Reward:" + Reward + ","
        + "MaxCount:" + MaxCount + ","
        + "type:" + type + ","
        + "IAPId:" + IAPId + ","
        + "style:" + style + ","
        + "}";
    }
}


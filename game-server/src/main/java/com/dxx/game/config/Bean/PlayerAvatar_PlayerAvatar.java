
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class PlayerAvatar_PlayerAvatar {
    public PlayerAvatar_PlayerAvatar(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        notes = _buf.get("notes").getAsString();
        path = _buf.get("path").getAsString();
    }

    public static PlayerAvatar_PlayerAvatar deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.PlayerAvatar_PlayerAvatar(_buf);
    }

    /**
     * 内容ID
     */
    public final int id;
    /**
     * 注释
     */
    public final String notes;
    /**
     * 切片图片路径
     */
    public final String path;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "notes:" + notes + ","
        + "path:" + path + ","
        + "}";
    }
}


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sevenday.proto

package com.dxx.game.dto;

public final class SevenDayTaskProto {
  private SevenDayTaskProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SevenDayTaskGetInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayTaskGetInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=11301 新手7日任务活动-获取数据
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskGetInfoRequest}
   */
  public static final class SevenDayTaskGetInfoRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayTaskGetInfoRequest)
      SevenDayTaskGetInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayTaskGetInfoRequest.newBuilder() to construct.
    private SevenDayTaskGetInfoRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayTaskGetInfoRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayTaskGetInfoRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayTaskGetInfoRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11301 新手7日任务活动-获取数据
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskGetInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayTaskGetInfoRequest)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayTaskGetInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayTaskGetInfoRequest)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayTaskGetInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayTaskGetInfoRequest>() {
      @java.lang.Override
      public SevenDayTaskGetInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayTaskGetInfoRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayTaskGetInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayTaskGetInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SevenDayTaskGetInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayTaskGetInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
     * @return Whether the sevenDayDto field is set.
     */
    boolean hasSevenDayDto();
    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
     * @return The sevenDayDto.
     */
    com.dxx.game.dto.SevenDayTaskProto.SevenDayDto getSevenDayDto();
    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
     */
    com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder getSevenDayDtoOrBuilder();

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=11302
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskGetInfoResponse}
   */
  public static final class SevenDayTaskGetInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayTaskGetInfoResponse)
      SevenDayTaskGetInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayTaskGetInfoResponse.newBuilder() to construct.
    private SevenDayTaskGetInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayTaskGetInfoResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayTaskGetInfoResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayTaskGetInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder subBuilder = null;
              if (sevenDayDto_ != null) {
                subBuilder = sevenDayDto_.toBuilder();
              }
              sevenDayDto_ = input.readMessage(com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sevenDayDto_);
                sevenDayDto_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SEVENDAYDTO_FIELD_NUMBER = 2;
    private com.dxx.game.dto.SevenDayTaskProto.SevenDayDto sevenDayDto_;
    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
     * @return Whether the sevenDayDto field is set.
     */
    @java.lang.Override
    public boolean hasSevenDayDto() {
      return sevenDayDto_ != null;
    }
    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
     * @return The sevenDayDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto getSevenDayDto() {
      return sevenDayDto_ == null ? com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.getDefaultInstance() : sevenDayDto_;
    }
    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder getSevenDayDtoOrBuilder() {
      return getSevenDayDto();
    }

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (sevenDayDto_ != null) {
        output.writeMessage(2, getSevenDayDto());
      }
      if (commonData_ != null) {
        output.writeMessage(3, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (sevenDayDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSevenDayDto());
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasSevenDayDto() != other.hasSevenDayDto()) return false;
      if (hasSevenDayDto()) {
        if (!getSevenDayDto()
            .equals(other.getSevenDayDto())) return false;
      }
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasSevenDayDto()) {
        hash = (37 * hash) + SEVENDAYDTO_FIELD_NUMBER;
        hash = (53 * hash) + getSevenDayDto().hashCode();
      }
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11302
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskGetInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayTaskGetInfoResponse)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (sevenDayDtoBuilder_ == null) {
          sevenDayDto_ = null;
        } else {
          sevenDayDto_ = null;
          sevenDayDtoBuilder_ = null;
        }
        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse(this);
        result.code_ = code_;
        if (sevenDayDtoBuilder_ == null) {
          result.sevenDayDto_ = sevenDayDto_;
        } else {
          result.sevenDayDto_ = sevenDayDtoBuilder_.build();
        }
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasSevenDayDto()) {
          mergeSevenDayDto(other.getSevenDayDto());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.SevenDayTaskProto.SevenDayDto sevenDayDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.SevenDayTaskProto.SevenDayDto, com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder, com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder> sevenDayDtoBuilder_;
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       * @return Whether the sevenDayDto field is set.
       */
      public boolean hasSevenDayDto() {
        return sevenDayDtoBuilder_ != null || sevenDayDto_ != null;
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       * @return The sevenDayDto.
       */
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto getSevenDayDto() {
        if (sevenDayDtoBuilder_ == null) {
          return sevenDayDto_ == null ? com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.getDefaultInstance() : sevenDayDto_;
        } else {
          return sevenDayDtoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      public Builder setSevenDayDto(com.dxx.game.dto.SevenDayTaskProto.SevenDayDto value) {
        if (sevenDayDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sevenDayDto_ = value;
          onChanged();
        } else {
          sevenDayDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      public Builder setSevenDayDto(
          com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder builderForValue) {
        if (sevenDayDtoBuilder_ == null) {
          sevenDayDto_ = builderForValue.build();
          onChanged();
        } else {
          sevenDayDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      public Builder mergeSevenDayDto(com.dxx.game.dto.SevenDayTaskProto.SevenDayDto value) {
        if (sevenDayDtoBuilder_ == null) {
          if (sevenDayDto_ != null) {
            sevenDayDto_ =
              com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.newBuilder(sevenDayDto_).mergeFrom(value).buildPartial();
          } else {
            sevenDayDto_ = value;
          }
          onChanged();
        } else {
          sevenDayDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      public Builder clearSevenDayDto() {
        if (sevenDayDtoBuilder_ == null) {
          sevenDayDto_ = null;
          onChanged();
        } else {
          sevenDayDto_ = null;
          sevenDayDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder getSevenDayDtoBuilder() {
        
        onChanged();
        return getSevenDayDtoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder getSevenDayDtoOrBuilder() {
        if (sevenDayDtoBuilder_ != null) {
          return sevenDayDtoBuilder_.getMessageOrBuilder();
        } else {
          return sevenDayDto_ == null ?
              com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.getDefaultInstance() : sevenDayDto_;
        }
      }
      /**
       * <pre>
       * 活动数据
       * </pre>
       *
       * <code>.Proto.SevenDayTask.SevenDayDto sevenDayDto = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.SevenDayTaskProto.SevenDayDto, com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder, com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder> 
          getSevenDayDtoFieldBuilder() {
        if (sevenDayDtoBuilder_ == null) {
          sevenDayDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.SevenDayTaskProto.SevenDayDto, com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder, com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder>(
                  getSevenDayDto(),
                  getParentForChildren(),
                  isClean());
          sevenDayDto_ = null;
        }
        return sevenDayDtoBuilder_;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayTaskGetInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayTaskGetInfoResponse)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayTaskGetInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayTaskGetInfoResponse>() {
      @java.lang.Override
      public SevenDayTaskGetInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayTaskGetInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayTaskGetInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayTaskGetInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SevenDayTaskRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayTaskRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 任务ID
     * </pre>
     *
     * <code>uint32 taskId = 2;</code>
     * @return The taskId.
     */
    int getTaskId();
  }
  /**
   * <pre>
   *CMD PackageId=11303 新手7日任务活动-领取任务奖励
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskRewardRequest}
   */
  public static final class SevenDayTaskRewardRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayTaskRewardRequest)
      SevenDayTaskRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayTaskRewardRequest.newBuilder() to construct.
    private SevenDayTaskRewardRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayTaskRewardRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayTaskRewardRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayTaskRewardRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              taskId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int TASKID_FIELD_NUMBER = 2;
    private int taskId_;
    /**
     * <pre>
     * 任务ID
     * </pre>
     *
     * <code>uint32 taskId = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public int getTaskId() {
      return taskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (taskId_ != 0) {
        output.writeUInt32(2, taskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (taskId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, taskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getTaskId()
          != other.getTaskId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + TASKID_FIELD_NUMBER;
      hash = (53 * hash) + getTaskId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11303 新手7日任务活动-领取任务奖励
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayTaskRewardRequest)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        taskId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.taskId_ = taskId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getTaskId() != 0) {
          setTaskId(other.getTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int taskId_ ;
      /**
       * <pre>
       * 任务ID
       * </pre>
       *
       * <code>uint32 taskId = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public int getTaskId() {
        return taskId_;
      }
      /**
       * <pre>
       * 任务ID
       * </pre>
       *
       * <code>uint32 taskId = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(int value) {
        
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务ID
       * </pre>
       *
       * <code>uint32 taskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        
        taskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayTaskRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayTaskRewardRequest)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayTaskRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayTaskRewardRequest>() {
      @java.lang.Override
      public SevenDayTaskRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayTaskRewardRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayTaskRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayTaskRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SevenDayTaskRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayTaskRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 更新的活跃度
     * </pre>
     *
     * <code>uint32 active = 3;</code>
     * @return The active.
     */
    int getActive();

    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> 
        getUpdateTaskDtoList();
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    com.dxx.game.dto.CommonProto.SevenDayTaskDto getUpdateTaskDto(int index);
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    int getUpdateTaskDtoCount();
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
        getUpdateTaskDtoOrBuilderList();
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getUpdateTaskDtoOrBuilder(
        int index);
  }
  /**
   * <pre>
   *CMD PackageId=11304
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskRewardResponse}
   */
  public static final class SevenDayTaskRewardResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayTaskRewardResponse)
      SevenDayTaskRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayTaskRewardResponse.newBuilder() to construct.
    private SevenDayTaskRewardResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayTaskRewardResponse() {
      updateTaskDto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayTaskRewardResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayTaskRewardResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              active_ = input.readUInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                updateTaskDto_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.SevenDayTaskDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              updateTaskDto_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.SevenDayTaskDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          updateTaskDto_ = java.util.Collections.unmodifiableList(updateTaskDto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int ACTIVE_FIELD_NUMBER = 3;
    private int active_;
    /**
     * <pre>
     * 更新的活跃度
     * </pre>
     *
     * <code>uint32 active = 3;</code>
     * @return The active.
     */
    @java.lang.Override
    public int getActive() {
      return active_;
    }

    public static final int UPDATETASKDTO_FIELD_NUMBER = 4;
    private java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> updateTaskDto_;
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> getUpdateTaskDtoList() {
      return updateTaskDto_;
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
        getUpdateTaskDtoOrBuilderList() {
      return updateTaskDto_;
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public int getUpdateTaskDtoCount() {
      return updateTaskDto_.size();
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.SevenDayTaskDto getUpdateTaskDto(int index) {
      return updateTaskDto_.get(index);
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getUpdateTaskDtoOrBuilder(
        int index) {
      return updateTaskDto_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (active_ != 0) {
        output.writeUInt32(3, active_);
      }
      for (int i = 0; i < updateTaskDto_.size(); i++) {
        output.writeMessage(4, updateTaskDto_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (active_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, active_);
      }
      for (int i = 0; i < updateTaskDto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, updateTaskDto_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getActive()
          != other.getActive()) return false;
      if (!getUpdateTaskDtoList()
          .equals(other.getUpdateTaskDtoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + ACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getActive();
      if (getUpdateTaskDtoCount() > 0) {
        hash = (37 * hash) + UPDATETASKDTO_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateTaskDtoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11304
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayTaskRewardResponse)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getUpdateTaskDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        active_ = 0;

        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          updateTaskDtoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.active_ = active_;
        if (updateTaskDtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            updateTaskDto_ = java.util.Collections.unmodifiableList(updateTaskDto_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.updateTaskDto_ = updateTaskDto_;
        } else {
          result.updateTaskDto_ = updateTaskDtoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getActive() != 0) {
          setActive(other.getActive());
        }
        if (updateTaskDtoBuilder_ == null) {
          if (!other.updateTaskDto_.isEmpty()) {
            if (updateTaskDto_.isEmpty()) {
              updateTaskDto_ = other.updateTaskDto_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureUpdateTaskDtoIsMutable();
              updateTaskDto_.addAll(other.updateTaskDto_);
            }
            onChanged();
          }
        } else {
          if (!other.updateTaskDto_.isEmpty()) {
            if (updateTaskDtoBuilder_.isEmpty()) {
              updateTaskDtoBuilder_.dispose();
              updateTaskDtoBuilder_ = null;
              updateTaskDto_ = other.updateTaskDto_;
              bitField0_ = (bitField0_ & ~0x00000001);
              updateTaskDtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUpdateTaskDtoFieldBuilder() : null;
            } else {
              updateTaskDtoBuilder_.addAllMessages(other.updateTaskDto_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int active_ ;
      /**
       * <pre>
       * 更新的活跃度
       * </pre>
       *
       * <code>uint32 active = 3;</code>
       * @return The active.
       */
      @java.lang.Override
      public int getActive() {
        return active_;
      }
      /**
       * <pre>
       * 更新的活跃度
       * </pre>
       *
       * <code>uint32 active = 3;</code>
       * @param value The active to set.
       * @return This builder for chaining.
       */
      public Builder setActive(int value) {
        
        active_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 更新的活跃度
       * </pre>
       *
       * <code>uint32 active = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActive() {
        
        active_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> updateTaskDto_ =
        java.util.Collections.emptyList();
      private void ensureUpdateTaskDtoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          updateTaskDto_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.SevenDayTaskDto>(updateTaskDto_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> updateTaskDtoBuilder_;

      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> getUpdateTaskDtoList() {
        if (updateTaskDtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(updateTaskDto_);
        } else {
          return updateTaskDtoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public int getUpdateTaskDtoCount() {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_.size();
        } else {
          return updateTaskDtoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto getUpdateTaskDto(int index) {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_.get(index);
        } else {
          return updateTaskDtoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder setUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.set(index, value);
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder setUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.set(index, builderForValue.build());
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(value);
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(index, value);
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(
          com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(builderForValue.build());
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(index, builderForValue.build());
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addAllUpdateTaskDto(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDto> values) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, updateTaskDto_);
          onChanged();
        } else {
          updateTaskDtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder clearUpdateTaskDto() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          updateTaskDtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder removeUpdateTaskDto(int index) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.remove(index);
          onChanged();
        } else {
          updateTaskDtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder getUpdateTaskDtoBuilder(
          int index) {
        return getUpdateTaskDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getUpdateTaskDtoOrBuilder(
          int index) {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_.get(index);  } else {
          return updateTaskDtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
           getUpdateTaskDtoOrBuilderList() {
        if (updateTaskDtoBuilder_ != null) {
          return updateTaskDtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(updateTaskDto_);
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder addUpdateTaskDtoBuilder() {
        return getUpdateTaskDtoFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.SevenDayTaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder addUpdateTaskDtoBuilder(
          int index) {
        return getUpdateTaskDtoFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder> 
           getUpdateTaskDtoBuilderList() {
        return getUpdateTaskDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
          getUpdateTaskDtoFieldBuilder() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder>(
                  updateTaskDto_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          updateTaskDto_ = null;
        }
        return updateTaskDtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayTaskRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayTaskRewardResponse)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayTaskRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayTaskRewardResponse>() {
      @java.lang.Override
      public SevenDayTaskRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayTaskRewardResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayTaskRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayTaskRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SevenDayTaskActiveRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayTaskActiveRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 配置表ID
     * </pre>
     *
     * <code>uint32 configId = 2;</code>
     * @return The configId.
     */
    int getConfigId();

    /**
     * <pre>
     * 自选礼包选择的道具索引
     * </pre>
     *
     * <code>uint32 selectIdx = 3;</code>
     * @return The selectIdx.
     */
    int getSelectIdx();
  }
  /**
   * <pre>
   *CMD PackageId=11305 新手7日任务活动-领取活跃度奖励
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskActiveRewardRequest}
   */
  public static final class SevenDayTaskActiveRewardRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayTaskActiveRewardRequest)
      SevenDayTaskActiveRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayTaskActiveRewardRequest.newBuilder() to construct.
    private SevenDayTaskActiveRewardRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayTaskActiveRewardRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayTaskActiveRewardRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayTaskActiveRewardRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              configId_ = input.readUInt32();
              break;
            }
            case 24: {

              selectIdx_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int CONFIGID_FIELD_NUMBER = 2;
    private int configId_;
    /**
     * <pre>
     * 配置表ID
     * </pre>
     *
     * <code>uint32 configId = 2;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    public static final int SELECTIDX_FIELD_NUMBER = 3;
    private int selectIdx_;
    /**
     * <pre>
     * 自选礼包选择的道具索引
     * </pre>
     *
     * <code>uint32 selectIdx = 3;</code>
     * @return The selectIdx.
     */
    @java.lang.Override
    public int getSelectIdx() {
      return selectIdx_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (configId_ != 0) {
        output.writeUInt32(2, configId_);
      }
      if (selectIdx_ != 0) {
        output.writeUInt32(3, selectIdx_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, configId_);
      }
      if (selectIdx_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, selectIdx_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getConfigId()
          != other.getConfigId()) return false;
      if (getSelectIdx()
          != other.getSelectIdx()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (37 * hash) + SELECTIDX_FIELD_NUMBER;
      hash = (53 * hash) + getSelectIdx();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11305 新手7日任务活动-领取活跃度奖励
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskActiveRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayTaskActiveRewardRequest)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        configId_ = 0;

        selectIdx_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.configId_ = configId_;
        result.selectIdx_ = selectIdx_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        if (other.getSelectIdx() != 0) {
          setSelectIdx(other.getSelectIdx());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int configId_ ;
      /**
       * <pre>
       * 配置表ID
       * </pre>
       *
       * <code>uint32 configId = 2;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       * 配置表ID
       * </pre>
       *
       * <code>uint32 configId = 2;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置表ID
       * </pre>
       *
       * <code>uint32 configId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }

      private int selectIdx_ ;
      /**
       * <pre>
       * 自选礼包选择的道具索引
       * </pre>
       *
       * <code>uint32 selectIdx = 3;</code>
       * @return The selectIdx.
       */
      @java.lang.Override
      public int getSelectIdx() {
        return selectIdx_;
      }
      /**
       * <pre>
       * 自选礼包选择的道具索引
       * </pre>
       *
       * <code>uint32 selectIdx = 3;</code>
       * @param value The selectIdx to set.
       * @return This builder for chaining.
       */
      public Builder setSelectIdx(int value) {
        
        selectIdx_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 自选礼包选择的道具索引
       * </pre>
       *
       * <code>uint32 selectIdx = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSelectIdx() {
        
        selectIdx_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayTaskActiveRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayTaskActiveRewardRequest)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayTaskActiveRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayTaskActiveRewardRequest>() {
      @java.lang.Override
      public SevenDayTaskActiveRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayTaskActiveRewardRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayTaskActiveRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayTaskActiveRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SevenDayTaskActiveRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayTaskActiveRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 activeLog = 3;</code>
     * @return The activeLog.
     */
    long getActiveLog();

    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> 
        getUpdateTaskDtoList();
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    com.dxx.game.dto.CommonProto.SevenDayTaskDto getUpdateTaskDto(int index);
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    int getUpdateTaskDtoCount();
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
        getUpdateTaskDtoOrBuilderList();
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getUpdateTaskDtoOrBuilder(
        int index);
  }
  /**
   * <pre>
   *CMD PackageId=11306
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskActiveRewardResponse}
   */
  public static final class SevenDayTaskActiveRewardResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayTaskActiveRewardResponse)
      SevenDayTaskActiveRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayTaskActiveRewardResponse.newBuilder() to construct.
    private SevenDayTaskActiveRewardResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayTaskActiveRewardResponse() {
      updateTaskDto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayTaskActiveRewardResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayTaskActiveRewardResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              activeLog_ = input.readUInt64();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                updateTaskDto_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.SevenDayTaskDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              updateTaskDto_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.SevenDayTaskDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          updateTaskDto_ = java.util.Collections.unmodifiableList(updateTaskDto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int ACTIVELOG_FIELD_NUMBER = 3;
    private long activeLog_;
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 activeLog = 3;</code>
     * @return The activeLog.
     */
    @java.lang.Override
    public long getActiveLog() {
      return activeLog_;
    }

    public static final int UPDATETASKDTO_FIELD_NUMBER = 4;
    private java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> updateTaskDto_;
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> getUpdateTaskDtoList() {
      return updateTaskDto_;
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
        getUpdateTaskDtoOrBuilderList() {
      return updateTaskDto_;
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public int getUpdateTaskDtoCount() {
      return updateTaskDto_.size();
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.SevenDayTaskDto getUpdateTaskDto(int index) {
      return updateTaskDto_.get(index);
    }
    /**
     * <pre>
     * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getUpdateTaskDtoOrBuilder(
        int index) {
      return updateTaskDto_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (activeLog_ != 0L) {
        output.writeUInt64(3, activeLog_);
      }
      for (int i = 0; i < updateTaskDto_.size(); i++) {
        output.writeMessage(4, updateTaskDto_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (activeLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, activeLog_);
      }
      for (int i = 0; i < updateTaskDto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, updateTaskDto_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getActiveLog()
          != other.getActiveLog()) return false;
      if (!getUpdateTaskDtoList()
          .equals(other.getUpdateTaskDtoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + ACTIVELOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActiveLog());
      if (getUpdateTaskDtoCount() > 0) {
        hash = (37 * hash) + UPDATETASKDTO_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateTaskDtoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=11306
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayTaskActiveRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayTaskActiveRewardResponse)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getUpdateTaskDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        activeLog_ = 0L;

        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          updateTaskDtoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.activeLog_ = activeLog_;
        if (updateTaskDtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            updateTaskDto_ = java.util.Collections.unmodifiableList(updateTaskDto_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.updateTaskDto_ = updateTaskDto_;
        } else {
          result.updateTaskDto_ = updateTaskDtoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getActiveLog() != 0L) {
          setActiveLog(other.getActiveLog());
        }
        if (updateTaskDtoBuilder_ == null) {
          if (!other.updateTaskDto_.isEmpty()) {
            if (updateTaskDto_.isEmpty()) {
              updateTaskDto_ = other.updateTaskDto_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureUpdateTaskDtoIsMutable();
              updateTaskDto_.addAll(other.updateTaskDto_);
            }
            onChanged();
          }
        } else {
          if (!other.updateTaskDto_.isEmpty()) {
            if (updateTaskDtoBuilder_.isEmpty()) {
              updateTaskDtoBuilder_.dispose();
              updateTaskDtoBuilder_ = null;
              updateTaskDto_ = other.updateTaskDto_;
              bitField0_ = (bitField0_ & ~0x00000001);
              updateTaskDtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUpdateTaskDtoFieldBuilder() : null;
            } else {
              updateTaskDtoBuilder_.addAllMessages(other.updateTaskDto_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long activeLog_ ;
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 activeLog = 3;</code>
       * @return The activeLog.
       */
      @java.lang.Override
      public long getActiveLog() {
        return activeLog_;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 activeLog = 3;</code>
       * @param value The activeLog to set.
       * @return This builder for chaining.
       */
      public Builder setActiveLog(long value) {
        
        activeLog_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 activeLog = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveLog() {
        
        activeLog_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> updateTaskDto_ =
        java.util.Collections.emptyList();
      private void ensureUpdateTaskDtoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          updateTaskDto_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.SevenDayTaskDto>(updateTaskDto_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> updateTaskDtoBuilder_;

      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> getUpdateTaskDtoList() {
        if (updateTaskDtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(updateTaskDto_);
        } else {
          return updateTaskDtoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public int getUpdateTaskDtoCount() {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_.size();
        } else {
          return updateTaskDtoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto getUpdateTaskDto(int index) {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_.get(index);
        } else {
          return updateTaskDtoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder setUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.set(index, value);
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder setUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.set(index, builderForValue.build());
          onChanged();
        } else {
          updateTaskDtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(value);
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (updateTaskDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(index, value);
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(
          com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(builderForValue.build());
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addUpdateTaskDto(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.add(index, builderForValue.build());
          onChanged();
        } else {
          updateTaskDtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder addAllUpdateTaskDto(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDto> values) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, updateTaskDto_);
          onChanged();
        } else {
          updateTaskDtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder clearUpdateTaskDto() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          updateTaskDtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public Builder removeUpdateTaskDto(int index) {
        if (updateTaskDtoBuilder_ == null) {
          ensureUpdateTaskDtoIsMutable();
          updateTaskDto_.remove(index);
          onChanged();
        } else {
          updateTaskDtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder getUpdateTaskDtoBuilder(
          int index) {
        return getUpdateTaskDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getUpdateTaskDtoOrBuilder(
          int index) {
        if (updateTaskDtoBuilder_ == null) {
          return updateTaskDto_.get(index);  } else {
          return updateTaskDtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
           getUpdateTaskDtoOrBuilderList() {
        if (updateTaskDtoBuilder_ != null) {
          return updateTaskDtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(updateTaskDto_);
        }
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder addUpdateTaskDtoBuilder() {
        return getUpdateTaskDtoFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.SevenDayTaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder addUpdateTaskDtoBuilder(
          int index) {
        return getUpdateTaskDtoFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 更新的活动数据, 比如领取任务奖励会触发其他任务状态
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto updateTaskDto = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder> 
           getUpdateTaskDtoBuilderList() {
        return getUpdateTaskDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
          getUpdateTaskDtoFieldBuilder() {
        if (updateTaskDtoBuilder_ == null) {
          updateTaskDtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder>(
                  updateTaskDto_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          updateTaskDto_ = null;
        }
        return updateTaskDtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayTaskActiveRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayTaskActiveRewardResponse)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayTaskActiveRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayTaskActiveRewardResponse>() {
      @java.lang.Override
      public SevenDayTaskActiveRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayTaskActiveRewardResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayTaskActiveRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayTaskActiveRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SevenDayDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SevenDayTask.SevenDayDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 开启了第几天的任务
     * </pre>
     *
     * <code>uint32 days = 1;</code>
     * @return The days.
     */
    int getDays();

    /**
     * <pre>
     * 任务结束时间
     * </pre>
     *
     * <code>uint64 taskEndTimestamp = 2;</code>
     * @return The taskEndTimestamp.
     */
    long getTaskEndTimestamp();

    /**
     * <pre>
     * 活动结束时间(任务+领取奖励)
     * </pre>
     *
     * <code>uint64 endTimestamp = 3;</code>
     * @return The endTimestamp.
     */
    long getEndTimestamp();

    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 activeLog = 4;</code>
     * @return The activeLog.
     */
    long getActiveLog();

    /**
     * <pre>
     * 当前活跃度
     * </pre>
     *
     * <code>uint32 active = 5;</code>
     * @return The active.
     */
    int getActive();

    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> 
        getTasksList();
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    com.dxx.game.dto.CommonProto.SevenDayTaskDto getTasks(int index);
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    int getTasksCount();
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
        getTasksOrBuilderList();
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getTasksOrBuilder(
        int index);
  }
  /**
   * <pre>
   * 活动数据
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDayTask.SevenDayDto}
   */
  public static final class SevenDayDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.SevenDayTask.SevenDayDto)
      SevenDayDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SevenDayDto.newBuilder() to construct.
    private SevenDayDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SevenDayDto() {
      tasks_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SevenDayDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SevenDayDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              days_ = input.readUInt32();
              break;
            }
            case 16: {

              taskEndTimestamp_ = input.readUInt64();
              break;
            }
            case 24: {

              endTimestamp_ = input.readUInt64();
              break;
            }
            case 32: {

              activeLog_ = input.readUInt64();
              break;
            }
            case 40: {

              active_ = input.readUInt32();
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                tasks_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.SevenDayTaskDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              tasks_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.SevenDayTaskDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          tasks_ = java.util.Collections.unmodifiableList(tasks_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder.class);
    }

    public static final int DAYS_FIELD_NUMBER = 1;
    private int days_;
    /**
     * <pre>
     * 开启了第几天的任务
     * </pre>
     *
     * <code>uint32 days = 1;</code>
     * @return The days.
     */
    @java.lang.Override
    public int getDays() {
      return days_;
    }

    public static final int TASKENDTIMESTAMP_FIELD_NUMBER = 2;
    private long taskEndTimestamp_;
    /**
     * <pre>
     * 任务结束时间
     * </pre>
     *
     * <code>uint64 taskEndTimestamp = 2;</code>
     * @return The taskEndTimestamp.
     */
    @java.lang.Override
    public long getTaskEndTimestamp() {
      return taskEndTimestamp_;
    }

    public static final int ENDTIMESTAMP_FIELD_NUMBER = 3;
    private long endTimestamp_;
    /**
     * <pre>
     * 活动结束时间(任务+领取奖励)
     * </pre>
     *
     * <code>uint64 endTimestamp = 3;</code>
     * @return The endTimestamp.
     */
    @java.lang.Override
    public long getEndTimestamp() {
      return endTimestamp_;
    }

    public static final int ACTIVELOG_FIELD_NUMBER = 4;
    private long activeLog_;
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>uint64 activeLog = 4;</code>
     * @return The activeLog.
     */
    @java.lang.Override
    public long getActiveLog() {
      return activeLog_;
    }

    public static final int ACTIVE_FIELD_NUMBER = 5;
    private int active_;
    /**
     * <pre>
     * 当前活跃度
     * </pre>
     *
     * <code>uint32 active = 5;</code>
     * @return The active.
     */
    @java.lang.Override
    public int getActive() {
      return active_;
    }

    public static final int TASKS_FIELD_NUMBER = 6;
    private java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> tasks_;
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> getTasksList() {
      return tasks_;
    }
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
        getTasksOrBuilderList() {
      return tasks_;
    }
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    @java.lang.Override
    public int getTasksCount() {
      return tasks_.size();
    }
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.SevenDayTaskDto getTasks(int index) {
      return tasks_.get(index);
    }
    /**
     * <pre>
     * 任务数据
     * </pre>
     *
     * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getTasksOrBuilder(
        int index) {
      return tasks_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (days_ != 0) {
        output.writeUInt32(1, days_);
      }
      if (taskEndTimestamp_ != 0L) {
        output.writeUInt64(2, taskEndTimestamp_);
      }
      if (endTimestamp_ != 0L) {
        output.writeUInt64(3, endTimestamp_);
      }
      if (activeLog_ != 0L) {
        output.writeUInt64(4, activeLog_);
      }
      if (active_ != 0) {
        output.writeUInt32(5, active_);
      }
      for (int i = 0; i < tasks_.size(); i++) {
        output.writeMessage(6, tasks_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (days_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, days_);
      }
      if (taskEndTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, taskEndTimestamp_);
      }
      if (endTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, endTimestamp_);
      }
      if (activeLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, activeLog_);
      }
      if (active_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, active_);
      }
      for (int i = 0; i < tasks_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, tasks_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SevenDayTaskProto.SevenDayDto other = (com.dxx.game.dto.SevenDayTaskProto.SevenDayDto) obj;

      if (getDays()
          != other.getDays()) return false;
      if (getTaskEndTimestamp()
          != other.getTaskEndTimestamp()) return false;
      if (getEndTimestamp()
          != other.getEndTimestamp()) return false;
      if (getActiveLog()
          != other.getActiveLog()) return false;
      if (getActive()
          != other.getActive()) return false;
      if (!getTasksList()
          .equals(other.getTasksList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DAYS_FIELD_NUMBER;
      hash = (53 * hash) + getDays();
      hash = (37 * hash) + TASKENDTIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTaskEndTimestamp());
      hash = (37 * hash) + ENDTIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEndTimestamp());
      hash = (37 * hash) + ACTIVELOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActiveLog());
      hash = (37 * hash) + ACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getActive();
      if (getTasksCount() > 0) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasksList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SevenDayTaskProto.SevenDayDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 活动数据
     * </pre>
     *
     * Protobuf type {@code Proto.SevenDayTask.SevenDayDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SevenDayTask.SevenDayDto)
        com.dxx.game.dto.SevenDayTaskProto.SevenDayDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.class, com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTasksFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        days_ = 0;

        taskEndTimestamp_ = 0L;

        endTimestamp_ = 0L;

        activeLog_ = 0L;

        active_ = 0;

        if (tasksBuilder_ == null) {
          tasks_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          tasksBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SevenDayTaskProto.internal_static_Proto_SevenDayTask_SevenDayDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto getDefaultInstanceForType() {
        return com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto build() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto buildPartial() {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayDto result = new com.dxx.game.dto.SevenDayTaskProto.SevenDayDto(this);
        int from_bitField0_ = bitField0_;
        result.days_ = days_;
        result.taskEndTimestamp_ = taskEndTimestamp_;
        result.endTimestamp_ = endTimestamp_;
        result.activeLog_ = activeLog_;
        result.active_ = active_;
        if (tasksBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            tasks_ = java.util.Collections.unmodifiableList(tasks_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SevenDayTaskProto.SevenDayDto) {
          return mergeFrom((com.dxx.game.dto.SevenDayTaskProto.SevenDayDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SevenDayTaskProto.SevenDayDto other) {
        if (other == com.dxx.game.dto.SevenDayTaskProto.SevenDayDto.getDefaultInstance()) return this;
        if (other.getDays() != 0) {
          setDays(other.getDays());
        }
        if (other.getTaskEndTimestamp() != 0L) {
          setTaskEndTimestamp(other.getTaskEndTimestamp());
        }
        if (other.getEndTimestamp() != 0L) {
          setEndTimestamp(other.getEndTimestamp());
        }
        if (other.getActiveLog() != 0L) {
          setActiveLog(other.getActiveLog());
        }
        if (other.getActive() != 0) {
          setActive(other.getActive());
        }
        if (tasksBuilder_ == null) {
          if (!other.tasks_.isEmpty()) {
            if (tasks_.isEmpty()) {
              tasks_ = other.tasks_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTasksIsMutable();
              tasks_.addAll(other.tasks_);
            }
            onChanged();
          }
        } else {
          if (!other.tasks_.isEmpty()) {
            if (tasksBuilder_.isEmpty()) {
              tasksBuilder_.dispose();
              tasksBuilder_ = null;
              tasks_ = other.tasks_;
              bitField0_ = (bitField0_ & ~0x00000001);
              tasksBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTasksFieldBuilder() : null;
            } else {
              tasksBuilder_.addAllMessages(other.tasks_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.SevenDayTaskProto.SevenDayDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.SevenDayTaskProto.SevenDayDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int days_ ;
      /**
       * <pre>
       * 开启了第几天的任务
       * </pre>
       *
       * <code>uint32 days = 1;</code>
       * @return The days.
       */
      @java.lang.Override
      public int getDays() {
        return days_;
      }
      /**
       * <pre>
       * 开启了第几天的任务
       * </pre>
       *
       * <code>uint32 days = 1;</code>
       * @param value The days to set.
       * @return This builder for chaining.
       */
      public Builder setDays(int value) {
        
        days_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 开启了第几天的任务
       * </pre>
       *
       * <code>uint32 days = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDays() {
        
        days_ = 0;
        onChanged();
        return this;
      }

      private long taskEndTimestamp_ ;
      /**
       * <pre>
       * 任务结束时间
       * </pre>
       *
       * <code>uint64 taskEndTimestamp = 2;</code>
       * @return The taskEndTimestamp.
       */
      @java.lang.Override
      public long getTaskEndTimestamp() {
        return taskEndTimestamp_;
      }
      /**
       * <pre>
       * 任务结束时间
       * </pre>
       *
       * <code>uint64 taskEndTimestamp = 2;</code>
       * @param value The taskEndTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTaskEndTimestamp(long value) {
        
        taskEndTimestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务结束时间
       * </pre>
       *
       * <code>uint64 taskEndTimestamp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskEndTimestamp() {
        
        taskEndTimestamp_ = 0L;
        onChanged();
        return this;
      }

      private long endTimestamp_ ;
      /**
       * <pre>
       * 活动结束时间(任务+领取奖励)
       * </pre>
       *
       * <code>uint64 endTimestamp = 3;</code>
       * @return The endTimestamp.
       */
      @java.lang.Override
      public long getEndTimestamp() {
        return endTimestamp_;
      }
      /**
       * <pre>
       * 活动结束时间(任务+领取奖励)
       * </pre>
       *
       * <code>uint64 endTimestamp = 3;</code>
       * @param value The endTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setEndTimestamp(long value) {
        
        endTimestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动结束时间(任务+领取奖励)
       * </pre>
       *
       * <code>uint64 endTimestamp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTimestamp() {
        
        endTimestamp_ = 0L;
        onChanged();
        return this;
      }

      private long activeLog_ ;
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 activeLog = 4;</code>
       * @return The activeLog.
       */
      @java.lang.Override
      public long getActiveLog() {
        return activeLog_;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 activeLog = 4;</code>
       * @param value The activeLog to set.
       * @return This builder for chaining.
       */
      public Builder setActiveLog(long value) {
        
        activeLog_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>uint64 activeLog = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveLog() {
        
        activeLog_ = 0L;
        onChanged();
        return this;
      }

      private int active_ ;
      /**
       * <pre>
       * 当前活跃度
       * </pre>
       *
       * <code>uint32 active = 5;</code>
       * @return The active.
       */
      @java.lang.Override
      public int getActive() {
        return active_;
      }
      /**
       * <pre>
       * 当前活跃度
       * </pre>
       *
       * <code>uint32 active = 5;</code>
       * @param value The active to set.
       * @return This builder for chaining.
       */
      public Builder setActive(int value) {
        
        active_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前活跃度
       * </pre>
       *
       * <code>uint32 active = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearActive() {
        
        active_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> tasks_ =
        java.util.Collections.emptyList();
      private void ensureTasksIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          tasks_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.SevenDayTaskDto>(tasks_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> tasksBuilder_;

      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto> getTasksList() {
        if (tasksBuilder_ == null) {
          return java.util.Collections.unmodifiableList(tasks_);
        } else {
          return tasksBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public int getTasksCount() {
        if (tasksBuilder_ == null) {
          return tasks_.size();
        } else {
          return tasksBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto getTasks(int index) {
        if (tasksBuilder_ == null) {
          return tasks_.get(index);
        } else {
          return tasksBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder setTasks(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTasksIsMutable();
          tasks_.set(index, value);
          onChanged();
        } else {
          tasksBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder setTasks(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.set(index, builderForValue.build());
          onChanged();
        } else {
          tasksBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder addTasks(com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTasksIsMutable();
          tasks_.add(value);
          onChanged();
        } else {
          tasksBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder addTasks(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTasksIsMutable();
          tasks_.add(index, value);
          onChanged();
        } else {
          tasksBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder addTasks(
          com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.add(builderForValue.build());
          onChanged();
        } else {
          tasksBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder addTasks(
          int index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.add(index, builderForValue.build());
          onChanged();
        } else {
          tasksBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder addAllTasks(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDto> values) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, tasks_);
          onChanged();
        } else {
          tasksBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          tasksBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public Builder removeTasks(int index) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.remove(index);
          onChanged();
        } else {
          tasksBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder getTasksBuilder(
          int index) {
        return getTasksFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder getTasksOrBuilder(
          int index) {
        if (tasksBuilder_ == null) {
          return tasks_.get(index);  } else {
          return tasksBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
           getTasksOrBuilderList() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(tasks_);
        }
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder addTasksBuilder() {
        return getTasksFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.SevenDayTaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder addTasksBuilder(
          int index) {
        return getTasksFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.SevenDayTaskDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 任务数据
       * </pre>
       *
       * <code>repeated .Proto.Common.SevenDayTaskDto tasks = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder> 
           getTasksBuilderList() {
        return getTasksFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.SevenDayTaskDto, com.dxx.game.dto.CommonProto.SevenDayTaskDto.Builder, com.dxx.game.dto.CommonProto.SevenDayTaskDtoOrBuilder>(
                  tasks_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.SevenDayTask.SevenDayDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.SevenDayTask.SevenDayDto)
    private static final com.dxx.game.dto.SevenDayTaskProto.SevenDayDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayTaskProto.SevenDayDto();
    }

    public static com.dxx.game.dto.SevenDayTaskProto.SevenDayDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SevenDayDto>
        PARSER = new com.google.protobuf.AbstractParser<SevenDayDto>() {
      @java.lang.Override
      public SevenDayDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SevenDayDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SevenDayDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SevenDayDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayTaskProto.SevenDayDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SevenDayTask_SevenDayDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_SevenDayTask_SevenDayDto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016sevenday.proto\022\022Proto.SevenDayTask\032\014co" +
      "mmon.proto\"N\n\032SevenDayTaskGetInfoRequest" +
      "\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Common.Co" +
      "mmonParams\"\217\001\n\033SevenDayTaskGetInfoRespon" +
      "se\022\014\n\004code\030\001 \001(\005\0224\n\013sevenDayDto\030\002 \001(\0132\037." +
      "Proto.SevenDayTask.SevenDayDto\022,\n\ncommon" +
      "Data\030\003 \001(\0132\030.Proto.Common.CommonData\"]\n\031" +
      "SevenDayTaskRewardRequest\0220\n\014commonParam" +
      "s\030\001 \001(\0132\032.Proto.Common.CommonParams\022\016\n\006t" +
      "askId\030\002 \001(\r\"\236\001\n\032SevenDayTaskRewardRespon" +
      "se\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.P" +
      "roto.Common.CommonData\022\016\n\006active\030\003 \001(\r\0224" +
      "\n\rupdateTaskDto\030\004 \003(\0132\035.Proto.Common.Sev" +
      "enDayTaskDto\"x\n\037SevenDayTaskActiveReward" +
      "Request\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Co" +
      "mmon.CommonParams\022\020\n\010configId\030\002 \001(\r\022\021\n\ts" +
      "electIdx\030\003 \001(\r\"\247\001\n SevenDayTaskActiveRew" +
      "ardResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030" +
      "\002 \001(\0132\030.Proto.Common.CommonData\022\021\n\tactiv" +
      "eLog\030\003 \001(\004\0224\n\rupdateTaskDto\030\004 \003(\0132\035.Prot" +
      "o.Common.SevenDayTaskDto\"\234\001\n\013SevenDayDto" +
      "\022\014\n\004days\030\001 \001(\r\022\030\n\020taskEndTimestamp\030\002 \001(\004" +
      "\022\024\n\014endTimestamp\030\003 \001(\004\022\021\n\tactiveLog\030\004 \001(" +
      "\004\022\016\n\006active\030\005 \001(\r\022,\n\005tasks\030\006 \003(\0132\035.Proto" +
      ".Common.SevenDayTaskDtoB%\n\020com.dxx.game." +
      "dtoB\021SevenDayTaskProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayTaskGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "SevenDayDto", "CommonData", });
    internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayTaskRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "TaskId", });
    internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayTaskRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Active", "UpdateTaskDto", });
    internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ConfigId", "SelectIdx", });
    internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayTaskActiveRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "ActiveLog", "UpdateTaskDto", });
    internal_static_Proto_SevenDayTask_SevenDayDto_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_SevenDayTask_SevenDayDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_SevenDayTask_SevenDayDto_descriptor,
        new java.lang.String[] { "Days", "TaskEndTimestamp", "EndTimestamp", "ActiveLog", "Active", "Tasks", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

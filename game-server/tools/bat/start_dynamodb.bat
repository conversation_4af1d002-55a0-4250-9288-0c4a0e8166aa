@echo framework2 dynamodb port : 8000
@echo off

set library="D:\software\dynamodb_local_latest\DynamoDBLocal_lib"
set DynamoDBLocalJar="D:\software\dynamodb_local_latest\DynamoDBLocal.jar"
set port=8000
set dbPath="D:\software\dynamodb_local_latest\framework2"

IF NOT EXIST %dbPath% MD %dbPath%

java -D"java.library.path=%library%" -jar %DynamoDBLocalJar% -port %port% -dbPath %dbPath% -sharedDb 


pause 
package com.dxx.game.common.httpclient;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.Dns;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ResourceUtils;

import javax.net.ssl.*;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.UnknownHostException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@Configuration
public class OkHttpConfiguration {
    @Value("${ok.http.connect-timeout}")
    private Integer CONNECTTIMEOUT;

    @Value("${ok.http.read-timeout}")
    private Integer READTIMEOUT;

    @Value("${ok.http.write-timeout}")
    private Integer WRITETIMEOUT;

    @Value("${ok.http.max-idle-connections}")
    private Integer MAXIDLECONNECTION;

    @Value("${ok.http.keep-alive-time}")
    private Long KEEPALIVETIME;

    @Value("${ok.http.proxy.address}")
    private String proxyAddress;

    @Value("${ok.http.proxy.port}")
    private String proxyPort;

    @Bean
    public OkHttpClient okHttpClient() {

        OkHttpClient client = this.newOkHttpClient();
        OkHttpClientUtil.setOkHttpClient(client);

        // 修改dns缓存时间为20分钟
        System.setProperty("networkaddress.cache.ttl", "1200");
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        executor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                OkHttpClient client = newOkHttpClient();
                OkHttpClientUtil.setOkHttpClient(client);
            }
        }, 1200, 1200, TimeUnit.SECONDS);
        return client;
    }

    private OkHttpClient newOkHttpClient() {
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        if (!proxyAddress.isEmpty() && !proxyPort.isEmpty()) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyAddress, Integer.parseInt(proxyPort)));
            clientBuilder.proxy(proxy);
        }

        // 创建一个虚拟线程，注此虚拟线程只有在异步调用时生效
        ExecutorService virtualThreadExecutor = Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("v-okhttp-dispatcher-", 0).factory());
        Dispatcher dispatcher = new Dispatcher(virtualThreadExecutor);
        dispatcher.setMaxRequestsPerHost(10);
        dispatcher.setMaxRequests(100);

        OkHttpClient client = clientBuilder.sslSocketFactory(sslSocketFactory(), getX509TrustManager())
                .dispatcher(dispatcher)
                .retryOnConnectionFailure(false)//是否开启缓存
                .connectionPool(getConnectionPool())
                .connectTimeout(CONNECTTIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READTIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITETIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(new OkHttpRetryIntercepter(5))
                .build();
        return client;
    }

    @Bean
    public X509TrustManager getX509TrustManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }
            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
            }
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }

    @Bean
    public SSLSocketFactory sslSocketFactory() {
        try {
            // 信任任何链接
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{getX509TrustManager()}, new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.error("sslSocketFactory, e:",e);
        }
        return null;
    }

    @Bean
    public ConnectionPool getConnectionPool() {
        /**
         * MAXIDLECONNECTION 连接池中整体的空闲连接的最大数量
         * KEEPALIVETIME 连接空闲时间最多为五分钟
         */
        return new ConnectionPool(MAXIDLECONNECTION, KEEPALIVETIME, TimeUnit.SECONDS);
    }
}

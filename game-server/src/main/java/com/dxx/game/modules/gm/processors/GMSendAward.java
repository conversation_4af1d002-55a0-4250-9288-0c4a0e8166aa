package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.util.RequestIdUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.ProtobufUtils;
import com.dxx.game.dao.dynamodb.model.LogGmReward;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.LogGmRewardDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.SEND_AWARD)
public class GMSendAward extends AbstractGMProcessor {

    @Resource
    private LogGmRewardDao logGmRewardDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private RewardService rewardService;
    @Resource
    private UserDao userDao;
    @Resource
    private PayService payService;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private String uniqueId;
        private long userId;
        List<List<Integer>> rewardConfigs;
        private String preOrderId;
    }



    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        String uniqueId = request.getUniqueId();
        if (StringUtils.isEmpty(uniqueId)) {
            return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "params error");
        }
        long userId = request.getUserId();

        LogGmReward logGmReward = logGmRewardDao.getByUserIdAndUniqueId(userId, uniqueId);
        if (logGmReward != null) {
            return this.error(GMErrorCode.ERROR_CODE_RECEIVED, "award received");
        }

        List<List<Integer>> rewardConfigs = request.getRewardConfigs();

        List<Reward> rewards = rewardService.parseRewards(rewardConfigs);

        int type = params.getIntValue("type");
        RequestContext.setCommand((short)type);

        User user = userDao.getByUserId(userId);
        if (user == null) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
        }

        RequestContext.setUserId(userId);
        RequestContext.setUser(user);
        CommonProto.CommonParams.Builder commonParams = CommonProto.CommonParams.newBuilder();
        commonParams.setVersion(user.getClientNetVersion());
        RequestContext.setCommonParams(commonParams.buildPartial());

        // 加锁
        if (!redisLock.lock()) {
            return this.error(GMErrorCode.ERROR_CODE_DEFAULT, "Requests are too frequent");
        }

        RewardResultSet resultSet = rewardService.executeRewards(userId, rewards);
        if (resultSet.isFailed()) {
            log.error("gm send award failed, code:{}", resultSet.getResultCode());
            return this.error(resultSet.getResultCode(), "system error");
        }

        if (!StringUtils.isEmpty(request.getPreOrderId())) {
            long preOrderId = params.getLongValue("preOrderId");
            payService.iapSupplement(userId, uniqueId, preOrderId,0,"");
        }


        logGmReward = new LogGmReward();
        logGmReward.setUserId(userId);
        logGmReward.setUniqueId(uniqueId);
        logGmReward.setTimestamp(DateUtils.getUnixTime());
        logGmRewardDao.update(logGmReward);

        CommonProto.CommonData commonData = CommonHelper.buildCommonData(resultSet);

        CommonProto.CommonData.Builder commonDataBuilder = commonData.toBuilder();
        RequestIdUtil.fillTransId(commonDataBuilder);
        commonData = commonDataBuilder.build();
        return this.success(ProtobufUtils.messageToBase64String(commonData));
    }
}

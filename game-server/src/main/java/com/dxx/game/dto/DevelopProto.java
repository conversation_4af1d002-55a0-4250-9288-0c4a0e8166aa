// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: develop.proto

package com.dxx.game.dto;

public final class DevelopProto {
  private DevelopProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DevelopLoginRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Develop.DevelopLoginRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 userId = 1;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   *CMD PackageId=9001 开发测试用
   * </pre>
   *
   * Protobuf type {@code Proto.Develop.DevelopLoginRequest}
   */
  public static final class DevelopLoginRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Develop.DevelopLoginRequest)
      DevelopLoginRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DevelopLoginRequest.newBuilder() to construct.
    private DevelopLoginRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DevelopLoginRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DevelopLoginRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DevelopLoginRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              userId_ = input.readUInt64();
              break;
            }
            case 16: {

              type_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DevelopProto.DevelopLoginRequest.class, com.dxx.game.dto.DevelopProto.DevelopLoginRequest.Builder.class);
    }

    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>uint64 userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>uint32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (userId_ != 0L) {
        output.writeUInt64(1, userId_);
      }
      if (type_ != 0) {
        output.writeUInt32(2, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, userId_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DevelopProto.DevelopLoginRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DevelopProto.DevelopLoginRequest other = (com.dxx.game.dto.DevelopProto.DevelopLoginRequest) obj;

      if (getUserId()
          != other.getUserId()) return false;
      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DevelopProto.DevelopLoginRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=9001 开发测试用
     * </pre>
     *
     * Protobuf type {@code Proto.Develop.DevelopLoginRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Develop.DevelopLoginRequest)
        com.dxx.game.dto.DevelopProto.DevelopLoginRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DevelopProto.DevelopLoginRequest.class, com.dxx.game.dto.DevelopProto.DevelopLoginRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.DevelopProto.DevelopLoginRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        userId_ = 0L;

        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopLoginRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.DevelopProto.DevelopLoginRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopLoginRequest build() {
        com.dxx.game.dto.DevelopProto.DevelopLoginRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopLoginRequest buildPartial() {
        com.dxx.game.dto.DevelopProto.DevelopLoginRequest result = new com.dxx.game.dto.DevelopProto.DevelopLoginRequest(this);
        result.userId_ = userId_;
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DevelopProto.DevelopLoginRequest) {
          return mergeFrom((com.dxx.game.dto.DevelopProto.DevelopLoginRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DevelopProto.DevelopLoginRequest other) {
        if (other == com.dxx.game.dto.DevelopProto.DevelopLoginRequest.getDefaultInstance()) return this;
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DevelopProto.DevelopLoginRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DevelopProto.DevelopLoginRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long userId_ ;
      /**
       * <code>uint64 userId = 1;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>uint64 userId = 1;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {
        
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 userId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        
        userId_ = 0L;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <code>uint32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Develop.DevelopLoginRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Develop.DevelopLoginRequest)
    private static final com.dxx.game.dto.DevelopProto.DevelopLoginRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DevelopProto.DevelopLoginRequest();
    }

    public static com.dxx.game.dto.DevelopProto.DevelopLoginRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DevelopLoginRequest>
        PARSER = new com.google.protobuf.AbstractParser<DevelopLoginRequest>() {
      @java.lang.Override
      public DevelopLoginRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DevelopLoginRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DevelopLoginRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DevelopLoginRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DevelopProto.DevelopLoginRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DevelopLoginResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Develop.DevelopLoginResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonParams commonParams = 2;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 2;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <code>int64 dbIdx = 4;</code>
     * @return The dbIdx.
     */
    long getDbIdx();

    /**
     * <code>int64 tableIdx = 5;</code>
     * @return The tableIdx.
     */
    long getTableIdx();

    /**
     * <code>int64 coins = 6;</code>
     * @return The coins.
     */
    long getCoins();

    /**
     * <code>int64 diamonds = 7;</code>
     * @return The diamonds.
     */
    long getDiamonds();

    /**
     * <code>int32 chapterId = 8;</code>
     * @return The chapterId.
     */
    int getChapterId();

    /**
     * <code>uint32 loginType = 9;</code>
     * @return The loginType.
     */
    int getLoginType();

    /**
     * <code>uint32 level = 10;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <code>uint32 exp = 11;</code>
     * @return The exp.
     */
    int getExp();

    /**
     * <code>string extra = 12;</code>
     * @return The extra.
     */
    java.lang.String getExtra();
    /**
     * <code>string extra = 12;</code>
     * @return The bytes for extra.
     */
    com.google.protobuf.ByteString
        getExtraBytes();

    /**
     * <code>uint32 missionId = 13;</code>
     * @return The missionId.
     */
    int getMissionId();
  }
  /**
   * <pre>
   *CMD PackageId=9002 
   * </pre>
   *
   * Protobuf type {@code Proto.Develop.DevelopLoginResponse}
   */
  public static final class DevelopLoginResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Develop.DevelopLoginResponse)
      DevelopLoginResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DevelopLoginResponse.newBuilder() to construct.
    private DevelopLoginResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DevelopLoginResponse() {
      extra_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DevelopLoginResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DevelopLoginResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              userId_ = input.readInt64();
              break;
            }
            case 32: {

              dbIdx_ = input.readInt64();
              break;
            }
            case 40: {

              tableIdx_ = input.readInt64();
              break;
            }
            case 48: {

              coins_ = input.readInt64();
              break;
            }
            case 56: {

              diamonds_ = input.readInt64();
              break;
            }
            case 64: {

              chapterId_ = input.readInt32();
              break;
            }
            case 72: {

              loginType_ = input.readUInt32();
              break;
            }
            case 80: {

              level_ = input.readUInt32();
              break;
            }
            case 88: {

              exp_ = input.readUInt32();
              break;
            }
            case 98: {
              java.lang.String s = input.readStringRequireUtf8();

              extra_ = s;
              break;
            }
            case 104: {

              missionId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DevelopProto.DevelopLoginResponse.class, com.dxx.game.dto.DevelopProto.DevelopLoginResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 2;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 2;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_;
    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int DBIDX_FIELD_NUMBER = 4;
    private long dbIdx_;
    /**
     * <code>int64 dbIdx = 4;</code>
     * @return The dbIdx.
     */
    @java.lang.Override
    public long getDbIdx() {
      return dbIdx_;
    }

    public static final int TABLEIDX_FIELD_NUMBER = 5;
    private long tableIdx_;
    /**
     * <code>int64 tableIdx = 5;</code>
     * @return The tableIdx.
     */
    @java.lang.Override
    public long getTableIdx() {
      return tableIdx_;
    }

    public static final int COINS_FIELD_NUMBER = 6;
    private long coins_;
    /**
     * <code>int64 coins = 6;</code>
     * @return The coins.
     */
    @java.lang.Override
    public long getCoins() {
      return coins_;
    }

    public static final int DIAMONDS_FIELD_NUMBER = 7;
    private long diamonds_;
    /**
     * <code>int64 diamonds = 7;</code>
     * @return The diamonds.
     */
    @java.lang.Override
    public long getDiamonds() {
      return diamonds_;
    }

    public static final int CHAPTERID_FIELD_NUMBER = 8;
    private int chapterId_;
    /**
     * <code>int32 chapterId = 8;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    public static final int LOGINTYPE_FIELD_NUMBER = 9;
    private int loginType_;
    /**
     * <code>uint32 loginType = 9;</code>
     * @return The loginType.
     */
    @java.lang.Override
    public int getLoginType() {
      return loginType_;
    }

    public static final int LEVEL_FIELD_NUMBER = 10;
    private int level_;
    /**
     * <code>uint32 level = 10;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int EXP_FIELD_NUMBER = 11;
    private int exp_;
    /**
     * <code>uint32 exp = 11;</code>
     * @return The exp.
     */
    @java.lang.Override
    public int getExp() {
      return exp_;
    }

    public static final int EXTRA_FIELD_NUMBER = 12;
    private volatile java.lang.Object extra_;
    /**
     * <code>string extra = 12;</code>
     * @return The extra.
     */
    @java.lang.Override
    public java.lang.String getExtra() {
      java.lang.Object ref = extra_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extra_ = s;
        return s;
      }
    }
    /**
     * <code>string extra = 12;</code>
     * @return The bytes for extra.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getExtraBytes() {
      java.lang.Object ref = extra_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extra_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MISSIONID_FIELD_NUMBER = 13;
    private int missionId_;
    /**
     * <code>uint32 missionId = 13;</code>
     * @return The missionId.
     */
    @java.lang.Override
    public int getMissionId() {
      return missionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonParams_ != null) {
        output.writeMessage(2, getCommonParams());
      }
      if (userId_ != 0L) {
        output.writeInt64(3, userId_);
      }
      if (dbIdx_ != 0L) {
        output.writeInt64(4, dbIdx_);
      }
      if (tableIdx_ != 0L) {
        output.writeInt64(5, tableIdx_);
      }
      if (coins_ != 0L) {
        output.writeInt64(6, coins_);
      }
      if (diamonds_ != 0L) {
        output.writeInt64(7, diamonds_);
      }
      if (chapterId_ != 0) {
        output.writeInt32(8, chapterId_);
      }
      if (loginType_ != 0) {
        output.writeUInt32(9, loginType_);
      }
      if (level_ != 0) {
        output.writeUInt32(10, level_);
      }
      if (exp_ != 0) {
        output.writeUInt32(11, exp_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extra_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, extra_);
      }
      if (missionId_ != 0) {
        output.writeUInt32(13, missionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonParams());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (dbIdx_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, dbIdx_);
      }
      if (tableIdx_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, tableIdx_);
      }
      if (coins_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, coins_);
      }
      if (diamonds_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, diamonds_);
      }
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, chapterId_);
      }
      if (loginType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, loginType_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, level_);
      }
      if (exp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(11, exp_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extra_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, extra_);
      }
      if (missionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, missionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DevelopProto.DevelopLoginResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DevelopProto.DevelopLoginResponse other = (com.dxx.game.dto.DevelopProto.DevelopLoginResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (getDbIdx()
          != other.getDbIdx()) return false;
      if (getTableIdx()
          != other.getTableIdx()) return false;
      if (getCoins()
          != other.getCoins()) return false;
      if (getDiamonds()
          != other.getDiamonds()) return false;
      if (getChapterId()
          != other.getChapterId()) return false;
      if (getLoginType()
          != other.getLoginType()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (getExp()
          != other.getExp()) return false;
      if (!getExtra()
          .equals(other.getExtra())) return false;
      if (getMissionId()
          != other.getMissionId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + DBIDX_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDbIdx());
      hash = (37 * hash) + TABLEIDX_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTableIdx());
      hash = (37 * hash) + COINS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCoins());
      hash = (37 * hash) + DIAMONDS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDiamonds());
      hash = (37 * hash) + CHAPTERID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (37 * hash) + LOGINTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getLoginType();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + getExtra().hashCode();
      hash = (37 * hash) + MISSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getMissionId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DevelopProto.DevelopLoginResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=9002 
     * </pre>
     *
     * Protobuf type {@code Proto.Develop.DevelopLoginResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Develop.DevelopLoginResponse)
        com.dxx.game.dto.DevelopProto.DevelopLoginResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DevelopProto.DevelopLoginResponse.class, com.dxx.game.dto.DevelopProto.DevelopLoginResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.DevelopProto.DevelopLoginResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        userId_ = 0L;

        dbIdx_ = 0L;

        tableIdx_ = 0L;

        coins_ = 0L;

        diamonds_ = 0L;

        chapterId_ = 0;

        loginType_ = 0;

        level_ = 0;

        exp_ = 0;

        extra_ = "";

        missionId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopLoginResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopLoginResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.DevelopProto.DevelopLoginResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopLoginResponse build() {
        com.dxx.game.dto.DevelopProto.DevelopLoginResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopLoginResponse buildPartial() {
        com.dxx.game.dto.DevelopProto.DevelopLoginResponse result = new com.dxx.game.dto.DevelopProto.DevelopLoginResponse(this);
        result.code_ = code_;
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.userId_ = userId_;
        result.dbIdx_ = dbIdx_;
        result.tableIdx_ = tableIdx_;
        result.coins_ = coins_;
        result.diamonds_ = diamonds_;
        result.chapterId_ = chapterId_;
        result.loginType_ = loginType_;
        result.level_ = level_;
        result.exp_ = exp_;
        result.extra_ = extra_;
        result.missionId_ = missionId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DevelopProto.DevelopLoginResponse) {
          return mergeFrom((com.dxx.game.dto.DevelopProto.DevelopLoginResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DevelopProto.DevelopLoginResponse other) {
        if (other == com.dxx.game.dto.DevelopProto.DevelopLoginResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.getDbIdx() != 0L) {
          setDbIdx(other.getDbIdx());
        }
        if (other.getTableIdx() != 0L) {
          setTableIdx(other.getTableIdx());
        }
        if (other.getCoins() != 0L) {
          setCoins(other.getCoins());
        }
        if (other.getDiamonds() != 0L) {
          setDiamonds(other.getDiamonds());
        }
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        if (other.getLoginType() != 0) {
          setLoginType(other.getLoginType());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getExp() != 0) {
          setExp(other.getExp());
        }
        if (!other.getExtra().isEmpty()) {
          extra_ = other.extra_;
          onChanged();
        }
        if (other.getMissionId() != 0) {
          setMissionId(other.getMissionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DevelopProto.DevelopLoginResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DevelopProto.DevelopLoginResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 3;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {
        
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        
        userId_ = 0L;
        onChanged();
        return this;
      }

      private long dbIdx_ ;
      /**
       * <code>int64 dbIdx = 4;</code>
       * @return The dbIdx.
       */
      @java.lang.Override
      public long getDbIdx() {
        return dbIdx_;
      }
      /**
       * <code>int64 dbIdx = 4;</code>
       * @param value The dbIdx to set.
       * @return This builder for chaining.
       */
      public Builder setDbIdx(long value) {
        
        dbIdx_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 dbIdx = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDbIdx() {
        
        dbIdx_ = 0L;
        onChanged();
        return this;
      }

      private long tableIdx_ ;
      /**
       * <code>int64 tableIdx = 5;</code>
       * @return The tableIdx.
       */
      @java.lang.Override
      public long getTableIdx() {
        return tableIdx_;
      }
      /**
       * <code>int64 tableIdx = 5;</code>
       * @param value The tableIdx to set.
       * @return This builder for chaining.
       */
      public Builder setTableIdx(long value) {
        
        tableIdx_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 tableIdx = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTableIdx() {
        
        tableIdx_ = 0L;
        onChanged();
        return this;
      }

      private long coins_ ;
      /**
       * <code>int64 coins = 6;</code>
       * @return The coins.
       */
      @java.lang.Override
      public long getCoins() {
        return coins_;
      }
      /**
       * <code>int64 coins = 6;</code>
       * @param value The coins to set.
       * @return This builder for chaining.
       */
      public Builder setCoins(long value) {
        
        coins_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 coins = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCoins() {
        
        coins_ = 0L;
        onChanged();
        return this;
      }

      private long diamonds_ ;
      /**
       * <code>int64 diamonds = 7;</code>
       * @return The diamonds.
       */
      @java.lang.Override
      public long getDiamonds() {
        return diamonds_;
      }
      /**
       * <code>int64 diamonds = 7;</code>
       * @param value The diamonds to set.
       * @return This builder for chaining.
       */
      public Builder setDiamonds(long value) {
        
        diamonds_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 diamonds = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearDiamonds() {
        
        diamonds_ = 0L;
        onChanged();
        return this;
      }

      private int chapterId_ ;
      /**
       * <code>int32 chapterId = 8;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <code>int32 chapterId = 8;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {
        
        chapterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 chapterId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        
        chapterId_ = 0;
        onChanged();
        return this;
      }

      private int loginType_ ;
      /**
       * <code>uint32 loginType = 9;</code>
       * @return The loginType.
       */
      @java.lang.Override
      public int getLoginType() {
        return loginType_;
      }
      /**
       * <code>uint32 loginType = 9;</code>
       * @param value The loginType to set.
       * @return This builder for chaining.
       */
      public Builder setLoginType(int value) {
        
        loginType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 loginType = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoginType() {
        
        loginType_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>uint32 level = 10;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>uint32 level = 10;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 level = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private int exp_ ;
      /**
       * <code>uint32 exp = 11;</code>
       * @return The exp.
       */
      @java.lang.Override
      public int getExp() {
        return exp_;
      }
      /**
       * <code>uint32 exp = 11;</code>
       * @param value The exp to set.
       * @return This builder for chaining.
       */
      public Builder setExp(int value) {
        
        exp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 exp = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearExp() {
        
        exp_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object extra_ = "";
      /**
       * <code>string extra = 12;</code>
       * @return The extra.
       */
      public java.lang.String getExtra() {
        java.lang.Object ref = extra_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          extra_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string extra = 12;</code>
       * @return The bytes for extra.
       */
      public com.google.protobuf.ByteString
          getExtraBytes() {
        java.lang.Object ref = extra_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extra_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string extra = 12;</code>
       * @param value The extra to set.
       * @return This builder for chaining.
       */
      public Builder setExtra(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        extra_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string extra = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtra() {
        
        extra_ = getDefaultInstance().getExtra();
        onChanged();
        return this;
      }
      /**
       * <code>string extra = 12;</code>
       * @param value The bytes for extra to set.
       * @return This builder for chaining.
       */
      public Builder setExtraBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        extra_ = value;
        onChanged();
        return this;
      }

      private int missionId_ ;
      /**
       * <code>uint32 missionId = 13;</code>
       * @return The missionId.
       */
      @java.lang.Override
      public int getMissionId() {
        return missionId_;
      }
      /**
       * <code>uint32 missionId = 13;</code>
       * @param value The missionId to set.
       * @return This builder for chaining.
       */
      public Builder setMissionId(int value) {
        
        missionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 missionId = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearMissionId() {
        
        missionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Develop.DevelopLoginResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Develop.DevelopLoginResponse)
    private static final com.dxx.game.dto.DevelopProto.DevelopLoginResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DevelopProto.DevelopLoginResponse();
    }

    public static com.dxx.game.dto.DevelopProto.DevelopLoginResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DevelopLoginResponse>
        PARSER = new com.google.protobuf.AbstractParser<DevelopLoginResponse>() {
      @java.lang.Override
      public DevelopLoginResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DevelopLoginResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DevelopLoginResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DevelopLoginResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DevelopProto.DevelopLoginResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DevelopChangeResourceRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Develop.DevelopChangeResourceRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int32 resType = 2;</code>
     * @return The resType.
     */
    int getResType();

    /**
     * <code>int32 resNum = 3;</code>
     * @return The resNum.
     */
    int getResNum();

    /**
     * <code>int32 itemId = 4;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <code>string otherData = 5;</code>
     * @return The otherData.
     */
    java.lang.String getOtherData();
    /**
     * <code>string otherData = 5;</code>
     * @return The bytes for otherData.
     */
    com.google.protobuf.ByteString
        getOtherDataBytes();
  }
  /**
   * <pre>
   *CMD PackageId=9003 开发测试修改资源
   * </pre>
   *
   * Protobuf type {@code Proto.Develop.DevelopChangeResourceRequest}
   */
  public static final class DevelopChangeResourceRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Develop.DevelopChangeResourceRequest)
      DevelopChangeResourceRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DevelopChangeResourceRequest.newBuilder() to construct.
    private DevelopChangeResourceRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DevelopChangeResourceRequest() {
      otherData_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DevelopChangeResourceRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DevelopChangeResourceRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              resType_ = input.readInt32();
              break;
            }
            case 24: {

              resNum_ = input.readInt32();
              break;
            }
            case 32: {

              itemId_ = input.readInt32();
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              otherData_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.class, com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int RESTYPE_FIELD_NUMBER = 2;
    private int resType_;
    /**
     * <code>int32 resType = 2;</code>
     * @return The resType.
     */
    @java.lang.Override
    public int getResType() {
      return resType_;
    }

    public static final int RESNUM_FIELD_NUMBER = 3;
    private int resNum_;
    /**
     * <code>int32 resNum = 3;</code>
     * @return The resNum.
     */
    @java.lang.Override
    public int getResNum() {
      return resNum_;
    }

    public static final int ITEMID_FIELD_NUMBER = 4;
    private int itemId_;
    /**
     * <code>int32 itemId = 4;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int OTHERDATA_FIELD_NUMBER = 5;
    private volatile java.lang.Object otherData_;
    /**
     * <code>string otherData = 5;</code>
     * @return The otherData.
     */
    @java.lang.Override
    public java.lang.String getOtherData() {
      java.lang.Object ref = otherData_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        otherData_ = s;
        return s;
      }
    }
    /**
     * <code>string otherData = 5;</code>
     * @return The bytes for otherData.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOtherDataBytes() {
      java.lang.Object ref = otherData_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        otherData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (resType_ != 0) {
        output.writeInt32(2, resType_);
      }
      if (resNum_ != 0) {
        output.writeInt32(3, resNum_);
      }
      if (itemId_ != 0) {
        output.writeInt32(4, itemId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(otherData_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, otherData_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (resType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, resType_);
      }
      if (resNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, resNum_);
      }
      if (itemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, itemId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(otherData_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, otherData_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest other = (com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getResType()
          != other.getResType()) return false;
      if (getResNum()
          != other.getResNum()) return false;
      if (getItemId()
          != other.getItemId()) return false;
      if (!getOtherData()
          .equals(other.getOtherData())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + RESTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getResType();
      hash = (37 * hash) + RESNUM_FIELD_NUMBER;
      hash = (53 * hash) + getResNum();
      hash = (37 * hash) + ITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getItemId();
      hash = (37 * hash) + OTHERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getOtherData().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=9003 开发测试修改资源
     * </pre>
     *
     * Protobuf type {@code Proto.Develop.DevelopChangeResourceRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Develop.DevelopChangeResourceRequest)
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.class, com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        resType_ = 0;

        resNum_ = 0;

        itemId_ = 0;

        otherData_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest build() {
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest buildPartial() {
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest result = new com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.resType_ = resType_;
        result.resNum_ = resNum_;
        result.itemId_ = itemId_;
        result.otherData_ = otherData_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest) {
          return mergeFrom((com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest other) {
        if (other == com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getResType() != 0) {
          setResType(other.getResType());
        }
        if (other.getResNum() != 0) {
          setResNum(other.getResNum());
        }
        if (other.getItemId() != 0) {
          setItemId(other.getItemId());
        }
        if (!other.getOtherData().isEmpty()) {
          otherData_ = other.otherData_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int resType_ ;
      /**
       * <code>int32 resType = 2;</code>
       * @return The resType.
       */
      @java.lang.Override
      public int getResType() {
        return resType_;
      }
      /**
       * <code>int32 resType = 2;</code>
       * @param value The resType to set.
       * @return This builder for chaining.
       */
      public Builder setResType(int value) {
        
        resType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 resType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearResType() {
        
        resType_ = 0;
        onChanged();
        return this;
      }

      private int resNum_ ;
      /**
       * <code>int32 resNum = 3;</code>
       * @return The resNum.
       */
      @java.lang.Override
      public int getResNum() {
        return resNum_;
      }
      /**
       * <code>int32 resNum = 3;</code>
       * @param value The resNum to set.
       * @return This builder for chaining.
       */
      public Builder setResNum(int value) {
        
        resNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 resNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearResNum() {
        
        resNum_ = 0;
        onChanged();
        return this;
      }

      private int itemId_ ;
      /**
       * <code>int32 itemId = 4;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <code>int32 itemId = 4;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 itemId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        
        itemId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object otherData_ = "";
      /**
       * <code>string otherData = 5;</code>
       * @return The otherData.
       */
      public java.lang.String getOtherData() {
        java.lang.Object ref = otherData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          otherData_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string otherData = 5;</code>
       * @return The bytes for otherData.
       */
      public com.google.protobuf.ByteString
          getOtherDataBytes() {
        java.lang.Object ref = otherData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          otherData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string otherData = 5;</code>
       * @param value The otherData to set.
       * @return This builder for chaining.
       */
      public Builder setOtherData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        otherData_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string otherData = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearOtherData() {
        
        otherData_ = getDefaultInstance().getOtherData();
        onChanged();
        return this;
      }
      /**
       * <code>string otherData = 5;</code>
       * @param value The bytes for otherData to set.
       * @return This builder for chaining.
       */
      public Builder setOtherDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        otherData_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Develop.DevelopChangeResourceRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Develop.DevelopChangeResourceRequest)
    private static final com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest();
    }

    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DevelopChangeResourceRequest>
        PARSER = new com.google.protobuf.AbstractParser<DevelopChangeResourceRequest>() {
      @java.lang.Override
      public DevelopChangeResourceRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DevelopChangeResourceRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DevelopChangeResourceRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DevelopChangeResourceRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DevelopChangeResourceResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Develop.DevelopChangeResourceResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>string respData = 3;</code>
     * @return The respData.
     */
    java.lang.String getRespData();
    /**
     * <code>string respData = 3;</code>
     * @return The bytes for respData.
     */
    com.google.protobuf.ByteString
        getRespDataBytes();
  }
  /**
   * <pre>
   *CMD PackageId=9004
   * </pre>
   *
   * Protobuf type {@code Proto.Develop.DevelopChangeResourceResponse}
   */
  public static final class DevelopChangeResourceResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Develop.DevelopChangeResourceResponse)
      DevelopChangeResourceResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DevelopChangeResourceResponse.newBuilder() to construct.
    private DevelopChangeResourceResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DevelopChangeResourceResponse() {
      respData_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DevelopChangeResourceResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DevelopChangeResourceResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              respData_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.class, com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <pre>
     * 通用返回
     * </pre>
     *
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int RESPDATA_FIELD_NUMBER = 3;
    private volatile java.lang.Object respData_;
    /**
     * <code>string respData = 3;</code>
     * @return The respData.
     */
    @java.lang.Override
    public java.lang.String getRespData() {
      java.lang.Object ref = respData_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        respData_ = s;
        return s;
      }
    }
    /**
     * <code>string respData = 3;</code>
     * @return The bytes for respData.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getRespDataBytes() {
      java.lang.Object ref = respData_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        respData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(respData_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, respData_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(respData_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, respData_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse other = (com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getRespData()
          .equals(other.getRespData())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + RESPDATA_FIELD_NUMBER;
      hash = (53 * hash) + getRespData().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=9004
     * </pre>
     *
     * Protobuf type {@code Proto.Develop.DevelopChangeResourceResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Develop.DevelopChangeResourceResponse)
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.class, com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        respData_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopChangeResourceResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse build() {
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse buildPartial() {
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse result = new com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.respData_ = respData_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse) {
          return mergeFrom((com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse other) {
        if (other == com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (!other.getRespData().isEmpty()) {
          respData_ = other.respData_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <pre>
       * 通用返回
       * </pre>
       *
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private java.lang.Object respData_ = "";
      /**
       * <code>string respData = 3;</code>
       * @return The respData.
       */
      public java.lang.String getRespData() {
        java.lang.Object ref = respData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          respData_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string respData = 3;</code>
       * @return The bytes for respData.
       */
      public com.google.protobuf.ByteString
          getRespDataBytes() {
        java.lang.Object ref = respData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          respData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string respData = 3;</code>
       * @param value The respData to set.
       * @return This builder for chaining.
       */
      public Builder setRespData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        respData_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string respData = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespData() {
        
        respData_ = getDefaultInstance().getRespData();
        onChanged();
        return this;
      }
      /**
       * <code>string respData = 3;</code>
       * @param value The bytes for respData to set.
       * @return This builder for chaining.
       */
      public Builder setRespDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        respData_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Develop.DevelopChangeResourceResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Develop.DevelopChangeResourceResponse)
    private static final com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse();
    }

    public static com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DevelopChangeResourceResponse>
        PARSER = new com.google.protobuf.AbstractParser<DevelopChangeResourceResponse>() {
      @java.lang.Override
      public DevelopChangeResourceResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DevelopChangeResourceResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DevelopChangeResourceResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DevelopChangeResourceResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DevelopToolsRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Develop.DevelopToolsRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>string params = 2;</code>
     * @return The params.
     */
    java.lang.String getParams();
    /**
     * <code>string params = 2;</code>
     * @return The bytes for params.
     */
    com.google.protobuf.ByteString
        getParamsBytes();
  }
  /**
   * <pre>
   *CMD PackageId=9005 测试-工具
   * </pre>
   *
   * Protobuf type {@code Proto.Develop.DevelopToolsRequest}
   */
  public static final class DevelopToolsRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Develop.DevelopToolsRequest)
      DevelopToolsRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DevelopToolsRequest.newBuilder() to construct.
    private DevelopToolsRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DevelopToolsRequest() {
      params_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DevelopToolsRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DevelopToolsRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              params_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DevelopProto.DevelopToolsRequest.class, com.dxx.game.dto.DevelopProto.DevelopToolsRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int PARAMS_FIELD_NUMBER = 2;
    private volatile java.lang.Object params_;
    /**
     * <code>string params = 2;</code>
     * @return The params.
     */
    @java.lang.Override
    public java.lang.String getParams() {
      java.lang.Object ref = params_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        params_ = s;
        return s;
      }
    }
    /**
     * <code>string params = 2;</code>
     * @return The bytes for params.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getParamsBytes() {
      java.lang.Object ref = params_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        params_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(params_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, params_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(params_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, params_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DevelopProto.DevelopToolsRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DevelopProto.DevelopToolsRequest other = (com.dxx.game.dto.DevelopProto.DevelopToolsRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getParams()
          .equals(other.getParams())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + PARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getParams().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DevelopProto.DevelopToolsRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=9005 测试-工具
     * </pre>
     *
     * Protobuf type {@code Proto.Develop.DevelopToolsRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Develop.DevelopToolsRequest)
        com.dxx.game.dto.DevelopProto.DevelopToolsRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DevelopProto.DevelopToolsRequest.class, com.dxx.game.dto.DevelopProto.DevelopToolsRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.DevelopProto.DevelopToolsRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        params_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopToolsRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.DevelopProto.DevelopToolsRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopToolsRequest build() {
        com.dxx.game.dto.DevelopProto.DevelopToolsRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopToolsRequest buildPartial() {
        com.dxx.game.dto.DevelopProto.DevelopToolsRequest result = new com.dxx.game.dto.DevelopProto.DevelopToolsRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.params_ = params_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DevelopProto.DevelopToolsRequest) {
          return mergeFrom((com.dxx.game.dto.DevelopProto.DevelopToolsRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DevelopProto.DevelopToolsRequest other) {
        if (other == com.dxx.game.dto.DevelopProto.DevelopToolsRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (!other.getParams().isEmpty()) {
          params_ = other.params_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DevelopProto.DevelopToolsRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DevelopProto.DevelopToolsRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private java.lang.Object params_ = "";
      /**
       * <code>string params = 2;</code>
       * @return The params.
       */
      public java.lang.String getParams() {
        java.lang.Object ref = params_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          params_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string params = 2;</code>
       * @return The bytes for params.
       */
      public com.google.protobuf.ByteString
          getParamsBytes() {
        java.lang.Object ref = params_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          params_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string params = 2;</code>
       * @param value The params to set.
       * @return This builder for chaining.
       */
      public Builder setParams(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        params_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string params = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearParams() {
        
        params_ = getDefaultInstance().getParams();
        onChanged();
        return this;
      }
      /**
       * <code>string params = 2;</code>
       * @param value The bytes for params to set.
       * @return This builder for chaining.
       */
      public Builder setParamsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        params_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Develop.DevelopToolsRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Develop.DevelopToolsRequest)
    private static final com.dxx.game.dto.DevelopProto.DevelopToolsRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DevelopProto.DevelopToolsRequest();
    }

    public static com.dxx.game.dto.DevelopProto.DevelopToolsRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DevelopToolsRequest>
        PARSER = new com.google.protobuf.AbstractParser<DevelopToolsRequest>() {
      @java.lang.Override
      public DevelopToolsRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DevelopToolsRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DevelopToolsRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DevelopToolsRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DevelopProto.DevelopToolsRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DevelopToolsResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Develop.DevelopToolsResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>string respData = 2;</code>
     * @return The respData.
     */
    java.lang.String getRespData();
    /**
     * <code>string respData = 2;</code>
     * @return The bytes for respData.
     */
    com.google.protobuf.ByteString
        getRespDataBytes();
  }
  /**
   * <pre>
   *CMD PackageId=9006 测试-工具
   * </pre>
   *
   * Protobuf type {@code Proto.Develop.DevelopToolsResponse}
   */
  public static final class DevelopToolsResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Develop.DevelopToolsResponse)
      DevelopToolsResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DevelopToolsResponse.newBuilder() to construct.
    private DevelopToolsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DevelopToolsResponse() {
      respData_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DevelopToolsResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DevelopToolsResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              respData_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.DevelopProto.DevelopToolsResponse.class, com.dxx.game.dto.DevelopProto.DevelopToolsResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int RESPDATA_FIELD_NUMBER = 2;
    private volatile java.lang.Object respData_;
    /**
     * <code>string respData = 2;</code>
     * @return The respData.
     */
    @java.lang.Override
    public java.lang.String getRespData() {
      java.lang.Object ref = respData_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        respData_ = s;
        return s;
      }
    }
    /**
     * <code>string respData = 2;</code>
     * @return The bytes for respData.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getRespDataBytes() {
      java.lang.Object ref = respData_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        respData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(respData_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, respData_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(respData_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, respData_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.DevelopProto.DevelopToolsResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.DevelopProto.DevelopToolsResponse other = (com.dxx.game.dto.DevelopProto.DevelopToolsResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getRespData()
          .equals(other.getRespData())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + RESPDATA_FIELD_NUMBER;
      hash = (53 * hash) + getRespData().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.DevelopProto.DevelopToolsResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=9006 测试-工具
     * </pre>
     *
     * Protobuf type {@code Proto.Develop.DevelopToolsResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Develop.DevelopToolsResponse)
        com.dxx.game.dto.DevelopProto.DevelopToolsResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.DevelopProto.DevelopToolsResponse.class, com.dxx.game.dto.DevelopProto.DevelopToolsResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.DevelopProto.DevelopToolsResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        respData_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.DevelopProto.internal_static_Proto_Develop_DevelopToolsResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopToolsResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.DevelopProto.DevelopToolsResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopToolsResponse build() {
        com.dxx.game.dto.DevelopProto.DevelopToolsResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.DevelopProto.DevelopToolsResponse buildPartial() {
        com.dxx.game.dto.DevelopProto.DevelopToolsResponse result = new com.dxx.game.dto.DevelopProto.DevelopToolsResponse(this);
        result.code_ = code_;
        result.respData_ = respData_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.DevelopProto.DevelopToolsResponse) {
          return mergeFrom((com.dxx.game.dto.DevelopProto.DevelopToolsResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.DevelopProto.DevelopToolsResponse other) {
        if (other == com.dxx.game.dto.DevelopProto.DevelopToolsResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.getRespData().isEmpty()) {
          respData_ = other.respData_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.DevelopProto.DevelopToolsResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.DevelopProto.DevelopToolsResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object respData_ = "";
      /**
       * <code>string respData = 2;</code>
       * @return The respData.
       */
      public java.lang.String getRespData() {
        java.lang.Object ref = respData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          respData_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string respData = 2;</code>
       * @return The bytes for respData.
       */
      public com.google.protobuf.ByteString
          getRespDataBytes() {
        java.lang.Object ref = respData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          respData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string respData = 2;</code>
       * @param value The respData to set.
       * @return This builder for chaining.
       */
      public Builder setRespData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        respData_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string respData = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespData() {
        
        respData_ = getDefaultInstance().getRespData();
        onChanged();
        return this;
      }
      /**
       * <code>string respData = 2;</code>
       * @param value The bytes for respData to set.
       * @return This builder for chaining.
       */
      public Builder setRespDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        respData_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Develop.DevelopToolsResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Develop.DevelopToolsResponse)
    private static final com.dxx.game.dto.DevelopProto.DevelopToolsResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.DevelopProto.DevelopToolsResponse();
    }

    public static com.dxx.game.dto.DevelopProto.DevelopToolsResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DevelopToolsResponse>
        PARSER = new com.google.protobuf.AbstractParser<DevelopToolsResponse>() {
      @java.lang.Override
      public DevelopToolsResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DevelopToolsResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DevelopToolsResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DevelopToolsResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.DevelopProto.DevelopToolsResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Develop_DevelopLoginRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Develop_DevelopLoginRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Develop_DevelopLoginResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Develop_DevelopLoginResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Develop_DevelopChangeResourceRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Develop_DevelopChangeResourceRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Develop_DevelopChangeResourceResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Develop_DevelopChangeResourceResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Develop_DevelopToolsRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Develop_DevelopToolsRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Develop_DevelopToolsResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Develop_DevelopToolsResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rdevelop.proto\022\rProto.Develop\032\014common.p" +
      "roto\"3\n\023DevelopLoginRequest\022\016\n\006userId\030\001 " +
      "\001(\004\022\014\n\004type\030\002 \001(\r\"\214\002\n\024DevelopLoginRespon" +
      "se\022\014\n\004code\030\001 \001(\005\0220\n\014commonParams\030\002 \001(\0132\032" +
      ".Proto.Common.CommonParams\022\016\n\006userId\030\003 \001" +
      "(\003\022\r\n\005dbIdx\030\004 \001(\003\022\020\n\010tableIdx\030\005 \001(\003\022\r\n\005c" +
      "oins\030\006 \001(\003\022\020\n\010diamonds\030\007 \001(\003\022\021\n\tchapterI" +
      "d\030\010 \001(\005\022\021\n\tloginType\030\t \001(\r\022\r\n\005level\030\n \001(" +
      "\r\022\013\n\003exp\030\013 \001(\r\022\r\n\005extra\030\014 \001(\t\022\021\n\tmission" +
      "Id\030\r \001(\r\"\224\001\n\034DevelopChangeResourceReques" +
      "t\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Common.C" +
      "ommonParams\022\017\n\007resType\030\002 \001(\005\022\016\n\006resNum\030\003" +
      " \001(\005\022\016\n\006itemId\030\004 \001(\005\022\021\n\totherData\030\005 \001(\t\"" +
      "m\n\035DevelopChangeResourceResponse\022\014\n\004code" +
      "\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Commo" +
      "n.CommonData\022\020\n\010respData\030\003 \001(\t\"W\n\023Develo" +
      "pToolsRequest\0220\n\014commonParams\030\001 \001(\0132\032.Pr" +
      "oto.Common.CommonParams\022\016\n\006params\030\002 \001(\t\"" +
      "6\n\024DevelopToolsResponse\022\014\n\004code\030\001 \001(\005\022\020\n" +
      "\010respData\030\002 \001(\tB \n\020com.dxx.game.dtoB\014Dev" +
      "elopProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Develop_DevelopLoginRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Develop_DevelopLoginRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Develop_DevelopLoginRequest_descriptor,
        new java.lang.String[] { "UserId", "Type", });
    internal_static_Proto_Develop_DevelopLoginResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Develop_DevelopLoginResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Develop_DevelopLoginResponse_descriptor,
        new java.lang.String[] { "Code", "CommonParams", "UserId", "DbIdx", "TableIdx", "Coins", "Diamonds", "ChapterId", "LoginType", "Level", "Exp", "Extra", "MissionId", });
    internal_static_Proto_Develop_DevelopChangeResourceRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Develop_DevelopChangeResourceRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Develop_DevelopChangeResourceRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ResType", "ResNum", "ItemId", "OtherData", });
    internal_static_Proto_Develop_DevelopChangeResourceResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Develop_DevelopChangeResourceResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Develop_DevelopChangeResourceResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "RespData", });
    internal_static_Proto_Develop_DevelopToolsRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Develop_DevelopToolsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Develop_DevelopToolsRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Params", });
    internal_static_Proto_Develop_DevelopToolsResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Develop_DevelopToolsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Develop_DevelopToolsResponse_descriptor,
        new java.lang.String[] { "Code", "RespData", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

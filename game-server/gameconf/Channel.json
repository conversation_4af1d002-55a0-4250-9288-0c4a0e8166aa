{"game_name": "danke", "payWhiteList": {"test": [], "pre": [], "prod": [10573233, 22252146, 13277676, 30261070]}, "order_test_price": true, "game_urls": ["https://test-game-danke.lezuan.net", "https://pre-game-danke.lezuan.net", "https://prod-game-danke.lezuan.net"], "verify_login": true, "official": {"dxxVerifyLogin": true, "loginPassSecret": "9b4cad4d36433fb80665623d1418ecdb", "habbyVerifyLogin": true, "habbyVerifyLoginKey": "uwxsy3i14q9kodyb"}, "habby": {"habbyVerifyLogin": true, "habbyVerifyLoginKey": "uwxsy3i14q9kodyb", "habby_store_secret_key_test": "", "habby_store_secret_key_prod": "", "habby_id_url_en_dev": "https://dev-habbyid.habbyservice.com", "habby_id_url_en_test": "https://test-habbyid.habbyservice.com", "habby_id_url_en_prod": "https://habbyid.habbyservice.com", "habby_id_url_cn_dev": "https://dev-habbyid.habbyservice.com", "habby_id_url_cn_test": "https://test-habbyid.habbyservice.com", "habby_id_url_cn_prod": "https://habbyid.habbyservice.com", "habby_id_game_en": "Survivor", "habby_id_game_cn": "Survivor", "habby_id_uri_auth_code": "/habbyid/game/authCode", "habby_id_uri_bind": "/habbyid/game/bind", "habby_id_uri_login": "/habbyid/email/login", "habby_id_secret_key_en_dev": "vQ6Hs2diTB7hJjP8C7X7cQGV8M45uXin", "habby_id_secret_key_en_test": "vQ6Hs2diTB7hJjP8C7X7cQGV8M45uXin", "habby_id_secret_key_en_prod": "vd1E3q8chzgXFO9HaZMwl54DqojEK5oF"}, "google": {"packageName": "com.dxx.firenow", "integrity_decryption_key": "bB/4J/pikdTSb1HQyxHQZdbOEU4q2S29zh0cnItY74Y=", "integrity_verification_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEgvPoyUx+d0wTfMJ/AoQnDpmGQbNINzJPCzc6AhCzcBdLJnZtw2svacS4nrZ1/++Wu8iZYMwC3zncGeKwiA5wlQ==", "pay": {"application_name": "<PERSON>io", "type": "service_account", "project_id": "api-7972143603605848622-312778", "private_key_id": "8dc637ac3565d8220cb6aabd96c5c554e2c25059", "private_key": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDr0zSdzxUk1bW2UbgM9+BGNV6lUSBCDNzzaxfrA5f+bZhsDhc5NNJv6ImXHaJmpYPJ/B/+sOtQO5FJ5ehPP2jyI++Acf5xbSjMLQ+7PbvHcQlQ9Wbw1UYYsw7DfH036cEtbilLCFxcrxAtxJg08HXl2s0t8gczD/uLvtgwTavdeVqnawq9iPuAvCiaGq8LHvlitOF6EcS/8ZKOPkWf7L079eaaWRVktSekFwr/OrLXFvaQxIyFspwgAi5/wtzw0BlR5Ipkg5u0BQX1zFcc4dwpul0YIbNWfR0NllQNZg8Q/uRZv9YfDL+8seL+EuwK7Sy9NILLX5qLcg2iH2gQtqKzAgMBAAECggEAIA3ZPQoEybjythFtfvXCWkiu0LGyPMga0EtiOOOIKWEkMqvHpFL1t5hX+15f0Kv2ueG3R5ssoP83xF+nr/3ieedAiqjz+N7piLcnSEAf4gAPUB3LHDkhCgJso02sh4kSZOZwXm6If2CcRlxOhAObQt72vKQFLn+hpWEPaaFlFvlA9n72WT7dDx34PrVrh3GCAwEbfZV91o8zmCXcDcGq5sJ1Ne4rpvxK6jruI5j6EpEV+HT/e25GNz0DyXRh39G7Y1p2H3Q6Jq4/kcX9ye4FV3nDe1rlSg0xLVAVixgVmUnRdUC5S+pIOOZpFjfsD58158pczu12hGiNT227ndOTgQKBgQD6eN1ayCDo3Z1Udazf88bFLgsvlgSZeRR0hykV7wM4Hcs+1CmNLGBTSNsHQnZaxo+c+ScBNh8+6YChS+IU0YObBB8ogHJw05GFxd/T932v2rXYkTKhLQCEioauvw6/npa7e9N3caaKuYqjjhVMwOVrhkzpm9CZNRiJ6XcjM3RZ0QKBgQDxB5YraXldTLQrt4VPWo/qC+FCp3FsrvssTO6r2eaglEyRO3SSwzIskVL+m1dSGaJZM/seF+NS8Eq/QEnAwm2fYQeq8iODiWueiLzRxqz1jFAvxlHMA3sfn+DHIDK8RI60ckPGk4wFykp7MeMeGGQ/dh79CvA2vJHKw0KtmVhRQwKBgFwWWdPJpofXCsQFq7R+7QKLxiMNtc0zPfkFxG5C/N0B3zrPSCOWQCf+rkCCB/+h5yUgWV3RpzU7JkanHozm5sr0ffwnFWwz7NnRAVTuNjCUaa0g3Qty/zC7uX6/0c1Tojl18i6FA2UXiEhPsbcRaIfazaZJ2iyp38a8pkez4EAxAoGBAL8m3CKrsHOc7FqrXg8z7GSabViWQO8Hyu00SiiCVFRPqHWZqHboVTdhPWfsvJDBAa8q+WHBYiiZpz4L9zHnQFcg9ZrOE/oyluYQ3Wak4PCQCdwJtT9OF2xaOELTt0j2OhLDeBrZUzgYWX9gbhWlFIP74tGdxioy3JDo0E2rCo7PAoGBAN8I6PbrOU/L8eTEXh608aLB7WVyjfkvczynQtbL04Jt0GeDVISyf38VUtsjJUMqngxkoGzCTNM/aANuEJgHmcB6OTig7GuXDI64vwyJqnSm/UAfoSyoPP1AyULpsz7X/Y/A1Oi1jTln2bNqBfOel6si50ZaAQUQna33juS4lIwc", "client_email": "*******", "client_id": "108671111669363706622", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/survivor-new%40api-7972143603605848622-312778.iam.gserviceaccount.com"}, "account": {"verify": false, "applicationId": "************", "clientId": "************-n3bmice3249ki39iuj0ovas1hn2au396.apps.googleusercontent.com", "clientSecret": "4WxIt0X4JrKD7HC-9mFIdVeL"}}, "apple": {"packageName": "com.habby.danke", "pay": {"verifyReceiptURLs": ["https://sandbox.itunes.apple.com/verifyReceipt", "https://buy.itunes.apple.com/verifyReceipt"]}, "order": {"kid": "U5CV8LN828", "iss": "bcf0fb47-b6f0-4e6a-bd17-c9dc7eb625cc", "aud": "appstoreconnect-v1", "privateKey": "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgMNPJl8wRgYZrAc9hseUzM95eGxg/E+1egyz09Bk4wy6gCgYIKoZIzj0DAQehRANCAAQbbvwbXxJhdnoM63/5SUWRp6ljjhoxkC0GZA3Zk7F/M7T0WPY+WJ+JhiAVmfK48KfYsSSnQAeDECLOBNDIS4qJ"}, "account": {"verify": false, "bundleId": "com.habby.danke", "signatureTimeoutInSecond": 259200}}, "one_store": {"verifyReceiptURLs": ["https://sbpp.onestore.co.kr", "https://apis.onestore.co.kr"], "consume_purchase_url": "%s/v2/purchase/consume/%s/com.habby.onestore.archero", "dev_host": "sbpp.onestore.co.kr", "product_host": "apis.onestore.co.kr", "url": "https://%s/v2/oauth/token", "expire_in": 3600, "grant_type": "client_credentials", "client_id": "com.habby.onestore.archero", "client_secret": "lYeAX7WyrnKlAGkOCxDrKgQsOFBwW6DbIMZKRHPE2vQ="}, "weChat": {"open": false, "appId": "wxf2966096a150", "mchId": "1699963", "appSecret": "271e86384f50585a0f292a8bd7837bcf", "serialNo": "52E7038ED27DFAC41392B7429B4D5E31145CDFAF", "certificatesUrl": "https://api.mch.weixin.qq.com/v3/certificates", "apiV3Key": "523e90f10c82b621df794c0f8c2533f6", "privateKey": "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCn7N7powDn0rYuYhu8ff6qww+I2B841kE0mAaQFejN6AXRGXSrhHNjN2YHEP+IWuPJFFiQS/K/XMQRbSKMjsNbz3Ydpi+BHf8NNX8E47f9+cshIzk+8L6W8EDVPwBbsDxyvjqD2k2VaSJpEZMt4ls+/v1IW4n3zOdWStWz81w4rftk/iyLHrRmdINOm2rP2uEGQMDCheEsCBwW0lgtdimX5wpXznyLFaZ/sQGuvppIT3tPPcyAOJQXJ2PGgyb/RKUsVTocmf/MQKppSGFMhNBK50LjdiOxLyWLUy7gMJC3WjlVzuZEAqmP7PTQ9tYRx49agUkuqE7Wq00LC9wTkZjlAgMBAAECggEBAIDupwy+QgC80XJ1k5Q7LSDRmgjY38gVAYWvQEK6/7+w6V5t7B2RGcNREr5UN87q8kA+pe1M0vpI8KXspH1FVAwXEzqakZjSfjUXIQqRCYEwnN05nUeNMtjTaQQSVmUPwKndEzKPl8gjK+9+NgIh7StGPxSibCFeKzUfSkvbi7Bb7uiens9UFDYCqxdmaJJpvtyXHo1AQrWn2Y05h8PPUgb0Wkc4xapRon0C0Znl+Zlx4gZ43oVM7BXH208MvRolUCxagM0e1mIpBVJVTZV2mr1W5wxMouLpVvPUohJzqW80aoUZEwcHksWyuXSzN1y2IyMPcXlJgYMK0dWUNS7TJJUCgYEAz9VBrBiHKK3PJkL+Ij3u7zrBemK0LOtyFgMw6OTBgNsV8YbME/Jcr8uyheOi0UHDwDYNw9dIM/rqYuCG2pJwt5CeAzjasXSiEOUGimvqayafI9GR5K7muilrAc0EnDmeYg9Vba+F8SZ9CmjNCGng+PdhJZVpMh6o8CJ7vndc9rMCgYEAztfimaQyMbJfpfriTmlSS1V8lTbLTyxmodoLV5RU4cR7VccMORf++rqpi0AzUVQvMPB2X8xoNJaAnIQ+UumNSTioZhF9wCAJPLfTj+7KkrS+iXmL+KPtniKIljVgaH4sCv9p7Y1geGI+9mlMqv6r2kEGoJ273WQwt1UXFZJavgcCgYEAgLHndq/Bkhj+uwyk3YNIX28cu7I/K8mn3aWgNbbhc+82SVdVK8zaRCl4pJvvCHEWzAEsyOaYWUe4t/2cw+AGWET+87PTCCvW52tKf5CPmkZSKRXup5L+YKyhDH5tqmergcFI/yC/ajKRH08CF4Gow8I7r9AzUcvWhMx1n929/S0CgYAuPY734AAkogbF+Vc8RklSdBL83JoI4hkzl6YvdwaezijwXSY7YUTsUmxyp/CQ/uvp2efstKqbDwZDd0Djkji2pMbowoJFQCQwgbv3EmAQ+f0aiKSk+ii10GyOf/JEiehYsQ7CP3fQkc4XkiTEmVhb22ua3ohQDwThB1G4zdYhYwKBgQDNGng1YhF/bI9m7gKFoE1gg84SKLpHRrpE8psATYvYe0VTzT6KoqzxBio3ZXEC7Yx4W29doTPAWDc7G5Qb5ZaLFyOncTcykO7odY9gWqHAA2MQnUyaoX5w8PkU6/PrbY2/2vXrBCUC7VQI3rMfbgs5Kr1XWulaptRb1qtATbwWZw==", "doOrderUrl": "https://api.mch.weixin.qq.com/v3/pay/transactions/app", "reqAccessTokenUrl": "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code", "refreshTokenUrl": "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=%s&grant_type=refresh_token&refresh_token=%s", "reqUserInfoUrl": "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s", "refundUrl": "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds", "appIds": {"com.hd.xxdkhb2.and": "wxdc886b03fb793290"}, "appSecrets": {"com.hd.xxdkhb2.and": "e93cfcea2dce426f7c6755d90841e3bd"}, "out_trade_no_type": {"com.hd.xxdkhb2.and": "csj-"}}, "ali_pay": {"serverUrl": "https://openapi.alipay.com/gateway.do", "format": "json", "charset": "UTF-8", "signType": "RSA2", "packages": {"com.hd.xxdkhb.and": {"appId": "20210031", "privateKey": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC5uCQ8gMQUF4pfvI85KTlBLbQw3/wR+Sa7ScKSPBqfQ0b4ma0efIPiicqtaZORQSOsGjZynTU24Fs6zyHIVr6vLl1oFTF6/sPFa9H+YvAWNbntz1TpcRLkd94Ghn8x7xChed0Qh5fh+UswuFI/zo6jIB472GT0vQG2d73s8G6hV3fLBVf+ifnSWPhaJ67nX8ZGHPKx/AlEhpolEv6ZEPQr3B/aO/8lFAksRoLgT6P0mmXeM+gMgqnBNKJaCuHi+4cdTJ4hCxAhbAP3VcYl7YpMzWpOx9/mHRy7iLsvKdZmIQq5YYbVllzU0lL28bPsM1+z/JLlOmSi8UCgi8VFA3CBAgMBAAECggEBAKPECsAHU9/bujsdjLX88e3VnXUTrbUznMG+EwLcWSVjL+v7pzqg1bMnij9MP8HwTMJwAt8iXVBcmlXLXKDKTS8wlVE4jv9EM+GcZ/oHyGntSly3XMrU2LH6OSBL7VFc269oRFVVG8s6BuGZR33YaNLTeZWQBcYB/Man5CRRB1zgwYiOA3GfGMWPiKPJdy3XqhF77rKrCNIaGkLPfJM/iTHO2Am/QWHjIBFfWR3o8OcSCw3Vv8jtU91bkhKw8+JIbdsGOB/NEKw6GJz3vGqY0q06G9P+NJ4wfyY7rLYY1V7/bPC+DU9aPx9onykx0wBW4IZ7aiPz3zmwcFCR0b6cEHkCgYEA4fYO1wL2Zo9OPBVvKTgX8hZ0eEmD1SInZqZzFUsbmQriZQnTJXfnbkoszz/hBoLi5soZ0hekYbcRF+68JT6j73WI+P3kd6GWv+q6yOWbPsLcRPkPQ/cFk51Lw2mMbVpp1Km2mTH6Pj4DJwYryC3W7CigYch93uD9lAch4MI0G8cCgYEA0miRIa5RWzr++k6LsmcI/xsssKu9/lVWYllu0ppRwqOg+Ww4AIDY2tR+mirUImInr48kL/34mZb7wSTndQznDCdKN+E9C0L/zB6/m+ZYmPaVpbzNnNhBcdu3VVcBBZsLNb3Tmk7H0DEDSl/Q2A6D2vUMHY2L5Ts3L1Gz8vxbQXcCgYEA03rwbqnWJSzwgulorYJTXv9HKKFdYmfhyn/0a7Rs8vKt6vSkCohLMPz5LUtS9z4e0PPL8Max0cZ9mBGXQ6B53A78EFXHs3On3Jz7DjVe9AF6GI+liu2x6XMpSo4JoPsi26evP3f4BrMRTj/HUEYexc5MNNhpfeTDT/zQ50O7c1cCgYB+ykxKg4S6+P4VT6eWxcgCEhoIDDXdQQdhFItXlW6XX4rtQm0swHfsxgfUVuKmJYfMnvErilt4Eid66AYh3riL9lEfAzeJxcHm+tBz3YaeBll/Ghq83QPy3HfJkTgobJmAtPcCEV8O8boN41cyGu0R0ICQfm9Cn0BN32oUjkVfWQKBgAuCNqOYRfaaGIpIey/TocvBP2P7LbKZHSgkPfTNnCATw+T7ejnv6BqdPMBLXbcCM7vTdWndXvtbFhrX31/fUyWPyN6fGkxGJvJanKyMEgC3tYkzCzVt+hGCLof8N5hkfhiTdzKI0VhaCqQmmlkRNH0W2laDQryQdJpXmJ/DuIpU", "payPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgIsCPqcNJqrotaqS8vQxNnWuuQ6vXqSd1Jdlramc6OLe5xMmY6eW9K83KsI/pUvu3JzqcR67XDN4qlOil55S6real60ZxvvLWzYLKb0gy12/I3G4+h7KR1r9xAQJjzeGg7woG3jrfYj23ZZAmdrKrhtCcJoxyYu2WHNVvuH9odLT92hZ+5W21SCDplRQAjCACfJW0iungnPOoQNjrLPZrDBE1A7siniDOovdooTLGjpQzzzim+t6joHdtC8O+7sW6UMJ5pXdDn9HJjLteJUUByclmcyf2UGao4z+Oi6u7BIUmP1dknA5QHiBhDJNyrSf+NLT2k8ZY5SxPrnm+tBTtwIDAQAB"}, "com.hd.xxdkhb2.and": {"appId": "2021009191", "privateKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCbGG/yBg7dLhzaAFMARWIYPzfLumOy7nsc7ljlelzOZBqk7cHNwi+lkvZ6D52SHyahSrUIIlPXWjHH/Iq5ka2wN0HyKgesrmhQ4SacPmophoiOTg/+pCnh8vFjNj28s3Od2yq+w+LBMXNj/RYP7nPATvZmdm9rZUDo0vBCsYKiraeGmYvINUXGuaaoUZ8fz2/hZVLIN/Gke5+2zO2NLz8Szx5KQfOSWFYilVyCoDNi+F6PcNd1vJ1Y4Tv8JnLcUv6r1xEssdSHMXcJ9//YSF0VOle7GO0ZIr5F2EaKcSLdFaSJNjDjIA41v4Ytgs/o6zRTjYqQhKwvbANcOMJipvrpAgMBAAECggEACX6Ze8t7JXcpNP9IFYAp3SohlEs4xz9qMf81Wfb/r3LZXrdfYeU9XXl9tUM3djaJ+t3D71eKomhXG9MN+wY8B8maM+T2y09x+2GNJ4t6nEgsG+oTeJc8KSTiOhyb8u1ZapR8LWl4fDM2ySw5KYiDdYZf9y3Q7FYqfxZKtwp+4M6ASN6jGAvl/b1pCKWYbWGYkZB0zv4hJ+UsMyBcP0TSBd36q0hy69yONSWlBBR4JLwDkP9jMDQPYZfMaZZV1a64fZhAcVXwjO6VYH3FIaeQUj0yHj7M/89OOiIrAYgskG7WvP2YhLs2CsEd6MoHag16oPMSd7CvSMp3fA8gng3IcQKBgQDJ3GxtbtPur9DW9L2zKlJG+tMFQMxbtS+sfceORALXhQu3R3DGcDcz2wX77tewdAMRImUywm+RpoJGtb5z3mEZ7lSLsEdCtqmBkX3kaKpfpfRadzIQnS4tTWIRak2AVMan2ZPS2EK1R2umBHbZCxBsT3TpNb0BuMU1hSNBqM59xwKBgQDEsSLpWCsZuo2EBVcbf3EKEiayow2mZVJTK/fif/sP1j/sKatCzvTYqdWvFFOZko2oeuUXU6LUt0XRMtVF8isuvPO3J8gexqHYbtUfTR28fXg/aBYguG0pgkNX20c747hzotnpgdIyAot5bz13f98TqmaDkjhKJWAWKmECT0KBzwKBgGciY5OV6FovIfhxmuqLWPZC+MXaUY8Zh34N1YNuZDVRGKdPrOL2On4YDXqmkTTqURbUB/hjNzWnaTBPczsoB1yEVFX6fq4rPXehqjyk7UdpNutxsvaiGBG1U8GqGmbS6ZygodI8A/kywGFWLpK+AGoWPplYh3T1vmyV1WmaocXJAoGAb943dvPYf6ekuUO4SUFwb0Pnko4+Qzc5XWiScdZMWrOGY7FHYJYIl/nwOMWczqmVvA85oMzGvakHInaA6mlTuh9plfAgV7CteEWBHW5kzUGnVJjEjROXspYN6zgiCaP40nv66F4XyK292fE0ynSF0c+Gwqo1LaaYQcMUrZWaw+0CgYBVtCDLuZkh2kOg3cfRskFfpYc4p8riBjXk6wqrg5PcUKhhdFFt9MDvBV6XIaledka03tCo50GfdgDjrNKXHcVDne4UIKruV97Dw+0Cg74SatlrSlnB6YTl7cfrgWdoEoeHL16lBz3wl3gJOSqBqRJ0DNhEZPCzTbUKQ+HI6kA6+A==", "payPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgIsCPqcNJqrotaqS8vQxNnWuuQ6vXqSd1Jdlramc6OLe5xMmY6eW9K83KsI/pUvu3JzqcR67XDN4qlOil55S6real60ZxvvLWzYLKb0gy12/I3G4+h7KR1r9xAQJjzeGg7woG3jrfYj23ZZAmdrKrhtCcJoxyYu2WHNVvuH9odLT92hZ+5W21SCDplRQAjCACfJW0iungnPOoQNjrLPZrDBE1A7siniDOovdooTLGjpQzzzim+t6joHdtC8O+7sW6UMJ5pXdDn9HJjLteJUUByclmcyf2UGao4z+Oi6u7BIUmP1dknA5QHiBhDJNyrSf+NLT2k8ZY5SxPrnm+tBTtwIDAQAB"}}, "apps": {"2021003129658681": "com.hd.xxdkhb.and", "2021003143629191": "com.hd.xxdkhb2.and"}}, "huawei": {"isVerifyLogin": true, "pay_public_key": "MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAr8TyPfCVlJFITI2B07UBZgKDN1QY8VWhYtTQl1Qcd8wiiGSGfyNf3eyPA7/FC+0jF1iDqn9Im1psTSotnU7QDVe2FMCsUvppx0VGPJy9Jx0QvXCluwuwx4xxEhF/nnZ/Srq8aSP8IaQRLTJ+s1pUHh4CEMQB3gz/jtkgIB8uuSwgb4TTejFir+q2x3oEh2tWcjla9I6PVLcRMoHjA4iFloJ+28nowtyUvcnfDs6Xwcc7+YPCDjZZFYw1xijYlX1JaYNl39erzfAOBeHq4fsP30YXVTVv+9PyFo1xNse5IA8bBO0Auyf3aQ2pDn8SrkavrEIwtxU6tl3dzYFkyDIbvCX/yHuWrjcNkybIsMJkdsJBsY6oN0NkosGJI2qbyyvejcQKRuMHFHlAvsmYwy2OwL69yRONJHlt8/gn/vtM+ntRn4MV9ur+A151XpAZri0YfgIH6xB0/il1fJfHcd4k3rmpo2g5fc//zcYDviQBk6mZVkE9dUAwFTDJY8PsOFgpAgMBAAE=", "token_url": "https://oauth-login.cloud.huawei.com/oauth2/v3/token", "check_url": "https://orders-drcn.iap.hicloud.com/applications/purchases/tokens/verify", "confirm_url": "https://orders-drcn.iap.hicloud.com/applications/v2/purchases/confirm", "subscription_url": "https://subscr-drcn.iap.hicloud.com/sub/applications/v2/purchases/get", "app_id": "1012531", "app_secret": "4890ed0f00b7f9491dcd8da11845ef0b6be20e4f6d8409b934b91b", "verifyLoginUrl": "https://jos-open-api.cloud.huawei.com/gameservice/api/gbClientApi", "cpId": "285008600157", "cpPrivateKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCbiGkoISPV9eSq8KUXf8mea0kMlETr3AuDwDTOqenQc1GghDenI35vBe70naHCiKdAeQw404NkZo1MBVSzeKfM8L5AbrqefgApKqUu3aotwPdv72mKRRLfs/YyAFkoe4jf4JaN7X8+fvBep847zjXFLW3PAOblOeQNd85UbcLwyAXezWvXp0IwqJSxLFFOFF4nVOLxeLetu/j4tlrjoOi+cVBeCTY6/kGZqQIGzOZf8NFnSqbpUMfLWNCWrQuMtlqMw62TyD4ZUguqLhdhdYoCvsAWHhMfGxB0ohdsFpwgeCGgaUpU69XTaLUE1GQyi/EYuFh8Z0rdcbuCk+ZerdHvAgMBAAECggEABQCyQTQvruYl4Mm0xbi6h3nz84+hV+TQCgDYsf0NJENRUNRv5dRicLfEI+IeHd+/YXeDSSCkjUovKU7SlqM+4nyr4e6t2gM7hhd1ELjYjHg0fUk6H9Rw8vka5ijUtVSj5m+W6CHHgf8bRDAplgrf9+OqMIpa4uylxTkhTd3p80yu4Kd9h9YH75xdPEhkgptrE4g1LXoenV55WP32Xwm3gthn6EeywbE+0AtfRDvn+eLzR0R24ew3qCwmtSOkpMyAjSiyTbW6PQl5HnfVXQxPseh5xge5XVSNYy9KP00QF7tqoe6XtGT6yWJJSvq8o1ot7q4RuU2Hwxg5RIkf0zskwQKBgQDZ9ejnSjiSzKap6TvHpb5e5VKQhRnlkkswT0ySTxOBrz+tuTSJE/tOh9HNyLj6G6CUh5J9Om+QlmR19zfRvTYdqC7sau+1QvXUF1xTEq34EknsVuDDvR1v/Ni6lon1LxvbRxI2XlB8CXiWPtwEAy/NhTLPWX/imbRFK5/1g7ZcpwKBgQC2rVdhLFT3kh55hLSxIFRV2iQ9VKcwaIuoLhyfp3yTrok5onMnZazoOeTjpxqyunxNHuaJFvGKOdXi95UR7IIEV46BO00gD7dHnGtKRdUU08Z5k1akv/89ZzAr8VJu+58GEFN6lV66p83KlOdrlvji3Vu15z63XnWGRx4orPGheQKBgERzbE+nCZlg2D6oVvPeY6kd0A4/HuDwTti/LscMOo4R4afHcG5Ubio9kXBVR3KU1puO8DyP65W3BZneqBMgnyw1M3dgJiQBAX84TvBv/17XL4wfxLr0W5heChqfSHhN8SuuaFPWzuQFfW+CEkrYTVz4Jfrkmrsplmnax4Otd8olAoGAXJJtFF491JOkg9ofIx+J/VOlR7stWlewZhKwCKv8cm5hNea/yjg5Lhmgh0j4O5hLQ12kX9ZTosN6QyFJ1qJupLqmCZE+nT+5lj2LFUFcIMjM7HEg2C4/ryMqgyowpvM1/AGr/BPTxG0i2UUqHRiNmZGpVv4PhycEEX7apqw1aqkCgYAu9i/XuyVDO4uw70ZkFPqgVm0MtOSZzgymy9a2/nNi5n0Sr+h+Y1rCx+G0oqDgzl8O4qzzrXXzXL8I9at44+GrYx9qrlVhkX3iu0/3779XewZgwuTC50Q4uQwZ8H90xPxTk1zjMYLIn/1kguZdHNameKIVRQUyzKBrFtlzeghb4A=="}, "oppo": {"isVerifyLogin": true, "appKey": "9638c44c689216bb9ea62539be", "appSecret": "a4558874d8aa9416fd927153508", "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmreYIkPwVovKR8rLHWlFVw7YDfm9uQOJKL89Smt6ypXGVdrAKKl0wNYc3/jecAoPi2ylChfa2iRu5gunJyNmpWZzlCNRIau55fxGW0XEu553IiprOZcaw5OuYGlf60ga8QT6qToP0/dpiL/ZbmNUO9kUhosIjEu22uFgR+5cYyQIDAQAB", "notifyUrl": "https://iopen.game.oppomobile.com/sdkopen/v2/cp/deliveryNotify", "cpPrivateKey": "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL0xvI4+FkfKwrXV/ZMRe6c6VC/mFx8B+UV20xwTp44qInzAqhHO0Bq3btMTKiCjnYuqJBc8A96qiFYZv9uMVItAFwjqbNvmoWMV/3qtJ9MRSxNatE5aHHUi1BPGJWX/bG/SlnZ2n9E7/gAZd/s46+QuB3DU6+GmlSLaVpTIjIJ5AgMBAAECgYEAtAOf6iPPJU4ah9Nta8LFye7o7Y5bT4icOmj+I4fb9JvIprTW3QzP26vPSgzBF782Bw7DOkxDTlkqUeo8L9TNFvJc9nOpoIuGnawaqijFFKGZASNivEZU95qW9UFLPggxJZXa1u4mZP1uyYlkHzUFY8N8ePJ4oQ3scyzql5NEgAUCQQDodx4qMVgpoWY/kngD8nkwljM1Q0nhiEZhm0MOX9MUxEMDVlPxRlMBNLLUioZr6HEfMfTT37Hxr0RNPm9fvvTHAkEA0FkmJBy9XoFlpCdl9NIPfUaB3DXB7BygKfoZDLwlhcUwUuNh8OjYOYO7YiSX5KVRWVy9KCttHe+65wtgV8cOvwJAClxsysKiRFC+Ru0XTk27WxAGvkP+gf/Z6zXJWlWpCc7pkbeCTEmvH2mi4tg9v4d/qz1FxT++JaCDMdIrkp/nNwJAWoSw3nWiDIIZX7VCRZhR5HKSjtLIOsXBDBu9cFv5zWF7ejeR4LG451wFOEF/wHXWnwV8pQwg7yWtRR4bMGeqTwJAdpOaY/BdIXCEPGZRauMncvk47AWD/dh7oFbkuC1l3B1ZIsuyLzCa1Byv8/OSfd975D5NlqhOOSPI7mAuwRdpdg==", "cpPublicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9MbyOPhZHysK11f2TEXunOlQv5hcfAflFdtMcE6eOKiJ8wKoRztAat27TEyogo52LqiQXPAPeqohWGb/bjFSLQBcI6mzb5qFjFf96rSfTEUsTWrROWhx1ItQTxiVl/2xv0pZ2dp/RO/4AGXf7OOvkLgdw1OvhppUi2laUyIyCeQIDAQAB", "verifyLoginUrl": "https://iopen.game.oppomobile.com/sdkopen/user/fileIdInfo", "packageName": "com.habby.danke.nearme.gamecenter", "loginInfoUrl": "https://iopen.game.oppomobile.com/sdkopen/v2/attribution/loginInfo", "orderInfoUrl": "https://iopen.game.oppomobile.com/sdkopen/v2/attribution/orderInfo"}, "xiaomi": {"isVerifyLogin": true, "appId": "*************", "appKey": "**********", "appSecret": "jL/FmyG0ZTzYbv/kg==", "verifySessionUrl": "http://mis.migc.xiaomi.com/api/biz/service/verifySession.do"}, "vivo": {"isVerifyLogin": true, "verifyLoginUrl": "https://joint-account.vivo.com.cn/cp/user/auth", "appId": "1057255", "appSecret": "84a80dd1408c9d43b2fb5dcf7a8", "appKey": "f61637c964b10f759b67533"}, "4399": {"isVerifyLogin": true, "verifyLoginUrl": "http://m.4399api.com/openapi/oauth-check.html", "secret": "6db1cefb0ae38d8d6c5df48"}, "uc": {"isVerifyLogin": true, "gameId": "30902", "apiKey": "78a72026544f764e7db5ba", "verifySessionUrl": "http://sdk.9game.cn/cp/account.verifySession"}, "bilibili": {"isVerifyLogin": true, "verifyLoginUrl": "http://pnew.biligame.net/api/server/session.verify", "merchant_id": "1416", "app_id": "8236", "server_id": "7405", "server_name": "bilibili区", "app_key": "a1817ac634a021ba5a9fe9403b", "secret_key": "43646b509bd0a9f2bfb"}, "yyb": {"isVerifyLogin": true, "appId": "********", "appSecretSandBox": "OXIFNHqcElz&", "appSecret": "IqEkLPFaXnyp2d2UPeh1CHs&", "sandBoxUrl": "https://ysdktest.qq.com", "url": "https://ysdk.qq.com", "buyGoodsUri": "/mpay/buy_goods_m", "verifyLoginQQ": "/auth/qq_check_token", "verifyLoginWX": "/auth/wx_check_token", "buyGoodsUrlSandBox": "https://ysdktest.qq.com/mpay/buy_goods_m", "buyGoodsUrl": "https://ysdk.qq.com/mpay/buy_goods_m", "wxAppKey": "22347b23da307c01217667202d6ebb54", "qqAppKey": "OX9Dt5wIFNHqcElz"}, "kuaishou": {"isVerifyLogin": false, "private_key": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDr0zSdzxUk1bW2UbgM9+BGNV6lUSBCDNzzaxfrA5f+bZhsDhc5NNJv6ImXHaJmpYPJ/B/+sOtQO5FJ5ehPP2jyI++Acf5xbSjMLQ+7PbvHcQlQ9Wbw1UYYsw7DfH036cEtbilLCFxcrxAtxJg08HXl2s0t8gczD/uLvtgwTavdeVqnawq9iPuAvCiaGq8LHvlitOF6EcS/8ZKOPkWf7L079eaaWRVktSekFwr/OrLXFvaQxIyFspwgAi5/wtzw0BlR5Ipkg5u0BQX1zFcc4dwpul0YIbNWfR0NllQNZg8Q/uRZv9YfDL+8seL+EuwK7Sy9NILLX5qLcg2iH2gQtqKzAgMBAAECggEAIA3ZPQoEybjythFtfvXCWkiu0LGyPMga0EtiOOOIKWEkMqvHpFL1t5hX+15f0Kv2ueG3R5ssoP83xF+nr/3ieedAiqjz+N7piLcnSEAf4gAPUB3LHDkhCgJso02sh4kSZOZwXm6If2CcRlxOhAObQt72vKQFLn+hpWEPaaFlFvlA9n72WT7dDx34PrVrh3GCAwEbfZV91o8zmCXcDcGq5sJ1Ne4rpvxK6jruI5j6EpEV+HT/e25GNz0DyXRh39G7Y1p2H3Q6Jq4/kcX9ye4FV3nDe1rlSg0xLVAVixgVmUnRdUC5S+pIOOZpFjfsD58158pczu12hGiNT227ndOTgQKBgQD6eN1ayCDo3Z1Udazf88bFLgsvlgSZeRR0hykV7wM4Hcs+1CmNLGBTSNsHQnZaxo+c+ScBNh8+6YChS+IU0YObBB8ogHJw05GFxd/T932v2rXYkTKhLQCEioauvw6/npa7e9N3caaKuYqjjhVMwOVrhkzpm9CZNRiJ6XcjM3RZ0QKBgQDxB5YraXldTLQrt4VPWo/qC+FCp3FsrvssTO6r2eaglEyRO3SSwzIskVL+m1dSGaJZM/seF+NS8Eq/QEnAwm2fYQeq8iODiWueiLzRxqz1jFAvxlHMA3sfn+DHIDK8RI60ckPGk4wFykp7MeMeGGQ/dh79CvA2vJHKw0KtmVhRQwKBgFwWWdPJpofXCsQFq7R+7QKLxiMNtc0zPfkFxG5C/N0B3zrPSCOWQCf+rkCCB/+h5yUgWV3RpzU7JkanHozm5sr0ffwnFWwz7NnRAVTuNjCUaa0g3Qty/zC7uX6/0c1Tojl18i6FA2UXiEhPsbcRaIfazaZJ2iyp38a8pkez4EAxAoGBAL8m3CKrsHOc7FqrXg8z7GSabViWQO8Hyu00SiiCVFRPqHWZqHboVTdhPWfsvJDBAa8q+WHBYiiZpz4L9zHnQFcg9ZrOE/oyluYQ3Wak4PCQCdwJtT9OF2xaOELTt0j2OhLDeBrZUzgYWX9gbhWlFIP74tGdxioy3JDo0E2rCo7PAoGBAN8I6PbrOU/L8eTEXh608aLB7WVyjfkvczynQtbL04Jt0GeDVISyf38VUtsjJUMqngxkoGzCTNM/aANuEJgHmcB6OTig7GuXDI64vwyJqnSm/UAfoSyoPP1AyULpsz7X/Y/A1Oi1jTln2bNqBfOel6si50ZaAQUQna33juS4lIwc", "public_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjKrBNoWeEELF60ZrmW3fANu/eOPXyalG3QzGhBRU4KhoWZJyGBM3XngAne5zk5rWoRG+PL21vwUsVF10s5MXX6s2fcYNOQIZhdr9X4pGxSetq99xUBvEepoyy2W2dGDoICBJCiJ0Mf1c5XCVvvSg6UZAu+mr+5IlW0NcHLCaBB45sMAwB1FRo5RluabDl7dBX8BWLG8r3+x8RrEBxlounoJiOsshxbtyk9hozPZorPUlWfoKNGvnw6w652zXnVouZ5ZbP0yR0QwYbAVOE3WrIcjUqt4h4vFarZOpIF0UVwmEElM7NEv5+eHTijwdB6s9dvKCYA8/VeIY5jX4hZtFJwIDAQAB", "app_id": "ks651614572956213636"}, "douyin": {"isVerifyLogin": true, "appId": "429", "appSecret": "SZiSnRCHcpCZC8Zwi7V1dYcM", "loginUrl": "https://usdk.dailygn.com/gsdk/usdk/account/verify_user", "preOrderUrl": "https://usdk.dailygn.com/gsdk/usdk/payment/live_pre_order"}, "honor": {"isVerifyLogin": true, "appId": "********", "secret": "82e1bdcbe0491a44eb0b08fb24082c318fad23c160c9566faef72ef9c6", "publicKey": "MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAuowJfFELKMQzYQ0SP8JcjwD/1MsyxAUbWZt/fZbeZEXVS0Yw5b4PFngLnLFFydiKnzshryoFM/Ygjod0GlEYQakL4IxHEpS0yeU45rJD94mZvZVLEpZ7AqOdnPzaeVEM1Hat5Cc25qmtCupDO+is0mff/PuHxVYRjgAYbcBMmqI2U+V8BezIov46xsMMEcbJBA8ZCkAiZh48b+9RHokIocCod7FQF6bR+mC7ogqA8XNTlfZKl/0YKcV7iSSYyQLGTwqsD2fM6pGV/Z/R4F2Si1CP8hKgWRZ8UeBFS1pRUlEfGkBOzae7/yTwARaqN+iGCxx6Gx0Vwi0uCLe0wB7d2Z1+P2fNCD4HTuCUYEhTsgW6siTh5IfxUYpuZZw3edaRO6QhB0HYf1YIRZN+1tMK8hDX/zjl9LDfnFssvZSDoKhUTCmZ5zCA4YVJR6lBHFIWFCv86NOwiVz3Cax4MLqlPWtxgrwnu9jcjgkTkv07HD4qn4I+U6UTsxDgkyYKtR7hAgMBAAE=", "loginUrl": "https://gamecenter-api.cloud.honor.com/game/service/cp/v1/user/auth", "accessTokenUrl": "https://hnoauth-login-drcn.cloud.hihonor.com/oauth2/v3/token", "verifyPurchaseTokenUrl": "https://iap-api-drcn.cloud.hihonor.com/iap/server/verifyToken", "consumeProductUrl": "https://iap-api-drcn.cloud.hihonor.com/iap/server/consumeProduct"}, "harmony": {"isVerifyLogin": true, "req_token": false, "app_id": "57658807853111101", "client_id": "109701", "app_secret": "18a07ecf3bfeaa93e4a192e09d2f435638b63cb60d02fbe9cc89e3e06", "pay_public_key": "MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAk680HBJ4d9r8rAHt3zoHN41p2eYWPxd0wsrcg3zxwOcGk+GQBL4pNAA8Vp0g7gC7VC8cK4WPYzVMM8K4zUkhIy9Hi0fpOqnxO00UZhqf/R3GS9lx5BVubUcM7FHsbHY9VxhK33Osj5+NHaG7hFsx/hA7+wbdGRcfMbGI20nWMgZy7/Uzv3kUvJbaFtGUJBDQA+xLeAnZHBUoz/BkFtr7ia5CX+0DkA1JVqIPdzail07a3k3wcSuJ0Vfq0sazGpGn8wD33DgxJPjutmt5z8nsTntL493bWvtTA10plYZyFItAN91FNabfdLw8JH7J4372uyIEJ3WKCNAv7DihuC484B5Cpxl48ht7eDmZe9MwWm9gbWNxIF0fHWJ4HI+/9bNpW0PtZFqqfGLXIxy5ziAaWA6aqNp+e2B1laSZ87HKycvFHBBVldxVZtgOcBtgixV3jovQQ52/epdYL2dos8GQ0jR44wYjnaOa+YShgZ/fIctpYR0Fgxw+lTOnOsePSJp7AgMBAAE=", "token_url": "https://oauth-login.cloud.huawei.com/oauth2/v3/token", "verifyLoginUrl": "https://jos-open-api.cloud.huawei.com/gameservice/api/gbClientApi", "confirm_url": "https://orders-drcn.iap.hicloud.com/applications/v2/purchases/confirm", "check_url": "https://orders-drcn.iap.hicloud.com/applications/purchases/tokens/verify"}, "weChatMiniGame": {"appId": "wx538b4efd48e2", "secret": "ce67d9a95327cae7a82f7638fd", "tokenUrl": "https://api.weixin.qq.com/cgi-bin/token", "loginUrl": "https://api.weixin.qq.com/sns/jscode2session", "checkSessionUrl": "https://api.weixin.qq.com/wxa/checksession", "appKey": "J2mXQDseTNg3Ekmuv03CzY8DxU7vh0Uh", "appKeySandBox": "********************************", "offerId": "1450065270", "customMsgNotifyToken": "osJSquZyFLmmhv93d42IhR0JHeUdMyFj", "sendCustomMsgUrl": "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=%s", "customMsgThumbUrl": "https://danke-wechat-jsapi.lezuan.net/danke.png?v=2023071301", "msgSecCheckUrl": "https://api.weixin.qq.com/wxa/msg_sec_check"}, "weChatMiniGameJsApi": {"open": false, "appId": "wx664bda08fc", "appSecret": "f7594ca7570dd670d967b96345", "mchId": "1643774423", "serialNo": "60FF615B4F0F7132E5FCDC87C42E54B63600D8E8", "apiV3Key": "KELGNwU5IXYz5kZvddxO5KdqqQoNd3Hv", "privateKey": "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC0GM5yP4Jf9+x1kfT7TdfpVLHlujBmBIXMqVSFTJ/QaeBuGiedBZrdhbaDgakFJrNB+qbaTVphXKzh6sYFJh8PJEPZKVt4kv35vaxfS1txV0ChQZIu9Z0DFmCFsHInB5U+ZRXvCIHqEBn2TtQtKzIvMbhRByiwbtylc9YmbYQXM4ddk5AobxBZhl658a/lD/XxzBEjY6WslyhXxe/wiezh9EmRptA8yMQCZjzzmt7OewXJTeTbr4Pj6DW2h4kz0ZIZ8U6chQO0WiTTxzZuEi5BtkzLTWqwgb8MJLjx+4WzsFoVVv+lpoOA6TSnWXQuqYohYtILTrI5CZwZhb0gD1xZAgMBAAECggEBAJCiO2nVR0b7PJNPHIeWBIgG1LfBx4ympS2v74j9+HrQDMVfi+C216B/+guy3aKdaeja+5Tl+memndDxUwZfjyIB5dtJM4Spx/4BLaV6R33GrMjUeCnuZqJMHkg/Y+2kUV9Cn1LQakUnmTIOLKf5wNvV+khYj3ziC6F0Xc+OtD5McsDnHrsTZcKahZ9xBnrsivz/CypJGI5zPhW6ZBiK9JVeYt6m1d+O9cFo29qIqqWPUcYtsPTXTJV35e85GKt+JpT679TJePJODvCX7o0svrIKwUw1AFvmhGh5e80FWSXzZyJHhIYibPDkaoOMPNJfAF0fnWgIsN/d0efoWmTfuAECgYEA2qsNv+803nLkXtAUkODGAuANIM5gRAeTy14+AK8B6zXTgzZ55kzqs0rbco6K2FLTWhDfLkbU+3g3lKsQckqsCt4cDbNWdvT/IGdadA1eXVDeN/pH7P0JvqLwBDQefb0XVQUyyWY2Yr+tj4yv10T5Vjz2RbLalHdYiFXUmy00+mECgYEA0tf8TR6P/brlCxxMxNbTR2wguRwke0AsqlOSAPxF6NhbLhvvhy/aiZMqnT2fgaWrhrpDkHl/WxqxZAkYpr6zHigs2/msjo7WZnLZy0OAqtomyI38IUgJ/yuyXrcWUWmZOoAE5NEq2aWjU/k0XscTw7CYABISU6WGp6PP8IFgVPkCgYAGMHdIKn4lmbzrDhq26xXPuQXGGvFQm6JMTFOW810rii99dEpJAX5XKtRBTtWRP+7pira2eZzHrWRtWdYyVfi+qgDDRoM5BMA/64z84e/81jAkB3qHs7XV5ojCpbMDPhnt9yKz3qfFbcV83a+QeUxRa5JQ8LoNH7+qxDsrVUE9oQKBgAmvBdV869wZc1+ZQyVfQIDtRGt8EWjfv61q/S+yZnuVRErP2aZ52i2rZq+mHudTdpdPgr7l9aV3imIv0MQHyi4+ZiEMTJKXmN1HtR0S0pwGPPXFH6lfp6XimeSjwBCpKvJ24sKnoLIyW1gGABPCTPQV7kVCjzjsR0z3xxsitgvxAoGAW4Zyt+b9W8cxVC/bQNEZV7vvsjCVkHPp2mqd7f8w0ud1N/3gKookBql1nlFKVLAFL5yE0fMe7vXopUFFhVQFv4QSyesevjEo/a8mUfMmYw0jDVA55a1HmM5n+HemwvN6CB7buSpPa/oX02VjZdgRvHnwilm4dktff8YXIOvCDZA=", "doOrderUrl": "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi", "reqAccessTokenUrl": "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code", "reqCodeUrl": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%s#wechat_redirect", "payStaticUrl": ["https://danke-wechat-jsapi.lezuan.net/pay_test.html?name=%s&sum=%s&res=%s&appid=%s&ts=%s&nonce=%s&package=%s&signType=%s&sign=%s&preOrderId=%s&env=%s&userId=%s&productId=%s", "https://danke-wechat-jsapi.lezuan.net/pay.html?name=%s&sum=%s&res=%s&appid=%s&ts=%s&nonce=%s&package=%s&signType=%s&sign=%s&preOrderId=%s&env=%s&userId=%s&productId=%s"], "uploadUrl": "https://api.weixin.qq.com/cgi-bin/media/upload", "qrCodeUrl": "https://habby-tools.habbyservice.com/qrlogo?appKey=dk&cropWide=10&data="}, "douyinMiniGame": {"appId": "tt5dd52b68cfe02", "appSecret": "068da8edf2a0c072161f887242f2d8", "paySecret": "0e2d9e22e1fe462c890115ffa", "loginUrl": "https://minigame.zijieapi.com/mgplatform/api/apps/jscode2session", "tokenUrl": "https://minigame.zijieapi.com/mgplatform/api/apps/v2/token", "payUrl": "https://developer.toutiao.com/api/apps/game/wallet/game_pay", "payStateUrl": "https://developer.toutiao.com/api/apps/game/payment/queryPayState", "payCbToken": "D83E03BAF8EE181162DD07"}}
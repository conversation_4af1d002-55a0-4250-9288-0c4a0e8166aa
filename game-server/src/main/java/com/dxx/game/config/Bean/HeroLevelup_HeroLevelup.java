
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class HeroLevelup_HeroLevelup {
    public HeroLevelup_HeroLevelup(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        titleName = _buf.get("titleName").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("levelUpCost").getAsJsonArray(); levelUpCost = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  levelUpCost.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("gradeUpCost").getAsJsonArray(); gradeUpCost = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  gradeUpCost.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("levelUpRewards").getAsJsonArray(); levelUpRewards = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  levelUpRewards.add(_v0); }   }
    }

    public static HeroLevelup_HeroLevelup deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.HeroLevelup_HeroLevelup(_buf);
    }

    /**
     * 战队等级
     */
    public final int ID;
    /**
     * 名称多语言
     */
    public final String titleName;
    /**
     * 升至下一级时的消耗
     */
    public final java.util.List<String> levelUpCost;
    /**
     * 升至下一阶时的消耗
     */
    public final java.util.List<String> gradeUpCost;
    /**
     * 升至下一级时获得的属性奖励
     */
    public final java.util.List<String> levelUpRewards;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "titleName:" + titleName + ","
        + "levelUpCost:" + levelUpCost + ","
        + "gradeUpCost:" + gradeUpCost + ","
        + "levelUpRewards:" + levelUpRewards + ","
        + "}";
    }
}


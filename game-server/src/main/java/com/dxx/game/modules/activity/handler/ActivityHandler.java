package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.ActivityProto.*;
import com.dxx.game.modules.activity.service.ActivityService;
import com.google.protobuf.Message;
import jakarta.annotation.Resource;

@ApiHandler
public class ActivityHandler {
	
	@Resource
	private ActivityService activityService;

	@ApiMethod(command = MsgReqCommand.ActivityGetListRequest, name = "活动-获取所有活动数据")
	public Result<ActivityGetListResponse> getList(Message msg) {
		ActivityGetListRequest params = (ActivityGetListRequest)msg;
		return activityService.getListAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ActivityGetTaskRequest, name = "活动-根据活动id获取任务数据", skipIdempotent = false)
	public Result<ActivityGetTaskResponse> getTask(Message msg) {
		ActivityGetTaskRequest params = (ActivityGetTaskRequest)msg;
		return activityService.getTaskAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ActivityTaskRewardRequest, name = "活动-领取任务奖励")
	public Result<ActivityTaskRewardResponse> taskReward(Message msg) {
		ActivityTaskRewardRequest params = (ActivityTaskRewardRequest)msg;
		return activityService.taskRewardAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ActivityShopExchangeRequest, name = "活动-商城兑换", skipIdempotent = false)
	public Result<ActivityShopExchangeResponse> shopExchange(Message msg) {
		ActivityShopExchangeRequest params = (ActivityShopExchangeRequest)msg;
		return activityService.shopExchangeAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ActivityGetShopRequest, name = "活动-获取商城数据")
	public Result<ActivityGetShopResponse> getShop(Message msg) {
		ActivityGetShopRequest params = (ActivityGetShopRequest)msg;
		return activityService.getShopAction(params);
	}

	@ApiMethod(command = MsgReqCommand.ActivityGetRankRequest, name = "活动-获取排行榜数据")
	public Result<ActivityGetRankResponse> getRank(Message msg) {
		ActivityGetRankRequest params = (ActivityGetRankRequest)msg;
		return activityService.getRankAction(params);
	}

}
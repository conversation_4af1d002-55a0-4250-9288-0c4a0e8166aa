package com.dxx.game.modules.message.service;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/8/28 19:12
 */
@Slf4j
@Service
public class UserMessageService {
    @Resource
    private MessageService messageService;

    /**
     * 征服战红点
     */
    public void pushConquerRed(long toUserId) {
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("timestamp", DateUtils.getUnixTime());

//        PublishMessageDto dto = PublishMessageDto.valueOfByUser(MessageType.RED_CONQUER, jsonObject.toJSONString(), toUserId);
//        messageService.publishMessage(dto);
//        imService.sendPersonalMsg(MessageType.RED_CONQUER, toUserId, jsonObject.toJSONString());

    }

    /**
     * 征服战领主变更
     */
    public void pushConquerEventLord(long toUserId) {
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("timestamp", DateUtils.getUnixTime());

//        PublishMessageDto dto = PublishMessageDto.valueOfByUser(MessageType.EVENT_CONQUER_LORD, jsonObject.toJSONString(), toUserId);
//        messageService.publishMessage(dto);
//        imService.sendPersonalMsg(MessageType.EVENT_CONQUER_LORD, toUserId, jsonObject.toJSONString());
    }

    /**
     * 矿点事件
     */
    public void pushOreEvent(long toUserId) {
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("timestamp", DateUtils.getUnixTime());

//        imService.sendPersonalMsg(MessageType.EVENT_ORE, toUserId, jsonObject.toJSONString());
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_LevelFund {
    public IAP_LevelFund(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        groupId = _buf.get("groupId").getAsInt();
        paramType = _buf.get("paramType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Products").getAsJsonArray(); Products = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Products.add(_v0); }   }
    }

    public static IAP_LevelFund deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_LevelFund(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 基金奖励组id
     */
    public final int groupId;
    /**
     * 参数类型：<br/>1.主线关卡ID<br/>2.等级
     */
    public final int paramType;
    /**
     * 当即奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.List<String> Products;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "groupId:" + groupId + ","
        + "paramType:" + paramType + ","
        + "Products:" + Products + ","
        + "}";
    }
}


### dynamodb è¡¨å å¯¹åºç¯å¢ççå®è¡¨å

equip=framework2-equip
item=framework2-item
log-gm-reward=framework2-log-gm-reward
log-resource=framework2-log-resource
prepare-recharge-order=framework2-prepare-recharge-order
recharge-order=framework2-recharge-order
user=framework2-user
shop=framework2-shop
task=framework2-task
froze-device=framework2-froze-device
guild=framework2-guild
hero=framework2-hero
report=framework2-report
work=framework2-work
record=framework2-record
account=framework2-account
server=framework2-server
gameplay=framework2-gameplay
activity=framework2-activity
user-module=framework2-user-module

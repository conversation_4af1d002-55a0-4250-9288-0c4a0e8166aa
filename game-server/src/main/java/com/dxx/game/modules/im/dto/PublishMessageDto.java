package com.dxx.game.modules.im.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: lsc
 * @createDate: 2025/3/19
 * @description:
 */
@Data
public class PublishMessageDto {

    private String groupId;
    private long[] toUserIds;
    private boolean isToAllUser;
    private MsgContent msgContent;
    private boolean saveGroupMessage;

    /**
     * 根据分组ID推送消息
     */
    public static PublishMessageDto valueOfByGroup(int messageType, String groupId, String content, boolean saveGroupMessage) {
        return createDto(messageType, groupId, MsgContent.builder().content(content).messageType(messageType).build(), false, saveGroupMessage);
    }

    public static PublishMessageDto valueOfByGroup(int messageType, String groupId, MsgContent msgContent) {
        return createDto(messageType, groupId, msgContent, false, false);
    }

    /**
     * 给指定用户推送消息
     */
    public static PublishMessageDto valueOfByUser(int messageType, String content, long... toUserIds) {
        return createDto(messageType, null, MsgContent.builder().content(content).messageType(messageType).build(), false, false, toUserIds);
    }

    public static PublishMessageDto valueOfByUser(int messageType, MsgContent msgContent, long... toUserIds) {
        return createDto(messageType, null, msgContent, false, false, toUserIds);
    }

    public static PublishMessageDto valueOfByUser(int messageType, String content, List<Long> toUserIds) {
        long[] userIds = toUserIds != null ? toUserIds.stream().mapToLong(Long::longValue).toArray() : new long[0];
        return valueOfByUser(messageType, content, userIds);
    }

    /**
     * 给所有用户推送消息
     */
    public static PublishMessageDto valueOfByAllUser(int messageType, String content) {
        return createDto(messageType, null, MsgContent.builder().content(content).messageType(messageType).build(), true, false);
    }

    /**
     * 核心创建方法，统一封装逻辑
     */
    private static PublishMessageDto createDto(int messageType, String groupId, MsgContent msgContent,
                                               boolean isToAllUser, boolean saveGroupMessage, long... toUserIds) {
        PublishMessageDto dto = new PublishMessageDto();
        dto.setGroupId(groupId);
        dto.setMsgContent(msgContent);
        dto.setToUserIds(toUserIds);
        dto.setToAllUser(isToAllUser);
        dto.setSaveGroupMessage(saveGroupMessage);
        return dto;
    }

    private static PublishMessageDto createDto(int messageType, String groupId, MsgContent msgContent,
                                               boolean isToAllUser, boolean saveGroupMessage) {
        return createDto(messageType, groupId, msgContent, isToAllUser, saveGroupMessage, new long[0]);
    }
}

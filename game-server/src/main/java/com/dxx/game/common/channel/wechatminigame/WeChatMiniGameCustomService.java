package com.dxx.game.common.channel.wechatminigame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameConfig;
import com.dxx.game.common.channel.wechatminigame.model.WeChatCustomMsgNotifyVO;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameJsApiConfig;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.modules.activity.service.ActivityService;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.QueryStringDecoder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lsc
 * @createDate: 2023/8/21
 * @description:
 */
@Slf4j
@DependsOn("okHttpClient")
@Component
public class WeChatMiniGameCustomService {

    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private WeChatMiniGameService weChatMiniGameService;
    @Resource
    private ActivityService activityService;
    @Resource
    private RedisService redisService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final ChannelID CHANNEL_ID = ChannelID.WeChatMiniGame;

    private static final String MINI_PROGRAM_PAGE_MSG_TYPE = "miniprogrampage";

    // 接受客服会话消息
    public Object receiveCustomMsg(FullHttpRequest fullRequest) throws Exception {
        Map<String, String> postParams = HttpRequester.getParams(fullRequest);
        if (!verifiedCustomMsgSign(postParams)) {
            return "fail";
        }
        // 来自微信后台的校验请求
        if (postParams.containsKey("echostr")) {
            return postParams.get("echostr");
        }
        String body = fullRequest.content().toString(StandardCharsets.UTF_8);
        log.info("receiveCustomMsg body:{}, postParams:{}", body, postParams);

        WeChatCustomMsgNotifyVO customMsgNotify = JSON.parseObject(body, WeChatCustomMsgNotifyVO.class);

        String msgType = customMsgNotify.getMsgType();
        if (msgType.equals("miniprogrampage")) {
            // 支付小卡片
            return this.doHandleMiniProgramPage(customMsgNotify);
        } else if (msgType.equals("event")) {
            String event = customMsgNotify.getEvent();
            if (event.equals("minigame_deliver_goods")) {
                return this.doHandleMimiGameDeliverGoods(customMsgNotify);
            } else if (event.equals("user_enter_tempsession")) {
                return this.doHandleEnterTempSessionForProgramPage(customMsgNotify);
//                return this.doHandleEnterTempSession(customMsgNotify);
            } else {
                return "success";
            }
        } else {
            // 其他消息
            return "success";
        }
    }

    // 小游戏礼包(游戏圈)
    private Object doHandleMimiGameDeliverGoods(WeChatCustomMsgNotifyVO customMsgNotify) {
//        WeChatCustomMsgNotifyVO.MiniGame miniGame = customMsgNotify.getMiniGame();
//        boolean flag = activityService.doHandleMimiGameDeliverGoods(miniGame.getToUserOpenid(),
//                miniGame.getGoodsList(), miniGame.getOrderId(), miniGame.getGiftId(), miniGame.getGiftTypeId());
//        Map<String, Object> result = new HashMap<>();
//        if (flag) {
//            result.put("ErrCode", 0);
//            result.put("ErrMsg", "Success");
//        } else {
//            result.put("ErrCode", 1);
//            result.put("ErrMsg", "user not registered");
//            result.put("SubErrCode", 172935494);
//        }
//
//        return result;
        return null;
    }


    private Object doHandleEnterTempSessionForProgramPage(WeChatCustomMsgNotifyVO customMsgNotify) throws Exception {
        String openId = customMsgNotify.getFromUserName();
        String sessionFrom = customMsgNotify.getSessionFrom();
        if (StringUtils.isEmpty(sessionFrom) || !sessionFrom.contains("productId")) {
            return "success";
        }
        sessionFrom = URLDecoder.decode(sessionFrom, "utf-8");
        String[] pairs = sessionFrom.split("&");
        JSONObject params = new JSONObject();
        for (String pair : pairs) {
            // 拆分成键和值
            int idx = pair.indexOf("=");
            if (idx != -1 && idx < pair.length() - 1) {
                // 键和值都需要进行URL解码
                String key = pair.substring(0, idx);
                String value = pair.substring(idx + 1);
                params.put(key, value);
            }
        }

        return handleSendMiniProgramPage(openId, params);
    }

    private Object doHandleEnterTempSession(WeChatCustomMsgNotifyVO customMsgNotify) throws Exception {
        String openId = customMsgNotify.getFromUserName();
        String sessionFrom = customMsgNotify.getSessionFrom();
        if (StringUtils.isEmpty(sessionFrom) || !sessionFrom.contains("productId")) {
            return "success";
        }

        WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();
        String sendCustomMsgUrl = weChatMiniGameConfig.getSendCustomMsgUrl();

        sessionFrom = URLDecoder.decode(sessionFrom, "utf-8");
        String[] pairs = sessionFrom.split("&");
        JSONObject params = new JSONObject();
        for (String pair : pairs) {
            // 拆分成键和值
            int idx = pair.indexOf("=");
            if (idx != -1 && idx < pair.length() - 1) {
                // 键和值都需要进行URL解码
                String key = pair.substring(0, idx);
                String value = pair.substring(idx + 1);
                params.put(key, value);
            }
        }

        String accessToken = weChatMiniGameService.getAccessToken();
        String env = params.getString("env");
        if (StringUtils.isEmpty(env)) {
            env = "prod";
        }

        sendCustomMsgUrl = String.format(sendCustomMsgUrl, accessToken);
        // 文本
        JSONObject resp = this.sendCustomMsgText(openId, sendCustomMsgUrl, params);
        int errcode = resp.getIntValue("errcode");
        if (errcode == 40001) {
            accessToken = weChatMiniGameService.reqAccessToken();
            log.info("reRequestAccessToken:{}", accessToken);
            sendCustomMsgUrl = String.format(sendCustomMsgUrl, accessToken);
            log.info("reSendCustomMsgText:{}", sendCustomMsgUrl);
            this.sendCustomMsgText(openId, sendCustomMsgUrl, params);
        }

        // 生成二维码
        String userId = params.getString("userId");
        String qrCodeName = PaymentUtils.createCpOrderId() + "-" + userId + ".png";
        String fileName = "./qrcode/" + qrCodeName;
        File file = new File(fileName);
        String redirectUrl = generatorRedirectUrl(env, params);
        redirectUrl = URLEncoder.encode(redirectUrl, "utf-8");
        String qrCodeUrl = channelConfig.getWeChatMiniGameJsApiConfig().getQrCodeUrl() + redirectUrl;
        log.info("qrCodeUrl:{}", qrCodeUrl);
        boolean flag = OkHttpClientUtil.downLoadImage(qrCodeUrl, file);
        if (!flag) {
            return "generateQRCodeImageError";
        }
        // 上传图片获取mediaId
        String mediaId = uploadQRCode(file);
        if (StringUtils.isEmpty(mediaId)) {
            log.error("uploadQRCode error");
        }
        file.delete();
        // 图片
        resp = this.sendCustomMsgImage(openId, sendCustomMsgUrl, params, mediaId);
        errcode = resp.getIntValue("errcode");
        if (errcode == 0) {
            return "success";
        }
        // token失效 重试一次
        if (errcode == 40001) {
            accessToken = weChatMiniGameService.reqAccessToken();
            log.info("reRequestAccessToken:{}", accessToken);
            sendCustomMsgUrl = String.format(sendCustomMsgUrl, accessToken);
            log.info("reSendCustomMsgImage:{}", sendCustomMsgUrl);
            resp = this.sendCustomMsgImage(openId, sendCustomMsgUrl, params, mediaId);
            errcode = resp.getIntValue("errcode");
            if (errcode == 0) {
                return "success";
            }
        }
        return resp.getString("errmsg");
    }

    // ios支付卡片
    private Object doHandleMiniProgramPage(WeChatCustomMsgNotifyVO customMsgNotify) throws Exception {
        String openId = customMsgNotify.getFromUserName();
        String pagePath = customMsgNotify.getPagePath();
        QueryStringDecoder decoder = new QueryStringDecoder(pagePath);
        JSONObject params = new JSONObject();
        for (Map.Entry<String, List<String>> attr : decoder.parameters().entrySet()) {
            params.put(attr.getKey(), attr.getValue().get(0));
        }

        return handleSendMiniProgramPage(openId, params);
    }

    private Object handleSendMiniProgramPage(String openId, JSONObject params) throws Exception {
        // 相同的attach已发送
        String attach = params.getString("attach");
        String redisKey = String.format("mini_program:send_status:%s:%s", openId, attach);
        if (redisService.existsKey(redisKey)) {
            return "success";
        }

        String accessToken = weChatMiniGameService.getAccessToken();
        WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();
        String sendCustomMsgUrl = weChatMiniGameConfig.getSendCustomMsgUrl();

        // 格式化 URL 并发送消息
        sendCustomMsgUrl = String.format(sendCustomMsgUrl, accessToken);
        JSONObject resp = sendCustomMsgLink(openId, sendCustomMsgUrl, params);

        // 检查响应状态
        int errcode = resp.getIntValue("errcode");
        if (errcode == 0) {
            redisService.set(redisKey, "1", 3600);
            return "success";
        }

        // 如果 token 失效，重试一次
        if (errcode == 40001) {
            accessToken = weChatMiniGameService.reqAccessToken();
            log.info("reRequestAccessToken:{}", accessToken);
            sendCustomMsgUrl = String.format(sendCustomMsgUrl, accessToken);
            log.info("reSendCustomMsgLink:{}", sendCustomMsgUrl);
            resp = sendCustomMsgLink(openId, sendCustomMsgUrl, params);
            errcode = resp.getIntValue("errcode");
            if (errcode == 0) {
                redisService.set(redisKey, "1", 3600);
                return "success";
            }
        }

        // 返回错误信息
        return resp.getString("errmsg");
    }

    private JSONObject sendCustomMsgText(String openId, String sendCustomMsgUrl, JSONObject params) throws Exception {
        String title = URLDecoder.decode(params.getString("title"), "utf-8");  // TODO 标题读表
        String description = URLDecoder.decode(params.getString("desc"), "utf-8");  // TODO 描述读表
        WeChatMiniGameCustomMsgRequest weChatMiniGameCustomMsgRequest = new WeChatMiniGameCustomMsgRequest();
        weChatMiniGameCustomMsgRequest.setToUser(openId);
        weChatMiniGameCustomMsgRequest.setMsgType("text");
        WeChatMiniGameCustomMsgText weChatMiniGameCustomMsgText = new WeChatMiniGameCustomMsgText();
        weChatMiniGameCustomMsgText.setContent("打开下方二维码，长按识别后购买【" + description + "】");
        weChatMiniGameCustomMsgRequest.setText(weChatMiniGameCustomMsgText);
        JSONObject resp = OkHttpClientUtil.postJson(sendCustomMsgUrl, null, weChatMiniGameCustomMsgRequest, JSONObject.class);
        log.info("sendCustomMsgText resp = {}", resp);
        return resp;
    }

    private JSONObject sendCustomMsgImage(String openId, String sendCustomMsgUrl, JSONObject params, String mediaId) throws Exception {
        WeChatMiniGameCustomMsgRequest weChatMiniGameCustomMsgRequest = new WeChatMiniGameCustomMsgRequest();
        weChatMiniGameCustomMsgRequest.setToUser(openId);
        weChatMiniGameCustomMsgRequest.setMsgType("image");
        WeChatMiniGameCustomMsgImage weChatMiniGameCustomMsgImage = new WeChatMiniGameCustomMsgImage();
        weChatMiniGameCustomMsgImage.setMedia_id(mediaId);
        weChatMiniGameCustomMsgRequest.setImage(weChatMiniGameCustomMsgImage);

        JSONObject resp = OkHttpClientUtil.postJson(sendCustomMsgUrl, null, weChatMiniGameCustomMsgRequest, JSONObject.class);
        log.info("sendCustomMsgImage resp = {}", resp);

        return resp;
    }



    // 生成支付url
    private String generatorRedirectUrl(String env, JSONObject params) throws UnsupportedEncodingException {
        String notifyHost = channelConfig.getNotifyHost(env);
        WeChatMiniGameJsApiConfig weChatMiniGameJsApiConfig = channelConfig.getWeChatMiniGameJsApiConfig();
        String appId = weChatMiniGameJsApiConfig.getAppId();
        String reqCodeUrl = weChatMiniGameJsApiConfig.getReqCodeUrl();
        String state = URLEncoder.encode(params.toJSONString(), StandardCharsets.UTF_8.name());
        String customMsgRedirectUrl = notifyHost + "/wechat/jsapi_pay";
        return String.format(reqCodeUrl, appId, customMsgRedirectUrl, state);
    }

    private JSONObject sendCustomMsgLink(String openId, String sendCustomMsgUrl, JSONObject params) throws UnsupportedEncodingException {

        String env = params.getString("env");
        if (StringUtils.isEmpty(env)) {
            env = "prod";
        }
        String title = URLDecoder.decode(params.getString("title"), "utf-8");  // TODO 标题读表
        String description = URLDecoder.decode(params.getString("desc"), "utf-8");  // TODO 描述读表

        WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();
        String customMsgThumbUrl = weChatMiniGameConfig.getCustomMsgThumbUrl();
        String url = generatorRedirectUrl(env, params);
        // 线上环境
        WeChatMiniGameCustomMsgRequest weChatMiniGameCustomMsgRequest = new WeChatMiniGameCustomMsgRequest();
        weChatMiniGameCustomMsgRequest.setToUser(openId);
        weChatMiniGameCustomMsgRequest.setMsgType("link");
        WeChatMiniGameCustomMsgLink weChatMiniGameCustomMsgLink = new WeChatMiniGameCustomMsgLink();
        weChatMiniGameCustomMsgLink.setTitle(title);
        weChatMiniGameCustomMsgLink.setDescription(description);
        weChatMiniGameCustomMsgLink.setUrl(url);
        weChatMiniGameCustomMsgLink.setThumbUrl(customMsgThumbUrl);
        weChatMiniGameCustomMsgRequest.setLink(weChatMiniGameCustomMsgLink);
        JSONObject resp = OkHttpClientUtil.postJson(sendCustomMsgUrl, null, weChatMiniGameCustomMsgRequest, JSONObject.class);
        log.info("sendCustomMsg resp = {}", resp);

        return resp;
    }

    // 验证消息推送签名
    private boolean verifiedCustomMsgSign(Map<String, String> postParams) {
        String signatureParam = postParams.get("signature");
        String timestampParam = postParams.get("timestamp");
        String nonceParam = postParams.get("nonce");
        WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();
        String customMsgNotifyToken = weChatMiniGameConfig.getCustomMsgNotifyToken();
        List<String> signList = new ArrayList<>();
        signList.add(customMsgNotifyToken);
        signList.add(timestampParam);
        signList.add(nonceParam);
        String signData = signList.stream()
                .sorted()
                .collect(Collectors.joining());
        String sign = generateCustomMsgNotifySignature(signData);
        if (!Objects.equals(sign, signatureParam)) {
            log.error("verify signature failed, signData={}, sign={}, signatureParam={}", signData, sign, signatureParam);
            return false;
        }
        return true;
    }

    // 生成消息推送签名
    private String generateCustomMsgNotifySignature(String signData) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] hashInBytes = md.digest(signData.getBytes(StandardCharsets.UTF_8));
            return CryptUtil.byte2hex(hashInBytes).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }

    private String uploadQRCode(File file) {
        WeChatMiniGameJsApiConfig weChatMiniGameJsApiConfig = channelConfig.getWeChatMiniGameJsApiConfig();
        String url = weChatMiniGameJsApiConfig.getUploadUrl();
        String accessToken;
        String postUrl;
        String response;
        JSONObject respObj;
        int errcode;
        int MAX_RETRY_COUNT = 3;
        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            accessToken = weChatMiniGameService.getAccessToken();
            postUrl = url + "?access_token=" + accessToken + "&type=image";

            response = OkHttpClientUtil.uploadImageToWeixin(postUrl, file, String.class);
            respObj = JSONObject.parseObject(response);

            log.info("uploadQRCode, attempt:{}, postURL:{}, fileName:{}, response:{}", attempt, postUrl, file.getName(), response);

            errcode = respObj.getIntValue("errcode");
            if (errcode == 0) {
                return respObj.getString("media_id");
            } else if (errcode == 40001 && attempt < MAX_RETRY_COUNT) {
                // 如果错误码为40001（令牌失效），重新获取accessToken并重试
                accessToken = weChatMiniGameService.reqAccessToken();
            } else {
                // 遇到其他错误或达到最大重试次数，记录错误并返回null
                log.error("uploadQRCode error, resp:{}", response);
                break;
            }
        }
        return null;
    }

    @Data
    public static class WeChatMiniGameCustomMsgRequest {
        @JSONField(name = "touser")
        private String toUser;
        @JSONField(name = "msgtype")
        private String msgType;
        private WeChatMiniGameCustomMsgLink link;
        private WeChatMiniGameCustomMsgImage image;
        private WeChatMiniGameCustomMsgText text;
    }

    @Data
    public static class WeChatMiniGameCustomMsgLink {
        private String title;
        private String description;
        private String url;
        @JSONField(name = "thumb_url")
        private String thumbUrl;
    }

    @Data
    public static class WeChatMiniGameCustomMsgImage {
        private String media_id;
    }

    @Data
    public static class WeChatMiniGameCustomMsgText {
        private String content;
    }
}

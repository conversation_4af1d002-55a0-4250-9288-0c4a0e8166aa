package com.dxx.game.modules.activity.service.impl;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.Triple;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.ConsumeEntity;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.event.ShopEntity;
import com.dxx.game.config.entity.event.TaskEntity;
import com.dxx.game.config.entity.iap.PurchaseEntity;
import com.dxx.game.consts.ActivityType;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.ActivityCommon;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityDao;
import com.dxx.game.dto.*;
import com.dxx.game.dto.ActivityProto.ActivityGetListRequest;
import com.dxx.game.dto.ActivityProto.ActivityGetListResponse;
import com.dxx.game.modules.activity.base.ActivityManager;
import com.dxx.game.modules.activity.model.EventTaskProcess;
import com.dxx.game.modules.activity.service.ActivityService;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.activity.service.SignInService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.rank.model.UserRankExtraData;
import com.dxx.game.modules.rank.support.RankHelper;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.user.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@XRayEnabled
@Slf4j
@Service
public class ActivityServiceImpl implements ActivityService {

    @Resource
    private SignInService signInService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private SevenDayTaskService sevenDayTaskService;
    @Resource
    private ActivityDao activityDao;
    @Resource
    private ActivityBaseDao.EventsDao eventsDao;
    @Resource
    private MailService mailService;
//    @Resource
//    private EventService eventService;
    @Resource
    private RewardService rewardService;
    @Resource
    private CommonService commonService;
    @Resource
    private ServerListService serverListService;


    @Resource
    private UserService userService;

    @DynamoDBTransactional
    @Override
    public Result<ActivityGetListResponse> getListAction(ActivityGetListRequest params) {
        long userId = RequestContext.getUserId();
        ActivityGetListResponse.Builder response = ActivityGetListResponse.newBuilder();

        Activity activity = activityDao.getByUserId(userId);
        ActivityCommon events = activity.getActivityCommon();
        if (events == null) {
            events = new ActivityCommon();
            events.setUserId(userId);
            events.setActivityTasks(new HashMap<>());
            events.setActivityShops(new HashMap<>());
            events.setActivityConsumes(new HashMap<>());
            events.setLastRefTime(DateUtils.getSystemResetTime());
            activity.setActivityCommon(events);
            eventsDao.update(events);
        } else {
            this.checkEvents(events);
        }

        var openConfig = ActivityManager.getInstance().getOpenConfig();
        // 正在开启的活动
        if (!CollectionUtils.isNullOrEmpty(openConfig)) {
            for (Triple<EventEntity, Long, Long> pair : openConfig) {
                ActivityProto.ActivityInfoDto.Builder dto = ActivityProto.ActivityInfoDto.newBuilder();
                dto.setActivityId(pair.getFirst().getId());
                dto.setStartTime(pair.getSecond());
                dto.setEndTime(pair.getThird());
                response.addActivityInfos(dto);
            }
        }

        SignInProto.SignInData signInData = signInService.getSignData(userId);
        if (signInData != null) {
            response.setSignInData(signInData);
        }

        SevenDayTaskProto.SevenDayDto sevenDayDto = sevenDayTaskService.getInfo(userId);
        if (sevenDayDto != null) {
            response.setSevenDayTaskDto(sevenDayDto);
        }
        response.setRefTime(events.getLastRefTime());

        // 通用消耗活动
        var consumeDto = this.getConsumeDto(events, ActivityType.CONSUME_EQUIP);
        if (consumeDto != null) {
            response.addConsumeDtos(consumeDto);
        }
        consumeDto = this.getConsumeDto(events, ActivityType.CONSUME_HERO);
        if (consumeDto != null) {
            response.addConsumeDtos(consumeDto);
        }
        consumeDto = this.getConsumeDto(events, ActivityType.CONSUME_RELIC);
        if (consumeDto != null) {
            response.addConsumeDtos(consumeDto);
        }

        // 通用活动数据
        for (Triple<EventEntity, Long, Long> eventEntityLongLongTriple : openConfig) {
            var entity = eventEntityLongLongTriple.getFirst();
            var activityId = entity.getId();

            // 任务
            ActivityCommon.ActivityTaskModel eventTaskModel = events.getActivityTasks().get(activityId);
            if (eventTaskModel != null) {
                ActivityProto.TaskListDto.Builder taskListDto = ActivityProto.TaskListDto.newBuilder();
                taskListDto.addAllTasks(this.buildTaskDto(activityId, eventTaskModel));
                response.putTaskMap(activityId, taskListDto.build());
            }
            // 商店
            ActivityCommon.ActivityShopModel eventShopModel = events.getActivityShops().get(activityId);
            if (eventShopModel != null) {
                ActivityProto.ShopListDto.Builder shopListDto = ActivityProto.ShopListDto.newBuilder();
                shopListDto.addAllShop(this.buildShopDto(activityId, eventShopModel));
                response.putShopMap(activityId, shopListDto.build());
            }
        }


        ActivityManager.getInstance().populateActivityListResponse(userId, response);

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ActivityProto.ActivityGetTaskResponse> getTaskAction(ActivityProto.ActivityGetTaskRequest params) {
        long userId = RequestContext.getUserId();
        List<Integer> activityIds = params.getIdList();

        ActivityCommon events = eventsDao.getByUserId(userId);
        if (events == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        ActivityProto.ActivityGetTaskResponse.Builder response = ActivityProto.ActivityGetTaskResponse.newBuilder();
        for(Integer activityId : activityIds) {
            ActivityCommon.ActivityTaskModel taskModel = events.getActivityTasks().get(activityId);
            if (taskModel == null) {
                continue;
            }
            ActivityProto.TaskListDto.Builder taskListDto = ActivityProto.TaskListDto.newBuilder();
            taskListDto.addAllTasks(this.buildTaskDto(activityId, taskModel));
            response.putTaskMap(activityId, taskListDto.build());
        }

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ActivityProto.ActivityTaskRewardResponse> taskRewardAction(ActivityProto.ActivityTaskRewardRequest params) {
        long userId = RequestContext.getUserId();
        int activityId = params.getId();
        int taskId = params.getTaskId();

        if (ActivityManager.getInstance().isOpen(activityId)) {
            return Result.Error(ErrorCode.ACTIVITY_NOT_OPEN);
        }

        ActivityCommon events = eventsDao.getByUserId(userId);
        if (events == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        ActivityCommon.ActivityTaskModel taskModel = events.getActivityTasks().get(activityId);
        if (taskModel == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        TaskEntity taskEntity = gameConfigManager.getEventConfig().getTaskEntity(taskId);
        if (taskEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity.getTaskGroupType() != taskEntity.getGroupType()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        this.checkTask(events);

        long need = taskEntity.getNeed();
        long process = taskModel.getProcess().get(taskId);
        if (need > process) {
            return Result.Error(ErrorCode.TASK_NOT_FINISH);
        }

        boolean isReceive = taskModel.getReceive().contains(taskId);
        if (isReceive) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }

        // 发奖励
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, taskEntity.getReward());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        taskModel.getReceive().add(taskId);

        eventsDao.update(events);

        ActivityProto.ActivityTaskRewardResponse.Builder response = ActivityProto.ActivityTaskRewardResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.addAllDtos(buildTaskDto(activityId, taskModel));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ActivityProto.ActivityShopExchangeResponse> shopExchangeAction(ActivityProto.ActivityShopExchangeRequest params) {
        long userId = RequestContext.getUserId();

        int activityId = params.getId();
        int shopId = params.getShopId();

        if (!ActivityManager.getInstance().isOpen(activityId)) {
            return Result.Error(ErrorCode.ACTIVITY_NOT_OPEN);
        }

        ActivityCommon events = eventsDao.getByUserId(userId);
        if (events == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        ShopEntity shopEntity = gameConfigManager.getEventConfig().getShopEntity(shopId);
        if (shopEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        // 只能是兑换类型
        if (shopEntity.getType() != 1) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity.getShopGroupType() != shopEntity.getGroupType()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        ActivityCommon.ActivityShopModel shopModel = events.getActivityShops().get(activityId);
        if (shopModel == null) {
            shopModel = new ActivityCommon.ActivityShopModel();
            events.getActivityShops().put(activityId, shopModel);
        }

        int count = shopModel.getIdCounts().getOrDefault(shopId, 0);
        if(count >= shopEntity.getMaxCount()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        RewardResultSet costResultSet = rewardService.executeCosts(userId, shopEntity.getCost());
        if (costResultSet.isFailed()) {
            return Result.Error(costResultSet.getResultCode());
        }
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, shopEntity.getReward());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }
        shopModel.getIdCounts().put(shopId, count + 1);
        eventsDao.update(events);
        ActivityProto.ActivityShopExchangeResponse.Builder response = ActivityProto.ActivityShopExchangeResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet, rewardResultSet));
        response.setShop(this.buildShopDto(activityId, shopId, shopModel.getIdCounts().get(shopId)));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ActivityProto.ActivityGetShopResponse> getShopAction(ActivityProto.ActivityGetShopRequest params) {
        long userId = RequestContext.getUserId();
        List<Integer> activityIds = params.getIdList();
        ActivityCommon events = eventsDao.getByUserId(userId);
        if (events == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        ActivityProto.ActivityGetShopResponse.Builder response = ActivityProto.ActivityGetShopResponse.newBuilder();
        for(Integer activityId : activityIds) {
            ActivityCommon.ActivityShopModel shopModel = events.getActivityShops().get(activityId);
            if (shopModel == null) {
                continue;
            }
            ActivityProto.ShopListDto.Builder shopListDto = ActivityProto.ShopListDto.newBuilder();
            shopListDto.addAllShop(this.buildShopDto(activityId, shopModel));
            response.putShopMap(activityId, shopListDto.build());
        }
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ActivityProto.ActivityGetRankResponse> getRankAction(ActivityProto.ActivityGetRankRequest params) {
        long userId = RequestContext.getUserId();
        int activityId = params.getId();
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        int page = params.getPage();
        int basePageSize = commonService.getGameConfigIntValue(1601); // 原始配置值
        int pageSize = basePageSize - 1; // 每页实际大小

        boolean isMultiPage = page > 1; // 是否是多页
        int offset = isMultiPage ? page - 1 : 0; // 多页时的偏移量

        int start = (page - 1) * pageSize + offset;
        int end = page * pageSize + offset;

        String rankKey = ActivityManager.getInstance().getRankKey(userId, activityId);

        Map<Long, Pair<Long, UserRankExtraData>> rankList = RankHelper.rankList(rankKey, start, end);
        int ownerRank = RankHelper.getRank(rankKey, userId);
        long score = RankHelper.getScore(rankKey, userId);
        ActivityProto.ActivityGetRankResponse.Builder response = ActivityProto.ActivityGetRankResponse.newBuilder();
        response.setOwnerRank(ownerRank);
        response.setOwnerScore(score);

        rankList.forEach((k, v) -> {
            response.addRank(CommonHelper.buildRankDto(k, v.getLeft(), v.getRight()));
        });

        return Result.Success(response.build());
    }

    @Override
    public void updateConsume(long userId, int eventType, int count) {
        ActivityCommon events = eventsDao.getByUserId(userId);
        var consumeData = this.getConsumeModel(events, eventType);
        if (consumeData == null) {
            return;
        }
        var activityId = consumeData.getLeft();
        var model = consumeData.getRight();
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        int eventId = eventEntity.getId();
        int groupType = eventEntity.getConsumeGroupType();
        List<String> params = eventEntity.getParam();
        if (params == null || params.isEmpty()) {
            log.error("config is error, please set round param! eventId={}", eventId);
            return;
        }
        int round = Integer.parseInt(params.get(0));

        int finalCount = model.getCompleteCount() + count;
        model.setCompleteCount(finalCount);

        List<ConsumeEntity> consumeEntityList = getConsumeEntityListByType(groupType);

        for (ConsumeEntity consumeEntity : consumeEntityList) {
            int id = consumeEntity.getId();
            if (finalCount >= consumeEntity.getNum() && !model.getFinishList().contains(id)) {
                model.getFinishList().add(id);

                // 发邮件奖励
                String tmpId = gameConfigManager.getEventConsumeMailTempId(eventEntity);
                String mailUniqueId = userId + "_event_consume_" + eventId + "_" + id + "_" + model.getRound();

                mailService.createMail(userId, tmpId, mailUniqueId, consumeEntity.getReward(), "event", eventId);
            }
        }

        int roundTotalCount = consumeEntityList.stream().mapToInt(ConsumeEntity::getNum).max().orElse(Integer.MAX_VALUE);

        int remainCount = finalCount - roundTotalCount;
        if (remainCount >= 0 && model.getRound() < round) {
            // 下一轮
            model.setCompleteCount(remainCount);
            model.setFinishList(new ArrayList<>());
            model.setRound(model.getRound() + 1);
        }

        eventsDao.update(events);

        ActivityManager.getInstance().incRank(userId, activityId, count);
    }

    @Override
    public void updateTask(long userId, EventTaskProcess... processes) {
        var openConfig = ActivityManager.getInstance().getOpenConfig();
        if (CollectionUtils.isNullOrEmpty(openConfig)) {
            return;
        }
        ActivityCommon events = eventsDao.getByUserId(userId);
        if (events == null) {
            return;
        }
        Map<Integer, Map<Integer, TaskEntity>> eventConfigs = gameConfigManager.getEventTasks();
        if (CollectionUtils.isNullOrEmpty(eventConfigs)) {
            return;
        }

        Map<Integer, List<EventEntity>> groupTypeToEventsMap = new HashMap<>();
        for (Triple<EventEntity, Long, Long> triple : openConfig) {
            var entity = triple.getFirst();
            groupTypeToEventsMap.computeIfAbsent(entity.getTaskGroupType(), k -> new ArrayList<>()).add(entity);
        }

        boolean save = false;
        this.checkTask(events);

        for (EventTaskProcess process : processes) {
            int type = process.getTaskType();
            long count = process.getCount();

            Map<Integer, TaskEntity> taskEntityMap = eventConfigs.get(type);
            if (taskEntityMap == null) {
                continue;
            }

            for (TaskEntity taskEntity : taskEntityMap.values()) {
                int groupType = taskEntity.getGroupType();
                List<EventEntity> relatedEvents = groupTypeToEventsMap.get(groupType);
                if (CollectionUtils.isNullOrEmpty(relatedEvents)) {
                    continue;
                }

                int taskId = taskEntity.getId();
                int countType = taskEntity.getCountType();
                int need = taskEntity.getNeed();

                for (EventEntity eventEntity : relatedEvents) {
                    int activityId = eventEntity.getId();

                    ActivityCommon.ActivityTaskModel taskModel = events.getActivityTasks().computeIfAbsent(activityId, k -> new ActivityCommon.ActivityTaskModel());

                    long currentCount = taskModel.getProcess().getOrDefault(taskId, 0L);
                    if (currentCount >= need) {
                        continue; // 已完成任务，跳过
                    }

                    long newCount;
                    if (countType == 0) {
                        // 非累加
                        newCount = Math.min(count, need);
                    } else {
                        // 累加
                        newCount = Math.min(currentCount + count, need);
                    }

                    if (newCount > currentCount) {
                        taskModel.getProcess().put(taskId, newCount);
                        save = true;

                        ResponseContext.addFillCommon("activity_task_" + activityId + "_" + taskId, builder ->
                                builder.addActivityTasks(buildTaskDto(activityId, taskId, newCount, taskModel.getReceive().contains(taskId)))
                        );
                    }
                }
            }
        }

        if (save) {
            eventsDao.update(events);
        }
    }

    @Override
    public boolean checkShopCanBuy(long userId, String extraInfo) {
        if (StringUtils.isEmpty(extraInfo)) {
            return true;
        }
        String[] extras = extraInfo.split(",");
        if (extras.length != 2) {
            return true;
        }
        if (!NumberUtils.isCreatable(extras[0]) || !NumberUtils.isCreatable(extras[1])) {
            return true;
        }
        int activityId = Integer.parseInt(extras[0]);
        int shopId = Integer.parseInt(extras[1]);
        if (!ActivityManager.getInstance().isOpen(activityId)) {
            return true;
        }
        ShopEntity shopEntity = gameConfigManager.getEventConfig().getShopEntity(shopId);
        if (shopEntity == null) {
            return true;
        }
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity.getShopGroupType() != shopEntity.getGroupType()) {
            return true;
        }
        int purchaseId = shopEntity.getIAPId();

        PurchaseEntity purchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(purchaseId);
        if (purchaseEntity == null) {
            return true;
        }
        ActivityCommon events = eventsDao.getByUserId(userId);
        if (events == null) {
            return true;
        }
        // 不限购
        if (shopEntity.getMaxCount() <= 0) {
            return false;
        }
        Map<Integer, ActivityCommon.ActivityShopModel> shops = events.getActivityShops();
        if (shops != null && shops.containsKey(activityId)) {
            var model = shops.get(activityId);
            int count = Optional.ofNullable(model.getIdCounts()).map(o -> o.getOrDefault(shopId, 0)).orElse(0);
            if (count >= shopEntity.getMaxCount()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<List<Integer>> getShopIapReward(int activityId, int iapId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity == null) {
            return null;
        }

        int shopType = eventEntity.getShopGroupType();
        List<ShopEntity> shopConfigs = gameConfigManager.getEventConfig().getShop().values().stream().filter(v -> v.getGroupType() == shopType).collect(Collectors.toList());
        ShopEntity shopEntity = shopConfigs.stream().filter(v -> v.getIAPId() == iapId).findFirst().orElse(null);
        if (shopEntity == null) {
            return null;
        }

        return shopEntity.getReward();
    }

    public CommonProto.ActivityTaskDto buildTaskDto(int eventId, int id, long process, boolean receive) {
        CommonProto.ActivityTaskDto.Builder dto = CommonProto.ActivityTaskDto.newBuilder();
        dto.setId(id);
        dto.setActivityId(eventId);
        dto.setProcess(process);
        dto.setIsReceive(receive);

        return dto.build();
    }

    private List<ConsumeEntity> getConsumeEntityListByType(int groupType) {
        return gameConfigManager.getEventConfig().getConsume().values().stream().filter(v -> v.getGroupType() == groupType).collect(Collectors.toList());
    }

    private ActivityProto.ConsumeDto getConsumeDto(ActivityCommon events, int eventType) {
        var consumeData = this.getConsumeModel(events, eventType);
        if (consumeData == null) {
            return null;
        }

        var model = consumeData.getRight();
        int activityId = consumeData.getLeft();

        ActivityProto.ConsumeDto.Builder dto = ActivityProto.ConsumeDto.newBuilder();
        dto.setActivityId(activityId);
        dto.setCompleteCount(model.getCompleteCount());
        dto.addAllFinishList(model.getFinishList());
        dto.setRound(model.getRound());
        return dto.build();
    }

    private Pair<Integer, ActivityCommon.ActivityConsumeModel> getConsumeModel(ActivityCommon events, int eventType) {
        var entity = ActivityManager.getInstance().getEntityByType(eventType);
        if (entity == null) {
            return null;
        }
        int openActivityId = entity.getFirst().getId();
        if (!events.getActivityConsumes().containsKey(openActivityId)) {
            var model = new ActivityCommon.ActivityConsumeModel();
            model.setRound(1);
            model.setRankMail(false);
            model.setRankMailUniqueId(mailService.getUniqueId());
            events.getActivityConsumes().put(openActivityId, model);
            eventsDao.update(events);
        }
        if (events.getActivityConsumes().containsKey(openActivityId)) {
            return Pair.of(openActivityId, events.getActivityConsumes().get(openActivityId));
        }
        return null;
    }


    /**
     * 检测通用活动数据
     */
    private void checkEvents(ActivityCommon events) {

        boolean isUpdate = false;

        events.getActivityConsumes().forEach((activityId, model) -> {
            // 活动结束发排名邮件
            if (ActivityManager.getInstance().isEnd(activityId) && !model.isRankMail()) {
                boolean flag = ActivityManager.getInstance().sendRankMailReward(events.getUserId(), activityId, model.getRankMailUniqueId());
                if (flag) {
                    model.setRankMail(true);
                    eventsDao.update(events);
                }
            }
        });

        // 检测清理数据
        // 任务数据 活动结束超过15天的移除
        isUpdate |= events.getActivityTasks().keySet().removeIf(activityId -> ActivityManager.getInstance().canRemove(activityId));
        // 商店数据 活动结束超过15天的移除
        isUpdate |= events.getActivityShops().keySet().removeIf(activityId -> ActivityManager.getInstance().canRemove(activityId));
        // 消耗数据 活动结束超过15天的移除
        isUpdate |= events.getActivityConsumes().keySet().removeIf(activityId -> ActivityManager.getInstance().canRemove(activityId));

        if (isUpdate) {
            eventsDao.update(events);
        }

        // 检测活动数据是否需要刷新
        this.checkTask(events);
    }

    private void checkTask(ActivityCommon events) {
        long nowTimestamp = DateUtils.getUnixTime();
        if (nowTimestamp >= events.getLastRefTime()) {

            // 判断活动是否结束
            for (Map.Entry<Integer, ActivityCommon.ActivityTaskModel> entry : events.getActivityTasks().entrySet()) {
                if (ActivityManager.getInstance().isEnd(entry.getKey())) {
                    continue;
                }
                // 日常任务刷新
                entry.getValue().getProcess().entrySet().removeIf(e -> gameConfigManager.getEventConfig().getTaskEntity(e.getKey()).getLableType() == 1);
                entry.getValue().getReceive().removeIf(e -> gameConfigManager.getEventConfig().getTaskEntity(e).getLableType() == 1);
            }

            events.setLastRefTime(DateUtils.getSystemResetTime());

            eventsDao.update(events);
        }
    }

    private List<CommonProto.ActivityTaskDto> buildTaskDto(int eventId, ActivityCommon.ActivityTaskModel taskModel) {
        List<CommonProto.ActivityTaskDto> dtos = new ArrayList<>();
        if (taskModel == null) {
            return dtos;
        }

        taskModel.getProcess().forEach((k, v) -> {
            CommonProto.ActivityTaskDto.Builder dto = CommonProto.ActivityTaskDto.newBuilder();
            dto.setId(k);
            dto.setActivityId(eventId);
            dto.setProcess(v);
            dto.setIsReceive(taskModel.getReceive().contains(k));

            dtos.add(dto.build());
        });

        return dtos;
    }

    private List<CommonProto.ActivityShopDto> buildShopDto(int eventId, ActivityCommon.ActivityShopModel shopModel) {
        List<CommonProto.ActivityShopDto> dtos = new ArrayList<>();

        if (shopModel.getIdCounts() == null) {
            return dtos;
        }

        shopModel.getIdCounts().forEach((k, v) -> {
            dtos.add(buildShopDto(eventId, k, v));
        });

        return dtos;
    }

    private CommonProto.ActivityShopDto buildShopDto(int eventId, int id, int count) {
        CommonProto.ActivityShopDto.Builder dto = CommonProto.ActivityShopDto.newBuilder();
        dto.setActivityId(eventId);
        dto.setId(id);
        dto.setCount(count);
        return dto.build();
    }

}

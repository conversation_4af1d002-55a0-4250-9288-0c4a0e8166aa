package com.dxx.game.dto;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.56.1)",
    comments = "Source: battle.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class BattleServiceGrpc {

  private BattleServiceGrpc() {}

  public static final String SERVICE_NAME = "Proto.Battle.BattleService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RChapterCombatReq,
      com.dxx.game.dto.BattleProto.RChapterCombatResp> getHandleCombatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "handleCombat",
      requestType = com.dxx.game.dto.BattleProto.RChapterCombatReq.class,
      responseType = com.dxx.game.dto.BattleProto.RChapterCombatResp.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RChapterCombatReq,
      com.dxx.game.dto.BattleProto.RChapterCombatResp> getHandleCombatMethod() {
    io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RChapterCombatReq, com.dxx.game.dto.BattleProto.RChapterCombatResp> getHandleCombatMethod;
    if ((getHandleCombatMethod = BattleServiceGrpc.getHandleCombatMethod) == null) {
      synchronized (BattleServiceGrpc.class) {
        if ((getHandleCombatMethod = BattleServiceGrpc.getHandleCombatMethod) == null) {
          BattleServiceGrpc.getHandleCombatMethod = getHandleCombatMethod =
              io.grpc.MethodDescriptor.<com.dxx.game.dto.BattleProto.RChapterCombatReq, com.dxx.game.dto.BattleProto.RChapterCombatResp>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "handleCombat"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.dxx.game.dto.BattleProto.RChapterCombatReq.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.dxx.game.dto.BattleProto.RChapterCombatResp.getDefaultInstance()))
              .setSchemaDescriptor(new BattleServiceMethodDescriptorSupplier("handleCombat"))
              .build();
        }
      }
    }
    return getHandleCombatMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RpcPowerReq,
      com.dxx.game.dto.BattleProto.RpcPowerResp> getHandlePowerMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "handlePower",
      requestType = com.dxx.game.dto.BattleProto.RpcPowerReq.class,
      responseType = com.dxx.game.dto.BattleProto.RpcPowerResp.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RpcPowerReq,
      com.dxx.game.dto.BattleProto.RpcPowerResp> getHandlePowerMethod() {
    io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RpcPowerReq, com.dxx.game.dto.BattleProto.RpcPowerResp> getHandlePowerMethod;
    if ((getHandlePowerMethod = BattleServiceGrpc.getHandlePowerMethod) == null) {
      synchronized (BattleServiceGrpc.class) {
        if ((getHandlePowerMethod = BattleServiceGrpc.getHandlePowerMethod) == null) {
          BattleServiceGrpc.getHandlePowerMethod = getHandlePowerMethod =
              io.grpc.MethodDescriptor.<com.dxx.game.dto.BattleProto.RpcPowerReq, com.dxx.game.dto.BattleProto.RpcPowerResp>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "handlePower"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.dxx.game.dto.BattleProto.RpcPowerReq.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.dxx.game.dto.BattleProto.RpcPowerResp.getDefaultInstance()))
              .setSchemaDescriptor(new BattleServiceMethodDescriptorSupplier("handlePower"))
              .build();
        }
      }
    }
    return getHandlePowerMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RGuildBossCombatReq,
      com.dxx.game.dto.BattleProto.RGuildBossCombatResp> getHandleGuildBossMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "handleGuildBoss",
      requestType = com.dxx.game.dto.BattleProto.RGuildBossCombatReq.class,
      responseType = com.dxx.game.dto.BattleProto.RGuildBossCombatResp.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RGuildBossCombatReq,
      com.dxx.game.dto.BattleProto.RGuildBossCombatResp> getHandleGuildBossMethod() {
    io.grpc.MethodDescriptor<com.dxx.game.dto.BattleProto.RGuildBossCombatReq, com.dxx.game.dto.BattleProto.RGuildBossCombatResp> getHandleGuildBossMethod;
    if ((getHandleGuildBossMethod = BattleServiceGrpc.getHandleGuildBossMethod) == null) {
      synchronized (BattleServiceGrpc.class) {
        if ((getHandleGuildBossMethod = BattleServiceGrpc.getHandleGuildBossMethod) == null) {
          BattleServiceGrpc.getHandleGuildBossMethod = getHandleGuildBossMethod =
              io.grpc.MethodDescriptor.<com.dxx.game.dto.BattleProto.RGuildBossCombatReq, com.dxx.game.dto.BattleProto.RGuildBossCombatResp>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "handleGuildBoss"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.dxx.game.dto.BattleProto.RGuildBossCombatReq.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.dxx.game.dto.BattleProto.RGuildBossCombatResp.getDefaultInstance()))
              .setSchemaDescriptor(new BattleServiceMethodDescriptorSupplier("handleGuildBoss"))
              .build();
        }
      }
    }
    return getHandleGuildBossMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static BattleServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<BattleServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<BattleServiceStub>() {
        @java.lang.Override
        public BattleServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new BattleServiceStub(channel, callOptions);
        }
      };
    return BattleServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static BattleServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<BattleServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<BattleServiceBlockingStub>() {
        @java.lang.Override
        public BattleServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new BattleServiceBlockingStub(channel, callOptions);
        }
      };
    return BattleServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static BattleServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<BattleServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<BattleServiceFutureStub>() {
        @java.lang.Override
        public BattleServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new BattleServiceFutureStub(channel, callOptions);
        }
      };
    return BattleServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void handleCombat(com.dxx.game.dto.BattleProto.RChapterCombatReq request,
        io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RChapterCombatResp> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHandleCombatMethod(), responseObserver);
    }

    /**
     */
    default void handlePower(com.dxx.game.dto.BattleProto.RpcPowerReq request,
        io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RpcPowerResp> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHandlePowerMethod(), responseObserver);
    }

    /**
     */
    default void handleGuildBoss(com.dxx.game.dto.BattleProto.RGuildBossCombatReq request,
        io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RGuildBossCombatResp> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHandleGuildBossMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service BattleService.
   */
  public static abstract class BattleServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return BattleServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service BattleService.
   */
  public static final class BattleServiceStub
      extends io.grpc.stub.AbstractAsyncStub<BattleServiceStub> {
    private BattleServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected BattleServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new BattleServiceStub(channel, callOptions);
    }

    /**
     */
    public void handleCombat(com.dxx.game.dto.BattleProto.RChapterCombatReq request,
        io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RChapterCombatResp> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHandleCombatMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void handlePower(com.dxx.game.dto.BattleProto.RpcPowerReq request,
        io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RpcPowerResp> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHandlePowerMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void handleGuildBoss(com.dxx.game.dto.BattleProto.RGuildBossCombatReq request,
        io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RGuildBossCombatResp> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getHandleGuildBossMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service BattleService.
   */
  public static final class BattleServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<BattleServiceBlockingStub> {
    private BattleServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected BattleServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new BattleServiceBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.dxx.game.dto.BattleProto.RChapterCombatResp handleCombat(com.dxx.game.dto.BattleProto.RChapterCombatReq request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHandleCombatMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.dxx.game.dto.BattleProto.RpcPowerResp handlePower(com.dxx.game.dto.BattleProto.RpcPowerReq request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHandlePowerMethod(), getCallOptions(), request);
    }

    /**
     */
    public com.dxx.game.dto.BattleProto.RGuildBossCombatResp handleGuildBoss(com.dxx.game.dto.BattleProto.RGuildBossCombatReq request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getHandleGuildBossMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service BattleService.
   */
  public static final class BattleServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<BattleServiceFutureStub> {
    private BattleServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected BattleServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new BattleServiceFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.dxx.game.dto.BattleProto.RChapterCombatResp> handleCombat(
        com.dxx.game.dto.BattleProto.RChapterCombatReq request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHandleCombatMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.dxx.game.dto.BattleProto.RpcPowerResp> handlePower(
        com.dxx.game.dto.BattleProto.RpcPowerReq request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHandlePowerMethod(), getCallOptions()), request);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.dxx.game.dto.BattleProto.RGuildBossCombatResp> handleGuildBoss(
        com.dxx.game.dto.BattleProto.RGuildBossCombatReq request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getHandleGuildBossMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_HANDLE_COMBAT = 0;
  private static final int METHODID_HANDLE_POWER = 1;
  private static final int METHODID_HANDLE_GUILD_BOSS = 2;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_HANDLE_COMBAT:
          serviceImpl.handleCombat((com.dxx.game.dto.BattleProto.RChapterCombatReq) request,
              (io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RChapterCombatResp>) responseObserver);
          break;
        case METHODID_HANDLE_POWER:
          serviceImpl.handlePower((com.dxx.game.dto.BattleProto.RpcPowerReq) request,
              (io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RpcPowerResp>) responseObserver);
          break;
        case METHODID_HANDLE_GUILD_BOSS:
          serviceImpl.handleGuildBoss((com.dxx.game.dto.BattleProto.RGuildBossCombatReq) request,
              (io.grpc.stub.StreamObserver<com.dxx.game.dto.BattleProto.RGuildBossCombatResp>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getHandleCombatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.dxx.game.dto.BattleProto.RChapterCombatReq,
              com.dxx.game.dto.BattleProto.RChapterCombatResp>(
                service, METHODID_HANDLE_COMBAT)))
        .addMethod(
          getHandlePowerMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.dxx.game.dto.BattleProto.RpcPowerReq,
              com.dxx.game.dto.BattleProto.RpcPowerResp>(
                service, METHODID_HANDLE_POWER)))
        .addMethod(
          getHandleGuildBossMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.dxx.game.dto.BattleProto.RGuildBossCombatReq,
              com.dxx.game.dto.BattleProto.RGuildBossCombatResp>(
                service, METHODID_HANDLE_GUILD_BOSS)))
        .build();
  }

  private static abstract class BattleServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    BattleServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.dxx.game.dto.BattleProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("BattleService");
    }
  }

  private static final class BattleServiceFileDescriptorSupplier
      extends BattleServiceBaseDescriptorSupplier {
    BattleServiceFileDescriptorSupplier() {}
  }

  private static final class BattleServiceMethodDescriptorSupplier
      extends BattleServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    BattleServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (BattleServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new BattleServiceFileDescriptorSupplier())
              .addMethod(getHandleCombatMethod())
              .addMethod(getHandlePowerMethod())
              .addMethod(getHandleGuildBossMethod())
              .build();
        }
      }
    }
    return result;
  }
}

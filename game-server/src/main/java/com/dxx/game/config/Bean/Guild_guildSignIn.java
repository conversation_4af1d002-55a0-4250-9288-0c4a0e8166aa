
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildSignIn {
    public Guild_guildSignIn(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        NeedItemId = _buf.get("NeedItemId").getAsInt();
        NeedItemCount = _buf.get("NeedItemCount").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("OtherReward").getAsJsonArray(); OtherReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  OtherReward.add(_v0); }   }
    }

    public static Guild_guildSignIn deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildSignIn(_buf);
    }

    /**
     * 次数
     */
    public final int ID;
    /**
     * 消耗的道具ID<br/>0代表免费
     */
    public final int NeedItemId;
    /**
     * 消耗的道具数量
     */
    public final int NeedItemCount;
    /**
     * 奖励
     */
    public final java.util.List<String> Reward;
    /**
     * 其他奖励<br/>客户端不需要展示（必须填道具，不需要就填道具+数量0）
     */
    public final java.util.List<String> OtherReward;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "NeedItemId:" + NeedItemId + ","
        + "NeedItemCount:" + NeedItemCount + ","
        + "Reward:" + Reward + ","
        + "OtherReward:" + OtherReward + ","
        + "}";
    }
}


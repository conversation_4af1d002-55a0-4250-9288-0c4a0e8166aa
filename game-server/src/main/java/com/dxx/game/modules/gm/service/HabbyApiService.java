package com.dxx.game.modules.gm.service;

import com.alibaba.fastjson.JSONObject;
import io.netty.handler.codec.http.FullHttpRequest;

import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2024/7/1
 * @description:
 */
public interface HabbyApiService {


    Map<String, Object> unbind(JSONObject params);

    Map<String, Object> habbyStoreAccount(FullHttpRequest request);

    Map<String, Object> habbyStoreDeductItem(JSONObject params);
}

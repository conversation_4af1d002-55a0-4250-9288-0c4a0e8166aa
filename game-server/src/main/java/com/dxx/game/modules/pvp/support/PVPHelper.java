package com.dxx.game.modules.pvp.support;

import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.*;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.ReportDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.battle.BattleService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.user.service.UserService;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/8/17 19:36
 */
@Slf4j
@Component
public class PVPHelper {
    @Autowired
    private CommonService commonService;
    @Autowired
    private ReportDao reportDao;
    @Autowired
    private BattleService battleService;
    @Autowired
    private BattleUnitCache formationCache;
    @Autowired
    private UserService userService;


    public CommonProto.PVPRecordDto buildRecordDto(CommonProto.BattleUserDto pvp1, CommonProto.BattleUserDto pvp2) {
        CommonProto.PVPRecordDto.Builder dto = CommonProto.PVPRecordDto.newBuilder();
        dto.setOwnerUser(pvp1);
        dto.setOtherUser(pvp2);
        return dto.build();
    }

    public CommonProto.PVPRecordDto buildRecordDto(CommonProto.BattleUserDto pvp1, CommonProto.BattleUserDto pvp2,
                                                   int seed, int result,
                                                   List<CommonProto.CombatUnitDto> su, List<CommonProto.CombatUnitDto> eu) {
        CommonProto.PVPRecordDto.Builder dto = CommonProto.PVPRecordDto.newBuilder();
        dto.setOwnerUser(pvp1);
        dto.setOtherUser(pvp2);

        dto.setSeed(seed);
        dto.setResult(result);

        dto.addAllStartUnits(su);
        dto.addAllEndUnits(eu);

        return dto.build();
    }

    public CommonProto.BattleUserDto toBattleUser(int type, long userId, int serverId, String nickName, Integer avatar, Integer avatarFrame, CommonProto.BattleUnitDto battleData) {
        CommonProto.BattleUserDto.Builder battleUserDto = CommonProto.BattleUserDto.newBuilder();

        battleUserDto.setUserId(userId);
        battleUserDto.setServerId(serverId);
        battleUserDto.setNickName(Optional.ofNullable(nickName).orElse(""));
        battleUserDto.setAvatar(Optional.ofNullable(avatar).orElse(0));
        battleUserDto.setAvatarFrame(Optional.ofNullable(avatarFrame).orElse(0));

        if(battleData != null) {
            battleUserDto.setUnit(battleData);
        }
        else {
            battleUserDto.setUnit(formationCache.getBattleUnit(userId, type));
        }

        return battleUserDto.build();
    }

    public CommonProto.BattleUserDto toBattleUser(int type, User user, UserExtend userExtend, CommonProto.BattleUnitDto battleData) {
        CommonProto.BattleUserDto.Builder battleUserDto = CommonProto.BattleUserDto.newBuilder();

        long power = userService.getPower(user.getUserId());
        battleUserDto.setUserId(userExtend.getUserId());
        battleUserDto.setServerId(user.getServerId());
        battleUserDto.setNickName(Optional.ofNullable(user.getNickName()).orElse(""));
        battleUserDto.setAvatar(Optional.ofNullable(user.getAvatar()).orElse(0));
        battleUserDto.setAvatarFrame(Optional.ofNullable(user.getAvatarFrame()).orElse(0));
        battleUserDto.setPower(power);

        if(battleData != null) {
            battleUserDto.setUnit(battleData);
        }
        else {
            battleUserDto.setUnit(formationCache.getBattleUnit(user.getUserId(), type));
        }


        return battleUserDto.build();
    }

    public CommonProto.BattleUserDto toBattleUser(int type, User user, UserExtend userExtend) {
        return toBattleUser(type, user, userExtend, formationCache.getBattleUnit(user.getUserId(), type));
    }

    public CommonProto.PVPRecordDto buildPVPRecord(Report report) {
        CommonProto.PVPRecordDto.Builder dto = CommonProto.PVPRecordDto.newBuilder();

        try {
            if(report == null) {
                return dto.build();
            }

            JsonFormat.parser().merge(report.getReportInfo(), dto);

            dto.setReportRowId(report.getRowId());
            dto.setTime(report.getTime());
        } catch (Exception e) {
            log.error("buildPVPRecord e:", e);
        }

        return dto.build();
    }

    public long getReportId() {
        return commonService.generateId(RedisKeys.REPORT_PRIMARY_KEY);
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Equip_updateLevel {
    public Equip_updateLevel(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        level = _buf.get("level").getAsInt();
        nextID = _buf.get("nextID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("levelupCost").getAsJsonArray(); levelupCost = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  levelupCost.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("ExAttributes").getAsJsonArray(); ExAttributes = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  ExAttributes.add(_v0); }   }
    }

    public static Equip_updateLevel deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Equip_updateLevel(_buf);
    }

    /**
     * 内容ID
     */
    public final int id;
    /**
     * 等级
     */
    public final int level;
    /**
     * 下一等级ID 满级为0
     */
    public final int nextID;
    /**
     * 升至下一级的消耗
     */
    public final java.util.List<String> levelupCost;
    /**
     * 当前等级获得的额外属性<br/>(需要校验，只能包含base属性里有的类型)
     */
    public final java.util.List<String> ExAttributes;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "level:" + level + ","
        + "nextID:" + nextID + ","
        + "levelupCost:" + levelupCost + ","
        + "ExAttributes:" + ExAttributes + ","
        + "}";
    }
}


package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.RechargeOrder;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.RechargeOrderDao;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询玩家信息
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.USER_GET_INFO)
public class GMQueryUserInfo extends AbstractGMProcessor {

    @Resource
    private RechargeOrderDao rechargeOrderDao;
    @Resource
    private UserDao userDao;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private ShopDao shopDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private int searchType;
        private String searchValue;
    }

    @Data
    private static class GMResponse {
        private List<Map<String, Object>> users;
        private Map<String, String> fieldDescription;
    }


    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);

        int searchType = request.getSearchType();
        String searchValue = request.getSearchValue();
        List<User> users = new ArrayList<>();
        User user = null;
        if (searchType == 1) {
            if(!isNumber(searchValue)) {
                return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
            }
            user = userDao.getByUserId(Long.parseLong(searchValue));
            if (user == null) {
                return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
            }
            users.add(user);
        } else if (searchType == 2) {
            users = userDao.getByUsersByDeviceId(searchValue);
        } else if (searchType == 3) {
            users = userDao.getUsersByAccountId(searchValue);
        } else if (searchType == 4) {
            User findUser = userDao.getByNickName(searchValue, 1);
            if (findUser != null) {
                user = userDao.getByUserId(findUser.getUserId());
                users.add(user);
            }
        } else if (searchType == 5) {
            String orderId = searchValue;
            RechargeOrder rechargeOrder = rechargeOrderDao.getByOrderId(orderId);
            if (rechargeOrder != null) {
                long userId = rechargeOrder.getUserId();
                user = userDao.getByUserId(userId);
                if (user == null) {
                    return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
                }
            } else {
                return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "order not exist");
            }
            users.add(user);
        }


        Map<String, Object> result = new HashMap<>();
        // 字段说明
        Map<String, String> fieldDescriptionMap = new HashMap<>();
        fieldDescriptionMap.put("userInfo", "用户信息");
//        fieldDescriptionMap.put("equipList", "装备列表");
//        fieldDescriptionMap.put("heroList", "英雄列表");
//        fieldDescriptionMap.put("rechargeOrders", "充值记录");

        // 用户列表
        List<Map<String, Object>> usersInfo = new ArrayList<>();
        for (User userObj : users) {
            Map<String, Object> userInfo = this.queryUserInfo(userObj);
            usersInfo.add(userInfo);
        }

        GMResponse response = new GMResponse();
        response.setUsers(usersInfo);
        response.setFieldDescription(fieldDescriptionMap);
        return response;

    }

    private Map<String, Object> queryUserInfo(User user) {
        Map<String, Object> result = new HashMap<>();
        UserExtend userExtend = userExtendDao.getByUserId(user.getUserId());
        Shop shop = shopDao.getByUserId(user.getUserId());
        List<Map<String, Object>> userInfo = new ArrayList<>();
        userInfo.add(this.buildKVDM("userId", user.getUserId() + "", "用户ID", false));
        userInfo.add(this.buildKVDM("deviceId", user.getDeviceId(), "deviceId", false));
        userInfo.add(this.buildKVDM("accountId", user.getAccountId(), "账号ID", false));
        userInfo.add(this.buildKVDM("accountId2", user.getAccountId2(), "ios-TeamPlayerID", false));
        userInfo.add(this.buildKVDM("registerIp", user.getUserIp(), "注册IP", false));
        userInfo.add(this.buildKVDM("registerCountry", user.getCountry(), "注册国家代码", false));
        userInfo.add(this.buildKVDM("coins", user.getCoins(), "金币", false));
        userInfo.add(this.buildKVDM("diamonds", user.getDiamonds(), "钻石", false));
        userInfo.add(this.buildKVDM("level", user.getLevel(), "等级", true));
        userInfo.add(this.buildKVDM("chapterId", userExtend.getChapterId(), "当前主线章节ID", true));
        userInfo.add(this.buildKVDM("createTime", DateUtils.stampToDate(user.getCreateTimestamp()), "注册时间", false));
        userInfo.add(this.buildKVDM("logintime", DateUtils.stampToDate(user.getLoginTimestamp()), "最后登录时间", false));
        userInfo.add(this.buildKVDM("accountStatus", user.getAccountStatus() == 1 ? "封号" : "正常", "账号状态", false));
        userInfo.add(this.buildKVDM("systemMask", user.getSystemMask() + "", "系统掩码", true));
        //TODO
//        userInfo.add(this.buildUserData("totalRechargeAmount", shop.getTotalRechargeAmount() + "", "总充值金额", false));
        userInfo.add(this.buildKVDM("netVersion", user.getClientNetVersion() + "", "通信版本号", false));
        long chatBannedTM = 0;
        if (user.getChatBannedTM() != null) {
            chatBannedTM = user.getChatBannedTM();
        }
        userInfo.add(this.buildKVDM("chatBannedTM", chatBannedTM, "聊天禁言截止时间戳", true));

        userInfo.add(this.buildKVDM("serverId", user.getServerId(), "服务器ID", false));

        List<Map<String, Object>> wearEquipList = new ArrayList<>();
//        if (wearEquipRowIds != null) {
//            List<Long> equipRowIds = new ArrayList<>();
//            for (Long equipRowId : wearEquipRowIds) {
//                if (equipRowId > 0) {
//                    equipRowIds.add(equipRowId);
//                }
//            }
//            if (!equipRowIds.isEmpty()) {
//                List<Equip> equips = equipDao.getListByRowIds(user.getUserId(), equipRowIds);
//                for (Equip equip : equips) {
//                    EquipCfgEntity equipEntity = gameConfigManager.getEquipConfig().getEquipCfg().get(equip.getEquipId());
//                    Map<String, Object> e = new HashMap<>();
//                    e.put("rowId", equip.getRowId());
//                    e.put("equipId", equip.getEquipId());
//                    e.put("level", equip.getLevel());
//                    e.put("quality", equipEntity.getQuality());
//                    e.put("name", equipEntity.getName());
//                    wearEquipList.add(e);
//                }
//            }
//        }

        result.put("userInfo", userInfo);
        result.put("wearEquips", wearEquipList);
        return result;
    }


}

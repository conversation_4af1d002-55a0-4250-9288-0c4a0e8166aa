package com.dxx.game.modules.activity.base;

import com.dxx.game.common.error.GameError;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.dao.dynamodb.model.activity.ActivityBase;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.google.protobuf.Message;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.data.util.ProxyUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: lsc
 * @createDate: 2025/6/3
 * @description:
 */
@Component
public class ActivityRegistry {

    @Resource
    private List<ActivityLifeCycle<?, ?>> activityLifeCycles;

    private final TreeMap<Integer, ActivityMetaData<?, ?>> activityTypeMetaDataMap = new TreeMap<>();
    private final Map<Class<? extends ActivityLifeCycle<?, ?>>, ActivityMetaData<?, ?>> activityLifeCycleMetaDataMap = new HashMap<>();

    @SuppressWarnings("unchecked")
    @PostConstruct
    private void init() {
        if (CollectionUtils.isNullOrEmpty(activityLifeCycles)) {
            return;
        }
        for (ActivityLifeCycle<?, ?> activityLifeCycle : activityLifeCycles) {
            try {
                ActivityMetaData<?, ?> activityMetaData = activityLifeCycle.buildMetaData();

                if (activityTypeMetaDataMap.containsKey(activityMetaData.getActivityType())) {
                    throw new GameError("ActivityType already exists: " + activityLifeCycle.getClass().getSimpleName());
                }

                activityMetaData.initialize(activityLifeCycle);

                activityTypeMetaDataMap.put(activityMetaData.getActivityType(), activityMetaData);
                activityLifeCycleMetaDataMap.put(
                        (Class<? extends ActivityLifeCycle<?,?>>) ProxyUtils.getUserClass(activityLifeCycle.getClass()),
                        activityMetaData
                );
            } catch (Exception e) {
                throw new RuntimeException("Failed to initialize activity lifecycle: " + activityLifeCycle.getClass().getSimpleName(), e);
            }
        }
    }



    @SuppressWarnings("unchecked")
    public <M extends ActivityBase, D extends Message> ActivityMetaData<M, D> getActivityMetaData(int activityType) {
        return (ActivityMetaData<M, D>) activityTypeMetaDataMap.get(activityType);
    }

    @SuppressWarnings("unchecked")
    public <M extends ActivityBase, D extends Message> ActivityMetaData<M, D> getActivityMetaData(
            Class<? extends ActivityLifeCycle<M, D>> clazz
    ) {
        return (ActivityMetaData<M, D>) activityLifeCycleMetaDataMap.get(clazz);
    }

    public Collection<ActivityMetaData<?, ?>> getActivityMetaDataList() {
        return Collections.unmodifiableCollection(activityTypeMetaDataMap.values());
    }
}

package com.dxx.game.modules.gm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.habby.model.HabbyConfig;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dao.dynamodb.repository.LogGmRewardDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.gm.service.HabbyApiService;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;

/**
 * @author: lsc
 * @createDate: 2024/7/1
 * @description:
 */
@Slf4j
@Service
public class HabbyApiServiceImpl implements HabbyApiService {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserDao userDao;
    @Resource
    private LogGmRewardDao logGmRewardDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private RewardService rewardService;
    @Resource
    private LogService logService;
    @Resource
    private UserService userService;
    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private ItemDao itemDao;


    //////// habby id unbind ////////
    @DynamoDBTransactional
    @Override
    public Map<String, Object> unbind(JSONObject params) {
//        String habbyId = params.getString("habbyId");
//        String userId = params.getString("userId");
//        String timestamp = params.getString("timestamp");
//        String sign = params.getString("sign");
//        Map<String, String> values = new HashMap<>();
//        values.put("habbyId", habbyId);
//        values.put("userId", userId);
//        values.put("timestamp", timestamp);
//        String secretKey = channelConfig.getHabbyIdSecret();
//        boolean verifySign = this.verifySign(values, secretKey, sign);
//        if (!verifySign) {
//            log.error("habbyIdUnBindError verifySign error, params:{}, secretKey:{}", params, secretKey);
//            return this.error(-1, "verifySign failed");
//        }
//
//        User user = userDao.getByUserId(Long.parseLong(userId));
//        if (user == null) {
//            log.error("habbyIdUnBindError user not exist, params:{}, secretKey:{}", params, secretKey);
//            return this.error(-2, "user not exist");
//        }
//        if (user.getHabbyId() != null && !user.getHabbyId().equals(habbyId)) {
//            log.error("habbyIdUnBindError habbyId error, userBindHabbyId:{}, params:{}, secretKey:{}", user.getHabbyId(), params, secretKey);
//            return this.error(-3, "user bind id error");
//        }
//
//        user.setHabbyId("");
//        user.setBindEmail("");
//        userDao.updateHabbyId(user);

        return this.success();
    }

    @DynamoDBTransactional
    @Override
    public Map<String, Object> habbyStoreAccount(FullHttpRequest request) {
        try {
//            Map<String, String> getParams = HttpRequester.getParams(request);
//            log.info("habbyStoreAccount, params:{}", getParams);
//            if (!getParams.containsKey("gameAccountId") || !getParams.containsKey("sign")) {
//                log.error("habbyStoreAccountVerifyError gameAccountId is null, params:{}", getParams);
//                return this.error(-1, "params error");
//            }
//            String sign = getParams.remove("sign");
//            boolean flag = this.verifySign(getParams, this.getHabbyStoreSecretKey(), sign);
//            if (!flag) {
//                log.error("habbyStoreAccountVerifySignFailed, params:{}", getParams);
//                return this.error(-1, "verify sign failed");
//            }
//
//            long userId = Long.parseLong(getParams.get("gameAccountId"));
//
//            User user = userDao.getByUserId(userId);
//            if (user == null) {
//                return this.error(-1, "user not exist");
//            }
//            CommonHelper.buildCommonParams(user);
//            UserExtend userExtend = userExtendDao.getByUserId(userId);
//            Shop shop = shopDao.getByUserId(userId);
//            int chapterId = Math.min(userExtend.getChapterId(), gameConfigManager.getMaxChapterId());
//
//            Map<String, Object> userInfo = new HashMap<>();
//            userInfo.put("gameUserId", userId);
//            userInfo.put("nickName", Optional.ofNullable(user.getNickName()).orElse(String.valueOf(userId)));
//            userInfo.put("avatar", String.valueOf(Optional.ofNullable(user.getAvatar()).orElse(25000)));
//            userInfo.put("level", user.getLevel());
//            userInfo.put("lastLoginTime", user.getLoginTimestamp());
//            userInfo.put("chapterInfo", String.valueOf(chapterId));
//            userInfo.put("currency", Optional.ofNullable(shop.getLastOrderCurrency()).orElse(""));
//
//            Map<String, Object> result = new HashMap<>();
//            result.put("region", "");
//            result.put("gameAccountId", userId);
//
//            Map<String, Object> userServerList = new HashMap<>();
//            userServerList.put("serverId", "");
//            userServerList.put("userList", userInfo);
//            result.put("userServerList", userServerList);
//
//            return this.success(result);
            return this.success();

        } catch (Exception e) {
            log.error("habbyStoreAccount error:", e);
            return this.error(-1, e.getMessage());
        }
    }

    @DynamoDBTransactional
    @Override
    public Map<String, Object> habbyStoreDeductItem(JSONObject params) {
        try {
//            log.info("habbyStoreDeductItem, params:{}", params);
//
//            Map<String, String> postParams = new HashMap<>();
//            postParams.put("gameUserId", params.getString("gameUserId"));
//            postParams.put("itemId", params.getString("itemId"));
//            postParams.put("itemCount", String.valueOf(params.getIntValue("itemCount")));
//            postParams.put("transId", params.getString("transId"));
//            postParams.put("timestamp", String.valueOf(params.getLongValue("timestamp")));
//            String sign = params.getString("sign");
//
//            boolean flag = this.verifySign(postParams, this.getHabbyStoreSecretKey(), sign);
//            if (!flag) {
//                log.error("habbyStoreDeductItemVerifySignFailed, params:{}", params);
//                return this.error(-1, "verify sign failed");
//            }
//
//            long userId = params.getLongValue("gameUserId");
//            int itemId = params.getIntValue("itemId");
//            int itemCount = params.getIntValue("itemCount");
//            String transId = params.getString("transId");
//
//            User user = userDao.getItemWithoutCache(userId);
//            if (user == null) {
//                return this.error(-1, "user not exist");
//            }
//            RequestContext.setUserId(userId);
//            CommonHelper.buildCommonParams(user);
//            // 加锁
//            if (!redisLock.lock()) {
//                return this.error(-1, "get lock failed");
//            }
//
//            String uniqueId = "habby_store_" + transId;
//            LogGmReward logGmReward = logGmRewardDao.getByUserIdAndUniqueId(userId, uniqueId);
//            if (logGmReward != null) {
//                log.error("habbyStoreStoreDeductItem transId exist, userId:{}, itemId:{}, itemCount:{}, transId:{}",
//                        userId, itemId, itemCount, transId);
//                return this.success();
//            }
//            RequestContext.setCommand((short)4);
//            int costCount = Math.abs(itemCount);
//            Item item = itemDao.getByItemId(userId, itemId);
//            if (item == null) {
//                item = itemService.createItem(userId, itemId, 0);
//            }
//            item.setCount(item.getCount() - costCount);
//            itemDao.update(item);
//
//            long gameTransId = userService.getTransId(userId);
//            logService.sendItem(userId, RequestContext.getCommand(), gameTransId, itemId, -costCount, item.getCount());
//
//            logGmReward = new LogGmReward();
//            logGmReward.setUserId(userId);
//            logGmReward.setUniqueId(uniqueId);
//            logGmReward.setTimestamp(DateUtils.getUnixTime());
//            logGmRewardDao.insert(logGmReward);
//
//            logService.sendBasic(userId, RequestContext.getCommand(), gameTransId, "from_transId:" + transId);
            return this.success();
        } catch (Exception e) {
            log.error("habbyStoreDeductItem error:", e);
            return this.error(-1, e.getMessage());
        }
    }

    private String genSign(Map<String, String> params, String secretKey) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            String value = params.get(key);
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(key).append("=").append(value);
        }

        try {
            Mac hmacSha1 = Mac.getInstance("HmacSHA1");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA1");
            hmacSha1.init(secretKeySpec);
            byte[] hash = hmacSha1.doFinal(sb.toString().getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("habbyId unBind genSign error:", e);
            return null;
        }
    }

    private boolean verifySign(Map<String, String> params, String secretKey, String sign) {
        String signature = genSign(params, secretKey);
        if (StringUtils.isEmpty(signature)) {
            return false;
        }
        return signature.equals(sign);
    }

    private String getHabbyStoreSecretKey() {
        HabbyConfig habbyConfig = channelConfig.getHabbyConfig();
        if (channelConfig.isTest()) {
            return habbyConfig.getHabbyStoreSecretKeyTest();
        } else {
            return habbyConfig.getHabbyStoreSecretKeyProd();
        }
    }

    private Map<String, Object> error(int code, String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("message", msg);
        result.put("data", null);
        return result;
    }

    private Map<String, Object> success(Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "success");
        result.put("data", data);
        return result;
    }

    private Map<String, Object> success() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("message", "success");
        return result;
    }
}

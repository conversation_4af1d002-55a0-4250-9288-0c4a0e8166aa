package com.dxx.game.common.channel.vivo;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.vivo.model.VivoUnifiedOrderResult;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.common.utils.DateUtils;
import com.google.common.collect.Maps;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * vivo
 * <AUTHOR>
 * @date 2022/4/22 10:17
 */
@Slf4j
@Service
public class VivoService implements ChannelService {

    // 渠道ID
    private static final ChannelID CHANNEL_ID = ChannelID.Vivo;

    // 签名key
    public final static String SIGNATURE = "signature";
    // 签名方法key
    public final static String SIGN_METHOD = "signMethod";
    // =
    public static final String QSTRING_EQUAL = "=";
    // &
    public static final String QSTRING_SPLIT = "&";

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private AndroidCallBackService androidCallBackService;

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        if (!channelConfig.getVivoConfig().isVerifyLogin()) {
            return true;
        }
        JSONObject jsonObject = JSONObject.parseObject(verification);
        String openId = jsonObject.getString("openId");
        String authtoken = jsonObject.getString("authToken");
        if (!openId.equals(accountId)) {
            return false;
        }
        String url = channelConfig.getVivoConfig().getVerifyLoginUrl();
        Map<String, String> postData = new HashMap<>();
        postData.put("opentoken", authtoken);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");

        String resp = OkHttpClientUtil.postForm(url, headers, postData, String.class);
        JSONObject respObj = JSONObject.parseObject(resp);

        // 20002	当前游戏账号登录态过期(非VIVO账号登录态)
        // 20000	当前opentoken非法
        int retcode = respObj.getIntValue("retcode");
        if (retcode != 0) {
            log.error("vivo verifyLogin failed, resp:{}", resp);
            return false;
        }
        String respOpenId = respObj.getJSONObject("data").getString("openid");
        if (!respOpenId.equals(accountId)) {
            log.error("vivo verifyLogin failed, resp:{}", resp);
            return false;
        }
        return true;
    }

    public VivoUnifiedOrderResult unifiedOrder(long userId, String accountId, String roleLevel,
                                               String productName, String extInfo, int amount) {

        boolean isWhiteUser = channelConfig.isPayWhiteList(userId);
        if (isWhiteUser) {
            // 测试账号下单固定1分钱
            amount = 1;
        }

        String notifyUrl = channelConfig.getNotifyUrl(CHANNEL_ID);

        //        string vivoSignature = 6;
//        string extuid = 7;
        long timestamp = DateUtils.getUnixTime() + 3600;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date(timestamp * 1000);
        String expireTime = formatter.format(date);
        Map<String, String> params = new HashMap<>();
        params.put("appId", channelConfig.getVivoConfig().getAppId());
        params.put("cpOrderNumber", PaymentUtils.createCpOrderId());
        params.put("orderAmount", String.valueOf(amount));
        params.put("productName", productName);
        params.put("productDesc", productName);
        params.put("notifyUrl", notifyUrl);
//        params.put("expireTime", expireTime);
        params.put("extInfo", extInfo);
        params.put("balance", "0");
        params.put("vip", "0");
        params.put("level", roleLevel);
        params.put("party", "0");
        params.put("roleId", String.valueOf(userId));
        params.put("roleName", String.valueOf(userId));
        params.put("serverName", "1");

        String sign = this.getVivoSign(params, channelConfig.getVivoConfig().getAppSecret());
        VivoUnifiedOrderResult result = new VivoUnifiedOrderResult();
        result.setAppId(params.get("appId"));
        result.setCpOrderNumber(params.get("cpOrderNumber"));
        result.setProductName(params.get("productName"));
        result.setProductDesc(params.get("productDesc"));
        result.setOrderAmount(params.get("orderAmount"));
        result.setVivoSignature(sign);
        if (StringUtils.isEmpty(accountId)) {
            result.setExtuid("");
        } else {
            result.setExtuid(accountId);
        }
        result.setNotifyUrl(notifyUrl);
        result.setExpireTime(expireTime);
        result.setLevel(params.get("level"));
        result.setVip(params.get("vip"));
        result.setBalance(params.get("balance"));
        result.setParty(params.get("party"));
        result.setRoleId(params.get("roleId"));
        result.setRoleName(params.get("roleName"));
        result.setServerName(params.get("serverName"));
        result.setExtInfo(params.get("extInfo"));

        return result;
    }

    /**
     * 支付回调
     * @param request
     * @return
     */
    @Override
    public Object payCb(FullHttpRequest request) {
        Map<String, String> postParams = HttpRequester.getPostParams(request);
        String appId = postParams.get("appId");
        if (!appId.equals(channelConfig.getVivoConfig().getAppId())) {
            log.error("payCb appId error, params:{}", postParams);
            return "fail";
        }

        PayCbVo payCbVo = this.checkOrderState(postParams);
        if (payCbVo == null) {
            return "fail";
        }

        int code = androidCallBackService.doDeliverGoods(payCbVo);
        if (code != AndroidPayCbErrorCode.SUCCESS) {
            log.error("payCb doDeliverGoods failed, body:{}", payCbVo);
            return "fail";
        }

        return "success";
    }

    /**
     * 校验订单状态
     * @param params
     * @return
     */
    private PayCbVo checkOrderState(Map<String, String> params) {
        String key = channelConfig.getVivoConfig().getAppSecret();

        boolean flag = this.verifySignature(params, key);
        if (!flag) {
            log.error("payCb check order state failed sign error, params:{}", params);
            return null;
        }

        String extInfo = params.get("extInfo");
        PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(extInfo);

        String cpOrderId = params.get("cpOrderNumber");
        String orderId = params.get("orderNumber");
        int price = Integer.parseInt(params.get("orderAmount"));

        PayCbVo payCbVo = new PayCbVo();
        payCbVo.setUserId(payBackParamsModel.getUserId());
        payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
        payCbVo.setOrderId(orderId);
        payCbVo.setCpOrderId(cpOrderId);
        payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
        payCbVo.setProductId(payBackParamsModel.getProductId());
        payCbVo.setChannelId(CHANNEL_ID.getId());
        payCbVo.setAmount(price);    
        return payCbVo;
    }

    /**
     * 验证签名
     * @param params
     * @param key
     * @return
     */
    private boolean verifySignature(Map<String, String> params, String key) {
        Map<String, String> filteredReq = this.paraFilter(params);
        // 生成签名字符串
        String signature = this.getVivoSign(filteredReq, key);
        // 获取参数中的签名字符串
        String sign = params.get(SIGNATURE);

        return signature.equals(sign);
    }

    /**
     * 除去请求要素中的空值和签名参数
     * @param params
     * @return
     */
    private Map<String, String> paraFilter(Map<String, String> params) {
        Map<String, String> result = Maps.newHashMap();
        if (params == null || params.size() <= 0) {
            return params;
        }

        for (String key : params.keySet()) {
            String value = params.get(key);
            if (StringUtils.isEmpty(value) || key.equalsIgnoreCase(SIGNATURE) || key.equalsIgnoreCase(SIGN_METHOD)) {
                continue;
            }
            result.put(key, value);
        }
        return result;
    }

    /**
     * 获取签名
     * @param params
     * @param key
     * @return
     */
    private String getVivoSign(Map<String, String> params, String key) {
        String str = this.createLinkString(params);
        str = str + QSTRING_SPLIT + CryptUtil.md5(key).toLowerCase();
        return CryptUtil.md5(str).toLowerCase();
    }

    /**
     * 把请求要素按照“参数=参数值”的模式用“&”字符拼接成字符串
     * @return
     */
    private String createLinkString(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder sBuilder = new StringBuilder();
        for (int i = 0; i < keys.size(); i ++) {
            String key = keys.get(i);
            String value = params.get(key);

            if (i == keys.size() - 1) {
                sBuilder.append(key).append(QSTRING_EQUAL).append(value);
            } else {
                sBuilder.append(key).append(QSTRING_EQUAL).append(value).append(QSTRING_SPLIT);
            }
        }
        return sBuilder.toString();
    }
}

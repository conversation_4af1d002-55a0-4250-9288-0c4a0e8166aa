package com.dxx.game.common.config.game.event;

import com.dxx.game.common.aws.s3.S3Service;
import com.dxx.game.common.config.game.GameConfigSupport;
import com.dxx.game.config.GameConfigManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.File;

/**
 * @authoer: lsc
 * @createDate: 2022/5/31
 * @description:
 */
@Component
@EnableAsync
public class PutConfigToS3Listener implements ApplicationListener<PutConfigToS3Event> {

    @Resource
    private S3Service s3Service;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private GameConfigSupport gameConfigSupport;

    @Async
    @Override
    public void onApplicationEvent(PutConfigToS3Event putConfigToS3Event) {
        if (!StringUtils.isEmpty(putConfigToS3Event.getBucketName()) && !putConfigToS3Event.getFiles().isEmpty()) {
            for (File file : putConfigToS3Event.getFiles()) {
                s3Service.putObject(putConfigToS3Event.getBucketName(), file);
            }
        }

        gameConfigSupport.notifyServersToReloadConfig();
    }
}

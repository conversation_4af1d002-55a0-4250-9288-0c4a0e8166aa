package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.GUILD_MEMBER_GET_INFO)
public class GMGuildMemberGetInfo extends AbstractGMProcessor {

    @Resource
    private GuildUserDao guildUserDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long guildId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class GMResponse {
        private List<Map<String, Object>> guildUsers;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        long guildId = request.getGuildId();
        List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guildId);
        if(guildUsers == null || guildUsers.isEmpty()) {
            log.error("getGuildMemberInfo error, guildUsers not exist. params:{}", params);
            return this.error(GMErrorCode.ERROR_CODE_GUILD_NOT_EXIST, "guildUsers not exist, params:" +params);
        }

        List<Map<String, Object>> guildUserList = guildUsers.stream().map(this::buildGuildMemberInfo).collect(Collectors.toList());
        return GMResponse.builder().guildUsers(guildUserList).build();
    }

    private Map<String, Object> buildGuildMemberInfo(GuildUser guildUser) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> guildMemberInfo = new ArrayList<>();
        guildMemberInfo.add(this.buildKVDM("userId", guildUser.getUserId(), "userId", false));
        guildMemberInfo.add(this.buildKVDM("guildId", guildUser.getGuildId(), "公会ID", false));
        guildMemberInfo.add(this.buildKVDM("position", guildUser.getPosition(), "公会职位", false));
        guildMemberInfo.add(this.buildKVDM("joinTime", DateUtils.stampToDate(guildUser.getJoinTime()), "加入时间", false));
        guildMemberInfo.add(this.buildKVDM("active", guildUser.getActive(), "总活跃度", false));
        result.put("guildMemberInfo", guildMemberInfo);
        return result;
    }
}

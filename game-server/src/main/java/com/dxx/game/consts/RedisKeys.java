package com.dxx.game.consts;

/**
 * redis key
 * <AUTHOR>
 * @date 2019-12-17 18:36
 */
public class RedisKeys {
	public static final String LOCK_CROSS_ARENA_SEASON_END = "cross_arena_season_end";

	/** UserID **/
	public static final String USER_ID_KEY = "user_id_key";

	/** 用户操作ID */
	public static final String USER_TRANSID = "user_trans_id_";

	/** 全服邮件唯一ID string **/
	public static final String SERVER_MAIL_PRIMARY_KEY = "server_mail_primary_key";

	/** 通用唯一ID前缀 + UserID 表分片 **/
	public static final String GENERAL_INCR_ID = "general_incr_id_";


	/** 用户信息 **/
	public static final String USER_INFO = "user_info:";
	/** 用户最高战力 **/
	public static final String USER_POWER_MAX = "user_max_power:";

	/** 战报 **/
	public static final String REPORT_PRIMARY_KEY = "report_primary_key";

	/** 战力排行榜 */
	public static final String RANK_POWER_KEY= "r:power";


	/** 公会战 段位 */
	public static final String GUILD_POWER_KEY= "guild-power-";

	public static final String CROSS_ARENA_BASE = "ca:";
	/** 竞技场 赛季 */
	public static final String CROSS_ARENA_SEASON= CROSS_ARENA_BASE + "s";
	/** 竞技场 分组 */
	public static final String CROSS_ARENA_GROUP= ":g";
	/** 竞技场 分组索引 */
	public static final String CROSS_ARENA_GROUP_INDEX= ":gi";
	/** 竞技场 记录 */
	public static final String CROSS_ARENA_RECORD= ":rd";
	/** 竞技场 分组 */
	public static final String CROSS_ARENA_ROBOT_GEN= ":rge";

	/** 玩家战斗数据 */
	public static final String USER_BATTLE_DATA = "ubdv2:";



	/** 矿点 */
	public static final String ORE_KEY = "ore:";


	/** 会长转让频率 */
	public static final String GUILD_TRAN_FAST_KEY= "gd:ts:";

	/** ab test */
	public static final String AB = "ab:";

	/** 远征 */
	public static final String EXPEDITION_MATCH_KEY = "ExpeditionMatch:%s:%d";

	public static final String STATISTICS = "statistics_data:";


	/** 聊天频率 */
	public static final String CHAT_FREQUENCY = "chat_frequency:";

	/** 服务器信息 */
	public static final String SERVER_INFO = "server_info:";
	public static final String SERVER_ZONE_INFO = "server_zone_info:";
}

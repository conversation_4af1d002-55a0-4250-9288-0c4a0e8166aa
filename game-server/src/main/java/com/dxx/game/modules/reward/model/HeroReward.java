package com.dxx.game.modules.reward.model;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * <AUTHOR>
 * @date 2021/4/1 15:02
 */
public class HeroReward implements Reward {

    /**
     * 奖励类型   {@link RewardType}
     */
    private RewardType type = RewardType.HERO;

    /**
     * 奖励子类型   {@link RewardResourceType}
     */
    private RewardResourceType resourceType = RewardResourceType.NONE;

    /**
     * 数量
     */
    private int count = 0;

    /**
     * 配置表ID
     */
    private int configId = 0;

    /**
     * 等级
     */
    private int level = 1;

    /**
     * 星级
     */
    private int star = 0;

    /**
     * 品质
     */
    private int quality = 1;

    /**
     *
     */
    private int rowId=0;



    public HeroReward(int configId, int count, int level, int star, int quality) {
        this.configId = configId;
        this.count = count;
        this.level = level;
        this.star = star;
        this.quality = quality;
    }

    public HeroReward(int configId, int count, int level, int star, int quality, int rowId) {
        this.configId=configId;
        this.count=count;
        this.level=level;
        this.star=star;
        this.quality=quality;
        this.rowId=rowId;
    }


    public static HeroReward valueOf(int configId, int count, int quality) {
        return new HeroReward(configId, count, 1, 0, quality);
    }

//    public static HeroReward valueOf(int configId, int count, int level, int quality) {
//        return new HeroReward(configId, count, level, 0, 1);
//    }
//
//    public static HeroReward valueOf(int configId, int count, int level, int star, int quality) {
//        return new HeroReward(configId, count, level, star, 1);
//    }

    public static HeroReward valueOf(int configId, int count, int quality, int rowId) {
        return new HeroReward(configId,count,1,0,quality,rowId);
    }


    @Override
    public RewardType getType() {
        return this.type;
    }

    @Override
    public RewardResourceType getResourceType() {
        return this.resourceType;
    }

    @Override
    public int getCount() {
        return this.count;
    }

    @Override
    public int getConfigId() {
        return this.configId;
    }

    @Override
    public Reward increase(int incrCount) {
        return null;
    }

    @Override
    public Reward union(Reward reward) {
        return null;
    }

    @Override
    public boolean match(Reward reward) {
        return false;
    }

    public int getLevel() {
        return level;
    }

    public int getStar() {
        return star;
    }

    public int getQuality() {
        return quality;
    }

    public int getRowId(){
        return rowId;
    }
}

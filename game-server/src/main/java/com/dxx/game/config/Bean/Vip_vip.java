
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Vip_vip {
    public Vip_vip(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        Exp = _buf.get("Exp").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("UnlockReward").getAsJsonArray(); UnlockReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  UnlockReward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Price").getAsJsonArray(); Price = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Price.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("PriceOld").getAsJsonArray(); PriceOld = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  PriceOld.add(_v0); }   }
        Power1 = _buf.get("Power1").getAsString();
        Power2 = _buf.get("Power2").getAsString();
        Power3 = _buf.get("Power3").getAsString();
        Power4 = _buf.get("Power4").getAsString();
        Power5 = _buf.get("Power5").getAsString();
        Power6 = _buf.get("Power6").getAsString();
        Power7 = _buf.get("Power7").getAsString();
        Power8 = _buf.get("Power8").getAsString();
        Power9 = _buf.get("Power9").getAsString();
        Power10 = _buf.get("Power10").getAsString();
        Power11 = _buf.get("Power11").getAsString();
        Power12 = _buf.get("Power12").getAsString();
        Power13 = _buf.get("Power13").getAsString();
        Power14 = _buf.get("Power14").getAsString();
        Power15 = _buf.get("Power15").getAsString();
        Power16 = _buf.get("Power16").getAsString();
        Power17 = _buf.get("Power17").getAsString();
        Power18 = _buf.get("Power18").getAsString();
    }

    public static Vip_vip deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Vip_vip(_buf);
    }

    /**
     * vip等级
     */
    public final int id;
    /**
     * vip升级所需经验
     */
    public final int Exp;
    /**
     * 解锁奖励
     */
    public final java.util.List<String> UnlockReward;
    /**
     * 购买价格（现价）
     */
    public final java.util.List<String> Price;
    /**
     * 展示价格（原价）
     */
    public final java.util.List<String> PriceOld;
    /**
     * 权限1：额外增加快速挂机次数
     */
    public final String Power1;
    /**
     * 权限2：额外增加免费的竞技场门票
     */
    public final String Power2;
    /**
     * 权限3：额外增加挂机获得的金币奖励
     */
    public final String Power3;
    /**
     * 权限4：额外增加挂机获得的英雄经验奖励
     */
    public final String Power4;
    /**
     * 权限5：额外增加挂机获得的粉尘奖励
     */
    public final String Power5;
    /**
     * 权限6：悬赏栏任务数量
     */
    public final String Power6;
    /**
     * 权限7：公会首领挑战次数
     */
    public final String Power7;
    /**
     * 权限8：解锁竞技场跳过战斗
     */
    public final String Power8;
    /**
     * 权限9：解锁悬赏栏一键上阵
     */
    public final String Power9;
    /**
     * 权限10：解锁主线战斗4倍速
     */
    public final String Power10;
    /**
     * 权限11:解锁挑战之塔战斗4倍速
     */
    public final String Power11;
    /**
     * 权限12:解锁挑竞技场战斗4倍速
     */
    public final String Power12;
    /**
     * 权限13:解锁挑战之塔跳过战斗
     */
    public final String Power13;
    /**
     * 权限14:解锁战役跳过战斗
     */
    public final String Power14;
    /**
     * 权限15:黑市刷新次数
     */
    public final String Power15;
    /**
     * 权限16:解除聊天间隔
     */
    public final String Power16;
    /**
     * 权限17:上阵英雄增伤、减伤
     */
    public final String Power17;
    /**
     * 权限18:宝箱产出CD缩短
     */
    public final String Power18;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "Exp:" + Exp + ","
        + "UnlockReward:" + UnlockReward + ","
        + "Price:" + Price + ","
        + "PriceOld:" + PriceOld + ","
        + "Power1:" + Power1 + ","
        + "Power2:" + Power2 + ","
        + "Power3:" + Power3 + ","
        + "Power4:" + Power4 + ","
        + "Power5:" + Power5 + ","
        + "Power6:" + Power6 + ","
        + "Power7:" + Power7 + ","
        + "Power8:" + Power8 + ","
        + "Power9:" + Power9 + ","
        + "Power10:" + Power10 + ","
        + "Power11:" + Power11 + ","
        + "Power12:" + Power12 + ","
        + "Power13:" + Power13 + ","
        + "Power14:" + Power14 + ","
        + "Power15:" + Power15 + ","
        + "Power16:" + Power16 + ","
        + "Power17:" + Power17 + ","
        + "Power18:" + Power18 + ","
        + "}";
    }
}


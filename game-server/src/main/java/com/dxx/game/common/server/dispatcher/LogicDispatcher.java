package com.dxx.game.common.server.dispatcher;

import com.alibaba.fastjson.TypeReference;
import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.channel.common.config.ClientUpdateConfig;
import com.dxx.game.common.log.LogUtils;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisLockContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.protocol.MessageProto;
import com.dxx.game.common.server.util.RequestIdUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.redis.UserRequestRedisDao;
import com.dxx.game.modules.common.support.CommonDataManager;
import com.dxx.game.modules.user.service.UserService;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.handler.HttpResponser;
import com.dxx.game.common.server.handler.RequestProcessor;
import com.dxx.game.common.server.handler.RequestProcessors;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.statistics.StatisticsContext;
import com.dxx.game.common.server.statistics.StatisticsService;
import com.dxx.game.common.utils.IpUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.AppVersion;
import com.dxx.game.config.entity.ForceUpdate;
import com.dxx.game.config.entity.ServerMaintain;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.MsgReqCommand;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;

import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.*;
import io.netty.util.ResourceLeakDetector;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.dxx.game.common.server.consts.HttpProtocolValues;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dxx.game.common.log.DxxLogConsoleAppender.*;

@XRayEnabled
@Component("logicDispather")
@Slf4j
public class LogicDispatcher {

    @Autowired
    private GameConfigManager configManager;
    @Autowired
    private MessageProto messageProto;
    @Autowired
    private UserService userService;
    @Autowired
    private RequestProcessors requestProcessors;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ClientUpdateConfig clientUpdateConfig;
    @Autowired
    private UserRequestRedisDao userRequestDao;
	@Resource
	private StatisticsService statisticsService;
    @Resource
    private CommonDataManager commonDataManager;

    private static LogicDispatcher logicDispatcher;

    @PostConstruct
    public void init() {
        logicDispatcher = this;
    }

    public static void dispatch(ChannelHandlerContext ctx, FullHttpRequest fullRequest, HttpHeaders headers) {
        Message requestParams = null;
        short command = 0;
        long now = System.currentTimeMillis();
        try {
            if (!HttpUtil.isContentLengthSet(fullRequest) || HttpUtil.getContentLength(fullRequest) == 0) {
                log.error("Http Content-Length == 0");
                return;
            }
            if (headers.size() == 0) {
                log.error("headers params size=0");
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.FORMAT_ERROR, "Format error");
                return;
            }

            ByteBuf buff = fullRequest.content();
            if (buff.readableBytes() < HttpProtocolValues.REQ_OBJEC_STREAM_SEEK_START) {
                log.error("invalid body data length={}", buff.readableBytes());
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.FORMAT_ERROR, "Format error");
                return;
            }

            int apiVersion = Integer.parseInt(headers.get(HttpProtocolValues.DXX_VERSION_HEADER));

            // Verify SHA256
            if (verifySHA256(apiVersion, headers, buff)) {
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.APP_VERSION_TOO_LOW, "checksum not consitent");
                return;
            }

            if (buff.readableBytes() >= HttpProtocolValues.REQ_MAX_OBJ_LENGTH) {
                log.error("packet size too large");
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.FORMAT_ERROR, "packet size too large");
                return;
            }

            command = buff.readShortLE();
            int bodyLen = buff.readIntLE();

            if (bodyLen != buff.readableBytes()) {
                // 重新解析bodyLen
                buff.resetReaderIndex();
                buff.readShortLE();
                bodyLen = buff.readUnsignedShortLE();
                if (bodyLen != buff.readableBytes()) {
                    log.error("body length invalid, lengthValue={} bodyLen={}", bodyLen, buff.readableBytes());
                    HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.FORMAT_ERROR, "body length invalid");
                    return;
                }
            }
            byte[] data = new byte[bodyLen];
            buff.getBytes(buff.readerIndex(), data);

            requestParams = logicDispatcher.messageProto.buildRequestParams(data, command);
            if (requestParams == null) {
                log.error("client message to request failed, command = {}", command);
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.BUILD_REQUEST_FAILED, "client message to request failed");
                return;
            }
            RequestContext.setCommand(command);

            StatisticsContext.setCMD(command);

            MDC.put(COMMAND_NAME, requestParams.getClass().getSimpleName());

            // 获取请求处理方法
            RequestProcessor processor = logicDispatcher.requestProcessors.getProcessor(command);
            if (processor == null) {
                log.error("get request processor failed = {}", command);
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.REQUEST_METHOD_NOT_FOUND, "get request processor failed");
                return;
            }

            // 客户端系统 iOS / Android
            String os = headers.get(HttpProtocolValues.GAME_PLATFORM);
            RequestContext.setOs(os);

            //维护检查
            if (!processor.getAnnotation().developApi()) {
                // 停服
                if (checkServerMaintenance(RequestContext.getCommonParams().getDeviceId())) {
                    HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.SERVER_STOP, String.valueOf(logicDispatcher.configManager.getMainConfig().getServerMaintainConfig().getEndTimestamp()));
                    return;
                }
            }

            // 客户端版本强更
            if (command == MsgReqCommand.UserLoginRequest) {
                String url = checkApiVersion(apiVersion, fullRequest.headers());
                if (!StringUtils.isEmpty(url)) {
                    HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.APP_VERSION_TOO_LOW, url);
                    return;
                }
            }

            // 解析accessToken
            if (!processor.getAnnotation().skipAuth()) {
                if (RequestContext.getCommonParams() == null) {
                    HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.USER_ACCESS_TOKEN_ERROR, "");
                    return;
                }
                int code = logicDispatcher.userService.parseAccessToken(RequestContext.getCommonParams());
                if (code != ErrorCode.SUCCESS) {
                    HttpResponser.respErrorMsg(ctx, fullRequest, code, "");
                    return;
                }
            }

            // 加锁
            if (!processor.getAnnotation().skipRedisLock() && !logicDispatcher.redisLock.lock()) {
                log.error("get lock failed command:{}, lock_key: {}", command, RedisLockContext.getLockKey());
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.OPERATION_IS_TOO_FREQUENT, "get lock failed");
                return;
            }

            // 登录状态验证，用户是否存在/是否双开
            if (!processor.getAnnotation().skipLoginStateCheck()) {
                int code = logicDispatcher.userService.checkLoginState(!processor.getAnnotation().developApi());
                if (code != ErrorCode.SUCCESS) {
                    if (code == ErrorCode.USER_BE_SEALED) {
                        // 用户已被封号
                        respBeSealed(ctx, fullRequest);
                    } else {
                        HttpResponser.respErrorMsg(ctx, fullRequest, code, "");
                    }
                    return;
                }
            }

            if (processor.getAnnotation().collectIp()) {
                RequestContext.setClientIp(IpUtils.getRemoteIp(ctx, fullRequest));
            }

            // 国家代码
            String countryCode = "-";
            if (fullRequest.headers().contains("CloudFront-Viewer-Country")) {
                countryCode = fullRequest.headers().get("CloudFront-Viewer-Country");
            }

            RequestContext.setCountryCode(countryCode);

            if (processor.isVoid()) {
                log.error("method is void command:{}, userId: {}", command, RequestContext.getUserId());
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.SERVER_SYSTEM_ERROR, "method is void ");
                return;
            }

            Result<Message> result = null;
            //transid 0 就不要走幂等了 比如wiki
            if (!processor.getAnnotation().skipIdempotent() && !processor.getAnnotation().developApi() && RequestContext.getCommonParams().getTransId() != 0) {
                var requestId = RequestIdUtil.getRequestID(RequestContext.getCommonParams().getTransId(), data);
                RequestContext.setRequestId(requestId);
                result = logicDispatcher.checkIdempotent(command, requestId);
            }

            if (result == null) {
                result = processor.doProcess(requestParams);
            }
            if (result == null) {
                log.error("doProcess return null command = {}, name = {}", command, processor.getName());
                HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.SERVER_SYSTEM_ERROR, "process return null");
                return;
            }

            //给response赋值
            result.setCmd((short) (command + 1));

            // 更新transId
            if (result.getCode() == ErrorCode.SUCCESS) {
                logicDispatcher.messageProto.processResult(result, logicDispatcher.commonDataManager::fillCommonData);
                //保存响应结果
                if (!result.isIdempotent() && RequestContext.getRequestId() != null) {
                    logicDispatcher.userRequestDao.saveResponse(RequestContext.getUserId(), command, RequestContext.getRequestId(), result.getContent().toByteArray());
                }
            } else {
                //error了
                if (result.getCode() == ErrorCode.REQUEST_DATA_DUPLICATE) {
                    logicDispatcher.messageProto.processResult(result, RequestIdUtil::fillTransId);
                } else {
                    logicDispatcher.messageProto.processResult(result, t -> {});
                }
            }

            // 响应结果
            HttpResponser.doSend(ctx, fullRequest, result);

            if (logicDispatcher.configManager.isPrintApiLog()) {
                printApiLog(requestParams, command, now, result);
            }

            // 统计每个接口的总请求次数
            // logicDispatcher.redisService.hIncrBy("api_statistics_version_" + apiVersion, "cmd_" + command);
        } catch (Exception e) {
            log.error("doProcess error: command:{}, userId:{}, params:{}, e = {}",
                    command, RequestContext.getUserId(), requestParams, e);
            HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.SERVER_SYSTEM_ERROR, "system error");
        } finally {
            RequestContext.clear();
            ResponseContext.clear();
            logicDispatcher.redisLock.unlock();
            RedisLockContext.clear();
            logicDispatcher.statisticsService.doRecord();
        }
    }

    private static void printApiLog(Message requestParams, short command, long now, Result<Message> result) throws InvalidProtocolBufferException {
        if(command == 10105 || command == 10121) {
            return;
        }

        String params = JsonFormat.printer().print(requestParams);
        String respData = "";
        if (result.getContent() != null) {
            respData = JsonFormat.printer().print(result.getContent());
        }

        long latencyInMS = System.currentTimeMillis() - now;
        MDC.put(RESP_CODE, String.valueOf(result.getCode()));
        MDC.put(RESP_DATA, respData);
        MDC.put(LATENCY_IN_MS, String.valueOf(latencyInMS));
        if (!LogUtils.isStdOutput()) {
            log.info("Process command:{}:{}, userId:{}, params:{}, respCode:{}, respData:{}, Cost Time:{}", requestParams.getClass().getSimpleName(),
                    command, RequestContext.getUserId(), params, result.getCode(),
                    respData, latencyInMS);
        } else {
            MDC.put(REQUEST_BODY, params);
            log.info("accessLog");
        }
		// ddb容量统计
//            ConsumedCapacityTools.LogDetails(StatisticsContext.getStatisticsContextData().getDdbConsumedCapacity());
    }

    /**
     * 校验服务器状态-是否停服
     * @return
     */
    private static boolean checkServerMaintenance(String deviceId) {

        ServerMaintain serverState = logicDispatcher.configManager.getMainConfig().getServerMaintainConfig();
        long nowTimestamp = DateUtils.getUnixTime();
        if (nowTimestamp >= serverState.getBeginTimestamp() && nowTimestamp <= serverState.getEndTimestamp()) {
            return serverState.getWhiteDeviceIdList() == null ||
                    serverState.getWhiteDeviceIdList().isEmpty() ||
                    !serverState.getWhiteDeviceIdList().contains(deviceId);
        }

        return false;
    }

    /**
     * 客户端强更
     * @param apiVersion
     * @param packageName
     * @param packageId
     * @return
     */
//    private static String checkApiVersion(int apiVersion, String packageName, String packageId) {
//        ClientUpdateConfig.ClientUpdateEntity entity = logicDispatcher.clientUpdateConfig.getConfig(packageName, packageId);
//        if (entity == null) {
////            logger.error("ClientUpdateConfig not exist, packageName:{}, packageId:{}", packageName, packageId);
//            return null;
//        }
//        if (entity.getNetVersion() > apiVersion) {
//            return entity.getUrl();
//        }
//        return null;
//    }

    /**
     * 检测客户端版本是否强更提示
     * @param apiVersion
     * @param headers
     * @return
     */
    private static String checkApiVersion(int apiVersion, HttpHeaders headers) {
        // 针对设备进行版本强更提示
        ForceUpdate forceUpdate = logicDispatcher.configManager.getMainConfig().getForceUpdate();
        String gamePlatForm = headers.get(HttpProtocolValues.GAME_PLATFORM);
        int limitVersion = 0;
        String url = "-1";
        if (!StringUtils.isEmpty(gamePlatForm)) {
            // 排除不需要强更的国家
            String countryCode = headers.get("CloudFront-Viewer-Country");
            boolean isUpdate = true;
            if (countryCode != null && forceUpdate.getExcludeCountry().contains(countryCode)) {
                isUpdate = false;
            }
            if (isUpdate) {
                limitVersion = forceUpdate.getLimitVersion();
                if (gamePlatForm.equals("Android")) {
                    limitVersion = forceUpdate.getAndroidLimitVersion();
                    url = forceUpdate.getAndroidUrl();
                } else if (gamePlatForm.equals("iOS")) {
                    limitVersion = forceUpdate.getIosLimitVersion();
                    url = forceUpdate.getIosUrl();
                }
            }
        } else {
            limitVersion = forceUpdate.getLimitVersion();
        }
        if (limitVersion > apiVersion) {
            return url;
        }

        return "";
    }

    /**
     * 校验客户端请求的有效性
     * @return
     */
    private static boolean verifySHA256(int apiVersion, HttpHeaders headers, ByteBuf buff) {
        String checkSum = headers.get(HttpProtocolValues.DXX_CHECKSUM_HEADER);
        if (null == checkSum || checkSum.length() == 0) {
            log.error("checksum is not present");
            return true;
        }

        String secretKey = "";
        List<AppVersion> appVersions = logicDispatcher.configManager.getMainConfig().getAppVersions();
        for (int i = appVersions.size() - 1; i >= 0; i--) {
            if (apiVersion >= appVersions.get(i).getVersion()) {
                secretKey = appVersions.get(i).getKey();
                break;
            }
        }

        byte[] secretKeyBytes = secretKey.getBytes();
        String timestamp = headers.get(HttpProtocolValues.DXX_TIMESTAMP_HEADER);
        byte[] timestampBytes = timestamp.getBytes();
        int len = buff.readableBytes();
        byte[] contentBytes = new byte[len];
        buff.getBytes(0, contentBytes);

        byte[] verifyChecker = new byte[secretKeyBytes.length + timestampBytes.length + contentBytes.length];
        System.arraycopy(secretKeyBytes, 0, verifyChecker, 0, secretKeyBytes.length);
        System.arraycopy(timestampBytes, 0, verifyChecker, secretKeyBytes.length, timestampBytes.length);
        System.arraycopy(contentBytes, 0, verifyChecker, secretKeyBytes.length + timestampBytes.length,
                contentBytes.length);

        String sha256hex = DigestUtils.sha256Hex(verifyChecker);

        if (!sha256hex.equalsIgnoreCase(checkSum)) {
            log.error("[server side checksum={}, client checksum={}", sha256hex, checkSum);
            return true;
        }
        return false;
    }

    /**
     * 返回已被封号
     */
    private static void respBeSealed(ChannelHandlerContext ctx, FullHttpRequest fullRequest) {
        long now = DateUtils.getUnixTime();
        long frozeTime = ResponseContext.getFrozeTime();
        if (frozeTime == 0) {
            // 默认永久 99 年
            frozeTime = now + 99 * DateUtils.SECONDS_365_DAY;
        }
        JSONObject msg = new JSONObject();
        msg.put("now", now);
        msg.put("froze", frozeTime);
        HttpResponser.respErrorMsg(ctx, fullRequest, ErrorCode.USER_BE_SEALED, msg.toJSONString());
    }

    public static Map<String, Object> internal(ChannelHandlerContext ctx, FullHttpRequest fullRequest, HttpHeaders headers, JSONObject params) {
        Map<String, Object> ret = new HashMap<String, Object>();
        try {
            if (!logicDispatcher.configManager.isTest() && !logicDispatcher.configManager.isDevelop()) {
                return null;
            }
            long now = System.currentTimeMillis();
            ResourceLeakDetector.setLevel(ResourceLeakDetector.Level.ADVANCED);
            if (params == null || !params.containsKey("command")) {
                return null;
            }
            short command = params.getShortValue("command");
            String secret = params.getString("secret");
            if (!secret.equals("acc2eadf31b2729a26efa8589a5dceb4")) {
                return null;
            }

            Message requestParams = logicDispatcher.messageProto.buildRequestParams(params.toJSONString(), command);
            if (requestParams == null) {
                return null;
            }
            // 获取请求处理方法
            RequestProcessor processor = logicDispatcher.requestProcessors.getProcessor(command);
            if (processor == null) {
                log.error("get request processror failed = {}", command);
                return null;
            }

            // 解析accessToken
            if (!processor.getAnnotation().skipAuth() && RequestContext.getCommonParams() != null) {
                int code = logicDispatcher.userService.parseAccessToken(RequestContext.getCommonParams());
                if (code != ErrorCode.SUCCESS) {
                    log.error("parseAccessToken failed, command: {}, params:{}", command, RequestContext.getCommonParams());
                    ret.put("code", code);
                    return ret;
                }
            }

            RequestContext.setCommand(command);
            MDC.put(COMMAND_NAME, requestParams.getClass().getSimpleName());

            // 加锁
            if (!processor.getAnnotation().skipRedisLock() && !logicDispatcher.redisLock.lock()) {
                log.error("get lock failed command:{}, lock_key: {}", command, RedisLockContext.getLockKey());
                return null;
            }

            // 登录状态验证，用户是否存在/是否双开
            if (!processor.getAnnotation().skipLoginStateCheck()) {
                int code = logicDispatcher.userService.checkLoginState(processor.getAnnotation().developApi());
                if (code != ErrorCode.SUCCESS) {
                    log.error("MSG_USER_LOGIN_FAILED, command = {}, commonBase = {}, loginState = {}",
                            command, RequestContext.getCommonParams().toString(), code);
                    ret.put("code", code);
                    return ret;
                }
            }

            if (processor.getAnnotation().collectIp()) {
                RequestContext.setClientIp(IpUtils.getRemoteIp(ctx, fullRequest));
            }

            String countryCode = "-";
            if (fullRequest.headers().contains("CloudFront-Viewer-Country")) {
                countryCode = fullRequest.headers().get("CloudFront-Viewer-Country");
            }

            RequestContext.setCountryCode(countryCode);
            if (!processor.isVoid()) {
                // 非void方法
                Result<Message> result = null;
                try {
                    result = processor.doProcess(requestParams);
                } catch (Exception e) {
                    log.error("doProcess error: command = {}, name = {}, params = {}, e = {}", command, processor.getName(), params, e);
                    result = Result.Error(ErrorCode.SERVER_SYSTEM_ERROR);
                }

                if (result == null) {
                    log.error("doProcess return null code = {}, name = {}", command, processor.getName());
                    return null;
                }

                //给response赋值
                result.setCmd((short) (command + 1));

                // 响应结果
                if (result.getContent() == null) {
                    ret.put("code", result.getCode());
                    return ret;
                }

                ret = JSONObject.parseObject(JsonFormat.printer().print(result.getContent()), new TypeReference<Map<String, Object>>(){});
                ret.put("code", ErrorCode.SUCCESS);

                if (logicDispatcher.configManager.isPrintApiLog()) {
                    printApiLog(requestParams, command, now, result);
                }

                return ret;
            }
            return null;
        } catch (Exception e) {
            log.error("internal e:", e);
            ret.put("code", ErrorCode.SERVER_SYSTEM_ERROR);
            return ret;
        } finally {
            RequestContext.clear();
            ResponseContext.clear();
            logicDispatcher.redisLock.unlock();
            RedisLockContext.clear();
        }
    }

    private Result<Message> checkIdempotent(short cmd, String requestId){
        //没有userid,就不要幂等了
        if (RequestContext.getUserId() == null) {
            return null;
        }
        var resultBytes = userRequestDao.getResponse(RequestContext.getUserId(), cmd, requestId);
        if (resultBytes.length > 0) {
            try {
                //存是response，所以cmd+1
                var responseMsg = logicDispatcher.messageProto.deserializeBytesToMessage(resultBytes, (short) (cmd + 1));
                return Result.Idempotence(responseMsg);
            } catch (Exception e) {
                log.error("checkIdempotent e:", e);
            }
        }
        return null;
    }
}















package com.dxx.game.modules.pay.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.modules.pay.service.RefundProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * @author: lsc
 * @createDate: 2023/12/22
 * @description:
 */
@Slf4j
@Service
public class RefundProcessingServiceImpl implements RefundProcessingService {


    @DynamoDBTransactional
    @Override
    public void revokeItemsAfterRefund(String orderId) {

    }
}

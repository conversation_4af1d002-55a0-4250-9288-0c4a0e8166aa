//package com.dxx.game.modules.gm.processors;
//
//import com.alibaba.fastjson.JSONObject;
//import com.dxx.game.common.utils.DateUtils;
//import com.dxx.game.dao.dynamodb.model.RechargeOrder;
//import com.dxx.game.dao.dynamodb.repository.RechargeOrderDao;
//import com.dxx.game.modules.gm.annotation.GMCommand;
//import com.dxx.game.modules.gm.common.AbstractGMProcessor;
//import com.dxx.game.modules.gm.common.GMCommonReqMsg;
//import com.dxx.game.modules.gm.consts.GMCommandType;
//import lombok.*;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import jakarta.annotation.Resource;
//import java.util.List;
//
///**
// * 查询玩家订单数据
// * @author: lsc
// * @createDate: 2025/4/17
// * @description:
// */
//@Slf4j
//@Component
//@GMCommand(GMCommandType.USER_RECHARGE_LOG)
//public class GMQueryUserRechargeLog extends AbstractGMProcessor {
//
//    @Resource
//    private RechargeOrderDao rechargeOrderDao;
//
//    @Data
//    private static class GMRequest extends GMCommonReqMsg {
//        private long userId;
//    }
//
//
//    @Data
//    @AllArgsConstructor
//    @NoArgsConstructor
//    @Builder
//    private static class GMResponse {
//        private List<RechargeOrder> rechargeOrders;
//    }
//
//    @Override
//    protected Object execute(JSONObject params) {
//        GMRequest request = params.toJavaObject(GMRequest.class);
//        long userId= request.getUserId();
//        List<RechargeOrder> rechargeOrders = rechargeOrderDao.getAllByUserId(userId);
//        for (RechargeOrder rechargeOrder : rechargeOrders) {
//            rechargeOrder.setDate(DateUtils.stampToDate(rechargeOrder.getTimeStamp()));
//        }
//
//        return GMResponse.builder().rechargeOrders(rechargeOrders).build();
//    }
//}

package com.dxx.game.common.aws.opensearch;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.config.AWSConfig;
import com.dxx.game.common.aws.secretmanager.SecretManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest;
import org.opensearch.action.delete.DeleteRequest;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.indices.CreateIndexRequest;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.common.xcontent.XContentType;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * @authoer: lsc
 * @createDate: 2023/3/21
 * @description:
 */
@Slf4j
@Service
public class OpenSearchService {

    private RestHighLevelClient client;

    @Resource
    private SecretManager secretManager;

    @PostConstruct
    private void init() {
        if (StringUtils.isEmpty(AWSConfig.OPEN_SEARCH_DOMAIN_ENDPOINT)) {
            return;
        }
        String openSearchUser = AWSConfig.OPEN_SEARCH_USER;
        String openSearchPassword = AWSConfig.OPEN_SEARCH_PASSWORD;

        if (!StringUtils.isEmpty(AWSConfig.OPEN_SEARCH_PASSWORD_SECRET_NAME)) {
            openSearchPassword = secretManager.getSecret(AWSConfig.OPEN_SEARCH_PASSWORD_SECRET_NAME);
        }

//        Aws4Signer signer = Aws4Signer.create();
//        HttpRequestInterceptor interceptor = new AwsRequestSigningApacheInterceptor("es", signer,
//                awsCredentialsProvider, "eu-central-1");
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(openSearchUser, openSearchPassword));

        RestClientBuilder builder = RestClient.builder(new HttpHost(AWSConfig.OPEN_SEARCH_DOMAIN_ENDPOINT, 443, "https"))
                .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                    @Override
                    public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpAsyncClientBuilder) {
                        return httpAsyncClientBuilder
                                /*.setDefaultRequestConfig(
                                        RequestConfig.custom()
                                                .setConnectTimeout(5000)
                                                .setSocketTimeout(5000)
                                                .build()
                                )
                                .setConnectionManagerShared(true)
                                .setMaxConnTotal(128)
                                .setMaxConnPerRoute(8)*/
                                .setDefaultCredentialsProvider(credentialsProvider)/*.addInterceptorLast(interceptor)*/;
                    }
                });
        this.client = new RestHighLevelClient(builder);

    }

    /**
     * 添加数据
     * @param index
     * @param id
     * @param value
     */
    public void addDocument(String index, String id, Object value) {
        try {
            IndexRequest request = new IndexRequest(index);
            request.id(id);
            request.source(JSONObject.toJSONString(value), XContentType.JSON);
            client.index(request, RequestOptions.DEFAULT);

        } catch (Exception e) {
            log.error("Failed to add document, e:", e);
        }
    }

    /**
     * 删除数据
     * @param index
     * @param id
     */
    public void deleteDocument(String index, String id) {
        try {
            client.delete(new DeleteRequest(index, id), RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("Failed to delete document, e:", e);
        }
    }

    /**
     * 查询数据
     * @param searchRequest
     * @return
     */
    public List<String> queryDocument(SearchRequest searchRequest) {
        try {
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            List<String> result = new ArrayList<>(searchResponse.getHits().getHits().length);
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                result.add(hit.getSourceAsString());
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to query document, e:", e);
            return null;
        }
    }

    /**
     * 删除索引
     * @param indexName
     */
    public void deleteIndex(String indexName) {
        try {
            DeleteIndexRequest deleteIndexRequest = new DeleteIndexRequest(indexName);
            client.indices().delete(deleteIndexRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("Failed to delete index, e:", e);
        }
    }

    // 创建索引
    public void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("Failed to create index, e:", e);
        }
    }

    public boolean indexExists(String indexName) {
        try {
            if (StringUtils.isEmpty(AWSConfig.OPEN_SEARCH_DOMAIN_ENDPOINT)) {
                return true;
            }
            GetIndexRequest getIndexRequest = new GetIndexRequest(indexName);
            return client.indices().exists(getIndexRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("Failed to create index, e:", e);
            return false;
        }
    }

    public <T> List<T> queryDocument(SearchSourceBuilder searchSourceBuilder, String indexName, Class<T> clazz) {
        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(searchSourceBuilder);
        List<String> searchResult = this.queryDocument(searchRequest);
        List<T> result = new ArrayList<>();
        if (searchResult != null) {
            for (String resultItem : searchResult) {
                result.add(JSONObject.parseObject(resultItem, clazz));
            }
        }
        return result;
    }

}

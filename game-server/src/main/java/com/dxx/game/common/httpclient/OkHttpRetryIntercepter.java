package com.dxx.game.common.httpclient;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class OkHttpRetryIntercepter implements Interceptor {

	private static final Logger logger = LoggerFactory.getLogger(OkHttpRetryIntercepter.class);

	public int maxRetryCount;

	public OkHttpRetryIntercepter(int maxRetryCount) {
		this.maxRetryCount = maxRetryCount;
	}

	@Override
	public Response intercept(Chain chain) throws IOException {
		Request request = chain.request();
		Response response = null;
		IOException lastException = null;
		for (int retryCount = 0; retryCount <= maxRetryCount; retryCount++) {
			try {
				response = chain.proceed(request);
				if (response.isSuccessful() || (response.code() < 500 && response.code() != 408)) {
					return response;
				}
				logger.error("Request failed with status code {}. Retrying... ({} out of {})",
						response.code(), retryCount + 1, maxRetryCount);
			} catch (IOException e) {
				lastException = e;
				logger.error("Request failed due to {}. Retrying... ({} out of {})",
						e.getMessage(), retryCount + 1, maxRetryCount);
			}
		}

		if (lastException != null) {
			throw lastException;
		}

		if (response == null) {
			throw new IOException("Failed to execute request after " + maxRetryCount + " retries.");
		}

		return response;
//
//		int retryCount = 0;
//		return retry(chain, retryCount);
	}

//	@SuppressWarnings("resource")
//	private Response retry(Chain chain, int retryCount) {
//		Response response = null;
//		Request request = chain.request();
//
//		String errorMsg = null;
//		try {
//			response = chain.proceed(request);
//
//			while ((response.code() == 408 || response.code() >= 500) && retryCount < maxRetryCount) {
//				retryCount ++;
//				logger.error("OkHttpClient retry count : {}, url :{}", retryCount, request.url().toString());
//				return retry(chain, retryCount);
//			}
//
//		} catch (Exception e) {
//			while (retryCount < maxRetryCount) {
//				retryCount ++;
//				logger.error("OkHttpClient retry count : {}, cause : {}, url :{}", retryCount, e.getMessage(), request.url().toString());
//				return retry(chain, retryCount);
//			}
//
//			errorMsg = e.getMessage();
//		}
//
//		if (response == null && errorMsg != null) {
//			throw new RuntimeException("failed to request, errorMsg:" + errorMsg);
//		}
//		return response;
//	}
}

package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.FlipProto;
import com.dxx.game.modules.activity.service.FlipService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class FlipHandler {
    @Autowired
    private FlipService flipService;

    @ApiMethod(command = MsgReqCommand.FlipOnOpenRequest, name = "翻牌子活动-打开界面调用")
    public Result<FlipProto.FlipOnOpenResponse> onOpen(Message msg) {
        FlipProto.FlipOnOpenRequest params = (FlipProto.FlipOnOpenRequest)msg;
        return flipService.onOpen(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipAccRewardRequest, name = "翻牌子活动-领取累计线索奖励")
    public Result<FlipProto.FlipAccRewardResponse> accReward(Message msg) {
        FlipProto.FlipAccRewardRequest params = (FlipProto.FlipAccRewardRequest)msg;
        return flipService.accReward(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipBuyStepRequest, name = "翻牌子活动-购买步数")
    public Result<FlipProto.FlipBuyStepResponse> buyStep(Message msg) {
        FlipProto.FlipBuyStepRequest params = (FlipProto.FlipBuyStepRequest)msg;
        return flipService.buyStep(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipShowGridRequest, name = "翻牌子活动-展示格子")
    public Result<FlipProto.FlipShowGridResponse> showGrid(Message msg) {
        FlipProto.FlipShowGridRequest params = (FlipProto.FlipShowGridRequest)msg;
        return flipService.showGrid(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipRewardGridRequest, name = "翻牌子活动-领奖格子")
    public Result<FlipProto.FlipRewardGridResponse> rewardGrid(Message msg) {
        FlipProto.FlipRewardGridRequest params = (FlipProto.FlipRewardGridRequest)msg;
        return flipService.rewardGrid(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipClueGridRequest, name = "翻牌子活动-线索格子")
    public Result<FlipProto.FlipClueGridResponse> clueGrid(Message msg) {
        FlipProto.FlipClueGridRequest params = (FlipProto.FlipClueGridRequest)msg;
        return flipService.clueGrid(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipBombGridRequest, name = "翻牌子活动-炸弹格子")
    public Result<FlipProto.FlipBombGridResponse> bombGrid(Message msg) {
        FlipProto.FlipBombGridRequest params = (FlipProto.FlipBombGridRequest)msg;
        return flipService.bombGrid(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipMapFindSpecialRequest, name = "翻牌子活动-地图是否有特殊奖励未领取")
    public Result<FlipProto.FlipMapFindSpecialResponse> hasSpecila(Message msg) {
        FlipProto.FlipMapFindSpecialRequest params = (FlipProto.FlipMapFindSpecialRequest)msg;
        return flipService.hasSpecila(params);
    }

    @ApiMethod(command = MsgReqCommand.FlipAllAccRewardRequest, name = "翻牌子活动-一键领取累计线索奖励")
    public Result<FlipProto.FlipAllAccRewardResponse> allAccReward(Message msg) {
        FlipProto.FlipAllAccRewardRequest params = (FlipProto.FlipAllAccRewardRequest)msg;
        return flipService.allAccReward(params);
    }
}

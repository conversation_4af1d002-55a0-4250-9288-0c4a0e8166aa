package com.dxx.game.modules.shop.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dto.ShopProto.*;

public interface ShopService {


    /**
     * 获取商店相关数据
     * @param params
     * @return
     */
    Result<ShopGetInfoResponse> getInfoAction(ShopGetInfoRequest params);

    /**
     * 抽卡
     * @param params
     * @return
     */
    Result<ShopDoGachaResponse> doGachaAction(ShopDoGachaRequest params);

    /**
     * 积分商店获取数据
     */
    Result<ShopIntegralGetInfoResponse> integralGetInfoAction(ShopIntegralGetInfoRequest params);

    /**
     * 积分商店刷新道具
     */
    Result<ShopIntegralRefreshResponse> refreshIntegralAction(ShopIntegralRefreshRequest params);

    /**
     * 积分商店购买道具
     */
    Result<ShopIntegralBuyItemResponse> integralBuyItemAction(ShopIntegralBuyItemRequest params);

    /**
     * 抽卡许愿
     */
    Result<	ShopGacheWishResponse> gachaWishAction(ShopGacheWishRequest params);

    /**
     * 免费道具
     */
    Result<ShopFreeIAPItemResponse> free(ShopFreeIAPItemRequest params);

    /**
     * 领取
     */
    Result<MonthCardGetRewardResponse> monthCardGetReward(MonthCardGetRewardRequest params);



    /**
     * 领取VIP等级礼包
     */
    Result<VIPLevelRewardResponse> vipLevelReward(VIPLevelRewardRequest params);



    /**
     * 领取首充礼包
     */
    Result<FirstRechargeRewardResponse> firstRechargeReward(FirstRechargeRewardRequest params);

    /**
     * 初始化福利卡
     */
    void initWealCard(Shop shop);
}

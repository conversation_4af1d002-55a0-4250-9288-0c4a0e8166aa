<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>game - wiki</title>
    <link href="/css/bootstrap.css" rel="stylesheet">
    <script src="/js/jquery.min.js"></script>
    <script src="/js/jsonview.js"></script>
    <script src="/js/bootstrap.min.js"></script>
    <script src="/js/layer.min.js"></script>
    <link href="/css/jsonview.css" rel="stylesheet">
</head>
<body>
<hr/>
    <div>
        <label id="api">api:</label></br>
        <label id="cmd">cmd:</label></br>
        <label>params:</label>
    </div>
    <div class="col-md-5" style="padding:0px;height:100%;width: 30%;">
        <div class="numberedtextarea-wrapper ">
            <textarea id="requestData" placeholder="在此输入json格式data数据..." class="form-control common-font-size" style="height:100%;height: 87vh;min-height:520px;padding:10px 10px 10px 30px;border:0;border-right:solid 1px #E5EBEE;border-bottom:solid 1px #eee;border-radius:0;resize: none; outline:none;"></textarea>
        </div>
    </div>

    <div class="col-md-7" style="padding:3px;position:relative;height:100%;width: 40%;">
        <button type="button" id="doApiBtn" class="btn btn-primary btn-sm btn-block">点此请求</button>
        
        <div id="right-box" class="common-font-size" style="width:100%;min-height:488px;height: 80vh;border:solid 1px #f6f6f6;border-radius:0;resize: none;overflow-y:scroll;outline:none;position:relative;padding-top:10px;">
        </div>
    </div>


    <div class="col-md-7" style="padding:0;position:relative;height:100%;width: 30%;">
        
        <div id="right-box" class="common-font-size" style="width:100%;min-height:520px;height: 87vh;border:solid 1px #f6f6f6;border-radius:0;resize: none;outline:none;position:relative;padding-top:10px;">
            <label>参数说明:</label>
            <div id="requestParams">
            </div>
            <div id="responseParams">
            </div>
        </div>
    </div>

    <br style="clear:both;">
    <div class="status"></div>

</body>
</html>
<script type="text/javascript">
    var data = "<%= locals.data%>";
    var cmd = "<%= locals.cmd%>";
    var api = "<%= locals.api%>";
    var transId = "<%= locals.transId%>";

    console.log(transId);

    var reqParams = "<%= locals.reqParams%>";
    var resParams = "<%= locals.resParams%>";

    reqParams = JSON.parse(replaceAll(reqParams));
    resParams = JSON.parse(replaceAll(resParams));

    // commonData 对象参数名称
    var commonDataParamsName = "";
    var updateTransIdName = "";
    for (var key in resParams) {
        if (resParams[key]['type'].indexOf("CommonData") >= 0) {
            commonDataParamsName = resParams[key]['paramName'];
            var resCustom = resParams[key]['custom'];
            for (var customKey in resCustom) {
                if (resCustom[customKey]['type'] == "UpdateTransId") {
                    updateTransIdName = resCustom[customKey]['paramName'];
                    break;
                }
            }
            break;
        }
    }



    var serverIdx = "<%= locals.serverIdx%>";
    // 请求参数赋值
    var requestData = JSON.parse(replaceAll(data));
    requestData['commonParams']['accessToken'] = requestData['commonParams']['accessToken'].replace(new RegExp(/( )/g),"+");
    requestData['commonParams']['transId'] = parseInt(transId) + 1;
    requestData['commonParams']['serverId'] = 0;
    requestData['commonParams']['languageMark'] = "EDITOR";

    $("#requestData").val(JSON.stringify(requestData, null, 4));

    $("#cmd").html("cmd:" + cmd);
    $("#api").html("api:" + api);

    // response json 样式
    $('.hoverable').hover(function() {
        $(this).addClass('hovered');
    }, function() {
        $(this).removeClass('hovered');
    });

    // 处理请求
    $('#doApiBtn').click(function() {
        var index = parent.layer.load(1, {
            shade: [0.1,'#fff'] //0.1透明度的白色背景
        });
        $.ajax({
            type: "GET",
            url: "/doApi",
            data: {
                cmd:cmd,
                params:$("#requestData").val(),
                serverIdx:serverIdx
            },
            success: function(result) {
                parent.layer.close(index);
                if (result == "-1") {
                    $('#right-box').html("cmd : " + cmd + " not exist");
                } else if (result == "-2") {
                    $('#right-box').html("not found protoJson cmd : " + cmd);
                } else {
                    var responseObj = JSON.parse(result);
                    console.log(responseObj);
                    $('#right-box').html(objectToHTML(responseObj));

                    // 更新transId
                    if (commonDataParamsName != "" && updateTransIdName != "") {
                        var commonDataResp = responseObj[commonDataParamsName];
                        var updateTransIdObj = commonDataResp[updateTransIdName];
                        if (updateTransIdObj['isChange']) {
                            var domReqData = JSON.parse($("#requestData").val());
                            domReqData['commonParams']['transId'] = parseInt(updateTransIdObj['transId']) + 1;
                            $("#requestData").val(JSON.stringify(domReqData, null, 4));
                        }
                    }
                }
            },
            fail: function() {
                parent.layer.close(index);
            }
        });
    });


    document.body.addEventListener('mouseover', onmouseMove, false);
    document.body.addEventListener('click', ontoggle, false);
    

    var html = getParamsHtml(reqParams);
    $("#requestParams").html("request: " + html);

    html = getParamsHtml(resParams);
    $("#responseParams").html("response: " + html);


    function getParamsHtml(params) {
        
        var html = getParamsHeaderHtml();
        for (var i = 0; i < params.length; i ++) {
            
            var rule = params[i]['rule'];
            var type = params[i]['type'];
            if (rule == "list") {
                type = "list:" + type;
            }

            if (params[i]['isCustom'] && params[i].hasOwnProperty('custom')) {
                var customData = params[i]['custom'];
                var customHtml = "<div>";
                for (var j = 0; j < customData.length; j ++) {

                    var paramName = customData[j]['paramName'];
                    var paramType = customData[j]['type'];
                    if (customData[j]['rule'] == 'list') {  
                        paramType = '<span style="color:#337ab7;font-weight:bold;">list </span>[' + paramType + ']';
                    }

                    customHtml += customData[j]['paramName'] + " : " + paramType + " : " + customData[j]['desc'] + '<hr style="margin-top: 2px;margin-bottom: 2px;">';
                }
                customHtml += "</div>";
                // data-trigger='focus'  data-trigger='hover click'
                type = "<a href='javascript:void(0);' class='popHref' title='" + type + "' data-toggle='popover' data-html='true'  data-content='" + customHtml + "' data-placement='bottom'>" + type + "</a>"
            }

            html += "<tr>";
            html += "<td style='padding: 2px;word-wrap:break-word;word-break:break-all;'>" + params[i]['paramName'] + "</td>";
            html += "<td style='padding: 2px;word-wrap:break-word;word-break:break-all;'>" + type + "</td>";
            html += "<td style='padding: 2px;word-wrap:break-word;word-break:break-all;'>" + params[i]['desc'] + "</td>";
            html += "</tr>";
        }

        html += getParamsEndHtml();
        return html;
    }

    function getParamsHeaderHtml() {
        var html = "";
        html += '<table class="table table-bordered" style="table-layout=fixed; width: 100%">';
        html += "            <caption></caption>";
        html += "            <thead>";
        html += "                <tr>";
        html += "                    <th style='padding: 2px;word-wrap:break-word;word-break:break-all;'>参数</th>";
        html += "                    <th style='padding: 2px;word-wrap:break-word;word-break:break-all;'>类型</th>";
        html += "                    <th style='padding: 2px;word-wrap:break-word;word-break:break-all;'>注释</th>";
        html += "                </tr>";
        html += "            </thead>";
        html += "            <tbody>";
        return html;
    }

    function getParamsEndHtml() {
        var html = "";
        html += "            </tbody>";
        html += "            </table>";
        return html;
    }
    // $("[data-toggle='popover']").popover({trigger: 'focus'});
    $(".popHref").popover({
        html: true,
        trigger: "hover",
        delay: {hide: 200}
    }).on('shown.bs.popover', function (event) {
        var that = this;
        $(this).parent().find('div.popover').on('mouseenter', function () {
            $(that).attr('in', true);
        }).on('mouseleave', function () {
            $(that).removeAttr('in');
            $(that).popover('hide');
        });
    }).on('hide.bs.popover', function (event) {
        if ($(this).attr('in')) {
            event.preventDefault();
        }
    });

   
</script>












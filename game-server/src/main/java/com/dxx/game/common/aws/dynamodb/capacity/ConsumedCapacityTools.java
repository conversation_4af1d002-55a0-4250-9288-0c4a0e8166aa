package com.dxx.game.common.aws.dynamodb.capacity;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.log.DxxLogConsoleAppender;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.dxx.game.common.log.DxxLogConsoleAppender.TRACE_ID;

/**
 * @author: lsc
 * @createDate: 2024/11/11
 * @description:
 */
@Slf4j
public class ConsumedCapacityTools {
    public static Triple<Double, Double, Double> getConsumedCapacity(List<ConsumedCapacityInfo> list)  {
        Double totalCapacityUnits = 0.0;
        Double totalReadCapacityUnits = 0.0;
        Double totalWriteCapacityUnits = 0.0;
        for (ConsumedCapacityInfo consumedCapacityInfo : list) {
            totalCapacityUnits += consumedCapacityInfo.totalCapacityUnits;
            totalReadCapacityUnits += consumedCapacityInfo.totalReadCapacityUnits;
            totalWriteCapacityUnits += consumedCapacityInfo.totalWriteCapacityUnits;
        }
        return Triple.of(totalCapacityUnits, totalReadCapacityUnits, totalWriteCapacityUnits);
    }

    public static void LogDetails(List<ConsumedCapacityInfo> list)  {
        for (ConsumedCapacityInfo info : list) {
            LogTransactWrite(info);
        }
    }

    private static void LogTransactWrite(ConsumedCapacityInfo info)  {
        JSONObject logContent = new JSONObject(true);
        DxxLogConsoleAppender.put(TRACE_ID, logContent);
        logContent.put("operation", info.operation.getType());
        logContent.put("detail", info.infos);

        logContent.put("logLevel", "info");
        logContent.put("message", "consumed capacity");

        logContent.put("totalCapacityUnits", info.totalCapacityUnits);
        logContent.put("totalReadCapacityUnits", info.totalReadCapacityUnits);
        logContent.put("totalWriteCapacityUnits", info.totalWriteCapacityUnits);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = LocalDateTime.now().format(formatter);

        logContent.put("time", formattedDateTime);
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
            String jsonString = mapper.writeValueAsString(logContent);
            log.info(jsonString);
//            System.out.println(jsonString);
        } catch (Exception e) {
            log.error("LogTransactWrite error", e);
        }
    }
}

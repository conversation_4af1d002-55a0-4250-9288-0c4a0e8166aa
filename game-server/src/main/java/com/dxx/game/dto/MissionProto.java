// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: mission.proto

package com.dxx.game.dto;

public final class MissionProto {
  private MissionProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code Proto.Mission.MissionType}
   */
  public enum MissionType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 主线章节
     * </pre>
     *
     * <code>MAIN = 0;</code>
     */
    MAIN(0),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 主线章节
     * </pre>
     *
     * <code>MAIN = 0;</code>
     */
    public static final int MAIN_VALUE = 0;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MissionType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static MissionType forNumber(int value) {
      switch (value) {
        case 0: return MAIN;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MissionType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MissionType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MissionType>() {
            public MissionType findValueByNumber(int number) {
              return MissionType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.getDescriptor().getEnumTypes().get(0);
    }

    private static final MissionType[] VALUES = values();

    public static MissionType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MissionType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:Proto.Mission.MissionType)
  }

  public interface MissionGetInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionGetInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10301 关卡-获取数据
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionGetInfoRequest}
   */
  public static final class MissionGetInfoRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionGetInfoRequest)
      MissionGetInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionGetInfoRequest.newBuilder() to construct.
    private MissionGetInfoRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionGetInfoRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionGetInfoRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionGetInfoRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionGetInfoRequest.class, com.dxx.game.dto.MissionProto.MissionGetInfoRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionGetInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionGetInfoRequest other = (com.dxx.game.dto.MissionProto.MissionGetInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionGetInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10301 关卡-获取数据
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionGetInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionGetInfoRequest)
        com.dxx.game.dto.MissionProto.MissionGetInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionGetInfoRequest.class, com.dxx.game.dto.MissionProto.MissionGetInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionGetInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionGetInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetInfoRequest build() {
        com.dxx.game.dto.MissionProto.MissionGetInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetInfoRequest buildPartial() {
        com.dxx.game.dto.MissionProto.MissionGetInfoRequest result = new com.dxx.game.dto.MissionProto.MissionGetInfoRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionGetInfoRequest) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionGetInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionGetInfoRequest other) {
        if (other == com.dxx.game.dto.MissionProto.MissionGetInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionGetInfoRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionGetInfoRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionGetInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionGetInfoRequest)
    private static final com.dxx.game.dto.MissionProto.MissionGetInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionGetInfoRequest();
    }

    public static com.dxx.game.dto.MissionProto.MissionGetInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionGetInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<MissionGetInfoRequest>() {
      @java.lang.Override
      public MissionGetInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionGetInfoRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionGetInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionGetInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionGetInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionGetInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionGetInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Mission.MainMission mainMission = 2;</code>
     * @return Whether the mainMission field is set.
     */
    boolean hasMainMission();
    /**
     * <code>.Proto.Mission.MainMission mainMission = 2;</code>
     * @return The mainMission.
     */
    com.dxx.game.dto.MissionProto.MainMission getMainMission();
    /**
     * <code>.Proto.Mission.MainMission mainMission = 2;</code>
     */
    com.dxx.game.dto.MissionProto.MainMissionOrBuilder getMainMissionOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10302
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionGetInfoResponse}
   */
  public static final class MissionGetInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionGetInfoResponse)
      MissionGetInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionGetInfoResponse.newBuilder() to construct.
    private MissionGetInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionGetInfoResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionGetInfoResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionGetInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.MissionProto.MainMission.Builder subBuilder = null;
              if (mainMission_ != null) {
                subBuilder = mainMission_.toBuilder();
              }
              mainMission_ = input.readMessage(com.dxx.game.dto.MissionProto.MainMission.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(mainMission_);
                mainMission_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionGetInfoResponse.class, com.dxx.game.dto.MissionProto.MissionGetInfoResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int MAINMISSION_FIELD_NUMBER = 2;
    private com.dxx.game.dto.MissionProto.MainMission mainMission_;
    /**
     * <code>.Proto.Mission.MainMission mainMission = 2;</code>
     * @return Whether the mainMission field is set.
     */
    @java.lang.Override
    public boolean hasMainMission() {
      return mainMission_ != null;
    }
    /**
     * <code>.Proto.Mission.MainMission mainMission = 2;</code>
     * @return The mainMission.
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MainMission getMainMission() {
      return mainMission_ == null ? com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance() : mainMission_;
    }
    /**
     * <code>.Proto.Mission.MainMission mainMission = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MainMissionOrBuilder getMainMissionOrBuilder() {
      return getMainMission();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (mainMission_ != null) {
        output.writeMessage(2, getMainMission());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (mainMission_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getMainMission());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionGetInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionGetInfoResponse other = (com.dxx.game.dto.MissionProto.MissionGetInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasMainMission() != other.hasMainMission()) return false;
      if (hasMainMission()) {
        if (!getMainMission()
            .equals(other.getMainMission())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasMainMission()) {
        hash = (37 * hash) + MAINMISSION_FIELD_NUMBER;
        hash = (53 * hash) + getMainMission().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionGetInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10302
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionGetInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionGetInfoResponse)
        com.dxx.game.dto.MissionProto.MissionGetInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionGetInfoResponse.class, com.dxx.game.dto.MissionProto.MissionGetInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionGetInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (mainMissionBuilder_ == null) {
          mainMission_ = null;
        } else {
          mainMission_ = null;
          mainMissionBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionGetInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetInfoResponse build() {
        com.dxx.game.dto.MissionProto.MissionGetInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetInfoResponse buildPartial() {
        com.dxx.game.dto.MissionProto.MissionGetInfoResponse result = new com.dxx.game.dto.MissionProto.MissionGetInfoResponse(this);
        result.code_ = code_;
        if (mainMissionBuilder_ == null) {
          result.mainMission_ = mainMission_;
        } else {
          result.mainMission_ = mainMissionBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionGetInfoResponse) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionGetInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionGetInfoResponse other) {
        if (other == com.dxx.game.dto.MissionProto.MissionGetInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasMainMission()) {
          mergeMainMission(other.getMainMission());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionGetInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionGetInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.MissionProto.MainMission mainMission_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MainMission, com.dxx.game.dto.MissionProto.MainMission.Builder, com.dxx.game.dto.MissionProto.MainMissionOrBuilder> mainMissionBuilder_;
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       * @return Whether the mainMission field is set.
       */
      public boolean hasMainMission() {
        return mainMissionBuilder_ != null || mainMission_ != null;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       * @return The mainMission.
       */
      public com.dxx.game.dto.MissionProto.MainMission getMainMission() {
        if (mainMissionBuilder_ == null) {
          return mainMission_ == null ? com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance() : mainMission_;
        } else {
          return mainMissionBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      public Builder setMainMission(com.dxx.game.dto.MissionProto.MainMission value) {
        if (mainMissionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mainMission_ = value;
          onChanged();
        } else {
          mainMissionBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      public Builder setMainMission(
          com.dxx.game.dto.MissionProto.MainMission.Builder builderForValue) {
        if (mainMissionBuilder_ == null) {
          mainMission_ = builderForValue.build();
          onChanged();
        } else {
          mainMissionBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      public Builder mergeMainMission(com.dxx.game.dto.MissionProto.MainMission value) {
        if (mainMissionBuilder_ == null) {
          if (mainMission_ != null) {
            mainMission_ =
              com.dxx.game.dto.MissionProto.MainMission.newBuilder(mainMission_).mergeFrom(value).buildPartial();
          } else {
            mainMission_ = value;
          }
          onChanged();
        } else {
          mainMissionBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      public Builder clearMainMission() {
        if (mainMissionBuilder_ == null) {
          mainMission_ = null;
          onChanged();
        } else {
          mainMission_ = null;
          mainMissionBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      public com.dxx.game.dto.MissionProto.MainMission.Builder getMainMissionBuilder() {
        
        onChanged();
        return getMainMissionFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      public com.dxx.game.dto.MissionProto.MainMissionOrBuilder getMainMissionOrBuilder() {
        if (mainMissionBuilder_ != null) {
          return mainMissionBuilder_.getMessageOrBuilder();
        } else {
          return mainMission_ == null ?
              com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance() : mainMission_;
        }
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MainMission, com.dxx.game.dto.MissionProto.MainMission.Builder, com.dxx.game.dto.MissionProto.MainMissionOrBuilder> 
          getMainMissionFieldBuilder() {
        if (mainMissionBuilder_ == null) {
          mainMissionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.MissionProto.MainMission, com.dxx.game.dto.MissionProto.MainMission.Builder, com.dxx.game.dto.MissionProto.MainMissionOrBuilder>(
                  getMainMission(),
                  getParentForChildren(),
                  isClean());
          mainMission_ = null;
        }
        return mainMissionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionGetInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionGetInfoResponse)
    private static final com.dxx.game.dto.MissionProto.MissionGetInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionGetInfoResponse();
    }

    public static com.dxx.game.dto.MissionProto.MissionGetInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionGetInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<MissionGetInfoResponse>() {
      @java.lang.Override
      public MissionGetInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionGetInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionGetInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionGetInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionGetInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionStartRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionStartRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
     * @return Whether the startDto field is set.
     */
    boolean hasStartDto();
    /**
     * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
     * @return The startDto.
     */
    com.dxx.game.dto.MissionProto.MissionStartDto getStartDto();
    /**
     * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
     */
    com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder getStartDtoOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10303 关卡-开始战斗
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionStartRequest}
   */
  public static final class MissionStartRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionStartRequest)
      MissionStartRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionStartRequest.newBuilder() to construct.
    private MissionStartRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionStartRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionStartRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionStartRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              com.dxx.game.dto.MissionProto.MissionStartDto.Builder subBuilder = null;
              if (startDto_ != null) {
                subBuilder = startDto_.toBuilder();
              }
              startDto_ = input.readMessage(com.dxx.game.dto.MissionProto.MissionStartDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(startDto_);
                startDto_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionStartRequest.class, com.dxx.game.dto.MissionProto.MissionStartRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int STARTDTO_FIELD_NUMBER = 2;
    private com.dxx.game.dto.MissionProto.MissionStartDto startDto_;
    /**
     * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
     * @return Whether the startDto field is set.
     */
    @java.lang.Override
    public boolean hasStartDto() {
      return startDto_ != null;
    }
    /**
     * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
     * @return The startDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionStartDto getStartDto() {
      return startDto_ == null ? com.dxx.game.dto.MissionProto.MissionStartDto.getDefaultInstance() : startDto_;
    }
    /**
     * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder getStartDtoOrBuilder() {
      return getStartDto();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (startDto_ != null) {
        output.writeMessage(2, getStartDto());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (startDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getStartDto());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionStartRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionStartRequest other = (com.dxx.game.dto.MissionProto.MissionStartRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (hasStartDto() != other.hasStartDto()) return false;
      if (hasStartDto()) {
        if (!getStartDto()
            .equals(other.getStartDto())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      if (hasStartDto()) {
        hash = (37 * hash) + STARTDTO_FIELD_NUMBER;
        hash = (53 * hash) + getStartDto().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionStartRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10303 关卡-开始战斗
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionStartRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionStartRequest)
        com.dxx.game.dto.MissionProto.MissionStartRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionStartRequest.class, com.dxx.game.dto.MissionProto.MissionStartRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionStartRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        if (startDtoBuilder_ == null) {
          startDto_ = null;
        } else {
          startDto_ = null;
          startDtoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionStartRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartRequest build() {
        com.dxx.game.dto.MissionProto.MissionStartRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartRequest buildPartial() {
        com.dxx.game.dto.MissionProto.MissionStartRequest result = new com.dxx.game.dto.MissionProto.MissionStartRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        if (startDtoBuilder_ == null) {
          result.startDto_ = startDto_;
        } else {
          result.startDto_ = startDtoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionStartRequest) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionStartRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionStartRequest other) {
        if (other == com.dxx.game.dto.MissionProto.MissionStartRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.hasStartDto()) {
          mergeStartDto(other.getStartDto());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionStartRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionStartRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private com.dxx.game.dto.MissionProto.MissionStartDto startDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MissionStartDto, com.dxx.game.dto.MissionProto.MissionStartDto.Builder, com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder> startDtoBuilder_;
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       * @return Whether the startDto field is set.
       */
      public boolean hasStartDto() {
        return startDtoBuilder_ != null || startDto_ != null;
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       * @return The startDto.
       */
      public com.dxx.game.dto.MissionProto.MissionStartDto getStartDto() {
        if (startDtoBuilder_ == null) {
          return startDto_ == null ? com.dxx.game.dto.MissionProto.MissionStartDto.getDefaultInstance() : startDto_;
        } else {
          return startDtoBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      public Builder setStartDto(com.dxx.game.dto.MissionProto.MissionStartDto value) {
        if (startDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          startDto_ = value;
          onChanged();
        } else {
          startDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      public Builder setStartDto(
          com.dxx.game.dto.MissionProto.MissionStartDto.Builder builderForValue) {
        if (startDtoBuilder_ == null) {
          startDto_ = builderForValue.build();
          onChanged();
        } else {
          startDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      public Builder mergeStartDto(com.dxx.game.dto.MissionProto.MissionStartDto value) {
        if (startDtoBuilder_ == null) {
          if (startDto_ != null) {
            startDto_ =
              com.dxx.game.dto.MissionProto.MissionStartDto.newBuilder(startDto_).mergeFrom(value).buildPartial();
          } else {
            startDto_ = value;
          }
          onChanged();
        } else {
          startDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      public Builder clearStartDto() {
        if (startDtoBuilder_ == null) {
          startDto_ = null;
          onChanged();
        } else {
          startDto_ = null;
          startDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      public com.dxx.game.dto.MissionProto.MissionStartDto.Builder getStartDtoBuilder() {
        
        onChanged();
        return getStartDtoFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      public com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder getStartDtoOrBuilder() {
        if (startDtoBuilder_ != null) {
          return startDtoBuilder_.getMessageOrBuilder();
        } else {
          return startDto_ == null ?
              com.dxx.game.dto.MissionProto.MissionStartDto.getDefaultInstance() : startDto_;
        }
      }
      /**
       * <code>.Proto.Mission.MissionStartDto startDto = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MissionStartDto, com.dxx.game.dto.MissionProto.MissionStartDto.Builder, com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder> 
          getStartDtoFieldBuilder() {
        if (startDtoBuilder_ == null) {
          startDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.MissionProto.MissionStartDto, com.dxx.game.dto.MissionProto.MissionStartDto.Builder, com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder>(
                  getStartDto(),
                  getParentForChildren(),
                  isClean());
          startDto_ = null;
        }
        return startDtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionStartRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionStartRequest)
    private static final com.dxx.game.dto.MissionProto.MissionStartRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionStartRequest();
    }

    public static com.dxx.game.dto.MissionProto.MissionStartRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionStartRequest>
        PARSER = new com.google.protobuf.AbstractParser<MissionStartRequest>() {
      @java.lang.Override
      public MissionStartRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionStartRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionStartRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionStartRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionStartRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionStartResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionStartResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10304
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionStartResponse}
   */
  public static final class MissionStartResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionStartResponse)
      MissionStartResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionStartResponse.newBuilder() to construct.
    private MissionStartResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionStartResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionStartResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionStartResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionStartResponse.class, com.dxx.game.dto.MissionProto.MissionStartResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionStartResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionStartResponse other = (com.dxx.game.dto.MissionProto.MissionStartResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionStartResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10304
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionStartResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionStartResponse)
        com.dxx.game.dto.MissionProto.MissionStartResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionStartResponse.class, com.dxx.game.dto.MissionProto.MissionStartResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionStartResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionStartResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartResponse build() {
        com.dxx.game.dto.MissionProto.MissionStartResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartResponse buildPartial() {
        com.dxx.game.dto.MissionProto.MissionStartResponse result = new com.dxx.game.dto.MissionProto.MissionStartResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionStartResponse) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionStartResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionStartResponse other) {
        if (other == com.dxx.game.dto.MissionProto.MissionStartResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionStartResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionStartResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionStartResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionStartResponse)
    private static final com.dxx.game.dto.MissionProto.MissionStartResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionStartResponse();
    }

    public static com.dxx.game.dto.MissionProto.MissionStartResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionStartResponse>
        PARSER = new com.google.protobuf.AbstractParser<MissionStartResponse>() {
      @java.lang.Override
      public MissionStartResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionStartResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionStartResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionStartResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionStartResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionEndRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionEndRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
     * @return Whether the endDto field is set.
     */
    boolean hasEndDto();
    /**
     * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
     * @return The endDto.
     */
    com.dxx.game.dto.MissionProto.MissionEndDto getEndDto();
    /**
     * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
     */
    com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder getEndDtoOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10305 关卡-结算
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionEndRequest}
   */
  public static final class MissionEndRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionEndRequest)
      MissionEndRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionEndRequest.newBuilder() to construct.
    private MissionEndRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionEndRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionEndRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionEndRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              com.dxx.game.dto.MissionProto.MissionEndDto.Builder subBuilder = null;
              if (endDto_ != null) {
                subBuilder = endDto_.toBuilder();
              }
              endDto_ = input.readMessage(com.dxx.game.dto.MissionProto.MissionEndDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(endDto_);
                endDto_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionEndRequest.class, com.dxx.game.dto.MissionProto.MissionEndRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int ENDDTO_FIELD_NUMBER = 2;
    private com.dxx.game.dto.MissionProto.MissionEndDto endDto_;
    /**
     * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
     * @return Whether the endDto field is set.
     */
    @java.lang.Override
    public boolean hasEndDto() {
      return endDto_ != null;
    }
    /**
     * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
     * @return The endDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionEndDto getEndDto() {
      return endDto_ == null ? com.dxx.game.dto.MissionProto.MissionEndDto.getDefaultInstance() : endDto_;
    }
    /**
     * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder getEndDtoOrBuilder() {
      return getEndDto();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (endDto_ != null) {
        output.writeMessage(2, getEndDto());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (endDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getEndDto());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionEndRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionEndRequest other = (com.dxx.game.dto.MissionProto.MissionEndRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (hasEndDto() != other.hasEndDto()) return false;
      if (hasEndDto()) {
        if (!getEndDto()
            .equals(other.getEndDto())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      if (hasEndDto()) {
        hash = (37 * hash) + ENDDTO_FIELD_NUMBER;
        hash = (53 * hash) + getEndDto().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionEndRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10305 关卡-结算
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionEndRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionEndRequest)
        com.dxx.game.dto.MissionProto.MissionEndRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionEndRequest.class, com.dxx.game.dto.MissionProto.MissionEndRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionEndRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        if (endDtoBuilder_ == null) {
          endDto_ = null;
        } else {
          endDto_ = null;
          endDtoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionEndRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndRequest build() {
        com.dxx.game.dto.MissionProto.MissionEndRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndRequest buildPartial() {
        com.dxx.game.dto.MissionProto.MissionEndRequest result = new com.dxx.game.dto.MissionProto.MissionEndRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        if (endDtoBuilder_ == null) {
          result.endDto_ = endDto_;
        } else {
          result.endDto_ = endDtoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionEndRequest) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionEndRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionEndRequest other) {
        if (other == com.dxx.game.dto.MissionProto.MissionEndRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.hasEndDto()) {
          mergeEndDto(other.getEndDto());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionEndRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionEndRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private com.dxx.game.dto.MissionProto.MissionEndDto endDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MissionEndDto, com.dxx.game.dto.MissionProto.MissionEndDto.Builder, com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder> endDtoBuilder_;
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       * @return Whether the endDto field is set.
       */
      public boolean hasEndDto() {
        return endDtoBuilder_ != null || endDto_ != null;
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       * @return The endDto.
       */
      public com.dxx.game.dto.MissionProto.MissionEndDto getEndDto() {
        if (endDtoBuilder_ == null) {
          return endDto_ == null ? com.dxx.game.dto.MissionProto.MissionEndDto.getDefaultInstance() : endDto_;
        } else {
          return endDtoBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      public Builder setEndDto(com.dxx.game.dto.MissionProto.MissionEndDto value) {
        if (endDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          endDto_ = value;
          onChanged();
        } else {
          endDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      public Builder setEndDto(
          com.dxx.game.dto.MissionProto.MissionEndDto.Builder builderForValue) {
        if (endDtoBuilder_ == null) {
          endDto_ = builderForValue.build();
          onChanged();
        } else {
          endDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      public Builder mergeEndDto(com.dxx.game.dto.MissionProto.MissionEndDto value) {
        if (endDtoBuilder_ == null) {
          if (endDto_ != null) {
            endDto_ =
              com.dxx.game.dto.MissionProto.MissionEndDto.newBuilder(endDto_).mergeFrom(value).buildPartial();
          } else {
            endDto_ = value;
          }
          onChanged();
        } else {
          endDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      public Builder clearEndDto() {
        if (endDtoBuilder_ == null) {
          endDto_ = null;
          onChanged();
        } else {
          endDto_ = null;
          endDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      public com.dxx.game.dto.MissionProto.MissionEndDto.Builder getEndDtoBuilder() {
        
        onChanged();
        return getEndDtoFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      public com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder getEndDtoOrBuilder() {
        if (endDtoBuilder_ != null) {
          return endDtoBuilder_.getMessageOrBuilder();
        } else {
          return endDto_ == null ?
              com.dxx.game.dto.MissionProto.MissionEndDto.getDefaultInstance() : endDto_;
        }
      }
      /**
       * <code>.Proto.Mission.MissionEndDto endDto = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MissionEndDto, com.dxx.game.dto.MissionProto.MissionEndDto.Builder, com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder> 
          getEndDtoFieldBuilder() {
        if (endDtoBuilder_ == null) {
          endDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.MissionProto.MissionEndDto, com.dxx.game.dto.MissionProto.MissionEndDto.Builder, com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder>(
                  getEndDto(),
                  getParentForChildren(),
                  isClean());
          endDto_ = null;
        }
        return endDtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionEndRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionEndRequest)
    private static final com.dxx.game.dto.MissionProto.MissionEndRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionEndRequest();
    }

    public static com.dxx.game.dto.MissionProto.MissionEndRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionEndRequest>
        PARSER = new com.google.protobuf.AbstractParser<MissionEndRequest>() {
      @java.lang.Override
      public MissionEndRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionEndRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionEndRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionEndRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionEndRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionEndResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionEndResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>.Proto.Mission.MainMission mainMission = 3;</code>
     * @return Whether the mainMission field is set.
     */
    boolean hasMainMission();
    /**
     * <code>.Proto.Mission.MainMission mainMission = 3;</code>
     * @return The mainMission.
     */
    com.dxx.game.dto.MissionProto.MainMission getMainMission();
    /**
     * <code>.Proto.Mission.MainMission mainMission = 3;</code>
     */
    com.dxx.game.dto.MissionProto.MainMissionOrBuilder getMainMissionOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10306
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionEndResponse}
   */
  public static final class MissionEndResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionEndResponse)
      MissionEndResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionEndResponse.newBuilder() to construct.
    private MissionEndResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionEndResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionEndResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionEndResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.dxx.game.dto.MissionProto.MainMission.Builder subBuilder = null;
              if (mainMission_ != null) {
                subBuilder = mainMission_.toBuilder();
              }
              mainMission_ = input.readMessage(com.dxx.game.dto.MissionProto.MainMission.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(mainMission_);
                mainMission_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionEndResponse.class, com.dxx.game.dto.MissionProto.MissionEndResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int MAINMISSION_FIELD_NUMBER = 3;
    private com.dxx.game.dto.MissionProto.MainMission mainMission_;
    /**
     * <code>.Proto.Mission.MainMission mainMission = 3;</code>
     * @return Whether the mainMission field is set.
     */
    @java.lang.Override
    public boolean hasMainMission() {
      return mainMission_ != null;
    }
    /**
     * <code>.Proto.Mission.MainMission mainMission = 3;</code>
     * @return The mainMission.
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MainMission getMainMission() {
      return mainMission_ == null ? com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance() : mainMission_;
    }
    /**
     * <code>.Proto.Mission.MainMission mainMission = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MainMissionOrBuilder getMainMissionOrBuilder() {
      return getMainMission();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (mainMission_ != null) {
        output.writeMessage(3, getMainMission());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (mainMission_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMainMission());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionEndResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionEndResponse other = (com.dxx.game.dto.MissionProto.MissionEndResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasMainMission() != other.hasMainMission()) return false;
      if (hasMainMission()) {
        if (!getMainMission()
            .equals(other.getMainMission())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasMainMission()) {
        hash = (37 * hash) + MAINMISSION_FIELD_NUMBER;
        hash = (53 * hash) + getMainMission().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionEndResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10306
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionEndResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionEndResponse)
        com.dxx.game.dto.MissionProto.MissionEndResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionEndResponse.class, com.dxx.game.dto.MissionProto.MissionEndResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionEndResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (mainMissionBuilder_ == null) {
          mainMission_ = null;
        } else {
          mainMission_ = null;
          mainMissionBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionEndResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndResponse build() {
        com.dxx.game.dto.MissionProto.MissionEndResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndResponse buildPartial() {
        com.dxx.game.dto.MissionProto.MissionEndResponse result = new com.dxx.game.dto.MissionProto.MissionEndResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (mainMissionBuilder_ == null) {
          result.mainMission_ = mainMission_;
        } else {
          result.mainMission_ = mainMissionBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionEndResponse) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionEndResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionEndResponse other) {
        if (other == com.dxx.game.dto.MissionProto.MissionEndResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasMainMission()) {
          mergeMainMission(other.getMainMission());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionEndResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionEndResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.MissionProto.MainMission mainMission_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MainMission, com.dxx.game.dto.MissionProto.MainMission.Builder, com.dxx.game.dto.MissionProto.MainMissionOrBuilder> mainMissionBuilder_;
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       * @return Whether the mainMission field is set.
       */
      public boolean hasMainMission() {
        return mainMissionBuilder_ != null || mainMission_ != null;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       * @return The mainMission.
       */
      public com.dxx.game.dto.MissionProto.MainMission getMainMission() {
        if (mainMissionBuilder_ == null) {
          return mainMission_ == null ? com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance() : mainMission_;
        } else {
          return mainMissionBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      public Builder setMainMission(com.dxx.game.dto.MissionProto.MainMission value) {
        if (mainMissionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mainMission_ = value;
          onChanged();
        } else {
          mainMissionBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      public Builder setMainMission(
          com.dxx.game.dto.MissionProto.MainMission.Builder builderForValue) {
        if (mainMissionBuilder_ == null) {
          mainMission_ = builderForValue.build();
          onChanged();
        } else {
          mainMissionBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      public Builder mergeMainMission(com.dxx.game.dto.MissionProto.MainMission value) {
        if (mainMissionBuilder_ == null) {
          if (mainMission_ != null) {
            mainMission_ =
              com.dxx.game.dto.MissionProto.MainMission.newBuilder(mainMission_).mergeFrom(value).buildPartial();
          } else {
            mainMission_ = value;
          }
          onChanged();
        } else {
          mainMissionBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      public Builder clearMainMission() {
        if (mainMissionBuilder_ == null) {
          mainMission_ = null;
          onChanged();
        } else {
          mainMission_ = null;
          mainMissionBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      public com.dxx.game.dto.MissionProto.MainMission.Builder getMainMissionBuilder() {
        
        onChanged();
        return getMainMissionFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      public com.dxx.game.dto.MissionProto.MainMissionOrBuilder getMainMissionOrBuilder() {
        if (mainMissionBuilder_ != null) {
          return mainMissionBuilder_.getMessageOrBuilder();
        } else {
          return mainMission_ == null ?
              com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance() : mainMission_;
        }
      }
      /**
       * <code>.Proto.Mission.MainMission mainMission = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.MissionProto.MainMission, com.dxx.game.dto.MissionProto.MainMission.Builder, com.dxx.game.dto.MissionProto.MainMissionOrBuilder> 
          getMainMissionFieldBuilder() {
        if (mainMissionBuilder_ == null) {
          mainMissionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.MissionProto.MainMission, com.dxx.game.dto.MissionProto.MainMission.Builder, com.dxx.game.dto.MissionProto.MainMissionOrBuilder>(
                  getMainMission(),
                  getParentForChildren(),
                  isClean());
          mainMission_ = null;
        }
        return mainMissionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionEndResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionEndResponse)
    private static final com.dxx.game.dto.MissionProto.MissionEndResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionEndResponse();
    }

    public static com.dxx.game.dto.MissionProto.MissionEndResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionEndResponse>
        PARSER = new com.google.protobuf.AbstractParser<MissionEndResponse>() {
      @java.lang.Override
      public MissionEndResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionEndResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionEndResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionEndResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionEndResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionGetHangUpItemsRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionGetHangUpItemsRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10307 关卡-获取挂机奖励道具
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionGetHangUpItemsRequest}
   */
  public static final class MissionGetHangUpItemsRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionGetHangUpItemsRequest)
      MissionGetHangUpItemsRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionGetHangUpItemsRequest.newBuilder() to construct.
    private MissionGetHangUpItemsRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionGetHangUpItemsRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionGetHangUpItemsRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionGetHangUpItemsRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.class, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest other = (com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10307 关卡-获取挂机奖励道具
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionGetHangUpItemsRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionGetHangUpItemsRequest)
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.class, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest build() {
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest buildPartial() {
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest result = new com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest other) {
        if (other == com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionGetHangUpItemsRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionGetHangUpItemsRequest)
    private static final com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest();
    }

    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionGetHangUpItemsRequest>
        PARSER = new com.google.protobuf.AbstractParser<MissionGetHangUpItemsRequest>() {
      @java.lang.Override
      public MissionGetHangUpItemsRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionGetHangUpItemsRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionGetHangUpItemsRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionGetHangUpItemsRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionGetHangUpItemsResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionGetHangUpItemsResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.RewardDto> 
        getRewardList();
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDto getReward(int index);
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    int getRewardCount();
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
        getRewardOrBuilderList();
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardOrBuilder(
        int index);
  }
  /**
   * <pre>
   *CMD PackageId=10308
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionGetHangUpItemsResponse}
   */
  public static final class MissionGetHangUpItemsResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionGetHangUpItemsResponse)
      MissionGetHangUpItemsResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionGetHangUpItemsResponse.newBuilder() to construct.
    private MissionGetHangUpItemsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionGetHangUpItemsResponse() {
      reward_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionGetHangUpItemsResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionGetHangUpItemsResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                reward_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RewardDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              reward_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.RewardDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          reward_ = java.util.Collections.unmodifiableList(reward_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.class, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int REWARD_FIELD_NUMBER = 2;
    private java.util.List<com.dxx.game.dto.CommonProto.RewardDto> reward_;
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.RewardDto> getRewardList() {
      return reward_;
    }
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
        getRewardOrBuilderList() {
      return reward_;
    }
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    @java.lang.Override
    public int getRewardCount() {
      return reward_.size();
    }
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDto getReward(int index) {
      return reward_.get(index);
    }
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardOrBuilder(
        int index) {
      return reward_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      for (int i = 0; i < reward_.size(); i++) {
        output.writeMessage(2, reward_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      for (int i = 0; i < reward_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, reward_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse other = (com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getRewardList()
          .equals(other.getRewardList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getRewardCount() > 0) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getRewardList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10308
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionGetHangUpItemsResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionGetHangUpItemsResponse)
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.class, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (rewardBuilder_ == null) {
          reward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rewardBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionGetHangUpItemsResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse build() {
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse buildPartial() {
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse result = new com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            reward_ = java.util.Collections.unmodifiableList(reward_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.reward_ = reward_;
        } else {
          result.reward_ = rewardBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse other) {
        if (other == com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (rewardBuilder_ == null) {
          if (!other.reward_.isEmpty()) {
            if (reward_.isEmpty()) {
              reward_ = other.reward_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardIsMutable();
              reward_.addAll(other.reward_);
            }
            onChanged();
          }
        } else {
          if (!other.reward_.isEmpty()) {
            if (rewardBuilder_.isEmpty()) {
              rewardBuilder_.dispose();
              rewardBuilder_ = null;
              reward_ = other.reward_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardFieldBuilder() : null;
            } else {
              rewardBuilder_.addAllMessages(other.reward_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.RewardDto> reward_ =
        java.util.Collections.emptyList();
      private void ensureRewardIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          reward_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RewardDto>(reward_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> rewardBuilder_;

      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDto> getRewardList() {
        if (rewardBuilder_ == null) {
          return java.util.Collections.unmodifiableList(reward_);
        } else {
          return rewardBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public int getRewardCount() {
        if (rewardBuilder_ == null) {
          return reward_.size();
        } else {
          return rewardBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto getReward(int index) {
        if (rewardBuilder_ == null) {
          return reward_.get(index);
        } else {
          return rewardBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder setReward(
          int index, com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardIsMutable();
          reward_.set(index, value);
          onChanged();
        } else {
          rewardBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder setReward(
          int index, com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder addReward(com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardIsMutable();
          reward_.add(value);
          onChanged();
        } else {
          rewardBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder addReward(
          int index, com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardIsMutable();
          reward_.add(index, value);
          onChanged();
        } else {
          rewardBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder addReward(
          com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.add(builderForValue.build());
          onChanged();
        } else {
          rewardBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder addReward(
          int index, com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder addAllReward(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.RewardDto> values) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, reward_);
          onChanged();
        } else {
          rewardBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public Builder removeReward(int index) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.remove(index);
          onChanged();
        } else {
          rewardBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder getRewardBuilder(
          int index) {
        return getRewardFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardOrBuilder(
          int index) {
        if (rewardBuilder_ == null) {
          return reward_.get(index);  } else {
          return rewardBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
           getRewardOrBuilderList() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(reward_);
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder addRewardBuilder() {
        return getRewardFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.RewardDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder addRewardBuilder(
          int index) {
        return getRewardFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.RewardDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto reward = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDto.Builder> 
           getRewardBuilderList() {
        return getRewardFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder>(
                  reward_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionGetHangUpItemsResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionGetHangUpItemsResponse)
    private static final com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse();
    }

    public static com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionGetHangUpItemsResponse>
        PARSER = new com.google.protobuf.AbstractParser<MissionGetHangUpItemsResponse>() {
      @java.lang.Override
      public MissionGetHangUpItemsResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionGetHangUpItemsResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionGetHangUpItemsResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionGetHangUpItemsResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionReceiveHangUpItemsRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionReceiveHangUpItemsRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10309 关卡-领取挂机奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionReceiveHangUpItemsRequest}
   */
  public static final class MissionReceiveHangUpItemsRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionReceiveHangUpItemsRequest)
      MissionReceiveHangUpItemsRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionReceiveHangUpItemsRequest.newBuilder() to construct.
    private MissionReceiveHangUpItemsRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionReceiveHangUpItemsRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionReceiveHangUpItemsRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionReceiveHangUpItemsRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.class, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest other = (com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10309 关卡-领取挂机奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionReceiveHangUpItemsRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionReceiveHangUpItemsRequest)
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.class, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest build() {
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest buildPartial() {
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest result = new com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest other) {
        if (other == com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionReceiveHangUpItemsRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionReceiveHangUpItemsRequest)
    private static final com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest();
    }

    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionReceiveHangUpItemsRequest>
        PARSER = new com.google.protobuf.AbstractParser<MissionReceiveHangUpItemsRequest>() {
      @java.lang.Override
      public MissionReceiveHangUpItemsRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionReceiveHangUpItemsRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionReceiveHangUpItemsRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionReceiveHangUpItemsRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionReceiveHangUpItemsResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionReceiveHangUpItemsResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10310
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionReceiveHangUpItemsResponse}
   */
  public static final class MissionReceiveHangUpItemsResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionReceiveHangUpItemsResponse)
      MissionReceiveHangUpItemsResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionReceiveHangUpItemsResponse.newBuilder() to construct.
    private MissionReceiveHangUpItemsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionReceiveHangUpItemsResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionReceiveHangUpItemsResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionReceiveHangUpItemsResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.class, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse other = (com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10310
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionReceiveHangUpItemsResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionReceiveHangUpItemsResponse)
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.class, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse build() {
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse buildPartial() {
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse result = new com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse other) {
        if (other == com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionReceiveHangUpItemsResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionReceiveHangUpItemsResponse)
    private static final com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse();
    }

    public static com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionReceiveHangUpItemsResponse>
        PARSER = new com.google.protobuf.AbstractParser<MissionReceiveHangUpItemsResponse>() {
      @java.lang.Override
      public MissionReceiveHangUpItemsResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionReceiveHangUpItemsResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionReceiveHangUpItemsResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionReceiveHangUpItemsResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionQuickHangUpRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionQuickHangUpRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10311 关卡-快速挂机请求
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionQuickHangUpRequest}
   */
  public static final class MissionQuickHangUpRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionQuickHangUpRequest)
      MissionQuickHangUpRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionQuickHangUpRequest.newBuilder() to construct.
    private MissionQuickHangUpRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionQuickHangUpRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionQuickHangUpRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionQuickHangUpRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.class, com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest other = (com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10311 关卡-快速挂机请求
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionQuickHangUpRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionQuickHangUpRequest)
        com.dxx.game.dto.MissionProto.MissionQuickHangUpRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.class, com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest build() {
        com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest buildPartial() {
        com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest result = new com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest other) {
        if (other == com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionQuickHangUpRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionQuickHangUpRequest)
    private static final com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest();
    }

    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionQuickHangUpRequest>
        PARSER = new com.google.protobuf.AbstractParser<MissionQuickHangUpRequest>() {
      @java.lang.Override
      public MissionQuickHangUpRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionQuickHangUpRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionQuickHangUpRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionQuickHangUpRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionQuickHangUpResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionQuickHangUpResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10312
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionQuickHangUpResponse}
   */
  public static final class MissionQuickHangUpResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionQuickHangUpResponse)
      MissionQuickHangUpResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionQuickHangUpResponse.newBuilder() to construct.
    private MissionQuickHangUpResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionQuickHangUpResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionQuickHangUpResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionQuickHangUpResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.class, com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse other = (com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10312
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionQuickHangUpResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionQuickHangUpResponse)
        com.dxx.game.dto.MissionProto.MissionQuickHangUpResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.class, com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionQuickHangUpResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse build() {
        com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse buildPartial() {
        com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse result = new com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse other) {
        if (other == com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionQuickHangUpResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionQuickHangUpResponse)
    private static final com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse();
    }

    public static com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionQuickHangUpResponse>
        PARSER = new com.google.protobuf.AbstractParser<MissionQuickHangUpResponse>() {
      @java.lang.Override
      public MissionQuickHangUpResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionQuickHangUpResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionQuickHangUpResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionQuickHangUpResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionStartDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionStartDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The enum numeric value on the wire for missionType.
     */
    int getMissionTypeValue();
    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The missionType.
     */
    com.dxx.game.dto.MissionProto.MissionType getMissionType();

    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>uint32 missionId = 2;</code>
     * @return The missionId.
     */
    int getMissionId();
  }
  /**
   * <pre>
   * 开始战斗数据
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionStartDto}
   */
  public static final class MissionStartDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionStartDto)
      MissionStartDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionStartDto.newBuilder() to construct.
    private MissionStartDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionStartDto() {
      missionType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionStartDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionStartDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();

              missionType_ = rawValue;
              break;
            }
            case 16: {

              missionId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionStartDto.class, com.dxx.game.dto.MissionProto.MissionStartDto.Builder.class);
    }

    public static final int MISSIONTYPE_FIELD_NUMBER = 1;
    private int missionType_;
    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The enum numeric value on the wire for missionType.
     */
    @java.lang.Override public int getMissionTypeValue() {
      return missionType_;
    }
    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The missionType.
     */
    @java.lang.Override public com.dxx.game.dto.MissionProto.MissionType getMissionType() {
      @SuppressWarnings("deprecation")
      com.dxx.game.dto.MissionProto.MissionType result = com.dxx.game.dto.MissionProto.MissionType.valueOf(missionType_);
      return result == null ? com.dxx.game.dto.MissionProto.MissionType.UNRECOGNIZED : result;
    }

    public static final int MISSIONID_FIELD_NUMBER = 2;
    private int missionId_;
    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>uint32 missionId = 2;</code>
     * @return The missionId.
     */
    @java.lang.Override
    public int getMissionId() {
      return missionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (missionType_ != com.dxx.game.dto.MissionProto.MissionType.MAIN.getNumber()) {
        output.writeEnum(1, missionType_);
      }
      if (missionId_ != 0) {
        output.writeUInt32(2, missionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (missionType_ != com.dxx.game.dto.MissionProto.MissionType.MAIN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, missionType_);
      }
      if (missionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, missionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionStartDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionStartDto other = (com.dxx.game.dto.MissionProto.MissionStartDto) obj;

      if (missionType_ != other.missionType_) return false;
      if (getMissionId()
          != other.getMissionId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MISSIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + missionType_;
      hash = (37 * hash) + MISSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getMissionId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionStartDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionStartDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 开始战斗数据
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionStartDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionStartDto)
        com.dxx.game.dto.MissionProto.MissionStartDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionStartDto.class, com.dxx.game.dto.MissionProto.MissionStartDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionStartDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        missionType_ = 0;

        missionId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionStartDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartDto getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionStartDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartDto build() {
        com.dxx.game.dto.MissionProto.MissionStartDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionStartDto buildPartial() {
        com.dxx.game.dto.MissionProto.MissionStartDto result = new com.dxx.game.dto.MissionProto.MissionStartDto(this);
        result.missionType_ = missionType_;
        result.missionId_ = missionId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionStartDto) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionStartDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionStartDto other) {
        if (other == com.dxx.game.dto.MissionProto.MissionStartDto.getDefaultInstance()) return this;
        if (other.missionType_ != 0) {
          setMissionTypeValue(other.getMissionTypeValue());
        }
        if (other.getMissionId() != 0) {
          setMissionId(other.getMissionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionStartDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionStartDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int missionType_ = 0;
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @return The enum numeric value on the wire for missionType.
       */
      @java.lang.Override public int getMissionTypeValue() {
        return missionType_;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @param value The enum numeric value on the wire for missionType to set.
       * @return This builder for chaining.
       */
      public Builder setMissionTypeValue(int value) {
        
        missionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @return The missionType.
       */
      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionType getMissionType() {
        @SuppressWarnings("deprecation")
        com.dxx.game.dto.MissionProto.MissionType result = com.dxx.game.dto.MissionProto.MissionType.valueOf(missionType_);
        return result == null ? com.dxx.game.dto.MissionProto.MissionType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @param value The missionType to set.
       * @return This builder for chaining.
       */
      public Builder setMissionType(com.dxx.game.dto.MissionProto.MissionType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        missionType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMissionType() {
        
        missionType_ = 0;
        onChanged();
        return this;
      }

      private int missionId_ ;
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>uint32 missionId = 2;</code>
       * @return The missionId.
       */
      @java.lang.Override
      public int getMissionId() {
        return missionId_;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>uint32 missionId = 2;</code>
       * @param value The missionId to set.
       * @return This builder for chaining.
       */
      public Builder setMissionId(int value) {
        
        missionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>uint32 missionId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMissionId() {
        
        missionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionStartDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionStartDto)
    private static final com.dxx.game.dto.MissionProto.MissionStartDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionStartDto();
    }

    public static com.dxx.game.dto.MissionProto.MissionStartDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionStartDto>
        PARSER = new com.google.protobuf.AbstractParser<MissionStartDto>() {
      @java.lang.Override
      public MissionStartDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionStartDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionStartDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionStartDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionStartDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MissionEndDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MissionEndDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The enum numeric value on the wire for missionType.
     */
    int getMissionTypeValue();
    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The missionType.
     */
    com.dxx.game.dto.MissionProto.MissionType getMissionType();

    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>uint32 missionId = 2;</code>
     * @return The missionId.
     */
    int getMissionId();

    /**
     * <pre>
     * 开始战斗时传的transId
     * </pre>
     *
     * <code>uint64 startTransId = 3;</code>
     * @return The startTransId.
     */
    long getStartTransId();

    /**
     * <pre>
     * 是否胜利
     * </pre>
     *
     * <code>bool isWin = 4;</code>
     * @return The isWin.
     */
    boolean getIsWin();

    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.RewardDto> 
        getRewardsList();
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDto getRewards(int index);
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    int getRewardsCount();
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
        getRewardsOrBuilderList();
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardsOrBuilder(
        int index);

    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>string extar = 6;</code>
     * @return The extar.
     */
    java.lang.String getExtar();
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>string extar = 6;</code>
     * @return The bytes for extar.
     */
    com.google.protobuf.ByteString
        getExtarBytes();

    /**
     * <pre>
     * 伤害值
     * </pre>
     *
     * <code>uint64 damage = 7;</code>
     * @return The damage.
     */
    long getDamage();
  }
  /**
   * <pre>
   * 战斗结束数据
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MissionEndDto}
   */
  public static final class MissionEndDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MissionEndDto)
      MissionEndDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MissionEndDto.newBuilder() to construct.
    private MissionEndDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MissionEndDto() {
      missionType_ = 0;
      rewards_ = java.util.Collections.emptyList();
      extar_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MissionEndDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MissionEndDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();

              missionType_ = rawValue;
              break;
            }
            case 16: {

              missionId_ = input.readUInt32();
              break;
            }
            case 24: {

              startTransId_ = input.readUInt64();
              break;
            }
            case 32: {

              isWin_ = input.readBool();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rewards_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RewardDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              rewards_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.RewardDto.parser(), extensionRegistry));
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              extar_ = s;
              break;
            }
            case 56: {

              damage_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rewards_ = java.util.Collections.unmodifiableList(rewards_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MissionEndDto.class, com.dxx.game.dto.MissionProto.MissionEndDto.Builder.class);
    }

    public static final int MISSIONTYPE_FIELD_NUMBER = 1;
    private int missionType_;
    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The enum numeric value on the wire for missionType.
     */
    @java.lang.Override public int getMissionTypeValue() {
      return missionType_;
    }
    /**
     * <pre>
     * 关卡类型
     * </pre>
     *
     * <code>.Proto.Mission.MissionType missionType = 1;</code>
     * @return The missionType.
     */
    @java.lang.Override public com.dxx.game.dto.MissionProto.MissionType getMissionType() {
      @SuppressWarnings("deprecation")
      com.dxx.game.dto.MissionProto.MissionType result = com.dxx.game.dto.MissionProto.MissionType.valueOf(missionType_);
      return result == null ? com.dxx.game.dto.MissionProto.MissionType.UNRECOGNIZED : result;
    }

    public static final int MISSIONID_FIELD_NUMBER = 2;
    private int missionId_;
    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>uint32 missionId = 2;</code>
     * @return The missionId.
     */
    @java.lang.Override
    public int getMissionId() {
      return missionId_;
    }

    public static final int STARTTRANSID_FIELD_NUMBER = 3;
    private long startTransId_;
    /**
     * <pre>
     * 开始战斗时传的transId
     * </pre>
     *
     * <code>uint64 startTransId = 3;</code>
     * @return The startTransId.
     */
    @java.lang.Override
    public long getStartTransId() {
      return startTransId_;
    }

    public static final int ISWIN_FIELD_NUMBER = 4;
    private boolean isWin_;
    /**
     * <pre>
     * 是否胜利
     * </pre>
     *
     * <code>bool isWin = 4;</code>
     * @return The isWin.
     */
    @java.lang.Override
    public boolean getIsWin() {
      return isWin_;
    }

    public static final int REWARDS_FIELD_NUMBER = 5;
    private java.util.List<com.dxx.game.dto.CommonProto.RewardDto> rewards_;
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.RewardDto> getRewardsList() {
      return rewards_;
    }
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
        getRewardsOrBuilderList() {
      return rewards_;
    }
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    @java.lang.Override
    public int getRewardsCount() {
      return rewards_.size();
    }
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDto getRewards(int index) {
      return rewards_.get(index);
    }
    /**
     * <pre>
     * 战斗结算奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardsOrBuilder(
        int index) {
      return rewards_.get(index);
    }

    public static final int EXTAR_FIELD_NUMBER = 6;
    private volatile java.lang.Object extar_;
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>string extar = 6;</code>
     * @return The extar.
     */
    @java.lang.Override
    public java.lang.String getExtar() {
      java.lang.Object ref = extar_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extar_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>string extar = 6;</code>
     * @return The bytes for extar.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getExtarBytes() {
      java.lang.Object ref = extar_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extar_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DAMAGE_FIELD_NUMBER = 7;
    private long damage_;
    /**
     * <pre>
     * 伤害值
     * </pre>
     *
     * <code>uint64 damage = 7;</code>
     * @return The damage.
     */
    @java.lang.Override
    public long getDamage() {
      return damage_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (missionType_ != com.dxx.game.dto.MissionProto.MissionType.MAIN.getNumber()) {
        output.writeEnum(1, missionType_);
      }
      if (missionId_ != 0) {
        output.writeUInt32(2, missionId_);
      }
      if (startTransId_ != 0L) {
        output.writeUInt64(3, startTransId_);
      }
      if (isWin_ != false) {
        output.writeBool(4, isWin_);
      }
      for (int i = 0; i < rewards_.size(); i++) {
        output.writeMessage(5, rewards_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extar_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, extar_);
      }
      if (damage_ != 0L) {
        output.writeUInt64(7, damage_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (missionType_ != com.dxx.game.dto.MissionProto.MissionType.MAIN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, missionType_);
      }
      if (missionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, missionId_);
      }
      if (startTransId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, startTransId_);
      }
      if (isWin_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, isWin_);
      }
      for (int i = 0; i < rewards_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, rewards_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extar_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, extar_);
      }
      if (damage_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, damage_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MissionEndDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MissionEndDto other = (com.dxx.game.dto.MissionProto.MissionEndDto) obj;

      if (missionType_ != other.missionType_) return false;
      if (getMissionId()
          != other.getMissionId()) return false;
      if (getStartTransId()
          != other.getStartTransId()) return false;
      if (getIsWin()
          != other.getIsWin()) return false;
      if (!getRewardsList()
          .equals(other.getRewardsList())) return false;
      if (!getExtar()
          .equals(other.getExtar())) return false;
      if (getDamage()
          != other.getDamage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MISSIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + missionType_;
      hash = (37 * hash) + MISSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getMissionId();
      hash = (37 * hash) + STARTTRANSID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getStartTransId());
      hash = (37 * hash) + ISWIN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsWin());
      if (getRewardsCount() > 0) {
        hash = (37 * hash) + REWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getRewardsList().hashCode();
      }
      hash = (37 * hash) + EXTAR_FIELD_NUMBER;
      hash = (53 * hash) + getExtar().hashCode();
      hash = (37 * hash) + DAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDamage());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MissionEndDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MissionEndDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 战斗结束数据
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MissionEndDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MissionEndDto)
        com.dxx.game.dto.MissionProto.MissionEndDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MissionEndDto.class, com.dxx.game.dto.MissionProto.MissionEndDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MissionEndDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        missionType_ = 0;

        missionId_ = 0;

        startTransId_ = 0L;

        isWin_ = false;

        if (rewardsBuilder_ == null) {
          rewards_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rewardsBuilder_.clear();
        }
        extar_ = "";

        damage_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MissionEndDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndDto getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MissionEndDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndDto build() {
        com.dxx.game.dto.MissionProto.MissionEndDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionEndDto buildPartial() {
        com.dxx.game.dto.MissionProto.MissionEndDto result = new com.dxx.game.dto.MissionProto.MissionEndDto(this);
        int from_bitField0_ = bitField0_;
        result.missionType_ = missionType_;
        result.missionId_ = missionId_;
        result.startTransId_ = startTransId_;
        result.isWin_ = isWin_;
        if (rewardsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewards_ = java.util.Collections.unmodifiableList(rewards_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewards_ = rewards_;
        } else {
          result.rewards_ = rewardsBuilder_.build();
        }
        result.extar_ = extar_;
        result.damage_ = damage_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MissionEndDto) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MissionEndDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MissionEndDto other) {
        if (other == com.dxx.game.dto.MissionProto.MissionEndDto.getDefaultInstance()) return this;
        if (other.missionType_ != 0) {
          setMissionTypeValue(other.getMissionTypeValue());
        }
        if (other.getMissionId() != 0) {
          setMissionId(other.getMissionId());
        }
        if (other.getStartTransId() != 0L) {
          setStartTransId(other.getStartTransId());
        }
        if (other.getIsWin() != false) {
          setIsWin(other.getIsWin());
        }
        if (rewardsBuilder_ == null) {
          if (!other.rewards_.isEmpty()) {
            if (rewards_.isEmpty()) {
              rewards_ = other.rewards_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardsIsMutable();
              rewards_.addAll(other.rewards_);
            }
            onChanged();
          }
        } else {
          if (!other.rewards_.isEmpty()) {
            if (rewardsBuilder_.isEmpty()) {
              rewardsBuilder_.dispose();
              rewardsBuilder_ = null;
              rewards_ = other.rewards_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardsFieldBuilder() : null;
            } else {
              rewardsBuilder_.addAllMessages(other.rewards_);
            }
          }
        }
        if (!other.getExtar().isEmpty()) {
          extar_ = other.extar_;
          onChanged();
        }
        if (other.getDamage() != 0L) {
          setDamage(other.getDamage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MissionEndDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MissionEndDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int missionType_ = 0;
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @return The enum numeric value on the wire for missionType.
       */
      @java.lang.Override public int getMissionTypeValue() {
        return missionType_;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @param value The enum numeric value on the wire for missionType to set.
       * @return This builder for chaining.
       */
      public Builder setMissionTypeValue(int value) {
        
        missionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @return The missionType.
       */
      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MissionType getMissionType() {
        @SuppressWarnings("deprecation")
        com.dxx.game.dto.MissionProto.MissionType result = com.dxx.game.dto.MissionProto.MissionType.valueOf(missionType_);
        return result == null ? com.dxx.game.dto.MissionProto.MissionType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @param value The missionType to set.
       * @return This builder for chaining.
       */
      public Builder setMissionType(com.dxx.game.dto.MissionProto.MissionType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        missionType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡类型
       * </pre>
       *
       * <code>.Proto.Mission.MissionType missionType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMissionType() {
        
        missionType_ = 0;
        onChanged();
        return this;
      }

      private int missionId_ ;
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>uint32 missionId = 2;</code>
       * @return The missionId.
       */
      @java.lang.Override
      public int getMissionId() {
        return missionId_;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>uint32 missionId = 2;</code>
       * @param value The missionId to set.
       * @return This builder for chaining.
       */
      public Builder setMissionId(int value) {
        
        missionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>uint32 missionId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMissionId() {
        
        missionId_ = 0;
        onChanged();
        return this;
      }

      private long startTransId_ ;
      /**
       * <pre>
       * 开始战斗时传的transId
       * </pre>
       *
       * <code>uint64 startTransId = 3;</code>
       * @return The startTransId.
       */
      @java.lang.Override
      public long getStartTransId() {
        return startTransId_;
      }
      /**
       * <pre>
       * 开始战斗时传的transId
       * </pre>
       *
       * <code>uint64 startTransId = 3;</code>
       * @param value The startTransId to set.
       * @return This builder for chaining.
       */
      public Builder setStartTransId(long value) {
        
        startTransId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 开始战斗时传的transId
       * </pre>
       *
       * <code>uint64 startTransId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTransId() {
        
        startTransId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isWin_ ;
      /**
       * <pre>
       * 是否胜利
       * </pre>
       *
       * <code>bool isWin = 4;</code>
       * @return The isWin.
       */
      @java.lang.Override
      public boolean getIsWin() {
        return isWin_;
      }
      /**
       * <pre>
       * 是否胜利
       * </pre>
       *
       * <code>bool isWin = 4;</code>
       * @param value The isWin to set.
       * @return This builder for chaining.
       */
      public Builder setIsWin(boolean value) {
        
        isWin_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否胜利
       * </pre>
       *
       * <code>bool isWin = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsWin() {
        
        isWin_ = false;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.RewardDto> rewards_ =
        java.util.Collections.emptyList();
      private void ensureRewardsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewards_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RewardDto>(rewards_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> rewardsBuilder_;

      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDto> getRewardsList() {
        if (rewardsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewards_);
        } else {
          return rewardsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public int getRewardsCount() {
        if (rewardsBuilder_ == null) {
          return rewards_.size();
        } else {
          return rewardsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto getRewards(int index) {
        if (rewardsBuilder_ == null) {
          return rewards_.get(index);
        } else {
          return rewardsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder setRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.set(index, value);
          onChanged();
        } else {
          rewardsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder setRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder addRewards(com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.add(value);
          onChanged();
        } else {
          rewardsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder addRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.add(index, value);
          onChanged();
        } else {
          rewardsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder addRewards(
          com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.add(builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder addRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder addAllRewards(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.RewardDto> values) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewards_);
          onChanged();
        } else {
          rewardsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder clearRewards() {
        if (rewardsBuilder_ == null) {
          rewards_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public Builder removeRewards(int index) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.remove(index);
          onChanged();
        } else {
          rewardsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder getRewardsBuilder(
          int index) {
        return getRewardsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardsOrBuilder(
          int index) {
        if (rewardsBuilder_ == null) {
          return rewards_.get(index);  } else {
          return rewardsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
           getRewardsOrBuilderList() {
        if (rewardsBuilder_ != null) {
          return rewardsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewards_);
        }
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder addRewardsBuilder() {
        return getRewardsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.RewardDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder addRewardsBuilder(
          int index) {
        return getRewardsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.RewardDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结算奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 5;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDto.Builder> 
           getRewardsBuilderList() {
        return getRewardsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
          getRewardsFieldBuilder() {
        if (rewardsBuilder_ == null) {
          rewardsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder>(
                  rewards_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewards_ = null;
        }
        return rewardsBuilder_;
      }

      private java.lang.Object extar_ = "";
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>string extar = 6;</code>
       * @return The extar.
       */
      public java.lang.String getExtar() {
        java.lang.Object ref = extar_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          extar_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>string extar = 6;</code>
       * @return The bytes for extar.
       */
      public com.google.protobuf.ByteString
          getExtarBytes() {
        java.lang.Object ref = extar_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extar_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>string extar = 6;</code>
       * @param value The extar to set.
       * @return This builder for chaining.
       */
      public Builder setExtar(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        extar_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>string extar = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtar() {
        
        extar_ = getDefaultInstance().getExtar();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>string extar = 6;</code>
       * @param value The bytes for extar to set.
       * @return This builder for chaining.
       */
      public Builder setExtarBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        extar_ = value;
        onChanged();
        return this;
      }

      private long damage_ ;
      /**
       * <pre>
       * 伤害值
       * </pre>
       *
       * <code>uint64 damage = 7;</code>
       * @return The damage.
       */
      @java.lang.Override
      public long getDamage() {
        return damage_;
      }
      /**
       * <pre>
       * 伤害值
       * </pre>
       *
       * <code>uint64 damage = 7;</code>
       * @param value The damage to set.
       * @return This builder for chaining.
       */
      public Builder setDamage(long value) {
        
        damage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 伤害值
       * </pre>
       *
       * <code>uint64 damage = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearDamage() {
        
        damage_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MissionEndDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MissionEndDto)
    private static final com.dxx.game.dto.MissionProto.MissionEndDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MissionEndDto();
    }

    public static com.dxx.game.dto.MissionProto.MissionEndDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MissionEndDto>
        PARSER = new com.google.protobuf.AbstractParser<MissionEndDto>() {
      @java.lang.Override
      public MissionEndDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MissionEndDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MissionEndDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MissionEndDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MissionEndDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MainMissionOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mission.MainMission)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 最远未完成的关卡ID，会超出上限
     * </pre>
     *
     * <code>uint32 missionId = 1;</code>
     * @return The missionId.
     */
    int getMissionId();

    /**
     * <pre>
     * 波次
     * </pre>
     *
     * <code>int32 wave = 2;</code>
     * @return The wave.
     */
    int getWave();

    /**
     * <pre>
     * 已领取的章节奖励ID，不超上限
     * </pre>
     *
     * <code>int32 claimedRewardId = 3;</code>
     * @return The claimedRewardId.
     */
    int getClaimedRewardId();

    /**
     * <pre>
     * 进入章节次数
     * </pre>
     *
     * <code>int32 enterCount = 4;</code>
     * @return The enterCount.
     */
    int getEnterCount();
  }
  /**
   * <pre>
   * 主线关卡
   * </pre>
   *
   * Protobuf type {@code Proto.Mission.MainMission}
   */
  public static final class MainMission extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Mission.MainMission)
      MainMissionOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MainMission.newBuilder() to construct.
    private MainMission(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MainMission() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MainMission();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MainMission(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              missionId_ = input.readUInt32();
              break;
            }
            case 16: {

              wave_ = input.readInt32();
              break;
            }
            case 24: {

              claimedRewardId_ = input.readInt32();
              break;
            }
            case 32: {

              enterCount_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MainMission_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MainMission_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MissionProto.MainMission.class, com.dxx.game.dto.MissionProto.MainMission.Builder.class);
    }

    public static final int MISSIONID_FIELD_NUMBER = 1;
    private int missionId_;
    /**
     * <pre>
     * 最远未完成的关卡ID，会超出上限
     * </pre>
     *
     * <code>uint32 missionId = 1;</code>
     * @return The missionId.
     */
    @java.lang.Override
    public int getMissionId() {
      return missionId_;
    }

    public static final int WAVE_FIELD_NUMBER = 2;
    private int wave_;
    /**
     * <pre>
     * 波次
     * </pre>
     *
     * <code>int32 wave = 2;</code>
     * @return The wave.
     */
    @java.lang.Override
    public int getWave() {
      return wave_;
    }

    public static final int CLAIMEDREWARDID_FIELD_NUMBER = 3;
    private int claimedRewardId_;
    /**
     * <pre>
     * 已领取的章节奖励ID，不超上限
     * </pre>
     *
     * <code>int32 claimedRewardId = 3;</code>
     * @return The claimedRewardId.
     */
    @java.lang.Override
    public int getClaimedRewardId() {
      return claimedRewardId_;
    }

    public static final int ENTERCOUNT_FIELD_NUMBER = 4;
    private int enterCount_;
    /**
     * <pre>
     * 进入章节次数
     * </pre>
     *
     * <code>int32 enterCount = 4;</code>
     * @return The enterCount.
     */
    @java.lang.Override
    public int getEnterCount() {
      return enterCount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (missionId_ != 0) {
        output.writeUInt32(1, missionId_);
      }
      if (wave_ != 0) {
        output.writeInt32(2, wave_);
      }
      if (claimedRewardId_ != 0) {
        output.writeInt32(3, claimedRewardId_);
      }
      if (enterCount_ != 0) {
        output.writeInt32(4, enterCount_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (missionId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, missionId_);
      }
      if (wave_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, wave_);
      }
      if (claimedRewardId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, claimedRewardId_);
      }
      if (enterCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, enterCount_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MissionProto.MainMission)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MissionProto.MainMission other = (com.dxx.game.dto.MissionProto.MainMission) obj;

      if (getMissionId()
          != other.getMissionId()) return false;
      if (getWave()
          != other.getWave()) return false;
      if (getClaimedRewardId()
          != other.getClaimedRewardId()) return false;
      if (getEnterCount()
          != other.getEnterCount()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MISSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getMissionId();
      hash = (37 * hash) + WAVE_FIELD_NUMBER;
      hash = (53 * hash) + getWave();
      hash = (37 * hash) + CLAIMEDREWARDID_FIELD_NUMBER;
      hash = (53 * hash) + getClaimedRewardId();
      hash = (37 * hash) + ENTERCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getEnterCount();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MissionProto.MainMission parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MissionProto.MainMission prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 主线关卡
     * </pre>
     *
     * Protobuf type {@code Proto.Mission.MainMission}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mission.MainMission)
        com.dxx.game.dto.MissionProto.MainMissionOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MainMission_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MainMission_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MissionProto.MainMission.class, com.dxx.game.dto.MissionProto.MainMission.Builder.class);
      }

      // Construct using com.dxx.game.dto.MissionProto.MainMission.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        missionId_ = 0;

        wave_ = 0;

        claimedRewardId_ = 0;

        enterCount_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MissionProto.internal_static_Proto_Mission_MainMission_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MainMission getDefaultInstanceForType() {
        return com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MainMission build() {
        com.dxx.game.dto.MissionProto.MainMission result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MissionProto.MainMission buildPartial() {
        com.dxx.game.dto.MissionProto.MainMission result = new com.dxx.game.dto.MissionProto.MainMission(this);
        result.missionId_ = missionId_;
        result.wave_ = wave_;
        result.claimedRewardId_ = claimedRewardId_;
        result.enterCount_ = enterCount_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MissionProto.MainMission) {
          return mergeFrom((com.dxx.game.dto.MissionProto.MainMission)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MissionProto.MainMission other) {
        if (other == com.dxx.game.dto.MissionProto.MainMission.getDefaultInstance()) return this;
        if (other.getMissionId() != 0) {
          setMissionId(other.getMissionId());
        }
        if (other.getWave() != 0) {
          setWave(other.getWave());
        }
        if (other.getClaimedRewardId() != 0) {
          setClaimedRewardId(other.getClaimedRewardId());
        }
        if (other.getEnterCount() != 0) {
          setEnterCount(other.getEnterCount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.MissionProto.MainMission parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.MissionProto.MainMission) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int missionId_ ;
      /**
       * <pre>
       * 最远未完成的关卡ID，会超出上限
       * </pre>
       *
       * <code>uint32 missionId = 1;</code>
       * @return The missionId.
       */
      @java.lang.Override
      public int getMissionId() {
        return missionId_;
      }
      /**
       * <pre>
       * 最远未完成的关卡ID，会超出上限
       * </pre>
       *
       * <code>uint32 missionId = 1;</code>
       * @param value The missionId to set.
       * @return This builder for chaining.
       */
      public Builder setMissionId(int value) {
        
        missionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最远未完成的关卡ID，会超出上限
       * </pre>
       *
       * <code>uint32 missionId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMissionId() {
        
        missionId_ = 0;
        onChanged();
        return this;
      }

      private int wave_ ;
      /**
       * <pre>
       * 波次
       * </pre>
       *
       * <code>int32 wave = 2;</code>
       * @return The wave.
       */
      @java.lang.Override
      public int getWave() {
        return wave_;
      }
      /**
       * <pre>
       * 波次
       * </pre>
       *
       * <code>int32 wave = 2;</code>
       * @param value The wave to set.
       * @return This builder for chaining.
       */
      public Builder setWave(int value) {
        
        wave_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 波次
       * </pre>
       *
       * <code>int32 wave = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWave() {
        
        wave_ = 0;
        onChanged();
        return this;
      }

      private int claimedRewardId_ ;
      /**
       * <pre>
       * 已领取的章节奖励ID，不超上限
       * </pre>
       *
       * <code>int32 claimedRewardId = 3;</code>
       * @return The claimedRewardId.
       */
      @java.lang.Override
      public int getClaimedRewardId() {
        return claimedRewardId_;
      }
      /**
       * <pre>
       * 已领取的章节奖励ID，不超上限
       * </pre>
       *
       * <code>int32 claimedRewardId = 3;</code>
       * @param value The claimedRewardId to set.
       * @return This builder for chaining.
       */
      public Builder setClaimedRewardId(int value) {
        
        claimedRewardId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的章节奖励ID，不超上限
       * </pre>
       *
       * <code>int32 claimedRewardId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClaimedRewardId() {
        
        claimedRewardId_ = 0;
        onChanged();
        return this;
      }

      private int enterCount_ ;
      /**
       * <pre>
       * 进入章节次数
       * </pre>
       *
       * <code>int32 enterCount = 4;</code>
       * @return The enterCount.
       */
      @java.lang.Override
      public int getEnterCount() {
        return enterCount_;
      }
      /**
       * <pre>
       * 进入章节次数
       * </pre>
       *
       * <code>int32 enterCount = 4;</code>
       * @param value The enterCount to set.
       * @return This builder for chaining.
       */
      public Builder setEnterCount(int value) {
        
        enterCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进入章节次数
       * </pre>
       *
       * <code>int32 enterCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnterCount() {
        
        enterCount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Mission.MainMission)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mission.MainMission)
    private static final com.dxx.game.dto.MissionProto.MainMission DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MissionProto.MainMission();
    }

    public static com.dxx.game.dto.MissionProto.MainMission getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MainMission>
        PARSER = new com.google.protobuf.AbstractParser<MainMission>() {
      @java.lang.Override
      public MainMission parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MainMission(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MainMission> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MainMission> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MissionProto.MainMission getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionGetInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionGetInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionGetInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionGetInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionStartRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionStartRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionStartResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionStartResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionEndRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionEndRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionEndResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionEndResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionGetHangUpItemsRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionGetHangUpItemsRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionGetHangUpItemsResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionGetHangUpItemsResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionQuickHangUpRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionQuickHangUpRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionQuickHangUpResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionQuickHangUpResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionStartDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionStartDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MissionEndDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MissionEndDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mission_MainMission_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Mission_MainMission_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rmission.proto\022\rProto.Mission\032\014common.p" +
      "roto\"I\n\025MissionGetInfoRequest\0220\n\014commonP" +
      "arams\030\001 \001(\0132\032.Proto.Common.CommonParams\"" +
      "W\n\026MissionGetInfoResponse\022\014\n\004code\030\001 \001(\005\022" +
      "/\n\013mainMission\030\002 \001(\0132\032.Proto.Mission.Mai" +
      "nMission\"y\n\023MissionStartRequest\0220\n\014commo" +
      "nParams\030\001 \001(\0132\032.Proto.Common.CommonParam" +
      "s\0220\n\010startDto\030\002 \001(\0132\036.Proto.Mission.Miss" +
      "ionStartDto\"R\n\024MissionStartResponse\022\014\n\004c" +
      "ode\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Co" +
      "mmon.CommonData\"s\n\021MissionEndRequest\0220\n\014" +
      "commonParams\030\001 \001(\0132\032.Proto.Common.Common" +
      "Params\022,\n\006endDto\030\002 \001(\0132\034.Proto.Mission.M" +
      "issionEndDto\"\201\001\n\022MissionEndResponse\022\014\n\004c" +
      "ode\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Co" +
      "mmon.CommonData\022/\n\013mainMission\030\003 \001(\0132\032.P" +
      "roto.Mission.MainMission\"P\n\034MissionGetHa" +
      "ngUpItemsRequest\0220\n\014commonParams\030\001 \001(\0132\032" +
      ".Proto.Common.CommonParams\"V\n\035MissionGet" +
      "HangUpItemsResponse\022\014\n\004code\030\001 \001(\005\022\'\n\006rew" +
      "ard\030\002 \003(\0132\027.Proto.Common.RewardDto\"T\n Mi" +
      "ssionReceiveHangUpItemsRequest\0220\n\014common" +
      "Params\030\001 \001(\0132\032.Proto.Common.CommonParams" +
      "\"_\n!MissionReceiveHangUpItemsResponse\022\014\n" +
      "\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto." +
      "Common.CommonData\"M\n\031MissionQuickHangUpR" +
      "equest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Com" +
      "mon.CommonParams\"X\n\032MissionQuickHangUpRe" +
      "sponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\013" +
      "2\030.Proto.Common.CommonData\"U\n\017MissionSta" +
      "rtDto\022/\n\013missionType\030\001 \001(\0162\032.Proto.Missi" +
      "on.MissionType\022\021\n\tmissionId\030\002 \001(\r\"\301\001\n\rMi" +
      "ssionEndDto\022/\n\013missionType\030\001 \001(\0162\032.Proto" +
      ".Mission.MissionType\022\021\n\tmissionId\030\002 \001(\r\022" +
      "\024\n\014startTransId\030\003 \001(\004\022\r\n\005isWin\030\004 \001(\010\022(\n\007" +
      "rewards\030\005 \003(\0132\027.Proto.Common.RewardDto\022\r" +
      "\n\005extar\030\006 \001(\t\022\016\n\006damage\030\007 \001(\004\"[\n\013MainMis" +
      "sion\022\021\n\tmissionId\030\001 \001(\r\022\014\n\004wave\030\002 \001(\005\022\027\n" +
      "\017claimedRewardId\030\003 \001(\005\022\022\n\nenterCount\030\004 \001" +
      "(\005*\027\n\013MissionType\022\010\n\004MAIN\020\000B \n\020com.dxx.g" +
      "ame.dtoB\014MissionProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Mission_MissionGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Mission_MissionGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Mission_MissionGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Mission_MissionGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "MainMission", });
    internal_static_Proto_Mission_MissionStartRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Mission_MissionStartRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionStartRequest_descriptor,
        new java.lang.String[] { "CommonParams", "StartDto", });
    internal_static_Proto_Mission_MissionStartResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Mission_MissionStartResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionStartResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Mission_MissionEndRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Mission_MissionEndRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionEndRequest_descriptor,
        new java.lang.String[] { "CommonParams", "EndDto", });
    internal_static_Proto_Mission_MissionEndResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Mission_MissionEndResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionEndResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "MainMission", });
    internal_static_Proto_Mission_MissionGetHangUpItemsRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Mission_MissionGetHangUpItemsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionGetHangUpItemsRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Mission_MissionGetHangUpItemsResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Mission_MissionGetHangUpItemsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionGetHangUpItemsResponse_descriptor,
        new java.lang.String[] { "Code", "Reward", });
    internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionReceiveHangUpItemsRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionReceiveHangUpItemsResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Mission_MissionQuickHangUpRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Mission_MissionQuickHangUpRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionQuickHangUpRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Mission_MissionQuickHangUpResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_Proto_Mission_MissionQuickHangUpResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionQuickHangUpResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Mission_MissionStartDto_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_Proto_Mission_MissionStartDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionStartDto_descriptor,
        new java.lang.String[] { "MissionType", "MissionId", });
    internal_static_Proto_Mission_MissionEndDto_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_Proto_Mission_MissionEndDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MissionEndDto_descriptor,
        new java.lang.String[] { "MissionType", "MissionId", "StartTransId", "IsWin", "Rewards", "Extar", "Damage", });
    internal_static_Proto_Mission_MainMission_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_Proto_Mission_MainMission_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Mission_MainMission_descriptor,
        new java.lang.String[] { "MissionId", "Wave", "ClaimedRewardId", "EnterCount", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

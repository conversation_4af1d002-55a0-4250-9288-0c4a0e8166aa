
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Equip_equip {
    public Equip_equip(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        nameID = _buf.get("nameID").getAsString();
        atlasID = _buf.get("atlasID").getAsInt();
        iconName = _buf.get("iconName").getAsString();
        specialID = _buf.get("specialID").getAsInt();
        Type = _buf.get("Type").getAsInt();
        Quality = _buf.get("Quality").getAsInt();
        isMerge = _buf.get("isMerge").getAsInt();
        baseAttributes = _buf.get("baseAttributes").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("statsPool").getAsJsonArray(); statsPool = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  statsPool.add(_v0); }   }
        qualityAttributes = _buf.get("qualityAttributes").getAsInt();
        UpgradeMultiplier = _buf.get("UpgradeMultiplier").getAsInt();
    }

    public static Equip_equip deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Equip_equip(_buf);
    }

    /**
     * id<br/>（2000000+个性ID*10000+部位ID*100+品质序列）
     */
    public final int id;
    /**
     * 装备名称
     */
    public final String nameID;
    /**
     * 图集ID
     */
    public final int atlasID;
    /**
     * 图标<br/>ring_+个性id<br/>necklace_+个性id<br/>
     */
    public final String iconName;
    /**
     * 个性id
     */
    public final int specialID;
    /**
     * 装备部位<br/>（从上往下，从左往右）
     */
    public final int Type;
    /**
     * 品质
     */
    public final int Quality;
    /**
     * 是否可以合成<br/>0、不可<br/>1、可以
     */
    public final int isMerge;
    /**
     * 基础属性
     */
    public final String baseAttributes;
    /**
     * 基础属性词条加成的随机池<br/>映射AttrRandomPool
     */
    public final java.util.List<Integer> statsPool;
    /**
     * 品质属性，只在对应的品质激活<br/>引用自skill
     */
    public final int qualityAttributes;
    /**
     * 强化属性加成<br/>只加成强化属性，万分比<br/>
     */
    public final int UpgradeMultiplier;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "nameID:" + nameID + ","
        + "atlasID:" + atlasID + ","
        + "iconName:" + iconName + ","
        + "specialID:" + specialID + ","
        + "Type:" + Type + ","
        + "Quality:" + Quality + ","
        + "isMerge:" + isMerge + ","
        + "baseAttributes:" + baseAttributes + ","
        + "statsPool:" + statsPool + ","
        + "qualityAttributes:" + qualityAttributes + ","
        + "UpgradeMultiplier:" + UpgradeMultiplier + ","
        + "}";
    }
}


package com.dxx.game.common.server.handler;

import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.log.LogUtils;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.protocol.MessageProto;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.CommonProto.ErrorMsg;
import com.google.protobuf.Message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.*;
import io.netty.util.AsciiString;
import io.netty.util.CharsetUtil;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.dxx.game.common.log.DxxLogConsoleAppender.RESP_CODE;
import static com.dxx.game.common.log.DxxLogConsoleAppender.RESP_DATA;

@XRayEnabled
@Component
public class HttpResponser {

	private static final AsciiString CONTENT_TYPE = AsciiString.cached("Content-Type");
	private static final AsciiString CONTENT_LENGTH = AsciiString.cached("Content-Length");
	private static final AsciiString CONNECTION = AsciiString.cached("Connection");
	private static final AsciiString KEEP_ALIVE = AsciiString.cached("keep-alive");

	private static MessageProto messageProto;

	@Autowired
	public HttpResponser(MessageProto messageProto) {
		HttpResponser.messageProto = messageProto;
	}

	/**
	 * http响应消息
	 * @param ctx
	 * @param request
	 * @param result
	 */
	public static void doSend(ChannelHandlerContext ctx, FullHttpRequest request, Result<Message> result) {
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK);

		messageProto.buildResponse(result, response.content());

		// 构造response失败
		if (response.content().capacity() == 0) {
			respErrorMsg(ctx, request, ErrorCode.BUILD_RESPONSE_FAILED, "build response failed");
			return;
		}

		doSend(ctx, request, response);
	}

	public static void doSend(ChannelHandlerContext ctx, FullHttpRequest request, Object responseData) {
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1,
				HttpResponseStatus.FORBIDDEN);
		if (responseData != null) {
			String result = "";
			if (responseData instanceof String) {
				result = responseData.toString();
			} else {
				result = JSONObject.toJSONString(responseData);
			}

			MDC.put(RESP_DATA, result);

			response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK,
					Unpooled.copiedBuffer(result,  CharsetUtil.UTF_8));
			response.headers().set(CONTENT_TYPE, "application/json");
		}

		doSend(ctx, request, response);
	}


	/**
	 * http响应消息
	 * @param ctx
	 * @param request
	 * @param response
	 */
	public static void doSend(ChannelHandlerContext ctx, FullHttpRequest request, FullHttpResponse response) {
		if (response.status().code() != 200) {
			ByteBuf buf = Unpooled.copiedBuffer(response.status().toString(), CharsetUtil.UTF_8);
			response.content().writeBytes(buf);
			buf.release();
		}

		MDC.put(RESP_CODE, String.valueOf(response.status().code()));

		HttpUtil.setContentLength(response, response.content().readableBytes());

		if (!HttpUtil.isKeepAlive(request) || response.status().code() != 200) {
			// if not Keep-Alive，close it
			ctx.channel().writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
		} else {
			response.headers().set(CONNECTION, KEEP_ALIVE);
			ctx.channel().writeAndFlush(response);
		}

		LogUtils.accessLog(ctx, request, response);
	}

	public static void doSendForward(ChannelHandlerContext ctx, FullHttpRequest fullRequest, String url) {
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.FOUND);
		response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
		response.headers().set(HttpHeaderNames.LOCATION, url);
		doSend(ctx, fullRequest, response);
	}

	public static void doSendWithCros(ChannelHandlerContext ctx, FullHttpRequest request, Object responseData) {
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1,
				HttpResponseStatus.FORBIDDEN);
		if (responseData != null) {
			String result = "";
			if (responseData instanceof Map) {
				result = JSONObject.toJSONString(responseData);
			} else if (responseData instanceof String) {
				result = responseData.toString();
			}
			response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK,
					Unpooled.copiedBuffer(result,  CharsetUtil.UTF_8));
			response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
			response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS, "*");
			response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS, "*");
			response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
			response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json");
		}

		doSend(ctx, request, response);
	}

	public static void doSendTxt(ChannelHandlerContext ctx, FullHttpRequest fullRequest, String txt) {
		FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK,
				Unpooled.copiedBuffer(txt,  CharsetUtil.UTF_8));
		response.headers().set(CONTENT_TYPE, "text/plain; charset=UTF-8");
		doSend(ctx, fullRequest, response);
	}

	/**
	 * 返回错误消息
	 * @param ctx
	 * @param request
	 * @param errCode
	 * @param errorMsg
	 */
	public static void respErrorMsg(ChannelHandlerContext ctx, FullHttpRequest request, int errCode, String errorMsg) {
		ErrorMsg.Builder errMsg = ErrorMsg.newBuilder();
		errMsg.setCode(errCode);
		errMsg.setMsg(errorMsg);
		Result<Message> result = Result.Success(errMsg.build());
		result.setCmd((short)9999);
		doSend(ctx, request, result);
	}
}

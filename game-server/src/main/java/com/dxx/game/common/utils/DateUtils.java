package com.dxx.game.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.TimeZone;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2019-12-10 12:22
 */
@Slf4j
public class DateUtils {

    public static final int SECONDS_PRE_MINUTE = 60;
    public static final int SECONDS_PRE_HOUR = 60 * SECONDS_PRE_MINUTE;
    public static final int SECONDS_12_HOUR = 12 * SECONDS_PRE_HOUR;
    public static final int SECONDS_PRE_DAY = 24 * SECONDS_PRE_HOUR;
    public static final int SECONDS_7_DAy = 7 * SECONDS_PRE_DAY;
    public static final int SECONDS_15_DAY = 15 * SECONDS_PRE_DAY;
    public static final int SECONDS_30_DAY = 30 * SECONDS_PRE_DAY;
    public static final int SECONDS_60_DAY = 60 * SECONDS_PRE_DAY;
    public static final int SECONDS_90_DAY = 90 * SECONDS_PRE_DAY;
    public static final long SECONDS_365_DAY = 365L * SECONDS_PRE_DAY;


    private static final TimeZone timeZoneOfBeijing = TimeZone.getTimeZone("GMT+8");

    // 国内时区
    public static final String strZone = "GMT+8";
    // 国际时区
//    public static final String strZone = "UTC";
    private static final TimeZone timeZone = TimeZone.getTimeZone(strZone);

    static final ZoneId zoneId = ZoneId.of(strZone);

    private static final DateTimeFormatter UTC_ISO_8601_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneId.of("UTC"));
    public static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(zoneId);
    public static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(zoneId);
    public static final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(zoneId);

    static {
        // 设置时区
        TimeZone.setDefault(TimeZone.getTimeZone(strZone));
    }

    public static ZonedDateTime startOfDay() {
        // 获取午夜时间（00:00）
        return LocalDate.now(zoneId).atStartOfDay(zoneId);
    }

    public static ZonedDateTime startOfDay(long ts) {
        return Instant.ofEpochSecond(ts).atZone(zoneId).toLocalDate().atStartOfDay(zoneId);
    }

    /**
     * 获取时间戳
     *
     * @return
     */
    public static long getUnixTime() {
        return Instant.now().getEpochSecond();
    }

    /**
     * 当天0点时间 时间戳
     *
     * @return
     */
    public static long getTimeHour0InDay() {
        return startOfDay().toEpochSecond();
    }

    /**
     * 持续了几天
     */
    public static int getDayLast(long startTs, long endTs) {
        if (startTs <= 0 || endTs <= 0 || startTs >= endTs) {
            return 0;
        }

        // 将毫秒时间戳转换为 LocalDate 对象
        LocalDate startLocalDate = LocalDate.ofInstant(Instant.ofEpochSecond(startTs), zoneId);
        LocalDate endLocalDate = LocalDate.ofInstant(Instant.ofEpochSecond(endTs), zoneId);
        // 使用 ChronoUnit.DAYS.between 计算两个日期之间的天数差
        return (int) startLocalDate.until(endLocalDate, ChronoUnit.DAYS);
    }

    public static long nextDayStart() {
        return startOfDay().plusDays(1).toEpochSecond();
    }

    /**
     * 相关系统重置时间凌晨24点
     */
    public static long getSystemResetTime() {
        return nextDayStart();
    }

    public static ZonedDateTime startOfWeek(long ts) {
        return startOfDay(ts).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    }

    /**
     * 每周重置时间
     */
    public static long getSystemWeeklyResetTime() {
        return startOfWeek(startOfDay().toEpochSecond()).plusWeeks(1).toEpochSecond();
    }

    public static int getWeekDay(long ts) {
        ZonedDateTime zonedDateTime = Instant.ofEpochSecond(ts).atZone(timeZone.toZoneId());
        return zonedDateTime.getDayOfWeek().getValue();
    }


    /**
     * 判断两个时间戳 是否是同一月
     *
     * @param timestamp1
     * @param timestamp2
     * @return
     */
    public static boolean isSameMonth(long timestamp1, long timestamp2) {

        // 把时间戳转换成LocalDate
        LocalDate date1 = Instant.ofEpochSecond(timestamp1).atZone(zoneId).toLocalDate();
        LocalDate date2 = Instant.ofEpochSecond(timestamp2).atZone(zoneId).toLocalDate();
        // 比较年月是否相同
        return YearMonth.from(date1).equals(YearMonth.from(date2));
    }

    /**
     * 是否在同一周
     */
    public static boolean isSameWeek(long timestamp1, long timestamp2) {
        // 将时间戳转换为 LocalDate
        LocalDate date1 = Instant.ofEpochSecond(timestamp1).atZone(zoneId).toLocalDate();
        LocalDate date2 = Instant.ofEpochSecond(timestamp2).atZone(zoneId).toLocalDate();

        // 获取每个日期所在周的周一
        LocalDate weekStart1 = date1.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate weekStart2 = date2.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 比较两周的起始日期是否相同
        return weekStart1.equals(weekStart2);
    }


    /**
     * 判断两个时间戳是否是同一天
     */
    public static boolean isSameDay(long timestamp1, long timestamp2) {
        LocalDate date1 = Instant.ofEpochSecond(timestamp1).atZone(zoneId).toLocalDate();
        // 获取当前的LocalDate对象
        LocalDate date2 = Instant.ofEpochSecond(timestamp2).atZone(zoneId).toLocalDate();
        // 比较两个LocalDate对象是否相等
        return date1.equals(date2);
    }


    /**
     * 当天是周几
     *
     * @return
     */
    public static int getDayOfWeek() {
        return LocalDate.now(zoneId).getDayOfWeek().getValue();
    }

    public static int getDayOfWeek(long ts) {
        return Instant.ofEpochSecond(ts).atZone(zoneId).getDayOfWeek().getValue();
    }

    /**
     * 获取yyyyMMdd格式日期
     *
     * @return
     */
    public static String getDateyyyyMMdd() {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(strZone));
        return now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public static String getDateyyyyMMddf() {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(strZone));
        return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }


    /**
     * 获取 HHmmss 格式时间
     *
     * @return
     */
    public static String getTimeHHmmss() {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(strZone));
        return now.format(DateTimeFormatter.ofPattern("HHmmss"));
    }

    /**
     * 时间戳转日期格式
     *
     * @param timestamp
     * @return
     */
    public static String stampToDate(long timestamp) {
        return dateTimeFormatter.format(Instant.ofEpochSecond(timestamp));
    }


    /**
     * 获取时间 yyyy-MM-dd hh:mm:ss 格式
     *
     * @return
     */
    public static String getDateTime() {
        return dateTimeFormatter.format(Instant.now().atZone(zoneId));
    }


    /**
     * 获取年份
     *
     * @return
     */
    public static short getYear() {
        Calendar calendar = Calendar.getInstance(timeZone);
        return (short) calendar.get(Calendar.YEAR);
    }

    /**
     * 获取UTC格式时间字符串
     *
     * @param time
     * @return
     */
    public static String getUTCTimeStr(long time) {
        return UTC_ISO_8601_FORMATTER.format(Instant.ofEpochSecond(time));
    }

    /**
     * 获取当天是周几
     *
     * @return
     */
    public static int getWeekDay() {
        Calendar cal = Calendar.getInstance(timeZone);

        int weekDay = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekDay == 0) {
            weekDay = 7;
        }
        return weekDay;
    }

    /**
     * 获取周几的开始时间.
     *
     * @param weekDay
     * @return
     */
    public static long getWeekDayStartTimestamp(int weekDay) {
        int nowWeekDay = getWeekDay();
        long todayStartTime = getTimeHour0InDay();
        if (weekDay >= nowWeekDay) {
            return todayStartTime + ((weekDay - nowWeekDay) * 86400);
        } else {
            return getSundayTimestamp() + (weekDay * 86400);
        }
    }

    /**
     * 获取本周周日时间
     */
    public static long getSundayTimestamp() {
        Calendar cal = Calendar.getInstance(timeZone);

        cal.setFirstDayOfWeek(Calendar.MONDAY);

        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTimeInMillis() / 1000;
    }

    /**
     * 计算两个时间戳相差几天
     *
     * @param tm1
     * @param tm2
     * @return
     */
    public static int calcDays(long tm1, long tm2) {
        int diffSeconds = (int) Math.abs(tm1 - tm2);
        return (int) Math.ceil((double) diffSeconds / 86400);
    }

    /**
     * 本周结束时间戳
     *
     * @return
     */
    public static long getSundayEndTimestamp() {
        return getSundayTimestamp() + 86400;
    }

    public static long getSystemMonthResetTime() {
        // 获取当前时间
        LocalDate now = LocalDate.now(zoneId);
        // 获取下个月第一天
        LocalDate firstDayOfNextMonth = now.withDayOfMonth(1).plusMonths(1);
        // 将 LocalDate 转换为秒级时间戳
        return firstDayOfNextMonth.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    public static long getSystemMonthResetTimeByTime(long time) {
        // 将秒级时间戳转换为 LocalDate
        LocalDate date = Instant.ofEpochSecond(time)
                .atZone(zoneId)
                .toLocalDate();
        // 获取下个月的第一天
        LocalDate firstDayOfNextMonth = date.withDayOfMonth(1).plusMonths(1);
        // 将 LocalDate 转换为秒级时间戳
        return firstDayOfNextMonth.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    public static long getSystemWeekResetTimeByTime(long time) {
        // 将秒级时间戳转换为 LocalDate
        LocalDate date = Instant.ofEpochSecond(time)
                .atZone(zoneId)
                .toLocalDate();
        // 获取下一个星期一
        LocalDate nextMonday = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
        // 将 LocalDate 转换为秒级时间戳
        return nextMonday.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    public static long getSystemResetTimeByTime(long time) {
        // 直接将秒级时间戳转换为本地日期，避免多余的 LocalDateTime 转换
        LocalDate nextDay = Instant.ofEpochSecond(time)
                .atZone(timeZone.toZoneId())
                .toLocalDate()
                .plusDays(1);
        return nextDay.atStartOfDay(timeZone.toZoneId()).toEpochSecond();
    }

    public static boolean isBetween(long timestamp, long startTimestamp, long endTimestamp) {
        return timestamp >= startTimestamp && timestamp < endTimestamp;
    }

    /**
     * 从日期时间字符串（格式：yyyy-MM-dd HH:mm:ss）解析时间戳
     *
     * @param dateTimeStr 日期时间字符串，必须完整包含日期、时间
     * @return 失败时返回0
     */
    public static long parseToTimestamp(String dateTimeStr) {
        return parseToTimestamp(dateTimeStr, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 从日期时间字符串解析时间戳
     * 日期时间字符串必须完整包含日期、时间
     * 否则使用对应的 getMilliOfTime getMilliOfDate
     *
     * @param dateTimeStr 日期时间字符串，完整包含日期、时间
     * @return 失败时返回0
     */
    public static long parseToTimestamp(String dateTimeStr, String pattern) {
        LocalDateTime dateTime = parseDateTime(dateTimeStr, pattern);
        if (dateTime == null) {
            return 0L;
        }
        return getTimestampOfDateTime(dateTime);
    }


    private static LocalDateTime parseDateTime(String dateTimeStr, String pattern) {
        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern).withZone(zoneId));
        } catch (Exception e) {
            log.error("parseDateTime e:", e);
            return null;
        }
    }

    private static long getTimestampOfDateTime(LocalDateTime dateTime) {
        return dateTime.atZone(timeZone.toZoneId()).toInstant().getEpochSecond();
    }

    public static long getTimeHour0InDayOfBeijing() {
        Calendar cal = Calendar.getInstance(timeZoneOfBeijing);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis() / 1000;
    }


    public static void main(String[] args) throws Exception {
        System.out.println(DateUtils.getSystemResetTime());
        System.out.println(DateUtils.getSystemWeeklyResetTime());
        System.out.println(DateUtils.getSystemMonthResetTime());

    }
}























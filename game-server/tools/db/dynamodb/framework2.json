{"ModelName": "framework2", "ModelMetadata": {"Author": "", "DateCreated": "Dec 30, 2020, 03:11 PM", "DateLastModified": "Mar 28, 2025, 04:28 PM", "Description": "", "AWSService": "Amazon DynamoDB", "Version": "3.0"}, "DataModel": [{"TableName": "equip", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "rowId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "item", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "rowId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "log-gm-reward", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "uniqueId", "AttributeType": "S"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "log-resource", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "transId", "AttributeType": "N"}}, "NonKeyAttributes": [{"AttributeName": "logTime", "AttributeType": "N"}], "GlobalSecondaryIndexes": [{"IndexName": "userId-logTime-index", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "logTime", "AttributeType": "N"}}, "Projection": {"ProjectionType": "ALL"}}], "DataAccess": {"MySql": {}}, "SampleDataFormats": {}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "prepare-recharge-order", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "preOrderId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "recharge-order", "KeyAttributes": {"PartitionKey": {"AttributeName": "orderId", "AttributeType": "S"}}, "NonKeyAttributes": [{"AttributeName": "userId", "AttributeType": "N"}, {"AttributeName": "timeStamp", "AttributeType": "N"}], "GlobalSecondaryIndexes": [{"IndexName": "userId-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}}, "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "userId-Time", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "timeStamp", "AttributeType": "N"}}, "Projection": {"ProjectionType": "KEYS_ONLY"}}], "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "user", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}}, "NonKeyAttributes": [{"AttributeName": "accountId", "AttributeType": "S"}, {"AttributeName": "deviceId", "AttributeType": "S"}, {"AttributeName": "nick<PERSON><PERSON>", "AttributeType": "S"}, {"AttributeName": "serverId", "AttributeType": "N"}], "GlobalSecondaryIndexes": [{"IndexName": "accountId-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "accountId", "AttributeType": "S"}, "SortKey": {"AttributeName": "serverId", "AttributeType": "N"}}, "Projection": {"ProjectionType": "KEYS_ONLY"}}, {"IndexName": "deviceId-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "deviceId", "AttributeType": "S"}, "SortKey": {"AttributeName": "serverId", "AttributeType": "N"}}, "Projection": {"ProjectionType": "KEYS_ONLY"}}, {"IndexName": "nickName-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "nick<PERSON><PERSON>", "AttributeType": "S"}, "SortKey": {"AttributeName": "serverId", "AttributeType": "N"}}, "Projection": {"ProjectionType": "KEYS_ONLY"}}, {"IndexName": "serverId-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "serverId", "AttributeType": "N"}, "SortKey": {"AttributeName": "userId", "AttributeType": "N"}}, "Projection": {"ProjectionType": "KEYS_ONLY"}}], "DataAccess": {"MySql": {}}, "SampleDataFormats": {}, "BillingMode": "PAY_PER_REQUEST"}, {"TableName": "shop", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "task", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "froze-device", "KeyAttributes": {"PartitionKey": {"AttributeName": "deviceId", "AttributeType": "S"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "guild", "KeyAttributes": {"PartitionKey": {"AttributeName": "PK", "AttributeType": "S"}, "SortKey": {"AttributeName": "SK", "AttributeType": "S"}}, "NonKeyAttributes": [{"AttributeName": "guildName", "AttributeType": "S"}, {"AttributeName": "guildId", "AttributeType": "N"}, {"AttributeName": "SK2", "AttributeType": "S"}, {"AttributeName": "msgId", "AttributeType": "N"}], "GlobalSecondaryIndexes": [{"IndexName": "guildName-index", "KeyAttributes": {"PartitionKey": {"AttributeName": "guildName", "AttributeType": "S"}}, "Projection": {"ProjectionType": "KEYS_ONLY"}}, {"IndexName": "guildId-index", "KeyAttributes": {"PartitionKey": {"AttributeName": "guildId", "AttributeType": "N"}, "SortKey": {"AttributeName": "SK2", "AttributeType": "S"}}, "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "guildMessage-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "PK", "AttributeType": "S"}, "SortKey": {"AttributeName": "msgId", "AttributeType": "N"}}, "Projection": {"ProjectionType": "ALL"}}], "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "hero", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "rowId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PAY_PER_REQUEST"}, {"TableName": "report", "KeyAttributes": {"PartitionKey": {"AttributeName": "rowId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "account", "KeyAttributes": {"PartitionKey": {"AttributeName": "PK", "AttributeType": "S"}}, "NonKeyAttributes": [{"AttributeName": "accountId", "AttributeType": "S"}, {"AttributeName": "deviceId", "AttributeType": "S"}], "GlobalSecondaryIndexes": [{"IndexName": "accountId-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "accountId", "AttributeType": "S"}}, "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "deviceId-Index", "KeyAttributes": {"PartitionKey": {"AttributeName": "deviceId", "AttributeType": "S"}}, "Projection": {"ProjectionType": "ALL"}}], "DataAccess": {"MySql": {}}, "BillingMode": "PAY_PER_REQUEST"}, {"TableName": "server", "KeyAttributes": {"PartitionKey": {"AttributeName": "serverId", "AttributeType": "N"}}, "DataAccess": {"MySql": {}}, "SampleDataFormats": {}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "gameplay", "KeyAttributes": {"PartitionKey": {"AttributeName": "pk", "AttributeType": "S"}, "SortKey": {"AttributeName": "sk", "AttributeType": "S"}}, "DataAccess": {"MySql": {}}, "SampleDataFormats": {}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "activity", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "sk", "AttributeType": "S"}}, "DataAccess": {"MySql": {}}, "SampleDataFormats": {}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}, {"TableName": "user-module", "KeyAttributes": {"PartitionKey": {"AttributeName": "userId", "AttributeType": "N"}, "SortKey": {"AttributeName": "module", "AttributeType": "S"}}, "DataAccess": {"MySql": {}}, "SampleDataFormats": {}, "BillingMode": "PROVISIONED", "ProvisionedCapacitySettings": {"ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "AutoScalingRead": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}, "AutoScalingWrite": {"ScalableTargetRequest": {"MinCapacity": 1, "MaxCapacity": 10, "ServiceRole": "AWSServiceRoleForApplicationAutoScaling_DynamoDBTable"}, "ScalingPolicyConfiguration": {"TargetValue": 70}}}}]}
package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.dao.dynamodb.model.LogResource;
import com.dxx.game.dao.dynamodb.repository.LogResourceDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.QUERY_RESOURCE_LOG)
public class GMQueryUserResourceLog extends AbstractGMProcessor {

    @Resource
    private LogResourceDao logResourceDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long userId;
        private int pageSize;
        private int itemId;
        private int command;
        private JSONObject exclusiveStartKey;
        private JSONArray logTime;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class GMResponse {
        private List<LogResource> list;
        private Map<String, Object> lastEvaluatedKey;
    }
    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        long userId = request.getUserId();
        int pageSize = request.getPageSize();
        int itemId = request.getItemId();
        int command = request.getCommand();
        JSONObject exclusiveStartKey = request.getExclusiveStartKey();
        JSONArray logTime = request.getLogTime();

        List<LogResource> logResourceList = new ArrayList<>();
        Map<String, AttributeValue> queryNextStartKeymap = new HashMap<>();

        if (!exclusiveStartKey.isEmpty()) {
            queryNextStartKeymap.put("userId", AttributeValue.builder().n(String.valueOf(userId)).build());
            queryNextStartKeymap.put("transId", AttributeValue.builder().n(exclusiveStartKey.getString("transId")).build());
            queryNextStartKeymap.put("logTime", AttributeValue.builder().n(exclusiveStartKey.getString("logTime")).build());
        }
        DynamoDBConvertUtil.getAttributeMap(exclusiveStartKey);
        do {
            List<Long> transIds = new ArrayList<>();
            QueryResponse queryResponse = logResourceDao.queryLog(userId, pageSize, command, logTime, queryNextStartKeymap);
            for (Map<String, AttributeValue> item : queryResponse.items()) {
                transIds.add(Long.parseLong(item.get("transId").n()));
            }

            if (!transIds.isEmpty()) {
                List<LogResource> queryList = logResourceDao.getByTransIds(userId, transIds);

                if (itemId > 0) {
                    Iterator<LogResource> iterator = queryList.iterator();
                    while (iterator.hasNext()) {
                        LogResource logResource = iterator.next();
                        boolean found = false;
                        for (List<Long> item : logResource.getItems()) {
                            if (item.get(0) == itemId) {
                                found = true;
                                break;
                            }
                        }
                        if (!found) {
                            iterator.remove();
                        }
                    }
                }
                logResourceList.addAll(queryList);
            }
            queryNextStartKeymap = queryResponse.lastEvaluatedKey();
            if (!queryResponse.hasLastEvaluatedKey()) {
                break;
            }
        } while (logResourceList.size() < pageSize);

        Map<String, Object> lastEvaluatedKey = new HashMap<>();
        if (logResourceList.size() > pageSize) {
            logResourceList = logResourceList.subList(0, pageSize);
            LogResource lastData = logResourceList.get(pageSize - 1);
            lastEvaluatedKey.put("transId", lastData.getTransId());
            lastEvaluatedKey.put("userId", userId);
            lastEvaluatedKey.put("logTime", lastData.getLogTime());
        } else {
            lastEvaluatedKey = DynamoDBConvertUtil.getMapForComplexObject(queryNextStartKeymap);
        }

        if (!logResourceList.isEmpty()) {
            logResourceList.sort((o1, o2) -> {
                long a = o1.getTransId();
                long b = o2.getTransId();
                return Long.compare(b, a);
            });
        }

        return GMResponse.builder()
                .list(logResourceList)
                .lastEvaluatedKey(lastEvaluatedKey)
                .build();
    }
}

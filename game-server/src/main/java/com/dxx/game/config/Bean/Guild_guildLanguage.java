
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildLanguage {
    public Guild_guildLanguage(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Notes = _buf.get("Notes").getAsString();
        Code = _buf.get("Code").getAsString();
        TranslateCode = _buf.get("TranslateCode").getAsString();
    }

    public static Guild_guildLanguage deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildLanguage(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * 备注
     */
    public final String Notes;
    /**
     * 语言代码
     */
    public final String Code;
    /**
     * 翻译的语言代码
     */
    public final String TranslateCode;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Notes:" + Notes + ","
        + "Code:" + Code + ","
        + "TranslateCode:" + TranslateCode + ","
        + "}";
    }
}


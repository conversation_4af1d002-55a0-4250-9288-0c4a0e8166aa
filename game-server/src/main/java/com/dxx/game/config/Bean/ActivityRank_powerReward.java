
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityRank_powerReward {
    public ActivityRank_powerReward(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Need = _buf.get("Need").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        languageId = _buf.get("languageId").getAsInt();
    }

    public static ActivityRank_powerReward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityRank_powerReward(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * 目标进度
     */
    public final int Need;
    /**
     * 奖励
     */
    public final java.util.List<String> Reward;
    /**
     * 多语言ID
     */
    public final int languageId;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Need:" + Need + ","
        + "Reward:" + Reward + ","
        + "languageId:" + languageId + ","
        + "}";
    }
}


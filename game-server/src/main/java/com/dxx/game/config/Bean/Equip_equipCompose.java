
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Equip_equipCompose {
    public Equip_equipCompose(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        quality = _buf.get("quality").getAsInt();
        starID = _buf.get("starID").getAsInt();
        starCount = _buf.get("starCount").getAsInt();
        reduceTo = _buf.get("reduceTo").getAsInt();
        reduceNeed1 = _buf.get("reduceNeed1").getAsInt();
        reduceNeed2 = _buf.get("reduceNeed2").getAsInt();
        composeTo = _buf.get("composeTo").getAsInt();
        composeNeed1 = _buf.get("composeNeed1").getAsInt();
        composeNeed2 = _buf.get("composeNeed2").getAsInt();
        composeNeed3 = _buf.get("composeNeed3").getAsInt();
        composeNeed4 = _buf.get("composeNeed4").getAsInt();
        maxLevel = _buf.get("maxLevel").getAsInt();
    }

    public static Equip_equipCompose deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Equip_equipCompose(_buf);
    }

    /**
     * 内容ID
     */
    public final int id;
    /**
     * 当前品质
     */
    public final int quality;
    /**
     * 星星ID
     */
    public final int starID;
    /**
     * 星星数量
     */
    public final int starCount;
    /**
     * 降品ID<br/>0表示不可降品
     */
    public final int reduceTo;
    /**
     * 降品返还通用额外装备数量
     */
    public final int reduceNeed1;
    /**
     * 降品返还通用额外装备品质
     */
    public final int reduceNeed2;
    /**
     * 合成后获得的装备品质，0表示不可合成
     */
    public final int composeTo;
    /**
     * 合成<br/>1、部件<br/>2、ID<br/>3、个性ID和部件
     */
    public final int composeNeed1;
    /**
     * 合成下一品质需要的材料的内容ID
     */
    public final int composeNeed2;
    /**
     * 合成消耗的装备数量
     */
    public final int composeNeed3;
    /**
     * 是否可以使用装备通用材料<br/>0、不可以<br/>1、可以
     */
    public final int composeNeed4;
    /**
     * 强化等级上限
     */
    public final int maxLevel;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "quality:" + quality + ","
        + "starID:" + starID + ","
        + "starCount:" + starCount + ","
        + "reduceTo:" + reduceTo + ","
        + "reduceNeed1:" + reduceNeed1 + ","
        + "reduceNeed2:" + reduceNeed2 + ","
        + "composeTo:" + composeTo + ","
        + "composeNeed1:" + composeNeed1 + ","
        + "composeNeed2:" + composeNeed2 + ","
        + "composeNeed3:" + composeNeed3 + ","
        + "composeNeed4:" + composeNeed4 + ","
        + "maxLevel:" + maxLevel + ","
        + "}";
    }
}


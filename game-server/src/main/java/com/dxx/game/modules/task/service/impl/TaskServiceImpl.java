package com.dxx.game.modules.task.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.MaskUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.achievements.AchievementsEntity;
import com.dxx.game.config.entity.task.DailyActiveEntity;
import com.dxx.game.config.entity.task.DailyTaskEntity;
import com.dxx.game.config.entity.task.WeeklyActiveEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.consts.TaskType;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.model.Task;
import com.dxx.game.dao.dynamodb.model.Task.TaskModel;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dao.dynamodb.repository.TaskDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CommonProto.TaskDto;
import com.dxx.game.dto.TaskProto.*;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.model.TaskProcess;
import com.dxx.game.modules.task.service.TaskService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    private TaskDao taskDao;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private ShopDao shopDao;


    @DynamoDBTransactional
    @Override
    public Result<TaskGetInfoResponse> getInfoAction(TaskGetInfoRequest params) {
        long userId = RequestContext.getUserId();

        Task task = this.getTaskData(userId);
        if (task == null) {
            log.error("getTaskData failed, userId: {}", userId);
            return Result.Error(ErrorCode.INSERT_DATA_FAILED);
        }
        TaskGetInfoResponse.Builder response = TaskGetInfoResponse.newBuilder();
        response.setTasks(this.buildTasks(task, true));
        response.setDailyTaskActive(task.getDailyActive());
        response.setWeeklyTaskActive(task.getWeeklyActive());
        response.setDailyTaskResetTime(task.getDailyTime());
        response.setDailyTaskRewardLog(task.getDailyReward());
        response.setWeeklyTaskRewardLog(task.getWeeklyReward());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<TaskRewardDailyResponse> taskRewardDailyAction(TaskRewardDailyRequest params) {
        long userId = RequestContext.getUserId();
        Task task = taskDao.getByUserId(userId);
        if (task == null) {
            log.error("task data is null, userId:{}", userId);
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        TaskRewardDailyResponse.Builder response = TaskRewardDailyResponse.newBuilder();

        int id = params.getId();
        // 验证任务是否完成或者已领取
        Map<Integer, TaskModel> tasks = this.getTasks(TaskType.DAILY, task);
        TaskModel taskModel = tasks.get(id);
        if (taskModel == null) {
            log.error("task is null, id :{}, userId, :{}", id, userId);
            return Result.Error(ErrorCode.TASK_NOT_FOUND);
        }
        if (taskModel.getFinish() == 0) {
            return Result.Error(ErrorCode.TASK_NOT_FINISH);
        }
        if (taskModel.getReceive() == 1) {
            return Result.Error(ErrorCode.TASK_ALREADY_RECEIVE);
        }
        int active = gameConfigManager.getTaskConfig().getDailyTaskEntity(id).getDailyActiveReward();
        task.setDailyActive((short) (task.getDailyActive() + active));
        response.setActiveDaily(task.getDailyActive());

//        List<Integer> passReward = new ArrayList<>();
//        passReward.add(RewardResourceType.BATTLE_PASS_SCORE.getValue());
//        passReward.add(active);
//        RewardResultSet resultSet = rewardService.executeReward(userId, passReward);
//        if (resultSet.isFailed()) {
//            return Result.Error(resultSet.getResultCode());
//        }

        task.setWeeklyActive((short) (task.getWeeklyActive() + active));
        response.setActiveWeekly(task.getWeeklyActive());

        taskModel.setReceive(1);

        this.updateTasksToDB(TaskType.DAILY, tasks, task);

        TaskDto taskDto = this.buildTaskDto(id, TaskType.DAILY, taskModel);
        response.setUpdateTaskDto(taskDto);

        response.setTasks(this.buildTasks(task, false));

        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iapModel = shop.getIap();
        Shop.IAPBattlePassModel battlePassModel = iapModel.getBattlePassModel();

        CommonProto.CommonData commonData = CommonHelper.buildCommonData();
        commonData = commonData.toBuilder().setBattlePassScore(battlePassModel.getScore()).build();
        response.setCommonData(commonData);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<TaskRewardAchieveResponse> taskRewardAchieveAction(TaskRewardAchieveRequest params) {
        long userId = RequestContext.getUserId();
        Task task = taskDao.getByUserId(userId);
        if (task == null) {
            log.error("task data is null, userId:{}", userId);
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        TaskRewardAchieveResponse.Builder response = TaskRewardAchieveResponse.newBuilder();

        int id = params.getId();
        // 是否有下一阶段任务
        boolean isHaveNextAchieve = false;
        // 验证任务是否完成或者已领取
        Map<Integer, TaskModel> tasks = this.getTasks(TaskType.ACHIEVE, task);
        TaskModel taskModel = tasks.get(id);
        if (taskModel == null) {
            log.error("task is null, id :{}, userId, :{}", id, userId);
            return Result.Error(ErrorCode.TASK_NOT_FOUND);
        }
        if (taskModel.getFinish() == 0) {
            return Result.Error(ErrorCode.TASK_NOT_FINISH);
        }
        if (taskModel.getReceive() == 1) {
            return Result.Error(ErrorCode.TASK_ALREADY_RECEIVE);
        }
        // 活跃度
        AchievementsEntity achieveConfig = gameConfigManager.getAchievementsConfig().getAchievementsEntity(id);
        List<List<Integer>> rewardConfig = achieveConfig.getReward();

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        if (ResponseContext.getTaskData() != null) {
            task = (Task) ResponseContext.getTaskData();
            tasks = this.getTasks(TaskType.ACHIEVE, task);
            taskModel = tasks.get(id);
        }
        // 判断是否有下一段任务
        int level = achieveConfig.getAchievementsLevel();
        Map<Integer, AchievementsEntity> achievementsEntityMap = gameConfigManager.getAchievementsConfig().getAchievements();
        for (Map.Entry<Integer, AchievementsEntity> entry : achievementsEntityMap.entrySet()) {
            if (entry.getValue().getAchievementsType() == taskModel.getType() && entry.getValue().getAchievementsLevel() == level + 1) {
                taskModel.setReceive(0);
                taskModel.setFinish(0);
                if (taskModel.getProcess() >= entry.getValue().getAchievementsNeed()) {
                    taskModel.setFinish(1);
                    ResponseContext.addFillCommon("taskRedPoint", builder -> builder.setTaskRedPoint(true));
                }
                tasks.remove(id);
                tasks.put(entry.getValue().getID(), taskModel);
                response.setDeleteTaskDtoId(id);
                id = entry.getValue().getID();

                isHaveNextAchieve = true;
                break;
            }
        }

        if (!isHaveNextAchieve) {
            taskModel.setReceive(1);
        }

        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        this.updateTasksToDB(TaskType.ACHIEVE, tasks, task);
        TaskDto taskDto = this.buildTaskDto(id, TaskType.ACHIEVE, taskModel);
        response.setUpdateTaskDto(taskDto);

//        logService.sendBasic(userId, RequestContext.getCommand(), userService.getIncreTransId(userId), type, id);
        response.setTasks(this.buildTasks(task, false));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<TaskActiveRewardAllResponse> taskActiveRewardAllAction(TaskActiveRewardAllRequest params) {
        int type = params.getType();
        long userId = RequestContext.getUserId();
        Task task = taskDao.getByUserId(userId);
        if (task == null) {
            log.error("task data is null, userId:{}", userId);
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }
        if (type <= 0 || type >= 3) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<List<Integer>> rewardConfig = Lists.newArrayList();

        long rewardLog = 0;
        if (type == TaskType.DAILY) {
            int active = task.getDailyActive();
            rewardLog = task.getDailyReward();

            Collection<DailyActiveEntity> dataMap = gameConfigManager.getTaskConfig().getDailyActive().values();
            for (DailyActiveEntity tempEntry : dataMap) {
                if (active < tempEntry.getRequirements()) {
                    continue;
                }

                if (MaskUtil.isTrue(rewardLog, tempEntry.getID())) {
                    continue;
                }
                rewardLog = MaskUtil.setMask(rewardLog, tempEntry.getID());
                rewardConfig.addAll(tempEntry.getReward());
            }
            task.setDailyReward(rewardLog);

        } else {
            int active = task.getWeeklyActive();
            rewardLog = task.getWeeklyReward();
            Collection<WeeklyActiveEntity> dataMap = gameConfigManager.getTaskConfig().getWeeklyActive().values();
            for (WeeklyActiveEntity tempEntry : dataMap) {
                if (active < tempEntry.getRequirements()) {
                    continue;
                }

                if (MaskUtil.isTrue(rewardLog, tempEntry.getID())) {
                    continue;
                }
                rewardLog = MaskUtil.setMask(rewardLog, tempEntry.getID());
                rewardConfig.addAll(tempEntry.getFixReward());
            }
            task.setWeeklyReward(rewardLog);
        }
        RewardResultSet rewardResultSet = null;
        if (!rewardConfig.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
            taskDao.update(task);
        }
//        logService.sendBasic(userId, RequestContext.getCommand(), userService.getIncreTransId(userId), type, id);
        TaskActiveRewardAllResponse.Builder response = TaskActiveRewardAllResponse.newBuilder();
        response.setType(type);
        response.setRewardLog(rewardLog);
        if (rewardResultSet != null) {
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        }
        response.setTasks(this.buildTasks(task, false));
        return Result.Success(response.build());
    }

    @Override
    public void updateTask(long userId, TaskProcess... taskProcesses) {
        Task task = this.getTaskData(userId);
        if (task == null) {
            log.error("task is null, userId:{}", userId);
            return;
        }

        boolean needUpdate = false;
        for (TaskProcess taskProcess : taskProcesses) {
            int taskType = taskProcess.getTaskType();
            int taskSubType = taskProcess.getTaskSubType();
            long count = taskProcess.getCount();
            Map<Integer, TaskModel> tasks = this.getTasks(taskType, task); // 查询任务数据
            for (Map.Entry<Integer, TaskModel> entry : tasks.entrySet()) {
                if (entry.getValue().getType() == taskSubType) {
                    int id = entry.getKey();
                    int[] taskConfigValues = this.getTaskConfigValues(taskType, id); // 查询任务配置
                    int completeCount = taskConfigValues[0];    // 条件目标值
                    int accumulationType = taskConfigValues[1]; // 是否累加

                    // 非成就任务 完成的不处理
                    if (taskType != TaskType.ACHIEVE && entry.getValue().getProcess() >= completeCount && entry.getValue().getFinish() == 1) {
                        break;
                    }

                    // 1. 计算总进度
                    long totalCount = entry.getValue().getProcess() + count;
                    // 非累加任务进度
                    if (accumulationType == 0) {
                        // 当前进度 大于 新的进度不更新
                        if (count <= entry.getValue().getProcess()) {
                            break;
                        }
                        totalCount = count;
                    }

                    boolean isAddUpdateTask = true;
                    if (entry.getValue().getFinish() == 1) {
                        isAddUpdateTask = false;
                    }

                    // 2. 完成任务
                    if (totalCount >= completeCount && entry.getValue().getReceive() == 0) {
                        if (taskType != TaskType.ACHIEVE) {
                            totalCount = completeCount;
                        }

                        if (entry.getValue().getFinish() == 0) {
                            entry.getValue().setFinish(1);
                        }
                        // 红点
                        ResponseContext.addFillCommon("taskRedPoint", builder -> builder.setTaskRedPoint(true));
                    }

                    // 3. 更新进度
                    entry.getValue().setProcess(totalCount);

                    needUpdate = true;

                    // 所有更新进度的任务数据
                    if (isAddUpdateTask) {
                        TaskDto taskDto = this.buildTaskDto(entry.getKey(), taskType, entry.getValue());
                        ResponseContext.addFillCommon("task_" + entry.getKey() + "_" + taskType, builder -> builder.addUpdateTasks(taskDto));
                    }

                    // 4. 更新任务数据
                    if (taskType == TaskType.DAILY) {
                        task.setDailyTasks(tasks);
                    } else if (taskType == TaskType.WEEKLY) {
                        task.setWeeklyTasks(tasks);
                    } else if (taskType == TaskType.ACHIEVE) {
                        task.setAchievements(tasks);
                    }

                    // 5. 记录任务变化
                    if ((RequestContext.getCommand() == MsgReqCommand.TaskActiveRewardAllRequest
                            || RequestContext.getCommand() == MsgReqCommand.TaskRewardDailyRequest
                            || RequestContext.getCommand() == MsgReqCommand.TaskRewardAchieveRequest) && entry.getValue().getReceive() == 0) {
                        ResponseContext.setTaskDoneInfo(taskType, id);
                    }
                    break;
                }
            }
        }
        if (needUpdate) {
            taskDao.update(task);
        }
    }

    @Override
    public void updateTask(long userId, List<TaskProcess> taskProcesses) {
        TaskProcess[] processes = new TaskProcess[taskProcesses.size()];
        taskProcesses.toArray(processes);
        this.updateTask(userId, processes);
    }

    @Override
    public void updateTask(long userId, RewardResultSet rewardResultSet) {
    }

    @Override
    public void quickFinishDayTask(long userId, int taskId, int process) {
        DailyTaskEntity taskConfig = gameConfigManager.getTaskConfig().getDailyTaskEntity(taskId);
        TaskProcess taskProcess = TaskProcess.valueOf(TaskType.DAILY, taskConfig.getDailyType(), process);
        this.updateTask(userId, taskProcess);
    }

    @Override
    public void quickFinishAchieveTask(long userId, int taskType, int process) {
        TaskProcess taskProcess = TaskProcess.valueOf(TaskType.ACHIEVE, taskType, process);
        this.updateTask(userId, taskProcess);
    }

    /**
     * 更新数据
     *
     * @param taskType
     * @param tasks
     * @param task
     */
    private void updateTasksToDB(int taskType, Map<Integer, TaskModel> tasks, Task task) {
        if (taskType == TaskType.DAILY) {
            task.setDailyTasks(tasks);
        } else if (taskType == TaskType.WEEKLY) {
            task.setWeeklyTasks(tasks);
        } else if (taskType == TaskType.ACHIEVE) {
            task.setAchievements(tasks);
        }
        taskDao.update(task);
    }

    /**
     * 获取任务数据
     *
     * @param userId
     * @return
     */
    private Task getTaskData(long userId) {
        Task task = taskDao.getByUserId(userId);
        if (task == null) {
            // 初始化数据
            task = this.initTaskData(userId);
        } else {
            // 检测是否有新增的任务配置项，跨天，跨周更新数据
            task = this.updateTaskData(task);
        }

        return task;
    }

    /**
     * 初始化任务数据
     *
     * @param userId
     * @return
     */
    private Task initTaskData(long userId) {
        Task task = new Task();
        task.setUserId(userId);

        // 每日任务
        task.setDailyTasks(this.getDailyTasks());
        task.setDailyActive((short) 0);
        task.setDailyReward(0L);
        task.setDailyTime(DateUtils.getSystemResetTime());

        // 每周任务
        task.setWeeklyTasks(this.getWeeklyTasks());
        task.setWeeklyActive((short) 0);
        task.setWeeklyReward(0L);
        task.setWeeklyTime(DateUtils.getSundayEndTimestamp());

        // 成就
        task.setAchievements(this.getAchieveTask());

        taskDao.update(task);
        return task;
    }

    private Task updateTaskData(Task task) {
        long dailyTime = task.getDailyTime();
        boolean needUpdate = false;
        if (DateUtils.getUnixTime() >= dailyTime) {
            // 更新每日任务
            needUpdate = true;
            task.setDailyActive((short) 0);
            task.setDailyReward(0L);
            task.setDailyTime(DateUtils.getSystemResetTime());
            task.setDailyTasks(this.getDailyTasks());
        }

        long weeklyTime = task.getWeeklyTime();
        if (DateUtils.getUnixTime() >= weeklyTime) {
            // 更新每周任务
            needUpdate = true;
            task.setWeeklyActive((short)0);
            task.setWeeklyReward(0L);
            task.setWeeklyTime(DateUtils.getSundayEndTimestamp());
//            task.setWeeklyTasks(this.getWeeklyTasks());
        }


        // 成就数据
        Map<Integer, TaskModel> achievementTasks = task.getAchievements();
        Map<Integer, TaskModel> newAchievements = new HashMap<>();

        // 是否有新增的成就类型
        Map<Integer, TaskModel> achieveTasks = this.getAchieveTask();
        if (achievementTasks.size() < achieveTasks.size()) {
            // 根据已有类型分组
            Map<Integer, Integer> typesMap = new HashMap<>();
            for (Map.Entry<Integer, TaskModel> entry : achievementTasks.entrySet()) {
                typesMap.put(entry.getValue().getType(), entry.getKey());
            }

            // 已有类型与数据表类型判断
            for (Map.Entry<Integer, TaskModel> entry : achieveTasks.entrySet()) {
                if (!typesMap.containsKey(entry.getValue().getType())) {
                    newAchievements.put(entry.getKey(), entry.getValue());
                }
            }
        }

        // 已完成的成就是否有新增的成就项条件
        Map<Integer, AchievementsEntity> achievementsEntityMap = gameConfigManager.getAchievementsConfig().getAchievements();

        Iterator<Map.Entry<Integer, TaskModel>> mapIter = achievementTasks.entrySet().iterator();

        while (mapIter.hasNext()) {
            Map.Entry<Integer, TaskModel> entry = mapIter.next();
            if (entry.getValue().getReceive() == 1 && entry.getValue().getFinish() == 1) {
                AchievementsEntity config = gameConfigManager.getAchievementsConfig().getAchievementsEntity(entry.getKey());
                if (config == null) {
                    log.error("getAchievementsConfig failed, id:{}", entry.getKey());
                    break;
                }
                for (Map.Entry<Integer, AchievementsEntity> configEntry : achievementsEntityMap.entrySet()) {
                    if (entry.getValue().getType() == configEntry.getValue().getAchievementsType()
                            && configEntry.getValue().getAchievementsLevel() == config.getAchievementsLevel() + 1) {
                        TaskModel taskModel = entry.getValue();
                        taskModel.setReceive(0);
                        if (taskModel.getProcess() < configEntry.getValue().getAchievementsNeed()) {
                            taskModel.setFinish(0);
                        }
                        newAchievements.put(configEntry.getValue().getID(), taskModel);
                        mapIter.remove();
                        break;
                    }
                }
            }
        }
        if (!newAchievements.isEmpty()) {
            achievementTasks.putAll(newAchievements);
            needUpdate = true;
            task.setAchievements(achievementTasks);
        }

        if (needUpdate) {
            taskDao.update(task);
        }
        return task;
    }

    /**
     * 日常任务
     *
     * @return
     */
    private Map<Integer, TaskModel> getDailyTasks() {
        Map<Integer, TaskModel> result = new HashMap<>();
        Map<Integer, DailyTaskEntity> dailyTasks = gameConfigManager.getTaskConfig().getDailyTask();
        for (Map.Entry<Integer, DailyTaskEntity> entry : dailyTasks.entrySet()) {
            int dailyType = entry.getValue().getDailyType();
            TaskModel taskModel = TaskModel.valueOf(dailyType);
            taskModel.setTaskType(TaskType.DAILY);
            result.put(entry.getKey(), taskModel);
        }
        return result;
    }

    /**
     * 周常任务
     *
     * @return
     */
    private Map<Integer, TaskModel> getWeeklyTasks() {
        Map<Integer, TaskModel> result = new HashMap<>();
//        Map<Integer, WeeklyTaskEntity> dailyTasks = gameConfigManager.getTaskConfig().getWeeklyTask();
//        for (Map.Entry<Integer, WeeklyTaskEntity> entry : dailyTasks.entrySet()) {
//            TaskModel taskModel = TaskModel.valueOf(entry.getValue().getWeeklyType());
//            result.put(entry.getKey(), taskModel);
//        }
        return result;
    }

    /**
     * 成就
     *
     * @return
     */
    private Map<Integer, TaskModel> getAchieveTask() {
        Map<Integer, TaskModel> result = new HashMap<>();
        Map<Integer, AchievementsEntity> achieves = gameConfigManager.getAchievementsConfig().getAchievements();

        // 成就
        List<Integer> contains = new ArrayList<>();
        for (Map.Entry<Integer, AchievementsEntity> entry : achieves.entrySet()) {
            int type = entry.getValue().getAchievementsType();
            if (contains.contains(type)) {
                continue;
            }
            contains.add(type);
            TaskModel taskModel = TaskModel.valueOf(type);
            taskModel.setTaskType(TaskType.ACHIEVE);
            result.put(entry.getKey(), taskModel);
        }
        return result;
    }

    private Map<Integer, TaskModel> getTasks(int taskType, Task task) {
        if (taskType == TaskType.DAILY) {
            return task.getDailyTasks();
        } else if (taskType == TaskType.WEEKLY) {
            return task.getWeeklyTasks();
        } else {
            return task.getAchievements();
        }
    }

    /**
     * 返回客户端的任务列表
     *
     * @param task
     * @param isGetInfo 是否是获取数据
     * @return
     */
    private Tasks buildTasks(Task task, boolean isGetInfo) {
        Tasks.Builder builder = Tasks.newBuilder();
        builder.addAllDailyTask(this.buildTaskDtoList(task, TaskType.DAILY, isGetInfo));
//        builder.addAllWeeklyTask(this.buildTaskDtoList(task, TaskType.WEEKLY, isGetInfo));
        builder.addAllAchievements(this.buildTaskDtoList(task, TaskType.ACHIEVE, isGetInfo));
        return builder.build();
    }

    /**
     * 构建返回客户端的任务列表
     *
     * @param task
     * @param taskType
     * @param isGetInfo
     * @return
     */
    private List<TaskDto> buildTaskDtoList(Task task, int taskType, boolean isGetInfo) {

        if (ResponseContext.getTaskData() != null) {
            task = (Task) ResponseContext.getTaskData();
        }

        Map<Integer, TaskModel> tasks;
        if (taskType == TaskType.DAILY) {
            tasks = task.getDailyTasks();
        } else if (taskType == TaskType.WEEKLY) {
            tasks = task.getWeeklyTasks();
        } else {
            tasks = task.getAchievements();
        }
        // 领取任务奖励时是否触发其他任务已完成
        Map<Integer, List<Integer>> taskDoneInfo = null;
        List<Integer> taskIds = null;
        if (!isGetInfo) {
            taskDoneInfo = ResponseContext.getTaskDoneInfo();
            if (taskDoneInfo != null) {
                taskIds = taskDoneInfo.get(taskType);
            }
        }
        List<TaskDto> result = new ArrayList<>(tasks.size());
        for (Map.Entry<Integer, TaskModel> entry : tasks.entrySet()) {
            if (!isGetInfo && (taskIds == null || !taskIds.contains(entry.getKey()))) {
                continue;
            }
//            if (taskType == TaskType.ACHIEVE && entry.getValue().getReceive() == 1) {
//                continue;
//            }
            TaskDto taskDto = this.buildTaskDto(entry.getKey(), taskType, entry.getValue());
            result.add(taskDto);
        }
        return result;
    }

    /**
     * 获取任务完成总进度
     *
     * @param type
     * @param id
     * @return
     */
    private int[] getTaskConfigValues(int type, int id) {
        int[] result = new int[2];
        if (type == TaskType.DAILY) {
            DailyTaskEntity config = gameConfigManager.getTaskConfig().getDailyTaskEntity(id);
            result[0] = config.getDailyNeed();
            result[1] = config.getAccumulationType();
        } else if (type == TaskType.WEEKLY) {
//            WeeklyTaskEntity config = gameConfigManager.getTaskConfig().getWeeklyTaskEntity(id);
//            result[0] = config.getWeeklyNeed();
//            result[1] = config.getAccumulationType();
        } else if (type == TaskType.ACHIEVE) {
            AchievementsEntity config = gameConfigManager.getAchievementsConfig().getAchievementsEntity(id);
            result[0] = config.getAchievementsNeed();
            result[1] = config.getAccumulationType();
        }
        return result;
    }

    private TaskDto buildTaskDto(int taskId, int taskType, TaskModel taskModel) {
        TaskDto.Builder builder = TaskDto.newBuilder();
        builder.setId(taskId);
        builder.setTaskType(taskType);
        builder.setIsFinish(taskModel.getFinish() != 0);
        builder.setIsReceive(taskModel.getReceive() != 0);
        builder.setProcess(taskModel.getProcess());
        return builder.build();
    }
}

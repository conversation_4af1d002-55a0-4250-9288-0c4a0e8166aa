package com.dxx.game.modules.activity.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.ActivityProto.*;
import com.dxx.game.modules.activity.model.EventTaskProcess;
import com.dxx.game.modules.task.model.TaskProcess;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface ActivityService {

    /**
     * 获取活动列表
     */
    Result<ActivityGetListResponse> getListAction(ActivityGetListRequest params);

    /**
     * 获取活动数据
     */
    Result<ActivityGetTaskResponse> getTaskAction(ActivityGetTaskRequest params);

    /**
     * 领取任务奖励
     */
    Result<ActivityTaskRewardResponse> taskRewardAction(ActivityTaskRewardRequest params);

    /**
     * 商店兑换道具
     */
    Result<ActivityShopExchangeResponse> shopExchangeAction(ActivityShopExchangeRequest params);

    /**
     * 获取商店数据
     */
    Result<ActivityGetShopResponse> getShopAction(ActivityGetShopRequest params);

    /**
     * 获取排行榜
     */
    Result<ActivityGetRankResponse> getRankAction(ActivityGetRankRequest params);

    /**
     * 更新通用消耗活动数据
     */
    void updateConsume(long userId, int eventType, int count);

    /**
     * 更新通用活动数据
     */
    void updateTask(long userId, EventTaskProcess... processes);

    /**
     * 检测商店是否可以购买
     */
    boolean checkShopCanBuy(long userId, String extraInfo);

    /**
     * 获取内购道具
     */
    List<List<Integer>> getShopIapReward(int activityId, int iapId);
}

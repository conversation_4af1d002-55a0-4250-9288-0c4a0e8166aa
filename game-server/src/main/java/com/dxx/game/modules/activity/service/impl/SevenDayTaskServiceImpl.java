package com.dxx.game.modules.activity.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.MaskUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.sevenday.SevenDayActiveRewardEntity;
import com.dxx.game.config.entity.sevenday.SevenDayTaskEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.TaskType;
import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.SevenDayTask;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.SevenDayTaskProto.*;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/17 5:33 下午
 */
@Service
@Slf4j
public class SevenDayTaskServiceImpl implements SevenDayTaskService {

    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private MailService mailService;
    @Resource
    private ActivityBaseDao.SevenDayTaskDao sevenDayTaskDao;

    @DynamoDBTransactional
    @Override
    public Result<SevenDayTaskGetInfoResponse> getInfoAction(SevenDayTaskGetInfoRequest params) {
        long userId = RequestContext.getUserId();
        SevenDayTaskGetInfoResponse.Builder response = SevenDayTaskGetInfoResponse.newBuilder();
        SevenDayDto sevenDayDto = this.getInfo(userId);
        if (sevenDayDto != null) {
            response.setSevenDayDto(sevenDayDto);
        }

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<SevenDayTaskRewardResponse> taskRewardAction(SevenDayTaskRewardRequest params) {
        long userId = RequestContext.getUserId();

        SevenDayTask sevenDayTask = sevenDayTaskDao.getByUserId(userId);
        if (sevenDayTask == null || sevenDayTask.getSevenDayTask() == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        int taskId = params.getTaskId();
        SevenDayTaskEntity taskEntity = gameConfigManager.getSevenDayConfig().getSevenDayTaskEntity(taskId);
        if (taskEntity == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        SevenDayTask.SevenDayTaskModel taskModel = sevenDayTask.getSevenDayTask();
        // 验证是否达成条件
        int taskType = taskEntity.getTaskType();
        int need = taskEntity.getNeed();
        if (need > taskModel.getTaskData().get(taskType)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        if (this.isTaskReceive(taskModel.getTaskLog(), taskEntity)) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }

        // 保存领取记录
        int day = taskEntity.getDay();
        int log = taskModel.getTaskLog().getOrDefault(day, 0);
        int order = taskId % 100;
        log = (int) MaskUtil.setMask(log, order);
        taskModel.getTaskLog().put(day, log);

        taskModel.setActive(taskModel.getActive() + taskEntity.getActive());

        // 发奖励
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, taskEntity.getReward());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        sevenDayTask.setSevenDayTask(taskModel);
        sevenDayTaskDao.update(sevenDayTask);

        List<CommonProto.SevenDayTaskDto> taskData = this.buildTaskData(taskModel, Set.of(taskType));
        ResponseContext.addFillCommon("sevendaytask", builder -> builder.addAllSevenDayTaskDto(taskData));

        SevenDayTaskRewardResponse.Builder response = SevenDayTaskRewardResponse.newBuilder();

        response.setActive(taskModel.getActive());
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(response.build());

    }

    @DynamoDBTransactional
    @Override
    public Result<SevenDayTaskActiveRewardResponse> activeRewardAction(SevenDayTaskActiveRewardRequest params) {
        long userId = RequestContext.getUserId();
        SevenDayTask sevenDayTask = sevenDayTaskDao.getByUserId(userId);
        if (sevenDayTask == null || sevenDayTask.getSevenDayTask() == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        SevenDayTask.SevenDayTaskModel taskModel = sevenDayTask.getSevenDayTask();

        int configId = params.getConfigId();
        // 奖励是否已领取
        if (MaskUtil.isTrue(taskModel.getActiveLog(), configId)) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }

        List<List<Integer>> rewardsConfig = null;

        SevenDayActiveRewardEntity activeRewardEntity = gameConfigManager.getSevenDayConfig().getSevenDayActiveRewardEntity(configId);
        if (activeRewardEntity == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (activeRewardEntity.getNeedActive() > taskModel.getActive()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        rewardsConfig = CollectionUtils.copyM(activeRewardEntity.getReward());


        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardsConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        taskModel.setActiveLog(MaskUtil.setMask(taskModel.getActiveLog(), configId));

        sevenDayTask.setSevenDayTask(taskModel);
        sevenDayTaskDao.update(sevenDayTask);

        SevenDayTaskActiveRewardResponse.Builder response = SevenDayTaskActiveRewardResponse.newBuilder();

        response.setActiveLog(taskModel.getActiveLog());
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(response.build());
    }

    @Override
    public SevenDayDto getInfo(long userId) {
        Activity activity = activityDao.getByUserId(userId);

        SevenDayTask sevenDayTask = activity.getSevenDayTask();
        if (sevenDayTask == null) {
            sevenDayTask = new SevenDayTask();
            sevenDayTask.setUserId(userId);
            activity.setSevenDayTask(sevenDayTask);
        }
        SevenDayTask.SevenDayTaskModel model = sevenDayTask.getSevenDayTask();
        if (model == null) {
            model = this.initData();
            sevenDayTask.setSevenDayTask(model);
            sevenDayTaskDao.update(sevenDayTask);
        } else {
            if (model.getEndTM() > 0 && DateUtils.getUnixTime() >= model.getEndTM()) {
                // 超过15天的数据清除掉
                if (!model.getTaskData().isEmpty() && DateUtils.getUnixTime() - model.getEndTM() >= 1) {
                    sendMail(userId, model);
                    model.getTaskData().clear();
                    model.getTaskLog().clear();
                    model.setCheckDayTM(0);
                    activity.getSevenDayTask().setSevenDayTask(model);
                    sevenDayTaskDao.update(sevenDayTask);
                }
                return null;
            }

            int maxDays = 7;
            if (model.getEndTM() > 0 && model.getCheckDayTM() != DateUtils.getTimeHour0InDay() && model.getDays() < maxDays) {
                int days = model.getDays() + (int) ((DateUtils.getTimeHour0InDay() - model.getCheckDayTM()) / 86400);
                if (days > maxDays) {
                    days = maxDays;
                }
                model.setDays(days);
                model.setCheckDayTM(DateUtils.getTimeHour0InDay());
                activity.getSevenDayTask().setSevenDayTask(model);
                sevenDayTaskDao.update(sevenDayTask);
            }
        }

        if (model.getEndTM() == 0) {
            long taskEndTM = DateUtils.getTimeHour0InDay() + 7 * 86400;
            long endTM = taskEndTM;
            model.setEndTM(endTM);
            model.setTaskEndTM(taskEndTM);
            model.setCheckDayTM(DateUtils.getTimeHour0InDay());
            activity.getSevenDayTask().setSevenDayTask(model);
            sevenDayTaskDao.update(sevenDayTask);
        }
        return this.buildDto(model);
    }

    private void sendMail(long userId, SevenDayTask.SevenDayTaskModel model) {
        Map<Integer, Long> taskData = model.getTaskData();
        Map<Integer, SevenDayTaskEntity> taskEntityMap = gameConfigManager.getSevenDayConfig().getSevenDayTask();
        List<List<Integer>> allRewards = Lists.newArrayList();
        taskEntityMap.forEach((key, value) -> {
            int taskType = value.getTaskType();
            if (this.isTaskReceive(model.getTaskLog(), value)) {
                return;
            }
            Long userValue = taskData.get(taskType);
            if (userValue == null) {
                return;
            }
            int target = value.getNeed();
            if (userValue < target) {
                return;
            }

            List<List<Integer>> tempReward = value.getReward();
            if (tempReward != null) {
                allRewards.addAll(tempReward.stream().filter(temp -> temp.get(0) != 6001).collect(Collectors.toList()));
            }

            // 保存领取记录
            int day = value.getDay();
            int log = model.getTaskLog().getOrDefault(day, 0);
            int order = value.getID() % 100;
            log = (int) MaskUtil.setMask(log, order);
            model.getTaskLog().put(day, log);
            model.setActive(model.getActive() + value.getActive());

        });
        ConcurrentSkipListMap<Integer, SevenDayActiveRewardEntity> activeConfigMap = gameConfigManager.getSevenDayConfig().getSevenDayActiveReward();
        activeConfigMap.forEach((key, value) -> {
            int configId = value.getID();
            // 奖励是否已领取
            if (MaskUtil.isTrue(model.getActiveLog(), configId)) {
                return;
            }
            if (value.getNeedActive() > model.getActive()) {
                return;
            }
            if (value.getReward() != null) {
                allRewards.addAll(value.getReward());
            }
            model.setActiveLog(MaskUtil.setMask(model.getActiveLog(), configId));
        });
        if(!allRewards.isEmpty()) {
            String postID = gameConfigManager.getMailTempId(4505, 4510);
            mailService.createMailAsync(userId, postID, new HashMap<>(), allRewards);
        }
    }

    @Override
    public void updateTask(long userId, SevenDayTaskProcess... processes) {
        SevenDayTask sevenDayTask = sevenDayTaskDao.getByUserId(userId);
        if (sevenDayTask == null || sevenDayTask.getSevenDayTask() == null) {
            return;
        }

        SevenDayTask.SevenDayTaskModel model = sevenDayTask.getSevenDayTask();

        boolean needUpdate = false;
        Set<Integer> changeSet = Sets.newHashSet();
        for (SevenDayTaskProcess taskProcess : processes) {

            int taskSubType = taskProcess.getTaskType();
            long count = taskProcess.getCount();
            Map<Integer, Long> tasks = model.getTaskData();
            if (tasks.get(taskSubType) == null) {
                continue;
            }
            if (gameConfigManager.getSevenDayTaskStatisticsTypes().get(taskSubType) == null) {
                continue;
            }
            int statisticsType = gameConfigManager.getSevenDayTaskStatisticsTypes().get(taskSubType);
            if (statisticsType == 0) {
                // 非累加
                long nowProcess = tasks.get(taskSubType);
                if (nowProcess >= count) {
                    continue;
                }
                tasks.put(taskSubType, count);
            } else {
                // 累加
                long totalCount = tasks.get(taskSubType) + count;
                tasks.put(taskSubType, totalCount);
            }
            changeSet.add(taskSubType);
            needUpdate = true;
        }
        if (needUpdate) {

            List<CommonProto.SevenDayTaskDto> taskData = this.buildTaskData(model, changeSet);
            ResponseContext.addFillCommon("sevendaytask", builder -> builder.addAllSevenDayTaskDto(taskData));

            sevenDayTaskDao.update(sevenDayTask);
        }
    }

    @Override
    public void resetCheckDayTM(long userId, long days) {
        SevenDayTask sevenDayTask = sevenDayTaskDao.getByUserId(userId);
        if (sevenDayTask == null || sevenDayTask.getSevenDayTask() == null) {
            return;
        }

        SevenDayTask.SevenDayTaskModel model = sevenDayTask.getSevenDayTask();
        model.setDays((int) (days - 1));
        model.setCheckDayTM(DateUtils.getTimeHour0InDay() - 86400);
        sevenDayTaskDao.update(sevenDayTask);
    }

    @Override
    public void updateActive(long userId, int active) {
        SevenDayTask sevenDayTask = sevenDayTaskDao.getByUserId(userId);
        if (sevenDayTask == null || sevenDayTask.getSevenDayTask() == null) {
            return;
        }
        SevenDayTask.SevenDayTaskModel model = sevenDayTask.getSevenDayTask();
        if (model == null) {
            log.error("task is null, userId:{}", userId);
            return;
        }
        model.setActive(active);
        sevenDayTaskDao.update(sevenDayTask);
    }

    @Override
    public void quickFinishSevenDayTask(long userId, int taskType, int process) {
        SevenDayTaskProcess taskProcess = SevenDayTaskProcess.valueOf(taskType, process);
        this.updateTask(userId, taskProcess);
    }

    /**
     * 获取任务完成总进度
     *
     * @param id
     * @return
     */
    private int[] getTaskConfigValues(int id) {
        int[] result = new int[2];
        SevenDayTaskEntity config = gameConfigManager.getSevenDayConfig().getSevenDayTaskEntity(id);
        result[0] = config.getNeed();
        result[1] = config.getStatisticsType();
        return result;
    }

    private SevenDayDto buildDto(SevenDayTask.SevenDayTaskModel model) {
        SevenDayDto.Builder builder = SevenDayDto.newBuilder();
        if (DateUtils.getUnixTime() > model.getEndTM()) {
            return null;
        }
        builder.setTaskEndTimestamp(model.getTaskEndTM());
        builder.setEndTimestamp(model.getEndTM());
        builder.setDays(model.getDays());
        builder.setActive(model.getActive());
        builder.setActiveLog(model.getActiveLog());

        List<CommonProto.SevenDayTaskDto> taskDtoList = buildTaskData(model, null);

        builder.addAllTasks(taskDtoList);
        return builder.build();
    }

    private List<CommonProto.SevenDayTaskDto> buildTaskData(SevenDayTask.SevenDayTaskModel model, Set<Integer> upList) {
        List<CommonProto.SevenDayTaskDto> taskDtoList = new ArrayList<>();
        Map<Integer, SevenDayTaskEntity> taskEntityMap = gameConfigManager.getSevenDayConfig().getSevenDayTask();
        for (Map.Entry<Integer, SevenDayTaskEntity> entry : taskEntityMap.entrySet()) {
            if (upList == null || upList.contains(entry.getValue().getTaskType())) {
                taskDtoList.add(this.buildTaskDto(entry.getValue(), model));
            }
        }
        return taskDtoList;
    }

    /**
     * 任务数据
     *
     * @param taskEntity
     * @param model
     * @return
     */
    private CommonProto.SevenDayTaskDto buildTaskDto(SevenDayTaskEntity taskEntity, SevenDayTask.SevenDayTaskModel model) {

        int taskType = taskEntity.getTaskType();
        int need = taskEntity.getNeed();

        long process = model.getTaskData().getOrDefault(taskType, 0L);

        CommonProto.SevenDayTaskDto.Builder taskBuilder = CommonProto.SevenDayTaskDto.newBuilder();
        taskBuilder.setId(taskEntity.getID());

        // 章节是通关条件
//        if (taskType == SevenDayTaskType.MAIN_CHAPTER_ORDER) {
//            if (process > need) {
//                taskBuilder.setIsFinish(true);
//            }
//        } else {
        if (process >= need) {
            taskBuilder.setIsFinish(true);
        }
//        }


        taskBuilder.setProcess(process);

        // 是否领取
        taskBuilder.setIsReceive(this.isTaskReceive(model.getTaskLog(), taskEntity));
        return taskBuilder.build();
    }

    /**
     * 任务是否已领取奖励
     *
     * @param taskLog
     * @param taskEntity
     * @return
     */
    private boolean isTaskReceive(Map<Integer, Integer> taskLog, SevenDayTaskEntity taskEntity) {
        int day = taskEntity.getDay();
        int taskOrder = taskEntity.getID() % 100;
        int log = taskLog.getOrDefault(day, 0);
        return MaskUtil.isTrue(log, taskOrder);

    }

    /**
     * 初始化数据
     *
     * @return
     */
    private SevenDayTask.SevenDayTaskModel initData() {
        SevenDayTask.SevenDayTaskModel model = new SevenDayTask.SevenDayTaskModel();
        model.setActive(0);
        model.setDays(1);
        model.setActiveLog(0);
        model.setCheckDayTM(DateUtils.getTimeHour0InDay());

        model.setTaskLog(new HashMap<>());

        Map<Integer, Long> taskData = new HashMap<>();
        int chapterId = userService.getChapterId(RequestContext.getUserId());
        Map<Integer, SevenDayTaskEntity> taskEntityMap = gameConfigManager.getSevenDayConfig().getSevenDayTask();
        for (Map.Entry<Integer, SevenDayTaskEntity> entry : taskEntityMap.entrySet()) {
            int taskType = entry.getValue().getTaskType();
            log.debug("------------TaskType:{} target:{}", taskType, entry.getValue().getNeed());
            if (!taskData.containsKey(taskType)) {
                if (taskType == TaskType.ACHIEVE_PROGRESS_CHAPTER) {
                    taskData.put(taskType, (long) (chapterId - 1 <= 0 ? 1 : chapterId - 1));
                } else {
                    taskData.put(taskType, 0L);
                }
            }
        }
        model.setTaskData(taskData);

        return model;
    }
}

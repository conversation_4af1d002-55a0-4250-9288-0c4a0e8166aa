package com.dxx.game.common.server.dispatcher;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.channel.apple.service.AppleService;
import com.dxx.game.common.channel.bilibili.BiliService;
import com.dxx.game.common.channel.uc.UCService;
import com.dxx.game.common.channel.wechatminigame.WeChatMiniGameCustomService;
import com.dxx.game.common.channel.wechatminigame.WeChatMiniGameJsApiService;
import com.dxx.game.common.channel.wechatminigame.WeChatMiniGameService;
import com.dxx.game.common.config.game.GameConfigSupport;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisLockContext;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.handler.HttpResponser;
import com.dxx.game.common.server.statistics.StatisticsService;
import com.dxx.game.modules.gm.service.GmService;
import com.dxx.game.modules.internal.service.InternalService;
import com.dxx.game.modules.pay.service.AndroidService;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.im.service.IMCallbackService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.dxx.game.common.log.DxxLogConsoleAppender.*;

@XRayEnabled
@Component
@Slf4j
public class ApiDispatcher {

	private static GmService gmService;

	private static GameConfigSupport configSupport;

	private static UCService ucService;

	private static AndroidService androidService;

	private static BiliService biliService;

	private static RedisLock redisLock;

	private static AppleService appleService;

	private static WeChatMiniGameService weChatMiniGameService;

	private static WeChatMiniGameCustomService weChatMiniGameCustomService;

	private static WeChatMiniGameJsApiService weChatMiniGameJsApiService;

	private static UserService userService;


	private static StatisticsService statisticsService;

	private static IMCallbackService imCallbackService;

	private static InternalService internalService;
	@Autowired
	public ApiDispatcher(GmService gmService, GameConfigSupport configSupport,
						 UCService ucService, AndroidService androidService,
						 RedisLock redisLock, BiliService biliService,
						 AppleService appleService,
                         WeChatMiniGameService weChatMiniGameService,
						 WeChatMiniGameCustomService weChatMiniGameCustomService,
						 WeChatMiniGameJsApiService weChatMiniGameJsApiService,
						 StatisticsService statisticsService,
						 IMCallbackService imCallbackService,
						 InternalService internalService) {
		ApiDispatcher.gmService = gmService;
		ApiDispatcher.configSupport = configSupport;
		ApiDispatcher.ucService = ucService;
		ApiDispatcher.androidService = androidService;
		ApiDispatcher.redisLock = redisLock;
		ApiDispatcher.biliService = biliService;
		ApiDispatcher.appleService = appleService;
		ApiDispatcher.weChatMiniGameService = weChatMiniGameService;
		ApiDispatcher.weChatMiniGameCustomService = weChatMiniGameCustomService;
		ApiDispatcher.weChatMiniGameJsApiService = weChatMiniGameJsApiService;
		ApiDispatcher.userService = userService;
		ApiDispatcher.statisticsService = statisticsService;
		ApiDispatcher.imCallbackService = imCallbackService;
		ApiDispatcher.internalService = internalService;
	}

	public static void dispatch(ChannelHandlerContext ctx, FullHttpRequest fullRequest,
								HttpHeaders headers, JSONObject params) {

		String uri = fullRequest.uri();
		MDC.put(REQUEST_PATH, uri);
		if(params != null){
			MDC.put(REQUEST_BODY, params.toJSONString());
		}

		Object respData = null;
		try {
			if (uri.equals("/gm")) {
				respData = gmService.doHandler(params, fullRequest);
			} else if (uri.equals("/internal/verifyAccessToken")) {
				respData = internalService.verifyAccessToken(params, fullRequest);
			} else if (uri.equals("/internal")) {
				respData = LogicDispatcher.internal(ctx, fullRequest, headers, params);
			} else if (uri.startsWith("/internal/im/callback")) {
				respData = imCallbackService.callback(params);
			} else if (uri.equals("/syncGameConfig")) {
				respData = configSupport.syncConfig(params);
			} else if (uri.equals("/reloadConfig")) {
				respData = configSupport.reloadConfig(params);
			} else if(uri.equals("/ucLogin")) {
				respData = ucService.login(params);
			} else if (uri.startsWith("/payCb")) {
				respData = androidService.payCb(fullRequest);
			} else if (uri.startsWith("/bili/cancelAccount")) {
				respData = biliService.cancelAccount(fullRequest);
			} else if (uri.startsWith("/apple/notify")) {
				respData = appleService.notification(params);
			} else if (uri.equals("/reloadDirtyWords")) {
				respData = configSupport.reloadDirtyWords(params);
			} else if (uri.equals("/setLogDebug")) {
				respData = configSupport.setLogLevelDebug(params);
			} else if (uri.equals("/setLogInfo")) {
				respData = configSupport.setLogLevelInfo(params);
			} else if (uri.equals("/weChatMiniGame/code2Session")) {//微信登录验证
				respData = weChatMiniGameService.code2Session(params);
			} else if(uri.startsWith("/wechat/custom_msg")) {//微信客服会话消息回调
				respData = weChatMiniGameCustomService.receiveCustomMsg(fullRequest);
			} else if (uri.startsWith("/wechat/jsapi_pay")) {//微信jsapi支付方式
				Map<String, String> jsApiResult = weChatMiniGameJsApiService.jsapiPay(fullRequest);
				if (jsApiResult.get("code").equals("0")) {
					HttpResponser.doSendForward(ctx, fullRequest, jsApiResult.get("url"));
				} else {
					HttpResponser.doSend(ctx, fullRequest, jsApiResult);
				}
				return;
			} else if (uri.contains("/MP_verify_") && uri.endsWith(".txt")) {//验证是否是正确的微信认证服务器
				String txt = uri.substring(uri.lastIndexOf("/MP_verify_") + "/MP_verify_".length(), uri.length() - ".txt".length());
				HttpResponser.doSendTxt(ctx, fullRequest, txt);
				return;
			}else if (uri.equals("/3699a7f5df6ccb5472ff2898100b02c3.txt")) {//申请解封用
				HttpResponser.doSendTxt(ctx, fullRequest,"e67adf8ac87ab29a74020642bf556e1155fd2278");
				return;
			} else if(uri.equals("/wechat/jsapi_check")){
				respData = weChatMiniGameJsApiService.checkOrderExist(params);
				HttpResponser.doSendWithCros(ctx, fullRequest, respData);
				return;
			} else if (uri.startsWith("/statistics/query")) {
				respData = statisticsService.queryData(fullRequest);
			} /*else if (uri.startsWith("/apple/test")) {
				appleService.testNotification();
			}*/
			if (respData == null) {
				FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.FORBIDDEN);
				HttpResponser.doSend(ctx, fullRequest, response);
			} else {
				HttpResponser.doSend(ctx, fullRequest, respData);
			}
		} catch (Exception e) {
			log.error("dispatch e:", e);
			FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.SERVICE_UNAVAILABLE);
			HttpResponser.doSend(ctx, fullRequest, response);
		} finally {
			String reqTime = MDC.get(REQUEST_TIME_IN_MS);
			if (reqTime != null) {
				long latency = System.currentTimeMillis() - Long.parseLong(reqTime);
				MDC.put(LATENCY_IN_MS, String.valueOf(latency));
			}

//			log.info("dispatch");
			RequestContext.clear();
			ResponseContext.clear();
			redisLock.unlock();
			RedisLockContext.clear();
		}
	}
}

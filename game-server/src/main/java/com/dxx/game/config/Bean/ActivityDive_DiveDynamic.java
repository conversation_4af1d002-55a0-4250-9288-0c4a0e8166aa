
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityDive_DiveDynamic {
    public ActivityDive_DiveDynamic(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("ComsumRange").getAsJsonArray(); ComsumRange = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  ComsumRange.add(_v0); }   }
        MinDepth = _buf.get("MinDepth").getAsInt();
        MaxDepth = _buf.get("MaxDepth").getAsInt();
        MinItem = _buf.get("MinItem").getAsInt();
        MaxItem = _buf.get("MaxItem").getAsInt();
    }

    public static ActivityDive_DiveDynamic deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityDive_DiveDynamic(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 累计消耗水母范围
     */
    public final java.util.List<Integer> ComsumRange;
    /**
     * 最小深度
     */
    public final int MinDepth;
    /**
     * 最大深度
     */
    public final int MaxDepth;
    /**
     * 最小奖励
     */
    public final int MinItem;
    /**
     * 最大奖励
     */
    public final int MaxItem;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "ComsumRange:" + ComsumRange + ","
        + "MinDepth:" + MinDepth + ","
        + "MaxDepth:" + MaxDepth + ","
        + "MinItem:" + MinItem + ","
        + "MaxItem:" + MaxItem + ","
        + "}";
    }
}


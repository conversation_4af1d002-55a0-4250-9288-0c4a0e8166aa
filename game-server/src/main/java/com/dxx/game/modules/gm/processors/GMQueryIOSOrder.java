package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.apple.service.AppleService;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.consts.GMCommandType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.QUERY_IOS_ORDER_BY_ID)
public class GMQueryIOSOrder extends AbstractGMProcessor {

    @Resource
    private AppleService appleService;

    @Override
    protected Object execute(JSONObject params) {
        String orderId = params.getString("orderId");
        orderId = orderId.trim();
        Map<String, Object> result = new HashMap<>();
        List<Map<String, String>> orderInfo = appleService.queryOrderById(orderId);
        if (orderInfo == null || orderInfo.isEmpty()) {
            result.put("msg", "未查询到订单");
        } else {
            result.put("orderList", orderInfo);
        }
        return this.success(result);
    }
}

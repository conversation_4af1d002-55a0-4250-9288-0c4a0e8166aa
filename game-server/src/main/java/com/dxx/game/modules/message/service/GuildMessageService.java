package com.dxx.game.modules.message.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildMessage;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildMessageDao;
import com.dxx.game.dto.GuildProto;
import com.dxx.game.modules.im.service.IMService;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;
import com.dxx.game.dto.IMProto.MessageType;

/**
 * @authoer: lsc
 * @createDate: 2023/4/12
 * @description:
 */
@Slf4j
@Service
public class GuildMessageService {

    @Resource
    private MessageService messageService;
    @Resource
    private UserDao userDao;
    @Resource
    private GuildMessageDao guildMessageDao;
    @Resource
    private IMService imService;

    /**
     * 自己成功加入公会(提交审批的)
     * messageType:101, messageContent:{"guildId":10081,"guildName":"测试222"}
     */
    public void pushJoinSuccess(long guildId, String guildName, List<Long> toUserIds) {
        // 自己成功加入公会(提交审批的) messageType = 101
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", messageService.generateGuildMsgId(guildId));
        jsonObject.put("guildId", guildId);      // 公会ID
        jsonObject.put("guildName", guildName);  // 公会名称
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        imService.batchSendPersonalMsg(MessageType.GUILD_JOIN_SUCCESS, toUserIds, jsonObject.toJSONString());
    }

    /**
     * 有人加入公会
     * messageType:102, messageContent:{"joinUsers":[{"weekActive":70,"level":1,"joinTime":"1681298006","nickName":"wowowo","chapterId":2,"activeTime":"1681297997","avatarFrame":2,"avatar":1,"position":4,"userId":"10001695","dailyActive":30}]}
     */
    public void pushUserJoin(long guildId, List<GuildProto.GuildMemberInfoDto> joinUsers) {

        JSONArray jsonArray = new JSONArray();
        try {
            for (GuildProto.GuildMemberInfoDto joinUser : joinUsers) {
                String jsonString = JsonFormat.printer().print(joinUser);
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                if (!jsonObject.containsKey("nickName")) {
                    jsonObject.put("nickName", "");
                }
                if (!jsonObject.containsKey("avatar")) {
                    jsonObject.put("avatar", 0);
                }
                if (!jsonObject.containsKey("avatarFrame")) {
                    jsonObject.put("avatarFrame", 0);
                }
                jsonArray.add(jsonObject);
            }
        } catch (Exception e) {
            log.error("parse GuildMemberInfoDto to JSONObject failed, e:", e);
        }

        if (jsonArray.isEmpty()) {
            return;
        }

        long msgId = messageService.generateGuildMsgId(guildId);
        // 有人加入公会(计算公会人数) messageType = 102
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("joinUsers", jsonArray);     // 加入用户数据w[GuildMemberInfoDto] 数组
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        String messageContent = jsonObject.toJSONString();
        imService.sendGuildGroupMsg(MessageType.GUILD_USER_JOIN, guildId, messageContent, true);

//        guildMessageDao.addMessageRecord(guildId, msgId, MessageType.GUILD_USER_JOIN, messageContent);
    }

    /**
     * 有人退出公会
     * messageType:103, messageContent:{"leaveUserNickName":"wowowo","leaveUserId":10001695}
     */
    public void pushUserLeave(long guildId, long leaveUserId) {

        User user = userDao.getByUserId(leaveUserId);
        String leaveUserNickName = Optional.ofNullable(user.getNickName()).orElse("");

        long msgId = messageService.generateGuildMsgId(guildId);
        // 有人退出公会(计算公会人数) messageType = 103
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("leaveUserId", leaveUserId);             // 退出用户ID
        jsonObject.put("leaveUserNickName", leaveUserNickName); // 退出用户昵称
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        String messageContent = jsonObject.toJSONString();
        imService.sendGuildGroupMsg(MessageType.GUILD_USER_LEAVE, guildId, messageContent, true);

        // 保存消息记录
//        guildMessageDao.addMessageRecord(guildId, msgId, MessageType.GUILD_USER_LEAVE, messageContent);
    }

    /**
     * 职位变动
     * messageType:104, messageContent:{"msgId":200,"fromUserId":10001723,"fromUserNickName":"","fromUserPosition":1,"oldPosition":2,"upPosition":3,"toUserId":10001696,"toUserNickName":"","timestamp":1682583453}
     */
    public void pushPositionChange(long guildId, long toUserId, long fromUserId, int fromUserPosition, int oldPosition, int upPosition) {
        User fromUser = userDao.getByUserId(fromUserId);
        String fromUserNickName = Optional.ofNullable(fromUser.getNickName()).orElse("");
        User toUser = userDao.getByUserId(toUserId);
        String toUserNickName = Optional.ofNullable(toUser.getNickName()).orElse("");
        // 自己职位变动 messageType = 104
        long msgId = messageService.generateGuildMsgId(guildId);
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("fromUserId", fromUserId);               // 发起人用户ID
        jsonObject.put("fromUserNickName", fromUserNickName);   // 发起人昵称
        jsonObject.put("fromUserPosition", fromUserPosition);   // 发起人职位
        jsonObject.put("oldPosition", oldPosition);             // 原职位
        jsonObject.put("upPosition", upPosition);               // 现职位
        jsonObject.put("toUserId", toUserId);
        jsonObject.put("toUserNickName", toUserNickName);
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        String messageContent = jsonObject.toJSONString();
        imService.sendGuildGroupMsg(MessageType.GUILD_POSITION_CHANGE, guildId, messageContent, true);

        // 保存消息记录
//        guildMessageDao.addMessageRecord(guildId, msgId, MessageType.GUILD_POSITION_CHANGE, messageContent);
    }

    /**
     * 自己被踢出公会
     * messageType:105, messageContent:{"fromUserId":10001696,"fromUserNickName":"","fromUserPosition":1}
     */
    public void pushKickedOut(long guildId, long toUserId, long fromUserId, long fromUserPosition) {
        User fromUser = userDao.getByUserId(fromUserId);
        String fromUserNickName = Optional.ofNullable(fromUser.getNickName()).orElse("");

        // 自己被踢出公会 messageType = 105
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", messageService.generateGuildMsgId(guildId));
        jsonObject.put("fromUserId", fromUserId);               // 发起人用户ID
        jsonObject.put("fromUserNickName", fromUserNickName);   // 发起人昵称
        jsonObject.put("fromUserPosition", fromUserPosition);   // 发起人职位
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        imService.sendPersonalMsg(MessageType.GUILD_BE_KICKED_OUT, toUserId, jsonObject.toJSONString());
    }

    /**
     * 有人被踢出公会
     * messageType:106, messageContent:{"beKickedUserNickName":"wowowo","beKickedUserId":10001695,"fromUserId":10001696,"fromUserNickName":"","fromUserPosition":1}
     */
    public void pushUserBeKickedOut(long guildId, long beKickedUserId, long fromUserId, long fromUserPosition) {
        User fromUser = userDao.getByUserId(fromUserId);
        String fromUserNickName = Optional.ofNullable(fromUser.getNickName()).orElse("");

        User beKickedUser = userDao.getByUserId(beKickedUserId);
        String beKickedUserNickName = Optional.ofNullable(beKickedUser.getNickName()).orElse("");

        long msgId = messageService.generateGuildMsgId(guildId);

        // 有人被踢出公会(计算公会人数) messageType = 106
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("beKickedUserId", beKickedUserId);
        jsonObject.put("beKickedUserNickName", beKickedUserNickName);       // 被踢人用户ID
        jsonObject.put("fromUserId", fromUserId);                           // 被踢人用户昵称
        jsonObject.put("fromUserNickName", fromUserNickName);               // 发起人昵称
        jsonObject.put("fromUserPosition", fromUserPosition);               // 发起人职位
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        String messageContent = jsonObject.toJSONString();
        imService.sendGuildGroupMsg(MessageType.GUILD_USER_BE_KICKED_OUT, guildId, messageContent, true);

        // 保存消息记录
//        guildMessageDao.addMessageRecord(guildId, msgId, MessageType.GUILD_USER_BE_KICKED_OUT, messageContent);
    }

    /**
     * 公会信息修改
     * messageType:107, messageContent:{"guildIcon":1001,"guildNotice":"333","guildIconBg":2001,"guildName":"为什么不合法"}
     */
    public void pushGuildInfoModify(Guild guild) {
        long guildId = guild.getGuildId();
        // 公会信息修改 messageType = 107
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", messageService.generateGuildMsgId(guildId));
        jsonObject.put("guildName", guild.getGuildName());          // 公会昵称
        jsonObject.put("guildIcon", guild.getGuildIcon());          // 公会图标
        jsonObject.put("guildIconBg", guild.getGuildIconBg());      // 公会图标背景
        jsonObject.put("guildNotice", guild.getGuildNotice());      // 公会公告
        jsonObject.put("guildIntro", guild.getGuildIntro());
        jsonObject.put("guildExp", guild.getGuildExp());
        jsonObject.put("guildLevel", guild.getGuildLevel());
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        imService.sendGuildGroupMsg(MessageType.GUILD_INFO_MODIFY, guildId, jsonObject.toJSONString());
    }

    // 推送申请数据
    public void publishApplyJoin(long guildId, List<Long> toUserList) {
        if (toUserList == null || toUserList.isEmpty()) {
            return;
        }

        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("guildId", guildId);
        jsonObject.put("timestamp", DateUtils.getUnixTime());
        imService.batchSendPersonalMsg(MessageType.GUILD_APPLY_JOIN, toUserList, jsonObject.toJSONString());
    }

    // 推送捐赠数据
    public void publishDonation(long guildId, long userId, GuildMessage.RequestItemModel requestItemModel) {
        // 公会信息修改 messageType = 108
        String content = requestItemModel.toJsonString();
        imService.sendGuildGroupMsg(MessageType.GUILD_DONATION, guildId, content, true);
//        guildMessageDao.addDonation(guildId, userId, requestItemModel.getMsgId(), MessageType.GUILD_DONATION, requestItemModel);
    }

    // 推送捐赠道具数量变化
    public void publishDonationChangeCount(long guildId, long msgId, int count) {
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("count", count);
        imService.sendGuildGroupMsg(MessageType.GUILD_DONATION_CHANGE_ITEM_COUNT, guildId, jsonObject.toJSONString());
    }

    // 推送捐赠删除数据
    public void publishDonationDelete(long guildId, List<Long> msgIds) {
        if (msgIds == null || msgIds.isEmpty()) {
            return;
        }
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("delMsgId", msgIds);

        imService.sendGuildGroupMsg(MessageType.GUILD_DONATION_DELETE, guildId, jsonObject.toJSONString());
    }

    // 推送申请数据
    public void publishDonationApply(long guildId, int totalCountList, List<GuildProto.GuildMemberInfoDto> userDataList, List<Long> toUserList) {
        if (toUserList == null || toUserList.isEmpty()) {
            return;
        }

        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("guildId", guildId);
        jsonObject.put("totalCount", totalCountList);
        JSONArray jsonArray = new JSONArray();
        userDataList.forEach(userData -> {
            if (userData != null) {
                JSONObject tempObj = new JSONObject(true);
                tempObj.put("userId", userData.getUserId());
                tempObj.put("nickName", userData.getNickName());
                tempObj.put("avatar", userData.getAvatar());
                tempObj.put("avatarFrame", userData.getAvatarFrame());
                jsonArray.add(tempObj);
            }
        });
        jsonObject.put("timestamp", DateUtils.getUnixTime());
        jsonObject.put("dataList", jsonArray);

        imService.batchSendPersonalMsg(MessageType.GUILD_APPLY_JOIN, toUserList, jsonObject.toJSONString());
    }

    public void publishTransferPresidentByLoginTime(long guildId, long transferUserId, String newPresidentUserName) {

        long msgId = messageService.generateGuildMsgId(guildId);
        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("msgId", msgId);
        jsonObject.put("newPresidentUserId", transferUserId);
        jsonObject.put("newPresidentUserName", newPresidentUserName);
        jsonObject.put("timestamp", DateUtils.getUnixTime());

        String messageContent = jsonObject.toJSONString();
        imService.sendGuildGroupMsg(MessageType.GUILD_CHECK_TRANSFER_PRESIDENT, guildId, jsonObject.toJSONString(), true);

        // 保存消息记录
//        guildMessageDao.addMessageRecord(guildId, msgId, MessageType.GUILD_CHECK_TRANSFER_PRESIDENT, messageContent);
    }
}

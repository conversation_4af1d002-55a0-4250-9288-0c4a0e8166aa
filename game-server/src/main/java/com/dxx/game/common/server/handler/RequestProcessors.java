package com.dxx.game.common.server.handler;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTransactionScannerRegistrar;
import com.dxx.game.common.server.protocol.MessageProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.ReflectionUtils.MethodCallback;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;

/**
 * 请求处理器集合
 * <AUTHOR>
 * @since 2019年12月5日
 */
@Component
public class RequestProcessors implements BeanPostProcessor {


	private static final Logger logger = LoggerFactory.getLogger(RequestProcessors.class);

	/**
	 * 请求处理器集合 {cmd：RequestProcessor}
	 */
	private final  ConcurrentMap<Short, RequestProcessor> processorMap = new ConcurrentHashMap<Short, RequestProcessor>();

	/**
	 * 获取接口方法信息
	 */
	public Object postProcessAfterInitialization(final Object bean, String beanName) {
		ApiHandler apiHandler = bean.getClass().getAnnotation(ApiHandler.class);
		if (apiHandler == null) {
			return bean;
		}
		ReflectionUtils.doWithMethods(AopUtils.getTargetClass(bean), new MethodCallback() {
			public void doWith(Method method) throws IllegalArgumentException,
					IllegalAccessException {

				ApiMethod annotation = AnnotationUtils.getAnnotation(method, ApiMethod.class);
				if (annotation == null) {
					return;
				}
				method.setAccessible(true);
				RequestProcessor requestProcessor
						= new RequestProcessor(annotation.command(), annotation.name(), method, bean, annotation);
				registerProcessor(requestProcessor);
			}
		});
		return bean;
	}

	/**
	 * 注册请求处理器
	 * @param processor RequestProcessor
	 */
	public void registerProcessor(RequestProcessor processor) {
		if (processor == null) {
			logger.error("请求处理器 is null");
			return;
		}

		short command = processor.getCommand();

		RequestProcessor existsProcess = this.processorMap.put(command, processor);
		if (existsProcess != null) {
			logger.error("请求处理器[command : {}, name : {}, method : {}.{}]被覆盖",
					command, processor.getName(), processor.getClassObj().getClass().getName(), processor.getMethod().getName());
			throw new RuntimeException("请求处理器被覆盖");
		}

		logger.info("registerProcessor[cmd: {}, name :{}, method: {}.{}]",
				command, processor.getName(), processor.getClassObj().getClass().getName(), processor.getMethod().getName());
	}

	/**
	 * 取得请求处理器
	 * @param command 命令ID
	 * @return RequestProcessor
	 */
	public RequestProcessor getProcessor(short command) {
		RequestProcessor processor = null;
		processor = this.processorMap.get(command);
		return processor;
	}
}

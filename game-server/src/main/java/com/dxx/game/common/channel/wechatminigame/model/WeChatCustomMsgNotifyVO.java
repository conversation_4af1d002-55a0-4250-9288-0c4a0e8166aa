package com.dxx.game.common.channel.wechatminigame.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @author: lsc
 * @createDate: 2023/8/21
 * @description:
 */
@Data
public class WeChatCustomMsgNotifyVO {

    // 小程序的原始ID
    @JSONField(name = "ToUserName")
    private String toUserName;
    // 发送者的openid
    @JSONField(name = "FromUserName")
    private String fromUserName;
    // 消息创建时间(整型）
    @JSONField(name = "CreateTime")
    private long createTime;
    // miniprogrampage
    @JSONField(name = "MsgType")
    private String msgType;
    // 消息id，64位整型
    @JSONField(name = "MsgId")
    private long msgId;
    @JSONField(name = "Event")
    private String event;
    // 标题
    @JSONField(name = "Title")
    private String title;
    // 小程序appid
    @JSONField(name = "AppId")
    private String appId;
    // 小程序页面路径
    @JSONField(name = "PagePath")
    private String pagePath;
    // 封面图片的临时cdn链接
    @JSONField(name = "ThumbUrl")
    private String thumbUrl;
    // 封面图片的临时素材id
    @JSONField(name = "ThumbMediaId")
    private String thumbMediaId;
    @JSONField(name = "SessionFrom")
    private String sessionFrom;

    @JSONField(name = "MiniGame")
    private MiniGame miniGame;

    @Data
    public static class MiniGame {
        private String OrderId;
        private int IsPreview;
        private String ToUserOpenid;
        private int Zone;
        private int GiftTypeId;
        private String GiftId;
        private int SendTime;
        private JSONArray GoodsList;
    }
}

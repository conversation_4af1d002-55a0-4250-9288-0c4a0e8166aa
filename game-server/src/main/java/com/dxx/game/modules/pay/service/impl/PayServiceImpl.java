package com.dxx.game.modules.pay.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBWriteType;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.channel.apple.service.AppleService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.google.GoogleService;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.iap.*;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.IapType;
import com.dxx.game.dao.dynamodb.model.*;
import com.dxx.game.dao.dynamodb.repository.*;
import com.dxx.game.dao.redis.AndroidPayCbResultRedisDao;
import com.dxx.game.dto.PayProto.*;
import com.dxx.game.modules.activity.service.ActivityService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.pay.model.IAPInfoModel;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.pay.support.PaySupport;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.shop.support.ShopSupport;
import com.dxx.game.modules.user.service.UserService;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;


@Service
@Slf4j
public class PayServiceImpl implements PayService {

    @Resource
    AndroidPayCbResultRedisDao androidPayCbResultRedisDao;
    @Resource
    private RechargeOrderDao rechargeOrderDao;
    @Resource
    private UserService userService;
    @Resource
    private RedisLock redisLock;
    @Resource
    private RewardService rewardService;
    @Resource
    private UserDao userDao;
    @Resource
    private GoogleService googleService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private AppleService appleService;
    @Resource
    private PreRechargeOrderDao preRechargeOrderDao;
    @Resource
    private ShopDao shopDao;
    @Resource
    private ActivityService activityService;
    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private ShopSupport shopSupport;
    @Resource
    private PaySupport paySupport;

    /**
     * 内购验单并发货
     */
    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION_ENHANCED)
    @Override
    public Result<PayInAppPurchaseResponse> inAppPurchase(PayInAppPurchaseRequest params) {

        String receiptData = params.getReceiptData();
        ChannelID channelID = ChannelID.valueOf(params.getChannelId());

        long userId = RequestContext.getUserId();

//		MIIT3wYJKoZIhvcNAQcCoIIT0DCCE8wCAQExCzAJBgUrDgMCGgUAMIIDgAYJKoZIhvcNAQcBoIIDcQSCA20xggNpMAoCAQgCAQEEAhYAMAoCARQCAQEEAgwAMAsCAQECAQEEAwIBADALAgEDAgEBBAMMATMwCwIBCwIBAQQDAgEAMAsCAQ8CAQEEAwIBADALAgEQAgEBBAMCAQAwCwIBGQIBAQQDAgEDMAwCAQoCAQEEBBYCNCswDAIBDgIBAQQEAgIAnzANAgENAgEBBAUCAwH8mzANAgETAgEBBAUMAzEuMDAOAgEJAgEBBAYCBFAyNTMwGAIBBAIBAgQQPPnvqqZEMKCJooKgYmLXazAbAgEAAgEBBBMMEVByb2R1Y3Rpb25TYW5kYm94MBsCAQICAQEEEwwRY29tLmhhYmJ5LmFyY2hlcm8wHAIBBQIBAQQUGJ8xSV8ZrqStBdG6qg3YjSBP9X4wHgIBDAIBAQQWFhQyMDIwLTAyLTA1VDA4OjE4OjQyWjAeAgESAgEBBBYWFDIwMTMtMDgtMDFUMDc6MDA6MDBaMEwCAQYCAQEERMO/ta7UigeZk3fblLnEP1vQWeDqIEH0FC3GrTCoE+LWmDwcmJ43ZZNAHObskRw6Zhvzt5j8KCVL5VeS/YyAZNUPfvsOMFECAQcCAQEESefVZdz18I5N+ENYCqfhdAQkR3gy/8tF8xiWVE6T4/Popu5CQnOyz4lLdc8r1wGe1YcocXk6xh3PZVS2igu/TCWGRSF20mQ/dDcwggFiAgERAgEBBIIBWDGCAVQwCwICBqwCAQEEAhYAMAsCAgatAgEBBAIMADALAgIGsAIBAQQCFgAwCwICBrICAQEEAgwAMAsCAgazAgEBBAIMADALAgIGtAIBAQQCDAAwCwICBrUCAQEEAgwAMAsCAga2AgEBBAIMADAMAgIGpQIBAQQDAgEBMAwCAgarAgEBBAMCAQEwDAICBq4CAQEEAwIBADAMAgIGrwIBAQQDAgEAMAwCAgaxAgEBBAMCAQAwGwICBqcCAQEEEgwQMTAwMDAwMDYyMzE0OTU3MzAbAgIGqQIBAQQSDBAxMDAwMDAwNjIzMTQ5NTczMB8CAgaoAgEBBBYWFDIwMjAtMDItMDVUMDg6MTg6NDJaMB8CAgaqAgEBBBYWFDIwMjAtMDItMDVUMDg6MTg6NDJaMCgCAgamAgEBBB8MHWNvbS5oYWJieS5hcmNoZXJvX2Rpc2NvdW50MTA1oIIOZTCCBXwwggRkoAMCAQICCA7rV4fnngmNMA0GCSqGSIb3DQEBBQUAMIGWMQswCQYDVQQGEwJVUzETMBEGA1UECgwKQXBwbGUgSW5jLjEsMCoGA1UECwwjQXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMxRDBCBgNVBAMMO0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MB4XDTE1MTExMzAyMTUwOVoXDTIzMDIwNzIxNDg0N1owgYkxNzA1BgNVBAMMLk1hYyBBcHAgU3RvcmUgYW5kIGlUdW5lcyBTdG9yZSBSZWNlaXB0IFNpZ25pbmcxLDAqBgNVBAsMI0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zMRMwEQYDVQQKDApBcHBsZSBJbmMuMQswCQYDVQQGEwJVUzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKXPgf0looFb1oftI9ozHI7iI8ClxCbLPcaf7EoNVYb/pALXl8o5VG19f7JUGJ3ELFJxjmR7gs6JuknWCOW0iHHPP1tGLsbEHbgDqViiBD4heNXbt9COEo2DTFsqaDeTwvK9HsTSoQxKWFKrEuPt3R+YFZA1LcLMEsqNSIH3WHhUa+iMMTYfSgYMR1TzN5C4spKJfV+khUrhwJzguqS7gpdj9CuTwf0+b8rB9Typj1IawCUKdg7e/pn+/8Jr9VterHNRSQhWicxDkMyOgQLQoJe2XLGhaWmHkBBoJiY5uB0Qc7AKXcVz0N92O9gt2Yge4+wHz+KO0NP6JlWB7+IDSSMCAwEAAaOCAdcwggHTMD8GCCsGAQUFBwEBBDMwMTAvBggrBgEFBQcwAYYjaHR0cDovL29jc3AuYXBwbGUuY29tL29jc3AwMy13d2RyMDQwHQYDVR0OBBYEFJGknPzEdrefoIr0TfWPNl3tKwSFMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAUiCcXCam2GGCL7Ou69kdZxVJUo7cwggEeBgNVHSAEggEVMIIBETCCAQ0GCiqGSIb3Y2QFBgEwgf4wgcMGCCsGAQUFBwICMIG2DIGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wNgYIKwYBBQUHAgEWKmh0dHA6Ly93d3cuYXBwbGUuY29tL2NlcnRpZmljYXRlYXV0aG9yaXR5LzAOBgNVHQ8BAf8EBAMCB4AwEAYKKoZIhvdjZAYLAQQCBQAwDQYJKoZIhvcNAQEFBQADggEBAA2mG9MuPeNbKwduQpZs0+iMQzCCX+Bc0Y2+vQ+9GvwlktuMhcOAWd/j4tcuBRSsDdu2uP78NS58y60Xa45/H+R3ubFnlbQTXqYZhnb4WiCV52OMD3P86O3GH66Z+GVIXKDgKDrAEDctuaAEOR9zucgF/fLefxoqKm4rAfygIFzZ630npjP49ZjgvkTbsUxn/G4KT8niBqjSl/OnjmtRolqEdWXRFgRi48Ff9Qipz2jZkgDJwYyz+I0AZLpYYMB8r491ymm5WyrWHWhumEL1TKc3GZvMOxx6GUPzo22/SGAGDDaSK+zeGLUR2i0j0I78oGmcFxuegHs5R0UwYS/HE6gwggQiMIIDCqADAgECAggB3rzEOW2gEDANBgkqhkiG9w0BAQUFADBiMQswCQYDVQQGEwJVUzETMBEGA1UEChMKQXBwbGUgSW5jLjEmMCQGA1UECxMdQXBwbGUgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxFjAUBgNVBAMTDUFwcGxlIFJvb3QgQ0EwHhcNMTMwMjA3MjE0ODQ3WhcNMjMwMjA3MjE0ODQ3WjCBljELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xLDAqBgNVBAsMI0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zMUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMo4VKbLVqrIJDlI6Yzu7F+4fyaRvDRTes58Y4Bhd2RepQcjtjn+UC0VVlhwLX7EbsFKhT4v8N6EGqFXya97GP9q+hUSSRUIGayq2yoy7ZZjaFIVPYyK7L9rGJXgA6wBfZcFZ84OhZU3au0Jtq5nzVFkn8Zc0bxXbmc1gHY2pIeBbjiP2CsVTnsl2Fq/ToPBjdKT1RpxtWCcnTNOVfkSWAyGuBYNweV3RY1QSLorLeSUheHoxJ3GaKWwo/xnfnC6AllLd0KRObn1zeFM78A7SIym5SFd/Wpqu6cWNWDS5q3zRinJ6MOL6XnAamFnFbLw/eVovGJfbs+Z3e8bY/6SZasCAwEAAaOBpjCBozAdBgNVHQ4EFgQUiCcXCam2GGCL7Ou69kdZxVJUo7cwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjAuBgNVHR8EJzAlMCOgIaAfhh1odHRwOi8vY3JsLmFwcGxlLmNvbS9yb290LmNybDAOBgNVHQ8BAf8EBAMCAYYwEAYKKoZIhvdjZAYCAQQCBQAwDQYJKoZIhvcNAQEFBQADggEBAE/P71m+LPWybC+P7hOHMugFNahui33JaQy52Re8dyzUZ+L9mm06WVzfgwG9sq4qYXKxr83DRTCPo4MNzh1HtPGTiqN0m6TDmHKHOz6vRQuSVLkyu5AYU2sKThC22R1QbCGAColOV4xrWzw9pv3e9w0jHQtKJoc/upGSTKQZEhltV/V6WId7aIrkhoxK6+JJFKql3VUAqa67SzCu4aCxvCmA5gl35b40ogHKf9ziCuY7uLvsumKV8wVjQYLNDzsdTJWk26v5yZXpT+RN5yaZgem8+bQp0gF6ZuEujPYhisX4eOGBrr/TkJ2prfOv/TgalmcwHFGlXOxxioK0bA8MFR8wggS7MIIDo6ADAgECAgECMA0GCSqGSIb3DQEBBQUAMGIxCzAJBgNVBAYTAlVTMRMwEQYDVQQKEwpBcHBsZSBJbmMuMSYwJAYDVQQLEx1BcHBsZSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEWMBQGA1UEAxMNQXBwbGUgUm9vdCBDQTAeFw0wNjA0MjUyMTQwMzZaFw0zNTAyMDkyMTQwMzZaMGIxCzAJBgNVBAYTAlVTMRMwEQYDVQQKEwpBcHBsZSBJbmMuMSYwJAYDVQQLEx1BcHBsZSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEWMBQGA1UEAxMNQXBwbGUgUm9vdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOSRqQkfkdseR1DrBe1eeYQt6zaiV0xV7IsZid75S2z1B6siMALoGD74UAnTf0GomPnRymacJGsR0KO75Bsqwx+VnnoMpEeLW9QWNzPLxA9NzhRp0ckZcvVdDtV/X5vyJQO6VY9NXQ3xZDUjFUsVWR2zlPf2nJ7PULrBWFBnjwi0IPfLrCwgb3C2PwEwjLdDzw+dPfMrSSgayP7OtbkO2V4c1ss9tTqt9A8OAJILsSEWLnTVPA3bYharo3GSR1NVwa8vQbP4++NwzeajTEV+H0xrUJZBicR0YgsQg0GHM4qBsTBY7FoEMoxos48d3mVz/2deZbxJ2HafMxRloXeUyS0CAwEAAaOCAXowggF2MA4GA1UdDwEB/wQEAwIBBjAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjAfBgNVHSMEGDAWgBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjCCAREGA1UdIASCAQgwggEEMIIBAAYJKoZIhvdjZAUBMIHyMCoGCCsGAQUFBwIBFh5odHRwczovL3d3dy5hcHBsZS5jb20vYXBwbGVjYS8wgcMGCCsGAQUFBwICMIG2GoGzUmVsaWFuY2Ugb24gdGhpcyBjZXJ0aWZpY2F0ZSBieSBhbnkgcGFydHkgYXNzdW1lcyBhY2NlcHRhbmNlIG9mIHRoZSB0aGVuIGFwcGxpY2FibGUgc3RhbmRhcmQgdGVybXMgYW5kIGNvbmRpdGlvbnMgb2YgdXNlLCBjZXJ0aWZpY2F0ZSBwb2xpY3kgYW5kIGNlcnRpZmljYXRpb24gcHJhY3RpY2Ugc3RhdGVtZW50cy4wDQYJKoZIhvcNAQEFBQADggEBAFw2mUwteLftjJvc83eb8nbSdzBPwR+Fg4UbmT1HN/Kpm0COLNSxkBLYvvRzm+7SZA/LeU802KI++Xj/a8gH7H05g4tTINM4xLG/mk8Ka/8r/FmnBQl8F0BWER5007eLIztHo9VvJOLr0bdw3w9F4SfK8W147ee1Fxeo3H4iNcol1dkP1mvUoiQjEfehrI9zgWDGG1sJL5Ky+ERI8GA4nhX1PSZnIIozavcNgs/e66Mv+VNqW2TAYzN39zoHLFbr2g8hDtq6cxlPtdk2f8GHVdmnmbkyQvvY1XGefqFStxu9k0IkEirHDx22TZxeY8hLgBdQqorV2uT80AkHN7B1dSExggHLMIIBxwIBATCBozCBljELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xLDAqBgNVBAsMI0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zMUQwQgYDVQQDDDtBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9ucyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eQIIDutXh+eeCY0wCQYFKw4DAhoFADANBgkqhkiG9w0BAQEFAASCAQAjAVXoSKafreM4rK43wz2z5pW404KhCPVKQ+rNzVCToOwEhVwJDNVplrpLEXDuC/0CmzMbs3+wAlMCjIof1UD2ZoujIJk/rWX/67FeC8JAgH9obGCl6kmwhQXYr/EKtrP7Pj27rAHu8os1JZFSTBQ/+dkwOJgputyujIFhhIR/YrHWeO7iLt4cUsruViyl/tGwMj0PG5v61au6iM0nHsfy82y8O7fNdPvgFFbYRip2BgT9O3b+mSnanobMyJrUL3EiYnnka9oys94zniJaZ34v78XLeoCFEQ+nndk08PttIfFcfAJaJ8h4JjlTavPAtl24IT2+AEJFVJWZOpJBmC9c";
//		receiptData = "{\"Store\":\"AppleAppStore\",\"TransactionID\":\"1000000700080408\",\"Payload\":\"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\"}";

        // google测试订单
//		receiptData = "{\"Store\":\"GooglePlay\",\"TransactionID\":\"GPA.3310-7321-5376-34002\",\"Payload\":\"{\\\"json\\\":\\\"{\\\\\\\"orderId\\\\\\\":\\\\\\\"GPA.3310-7321-5376-34002\\\\\\\",\\\\\\\"packageName\\\\\\\":\\\\\\\"com.dxx.firebattle\\\\\\\",\\\\\\\"productId\\\\\\\":\\\\\\\"com.dxx.firebattle_d1\\\\\\\",\\\\\\\"purchaseTime\\\\\\\":1591950425247,\\\\\\\"purchaseState\\\\\\\":0,\\\\\\\"developerPayload\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"developerPayload\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"is_free_trial\\\\\\\\\\\\\\\":false,\\\\\\\\\\\\\\\"has_introductory_price_trial\\\\\\\\\\\\\\\":false,\\\\\\\\\\\\\\\"is_updated\\\\\\\\\\\\\\\":false}\\\\\\\",\\\\\\\"purchaseToken\\\\\\\":\\\\\\\"ppflehjopjcoklclolgoogab.AO-J1OyneeKyalYcusQvk2wW-GOXulDfxFXnHA7bBv8Zic0MXhvgN-RLJTmklZ9VH76-gshc6QnjWisIPK0cZzZ16bzpJeqhc_J5undllRIcdwHqIyQ-B_rXQqqegMl80gerZYIWGXyL\\\\\\\"}\\\",\\\"signature\\\":\\\"bqzo1L+Ag8W0nwGIlDS2XnLynhaoJhI5FrCon+tZI+c6wD3uIN0Jsbg5gZ93gvG+vnY5hVVZIyRdr7MD\\\\/lrFaRjhb7Su+qL\\\\/hyFUXakkij64lABY3R8xp5RtX1Pv7UMRRVbfAICAxnARuRaWzYYnjFtLtVMgLAWRMVYSHNsoOHgJuFUJxwQdEVh+B6fWP220GFFfSlh5Vy7sdUxwhel7dNRBTJX7bkCYt\\\\/IkI+H4RPJs0\\\\/GQasikCCdf28pFhAJDc\\\\/nPaKoWuv6YiP6a1w0IG06lGMz2RV9ROmr2LMgyy8GLs2BRRvaW9ouG4b5fzMZga534tUJIMYuUfPwlUSD7VQ==\\\",\\\"skuDetails\\\":\\\"{\\\\\\\"skuDetailsToken\\\\\\\":\\\\\\\"AEuhp4IxqY7yDkqoTrxhbxcWbqGcOIIxB8hlTtu5oYFANJ6g1X6Q8ioYNgrphzNS_qCa\\\\\\\",\\\\\\\"productId\\\\\\\":\\\\\\\"com.dxx.firebattle_d1\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"inapp\\\\\\\",\\\\\\\"price\\\\\\\":\\\\\\\"\302\2430.59\\\\\\\",\\\\\\\"price_amount_micros\\\\\\\":590000,\\\\\\\"price_currency_code\\\\\\\":\\\\\\\"GBP\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"com.dxx.firebattle_d1 (FireBattle)\\\\\\\",\\\\\\\"description\\\\\\\":\\\\\\\"com.dxx.firebattle_d1\\\\\\\"}\\\",\\\"isPurchaseHistorySupported\\\":true}\"}";
        // ios测试订单
//		receiptData = "{\"Store\":\"AppleAppStore\",\"TransactionID\":\"1000000638327503\",\"Payload\":\"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\"}";
        // oneStore 测试订单
//		receiptData = "{\"orderId\":\"ONESTORE7_000000000000000000000000521018\",\"packageName\":\"com.habby.onestore.archero\",\"productId\":\"com.habby.archero_discount101\",\"purchaseTime\":1564553081996,\"purchaseId\":\"SANDBOX3000000523016\",\"developerPayload\":\"\",\"purchaseState\":0,\"recurringState\":0}";

        JSONObject receiptJsonObj = (JSONObject) JSONObject.parse(receiptData);
        if (channelID == ChannelID.APPLE) {
            // ios 验单并发货
            return this.iosReceiptDataWitness(userId, 1, receiptJsonObj, params);
        } else if (channelID == ChannelID.GOOGLE) {
            // 谷歌验单并发货
            return this.googleReceiptDataWitness(userId, receiptJsonObj, params);
        } else {
            // 安卓&小游戏获取支付结果
            PayInAppPurchaseResponse payInAppPurchaseResponse = this.otherChannelPayResult(userId, params.getPreOrderId());
            if (payInAppPurchaseResponse == null) {
                return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
            }
            return Result.Success(payInAppPurchaseResponse);
        }
    }

    /**
     * IOS订单内购
     */
    private Result<PayInAppPurchaseResponse> iosReceiptDataWitness(long userId, int urlIdx, JSONObject receiptJsonObj,
                                                                   PayInAppPurchaseRequest payInAppPurchaseRequest) {

        String pszPayload = receiptJsonObj.getString("Payload");
        Map<String, Object> postData = new HashMap<>();
        postData.put("receipt-data", pszPayload);

        Set<String> whiteDeviceIds = new HashSet<>();
        boolean retry = false;
        //校验订单
        Map<String, Object> respDataMap = appleService.checkOrderState(urlIdx, retry, postData,
                whiteDeviceIds, RequestContext.getCommonParams().getDeviceId(), userId);
        if (respDataMap == null) {
            log.error("ios 与IAP服务器进行通信失败,  userId: {}, receiptData :{}", userId, payInAppPurchaseRequest.getReceiptData());
            return Result.Error(ErrorCode.IAP_VERIFY_SERVER_UNAVAILABLE);
        }

        int iapCode = (int) respDataMap.get("status");
        if (iapCode != 0) {
            log.error("iosVerifyReceipt status error,  userId: {}, status:{}, respData:{}, receiptData :{}",
                    userId, iapCode, respDataMap, payInAppPurchaseRequest.getReceiptData());
            // 单据校验失败
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        JSONObject jsonObject = (JSONObject) respDataMap.get("receipt");
        String bundleId = jsonObject.getString("bundle_id");
        String cfgBundleId = channelConfig.getAppleConfig().getPackageName();
        if (StringUtils.isEmpty(bundleId) || !bundleId.equals(cfgBundleId)) {
            log.error("iosVerifyReceiptBundleId error,  userId: {}, status:{}, respData:{}, receiptData :{}",
                    userId, iapCode, respDataMap, payInAppPurchaseRequest.getReceiptData());
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        String environment = respDataMap.get("environment").toString();
        String transactionID = receiptJsonObj.getString("TransactionID");

        JSONArray inAppArray = (JSONArray) jsonObject.get("in_app");

        PurchaseEntity purchaseEntity = paySupport.getByPurchaseId(payInAppPurchaseRequest.getPurchaseId());
        if (purchaseEntity == null) {
            log.error("purchaseEntity is null, userId:{}, purchaseId:{}", userId, payInAppPurchaseRequest.getPurchaseId());
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }
        IAPEntity iapEntity = paySupport.getIAPEntityByPurchaseId(payInAppPurchaseRequest.getPurchaseId());
        if (iapEntity == null) {
            log.error("iapEntity is null, userId:{}, purchaseId:{}", userId, payInAppPurchaseRequest.getPurchaseId());
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        boolean isExist = false;
        for (int i = 0, len = inAppArray.size(); i < len; i++) {
            JSONObject inAppObj = inAppArray.getJSONObject(i);
            String transaction_id = inAppObj.getString("transaction_id");
            String productId = inAppObj.getString("product_id");
            String IAPID = iapEntity.getIapId();
            if (gameConfigManager.isCn()) {
                IAPID = iapEntity.getCNIapId();
            }
            if (transactionID.equals(transaction_id) && IAPID.equals(productId)) {
                isExist = true;
                break;
            }
        }

        if (!isExist) {
            log.error("TransactionID is not exist, userId:{}, TransactionID:{}", userId, transactionID);
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        return transferIAPItems(userId, environment.equals("Sandbox"), transactionID, purchaseEntity.getId(),
                payInAppPurchaseRequest.getChannelId(), payInAppPurchaseRequest.getPreOrderId(), payInAppPurchaseRequest.getExtraInfo(), "",
                payInAppPurchaseRequest.getCurrency());
    }

    /**
     * google内购
     */
    private Result<PayInAppPurchaseResponse> googleReceiptDataWitness(long userId, JSONObject receiptJsonObj, PayInAppPurchaseRequest payInAppPurchaseRequest) {

        JSONObject payLoadJsonObj = receiptJsonObj.getJSONObject("Payload").getJSONObject("json");

        String packageName = payLoadJsonObj.getString("packageName");
        String jsonProductId = payLoadJsonObj.getString("productId");
        String purchaseToken = payLoadJsonObj.getString("purchaseToken");
        String jsonOrderId = payLoadJsonObj.getString("orderId");

        ProductPurchase purchase = googleService.checkOrderState(packageName, jsonProductId, jsonOrderId, purchaseToken);
        if (purchase == null) {
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        // 订单的购买状态。0。购买 1。取消 2。等待中
        if (purchase.getPurchaseState() != 0) {
            log.error("google pay purchase error, userId:{}, purchaseState:{}, purchaseOrderId:{}, jsonOrderId:{}", userId, purchase.getPurchaseState(), purchase.getOrderId(), jsonOrderId);
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        String orderId = "";
        // 1。促销（即使用促销代码购买）google points
        Integer purchaseType = purchase.getPurchaseType();
        boolean isSandBox = Optional.ofNullable(purchaseType).map(o -> Objects.equals(o, 0)).orElseGet(() -> false);
        if (purchaseType != null && purchaseType == 1) {
            String transactionID = receiptJsonObj.getString("TransactionID");
            if (StringUtils.isEmpty(transactionID)) {
                transactionID = purchaseToken;
            }
            orderId = CryptUtil.md5(transactionID);
            if (StringUtils.isEmpty(orderId)) {
                orderId = transactionID;
            }
        } else {
            // 验证订单号是否一致
            if (!Objects.equals(purchase.getOrderId(), jsonOrderId)) {
                log.error("google pay purchase error, userId:{},  purchaseState:{}, purchaseOrderId:{}, jsonOrderId:{}", userId, purchase.getPurchaseState(), purchase.getOrderId(), jsonOrderId);
                return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
            }
            orderId = purchase.getOrderId();
        }

        PurchaseEntity purchaseEntity = paySupport.getByPurchaseId(payInAppPurchaseRequest.getPurchaseId());
        if (purchaseEntity == null) {
            log.error("purchaseEntity is null, userId:{}, purchaseId:{}", userId, payInAppPurchaseRequest.getPurchaseId());
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }
        IAPEntity iapEntity = paySupport.getIAPEntityByPurchaseId(payInAppPurchaseRequest.getPurchaseId());
        if (iapEntity == null) {
            log.error("iapEntity is null, userId:{}, purchaseId:{}", userId, payInAppPurchaseRequest.getPurchaseId());
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }
        if (!iapEntity.getIapId().equals(jsonProductId)) {
            log.error("google pay purchase error, userId:{},  purchaseState:{}, purchaseOrderId:{}, jsonOrderId:{}", userId, purchase.getPurchaseState(), purchase.getOrderId(), jsonOrderId);
            return Result.Error(ErrorCode.IAP_VERIFY_FAILED);
        }

        return transferIAPItems(userId, isSandBox, orderId, payInAppPurchaseRequest.getPurchaseId(),
                payInAppPurchaseRequest.getChannelId(), payInAppPurchaseRequest.getPreOrderId(), payInAppPurchaseRequest.getExtraInfo(),
                "", payInAppPurchaseRequest.getCurrency());
    }

    private PayInAppPurchaseResponse otherChannelPayResult(long userId, long preOrderId) {
        PayInAppPurchaseResponse.Builder builder = PayInAppPurchaseResponse.newBuilder();
        AndroidPayCbResultRedisDao.AndroidPayCbModel androidPayCbModel = androidPayCbResultRedisDao.getPayCbResult(userId, preOrderId);
        if (androidPayCbModel == null) {
            return null;
        }
        String commData = androidPayCbModel.getCommonData();
        try {
            JsonFormat.parser().merge(commData, builder);
        } catch (InvalidProtocolBufferException e) {
            log.error("parse err {}", commData);
            log.error("parse err", e);
            return null;
        }
        return builder.build();
    }

    @Override
    public boolean isPurchaseLimitExceeded(long userId, int purchaseId, String exInfo) {
        PurchaseEntity purchaseEntity = paySupport.getByPurchaseId(purchaseId);
        if (purchaseEntity == null) {
            log.error("purchaseEntity cant found, purchaseId:{}", purchaseId);
            return true;
        }

        Shop shop = shopDao.getByUserId(userId);
        Map<Integer, Integer> rechargeCountMap = shop.getIap().getRechargeIds();
        int buyCount = rechargeCountMap.getOrDefault(purchaseEntity.getId(), 0);
        int limitCount = purchaseEntity.getLimitCount();

        if (purchaseEntity.getProductType() == IapType.GIFT_PACK && limitCount > 0) {
            Map<Integer, Integer> buyCountMap = shopSupport.calPacksBuyCount();
            if (buyCountMap.get(purchaseEntity.getId()) != null && buyCountMap.get(purchaseEntity.getId()) >= limitCount) {
                return true;
            }
        } else if (purchaseEntity.getProductType() == IapType.PUSH_GIFT) {
            return checkPushPacks(userId, purchaseId);
        } else if (purchaseEntity.getProductType() == IapType.ACTIVITY_GIFT) {
            return activityService.checkShopCanBuy(userId, exInfo);
        } else if (limitCount > 0 && buyCount >= limitCount) {
            // 通用Limit验证
            return true;
        }

        return false;
    }

    private boolean checkPushPacks(long userId, int purchaseId) {
        PurchaseEntity purchaseEntity = paySupport.getByPurchaseId(purchaseId);
        if (purchaseEntity == null) {
            log.error("purchaseEntity cant found, purchaseId={}", purchaseId);
            return true;
        }
        PushPacksEntity pushPacksEntity = gameConfigManager.getIAPConfig().getPushPacksEntity(purchaseId);
        if (pushPacksEntity == null) {
            log.error("pushPacksEntity cant found, purchaseId={}", purchaseId);
            return true;
        }
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        int packType = pushPacksEntity.getPackType();
        if (Objects.equals(packType, 2) || Objects.equals(packType, 4)) {
            Map<Integer, Long> map;
            if (Objects.equals(packType, 2)) {
                map = iap.getOpenServerGiftTime();
            } else {
                map = iap.getChapterGiftTime();
            }
            long time = map.getOrDefault(purchaseId, 0L);//通关的时间，不存在是没通关，值是0是已购买过
            if (Objects.equals(time, 0L)) {
                return true;
            }
            if (DateUtils.getUnixTime() > time) {//过了可购买时间
                return true;
            }
        }
        return false;
    }

    @DynamoDBTransactional
    @Override
    public Result<PayPreOrderResponse> preOrderAction(PayPreOrderRequest params) {
        long userId = RequestContext.getUserId();
        PurchaseEntity purchaseEntity = paySupport.getByPurchaseId(params.getPurchaseId());
        if (purchaseEntity == null) {
            return Result.Error(ErrorCode.PURCHASE_PRODUCTID_NULL);
        }
        IAPEntity iapEntity = paySupport.getIAPEntityByPurchaseId(params.getPurchaseId());
        if (iapEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }
        long preOrderId = params.getPreOrderId();//预下单id

        // 是否超出限购
        if (this.isPurchaseLimitExceeded(userId, purchaseEntity.getId(), params.getExtraInfo())) {
            return Result.Error(ErrorCode.IAP_PURCHASE_COUNT_FULL);
        }

        PreRechargeOrder preRechargeOrder = new PreRechargeOrder();
        preRechargeOrder.setUserId(userId);
        preRechargeOrder.setPreOrderId(preOrderId);
        preRechargeOrder.setTtlTime(DateUtils.getUnixTime() + 30 * 86400);
        preRechargeOrder.setExtraInfo(params.getExtraInfo());
        preRechargeOrder.setPurchaseId(params.getPurchaseId());
        preRechargeOrder.setSuccess(0);

        preRechargeOrderDao.insert(preRechargeOrder);

        PayPreOrderResponse.Builder response = PayPreOrderResponse.newBuilder();
        response.setPreOrderId(preOrderId);

        boolean result = paySupport.unifiedOrder(userId, params, response);
        if (!result) {
            // 下单失败
            return Result.Error(ErrorCode.IAP_UNIFIED_ORDER_FAILED);
        }


        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<PayOnUnityResponse> payOnUnity(PayOnUnityRequest params) {
        if (!gameConfigManager.isDevelop() && !gameConfigManager.isTest()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        long userId = RequestContext.getUserId();
        String orderId = userId + "_" + System.currentTimeMillis();

        Result<PayInAppPurchaseResponse> result = transferIAPItems(userId, true, orderId, params.getPurchaseId(),
                0, 0, params.getExtraInfo(), "", "");
        if (result.getCode() != ErrorCode.SUCCESS) {
            return Result.Error(result.getCode());
        }
        PayOnUnityResponse.Builder response = PayOnUnityResponse.newBuilder();
        response.setPayInAppPurchaseResponse(result.getContent());
        return Result.Success(response.build());
    }


    @Override
    public Map<String, Object> getRechargeOrderItems(long userId, int purchaseId) {
        PreRechargeOrder preRechargeOrder = preRechargeOrderDao.getByLastProductId(userId, purchaseId);
        if (preRechargeOrder == null) {
            return null;
        }
        IAPInfoModel model = new IAPInfoModel();
        if (!this.getIAPProductInfo(model, purchaseId)) {
            return null;
        }
        List<List<Integer>> rewards = new ArrayList<>();
        PurchaseEntity rewardsInfo = model.getRewardsInfo();
        switch (rewardsInfo.getProductType()) {
            case 1:
                DiamondsEntity diamondsEntity = gameConfigManager.getIAPConfig().getDiamondsEntity(rewardsInfo.getId());
                rewards.addAll(diamondsEntity.getProducts());
                break;
            default:
                break;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("rewards", rewards);
        result.put("preOrderId", preRechargeOrder.getPreOrderId());
        return result;
    }

    @Override
    public void iapSupplement(long userId, String uniqueId, long preOrderId, int extraType, String extraInfo) {
        PreRechargeOrder preRechargeOrder = preRechargeOrderDao.getByOrderId(userId, preOrderId);
        if (preRechargeOrder == null) {
            return;
        }
        preRechargeOrder.setSuccess(1);
        preRechargeOrderDao.update(preRechargeOrder);
        IAPInfoModel model = new IAPInfoModel();
        this.getIAPProductInfo(model, preRechargeOrder.getPurchaseId());
        this.saveIAPInfo(userId, model, (uniqueId + "_" + preOrderId), preRechargeOrder.getPurchaseId(), "", extraInfo);

    }

    /**
     * 根据商品ID 查询商品信息
     *
     * @param model
     * @param purchaseId
     * @return
     */
    private boolean getIAPProductInfo(IAPInfoModel model, int purchaseId) {
        PurchaseEntity purchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(purchaseId);
        if (purchaseEntity == null) {
            return false;
        }
        model.setRewardsInfo(purchaseEntity);
        return true;
    }

    /**
     * 处理发货
     */
    @DynamoDBTransactional
    @Override
    public Result<PayInAppPurchaseResponse> transferIAPItems(long userId, boolean isSandBox, String orderId, int purchaseId,
                                                             int channelId, long preOrderId,
                                                             String extraInfo, String cpOrderId, String currency) {
        if (this.isPurchaseLimitExceeded(userId, purchaseId, extraInfo)) {
            log.error("transferIAPItems failed, purchaseLimit, userId:{}, orderId:{}, purchaseId:{}, extraInfo:{}", userId, orderId, purchaseId, extraInfo);
            return Result.Error(ErrorCode.IAP_PURCHASE_COUNT_FULL);
        }
        IAPInfoModel model = new IAPInfoModel();
        model.setChannelId(channelId);
        model.setSandBox(isSandBox);
        if (!getIAPProductInfo(model, purchaseId)) {
            log.error("getIAPProductInfo failed, userId:{}, productId:{}", userId, purchaseId);
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        String lockKey = userId + "-" + orderId;
        if (!redisLock.lockWithOutRetry(lockKey, "1", 180000)) {
            // 相同的订单锁3分钟
            return Result.Error(ErrorCode.IAP_ORDER_EXIST);
        }

        Shop shop = shopDao.getByUserId(userId);
        PurchaseEntity config = model.getRewardsInfo();
        if (config == null) {
            log.error("diamond config is empty ,purchaseId:{}", purchaseId);
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }
        IAPEntity iapEntity = paySupport.getIAPEntityByPurchaseId(config.getIAPID());
        if (iapEntity == null) {
            log.error("iapEntity is null, purchaseId:{}", purchaseId);
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        // 保存订单
        int code = saveIAPInfo(userId, model, orderId, purchaseId, cpOrderId, extraInfo);
        if (code != ErrorCode.SUCCESS) {
            return Result.Error(code);
        }

        // 标记是否付费
        User user = RequestContext.getUser();
        if (user.getIsPay() == null || !user.getIsPay()) {
            user.setIsPay(true);
            userDao.updateIsPay(user);
        }

        var rewardResult = paySupport.getPurchaseRewards(userId, purchaseId, extraInfo);
        if (!rewardResult.getLeft()) {
            log.error("rewards is null, userId:{}, purchaseId:{}", userId, purchaseId);
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        PayInAppPurchaseResponse.Builder response = PayInAppPurchaseResponse.newBuilder();

        // 发奖
        if (!rewardResult.getRight().isEmpty()) {
            RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardResult.getRight());
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        }

        // 更新购买次数
        Integer buyCount = shop.getIap().getRechargeIds().getOrDefault(config.getId(), 0);
        shop.getIap().getRechargeIds().put(config.getId(), buyCount + 1);



        // 更新总充值金额
        int addRecharge = Math.round(iapEntity.getPrice() * 100);
        Shop.IAPModel iap = shop.getIap();
        int totalRecharge = iap.getTotalRecharge();
        iap.setTotalRecharge(totalRecharge + addRecharge);
        shopDao.updateIap(shop);

        if (preOrderId > 0) {
            PreRechargeOrder preRechargeOrder = preRechargeOrderDao.getByOrderId(userId, preOrderId);
            if (preRechargeOrder != null) {
                preRechargeOrder.setSuccess(1);
                preRechargeOrderDao.update(preRechargeOrder);
            }
        }

        response.setIAPTransID(orderId);
        response.putRechargeIds(config.getId(), buyCount + 1);
        response.setRechargeId(model.getRewardsInfo().getId());
        response.setIapInfo(shopSupport.buildUserIAPInfo());
        return Result.Success(response.build());

    }

    public int saveIAPInfo(long userId, IAPInfoModel model, String orderId, int purchaseId, String cpOrderId, String extraInfo) {
        RechargeOrder rechargeOrder = rechargeOrderDao.getByOrderId(orderId);
        if (rechargeOrder != null && rechargeOrder.getState() == 1) {
            log.error("order exist, userId:{}, orderId:{}, purchaseId:{} ", userId, orderId, purchaseId);
            return ErrorCode.IAP_ORDER_EXIST;
        }
        // 订单不存在插入新订单
        long transId = userService.getTransId(userId);
        if (transId == 0) {
            log.error("getIncreTransId failed, userId:{}, orderId:{}, purchaseId:{}", userId, orderId, purchaseId);
            return ErrorCode.QUERY_USER_DATA_ERROR;
        }
        IAPEntity iapEntity = paySupport.getIAPEntityByPurchaseId(purchaseId);
        rechargeOrder = new RechargeOrder();
        rechargeOrder.setUserId(userId);
        rechargeOrder.setPurchaseId(purchaseId);
        rechargeOrder.setTransId(transId);
        rechargeOrder.setOrderId(orderId);
        rechargeOrder.setCpOrderId(cpOrderId);
        rechargeOrder.setSandbox(model.isSandBox());
        long unixTime = DateUtils.getUnixTime();
        rechargeOrder.setTimeStamp(unixTime);
        rechargeOrder.setChannelId((short) model.getChannelId());
        rechargeOrder.setState((short) 1);
        rechargeOrder.setExtraInfo(extraInfo);
        rechargeOrder.setIapId(iapEntity.getIapId());
        rechargeOrder.setAmount(iapEntity.getPrice());
        rechargeOrder.setCreateAt(DateUtils.getUTCTimeStr(unixTime));


        rechargeOrderDao.insertNow(rechargeOrder);
        return ErrorCode.SUCCESS;
    }
}
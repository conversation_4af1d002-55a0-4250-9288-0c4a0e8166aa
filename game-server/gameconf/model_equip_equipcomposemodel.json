[{"id": 1, "quality": 2, "starID": 0, "starCount": 0, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 2, "composeNeed1": 1, "composeNeed2": 1, "composeNeed3": 2, "composeNeed4": 1, "maxLevel": 0}, {"id": 2, "quality": 3, "starID": 0, "starCount": 0, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 3, "composeNeed1": 1, "composeNeed2": 2, "composeNeed3": 2, "composeNeed4": 1, "maxLevel": 0}, {"id": 3, "quality": 5, "starID": 0, "starCount": 0, "reduceTo": 2, "reduceNeed1": 2, "reduceNeed2": 2, "composeTo": 4, "composeNeed1": 2, "composeNeed2": 3, "composeNeed3": 2, "composeNeed4": 0, "maxLevel": 0}, {"id": 4, "quality": 7, "starID": 0, "starCount": 0, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 5, "composeNeed1": 2, "composeNeed2": 4, "composeNeed3": 2, "composeNeed4": 0, "maxLevel": 0}, {"id": 5, "quality": 9, "starID": 0, "starCount": 0, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 6, "composeNeed1": 1, "composeNeed2": 5, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 6, "quality": 10, "starID": 2, "starCount": 1, "reduceTo": 5, "reduceNeed1": 1, "reduceNeed2": 5, "composeTo": 7, "composeNeed1": 1, "composeNeed2": 5, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 7, "quality": 11, "starID": 3, "starCount": 2, "reduceTo": 5, "reduceNeed1": 2, "reduceNeed2": 5, "composeTo": 8, "composeNeed1": 1, "composeNeed2": 5, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 8, "quality": 12, "starID": 4, "starCount": 3, "reduceTo": 5, "reduceNeed1": 3, "reduceNeed2": 5, "composeTo": 9, "composeNeed1": 3, "composeNeed2": 5, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 9, "quality": 13, "starID": 5, "starCount": 4, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 10, "composeNeed1": 3, "composeNeed2": 5, "composeNeed3": 2, "composeNeed4": 0, "maxLevel": 0}, {"id": 10, "quality": 14, "starID": 6, "starCount": 5, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 11, "composeNeed1": 1, "composeNeed2": 10, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 11, "quality": 15, "starID": 7, "starCount": 1, "reduceTo": 10, "reduceNeed1": 1, "reduceNeed2": 10, "composeTo": 12, "composeNeed1": 1, "composeNeed2": 10, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 12, "quality": 16, "starID": 8, "starCount": 2, "reduceTo": 10, "reduceNeed1": 2, "reduceNeed2": 10, "composeTo": 13, "composeNeed1": 1, "composeNeed2": 10, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 13, "quality": 17, "starID": 9, "starCount": 3, "reduceTo": 10, "reduceNeed1": 3, "reduceNeed2": 10, "composeTo": 14, "composeNeed1": 3, "composeNeed2": 10, "composeNeed3": 1, "composeNeed4": 1, "maxLevel": 0}, {"id": 14, "quality": 18, "starID": 10, "starCount": 4, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 15, "composeNeed1": 3, "composeNeed2": 10, "composeNeed3": 2, "composeNeed4": 0, "maxLevel": 0}, {"id": 15, "quality": 19, "starID": 11, "starCount": 5, "reduceTo": 0, "reduceNeed1": 0, "reduceNeed2": 0, "composeTo": 0, "composeNeed1": 3, "composeNeed2": 6, "composeNeed3": 2, "composeNeed4": 0, "maxLevel": 0}]
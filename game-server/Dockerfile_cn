FROM public.ecr.aws/gorilla/jdk-21-cn:21.0.7-arm64

ARG build_env

RUN mkdir -p /data/build && \
    mkdir -p /data/app

# 首先只复制pom.xml文件
COPY pom.xml /data/build/
# 预先下载所有依赖
WORKDIR /data/build
RUN mvn dependency:go-offline -P $build_env

COPY . /data/build

# build faketime
#WORKDIR /data/build/tools/libfaketime
#RUN make install

WORKDIR /data/build
RUN mvn clean package -P $build_env -DskipTests
WORKDIR /data/build/target
RUN tar -zvxf *.tar.gz -C /data/app
WORKDIR /data/app

EXPOSE 8010

ENTRYPOINT [ "game/bin/start.sh"]
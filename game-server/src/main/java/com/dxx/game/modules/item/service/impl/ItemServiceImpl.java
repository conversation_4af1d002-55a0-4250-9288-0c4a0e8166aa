package com.dxx.game.modules.item.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.CommonUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.gamemember.MemberEntity;
import com.dxx.game.config.entity.item.ItemEntity;
import com.dxx.game.config.entity.mainlevelreward.AFKrewardEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dto.CommonProto.ItemDto;
import com.dxx.game.dto.ItemProto;
import com.dxx.game.modules.common.HangUpHelper;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.item.service.ItemService;
import com.dxx.game.modules.reward.model.HeroReward;
import com.dxx.game.modules.reward.model.ItemReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
public class ItemServiceImpl implements ItemService {

    @Autowired
    private ItemDao itemDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private DropService dropService;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private HangUpHelper hangUpHelper;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private HeroService heroService;
    @Autowired
    private UserService userService;

    @DynamoDBTransactional
    @Override
    public Result<ItemProto.ItemUseResponse> useItemAction(ItemProto.ItemUseRequest params) {
        long rowId = params.getRowId();
        long userId = RequestContext.getUserId();
        int count = params.getCount();
        int index = params.getIndex();
        if(!CommonUtils.checkUseCountLimit(count)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Item item = itemDao.getByRowId(userId, rowId);
        if (item == null) {
            return Result.Error(ErrorCode.ITEM_IS_NOT_ENOUGH);
        }

        // 扣除道具
        ItemReward cost = ItemReward.valueOf(item.getItemId(), -count);
        RewardResultSet costRewardResultSet = rewardService.executeReward(userId, cost);
        if (costRewardResultSet.isFailed()) {
            return Result.Error(costRewardResultSet.getResultCode());
        }

        List<Reward> rewards = new ArrayList<>();
        ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(item.getItemId());
        int itemType = itemEntity.getItemType();
        if (itemEntity.getItemTypeParam().isEmpty()) {
            log.error("item_type_params is empty, itemId:{}", item.getItemId());
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (itemType == RewardType.ITEM_RAND_GIFT.getValue()) {
            // 随机礼包
            int dropId = itemEntity.getItemTypeParam().get(0).get(0);
            rewards = dropService.dropRewards(dropId, count);
        }
        else if (itemType == RewardType.ITEM_OPTIONAL_GIFT.getValue()) {
            // 自选礼包
            if (index >= itemEntity.getItemTypeParam().size()) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            List<Integer> selectConfig = CollectionUtils.copyS(itemEntity.getItemTypeParam().get(index));
            selectConfig.set(1, selectConfig.get(1) * count);
            rewards.addAll(rewardService.parseReward(selectConfig));
        }
        else if (itemType == RewardType.ITEM_HUANG_UP_TIME_GIFT.getValue()) {
            // 挂机奖励相关礼包
            int configId = itemEntity.getItemTypeParam().get(0).get(0);
            int seconds = itemEntity.getItemTypeParam().get(0).get(1);
            int type = 0;

            UserExtend userExtend = userExtendDao.getByUserId(userId);
            AFKrewardEntity levelEntity = this.getRewardConfig(userExtend.getChapterId());
            if(levelEntity == null) {
                return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
            }

            double vpv = 0;
            int itemCount = 0;
            if (configId == 1) {
                // 金币
                type = 101;
                itemCount = levelEntity.getHangGold();
                // vip权限检查
//                vpv = userService.vipPermissionValueCheck(userId, VipPermission.PERMISSION_3);
            } else if (configId == 3) {
                // 粉尘
                type = 102;
                itemCount = levelEntity.getHangDust();
            } else if (configId == 4) {
                // 英雄经验
                type = 103;
                itemCount = levelEntity.getHangHeroExp();
            }

            List<Integer> rewardConfig = hangUpHelper.calcHangUpItems((seconds * count), itemCount, type, configId);
            if(rewardConfig == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
//            if(!rewardConfig.isEmpty()) {
//                rewardConfig.set(2, (int) (rewardConfig.get(2) * (1 + vpv)));
//            }
            rewards = rewardService.parseReward(rewardConfig);
        }
        else if (itemType == RewardType.ITEM_HERO_FRAGMENT.getValue()) {
            // 英雄碎片
            int heroId = itemEntity.getItemTypeParam().get(0).get(0);
            MemberEntity memberEntity = gameConfigManager.getGameMemberConfig().getMemberEntity(heroId);
            if(memberEntity == null) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

//            if(heroService.getHero(userId, heroId) != null) {
//                return Result.Error(ErrorCode.HERO_EXIST);
//            }
//
//            heroService.createHero(userId, heroId, 1, 0, memberEntity.getInitialQuality(), 0);

            rewards.add(HeroReward.valueOf(heroId, 1, memberEntity.getInitialQuality(), 0));
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (costRewardResultSet.isFailed()) {
            return Result.Error(costRewardResultSet.getResultCode());
        }

        ItemProto.ItemUseResponse.Builder response = ItemProto.ItemUseResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costRewardResultSet, rewardResultSet));
        return Result.Success(response.build());
    }

    @Override
    public Item createItem(long userId, int itemId, int count) {
        Item item = new Item();
        item.setRowId(commonService.generateId(userId));
        item.setItemId(itemId);
        item.setCount(count);
        item.setUserId(userId);
        itemDao.insert(item);
        return item;
    }

    @Override
    public List<ItemDto> getAllItems(long userId) {
        List<Item> items = itemDao.getAllByUserId(userId);
        List<ItemDto> result = new ArrayList<ItemDto>(items.size());
        for (Item item : items) {
            if (item.getCount() > 0) {
                result.add(CommonHelper.buildItemDto(item));
            }
        }
        return result;
    }

    @Override
    public List<Reward> randGiftReward(int itemId, int count) {

        ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(itemId);
        int dropId = itemEntity.getItemTypeParam().get(0).get(0);
        return dropService.dropRewards(dropId, count);
    }

    public AFKrewardEntity getRewardConfig(int chapterId) {
        List<AFKrewardEntity> entityList = new ArrayList<>(gameConfigManager.getMainLevelRewardConfig().getAFKreward().values());
        return entityList.stream()
                .filter(v -> chapterId >= v.getRequiredLevel())
                .max(Comparator.comparingInt(AFKrewardEntity::getRequiredLevel))
                .orElse(null);
    }
}

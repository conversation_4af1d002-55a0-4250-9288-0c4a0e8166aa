
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFlipCard_FlipMap {
    public ActivityFlipCard_FlipMap(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Type = _buf.get("Type").getAsInt();
        NormalGrid1 = _buf.get("NormalGrid1").getAsInt();
        Step = _buf.get("Step").getAsInt();
        Bomb = _buf.get("Bomb").getAsInt();
        BombH = _buf.get("BombH").getAsInt();
        BombV = _buf.get("BombV").getAsInt();
        Show = _buf.get("Show").getAsInt();
        PartShow = _buf.get("PartShow").getAsInt();
        CollectGrid = _buf.get("CollectGrid").getAsInt();
        Special = _buf.get("Special").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("ClueDistance").getAsJsonArray(); ClueDistance = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  ClueDistance.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("SpecialDistance").getAsJsonArray(); SpecialDistance = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  SpecialDistance.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("ItemLib").getAsJsonArray(); ItemLib = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  ItemLib.add(_v0); }   }
    }

    public static ActivityFlipCard_FlipMap deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFlipCard_FlipMap(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 地图类型<br/>1：通用地图<br/>2：补强库（线索太少了走这个）<br/>3：补弱库（线索太多了走这个）
     */
    public final int Type;
    /**
     * 普通格子总数
     */
    public final int NormalGrid1;
    /**
     * 步数格子数量
     */
    public final int Step;
    /**
     * 炸弹格子数量
     */
    public final int Bomb;
    /**
     * 横向炸弹格子数量
     */
    public final int BombH;
    /**
     * 纵向炸弹格子数量
     */
    public final int BombV;
    /**
     * 全开格子数量
     */
    public final int Show;
    /**
     * 部分开（5个）格子数量
     */
    public final int PartShow;
    /**
     * 全收格子总数
     */
    public final int CollectGrid;
    /**
     * 特殊奖励数量
     */
    public final int Special;
    /**
     * 线索距离（范围）
     */
    public final java.util.List<Integer> ClueDistance;
    /**
     * 特殊奖励距离（范围）
     */
    public final java.util.List<Integer> SpecialDistance;
    /**
     * 道具库<br/>道具ID，数量,权重<br/>纯随机抽取
     */
    public final java.util.List<String> ItemLib;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Type:" + Type + ","
        + "NormalGrid1:" + NormalGrid1 + ","
        + "Step:" + Step + ","
        + "Bomb:" + Bomb + ","
        + "BombH:" + BombH + ","
        + "BombV:" + BombV + ","
        + "Show:" + Show + ","
        + "PartShow:" + PartShow + ","
        + "CollectGrid:" + CollectGrid + ","
        + "Special:" + Special + ","
        + "ClueDistance:" + ClueDistance + ","
        + "SpecialDistance:" + SpecialDistance + ","
        + "ItemLib:" + ItemLib + ","
        + "}";
    }
}


package com.dxx.game.modules.hero.service;


import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dto.HeroProto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:50
 */
public interface HeroService {

    /**
     * 初始化英雄数据
     */
    void init(UserExtend userExtend);

    /**
     * 新增英雄
     */
    Hero createHero(long userId, int heroId, int level, int star, int quality, int rowId);

    Hero createHeroNow(long userId, int heroId, int level, int star, int quality, int rowId);

    Hero initHero(long userId, int heroId, int level, int star, int quality, int rowId);

        /**
         * 玩家所有英雄
         */
    List<Hero> getAllHeros(long userId);

    List<Hero> getAllHeros(long userId, List<Long> rowIds);

    /**
     * 获取英雄信息
     */
    Hero getHero(long userId, long rowId);

    Hero getHeroById(long userId, int heroId);

    Result<HeroProto.HeroUpgradeResponse> heroUpgradeAction(HeroProto.HeroUpgradeRequest params);

    Result<HeroProto.HeroAdvanceResponse> advance(HeroProto.HeroAdvanceRequest params);

    Result<HeroProto.HeroStarResponse> star(HeroProto.HeroStarRequest params);

    Result<HeroProto.HeroResetResponse> reset(HeroProto.HeroResetRequest params);

    Result<HeroProto.HeroBookScoreResponse> heroBookScore(HeroProto.HeroBookScoreRequest params);

    Result<HeroProto.HeroBookRewardResponse> heroBookReward(HeroProto.HeroBookRewardRequest params);

    List<Hero> getHeroByFormations(long userId, Map<Integer, List<Long>> formations);

    List<Hero> getHeroByFormations(long userId, Map<Integer, List<Long>> formations, List<Hero> heroes);

    Result<HeroProto.HeroReplaceSkinResponse> heroReplaceSkin(HeroProto.HeroReplaceSkinRequest params);

    int totalFormationHeroLevel(long userId, List<Long> formation);

    Result<HeroProto.HeroBondLevelUpResponse> heroBondLevelUp(HeroProto.HeroBondLevelUpRequest params);

    Result<HeroProto.HeroLosslessResponse> lossless(HeroProto.HeroLosslessRequest params);
}

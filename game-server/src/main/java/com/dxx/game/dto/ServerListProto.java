// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: serverlist.proto

package com.dxx.game.dto;

public final class ServerListProto {
  private ServerListProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface UserGetLastLoginRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.UserGetLastLoginRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10141 请求角色列表
   * </pre>
   *
   * Protobuf type {@code Proto.ServerList.UserGetLastLoginRequest}
   */
  public static final class UserGetLastLoginRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.UserGetLastLoginRequest)
      UserGetLastLoginRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UserGetLastLoginRequest.newBuilder() to construct.
    private UserGetLastLoginRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UserGetLastLoginRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UserGetLastLoginRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UserGetLastLoginRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.class, com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest other = (com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10141 请求角色列表
     * </pre>
     *
     * Protobuf type {@code Proto.ServerList.UserGetLastLoginRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.UserGetLastLoginRequest)
        com.dxx.game.dto.ServerListProto.UserGetLastLoginRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.class, com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest build() {
        com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest buildPartial() {
        com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest result = new com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest other) {
        if (other == com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.UserGetLastLoginRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.UserGetLastLoginRequest)
    private static final com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest();
    }

    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UserGetLastLoginRequest>
        PARSER = new com.google.protobuf.AbstractParser<UserGetLastLoginRequest>() {
      @java.lang.Override
      public UserGetLastLoginRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UserGetLastLoginRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UserGetLastLoginRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UserGetLastLoginRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UserGetLastLoginResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.UserGetLastLoginResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    java.util.List<com.dxx.game.dto.ServerListProto.RoleDetailDto> 
        getRoleListList();
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    com.dxx.game.dto.ServerListProto.RoleDetailDto getRoleList(int index);
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    int getRoleListCount();
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    java.util.List<? extends com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder> 
        getRoleListOrBuilderList();
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder getRoleListOrBuilder(
        int index);

    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */
    int getServerListCount();
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */
    boolean containsServerList(
        int key);
    /**
     * Use {@link #getServerListMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
    getServerList();
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */
    java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
    getServerListMap();
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */

    com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerListOrDefault(
        int key,
        com.dxx.game.dto.ServerListProto.ZoneInfoDto defaultValue);
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */

    com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerListOrThrow(
        int key);

    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10142 响应
   * </pre>
   *
   * Protobuf type {@code Proto.ServerList.UserGetLastLoginResponse}
   */
  public static final class UserGetLastLoginResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.UserGetLastLoginResponse)
      UserGetLastLoginResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UserGetLastLoginResponse.newBuilder() to construct.
    private UserGetLastLoginResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UserGetLastLoginResponse() {
      roleList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UserGetLastLoginResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UserGetLastLoginResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                roleList_ = new java.util.ArrayList<com.dxx.game.dto.ServerListProto.RoleDetailDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              roleList_.add(
                  input.readMessage(com.dxx.game.dto.ServerListProto.RoleDetailDto.parser(), extensionRegistry));
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                serverList_ = com.google.protobuf.MapField.newMapField(
                    ServerListDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
              serverList__ = input.readMessage(
                  ServerListDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              serverList_.getMutableMap().put(
                  serverList__.getKey(), serverList__.getValue());
              break;
            }
            case 34: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          roleList_ = java.util.Collections.unmodifiableList(roleList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetServerList();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.class, com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ROLELIST_FIELD_NUMBER = 2;
    private java.util.List<com.dxx.game.dto.ServerListProto.RoleDetailDto> roleList_;
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.ServerListProto.RoleDetailDto> getRoleListList() {
      return roleList_;
    }
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder> 
        getRoleListOrBuilderList() {
      return roleList_;
    }
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    @java.lang.Override
    public int getRoleListCount() {
      return roleList_.size();
    }
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.RoleDetailDto getRoleList(int index) {
      return roleList_.get(index);
    }
    /**
     * <pre>
     *角色列表
     * </pre>
     *
     * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder getRoleListOrBuilder(
        int index) {
      return roleList_.get(index);
    }

    public static final int SERVERLIST_FIELD_NUMBER = 3;
    private static final class ServerListDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>newDefaultInstance(
                  com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginResponse_ServerListEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.dxx.game.dto.ServerListProto.ZoneInfoDto.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> serverList_;
    private com.google.protobuf.MapField<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
    internalGetServerList() {
      if (serverList_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ServerListDefaultEntryHolder.defaultEntry);
      }
      return serverList_;
    }

    public int getServerListCount() {
      return internalGetServerList().getMap().size();
    }
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */

    @java.lang.Override
    public boolean containsServerList(
        int key) {
      
      return internalGetServerList().getMap().containsKey(key);
    }
    /**
     * Use {@link #getServerListMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> getServerList() {
      return getServerListMap();
    }
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> getServerListMap() {
      return internalGetServerList().getMap();
    }
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */
    @java.lang.Override

    public com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerListOrDefault(
        int key,
        com.dxx.game.dto.ServerListProto.ZoneInfoDto defaultValue) {
      
      java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> map =
          internalGetServerList().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *zoneId-&gt;ZoneInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
     */
    @java.lang.Override

    public com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerListOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> map =
          internalGetServerList().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int COMMONDATA_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      for (int i = 0; i < roleList_.size(); i++) {
        output.writeMessage(2, roleList_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetServerList(),
          ServerListDefaultEntryHolder.defaultEntry,
          3);
      if (commonData_ != null) {
        output.writeMessage(4, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      for (int i = 0; i < roleList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, roleList_.get(i));
      }
      for (java.util.Map.Entry<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> entry
           : internalGetServerList().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
        serverList__ = ServerListDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, serverList__);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse other = (com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getRoleListList()
          .equals(other.getRoleListList())) return false;
      if (!internalGetServerList().equals(
          other.internalGetServerList())) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getRoleListCount() > 0) {
        hash = (37 * hash) + ROLELIST_FIELD_NUMBER;
        hash = (53 * hash) + getRoleListList().hashCode();
      }
      if (!internalGetServerList().getMap().isEmpty()) {
        hash = (37 * hash) + SERVERLIST_FIELD_NUMBER;
        hash = (53 * hash) + internalGetServerList().hashCode();
      }
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10142 响应
     * </pre>
     *
     * Protobuf type {@code Proto.ServerList.UserGetLastLoginResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.UserGetLastLoginResponse)
        com.dxx.game.dto.ServerListProto.UserGetLastLoginResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetServerList();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableServerList();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.class, com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRoleListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (roleListBuilder_ == null) {
          roleList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          roleListBuilder_.clear();
        }
        internalGetMutableServerList().clear();
        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse build() {
        com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse buildPartial() {
        com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse result = new com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (roleListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            roleList_ = java.util.Collections.unmodifiableList(roleList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.roleList_ = roleList_;
        } else {
          result.roleList_ = roleListBuilder_.build();
        }
        result.serverList_ = internalGetServerList();
        result.serverList_.makeImmutable();
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse other) {
        if (other == com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (roleListBuilder_ == null) {
          if (!other.roleList_.isEmpty()) {
            if (roleList_.isEmpty()) {
              roleList_ = other.roleList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRoleListIsMutable();
              roleList_.addAll(other.roleList_);
            }
            onChanged();
          }
        } else {
          if (!other.roleList_.isEmpty()) {
            if (roleListBuilder_.isEmpty()) {
              roleListBuilder_.dispose();
              roleListBuilder_ = null;
              roleList_ = other.roleList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              roleListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRoleListFieldBuilder() : null;
            } else {
              roleListBuilder_.addAllMessages(other.roleList_);
            }
          }
        }
        internalGetMutableServerList().mergeFrom(
            other.internalGetServerList());
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.ServerListProto.RoleDetailDto> roleList_ =
        java.util.Collections.emptyList();
      private void ensureRoleListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          roleList_ = new java.util.ArrayList<com.dxx.game.dto.ServerListProto.RoleDetailDto>(roleList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.ServerListProto.RoleDetailDto, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder, com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder> roleListBuilder_;

      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.ServerListProto.RoleDetailDto> getRoleListList() {
        if (roleListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(roleList_);
        } else {
          return roleListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public int getRoleListCount() {
        if (roleListBuilder_ == null) {
          return roleList_.size();
        } else {
          return roleListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.RoleDetailDto getRoleList(int index) {
        if (roleListBuilder_ == null) {
          return roleList_.get(index);
        } else {
          return roleListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder setRoleList(
          int index, com.dxx.game.dto.ServerListProto.RoleDetailDto value) {
        if (roleListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleListIsMutable();
          roleList_.set(index, value);
          onChanged();
        } else {
          roleListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder setRoleList(
          int index, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder builderForValue) {
        if (roleListBuilder_ == null) {
          ensureRoleListIsMutable();
          roleList_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder addRoleList(com.dxx.game.dto.ServerListProto.RoleDetailDto value) {
        if (roleListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleListIsMutable();
          roleList_.add(value);
          onChanged();
        } else {
          roleListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder addRoleList(
          int index, com.dxx.game.dto.ServerListProto.RoleDetailDto value) {
        if (roleListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleListIsMutable();
          roleList_.add(index, value);
          onChanged();
        } else {
          roleListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder addRoleList(
          com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder builderForValue) {
        if (roleListBuilder_ == null) {
          ensureRoleListIsMutable();
          roleList_.add(builderForValue.build());
          onChanged();
        } else {
          roleListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder addRoleList(
          int index, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder builderForValue) {
        if (roleListBuilder_ == null) {
          ensureRoleListIsMutable();
          roleList_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder addAllRoleList(
          java.lang.Iterable<? extends com.dxx.game.dto.ServerListProto.RoleDetailDto> values) {
        if (roleListBuilder_ == null) {
          ensureRoleListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, roleList_);
          onChanged();
        } else {
          roleListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder clearRoleList() {
        if (roleListBuilder_ == null) {
          roleList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          roleListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public Builder removeRoleList(int index) {
        if (roleListBuilder_ == null) {
          ensureRoleListIsMutable();
          roleList_.remove(index);
          onChanged();
        } else {
          roleListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder getRoleListBuilder(
          int index) {
        return getRoleListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder getRoleListOrBuilder(
          int index) {
        if (roleListBuilder_ == null) {
          return roleList_.get(index);  } else {
          return roleListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder> 
           getRoleListOrBuilderList() {
        if (roleListBuilder_ != null) {
          return roleListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(roleList_);
        }
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder addRoleListBuilder() {
        return getRoleListFieldBuilder().addBuilder(
            com.dxx.game.dto.ServerListProto.RoleDetailDto.getDefaultInstance());
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder addRoleListBuilder(
          int index) {
        return getRoleListFieldBuilder().addBuilder(
            index, com.dxx.game.dto.ServerListProto.RoleDetailDto.getDefaultInstance());
      }
      /**
       * <pre>
       *角色列表
       * </pre>
       *
       * <code>repeated .Proto.ServerList.RoleDetailDto roleList = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder> 
           getRoleListBuilderList() {
        return getRoleListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.ServerListProto.RoleDetailDto, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder, com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder> 
          getRoleListFieldBuilder() {
        if (roleListBuilder_ == null) {
          roleListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.ServerListProto.RoleDetailDto, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder, com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder>(
                  roleList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          roleList_ = null;
        }
        return roleListBuilder_;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> serverList_;
      private com.google.protobuf.MapField<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
      internalGetServerList() {
        if (serverList_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ServerListDefaultEntryHolder.defaultEntry);
        }
        return serverList_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
      internalGetMutableServerList() {
        onChanged();;
        if (serverList_ == null) {
          serverList_ = com.google.protobuf.MapField.newMapField(
              ServerListDefaultEntryHolder.defaultEntry);
        }
        if (!serverList_.isMutable()) {
          serverList_ = serverList_.copy();
        }
        return serverList_;
      }

      public int getServerListCount() {
        return internalGetServerList().getMap().size();
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */

      @java.lang.Override
      public boolean containsServerList(
          int key) {
        
        return internalGetServerList().getMap().containsKey(key);
      }
      /**
       * Use {@link #getServerListMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> getServerList() {
        return getServerListMap();
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> getServerListMap() {
        return internalGetServerList().getMap();
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */
      @java.lang.Override

      public com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerListOrDefault(
          int key,
          com.dxx.game.dto.ServerListProto.ZoneInfoDto defaultValue) {
        
        java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> map =
            internalGetServerList().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */
      @java.lang.Override

      public com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerListOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> map =
            internalGetServerList().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearServerList() {
        internalGetMutableServerList().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */

      public Builder removeServerList(
          int key) {
        
        internalGetMutableServerList().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto>
      getMutableServerList() {
        return internalGetMutableServerList().getMutableMap();
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */
      public Builder putServerList(
          int key,
          com.dxx.game.dto.ServerListProto.ZoneInfoDto value) {
        
        if (value == null) {
  throw new NullPointerException("map value");
}

        internalGetMutableServerList().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *zoneId-&gt;ZoneInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ZoneInfoDto&gt; serverList = 3;</code>
       */

      public Builder putAllServerList(
          java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ZoneInfoDto> values) {
        internalGetMutableServerList().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.UserGetLastLoginResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.UserGetLastLoginResponse)
    private static final com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse();
    }

    public static com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UserGetLastLoginResponse>
        PARSER = new com.google.protobuf.AbstractParser<UserGetLastLoginResponse>() {
      @java.lang.Override
      public UserGetLastLoginResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UserGetLastLoginResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UserGetLastLoginResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UserGetLastLoginResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FindServerListRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.FindServerListRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>uint32 groupId = 2;</code>
     * @return The groupId.
     */
    int getGroupId();
  }
  /**
   * <pre>
   *CMD PackageId=10143 请求服务器列表
   * </pre>
   *
   * Protobuf type {@code Proto.ServerList.FindServerListRequest}
   */
  public static final class FindServerListRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.FindServerListRequest)
      FindServerListRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FindServerListRequest.newBuilder() to construct.
    private FindServerListRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FindServerListRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FindServerListRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FindServerListRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              groupId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.FindServerListRequest.class, com.dxx.game.dto.ServerListProto.FindServerListRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int GROUPID_FIELD_NUMBER = 2;
    private int groupId_;
    /**
     * <code>uint32 groupId = 2;</code>
     * @return The groupId.
     */
    @java.lang.Override
    public int getGroupId() {
      return groupId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (groupId_ != 0) {
        output.writeUInt32(2, groupId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (groupId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, groupId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.FindServerListRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.FindServerListRequest other = (com.dxx.game.dto.ServerListProto.FindServerListRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getGroupId()
          != other.getGroupId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + GROUPID_FIELD_NUMBER;
      hash = (53 * hash) + getGroupId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.FindServerListRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10143 请求服务器列表
     * </pre>
     *
     * Protobuf type {@code Proto.ServerList.FindServerListRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.FindServerListRequest)
        com.dxx.game.dto.ServerListProto.FindServerListRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.FindServerListRequest.class, com.dxx.game.dto.ServerListProto.FindServerListRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.FindServerListRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        groupId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.FindServerListRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.FindServerListRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.FindServerListRequest build() {
        com.dxx.game.dto.ServerListProto.FindServerListRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.FindServerListRequest buildPartial() {
        com.dxx.game.dto.ServerListProto.FindServerListRequest result = new com.dxx.game.dto.ServerListProto.FindServerListRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.groupId_ = groupId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.FindServerListRequest) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.FindServerListRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.FindServerListRequest other) {
        if (other == com.dxx.game.dto.ServerListProto.FindServerListRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getGroupId() != 0) {
          setGroupId(other.getGroupId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.FindServerListRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.FindServerListRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int groupId_ ;
      /**
       * <code>uint32 groupId = 2;</code>
       * @return The groupId.
       */
      @java.lang.Override
      public int getGroupId() {
        return groupId_;
      }
      /**
       * <code>uint32 groupId = 2;</code>
       * @param value The groupId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupId(int value) {
        
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 groupId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupId() {
        
        groupId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.FindServerListRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.FindServerListRequest)
    private static final com.dxx.game.dto.ServerListProto.FindServerListRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.FindServerListRequest();
    }

    public static com.dxx.game.dto.ServerListProto.FindServerListRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FindServerListRequest>
        PARSER = new com.google.protobuf.AbstractParser<FindServerListRequest>() {
      @java.lang.Override
      public FindServerListRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FindServerListRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FindServerListRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FindServerListRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.FindServerListRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FindServerListResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.FindServerListResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * </pre>
     *
     * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
     * @return Whether the serverInfoDto field is set.
     */
    boolean hasServerInfoDto();
    /**
     * <pre>
     * </pre>
     *
     * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
     * @return The serverInfoDto.
     */
    com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerInfoDto();
    /**
     * <pre>
     * </pre>
     *
     * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
     */
    com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder getServerInfoDtoOrBuilder();

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=10144 响应
   * </pre>
   *
   * Protobuf type {@code Proto.ServerList.FindServerListResponse}
   */
  public static final class FindServerListResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.FindServerListResponse)
      FindServerListResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FindServerListResponse.newBuilder() to construct.
    private FindServerListResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FindServerListResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FindServerListResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FindServerListResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder subBuilder = null;
              if (serverInfoDto_ != null) {
                subBuilder = serverInfoDto_.toBuilder();
              }
              serverInfoDto_ = input.readMessage(com.dxx.game.dto.ServerListProto.ZoneInfoDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(serverInfoDto_);
                serverInfoDto_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.FindServerListResponse.class, com.dxx.game.dto.ServerListProto.FindServerListResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SERVERINFODTO_FIELD_NUMBER = 2;
    private com.dxx.game.dto.ServerListProto.ZoneInfoDto serverInfoDto_;
    /**
     * <pre>
     * </pre>
     *
     * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
     * @return Whether the serverInfoDto field is set.
     */
    @java.lang.Override
    public boolean hasServerInfoDto() {
      return serverInfoDto_ != null;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
     * @return The serverInfoDto.
     */
    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerInfoDto() {
      return serverInfoDto_ == null ? com.dxx.game.dto.ServerListProto.ZoneInfoDto.getDefaultInstance() : serverInfoDto_;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder getServerInfoDtoOrBuilder() {
      return getServerInfoDto();
    }

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (serverInfoDto_ != null) {
        output.writeMessage(2, getServerInfoDto());
      }
      if (commonData_ != null) {
        output.writeMessage(3, getCommonData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (serverInfoDto_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getServerInfoDto());
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.FindServerListResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.FindServerListResponse other = (com.dxx.game.dto.ServerListProto.FindServerListResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasServerInfoDto() != other.hasServerInfoDto()) return false;
      if (hasServerInfoDto()) {
        if (!getServerInfoDto()
            .equals(other.getServerInfoDto())) return false;
      }
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasServerInfoDto()) {
        hash = (37 * hash) + SERVERINFODTO_FIELD_NUMBER;
        hash = (53 * hash) + getServerInfoDto().hashCode();
      }
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.FindServerListResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.FindServerListResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=10144 响应
     * </pre>
     *
     * Protobuf type {@code Proto.ServerList.FindServerListResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.FindServerListResponse)
        com.dxx.game.dto.ServerListProto.FindServerListResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.FindServerListResponse.class, com.dxx.game.dto.ServerListProto.FindServerListResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.FindServerListResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (serverInfoDtoBuilder_ == null) {
          serverInfoDto_ = null;
        } else {
          serverInfoDto_ = null;
          serverInfoDtoBuilder_ = null;
        }
        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_FindServerListResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.FindServerListResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.FindServerListResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.FindServerListResponse build() {
        com.dxx.game.dto.ServerListProto.FindServerListResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.FindServerListResponse buildPartial() {
        com.dxx.game.dto.ServerListProto.FindServerListResponse result = new com.dxx.game.dto.ServerListProto.FindServerListResponse(this);
        result.code_ = code_;
        if (serverInfoDtoBuilder_ == null) {
          result.serverInfoDto_ = serverInfoDto_;
        } else {
          result.serverInfoDto_ = serverInfoDtoBuilder_.build();
        }
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.FindServerListResponse) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.FindServerListResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.FindServerListResponse other) {
        if (other == com.dxx.game.dto.ServerListProto.FindServerListResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasServerInfoDto()) {
          mergeServerInfoDto(other.getServerInfoDto());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.FindServerListResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.FindServerListResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.ServerListProto.ZoneInfoDto serverInfoDto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.ServerListProto.ZoneInfoDto, com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder, com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder> serverInfoDtoBuilder_;
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       * @return Whether the serverInfoDto field is set.
       */
      public boolean hasServerInfoDto() {
        return serverInfoDtoBuilder_ != null || serverInfoDto_ != null;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       * @return The serverInfoDto.
       */
      public com.dxx.game.dto.ServerListProto.ZoneInfoDto getServerInfoDto() {
        if (serverInfoDtoBuilder_ == null) {
          return serverInfoDto_ == null ? com.dxx.game.dto.ServerListProto.ZoneInfoDto.getDefaultInstance() : serverInfoDto_;
        } else {
          return serverInfoDtoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      public Builder setServerInfoDto(com.dxx.game.dto.ServerListProto.ZoneInfoDto value) {
        if (serverInfoDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          serverInfoDto_ = value;
          onChanged();
        } else {
          serverInfoDtoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      public Builder setServerInfoDto(
          com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder builderForValue) {
        if (serverInfoDtoBuilder_ == null) {
          serverInfoDto_ = builderForValue.build();
          onChanged();
        } else {
          serverInfoDtoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      public Builder mergeServerInfoDto(com.dxx.game.dto.ServerListProto.ZoneInfoDto value) {
        if (serverInfoDtoBuilder_ == null) {
          if (serverInfoDto_ != null) {
            serverInfoDto_ =
              com.dxx.game.dto.ServerListProto.ZoneInfoDto.newBuilder(serverInfoDto_).mergeFrom(value).buildPartial();
          } else {
            serverInfoDto_ = value;
          }
          onChanged();
        } else {
          serverInfoDtoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      public Builder clearServerInfoDto() {
        if (serverInfoDtoBuilder_ == null) {
          serverInfoDto_ = null;
          onChanged();
        } else {
          serverInfoDto_ = null;
          serverInfoDtoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder getServerInfoDtoBuilder() {
        
        onChanged();
        return getServerInfoDtoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      public com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder getServerInfoDtoOrBuilder() {
        if (serverInfoDtoBuilder_ != null) {
          return serverInfoDtoBuilder_.getMessageOrBuilder();
        } else {
          return serverInfoDto_ == null ?
              com.dxx.game.dto.ServerListProto.ZoneInfoDto.getDefaultInstance() : serverInfoDto_;
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>.Proto.ServerList.ZoneInfoDto serverInfoDto = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.ServerListProto.ZoneInfoDto, com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder, com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder> 
          getServerInfoDtoFieldBuilder() {
        if (serverInfoDtoBuilder_ == null) {
          serverInfoDtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.ServerListProto.ZoneInfoDto, com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder, com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder>(
                  getServerInfoDto(),
                  getParentForChildren(),
                  isClean());
          serverInfoDto_ = null;
        }
        return serverInfoDtoBuilder_;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.FindServerListResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.FindServerListResponse)
    private static final com.dxx.game.dto.ServerListProto.FindServerListResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.FindServerListResponse();
    }

    public static com.dxx.game.dto.ServerListProto.FindServerListResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FindServerListResponse>
        PARSER = new com.google.protobuf.AbstractParser<FindServerListResponse>() {
      @java.lang.Override
      public FindServerListResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FindServerListResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FindServerListResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FindServerListResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.FindServerListResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RoleDetailDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.RoleDetailDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string nickName = 1;</code>
     * @return The nickName.
     */
    java.lang.String getNickName();
    /**
     * <code>string nickName = 1;</code>
     * @return The bytes for nickName.
     */
    com.google.protobuf.ByteString
        getNickNameBytes();

    /**
     * <code>uint32 groupId = 2;</code>
     * @return The groupId.
     */
    int getGroupId();

    /**
     * <code>uint32 avatar = 3;</code>
     * @return The avatar.
     */
    int getAvatar();

    /**
     * <code>uint32 avatarFrame = 4;</code>
     * @return The avatarFrame.
     */
    int getAvatarFrame();

    /**
     * <code>uint32 serverId = 5;</code>
     * @return The serverId.
     */
    int getServerId();

    /**
     * <code>uint64 power = 6;</code>
     * @return The power.
     */
    long getPower();

    /**
     * <pre>
     *距离最近一次登录的时间差
     * </pre>
     *
     * <code>uint64 lastLoginPass = 7;</code>
     * @return The lastLoginPass.
     */
    long getLastLoginPass();

    /**
     * <code>uint64 userId = 8;</code>
     * @return The userId.
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code Proto.ServerList.RoleDetailDto}
   */
  public static final class RoleDetailDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.RoleDetailDto)
      RoleDetailDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RoleDetailDto.newBuilder() to construct.
    private RoleDetailDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RoleDetailDto() {
      nickName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RoleDetailDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RoleDetailDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              nickName_ = s;
              break;
            }
            case 16: {

              groupId_ = input.readUInt32();
              break;
            }
            case 24: {

              avatar_ = input.readUInt32();
              break;
            }
            case 32: {

              avatarFrame_ = input.readUInt32();
              break;
            }
            case 40: {

              serverId_ = input.readUInt32();
              break;
            }
            case 48: {

              power_ = input.readUInt64();
              break;
            }
            case 56: {

              lastLoginPass_ = input.readUInt64();
              break;
            }
            case 64: {

              userId_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_RoleDetailDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_RoleDetailDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.RoleDetailDto.class, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder.class);
    }

    public static final int NICKNAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object nickName_;
    /**
     * <code>string nickName = 1;</code>
     * @return The nickName.
     */
    @java.lang.Override
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickName_ = s;
        return s;
      }
    }
    /**
     * <code>string nickName = 1;</code>
     * @return The bytes for nickName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GROUPID_FIELD_NUMBER = 2;
    private int groupId_;
    /**
     * <code>uint32 groupId = 2;</code>
     * @return The groupId.
     */
    @java.lang.Override
    public int getGroupId() {
      return groupId_;
    }

    public static final int AVATAR_FIELD_NUMBER = 3;
    private int avatar_;
    /**
     * <code>uint32 avatar = 3;</code>
     * @return The avatar.
     */
    @java.lang.Override
    public int getAvatar() {
      return avatar_;
    }

    public static final int AVATARFRAME_FIELD_NUMBER = 4;
    private int avatarFrame_;
    /**
     * <code>uint32 avatarFrame = 4;</code>
     * @return The avatarFrame.
     */
    @java.lang.Override
    public int getAvatarFrame() {
      return avatarFrame_;
    }

    public static final int SERVERID_FIELD_NUMBER = 5;
    private int serverId_;
    /**
     * <code>uint32 serverId = 5;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }

    public static final int POWER_FIELD_NUMBER = 6;
    private long power_;
    /**
     * <code>uint64 power = 6;</code>
     * @return The power.
     */
    @java.lang.Override
    public long getPower() {
      return power_;
    }

    public static final int LASTLOGINPASS_FIELD_NUMBER = 7;
    private long lastLoginPass_;
    /**
     * <pre>
     *距离最近一次登录的时间差
     * </pre>
     *
     * <code>uint64 lastLoginPass = 7;</code>
     * @return The lastLoginPass.
     */
    @java.lang.Override
    public long getLastLoginPass() {
      return lastLoginPass_;
    }

    public static final int USERID_FIELD_NUMBER = 8;
    private long userId_;
    /**
     * <code>uint64 userId = 8;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nickName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, nickName_);
      }
      if (groupId_ != 0) {
        output.writeUInt32(2, groupId_);
      }
      if (avatar_ != 0) {
        output.writeUInt32(3, avatar_);
      }
      if (avatarFrame_ != 0) {
        output.writeUInt32(4, avatarFrame_);
      }
      if (serverId_ != 0) {
        output.writeUInt32(5, serverId_);
      }
      if (power_ != 0L) {
        output.writeUInt64(6, power_);
      }
      if (lastLoginPass_ != 0L) {
        output.writeUInt64(7, lastLoginPass_);
      }
      if (userId_ != 0L) {
        output.writeUInt64(8, userId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nickName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, nickName_);
      }
      if (groupId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, groupId_);
      }
      if (avatar_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, avatar_);
      }
      if (avatarFrame_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, avatarFrame_);
      }
      if (serverId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, serverId_);
      }
      if (power_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, power_);
      }
      if (lastLoginPass_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, lastLoginPass_);
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(8, userId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.RoleDetailDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.RoleDetailDto other = (com.dxx.game.dto.ServerListProto.RoleDetailDto) obj;

      if (!getNickName()
          .equals(other.getNickName())) return false;
      if (getGroupId()
          != other.getGroupId()) return false;
      if (getAvatar()
          != other.getAvatar()) return false;
      if (getAvatarFrame()
          != other.getAvatarFrame()) return false;
      if (getServerId()
          != other.getServerId()) return false;
      if (getPower()
          != other.getPower()) return false;
      if (getLastLoginPass()
          != other.getLastLoginPass()) return false;
      if (getUserId()
          != other.getUserId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
      hash = (37 * hash) + GROUPID_FIELD_NUMBER;
      hash = (53 * hash) + getGroupId();
      hash = (37 * hash) + AVATAR_FIELD_NUMBER;
      hash = (53 * hash) + getAvatar();
      hash = (37 * hash) + AVATARFRAME_FIELD_NUMBER;
      hash = (53 * hash) + getAvatarFrame();
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getServerId();
      hash = (37 * hash) + POWER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPower());
      hash = (37 * hash) + LASTLOGINPASS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastLoginPass());
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.RoleDetailDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.RoleDetailDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.ServerList.RoleDetailDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.RoleDetailDto)
        com.dxx.game.dto.ServerListProto.RoleDetailDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_RoleDetailDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_RoleDetailDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.RoleDetailDto.class, com.dxx.game.dto.ServerListProto.RoleDetailDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.RoleDetailDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nickName_ = "";

        groupId_ = 0;

        avatar_ = 0;

        avatarFrame_ = 0;

        serverId_ = 0;

        power_ = 0L;

        lastLoginPass_ = 0L;

        userId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_RoleDetailDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.RoleDetailDto getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.RoleDetailDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.RoleDetailDto build() {
        com.dxx.game.dto.ServerListProto.RoleDetailDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.RoleDetailDto buildPartial() {
        com.dxx.game.dto.ServerListProto.RoleDetailDto result = new com.dxx.game.dto.ServerListProto.RoleDetailDto(this);
        result.nickName_ = nickName_;
        result.groupId_ = groupId_;
        result.avatar_ = avatar_;
        result.avatarFrame_ = avatarFrame_;
        result.serverId_ = serverId_;
        result.power_ = power_;
        result.lastLoginPass_ = lastLoginPass_;
        result.userId_ = userId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.RoleDetailDto) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.RoleDetailDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.RoleDetailDto other) {
        if (other == com.dxx.game.dto.ServerListProto.RoleDetailDto.getDefaultInstance()) return this;
        if (!other.getNickName().isEmpty()) {
          nickName_ = other.nickName_;
          onChanged();
        }
        if (other.getGroupId() != 0) {
          setGroupId(other.getGroupId());
        }
        if (other.getAvatar() != 0) {
          setAvatar(other.getAvatar());
        }
        if (other.getAvatarFrame() != 0) {
          setAvatarFrame(other.getAvatarFrame());
        }
        if (other.getServerId() != 0) {
          setServerId(other.getServerId());
        }
        if (other.getPower() != 0L) {
          setPower(other.getPower());
        }
        if (other.getLastLoginPass() != 0L) {
          setLastLoginPass(other.getLastLoginPass());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.RoleDetailDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.RoleDetailDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object nickName_ = "";
      /**
       * <code>string nickName = 1;</code>
       * @return The nickName.
       */
      public java.lang.String getNickName() {
        java.lang.Object ref = nickName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          nickName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string nickName = 1;</code>
       * @return The bytes for nickName.
       */
      public com.google.protobuf.ByteString
          getNickNameBytes() {
        java.lang.Object ref = nickName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          nickName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string nickName = 1;</code>
       * @param value The nickName to set.
       * @return This builder for chaining.
       */
      public Builder setNickName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        nickName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string nickName = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNickName() {
        
        nickName_ = getDefaultInstance().getNickName();
        onChanged();
        return this;
      }
      /**
       * <code>string nickName = 1;</code>
       * @param value The bytes for nickName to set.
       * @return This builder for chaining.
       */
      public Builder setNickNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        nickName_ = value;
        onChanged();
        return this;
      }

      private int groupId_ ;
      /**
       * <code>uint32 groupId = 2;</code>
       * @return The groupId.
       */
      @java.lang.Override
      public int getGroupId() {
        return groupId_;
      }
      /**
       * <code>uint32 groupId = 2;</code>
       * @param value The groupId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupId(int value) {
        
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 groupId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupId() {
        
        groupId_ = 0;
        onChanged();
        return this;
      }

      private int avatar_ ;
      /**
       * <code>uint32 avatar = 3;</code>
       * @return The avatar.
       */
      @java.lang.Override
      public int getAvatar() {
        return avatar_;
      }
      /**
       * <code>uint32 avatar = 3;</code>
       * @param value The avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatar(int value) {
        
        avatar_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 avatar = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatar() {
        
        avatar_ = 0;
        onChanged();
        return this;
      }

      private int avatarFrame_ ;
      /**
       * <code>uint32 avatarFrame = 4;</code>
       * @return The avatarFrame.
       */
      @java.lang.Override
      public int getAvatarFrame() {
        return avatarFrame_;
      }
      /**
       * <code>uint32 avatarFrame = 4;</code>
       * @param value The avatarFrame to set.
       * @return This builder for chaining.
       */
      public Builder setAvatarFrame(int value) {
        
        avatarFrame_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 avatarFrame = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatarFrame() {
        
        avatarFrame_ = 0;
        onChanged();
        return this;
      }

      private int serverId_ ;
      /**
       * <code>uint32 serverId = 5;</code>
       * @return The serverId.
       */
      @java.lang.Override
      public int getServerId() {
        return serverId_;
      }
      /**
       * <code>uint32 serverId = 5;</code>
       * @param value The serverId to set.
       * @return This builder for chaining.
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 serverId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }

      private long power_ ;
      /**
       * <code>uint64 power = 6;</code>
       * @return The power.
       */
      @java.lang.Override
      public long getPower() {
        return power_;
      }
      /**
       * <code>uint64 power = 6;</code>
       * @param value The power to set.
       * @return This builder for chaining.
       */
      public Builder setPower(long value) {
        
        power_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 power = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearPower() {
        
        power_ = 0L;
        onChanged();
        return this;
      }

      private long lastLoginPass_ ;
      /**
       * <pre>
       *距离最近一次登录的时间差
       * </pre>
       *
       * <code>uint64 lastLoginPass = 7;</code>
       * @return The lastLoginPass.
       */
      @java.lang.Override
      public long getLastLoginPass() {
        return lastLoginPass_;
      }
      /**
       * <pre>
       *距离最近一次登录的时间差
       * </pre>
       *
       * <code>uint64 lastLoginPass = 7;</code>
       * @param value The lastLoginPass to set.
       * @return This builder for chaining.
       */
      public Builder setLastLoginPass(long value) {
        
        lastLoginPass_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *距离最近一次登录的时间差
       * </pre>
       *
       * <code>uint64 lastLoginPass = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastLoginPass() {
        
        lastLoginPass_ = 0L;
        onChanged();
        return this;
      }

      private long userId_ ;
      /**
       * <code>uint64 userId = 8;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>uint64 userId = 8;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {
        
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 userId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        
        userId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.RoleDetailDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.RoleDetailDto)
    private static final com.dxx.game.dto.ServerListProto.RoleDetailDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.RoleDetailDto();
    }

    public static com.dxx.game.dto.ServerListProto.RoleDetailDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RoleDetailDto>
        PARSER = new com.google.protobuf.AbstractParser<RoleDetailDto>() {
      @java.lang.Override
      public RoleDetailDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RoleDetailDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RoleDetailDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RoleDetailDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.RoleDetailDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ZoneInfoDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.ZoneInfoDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *当前大区已开放最大服务器
     * </pre>
     *
     * <code>uint32 maxServer = 1;</code>
     * @return The maxServer.
     */
    int getMaxServer();

    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */
    int getServerListCount();
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */
    boolean containsServerList(
        int key);
    /**
     * Use {@link #getServerListMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
    getServerList();
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */
    java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
    getServerListMap();
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */

    com.dxx.game.dto.ServerListProto.ServerGroupDto getServerListOrDefault(
        int key,
        com.dxx.game.dto.ServerListProto.ServerGroupDto defaultValue);
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */

    com.dxx.game.dto.ServerListProto.ServerGroupDto getServerListOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code Proto.ServerList.ZoneInfoDto}
   */
  public static final class ZoneInfoDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.ZoneInfoDto)
      ZoneInfoDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ZoneInfoDto.newBuilder() to construct.
    private ZoneInfoDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ZoneInfoDto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ZoneInfoDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ZoneInfoDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              maxServer_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                serverList_ = com.google.protobuf.MapField.newMapField(
                    ServerListDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
              serverList__ = input.readMessage(
                  ServerListDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              serverList_.getMutableMap().put(
                  serverList__.getKey(), serverList__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ZoneInfoDto_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetServerList();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ZoneInfoDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.ZoneInfoDto.class, com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder.class);
    }

    public static final int MAXSERVER_FIELD_NUMBER = 1;
    private int maxServer_;
    /**
     * <pre>
     *当前大区已开放最大服务器
     * </pre>
     *
     * <code>uint32 maxServer = 1;</code>
     * @return The maxServer.
     */
    @java.lang.Override
    public int getMaxServer() {
      return maxServer_;
    }

    public static final int SERVERLIST_FIELD_NUMBER = 2;
    private static final class ServerListDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>newDefaultInstance(
                  com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ZoneInfoDto_ServerListEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.dxx.game.dto.ServerListProto.ServerGroupDto.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> serverList_;
    private com.google.protobuf.MapField<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
    internalGetServerList() {
      if (serverList_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ServerListDefaultEntryHolder.defaultEntry);
      }
      return serverList_;
    }

    public int getServerListCount() {
      return internalGetServerList().getMap().size();
    }
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */

    @java.lang.Override
    public boolean containsServerList(
        int key) {
      
      return internalGetServerList().getMap().containsKey(key);
    }
    /**
     * Use {@link #getServerListMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> getServerList() {
      return getServerListMap();
    }
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> getServerListMap() {
      return internalGetServerList().getMap();
    }
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */
    @java.lang.Override

    public com.dxx.game.dto.ServerListProto.ServerGroupDto getServerListOrDefault(
        int key,
        com.dxx.game.dto.ServerListProto.ServerGroupDto defaultValue) {
      
      java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> map =
          internalGetServerList().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *group-&gt;ServerInfoDto
     * </pre>
     *
     * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
     */
    @java.lang.Override

    public com.dxx.game.dto.ServerListProto.ServerGroupDto getServerListOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> map =
          internalGetServerList().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (maxServer_ != 0) {
        output.writeUInt32(1, maxServer_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetServerList(),
          ServerListDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (maxServer_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, maxServer_);
      }
      for (java.util.Map.Entry<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> entry
           : internalGetServerList().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
        serverList__ = ServerListDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, serverList__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.ZoneInfoDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.ZoneInfoDto other = (com.dxx.game.dto.ServerListProto.ZoneInfoDto) obj;

      if (getMaxServer()
          != other.getMaxServer()) return false;
      if (!internalGetServerList().equals(
          other.internalGetServerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MAXSERVER_FIELD_NUMBER;
      hash = (53 * hash) + getMaxServer();
      if (!internalGetServerList().getMap().isEmpty()) {
        hash = (37 * hash) + SERVERLIST_FIELD_NUMBER;
        hash = (53 * hash) + internalGetServerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.ZoneInfoDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.ServerList.ZoneInfoDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.ZoneInfoDto)
        com.dxx.game.dto.ServerListProto.ZoneInfoDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ZoneInfoDto_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetServerList();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableServerList();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ZoneInfoDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.ZoneInfoDto.class, com.dxx.game.dto.ServerListProto.ZoneInfoDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.ZoneInfoDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        maxServer_ = 0;

        internalGetMutableServerList().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ZoneInfoDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ZoneInfoDto getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.ZoneInfoDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ZoneInfoDto build() {
        com.dxx.game.dto.ServerListProto.ZoneInfoDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ZoneInfoDto buildPartial() {
        com.dxx.game.dto.ServerListProto.ZoneInfoDto result = new com.dxx.game.dto.ServerListProto.ZoneInfoDto(this);
        int from_bitField0_ = bitField0_;
        result.maxServer_ = maxServer_;
        result.serverList_ = internalGetServerList();
        result.serverList_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.ZoneInfoDto) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.ZoneInfoDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.ZoneInfoDto other) {
        if (other == com.dxx.game.dto.ServerListProto.ZoneInfoDto.getDefaultInstance()) return this;
        if (other.getMaxServer() != 0) {
          setMaxServer(other.getMaxServer());
        }
        internalGetMutableServerList().mergeFrom(
            other.internalGetServerList());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.ZoneInfoDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.ZoneInfoDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int maxServer_ ;
      /**
       * <pre>
       *当前大区已开放最大服务器
       * </pre>
       *
       * <code>uint32 maxServer = 1;</code>
       * @return The maxServer.
       */
      @java.lang.Override
      public int getMaxServer() {
        return maxServer_;
      }
      /**
       * <pre>
       *当前大区已开放最大服务器
       * </pre>
       *
       * <code>uint32 maxServer = 1;</code>
       * @param value The maxServer to set.
       * @return This builder for chaining.
       */
      public Builder setMaxServer(int value) {
        
        maxServer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前大区已开放最大服务器
       * </pre>
       *
       * <code>uint32 maxServer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxServer() {
        
        maxServer_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> serverList_;
      private com.google.protobuf.MapField<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
      internalGetServerList() {
        if (serverList_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ServerListDefaultEntryHolder.defaultEntry);
        }
        return serverList_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
      internalGetMutableServerList() {
        onChanged();;
        if (serverList_ == null) {
          serverList_ = com.google.protobuf.MapField.newMapField(
              ServerListDefaultEntryHolder.defaultEntry);
        }
        if (!serverList_.isMutable()) {
          serverList_ = serverList_.copy();
        }
        return serverList_;
      }

      public int getServerListCount() {
        return internalGetServerList().getMap().size();
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */

      @java.lang.Override
      public boolean containsServerList(
          int key) {
        
        return internalGetServerList().getMap().containsKey(key);
      }
      /**
       * Use {@link #getServerListMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> getServerList() {
        return getServerListMap();
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> getServerListMap() {
        return internalGetServerList().getMap();
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */
      @java.lang.Override

      public com.dxx.game.dto.ServerListProto.ServerGroupDto getServerListOrDefault(
          int key,
          com.dxx.game.dto.ServerListProto.ServerGroupDto defaultValue) {
        
        java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> map =
            internalGetServerList().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */
      @java.lang.Override

      public com.dxx.game.dto.ServerListProto.ServerGroupDto getServerListOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> map =
            internalGetServerList().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearServerList() {
        internalGetMutableServerList().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */

      public Builder removeServerList(
          int key) {
        
        internalGetMutableServerList().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto>
      getMutableServerList() {
        return internalGetMutableServerList().getMutableMap();
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */
      public Builder putServerList(
          int key,
          com.dxx.game.dto.ServerListProto.ServerGroupDto value) {
        
        if (value == null) {
  throw new NullPointerException("map value");
}

        internalGetMutableServerList().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *group-&gt;ServerInfoDto
       * </pre>
       *
       * <code>map&lt;uint32, .Proto.ServerList.ServerGroupDto&gt; serverList = 2;</code>
       */

      public Builder putAllServerList(
          java.util.Map<java.lang.Integer, com.dxx.game.dto.ServerListProto.ServerGroupDto> values) {
        internalGetMutableServerList().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.ZoneInfoDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.ZoneInfoDto)
    private static final com.dxx.game.dto.ServerListProto.ZoneInfoDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.ZoneInfoDto();
    }

    public static com.dxx.game.dto.ServerListProto.ZoneInfoDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ZoneInfoDto>
        PARSER = new com.google.protobuf.AbstractParser<ZoneInfoDto>() {
      @java.lang.Override
      public ZoneInfoDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ZoneInfoDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ZoneInfoDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ZoneInfoDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ZoneInfoDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ServerGroupDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.ServerGroupDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 group = 1;</code>
     * @return The group.
     */
    int getGroup();

    /**
     * <code>uint32 startServer = 2;</code>
     * @return The startServer.
     */
    int getStartServer();

    /**
     * <code>uint32 endServer = 3;</code>
     * @return The endServer.
     */
    int getEndServer();

    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    java.util.List<com.dxx.game.dto.ServerListProto.ServerInfoDto> 
        getServerInfoDtoList();
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    com.dxx.game.dto.ServerListProto.ServerInfoDto getServerInfoDto(int index);
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    int getServerInfoDtoCount();
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder> 
        getServerInfoDtoOrBuilderList();
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder getServerInfoDtoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code Proto.ServerList.ServerGroupDto}
   */
  public static final class ServerGroupDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.ServerGroupDto)
      ServerGroupDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ServerGroupDto.newBuilder() to construct.
    private ServerGroupDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ServerGroupDto() {
      serverInfoDto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ServerGroupDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ServerGroupDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              group_ = input.readUInt32();
              break;
            }
            case 16: {

              startServer_ = input.readUInt32();
              break;
            }
            case 24: {

              endServer_ = input.readUInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                serverInfoDto_ = new java.util.ArrayList<com.dxx.game.dto.ServerListProto.ServerInfoDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              serverInfoDto_.add(
                  input.readMessage(com.dxx.game.dto.ServerListProto.ServerInfoDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          serverInfoDto_ = java.util.Collections.unmodifiableList(serverInfoDto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerGroupDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerGroupDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.ServerGroupDto.class, com.dxx.game.dto.ServerListProto.ServerGroupDto.Builder.class);
    }

    public static final int GROUP_FIELD_NUMBER = 1;
    private int group_;
    /**
     * <code>uint32 group = 1;</code>
     * @return The group.
     */
    @java.lang.Override
    public int getGroup() {
      return group_;
    }

    public static final int STARTSERVER_FIELD_NUMBER = 2;
    private int startServer_;
    /**
     * <code>uint32 startServer = 2;</code>
     * @return The startServer.
     */
    @java.lang.Override
    public int getStartServer() {
      return startServer_;
    }

    public static final int ENDSERVER_FIELD_NUMBER = 3;
    private int endServer_;
    /**
     * <code>uint32 endServer = 3;</code>
     * @return The endServer.
     */
    @java.lang.Override
    public int getEndServer() {
      return endServer_;
    }

    public static final int SERVERINFODTO_FIELD_NUMBER = 4;
    private java.util.List<com.dxx.game.dto.ServerListProto.ServerInfoDto> serverInfoDto_;
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.ServerListProto.ServerInfoDto> getServerInfoDtoList() {
      return serverInfoDto_;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder> 
        getServerInfoDtoOrBuilderList() {
      return serverInfoDto_;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    @java.lang.Override
    public int getServerInfoDtoCount() {
      return serverInfoDto_.size();
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ServerInfoDto getServerInfoDto(int index) {
      return serverInfoDto_.get(index);
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder getServerInfoDtoOrBuilder(
        int index) {
      return serverInfoDto_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (group_ != 0) {
        output.writeUInt32(1, group_);
      }
      if (startServer_ != 0) {
        output.writeUInt32(2, startServer_);
      }
      if (endServer_ != 0) {
        output.writeUInt32(3, endServer_);
      }
      for (int i = 0; i < serverInfoDto_.size(); i++) {
        output.writeMessage(4, serverInfoDto_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (group_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, group_);
      }
      if (startServer_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, startServer_);
      }
      if (endServer_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, endServer_);
      }
      for (int i = 0; i < serverInfoDto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, serverInfoDto_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.ServerGroupDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.ServerGroupDto other = (com.dxx.game.dto.ServerListProto.ServerGroupDto) obj;

      if (getGroup()
          != other.getGroup()) return false;
      if (getStartServer()
          != other.getStartServer()) return false;
      if (getEndServer()
          != other.getEndServer()) return false;
      if (!getServerInfoDtoList()
          .equals(other.getServerInfoDtoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GROUP_FIELD_NUMBER;
      hash = (53 * hash) + getGroup();
      hash = (37 * hash) + STARTSERVER_FIELD_NUMBER;
      hash = (53 * hash) + getStartServer();
      hash = (37 * hash) + ENDSERVER_FIELD_NUMBER;
      hash = (53 * hash) + getEndServer();
      if (getServerInfoDtoCount() > 0) {
        hash = (37 * hash) + SERVERINFODTO_FIELD_NUMBER;
        hash = (53 * hash) + getServerInfoDtoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ServerGroupDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.ServerGroupDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.ServerList.ServerGroupDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.ServerGroupDto)
        com.dxx.game.dto.ServerListProto.ServerGroupDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerGroupDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerGroupDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.ServerGroupDto.class, com.dxx.game.dto.ServerListProto.ServerGroupDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.ServerGroupDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getServerInfoDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        group_ = 0;

        startServer_ = 0;

        endServer_ = 0;

        if (serverInfoDtoBuilder_ == null) {
          serverInfoDto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          serverInfoDtoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerGroupDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ServerGroupDto getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.ServerGroupDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ServerGroupDto build() {
        com.dxx.game.dto.ServerListProto.ServerGroupDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ServerGroupDto buildPartial() {
        com.dxx.game.dto.ServerListProto.ServerGroupDto result = new com.dxx.game.dto.ServerListProto.ServerGroupDto(this);
        int from_bitField0_ = bitField0_;
        result.group_ = group_;
        result.startServer_ = startServer_;
        result.endServer_ = endServer_;
        if (serverInfoDtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            serverInfoDto_ = java.util.Collections.unmodifiableList(serverInfoDto_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.serverInfoDto_ = serverInfoDto_;
        } else {
          result.serverInfoDto_ = serverInfoDtoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.ServerGroupDto) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.ServerGroupDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.ServerGroupDto other) {
        if (other == com.dxx.game.dto.ServerListProto.ServerGroupDto.getDefaultInstance()) return this;
        if (other.getGroup() != 0) {
          setGroup(other.getGroup());
        }
        if (other.getStartServer() != 0) {
          setStartServer(other.getStartServer());
        }
        if (other.getEndServer() != 0) {
          setEndServer(other.getEndServer());
        }
        if (serverInfoDtoBuilder_ == null) {
          if (!other.serverInfoDto_.isEmpty()) {
            if (serverInfoDto_.isEmpty()) {
              serverInfoDto_ = other.serverInfoDto_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureServerInfoDtoIsMutable();
              serverInfoDto_.addAll(other.serverInfoDto_);
            }
            onChanged();
          }
        } else {
          if (!other.serverInfoDto_.isEmpty()) {
            if (serverInfoDtoBuilder_.isEmpty()) {
              serverInfoDtoBuilder_.dispose();
              serverInfoDtoBuilder_ = null;
              serverInfoDto_ = other.serverInfoDto_;
              bitField0_ = (bitField0_ & ~0x00000001);
              serverInfoDtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getServerInfoDtoFieldBuilder() : null;
            } else {
              serverInfoDtoBuilder_.addAllMessages(other.serverInfoDto_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.ServerGroupDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.ServerGroupDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int group_ ;
      /**
       * <code>uint32 group = 1;</code>
       * @return The group.
       */
      @java.lang.Override
      public int getGroup() {
        return group_;
      }
      /**
       * <code>uint32 group = 1;</code>
       * @param value The group to set.
       * @return This builder for chaining.
       */
      public Builder setGroup(int value) {
        
        group_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 group = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroup() {
        
        group_ = 0;
        onChanged();
        return this;
      }

      private int startServer_ ;
      /**
       * <code>uint32 startServer = 2;</code>
       * @return The startServer.
       */
      @java.lang.Override
      public int getStartServer() {
        return startServer_;
      }
      /**
       * <code>uint32 startServer = 2;</code>
       * @param value The startServer to set.
       * @return This builder for chaining.
       */
      public Builder setStartServer(int value) {
        
        startServer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 startServer = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartServer() {
        
        startServer_ = 0;
        onChanged();
        return this;
      }

      private int endServer_ ;
      /**
       * <code>uint32 endServer = 3;</code>
       * @return The endServer.
       */
      @java.lang.Override
      public int getEndServer() {
        return endServer_;
      }
      /**
       * <code>uint32 endServer = 3;</code>
       * @param value The endServer to set.
       * @return This builder for chaining.
       */
      public Builder setEndServer(int value) {
        
        endServer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 endServer = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndServer() {
        
        endServer_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.ServerListProto.ServerInfoDto> serverInfoDto_ =
        java.util.Collections.emptyList();
      private void ensureServerInfoDtoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          serverInfoDto_ = new java.util.ArrayList<com.dxx.game.dto.ServerListProto.ServerInfoDto>(serverInfoDto_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.ServerListProto.ServerInfoDto, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder, com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder> serverInfoDtoBuilder_;

      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.ServerListProto.ServerInfoDto> getServerInfoDtoList() {
        if (serverInfoDtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(serverInfoDto_);
        } else {
          return serverInfoDtoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public int getServerInfoDtoCount() {
        if (serverInfoDtoBuilder_ == null) {
          return serverInfoDto_.size();
        } else {
          return serverInfoDtoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public com.dxx.game.dto.ServerListProto.ServerInfoDto getServerInfoDto(int index) {
        if (serverInfoDtoBuilder_ == null) {
          return serverInfoDto_.get(index);
        } else {
          return serverInfoDtoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder setServerInfoDto(
          int index, com.dxx.game.dto.ServerListProto.ServerInfoDto value) {
        if (serverInfoDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.set(index, value);
          onChanged();
        } else {
          serverInfoDtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder setServerInfoDto(
          int index, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder builderForValue) {
        if (serverInfoDtoBuilder_ == null) {
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.set(index, builderForValue.build());
          onChanged();
        } else {
          serverInfoDtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder addServerInfoDto(com.dxx.game.dto.ServerListProto.ServerInfoDto value) {
        if (serverInfoDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.add(value);
          onChanged();
        } else {
          serverInfoDtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder addServerInfoDto(
          int index, com.dxx.game.dto.ServerListProto.ServerInfoDto value) {
        if (serverInfoDtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.add(index, value);
          onChanged();
        } else {
          serverInfoDtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder addServerInfoDto(
          com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder builderForValue) {
        if (serverInfoDtoBuilder_ == null) {
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.add(builderForValue.build());
          onChanged();
        } else {
          serverInfoDtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder addServerInfoDto(
          int index, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder builderForValue) {
        if (serverInfoDtoBuilder_ == null) {
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.add(index, builderForValue.build());
          onChanged();
        } else {
          serverInfoDtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder addAllServerInfoDto(
          java.lang.Iterable<? extends com.dxx.game.dto.ServerListProto.ServerInfoDto> values) {
        if (serverInfoDtoBuilder_ == null) {
          ensureServerInfoDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, serverInfoDto_);
          onChanged();
        } else {
          serverInfoDtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder clearServerInfoDto() {
        if (serverInfoDtoBuilder_ == null) {
          serverInfoDto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          serverInfoDtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public Builder removeServerInfoDto(int index) {
        if (serverInfoDtoBuilder_ == null) {
          ensureServerInfoDtoIsMutable();
          serverInfoDto_.remove(index);
          onChanged();
        } else {
          serverInfoDtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder getServerInfoDtoBuilder(
          int index) {
        return getServerInfoDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder getServerInfoDtoOrBuilder(
          int index) {
        if (serverInfoDtoBuilder_ == null) {
          return serverInfoDto_.get(index);  } else {
          return serverInfoDtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder> 
           getServerInfoDtoOrBuilderList() {
        if (serverInfoDtoBuilder_ != null) {
          return serverInfoDtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(serverInfoDto_);
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder addServerInfoDtoBuilder() {
        return getServerInfoDtoFieldBuilder().addBuilder(
            com.dxx.game.dto.ServerListProto.ServerInfoDto.getDefaultInstance());
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder addServerInfoDtoBuilder(
          int index) {
        return getServerInfoDtoFieldBuilder().addBuilder(
            index, com.dxx.game.dto.ServerListProto.ServerInfoDto.getDefaultInstance());
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .Proto.ServerList.ServerInfoDto serverInfoDto = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder> 
           getServerInfoDtoBuilderList() {
        return getServerInfoDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.ServerListProto.ServerInfoDto, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder, com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder> 
          getServerInfoDtoFieldBuilder() {
        if (serverInfoDtoBuilder_ == null) {
          serverInfoDtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.ServerListProto.ServerInfoDto, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder, com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder>(
                  serverInfoDto_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          serverInfoDto_ = null;
        }
        return serverInfoDtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.ServerGroupDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.ServerGroupDto)
    private static final com.dxx.game.dto.ServerListProto.ServerGroupDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.ServerGroupDto();
    }

    public static com.dxx.game.dto.ServerListProto.ServerGroupDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ServerGroupDto>
        PARSER = new com.google.protobuf.AbstractParser<ServerGroupDto>() {
      @java.lang.Override
      public ServerGroupDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ServerGroupDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ServerGroupDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ServerGroupDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ServerGroupDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ServerInfoDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.ServerList.ServerInfoDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 serverId = 1;</code>
     * @return The serverId.
     */
    int getServerId();

    /**
     * <pre>
     *0空状态1新2爆满
     * </pre>
     *
     * <code>uint64 status = 2;</code>
     * @return The status.
     */
    long getStatus();
  }
  /**
   * Protobuf type {@code Proto.ServerList.ServerInfoDto}
   */
  public static final class ServerInfoDto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.ServerList.ServerInfoDto)
      ServerInfoDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ServerInfoDto.newBuilder() to construct.
    private ServerInfoDto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ServerInfoDto() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ServerInfoDto();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ServerInfoDto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              serverId_ = input.readUInt32();
              break;
            }
            case 16: {

              status_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerInfoDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerInfoDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ServerListProto.ServerInfoDto.class, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder.class);
    }

    public static final int SERVERID_FIELD_NUMBER = 1;
    private int serverId_;
    /**
     * <code>uint32 serverId = 1;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }

    public static final int STATUS_FIELD_NUMBER = 2;
    private long status_;
    /**
     * <pre>
     *0空状态1新2爆满
     * </pre>
     *
     * <code>uint64 status = 2;</code>
     * @return The status.
     */
    @java.lang.Override
    public long getStatus() {
      return status_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (serverId_ != 0) {
        output.writeUInt32(1, serverId_);
      }
      if (status_ != 0L) {
        output.writeUInt64(2, status_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (serverId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, serverId_);
      }
      if (status_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, status_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ServerListProto.ServerInfoDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ServerListProto.ServerInfoDto other = (com.dxx.game.dto.ServerListProto.ServerInfoDto) obj;

      if (getServerId()
          != other.getServerId()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getServerId();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getStatus());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ServerListProto.ServerInfoDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ServerListProto.ServerInfoDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.ServerList.ServerInfoDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.ServerList.ServerInfoDto)
        com.dxx.game.dto.ServerListProto.ServerInfoDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerInfoDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerInfoDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ServerListProto.ServerInfoDto.class, com.dxx.game.dto.ServerListProto.ServerInfoDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.ServerListProto.ServerInfoDto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        serverId_ = 0;

        status_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ServerListProto.internal_static_Proto_ServerList_ServerInfoDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ServerInfoDto getDefaultInstanceForType() {
        return com.dxx.game.dto.ServerListProto.ServerInfoDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ServerInfoDto build() {
        com.dxx.game.dto.ServerListProto.ServerInfoDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ServerListProto.ServerInfoDto buildPartial() {
        com.dxx.game.dto.ServerListProto.ServerInfoDto result = new com.dxx.game.dto.ServerListProto.ServerInfoDto(this);
        result.serverId_ = serverId_;
        result.status_ = status_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ServerListProto.ServerInfoDto) {
          return mergeFrom((com.dxx.game.dto.ServerListProto.ServerInfoDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ServerListProto.ServerInfoDto other) {
        if (other == com.dxx.game.dto.ServerListProto.ServerInfoDto.getDefaultInstance()) return this;
        if (other.getServerId() != 0) {
          setServerId(other.getServerId());
        }
        if (other.getStatus() != 0L) {
          setStatus(other.getStatus());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.ServerListProto.ServerInfoDto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.ServerListProto.ServerInfoDto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int serverId_ ;
      /**
       * <code>uint32 serverId = 1;</code>
       * @return The serverId.
       */
      @java.lang.Override
      public int getServerId() {
        return serverId_;
      }
      /**
       * <code>uint32 serverId = 1;</code>
       * @param value The serverId to set.
       * @return This builder for chaining.
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 serverId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }

      private long status_ ;
      /**
       * <pre>
       *0空状态1新2爆满
       * </pre>
       *
       * <code>uint64 status = 2;</code>
       * @return The status.
       */
      @java.lang.Override
      public long getStatus() {
        return status_;
      }
      /**
       * <pre>
       *0空状态1新2爆满
       * </pre>
       *
       * <code>uint64 status = 2;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(long value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0空状态1新2爆满
       * </pre>
       *
       * <code>uint64 status = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.ServerList.ServerInfoDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.ServerList.ServerInfoDto)
    private static final com.dxx.game.dto.ServerListProto.ServerInfoDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ServerListProto.ServerInfoDto();
    }

    public static com.dxx.game.dto.ServerListProto.ServerInfoDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ServerInfoDto>
        PARSER = new com.google.protobuf.AbstractParser<ServerInfoDto>() {
      @java.lang.Override
      public ServerInfoDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ServerInfoDto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ServerInfoDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ServerInfoDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ServerListProto.ServerInfoDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_UserGetLastLoginRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_UserGetLastLoginRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_UserGetLastLoginResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_UserGetLastLoginResponse_ServerListEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_UserGetLastLoginResponse_ServerListEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_FindServerListRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_FindServerListRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_FindServerListResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_FindServerListResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_RoleDetailDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_RoleDetailDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_ZoneInfoDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_ZoneInfoDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_ZoneInfoDto_ServerListEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_ZoneInfoDto_ServerListEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_ServerGroupDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_ServerGroupDto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_ServerList_ServerInfoDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_ServerList_ServerInfoDto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020serverlist.proto\022\020Proto.ServerList\032\014co" +
      "mmon.proto\"K\n\027UserGetLastLoginRequest\0220\n" +
      "\014commonParams\030\001 \001(\0132\032.Proto.Common.Commo" +
      "nParams\"\253\002\n\030UserGetLastLoginResponse\022\014\n\004" +
      "code\030\001 \001(\005\0221\n\010roleList\030\002 \003(\0132\037.Proto.Ser" +
      "verList.RoleDetailDto\022N\n\nserverList\030\003 \003(" +
      "\0132:.Proto.ServerList.UserGetLastLoginRes" +
      "ponse.ServerListEntry\022,\n\ncommonData\030\004 \001(" +
      "\0132\030.Proto.Common.CommonData\032P\n\017ServerLis" +
      "tEntry\022\013\n\003key\030\001 \001(\r\022,\n\005value\030\002 \001(\0132\035.Pro" +
      "to.ServerList.ZoneInfoDto:\0028\001\"Z\n\025FindSer" +
      "verListRequest\0220\n\014commonParams\030\001 \001(\0132\032.P" +
      "roto.Common.CommonParams\022\017\n\007groupId\030\002 \001(" +
      "\r\"\212\001\n\026FindServerListResponse\022\014\n\004code\030\001 \001" +
      "(\005\0224\n\rserverInfoDto\030\002 \001(\0132\035.Proto.Server" +
      "List.ZoneInfoDto\022,\n\ncommonData\030\003 \001(\0132\030.P" +
      "roto.Common.CommonData\"\237\001\n\rRoleDetailDto" +
      "\022\020\n\010nickName\030\001 \001(\t\022\017\n\007groupId\030\002 \001(\r\022\016\n\006a" +
      "vatar\030\003 \001(\r\022\023\n\013avatarFrame\030\004 \001(\r\022\020\n\010serv" +
      "erId\030\005 \001(\r\022\r\n\005power\030\006 \001(\004\022\025\n\rlastLoginPa" +
      "ss\030\007 \001(\004\022\016\n\006userId\030\010 \001(\004\"\270\001\n\013ZoneInfoDto" +
      "\022\021\n\tmaxServer\030\001 \001(\r\022A\n\nserverList\030\002 \003(\0132" +
      "-.Proto.ServerList.ZoneInfoDto.ServerLis" +
      "tEntry\032S\n\017ServerListEntry\022\013\n\003key\030\001 \001(\r\022/" +
      "\n\005value\030\002 \001(\0132 .Proto.ServerList.ServerG" +
      "roupDto:\0028\001\"\177\n\016ServerGroupDto\022\r\n\005group\030\001" +
      " \001(\r\022\023\n\013startServer\030\002 \001(\r\022\021\n\tendServer\030\003" +
      " \001(\r\0226\n\rserverInfoDto\030\004 \003(\0132\037.Proto.Serv" +
      "erList.ServerInfoDto\"1\n\rServerInfoDto\022\020\n" +
      "\010serverId\030\001 \001(\r\022\016\n\006status\030\002 \001(\004B#\n\020com.d" +
      "xx.game.dtoB\017ServerListProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_ServerList_UserGetLastLoginRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_ServerList_UserGetLastLoginRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_UserGetLastLoginRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_ServerList_UserGetLastLoginResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor,
        new java.lang.String[] { "Code", "RoleList", "ServerList", "CommonData", });
    internal_static_Proto_ServerList_UserGetLastLoginResponse_ServerListEntry_descriptor =
      internal_static_Proto_ServerList_UserGetLastLoginResponse_descriptor.getNestedTypes().get(0);
    internal_static_Proto_ServerList_UserGetLastLoginResponse_ServerListEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_UserGetLastLoginResponse_ServerListEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_ServerList_FindServerListRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_ServerList_FindServerListRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_FindServerListRequest_descriptor,
        new java.lang.String[] { "CommonParams", "GroupId", });
    internal_static_Proto_ServerList_FindServerListResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_ServerList_FindServerListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_FindServerListResponse_descriptor,
        new java.lang.String[] { "Code", "ServerInfoDto", "CommonData", });
    internal_static_Proto_ServerList_RoleDetailDto_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_ServerList_RoleDetailDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_RoleDetailDto_descriptor,
        new java.lang.String[] { "NickName", "GroupId", "Avatar", "AvatarFrame", "ServerId", "Power", "LastLoginPass", "UserId", });
    internal_static_Proto_ServerList_ZoneInfoDto_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_ServerList_ZoneInfoDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_ZoneInfoDto_descriptor,
        new java.lang.String[] { "MaxServer", "ServerList", });
    internal_static_Proto_ServerList_ZoneInfoDto_ServerListEntry_descriptor =
      internal_static_Proto_ServerList_ZoneInfoDto_descriptor.getNestedTypes().get(0);
    internal_static_Proto_ServerList_ZoneInfoDto_ServerListEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_ZoneInfoDto_ServerListEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_ServerList_ServerGroupDto_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_ServerList_ServerGroupDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_ServerGroupDto_descriptor,
        new java.lang.String[] { "Group", "StartServer", "EndServer", "ServerInfoDto", });
    internal_static_Proto_ServerList_ServerInfoDto_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_ServerList_ServerInfoDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_ServerList_ServerInfoDto_descriptor,
        new java.lang.String[] { "ServerId", "Status", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

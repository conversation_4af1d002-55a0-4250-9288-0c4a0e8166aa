package com.dxx.game.modules.message.service;

import com.dxx.game.common.redis.RedisService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/3
 * @description:
 */
@Service
public class MessageService {

    private final String REDIS_GUILD_MSG_ID_KEY = "guild_msg_id:";

    private static final String REDIS_SERVER_MSG_ID_KEY_PREFIX = "server_msg_id:";

    @Resource
    private RedisService redisService;

    // 公会消息ID自增
    public long generateGuildMsgId(long guildId) {
        String key = REDIS_GUILD_MSG_ID_KEY + guildId;
        return redisService.incrBy(key);
    }

    public long generateServerMsgId(long serverId) {
        String key = REDIS_SERVER_MSG_ID_KEY_PREFIX + serverId;
        return redisService.incrBy(key);
    }

}

/**
 * 一、公会内消息
 *
 * 1. 自己成功加入公会(提交审批的)
 * 	messageType: 101
 * 	messageContent: {"msgId":4,"guildId":10084,"guildName":"厕所","timestamp":1681717841}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		guildId: 公会ID
 * 		guildName: 公会名称
 * 	    timestamp: 时间戳
 *
 * 2. 有人加入公会(计算公会人数)
 * 	messageType: 102
 * 	messageContent: {"msgId":5,"joinUsers":[{"level":1,"joinTime":"1681717841","chapterId":1,"activeTime":"1681717828","position":4,"userId":"10001696"}],"timestamp":1681717841}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		joinUsers: guild.proto 中 [GuildMemberInfoDto] 对象数组
 * 	    timestamp: 时间戳
 *
 * 3. 有人退出公会(计算公会人数)
 * 	messageType: 103
 * 	messageContent: {"msgId":11,"leaveUserId":10001696,"leaveUserNickName":"","timestamp":1681717996}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		leaveUserId: 退出用户ID
 * 		leaveUserNickName: 退出用户昵称
 * 	    timestamp: 时间戳
 *
 * 4. 职位变动
 * 	messageType: 104
 * 	messageContent: {"msgId":200,"fromUserId":10001723,"fromUserNickName":"","fromUserPosition":1,"oldPosition":2,"upPosition":3,"toUserId":10001696,"toUserNickName":"","timestamp":1682583453}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		fromUserId: 发起人用户ID
 * 		fromUserNickName: 发起人昵称
 * 		fromUserPosition: 发起人职位
 * 		upPosition: 我的职位(新职位)
 * 	    oldPosition: 原来的职位
 * 	    toUserId: 目标用户ID
 * 	    toUserNickName 目标用户昵称
 * 	    timestamp: 时间戳
 *
 * 5. 自己被踢出公会
 * 	messageType: 105
 * 	messageContent: {"msgId":7,"fromUserId":10001723,"fromUserNickName":"","fromUserPosition":1,"timestamp":1681717928}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		fromUserId: 发起人用户ID
 * 		fromUserNickName: 发起人昵称
 * 		fromUserPosition: 发起人职位
 * 	    timestamp: 时间戳
 *
 * 6. 有人被踢出公会(计算公会人数)
 * 	messageType: 106
 * 	messageContent: {"msgId":8,"beKickedUserId":10001696,"beKickedUserNickName":"","fromUserId":10001723,"fromUserNickName":"","fromUserPosition":1,"timestamp":1681717928}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		fromUserId: 发起人用户ID
 * 		fromUserNickName: 发起人昵称
 * 		fromUserPosition: 发起人职位
 * 		beKickedUserId: 被踢人用户ID
 * 		beKickedUserNickName: 被踢人用户昵称
 * 	    timestamp: 时间戳
 * 7. 公会信息变化
 * messageType:107
 * messageContent:{"msgId":55,"guildName":"测试公会","guildIcon":0,"guildIconBg":0,"guildNotice":"","guildIntro":"","guildExp":109,"guildLevel":1,"timestamp":1681905174}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		guildIcon: 公会图标
 * 		guildIconBg: 公会图标背景
 * 		guildNotice: 公会公告
 * 		guildName: 公会名称
 * 	    timestamp: 时间戳
 *
 * 8. 公会请求捐赠消息
 * messageType:108
 * messageType:108, messageContent:{"count":0,"donated":false,"expiredTM":1685107246,"itemId":6102,"maxCount":10,"msgId":320,"nickName":"t332","receiveCnt":0,"timestamp":1685106946,"userId":10001733}
 * json字段说明:
 * 	    msgId: 消息唯一Id
 * 	    userId: 用户ID
 * 	    nickName: 昵称
 * 	    count: 收到的数量
 * 	    donated: 是否赠送过
 * 	    expiredTM: 过期时间戳
 * 	    itemId: 道具id
 * 	    maxCount: 最大数量
 * 	    receiveCnt: 已领取数量
 * 	    timestamp: 时间戳
 * 9. 公会-捐赠收到的道具数量变化
 *   messageType:109
 *   messageContent:{"msgId":317,"count":1}
 *     json字段说明:
 *     msgId: 消息ID
 *     count: 收到的数量
 * 10. 公会-捐赠移除数据
 *   messageType:110
 *   messageContent:{"delMsgId":[313]}
 *     json字段说明:
 *     msgId: 移除的消息ID[数组]
 *
 * 11. 工会-更新申请信息
 *     messageType:111
 *          json字段说明:
 *          guildId:工会id,
 *          dataList:[{
 *             userId:用户ID
 *             nickName:昵称
 *             avatar:头像
 *             avatarFrame:头像框
 *          }],
 *          applyCount:申请数量
 *
 * 二、聊天
 *
 * 1. 公会聊天:
 * 	messageType: 201
 * 	messageContent: {"msgId":3,"userId":10001723,"nickName":"","avatar":0,"avatarFrame":0,"chatContent":"222","languageId":0, "userPosition":1,"timestamp":1681717709}
 *
 * 	json字段说明:
 * 	    msgId: 消息唯一Id
 * 		userId: 用户ID
 * 		nickName: 用户昵称
 * 		avatar: 用户头像
 * 		avatarFrame: 用户头像框
 * 		chatContent: 聊天内容
 * 	    languageId: 语言ID
 * 	    userPosition: 职位
 * 	    timestamp: 时间戳
 *
 * 2. 公会展示物品
 *  messageType: 202
 *  messageType:202, messageContent:{"msgId":242,"userId":10001733,"nickName":"t332","avatar":0,"avatarFrame":0,"itemType":1,"itemProto":{"equipId":210104,"level":1,"rowId":"26"},"timestamp":1684810733,"userPosition":1}
 *  messageType:202, messageContent:{"msgId":243,"userId":10001733,"nickName":"t332","avatar":0,"avatarFrame":0,"itemType":16,"itemProto":{"traning":{},"traningData":{},"star":1,"level":1,"heroId":6001,"rowId":"2"},"timestamp":1684810801,"userPosition":1}
 *  messageType:202, messageContent:{"msgId":244,"userId":10001733,"nickName":"t332","avatar":0,"avatarFrame":0,"itemType":18,"itemProto":{"supportSkillIds":[403004],"petId":23040804,"level":1,"rowId":"4"},"timestamp":1684810828,"userPosition":1}
 *
 *  需要做版本隔离， 道具表中根据id找不到忽略此消息
 *
 *  json字段说明:
 * 	    msgId: 消息唯一Id
 * 		userId: 用户ID
 * 		nickName: 用户昵称
 * 		avatar: 用户头像
 * 		avatarFrame: 用户头像框
 * 		itemType: 道具类型(道具表类型)
 * 	    itemProto: 根据道具类型分别对应common.proto中 EquipmentDto, HeroDto, PetDto
 * 	    userPosition: 职位
 * 	    timestamp: 时间戳
 */

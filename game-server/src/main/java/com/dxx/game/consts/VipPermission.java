package com.dxx.game.consts;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum VipPermission {
    PERMISSION_3(3, "权限3 额外增加挂机获得的金币奖励"),
    PERMISSION_8(8, "权限8 解锁竞技场跳过战斗"),
    PERMISSION_10(10, "权限10 解锁主线战斗4倍速"),
    PERMISSION_11(11, "权限11 解锁挑战之塔战斗4倍速"),
    PERMISSION_12(12, "权限12 解锁挑竞技场战斗4倍速"),
    PERMISSION_13(13, "权限13 解锁挑战之塔跳过战斗"),
    PERMISSION_14(14, "权限14 解锁战役跳过战斗"),
    PERMISSION_15(15, "权限15 黑市刷新次数"),
    PERMISSION_16(16, "权限16 解除聊天间隔"),
    PERMISSION_18(18, "权限18 宝箱产出CD缩短");

    private final int id; // Vip表Data表的id
    private final String desc;

    VipPermission(int id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    private static Map<Integer, VipPermission> values = new HashMap<>();

    static {
        VipPermission[] values1 = values();
        for (VipPermission type : values1) {
            values.put(type.getId(), type);
        }
    }

    public static VipPermission getEnumByKey(int type) {
        return values.get(type);
    }

}

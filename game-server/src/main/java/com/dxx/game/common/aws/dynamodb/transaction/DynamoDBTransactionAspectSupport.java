package com.dxx.game.common.aws.dynamodb.transaction;

import com.dxx.game.common.aws.config.AWSConfig;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBatchWriteData;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBModelKeyInfo;
import com.dxx.game.common.aws.dynamodb.model.mapper.DynamoDBMapperRegistry;
import com.google.common.collect.Lists;
import io.netty.util.concurrent.FastThreadLocal;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.mapper.BeanTableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.*;
import software.amazon.awssdk.services.dynamodb.model.*;

import java.util.*;

@SuppressWarnings("unchecked")
public class DynamoDBTransactionAspectSupport {

    /** batchWrite 最大条数 */
    private static final double BATCH_OPERATION_MAX_COUNT = 25;
    /** 事务写入最大条数 */
    private static final double TRANSACTION_WRITE_ITEMS_MAX_COUNT = 100;

    private static final int PUT = 0;
    private static final int UPDATE = 1;
    private static final int DELETE = 2;


    @Getter
    private static Map<String, DynamoDBTableInfo<DynamoDBBaseModel>> tableInfoMap = new HashMap<>();

    public static <T> void addTableInfo(Class<T> tClass, DynamoDbTable<T> mappedTable, BeanTableSchema<T> tableSchema) {
        DynamoDBTableInfo<T> data = new DynamoDBTableInfo<T>(tClass, mappedTable, tableSchema);
        tableInfoMap.put(tClass.getName(), (DynamoDBTableInfo<DynamoDBBaseModel>) data);
    }


    private static FastThreadLocal<Object> localTransaction = new FastThreadLocal<>();
    private static FastThreadLocal<Boolean> isRollBack = new FastThreadLocal<>();
    private static FastThreadLocal<List<UpdateItemRequest>> updateItemRequests = new FastThreadLocal<>();
    private static FastThreadLocal<Map<String, Object>> putItems = new FastThreadLocal<>();
    private static FastThreadLocal<Map<String, Object>> updateItems = new FastThreadLocal<>();
    private static FastThreadLocal<Map<String, Object>> deleteItems = new FastThreadLocal<>();


    /**
     * add put item
     * @param object
     * @param <T>
     */
    public static <T extends DynamoDBBaseModel> void addPutItem(T object) {
        if (object == null) return;
        String identity = object.getIdentity();
        if (updateItems.get() != null) {
            updateItems.get().remove(identity);
        }

        if (putItems.get() == null) {
            putItems.set(new HashMap<>());
        }

        putItems.get().put(identity, object);
    }

    /**
     * add delete item
     * @param object
     * @param <T>
     */
    public static <T extends DynamoDBBaseModel> void addDeleteItem(T object) {
        if (object == null) return;
        if (putItems.get() != null) {
            putItems.get().remove(object.getIdentity());
        }
        if (updateItems.get() != null) {
            updateItems.get().remove(object.getIdentity());
        }

        if (deleteItems.get() == null) {
            deleteItems.set(new HashMap<>());
        }
        String identity = object.getIdentity();
        deleteItems.get().put(identity, object);
    }

    /**
     * add update item ignore nulls
     * @param object
     * @param <T>
     */
    public static <T extends DynamoDBBaseModel> void addUpdateItem(T object, boolean ignoreNulls) {
        if (object == null) return;
        if (!ignoreNulls) {
            addPutItem(object);
        } else {
            String identity = object.getIdentity();
            if (putItems.get() != null && putItems.get().containsKey(identity)) {
                return;
            }
            if (deleteItems.get() != null && deleteItems.get().containsKey(identity)) {
                return;
            }

            if (updateItems.get() == null) {
                updateItems.set(new HashMap<>());
            }
            object.setIgnoreNulls(ignoreNulls);
            updateItems.get().put(identity, object);
            DynamoDBCacheManager.putUpdateObj(object);
        }
    }


    // ---------------------------- 批量写入请求 ------------------------------------ //
    /**
     * get write batch
     * @return
     */
    public static Collection<Map<String, DynamoDBBatchWriteData>> getWriteBatch() {
        if (putItems.get() == null && deleteItems.get() == null && updateItems.get() == null) {
            return null;
        }

        int count = 0;
        Map<Integer, Map<String, DynamoDBBatchWriteData>> builderMap = new HashMap<>();
        // 更新数据
        if (putItems.get() != null) {
            count = fillWriteBatch(putItems.get(), builderMap, count, PUT);
        }

        if (updateItems.get() != null) {
            count = fillWriteBatch(updateItems.get(), builderMap, count, UPDATE);
        }

        // 删除数据
        if (deleteItems.get() != null) {
            fillWriteBatch(deleteItems.get(), builderMap, count, DELETE);
        }

        return builderMap.values();

    }

    public static int fillWriteBatch(Map<String, Object> items, Map<Integer, Map<String, DynamoDBBatchWriteData>> builderMap,
                                     Integer count, int type) {
        for (Map.Entry<String, Object> entry : items.entrySet()) {

            DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
            if (type == UPDATE && model.isIgnoreNulls()) {
                continue;
            }

            String className = model.getClass().getName();
            int index = (int) Math.floor(count / BATCH_OPERATION_MAX_COUNT);
            if (!builderMap.containsKey(index)) {
                builderMap.put(index, new HashMap<>());
            }
            if (!builderMap.get(index).containsKey(className)) {
                DynamoDBBatchWriteData dynamoDBBatchWriteData = new DynamoDBBatchWriteData(tableInfoMap.get(className).getClazz(), tableInfoMap.get(className).getMappedTable());
                builderMap.get(index).put(className, dynamoDBBatchWriteData);
            }

            if (type == PUT) {
                builderMap.get(index).get(className).getWriteBatchBuilder().addPutItem(model);
            } else if (type == UPDATE) {
                if (model.isIgnoreNulls()) {
                    continue;
                }
                // 非ignoreNulls 用put方法
                builderMap.get(index).get(className).getWriteBatchBuilder().addPutItem(model);
            } else if (type == DELETE) {
                builderMap.get(index).get(className).getWriteBatchBuilder().addDeleteItem(model);
            }
            count ++;
        }
        return count;
    }

    // ---------------------------- 事务写入请求(增强型客户端) ------------------------------------ //
    /**
     * get transaction write request
     * @return
     */
    public static Map<Integer, TransactWriteItemsEnhancedRequest.Builder> getTransactWriteItemsEnhancedRequests() {
        if (putItems.get() == null && deleteItems.get() == null && updateItems.get() == null) {
            return null;
        }

        int count = 0;
        Map<Integer, TransactWriteItemsEnhancedRequest.Builder> result = new HashMap<>();
        if (putItems.get() != null) {
            count = fillTransactionWriteRequest(count, putItems.get(), PUT, result);
        }

        if (updateItems.get() != null) {
            count = fillTransactionWriteRequest(count, updateItems.get(), UPDATE, result);
        }

        if (deleteItems.get() != null) {
            fillTransactionWriteRequest(count, deleteItems.get(), DELETE, result);
        }

        return result;
    }

    private static int fillTransactionWriteRequest(Integer count, Map<String, Object> items, int type,
                                                   Map<Integer, TransactWriteItemsEnhancedRequest.Builder> builders) {
        for (Map.Entry<String, Object> entry : items.entrySet()) {
            int index = (int) Math.floor(count / TRANSACTION_WRITE_ITEMS_MAX_COUNT);
            if (AWSConfig.APP_ENV.equals("develop")) {
                index = (int) Math.floor(count / BATCH_OPERATION_MAX_COUNT);
            }

            if (!builders.containsKey(index)) {
                builders.put(index, TransactWriteItemsEnhancedRequest.builder());
            }
            DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
            String className = model.getClass().getName();
            if (type == PUT) {
                builders.get(index).addPutItem(tableInfoMap.get(className).getMappedTable(), model);
            } else if (type == UPDATE) {
                // 非 ignoreNulls 用 put方法
                if (model.isIgnoreNulls()) {
                    TransactUpdateItemEnhancedRequest.Builder<DynamoDBBaseModel> request
                            = TransactUpdateItemEnhancedRequest.builder(DynamoDBBaseModel.class)
                            .item(model)
                            .ignoreNulls(true);

                    Expression updateCondition = model.getUpdateCondition();
                    if (updateCondition != null) {
                        request.conditionExpression(updateCondition);
                    }
                    builders.get(index).addUpdateItem(tableInfoMap.get(className).getMappedTable(), request.build());
                } else {
                    builders.get(index).addPutItem(tableInfoMap.get(className).getMappedTable(), model);
                }
            } else if (type == DELETE) {
                builders.get(index).addDeleteItem(tableInfoMap.get(className).getMappedTable(), model);
            }
            count ++;
        }
        return count;
    }

    // ---------------------------- 事务写入请求(普通客户端) ------------------------------------ //
    /**
     * 获取所有写入请求包括, Put, Update, 和 Delete 用户普通客户端处理
     * @return
     */
    public static List<TransactWriteItemsRequest> getTransactionWriteRequests() {
        List<TransactWriteItemsRequest> result = new ArrayList<>();

        // 存放所有写入请求
        List<TransactWriteItem> transactWriteItems = new ArrayList<>();

//        System.out.println(putItems.get());
//        System.out.println(updateItems.get());
//        System.out.println(deleteItems.get());

        // 处理Put操作
        if (putItems.get() != null) {
            for (Map.Entry<String, Object> entry : putItems.get().entrySet()) {
                // 获取当前对象的类名和对应的表信息
                DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
                String className = model.getClass().getName();
                DynamoDBTableInfo<DynamoDBBaseModel> tableInfo = tableInfoMap.get(className);
                // 将对象转化为 DynamoDB 的 AttributeValue 格式，并添加到 transactWriteItems 中
                Map<String, AttributeValue> attributeValueMap = tableInfo.getTableSchema().itemToMap(model, model.isIgnoreNulls());
                // 上边的方法返回的是Collections.unmodifiableMap()一个不可修改的 Map
                attributeValueMap = new HashMap<>(attributeValueMap);
                // 移除掉值=null的索引字段
                DynamoDBModelKeyInfo dynamoDBModelKeyInfo = DynamoDBMapperRegistry.getModelKeyInfo(className);
                List<String> removeAttributeValueKeys = new ArrayList<>();
                if (dynamoDBModelKeyInfo.getSecondaryPartitionKeys() != null) {
                    removeAttributeValueKeys.addAll(dynamoDBModelKeyInfo.getSecondaryPartitionKeys());
                }
                if (dynamoDBModelKeyInfo.getSecondarySortKeys() != null) {
                    removeAttributeValueKeys.addAll(dynamoDBModelKeyInfo.getSecondarySortKeys());
                }
                if (!removeAttributeValueKeys.isEmpty()) {
                    for (String removeKey : removeAttributeValueKeys) {
                        AttributeValue attributeValue = attributeValueMap.get(removeKey);
                        if (attributeValue != null && attributeValue.type() == AttributeValue.Type.NUL) {
                            attributeValueMap.remove(removeKey);
                        }
                    }
                }

                transactWriteItems.add(
                        TransactWriteItem.builder()
                                .put(Put.builder()
                                        .tableName(model.getTableName())
                                        .item(attributeValueMap)
                                        .build()).build()
                );
            }
        }

        // 处理Update操作
        if (updateItems.get() != null) {
            for (Map.Entry<String, Object> entry : updateItems.get().entrySet()) {
                // 获取当前对象的类名和对应的表信息
                DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
                String className = model.getClass().getName();
                DynamoDBTableInfo<DynamoDBBaseModel> tableInfo = tableInfoMap.get(className);
                // 获取对象对应的主键信息，并将对象转化为 DynamoDB 的 AttributeValue 格式
                Key key = tableInfo.getMappedTable().keyFrom(model);
                Map<String, AttributeValue> keyMap = key.primaryKeyMap(tableInfo.getTableSchema());
//                System.out.println(keyMap);
                // 获取将object实体类转换成 Map<String, AttributeValue>
                Map<String, AttributeValue> attributeValueMap = tableInfo.getTableSchema().itemToMap(model, model.isIgnoreNulls());


                // 处理 UpdateExpression
                List<String> updateAttributes = new ArrayList<>(attributeValueMap.size());
                Map<String, AttributeValue> expressionAttributeValues = new HashMap<>();
                Map<String, String> expressionAttributeNames = new HashMap<>();
                Map<String, Expression> updateExpressions = model.getUpdateExpressions();
                List<String> excludeNames = new ArrayList<>();
                if (updateExpressions != null) {
                    for (Map.Entry<String, Expression> updateExpressionEntry : updateExpressions.entrySet()) {
                        Expression expression = updateExpressionEntry.getValue();
                        updateAttributes.add(expression.expression());
                        excludeNames.addAll(expression.expressionNames().values());
                        expressionAttributeNames.putAll(expression.expressionNames());
                        expressionAttributeValues.putAll(expression.expressionValues());
                    }
                }

                // 处理 Attributes 和 ConditionExpression
                for (Map.Entry<String, AttributeValue> attributeValueEntry : attributeValueMap.entrySet()) {
                    // 排除掉已经包含在主键中的属性名和 Expression 中用到的属性名，将剩余的属性转化为 DynamoDB 的 UpdateExpression 格式
                    if (keyMap.containsKey(attributeValueEntry.getKey()) || (excludeNames != null && excludeNames.contains(attributeValueEntry.getKey()))) {
                        continue;
                    }
                    String nameKey = "#" + attributeValueEntry.getKey();
                    String valueKey = ":" + attributeValueEntry.getKey();
                    updateAttributes.add(nameKey + " = " + valueKey);
                    expressionAttributeNames.put(nameKey, attributeValueEntry.getKey());
                    expressionAttributeValues.put(valueKey, attributeValueEntry.getValue());
                }

                // 将 UpdateExpression 和其他相关信息组装成 DynamoDB 的 UpdateRequest 格式，并添加到 transactWriteItems 中
                String updateExpress = " set " + StringUtils.join(updateAttributes, " , ");
                if (model.getUpdateCondition() != null) {
                    if (model.getUpdateCondition().expressionNames() != null) {
                        expressionAttributeNames.putAll(model.getUpdateCondition().expressionNames());
                    }
                    if (model.getUpdateCondition().expressionValues() != null) {
                        expressionAttributeValues.putAll(model.getUpdateCondition().expressionValues());
                    }
                }

                Update.Builder updateBuilder = Update.builder()
                        .tableName(model.getTableName())
                        .key(keyMap)
                        .updateExpression(updateExpress)
                        .expressionAttributeNames(expressionAttributeNames)
                        .expressionAttributeValues(expressionAttributeValues);

                if (model.getUpdateCondition() != null) {
                    updateBuilder.conditionExpression(model.getUpdateCondition().expression());
                }
//                System.out.println(updateExpress);
//                System.out.println(expressionAttributeNames);
//                System.out.println(expressionAttributeValues);
//                if (model.getUpdateCondition() != null) {
//                    System.out.println(model.getUpdateCondition().expression());
//                    System.out.println(model.getUpdateCondition().expressionNames());
//                    System.out.println(model.getUpdateCondition().expressionValues());
//                }
                transactWriteItems.add(
                        TransactWriteItem.builder()
                                .update(updateBuilder.build()).build()
                );
            }
        }

        // 处理Delete操作
        if (deleteItems.get() != null) {
            for (Map.Entry<String, Object> entry : deleteItems.get().entrySet()) {
                // 获取当前对象的类名和对应的表信息
                DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
                String className = model.getClass().getName();
                DynamoDBTableInfo<DynamoDBBaseModel> tableInfo = tableInfoMap.get(className);
                // 获取对象对应的主键信息，并将对象转化为 DynamoDB 的 AttributeValue 格式
                Key key = tableInfo.getMappedTable().keyFrom(model);
                Map<String, AttributeValue> keyMap = key.primaryKeyMap(tableInfo.getTableSchema());
                transactWriteItems.add(
                        TransactWriteItem.builder()
                                .delete(Delete.builder()
                                        .tableName(model.getTableName())
                                        .key(keyMap)
                                        .build())
                                .build()
                );
            }
        }

        int maxCount = (int)TRANSACTION_WRITE_ITEMS_MAX_COUNT;
        if (AWSConfig.APP_ENV.equals("develop")) {
            maxCount = (int)BATCH_OPERATION_MAX_COUNT;
        }

        List<List<TransactWriteItem>> partitions = Lists.partition(transactWriteItems, maxCount);
        for (List<TransactWriteItem> partition : partitions) {
            result.add(TransactWriteItemsRequest.builder().transactItems(partition).build());
        }

        return result;
    }

    /**
     * 执行更新操作
     */
    public static void executeUpdateItems() {
        if (updateItems.get() == null) {
            return;
        }

        for (Map.Entry<String, Object> entry : updateItems.get().entrySet()) {
            DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
            if (!model.isIgnoreNulls()) {
                continue;
            }

            String className = model.getClass().getName();

            UpdateItemEnhancedRequest.Builder<DynamoDBBaseModel> request = UpdateItemEnhancedRequest.builder(DynamoDBBaseModel.class).item(model).ignoreNulls(true);
            Expression expression = model.getUpdateCondition();
            if (expression != null) {
                request.conditionExpression(expression);
            }
            tableInfoMap.get(className).getMappedTable().updateItem(request.build());

        }
    }

    /**
     * 添加更新数据请求
     * @param updateItemRequest
     */
    public static void addUpdateItemRequest(UpdateItemRequest updateItemRequest) {
        if (updateItemRequests.get() == null) {
            updateItemRequests.set(new ArrayList<>());
        }
        updateItemRequests.get().add(updateItemRequest);
    }

    public static List<UpdateItemRequest> getUpdateItemRequest() {
        return updateItemRequests.get();
    }

    /**
     * 是否回滚
     * @return
     */
    public static boolean isRollBack() {
        return isRollBack.get() != null;
    }

    /**
     * 设置回滚
     */
    public static void setRollBack() {
        isRollBack.set(true);
    }

    /**
     * 是否开启了事务
     * @return
     */
    public static boolean isOpen() {
        return localTransaction.get() != null;
    }

    /**
     * 开启事务
     */
    public static void setLocalTransaction(Object object) {
        localTransaction.set(object);
    }

    public static boolean isLocalTransaction(Object object) {
        return localTransaction.get() != null && localTransaction.get().equals(object);
    }

    public static Map<String, Object> getUpdateItems() {
        return updateItems.get();
    }

    public static Map<String, Object> getPutItems() {
        return putItems.get();
    }

    public static Map<String, Object> getDeleteItems() {
        return deleteItems.get();
    }

    /**
     * 获取update条件字符串
     * @return
     */
    public static List<String> getUpdateConditions() {
        if (getUpdateItems() == null) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (Map.Entry<String, Object> entry : getUpdateItems().entrySet()) {
            DynamoDBBaseModel model = (DynamoDBBaseModel) entry.getValue();
            Expression expression = model.getUpdateCondition();
            if (expression != null) {
                String expressionStr = expression.expression();
                if (expression.expressionNames() != null) {
                    for (Map.Entry<String, String> namesEntry : expression.expressionNames().entrySet()) {
                        expressionStr = expressionStr.replace(namesEntry.getKey(), namesEntry.getValue());
                    }
                }

                if (expression.expressionValues() != null) {
                    for (Map.Entry<String, AttributeValue> valuesEntry : expression.expressionValues().entrySet()) {
                        expressionStr = expressionStr.replace(valuesEntry.getKey(), valuesEntry.getValue().toString());
                    }
                }

                result.add(entry.getKey() + ":" + expressionStr);
            }
        }
        return result;
    }




    public static void clear() {
        isRollBack.remove();
        putItems.remove();
        deleteItems.remove();
        updateItemRequests.remove();
        localTransaction.remove();
        updateItems.remove();
    }
}

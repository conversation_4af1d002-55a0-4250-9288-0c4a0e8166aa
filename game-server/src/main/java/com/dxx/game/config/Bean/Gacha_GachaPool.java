
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Gacha_GachaPool {
    public Gacha_GachaPool(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        costItemid = _buf.get("costItemid").getAsInt();
        price1 = _buf.get("price1").getAsInt();
        price10 = _buf.get("price10").getAsInt();
        tokenId = _buf.get("tokenId").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("beginnerReward").getAsJsonArray(); beginnerReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  beginnerReward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("reward").getAsJsonArray(); reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  reward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("rareReward").getAsJsonArray(); rareReward = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  rareReward.add(_v0); }   }
        openType = _buf.get("openType").getAsInt();
        openPara1 = _buf.get("openPara1").getAsInt();
        openPara2 = _buf.get("openPara2").getAsInt();
        timeLimit = _buf.get("timeLimit").getAsInt();
        AtlasID = _buf.get("AtlasID").getAsInt();
        SpriteBg = _buf.get("SpriteBg").getAsString();
        SpriteBgBig = _buf.get("SpriteBgBig").getAsString();
        SpriteCardBack = _buf.get("SpriteCardBack").getAsString();
        ExtraChance = _buf.get("ExtraChance").getAsInt();
        OpenLevel = _buf.get("OpenLevel").getAsInt();
    }

    public static Gacha_GachaPool deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Gacha_GachaPool(_buf);
    }

    /**
     * 卡池id
     */
    public final int id;
    /**
     * 抽卡消耗的道具id（货币）
     */
    public final int costItemid;
    /**
     * 单抽消耗的道具数量
     */
    public final int price1;
    /**
     * 十连抽消耗的道具数量
     */
    public final int price10;
    /**
     * 支持的抽卡代币道具id<br/>（优先使用代币）
     */
    public final int tokenId;
    /**
     * 特定次数抽卡的奖励固定<br/>第n次,dropid,数量|第m次,dropid,数量
     */
    public final java.util.List<String> beginnerReward;
    /**
     * 随机奖励内容及权重：<br/>道具id,数量,权重|道具id,数量,权重
     */
    public final java.util.List<String> reward;
    /**
     * 连续10次抽卡不中红，下次必中
     */
    public final java.util.List<Integer> rareReward;
    /**
     * 卡池开启类型：0为永远开启，1为限时开启
     */
    public final int openType;
    /**
     * openType=1时，此字段为开启时间（时间戳）
     */
    public final int openPara1;
    /**
     * openType=1时，此字段为结束时间
     */
    public final int openPara2;
    /**
     * 每日抽卡次数限制
     */
    public final int timeLimit;
    /**
     * 资源图集ID
     */
    public final int AtlasID;
    /**
     * 小背景切片名称
     */
    public final String SpriteBg;
    /**
     * 大背景切片名称
     */
    public final String SpriteBgBig;
    /**
     * 卡背切片名称
     */
    public final String SpriteCardBack;
    /**
     * 被许愿的卡的额外概率倍数（百分比，原有权重*（1+这个值/100））
     */
    public final int ExtraChance;
    /**
     * 开启关卡<br/>（完成关卡之前不会出现）
     */
    public final int OpenLevel;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "costItemid:" + costItemid + ","
        + "price1:" + price1 + ","
        + "price10:" + price10 + ","
        + "tokenId:" + tokenId + ","
        + "beginnerReward:" + beginnerReward + ","
        + "reward:" + reward + ","
        + "rareReward:" + rareReward + ","
        + "openType:" + openType + ","
        + "openPara1:" + openPara1 + ","
        + "openPara2:" + openPara2 + ","
        + "timeLimit:" + timeLimit + ","
        + "AtlasID:" + AtlasID + ","
        + "SpriteBg:" + SpriteBg + ","
        + "SpriteBgBig:" + SpriteBgBig + ","
        + "SpriteCardBack:" + SpriteCardBack + ","
        + "ExtraChance:" + ExtraChance + ","
        + "OpenLevel:" + OpenLevel + ","
        + "}";
    }
}


var rp = require('request-promise');
var request = require("request");
var fs = require("fs");
var path = require("path");

// 读取配置文件
const conifg = fs.readFileSync(path.join(__dirname, "../../config.json"), 'utf-8');
const conifgObj = JSON.parse(conifg);
const configGameId = conifgObj["config_game_id"];

async function handler() {
	url = "http://config.gorilla10.com/game/getByGameId?key=a5f0a9b3c9e1c7e0&gameId=" + configGameId;
	var response = await rp({resolveWithFullResponse: true, method:'POST', uri:url});
	var statusCode = response.statusCode;
	if (statusCode != 200) {
		console.log('\x1B[31m%s\x1B[0m', "配置不存在, configGameId = " + configGameId);
		process.exit(1);
	}

	var gameData = JSON.parse(response.body);
	var gameName = gameData['GName'];

	if (gameName == "") {
		console.log('\x1B[31m%s\x1B[0m', "配置不存在, configGameId = " + configGameId);
		process.exit(1);
	} else {
		console.log('\x1B[32m' + "项目名称 : " + gameName + "\x1B[0m");	
	}
}

handler();


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: battle.proto

package com.dxx.game.dto;

public final class BattleProto {
  private BattleProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RChapterCombatReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Battle.RChapterCombatReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 章节ID
     * </pre>
     *
     * <code>int32 chapterId = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();

    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>int32 waveIndex = 2;</code>
     * @return The waveIndex.
     */
    int getWaveIndex();

    /**
     * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
     * @return Whether the unit field is set.
     */
    boolean hasUnit();
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
     * @return The unit.
     */
    com.dxx.game.dto.CommonProto.BattleUnitDto getUnit();
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
     */
    com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder();

    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 4;</code>
     * @return A list containing the skills.
     */
    java.util.List<java.lang.Integer> getSkillsList();
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 4;</code>
     * @return The count of skills.
     */
    int getSkillsCount();
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 4;</code>
     * @param index The index of the element to return.
     * @return The skills at the given index.
     */
    int getSkills(int index);

    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 5;</code>
     * @return The seed.
     */
    int getSeed();

    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getUnitsList();
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getUnits(int index);
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    int getUnitsCount();
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getUnitsOrBuilder(
        int index);
  }
  /**
   * <pre>
   * 章节
   * </pre>
   *
   * Protobuf type {@code Proto.Battle.RChapterCombatReq}
   */
  public static final class RChapterCombatReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Battle.RChapterCombatReq)
      RChapterCombatReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RChapterCombatReq.newBuilder() to construct.
    private RChapterCombatReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RChapterCombatReq() {
      skills_ = emptyIntList();
      units_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RChapterCombatReq();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RChapterCombatReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              chapterId_ = input.readInt32();
              break;
            }
            case 16: {

              waveIndex_ = input.readInt32();
              break;
            }
            case 26: {
              com.dxx.game.dto.CommonProto.BattleUnitDto.Builder subBuilder = null;
              if (unit_ != null) {
                subBuilder = unit_.toBuilder();
              }
              unit_ = input.readMessage(com.dxx.game.dto.CommonProto.BattleUnitDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(unit_);
                unit_ = subBuilder.buildPartial();
              }

              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                skills_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              skills_.addInt(input.readInt32());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                skills_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                skills_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 40: {

              seed_ = input.readInt32();
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                units_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000002;
              }
              units_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          skills_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          units_ = java.util.Collections.unmodifiableList(units_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.BattleProto.RChapterCombatReq.class, com.dxx.game.dto.BattleProto.RChapterCombatReq.Builder.class);
    }

    public static final int CHAPTERID_FIELD_NUMBER = 1;
    private int chapterId_;
    /**
     * <pre>
     * 章节ID
     * </pre>
     *
     * <code>int32 chapterId = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    public static final int WAVEINDEX_FIELD_NUMBER = 2;
    private int waveIndex_;
    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>int32 waveIndex = 2;</code>
     * @return The waveIndex.
     */
    @java.lang.Override
    public int getWaveIndex() {
      return waveIndex_;
    }

    public static final int UNIT_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.BattleUnitDto unit_;
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
     * @return Whether the unit field is set.
     */
    @java.lang.Override
    public boolean hasUnit() {
      return unit_ != null;
    }
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
     * @return The unit.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.BattleUnitDto getUnit() {
      return unit_ == null ? com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
    }
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder() {
      return getUnit();
    }

    public static final int SKILLS_FIELD_NUMBER = 4;
    private com.google.protobuf.Internal.IntList skills_;
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 4;</code>
     * @return A list containing the skills.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getSkillsList() {
      return skills_;
    }
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 4;</code>
     * @return The count of skills.
     */
    public int getSkillsCount() {
      return skills_.size();
    }
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 4;</code>
     * @param index The index of the element to return.
     * @return The skills at the given index.
     */
    public int getSkills(int index) {
      return skills_.getInt(index);
    }
    private int skillsMemoizedSerializedSize = -1;

    public static final int SEED_FIELD_NUMBER = 5;
    private int seed_;
    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 5;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    public static final int UNITS_FIELD_NUMBER = 6;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> units_;
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getUnitsList() {
      return units_;
    }
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getUnitsOrBuilderList() {
      return units_;
    }
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    @java.lang.Override
    public int getUnitsCount() {
      return units_.size();
    }
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getUnits(int index) {
      return units_.get(index);
    }
    /**
     * <pre>
     * 战斗单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getUnitsOrBuilder(
        int index) {
      return units_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (chapterId_ != 0) {
        output.writeInt32(1, chapterId_);
      }
      if (waveIndex_ != 0) {
        output.writeInt32(2, waveIndex_);
      }
      if (unit_ != null) {
        output.writeMessage(3, getUnit());
      }
      if (getSkillsList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(skillsMemoizedSerializedSize);
      }
      for (int i = 0; i < skills_.size(); i++) {
        output.writeInt32NoTag(skills_.getInt(i));
      }
      if (seed_ != 0) {
        output.writeInt32(5, seed_);
      }
      for (int i = 0; i < units_.size(); i++) {
        output.writeMessage(6, units_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chapterId_);
      }
      if (waveIndex_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, waveIndex_);
      }
      if (unit_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getUnit());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < skills_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(skills_.getInt(i));
        }
        size += dataSize;
        if (!getSkillsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        skillsMemoizedSerializedSize = dataSize;
      }
      if (seed_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, seed_);
      }
      for (int i = 0; i < units_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, units_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.BattleProto.RChapterCombatReq)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.BattleProto.RChapterCombatReq other = (com.dxx.game.dto.BattleProto.RChapterCombatReq) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (getWaveIndex()
          != other.getWaveIndex()) return false;
      if (hasUnit() != other.hasUnit()) return false;
      if (hasUnit()) {
        if (!getUnit()
            .equals(other.getUnit())) return false;
      }
      if (!getSkillsList()
          .equals(other.getSkillsList())) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (!getUnitsList()
          .equals(other.getUnitsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTERID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (37 * hash) + WAVEINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getWaveIndex();
      if (hasUnit()) {
        hash = (37 * hash) + UNIT_FIELD_NUMBER;
        hash = (53 * hash) + getUnit().hashCode();
      }
      if (getSkillsCount() > 0) {
        hash = (37 * hash) + SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getSkillsList().hashCode();
      }
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + getSeed();
      if (getUnitsCount() > 0) {
        hash = (37 * hash) + UNITS_FIELD_NUMBER;
        hash = (53 * hash) + getUnitsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.BattleProto.RChapterCombatReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 章节
     * </pre>
     *
     * Protobuf type {@code Proto.Battle.RChapterCombatReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Battle.RChapterCombatReq)
        com.dxx.game.dto.BattleProto.RChapterCombatReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.BattleProto.RChapterCombatReq.class, com.dxx.game.dto.BattleProto.RChapterCombatReq.Builder.class);
      }

      // Construct using com.dxx.game.dto.BattleProto.RChapterCombatReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getUnitsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chapterId_ = 0;

        waveIndex_ = 0;

        if (unitBuilder_ == null) {
          unit_ = null;
        } else {
          unit_ = null;
          unitBuilder_ = null;
        }
        skills_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        seed_ = 0;

        if (unitsBuilder_ == null) {
          units_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          unitsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatReq_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RChapterCombatReq getDefaultInstanceForType() {
        return com.dxx.game.dto.BattleProto.RChapterCombatReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RChapterCombatReq build() {
        com.dxx.game.dto.BattleProto.RChapterCombatReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RChapterCombatReq buildPartial() {
        com.dxx.game.dto.BattleProto.RChapterCombatReq result = new com.dxx.game.dto.BattleProto.RChapterCombatReq(this);
        int from_bitField0_ = bitField0_;
        result.chapterId_ = chapterId_;
        result.waveIndex_ = waveIndex_;
        if (unitBuilder_ == null) {
          result.unit_ = unit_;
        } else {
          result.unit_ = unitBuilder_.build();
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          skills_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.skills_ = skills_;
        result.seed_ = seed_;
        if (unitsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            units_ = java.util.Collections.unmodifiableList(units_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.units_ = units_;
        } else {
          result.units_ = unitsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.BattleProto.RChapterCombatReq) {
          return mergeFrom((com.dxx.game.dto.BattleProto.RChapterCombatReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.BattleProto.RChapterCombatReq other) {
        if (other == com.dxx.game.dto.BattleProto.RChapterCombatReq.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        if (other.getWaveIndex() != 0) {
          setWaveIndex(other.getWaveIndex());
        }
        if (other.hasUnit()) {
          mergeUnit(other.getUnit());
        }
        if (!other.skills_.isEmpty()) {
          if (skills_.isEmpty()) {
            skills_ = other.skills_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureSkillsIsMutable();
            skills_.addAll(other.skills_);
          }
          onChanged();
        }
        if (other.getSeed() != 0) {
          setSeed(other.getSeed());
        }
        if (unitsBuilder_ == null) {
          if (!other.units_.isEmpty()) {
            if (units_.isEmpty()) {
              units_ = other.units_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureUnitsIsMutable();
              units_.addAll(other.units_);
            }
            onChanged();
          }
        } else {
          if (!other.units_.isEmpty()) {
            if (unitsBuilder_.isEmpty()) {
              unitsBuilder_.dispose();
              unitsBuilder_ = null;
              units_ = other.units_;
              bitField0_ = (bitField0_ & ~0x00000002);
              unitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUnitsFieldBuilder() : null;
            } else {
              unitsBuilder_.addAllMessages(other.units_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.BattleProto.RChapterCombatReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.BattleProto.RChapterCombatReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <pre>
       * 章节ID
       * </pre>
       *
       * <code>int32 chapterId = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <pre>
       * 章节ID
       * </pre>
       *
       * <code>int32 chapterId = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {
        
        chapterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 章节ID
       * </pre>
       *
       * <code>int32 chapterId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        
        chapterId_ = 0;
        onChanged();
        return this;
      }

      private int waveIndex_ ;
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>int32 waveIndex = 2;</code>
       * @return The waveIndex.
       */
      @java.lang.Override
      public int getWaveIndex() {
        return waveIndex_;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>int32 waveIndex = 2;</code>
       * @param value The waveIndex to set.
       * @return This builder for chaining.
       */
      public Builder setWaveIndex(int value) {
        
        waveIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>int32 waveIndex = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWaveIndex() {
        
        waveIndex_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.BattleUnitDto unit_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder> unitBuilder_;
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       * @return Whether the unit field is set.
       */
      public boolean hasUnit() {
        return unitBuilder_ != null || unit_ != null;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       * @return The unit.
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDto getUnit() {
        if (unitBuilder_ == null) {
          return unit_ == null ? com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
        } else {
          return unitBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      public Builder setUnit(com.dxx.game.dto.CommonProto.BattleUnitDto value) {
        if (unitBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          unit_ = value;
          onChanged();
        } else {
          unitBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      public Builder setUnit(
          com.dxx.game.dto.CommonProto.BattleUnitDto.Builder builderForValue) {
        if (unitBuilder_ == null) {
          unit_ = builderForValue.build();
          onChanged();
        } else {
          unitBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      public Builder mergeUnit(com.dxx.game.dto.CommonProto.BattleUnitDto value) {
        if (unitBuilder_ == null) {
          if (unit_ != null) {
            unit_ =
              com.dxx.game.dto.CommonProto.BattleUnitDto.newBuilder(unit_).mergeFrom(value).buildPartial();
          } else {
            unit_ = value;
          }
          onChanged();
        } else {
          unitBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      public Builder clearUnit() {
        if (unitBuilder_ == null) {
          unit_ = null;
          onChanged();
        } else {
          unit_ = null;
          unitBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDto.Builder getUnitBuilder() {
        
        onChanged();
        return getUnitFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder() {
        if (unitBuilder_ != null) {
          return unitBuilder_.getMessageOrBuilder();
        } else {
          return unit_ == null ?
              com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
        }
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder> 
          getUnitFieldBuilder() {
        if (unitBuilder_ == null) {
          unitBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder>(
                  getUnit(),
                  getParentForChildren(),
                  isClean());
          unit_ = null;
        }
        return unitBuilder_;
      }

      private com.google.protobuf.Internal.IntList skills_ = emptyIntList();
      private void ensureSkillsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          skills_ = mutableCopy(skills_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @return A list containing the skills.
       */
      public java.util.List<java.lang.Integer>
          getSkillsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(skills_) : skills_;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @return The count of skills.
       */
      public int getSkillsCount() {
        return skills_.size();
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @param index The index of the element to return.
       * @return The skills at the given index.
       */
      public int getSkills(int index) {
        return skills_.getInt(index);
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @param index The index to set the value at.
       * @param value The skills to set.
       * @return This builder for chaining.
       */
      public Builder setSkills(
          int index, int value) {
        ensureSkillsIsMutable();
        skills_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @param value The skills to add.
       * @return This builder for chaining.
       */
      public Builder addSkills(int value) {
        ensureSkillsIsMutable();
        skills_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @param values The skills to add.
       * @return This builder for chaining.
       */
      public Builder addAllSkills(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureSkillsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skills_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkills() {
        skills_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private int seed_ ;
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 5;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 5;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        
        seed_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> units_ =
        java.util.Collections.emptyList();
      private void ensureUnitsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          units_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(units_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> unitsBuilder_;

      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getUnitsList() {
        if (unitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(units_);
        } else {
          return unitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public int getUnitsCount() {
        if (unitsBuilder_ == null) {
          return units_.size();
        } else {
          return unitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getUnits(int index) {
        if (unitsBuilder_ == null) {
          return units_.get(index);
        } else {
          return unitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder setUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (unitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUnitsIsMutable();
          units_.set(index, value);
          onChanged();
        } else {
          unitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder setUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.set(index, builderForValue.build());
          onChanged();
        } else {
          unitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder addUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (unitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUnitsIsMutable();
          units_.add(value);
          onChanged();
        } else {
          unitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder addUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (unitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUnitsIsMutable();
          units_.add(index, value);
          onChanged();
        } else {
          unitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder addUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.add(builderForValue.build());
          onChanged();
        } else {
          unitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder addUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.add(index, builderForValue.build());
          onChanged();
        } else {
          unitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder addAllUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, units_);
          onChanged();
        } else {
          unitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder clearUnits() {
        if (unitsBuilder_ == null) {
          units_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          unitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public Builder removeUnits(int index) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.remove(index);
          onChanged();
        } else {
          unitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getUnitsBuilder(
          int index) {
        return getUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getUnitsOrBuilder(
          int index) {
        if (unitsBuilder_ == null) {
          return units_.get(index);  } else {
          return unitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getUnitsOrBuilderList() {
        if (unitsBuilder_ != null) {
          return unitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(units_);
        }
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addUnitsBuilder() {
        return getUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addUnitsBuilder(
          int index) {
        return getUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto units = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getUnitsBuilderList() {
        return getUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getUnitsFieldBuilder() {
        if (unitsBuilder_ == null) {
          unitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  units_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          units_ = null;
        }
        return unitsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Battle.RChapterCombatReq)
    }

    // @@protoc_insertion_point(class_scope:Proto.Battle.RChapterCombatReq)
    private static final com.dxx.game.dto.BattleProto.RChapterCombatReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.BattleProto.RChapterCombatReq();
    }

    public static com.dxx.game.dto.BattleProto.RChapterCombatReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RChapterCombatReq>
        PARSER = new com.google.protobuf.AbstractParser<RChapterCombatReq>() {
      @java.lang.Override
      public RChapterCombatReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RChapterCombatReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RChapterCombatReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RChapterCombatReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.BattleProto.RChapterCombatReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RChapterCombatRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Battle.RChapterCombatResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 章节ID
     * </pre>
     *
     * <code>int32 chapterId = 2;</code>
     * @return The chapterId.
     */
    int getChapterId();

    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>int32 waveIndex = 3;</code>
     * @return The waveIndex.
     */
    int getWaveIndex();

    /**
     * <pre>
     * 0:lose 1:win
     * </pre>
     *
     * <code>int32 result = 4;</code>
     * @return The result.
     */
    int getResult();

    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 6;</code>
     * @return The seed.
     */
    int getSeed();

    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getStartUnitsList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index);
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    int getStartUnitsCount();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getEndUnitsList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index);
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    int getEndUnitsCount();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index);

    /**
     * <code>int64 power = 9;</code>
     * @return The power.
     */
    long getPower();
  }
  /**
   * Protobuf type {@code Proto.Battle.RChapterCombatResp}
   */
  public static final class RChapterCombatResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Battle.RChapterCombatResp)
      RChapterCombatRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RChapterCombatResp.newBuilder() to construct.
    private RChapterCombatResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RChapterCombatResp() {
      startUnits_ = java.util.Collections.emptyList();
      endUnits_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RChapterCombatResp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RChapterCombatResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 16: {

              chapterId_ = input.readInt32();
              break;
            }
            case 24: {

              waveIndex_ = input.readInt32();
              break;
            }
            case 32: {

              result_ = input.readInt32();
              break;
            }
            case 48: {

              seed_ = input.readInt32();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              startUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000002;
              }
              endUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 72: {

              power_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.BattleProto.RChapterCombatResp.class, com.dxx.game.dto.BattleProto.RChapterCombatResp.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int CHAPTERID_FIELD_NUMBER = 2;
    private int chapterId_;
    /**
     * <pre>
     * 章节ID
     * </pre>
     *
     * <code>int32 chapterId = 2;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    public static final int WAVEINDEX_FIELD_NUMBER = 3;
    private int waveIndex_;
    /**
     * <pre>
     * 关卡ID
     * </pre>
     *
     * <code>int32 waveIndex = 3;</code>
     * @return The waveIndex.
     */
    @java.lang.Override
    public int getWaveIndex() {
      return waveIndex_;
    }

    public static final int RESULT_FIELD_NUMBER = 4;
    private int result_;
    /**
     * <pre>
     * 0:lose 1:win
     * </pre>
     *
     * <code>int32 result = 4;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    public static final int SEED_FIELD_NUMBER = 6;
    private int seed_;
    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 6;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    public static final int STARTUNITS_FIELD_NUMBER = 7;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_;
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    @java.lang.Override
    public int getStartUnitsCount() {
      return startUnits_.size();
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
      return startUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index) {
      return startUnits_.get(index);
    }

    public static final int ENDUNITS_FIELD_NUMBER = 8;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_;
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    @java.lang.Override
    public int getEndUnitsCount() {
      return endUnits_.size();
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
      return endUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index) {
      return endUnits_.get(index);
    }

    public static final int POWER_FIELD_NUMBER = 9;
    private long power_;
    /**
     * <code>int64 power = 9;</code>
     * @return The power.
     */
    @java.lang.Override
    public long getPower() {
      return power_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (chapterId_ != 0) {
        output.writeInt32(2, chapterId_);
      }
      if (waveIndex_ != 0) {
        output.writeInt32(3, waveIndex_);
      }
      if (result_ != 0) {
        output.writeInt32(4, result_);
      }
      if (seed_ != 0) {
        output.writeInt32(6, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        output.writeMessage(7, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        output.writeMessage(8, endUnits_.get(i));
      }
      if (power_ != 0L) {
        output.writeInt64(9, power_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, chapterId_);
      }
      if (waveIndex_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, waveIndex_);
      }
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, result_);
      }
      if (seed_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, endUnits_.get(i));
      }
      if (power_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, power_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.BattleProto.RChapterCombatResp)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.BattleProto.RChapterCombatResp other = (com.dxx.game.dto.BattleProto.RChapterCombatResp) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getChapterId()
          != other.getChapterId()) return false;
      if (getWaveIndex()
          != other.getWaveIndex()) return false;
      if (getResult()
          != other.getResult()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (!getStartUnitsList()
          .equals(other.getStartUnitsList())) return false;
      if (!getEndUnitsList()
          .equals(other.getEndUnitsList())) return false;
      if (getPower()
          != other.getPower()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + CHAPTERID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (37 * hash) + WAVEINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getWaveIndex();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + getSeed();
      if (getStartUnitsCount() > 0) {
        hash = (37 * hash) + STARTUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getStartUnitsList().hashCode();
      }
      if (getEndUnitsCount() > 0) {
        hash = (37 * hash) + ENDUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getEndUnitsList().hashCode();
      }
      hash = (37 * hash) + POWER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPower());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RChapterCombatResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.BattleProto.RChapterCombatResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Battle.RChapterCombatResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Battle.RChapterCombatResp)
        com.dxx.game.dto.BattleProto.RChapterCombatRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.BattleProto.RChapterCombatResp.class, com.dxx.game.dto.BattleProto.RChapterCombatResp.Builder.class);
      }

      // Construct using com.dxx.game.dto.BattleProto.RChapterCombatResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getStartUnitsFieldBuilder();
          getEndUnitsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        chapterId_ = 0;

        waveIndex_ = 0;

        result_ = 0;

        seed_ = 0;

        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          startUnitsBuilder_.clear();
        }
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          endUnitsBuilder_.clear();
        }
        power_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RChapterCombatResp_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RChapterCombatResp getDefaultInstanceForType() {
        return com.dxx.game.dto.BattleProto.RChapterCombatResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RChapterCombatResp build() {
        com.dxx.game.dto.BattleProto.RChapterCombatResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RChapterCombatResp buildPartial() {
        com.dxx.game.dto.BattleProto.RChapterCombatResp result = new com.dxx.game.dto.BattleProto.RChapterCombatResp(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        result.chapterId_ = chapterId_;
        result.waveIndex_ = waveIndex_;
        result.result_ = result_;
        result.seed_ = seed_;
        if (startUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.startUnits_ = startUnits_;
        } else {
          result.startUnits_ = startUnitsBuilder_.build();
        }
        if (endUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.endUnits_ = endUnits_;
        } else {
          result.endUnits_ = endUnitsBuilder_.build();
        }
        result.power_ = power_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.BattleProto.RChapterCombatResp) {
          return mergeFrom((com.dxx.game.dto.BattleProto.RChapterCombatResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.BattleProto.RChapterCombatResp other) {
        if (other == com.dxx.game.dto.BattleProto.RChapterCombatResp.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        if (other.getWaveIndex() != 0) {
          setWaveIndex(other.getWaveIndex());
        }
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getSeed() != 0) {
          setSeed(other.getSeed());
        }
        if (startUnitsBuilder_ == null) {
          if (!other.startUnits_.isEmpty()) {
            if (startUnits_.isEmpty()) {
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureStartUnitsIsMutable();
              startUnits_.addAll(other.startUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.startUnits_.isEmpty()) {
            if (startUnitsBuilder_.isEmpty()) {
              startUnitsBuilder_.dispose();
              startUnitsBuilder_ = null;
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
              startUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getStartUnitsFieldBuilder() : null;
            } else {
              startUnitsBuilder_.addAllMessages(other.startUnits_);
            }
          }
        }
        if (endUnitsBuilder_ == null) {
          if (!other.endUnits_.isEmpty()) {
            if (endUnits_.isEmpty()) {
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureEndUnitsIsMutable();
              endUnits_.addAll(other.endUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.endUnits_.isEmpty()) {
            if (endUnitsBuilder_.isEmpty()) {
              endUnitsBuilder_.dispose();
              endUnitsBuilder_ = null;
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
              endUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEndUnitsFieldBuilder() : null;
            } else {
              endUnitsBuilder_.addAllMessages(other.endUnits_);
            }
          }
        }
        if (other.getPower() != 0L) {
          setPower(other.getPower());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.BattleProto.RChapterCombatResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.BattleProto.RChapterCombatResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private int chapterId_ ;
      /**
       * <pre>
       * 章节ID
       * </pre>
       *
       * <code>int32 chapterId = 2;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <pre>
       * 章节ID
       * </pre>
       *
       * <code>int32 chapterId = 2;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {
        
        chapterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 章节ID
       * </pre>
       *
       * <code>int32 chapterId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        
        chapterId_ = 0;
        onChanged();
        return this;
      }

      private int waveIndex_ ;
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>int32 waveIndex = 3;</code>
       * @return The waveIndex.
       */
      @java.lang.Override
      public int getWaveIndex() {
        return waveIndex_;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>int32 waveIndex = 3;</code>
       * @param value The waveIndex to set.
       * @return This builder for chaining.
       */
      public Builder setWaveIndex(int value) {
        
        waveIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关卡ID
       * </pre>
       *
       * <code>int32 waveIndex = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearWaveIndex() {
        
        waveIndex_ = 0;
        onChanged();
        return this;
      }

      private int result_ ;
      /**
       * <pre>
       * 0:lose 1:win
       * </pre>
       *
       * <code>int32 result = 4;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       * 0:lose 1:win
       * </pre>
       *
       * <code>int32 result = 4;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0:lose 1:win
       * </pre>
       *
       * <code>int32 result = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int seed_ ;
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 6;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 6;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        
        seed_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_ =
        java.util.Collections.emptyList();
      private void ensureStartUnitsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(startUnits_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> startUnitsBuilder_;

      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
        if (startUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(startUnits_);
        } else {
          return startUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public int getStartUnitsCount() {
        if (startUnitsBuilder_ == null) {
          return startUnits_.size();
        } else {
          return startUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);
        } else {
          return startUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.set(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder addStartUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder addStartUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder addAllStartUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, startUnits_);
          onChanged();
        } else {
          startUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder clearStartUnits() {
        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          startUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public Builder removeStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.remove(index);
          onChanged();
        } else {
          startUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
          int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);  } else {
          return startUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getStartUnitsOrBuilderList() {
        if (startUnitsBuilder_ != null) {
          return startUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(startUnits_);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder() {
        return getStartUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 7;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getStartUnitsBuilderList() {
        return getStartUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getStartUnitsFieldBuilder() {
        if (startUnitsBuilder_ == null) {
          startUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  startUnits_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          startUnits_ = null;
        }
        return startUnitsBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_ =
        java.util.Collections.emptyList();
      private void ensureEndUnitsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(endUnits_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> endUnitsBuilder_;

      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
        if (endUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(endUnits_);
        } else {
          return endUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public int getEndUnitsCount() {
        if (endUnitsBuilder_ == null) {
          return endUnits_.size();
        } else {
          return endUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);
        } else {
          return endUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.set(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder addEndUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder addEndUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder addAllEndUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, endUnits_);
          onChanged();
        } else {
          endUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder clearEndUnits() {
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          endUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public Builder removeEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.remove(index);
          onChanged();
        } else {
          endUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
          int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);  } else {
          return endUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getEndUnitsOrBuilderList() {
        if (endUnitsBuilder_ != null) {
          return endUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(endUnits_);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder() {
        return getEndUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 8;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getEndUnitsBuilderList() {
        return getEndUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getEndUnitsFieldBuilder() {
        if (endUnitsBuilder_ == null) {
          endUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  endUnits_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          endUnits_ = null;
        }
        return endUnitsBuilder_;
      }

      private long power_ ;
      /**
       * <code>int64 power = 9;</code>
       * @return The power.
       */
      @java.lang.Override
      public long getPower() {
        return power_;
      }
      /**
       * <code>int64 power = 9;</code>
       * @param value The power to set.
       * @return This builder for chaining.
       */
      public Builder setPower(long value) {
        
        power_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 power = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearPower() {
        
        power_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Battle.RChapterCombatResp)
    }

    // @@protoc_insertion_point(class_scope:Proto.Battle.RChapterCombatResp)
    private static final com.dxx.game.dto.BattleProto.RChapterCombatResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.BattleProto.RChapterCombatResp();
    }

    public static com.dxx.game.dto.BattleProto.RChapterCombatResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RChapterCombatResp>
        PARSER = new com.google.protobuf.AbstractParser<RChapterCombatResp>() {
      @java.lang.Override
      public RChapterCombatResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RChapterCombatResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RChapterCombatResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RChapterCombatResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.BattleProto.RChapterCombatResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RGuildBossCombatReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Battle.RGuildBossCombatReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * bossId
     * </pre>
     *
     * <code>int32 bossId = 1;</code>
     * @return The bossId.
     */
    int getBossId();

    /**
     * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
     * @return Whether the unit field is set.
     */
    boolean hasUnit();
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
     * @return The unit.
     */
    com.dxx.game.dto.CommonProto.BattleUnitDto getUnit();
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
     */
    com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder();

    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 3;</code>
     * @return The seed.
     */
    int getSeed();
  }
  /**
   * <pre>
   * 公会boss
   * </pre>
   *
   * Protobuf type {@code Proto.Battle.RGuildBossCombatReq}
   */
  public static final class RGuildBossCombatReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Battle.RGuildBossCombatReq)
      RGuildBossCombatReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RGuildBossCombatReq.newBuilder() to construct.
    private RGuildBossCombatReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RGuildBossCombatReq() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RGuildBossCombatReq();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RGuildBossCombatReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              bossId_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.BattleUnitDto.Builder subBuilder = null;
              if (unit_ != null) {
                subBuilder = unit_.toBuilder();
              }
              unit_ = input.readMessage(com.dxx.game.dto.CommonProto.BattleUnitDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(unit_);
                unit_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              seed_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.BattleProto.RGuildBossCombatReq.class, com.dxx.game.dto.BattleProto.RGuildBossCombatReq.Builder.class);
    }

    public static final int BOSSID_FIELD_NUMBER = 1;
    private int bossId_;
    /**
     * <pre>
     * bossId
     * </pre>
     *
     * <code>int32 bossId = 1;</code>
     * @return The bossId.
     */
    @java.lang.Override
    public int getBossId() {
      return bossId_;
    }

    public static final int UNIT_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.BattleUnitDto unit_;
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
     * @return Whether the unit field is set.
     */
    @java.lang.Override
    public boolean hasUnit() {
      return unit_ != null;
    }
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
     * @return The unit.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.BattleUnitDto getUnit() {
      return unit_ == null ? com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
    }
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder() {
      return getUnit();
    }

    public static final int SEED_FIELD_NUMBER = 3;
    private int seed_;
    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 3;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (bossId_ != 0) {
        output.writeInt32(1, bossId_);
      }
      if (unit_ != null) {
        output.writeMessage(2, getUnit());
      }
      if (seed_ != 0) {
        output.writeInt32(3, seed_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (bossId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, bossId_);
      }
      if (unit_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getUnit());
      }
      if (seed_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, seed_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.BattleProto.RGuildBossCombatReq)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.BattleProto.RGuildBossCombatReq other = (com.dxx.game.dto.BattleProto.RGuildBossCombatReq) obj;

      if (getBossId()
          != other.getBossId()) return false;
      if (hasUnit() != other.hasUnit()) return false;
      if (hasUnit()) {
        if (!getUnit()
            .equals(other.getUnit())) return false;
      }
      if (getSeed()
          != other.getSeed()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BOSSID_FIELD_NUMBER;
      hash = (53 * hash) + getBossId();
      if (hasUnit()) {
        hash = (37 * hash) + UNIT_FIELD_NUMBER;
        hash = (53 * hash) + getUnit().hashCode();
      }
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + getSeed();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.BattleProto.RGuildBossCombatReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 公会boss
     * </pre>
     *
     * Protobuf type {@code Proto.Battle.RGuildBossCombatReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Battle.RGuildBossCombatReq)
        com.dxx.game.dto.BattleProto.RGuildBossCombatReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.BattleProto.RGuildBossCombatReq.class, com.dxx.game.dto.BattleProto.RGuildBossCombatReq.Builder.class);
      }

      // Construct using com.dxx.game.dto.BattleProto.RGuildBossCombatReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bossId_ = 0;

        if (unitBuilder_ == null) {
          unit_ = null;
        } else {
          unit_ = null;
          unitBuilder_ = null;
        }
        seed_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatReq_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RGuildBossCombatReq getDefaultInstanceForType() {
        return com.dxx.game.dto.BattleProto.RGuildBossCombatReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RGuildBossCombatReq build() {
        com.dxx.game.dto.BattleProto.RGuildBossCombatReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RGuildBossCombatReq buildPartial() {
        com.dxx.game.dto.BattleProto.RGuildBossCombatReq result = new com.dxx.game.dto.BattleProto.RGuildBossCombatReq(this);
        result.bossId_ = bossId_;
        if (unitBuilder_ == null) {
          result.unit_ = unit_;
        } else {
          result.unit_ = unitBuilder_.build();
        }
        result.seed_ = seed_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.BattleProto.RGuildBossCombatReq) {
          return mergeFrom((com.dxx.game.dto.BattleProto.RGuildBossCombatReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.BattleProto.RGuildBossCombatReq other) {
        if (other == com.dxx.game.dto.BattleProto.RGuildBossCombatReq.getDefaultInstance()) return this;
        if (other.getBossId() != 0) {
          setBossId(other.getBossId());
        }
        if (other.hasUnit()) {
          mergeUnit(other.getUnit());
        }
        if (other.getSeed() != 0) {
          setSeed(other.getSeed());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.BattleProto.RGuildBossCombatReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.BattleProto.RGuildBossCombatReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int bossId_ ;
      /**
       * <pre>
       * bossId
       * </pre>
       *
       * <code>int32 bossId = 1;</code>
       * @return The bossId.
       */
      @java.lang.Override
      public int getBossId() {
        return bossId_;
      }
      /**
       * <pre>
       * bossId
       * </pre>
       *
       * <code>int32 bossId = 1;</code>
       * @param value The bossId to set.
       * @return This builder for chaining.
       */
      public Builder setBossId(int value) {
        
        bossId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * bossId
       * </pre>
       *
       * <code>int32 bossId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBossId() {
        
        bossId_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.BattleUnitDto unit_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder> unitBuilder_;
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       * @return Whether the unit field is set.
       */
      public boolean hasUnit() {
        return unitBuilder_ != null || unit_ != null;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       * @return The unit.
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDto getUnit() {
        if (unitBuilder_ == null) {
          return unit_ == null ? com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
        } else {
          return unitBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      public Builder setUnit(com.dxx.game.dto.CommonProto.BattleUnitDto value) {
        if (unitBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          unit_ = value;
          onChanged();
        } else {
          unitBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      public Builder setUnit(
          com.dxx.game.dto.CommonProto.BattleUnitDto.Builder builderForValue) {
        if (unitBuilder_ == null) {
          unit_ = builderForValue.build();
          onChanged();
        } else {
          unitBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      public Builder mergeUnit(com.dxx.game.dto.CommonProto.BattleUnitDto value) {
        if (unitBuilder_ == null) {
          if (unit_ != null) {
            unit_ =
              com.dxx.game.dto.CommonProto.BattleUnitDto.newBuilder(unit_).mergeFrom(value).buildPartial();
          } else {
            unit_ = value;
          }
          onChanged();
        } else {
          unitBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      public Builder clearUnit() {
        if (unitBuilder_ == null) {
          unit_ = null;
          onChanged();
        } else {
          unit_ = null;
          unitBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDto.Builder getUnitBuilder() {
        
        onChanged();
        return getUnitFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder() {
        if (unitBuilder_ != null) {
          return unitBuilder_.getMessageOrBuilder();
        } else {
          return unit_ == null ?
              com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
        }
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder> 
          getUnitFieldBuilder() {
        if (unitBuilder_ == null) {
          unitBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder>(
                  getUnit(),
                  getParentForChildren(),
                  isClean());
          unit_ = null;
        }
        return unitBuilder_;
      }

      private int seed_ ;
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 3;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 3;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        
        seed_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Battle.RGuildBossCombatReq)
    }

    // @@protoc_insertion_point(class_scope:Proto.Battle.RGuildBossCombatReq)
    private static final com.dxx.game.dto.BattleProto.RGuildBossCombatReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.BattleProto.RGuildBossCombatReq();
    }

    public static com.dxx.game.dto.BattleProto.RGuildBossCombatReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RGuildBossCombatReq>
        PARSER = new com.google.protobuf.AbstractParser<RGuildBossCombatReq>() {
      @java.lang.Override
      public RGuildBossCombatReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RGuildBossCombatReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RGuildBossCombatReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RGuildBossCombatReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.BattleProto.RGuildBossCombatReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RGuildBossCombatRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Battle.RGuildBossCombatResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 2;</code>
     * @return The seed.
     */
    int getSeed();

    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getStartUnitsList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index);
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    int getStartUnitsCount();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getEndUnitsList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index);
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    int getEndUnitsCount();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 伤害量
     * </pre>
     *
     * <code>uint64 damage = 5;</code>
     * @return The damage.
     */
    long getDamage();
  }
  /**
   * Protobuf type {@code Proto.Battle.RGuildBossCombatResp}
   */
  public static final class RGuildBossCombatResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Battle.RGuildBossCombatResp)
      RGuildBossCombatRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RGuildBossCombatResp.newBuilder() to construct.
    private RGuildBossCombatResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RGuildBossCombatResp() {
      startUnits_ = java.util.Collections.emptyList();
      endUnits_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RGuildBossCombatResp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RGuildBossCombatResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 16: {

              seed_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              startUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000002;
              }
              endUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 40: {

              damage_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.BattleProto.RGuildBossCombatResp.class, com.dxx.game.dto.BattleProto.RGuildBossCombatResp.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SEED_FIELD_NUMBER = 2;
    private int seed_;
    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 2;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    public static final int STARTUNITS_FIELD_NUMBER = 3;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_;
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    @java.lang.Override
    public int getStartUnitsCount() {
      return startUnits_.size();
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
      return startUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index) {
      return startUnits_.get(index);
    }

    public static final int ENDUNITS_FIELD_NUMBER = 4;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_;
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    @java.lang.Override
    public int getEndUnitsCount() {
      return endUnits_.size();
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
      return endUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index) {
      return endUnits_.get(index);
    }

    public static final int DAMAGE_FIELD_NUMBER = 5;
    private long damage_;
    /**
     * <pre>
     * 伤害量
     * </pre>
     *
     * <code>uint64 damage = 5;</code>
     * @return The damage.
     */
    @java.lang.Override
    public long getDamage() {
      return damage_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (seed_ != 0) {
        output.writeInt32(2, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        output.writeMessage(3, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        output.writeMessage(4, endUnits_.get(i));
      }
      if (damage_ != 0L) {
        output.writeUInt64(5, damage_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (seed_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, endUnits_.get(i));
      }
      if (damage_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, damage_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.BattleProto.RGuildBossCombatResp)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.BattleProto.RGuildBossCombatResp other = (com.dxx.game.dto.BattleProto.RGuildBossCombatResp) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (!getStartUnitsList()
          .equals(other.getStartUnitsList())) return false;
      if (!getEndUnitsList()
          .equals(other.getEndUnitsList())) return false;
      if (getDamage()
          != other.getDamage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + getSeed();
      if (getStartUnitsCount() > 0) {
        hash = (37 * hash) + STARTUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getStartUnitsList().hashCode();
      }
      if (getEndUnitsCount() > 0) {
        hash = (37 * hash) + ENDUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getEndUnitsList().hashCode();
      }
      hash = (37 * hash) + DAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDamage());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.BattleProto.RGuildBossCombatResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Battle.RGuildBossCombatResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Battle.RGuildBossCombatResp)
        com.dxx.game.dto.BattleProto.RGuildBossCombatRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.BattleProto.RGuildBossCombatResp.class, com.dxx.game.dto.BattleProto.RGuildBossCombatResp.Builder.class);
      }

      // Construct using com.dxx.game.dto.BattleProto.RGuildBossCombatResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getStartUnitsFieldBuilder();
          getEndUnitsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        seed_ = 0;

        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          startUnitsBuilder_.clear();
        }
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          endUnitsBuilder_.clear();
        }
        damage_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RGuildBossCombatResp_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RGuildBossCombatResp getDefaultInstanceForType() {
        return com.dxx.game.dto.BattleProto.RGuildBossCombatResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RGuildBossCombatResp build() {
        com.dxx.game.dto.BattleProto.RGuildBossCombatResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RGuildBossCombatResp buildPartial() {
        com.dxx.game.dto.BattleProto.RGuildBossCombatResp result = new com.dxx.game.dto.BattleProto.RGuildBossCombatResp(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        result.seed_ = seed_;
        if (startUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.startUnits_ = startUnits_;
        } else {
          result.startUnits_ = startUnitsBuilder_.build();
        }
        if (endUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.endUnits_ = endUnits_;
        } else {
          result.endUnits_ = endUnitsBuilder_.build();
        }
        result.damage_ = damage_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.BattleProto.RGuildBossCombatResp) {
          return mergeFrom((com.dxx.game.dto.BattleProto.RGuildBossCombatResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.BattleProto.RGuildBossCombatResp other) {
        if (other == com.dxx.game.dto.BattleProto.RGuildBossCombatResp.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getSeed() != 0) {
          setSeed(other.getSeed());
        }
        if (startUnitsBuilder_ == null) {
          if (!other.startUnits_.isEmpty()) {
            if (startUnits_.isEmpty()) {
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureStartUnitsIsMutable();
              startUnits_.addAll(other.startUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.startUnits_.isEmpty()) {
            if (startUnitsBuilder_.isEmpty()) {
              startUnitsBuilder_.dispose();
              startUnitsBuilder_ = null;
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
              startUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getStartUnitsFieldBuilder() : null;
            } else {
              startUnitsBuilder_.addAllMessages(other.startUnits_);
            }
          }
        }
        if (endUnitsBuilder_ == null) {
          if (!other.endUnits_.isEmpty()) {
            if (endUnits_.isEmpty()) {
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureEndUnitsIsMutable();
              endUnits_.addAll(other.endUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.endUnits_.isEmpty()) {
            if (endUnitsBuilder_.isEmpty()) {
              endUnitsBuilder_.dispose();
              endUnitsBuilder_ = null;
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
              endUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEndUnitsFieldBuilder() : null;
            } else {
              endUnitsBuilder_.addAllMessages(other.endUnits_);
            }
          }
        }
        if (other.getDamage() != 0L) {
          setDamage(other.getDamage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.BattleProto.RGuildBossCombatResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.BattleProto.RGuildBossCombatResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private int seed_ ;
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 2;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 2;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        
        seed_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_ =
        java.util.Collections.emptyList();
      private void ensureStartUnitsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(startUnits_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> startUnitsBuilder_;

      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
        if (startUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(startUnits_);
        } else {
          return startUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public int getStartUnitsCount() {
        if (startUnitsBuilder_ == null) {
          return startUnits_.size();
        } else {
          return startUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);
        } else {
          return startUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.set(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder addStartUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder addStartUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder addAllStartUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, startUnits_);
          onChanged();
        } else {
          startUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder clearStartUnits() {
        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          startUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public Builder removeStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.remove(index);
          onChanged();
        } else {
          startUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
          int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);  } else {
          return startUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getStartUnitsOrBuilderList() {
        if (startUnitsBuilder_ != null) {
          return startUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(startUnits_);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder() {
        return getStartUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getStartUnitsBuilderList() {
        return getStartUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getStartUnitsFieldBuilder() {
        if (startUnitsBuilder_ == null) {
          startUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  startUnits_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          startUnits_ = null;
        }
        return startUnitsBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_ =
        java.util.Collections.emptyList();
      private void ensureEndUnitsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(endUnits_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> endUnitsBuilder_;

      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
        if (endUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(endUnits_);
        } else {
          return endUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public int getEndUnitsCount() {
        if (endUnitsBuilder_ == null) {
          return endUnits_.size();
        } else {
          return endUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);
        } else {
          return endUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.set(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder addEndUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder addEndUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder addAllEndUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, endUnits_);
          onChanged();
        } else {
          endUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder clearEndUnits() {
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          endUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public Builder removeEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.remove(index);
          onChanged();
        } else {
          endUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
          int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);  } else {
          return endUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getEndUnitsOrBuilderList() {
        if (endUnitsBuilder_ != null) {
          return endUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(endUnits_);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder() {
        return getEndUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getEndUnitsBuilderList() {
        return getEndUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getEndUnitsFieldBuilder() {
        if (endUnitsBuilder_ == null) {
          endUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  endUnits_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          endUnits_ = null;
        }
        return endUnitsBuilder_;
      }

      private long damage_ ;
      /**
       * <pre>
       * 伤害量
       * </pre>
       *
       * <code>uint64 damage = 5;</code>
       * @return The damage.
       */
      @java.lang.Override
      public long getDamage() {
        return damage_;
      }
      /**
       * <pre>
       * 伤害量
       * </pre>
       *
       * <code>uint64 damage = 5;</code>
       * @param value The damage to set.
       * @return This builder for chaining.
       */
      public Builder setDamage(long value) {
        
        damage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 伤害量
       * </pre>
       *
       * <code>uint64 damage = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDamage() {
        
        damage_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Battle.RGuildBossCombatResp)
    }

    // @@protoc_insertion_point(class_scope:Proto.Battle.RGuildBossCombatResp)
    private static final com.dxx.game.dto.BattleProto.RGuildBossCombatResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.BattleProto.RGuildBossCombatResp();
    }

    public static com.dxx.game.dto.BattleProto.RGuildBossCombatResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RGuildBossCombatResp>
        PARSER = new com.google.protobuf.AbstractParser<RGuildBossCombatResp>() {
      @java.lang.Override
      public RGuildBossCombatResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RGuildBossCombatResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RGuildBossCombatResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RGuildBossCombatResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.BattleProto.RGuildBossCombatResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RpcPowerReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Battle.RpcPowerReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
     * @return Whether the unit field is set.
     */
    boolean hasUnit();
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
     * @return The unit.
     */
    com.dxx.game.dto.CommonProto.BattleUnitDto getUnit();
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
     */
    com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder();
  }
  /**
   * Protobuf type {@code Proto.Battle.RpcPowerReq}
   */
  public static final class RpcPowerReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Battle.RpcPowerReq)
      RpcPowerReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RpcPowerReq.newBuilder() to construct.
    private RpcPowerReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RpcPowerReq() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RpcPowerReq();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RpcPowerReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.BattleUnitDto.Builder subBuilder = null;
              if (unit_ != null) {
                subBuilder = unit_.toBuilder();
              }
              unit_ = input.readMessage(com.dxx.game.dto.CommonProto.BattleUnitDto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(unit_);
                unit_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.BattleProto.RpcPowerReq.class, com.dxx.game.dto.BattleProto.RpcPowerReq.Builder.class);
    }

    public static final int UNIT_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.BattleUnitDto unit_;
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
     * @return Whether the unit field is set.
     */
    @java.lang.Override
    public boolean hasUnit() {
      return unit_ != null;
    }
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
     * @return The unit.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.BattleUnitDto getUnit() {
      return unit_ == null ? com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
    }
    /**
     * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder() {
      return getUnit();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (unit_ != null) {
        output.writeMessage(1, getUnit());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (unit_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getUnit());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.BattleProto.RpcPowerReq)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.BattleProto.RpcPowerReq other = (com.dxx.game.dto.BattleProto.RpcPowerReq) obj;

      if (hasUnit() != other.hasUnit()) return false;
      if (hasUnit()) {
        if (!getUnit()
            .equals(other.getUnit())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUnit()) {
        hash = (37 * hash) + UNIT_FIELD_NUMBER;
        hash = (53 * hash) + getUnit().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.BattleProto.RpcPowerReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Battle.RpcPowerReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Battle.RpcPowerReq)
        com.dxx.game.dto.BattleProto.RpcPowerReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.BattleProto.RpcPowerReq.class, com.dxx.game.dto.BattleProto.RpcPowerReq.Builder.class);
      }

      // Construct using com.dxx.game.dto.BattleProto.RpcPowerReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (unitBuilder_ == null) {
          unit_ = null;
        } else {
          unit_ = null;
          unitBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerReq_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RpcPowerReq getDefaultInstanceForType() {
        return com.dxx.game.dto.BattleProto.RpcPowerReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RpcPowerReq build() {
        com.dxx.game.dto.BattleProto.RpcPowerReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RpcPowerReq buildPartial() {
        com.dxx.game.dto.BattleProto.RpcPowerReq result = new com.dxx.game.dto.BattleProto.RpcPowerReq(this);
        if (unitBuilder_ == null) {
          result.unit_ = unit_;
        } else {
          result.unit_ = unitBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.BattleProto.RpcPowerReq) {
          return mergeFrom((com.dxx.game.dto.BattleProto.RpcPowerReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.BattleProto.RpcPowerReq other) {
        if (other == com.dxx.game.dto.BattleProto.RpcPowerReq.getDefaultInstance()) return this;
        if (other.hasUnit()) {
          mergeUnit(other.getUnit());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.BattleProto.RpcPowerReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.BattleProto.RpcPowerReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.BattleUnitDto unit_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder> unitBuilder_;
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       * @return Whether the unit field is set.
       */
      public boolean hasUnit() {
        return unitBuilder_ != null || unit_ != null;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       * @return The unit.
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDto getUnit() {
        if (unitBuilder_ == null) {
          return unit_ == null ? com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
        } else {
          return unitBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      public Builder setUnit(com.dxx.game.dto.CommonProto.BattleUnitDto value) {
        if (unitBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          unit_ = value;
          onChanged();
        } else {
          unitBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      public Builder setUnit(
          com.dxx.game.dto.CommonProto.BattleUnitDto.Builder builderForValue) {
        if (unitBuilder_ == null) {
          unit_ = builderForValue.build();
          onChanged();
        } else {
          unitBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      public Builder mergeUnit(com.dxx.game.dto.CommonProto.BattleUnitDto value) {
        if (unitBuilder_ == null) {
          if (unit_ != null) {
            unit_ =
              com.dxx.game.dto.CommonProto.BattleUnitDto.newBuilder(unit_).mergeFrom(value).buildPartial();
          } else {
            unit_ = value;
          }
          onChanged();
        } else {
          unitBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      public Builder clearUnit() {
        if (unitBuilder_ == null) {
          unit_ = null;
          onChanged();
        } else {
          unit_ = null;
          unitBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDto.Builder getUnitBuilder() {
        
        onChanged();
        return getUnitFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder getUnitOrBuilder() {
        if (unitBuilder_ != null) {
          return unitBuilder_.getMessageOrBuilder();
        } else {
          return unit_ == null ?
              com.dxx.game.dto.CommonProto.BattleUnitDto.getDefaultInstance() : unit_;
        }
      }
      /**
       * <code>.Proto.Common.BattleUnitDto unit = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder> 
          getUnitFieldBuilder() {
        if (unitBuilder_ == null) {
          unitBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.BattleUnitDto, com.dxx.game.dto.CommonProto.BattleUnitDto.Builder, com.dxx.game.dto.CommonProto.BattleUnitDtoOrBuilder>(
                  getUnit(),
                  getParentForChildren(),
                  isClean());
          unit_ = null;
        }
        return unitBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Battle.RpcPowerReq)
    }

    // @@protoc_insertion_point(class_scope:Proto.Battle.RpcPowerReq)
    private static final com.dxx.game.dto.BattleProto.RpcPowerReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.BattleProto.RpcPowerReq();
    }

    public static com.dxx.game.dto.BattleProto.RpcPowerReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RpcPowerReq>
        PARSER = new com.google.protobuf.AbstractParser<RpcPowerReq>() {
      @java.lang.Override
      public RpcPowerReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RpcPowerReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RpcPowerReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RpcPowerReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.BattleProto.RpcPowerReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RpcPowerRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Battle.RpcPowerResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>int64 result = 2;</code>
     * @return The result.
     */
    long getResult();
  }
  /**
   * Protobuf type {@code Proto.Battle.RpcPowerResp}
   */
  public static final class RpcPowerResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Battle.RpcPowerResp)
      RpcPowerRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RpcPowerResp.newBuilder() to construct.
    private RpcPowerResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RpcPowerResp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RpcPowerResp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RpcPowerResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 16: {

              result_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.BattleProto.RpcPowerResp.class, com.dxx.game.dto.BattleProto.RpcPowerResp.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int RESULT_FIELD_NUMBER = 2;
    private long result_;
    /**
     * <code>int64 result = 2;</code>
     * @return The result.
     */
    @java.lang.Override
    public long getResult() {
      return result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (result_ != 0L) {
        output.writeInt64(2, result_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (result_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, result_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.BattleProto.RpcPowerResp)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.BattleProto.RpcPowerResp other = (com.dxx.game.dto.BattleProto.RpcPowerResp) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getResult()
          != other.getResult()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getResult());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.BattleProto.RpcPowerResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.BattleProto.RpcPowerResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Battle.RpcPowerResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Battle.RpcPowerResp)
        com.dxx.game.dto.BattleProto.RpcPowerRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.BattleProto.RpcPowerResp.class, com.dxx.game.dto.BattleProto.RpcPowerResp.Builder.class);
      }

      // Construct using com.dxx.game.dto.BattleProto.RpcPowerResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        result_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_RpcPowerResp_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RpcPowerResp getDefaultInstanceForType() {
        return com.dxx.game.dto.BattleProto.RpcPowerResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RpcPowerResp build() {
        com.dxx.game.dto.BattleProto.RpcPowerResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.BattleProto.RpcPowerResp buildPartial() {
        com.dxx.game.dto.BattleProto.RpcPowerResp result = new com.dxx.game.dto.BattleProto.RpcPowerResp(this);
        result.code_ = code_;
        result.result_ = result_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.BattleProto.RpcPowerResp) {
          return mergeFrom((com.dxx.game.dto.BattleProto.RpcPowerResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.BattleProto.RpcPowerResp other) {
        if (other == com.dxx.game.dto.BattleProto.RpcPowerResp.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getResult() != 0L) {
          setResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.BattleProto.RpcPowerResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.BattleProto.RpcPowerResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private long result_ ;
      /**
       * <code>int64 result = 2;</code>
       * @return The result.
       */
      @java.lang.Override
      public long getResult() {
        return result_;
      }
      /**
       * <code>int64 result = 2;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(long value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 result = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        
        result_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Battle.RpcPowerResp)
    }

    // @@protoc_insertion_point(class_scope:Proto.Battle.RpcPowerResp)
    private static final com.dxx.game.dto.BattleProto.RpcPowerResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.BattleProto.RpcPowerResp();
    }

    public static com.dxx.game.dto.BattleProto.RpcPowerResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RpcPowerResp>
        PARSER = new com.google.protobuf.AbstractParser<RpcPowerResp>() {
      @java.lang.Override
      public RpcPowerResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RpcPowerResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RpcPowerResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RpcPowerResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.BattleProto.RpcPowerResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_RChapterCombatReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Battle_RChapterCombatReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_RChapterCombatResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Battle_RChapterCombatResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_RGuildBossCombatReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Battle_RGuildBossCombatReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_RGuildBossCombatResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Battle_RGuildBossCombatResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_RpcPowerReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Battle_RpcPowerReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_RpcPowerResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Battle_RpcPowerResp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014battle.proto\022\014Proto.Battle\032\014common.pro" +
      "to\"\256\001\n\021RChapterCombatReq\022\021\n\tchapterId\030\001 " +
      "\001(\005\022\021\n\twaveIndex\030\002 \001(\005\022)\n\004unit\030\003 \001(\0132\033.P" +
      "roto.Common.BattleUnitDto\022\016\n\006skills\030\004 \003(" +
      "\005\022\014\n\004seed\030\005 \001(\005\022*\n\005units\030\006 \003(\0132\033.Proto.C" +
      "ommon.CombatUnitDto\"\325\001\n\022RChapterCombatRe" +
      "sp\022\014\n\004code\030\001 \001(\005\022\021\n\tchapterId\030\002 \001(\005\022\021\n\tw" +
      "aveIndex\030\003 \001(\005\022\016\n\006result\030\004 \001(\005\022\014\n\004seed\030\006" +
      " \001(\005\022/\n\nstartUnits\030\007 \003(\0132\033.Proto.Common." +
      "CombatUnitDto\022-\n\010endUnits\030\010 \003(\0132\033.Proto." +
      "Common.CombatUnitDto\022\r\n\005power\030\t \001(\003\"^\n\023R" +
      "GuildBossCombatReq\022\016\n\006bossId\030\001 \001(\005\022)\n\004un" +
      "it\030\002 \001(\0132\033.Proto.Common.BattleUnitDto\022\014\n" +
      "\004seed\030\003 \001(\005\"\242\001\n\024RGuildBossCombatResp\022\014\n\004" +
      "code\030\001 \001(\005\022\014\n\004seed\030\002 \001(\005\022/\n\nstartUnits\030\003" +
      " \003(\0132\033.Proto.Common.CombatUnitDto\022-\n\010end" +
      "Units\030\004 \003(\0132\033.Proto.Common.CombatUnitDto" +
      "\022\016\n\006damage\030\005 \001(\004\"8\n\013RpcPowerReq\022)\n\004unit\030" +
      "\001 \001(\0132\033.Proto.Common.BattleUnitDto\",\n\014Rp" +
      "cPowerResp\022\014\n\004code\030\001 \001(\005\022\016\n\006result\030\002 \001(\003" +
      "2\210\002\n\rBattleService\022S\n\014handleCombat\022\037.Pro" +
      "to.Battle.RChapterCombatReq\032 .Proto.Batt" +
      "le.RChapterCombatResp\"\000\022F\n\013handlePower\022\031" +
      ".Proto.Battle.RpcPowerReq\032\032.Proto.Battle" +
      ".RpcPowerResp\"\000\022Z\n\017handleGuildBoss\022!.Pro" +
      "to.Battle.RGuildBossCombatReq\032\".Proto.Ba" +
      "ttle.RGuildBossCombatResp\"\000B\037\n\020com.dxx.g" +
      "ame.dtoB\013BattleProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Battle_RChapterCombatReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Battle_RChapterCombatReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Battle_RChapterCombatReq_descriptor,
        new java.lang.String[] { "ChapterId", "WaveIndex", "Unit", "Skills", "Seed", "Units", });
    internal_static_Proto_Battle_RChapterCombatResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Battle_RChapterCombatResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Battle_RChapterCombatResp_descriptor,
        new java.lang.String[] { "Code", "ChapterId", "WaveIndex", "Result", "Seed", "StartUnits", "EndUnits", "Power", });
    internal_static_Proto_Battle_RGuildBossCombatReq_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Battle_RGuildBossCombatReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Battle_RGuildBossCombatReq_descriptor,
        new java.lang.String[] { "BossId", "Unit", "Seed", });
    internal_static_Proto_Battle_RGuildBossCombatResp_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Battle_RGuildBossCombatResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Battle_RGuildBossCombatResp_descriptor,
        new java.lang.String[] { "Code", "Seed", "StartUnits", "EndUnits", "Damage", });
    internal_static_Proto_Battle_RpcPowerReq_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Battle_RpcPowerReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Battle_RpcPowerReq_descriptor,
        new java.lang.String[] { "Unit", });
    internal_static_Proto_Battle_RpcPowerResp_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Battle_RpcPowerResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Battle_RpcPowerResp_descriptor,
        new java.lang.String[] { "Code", "Result", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

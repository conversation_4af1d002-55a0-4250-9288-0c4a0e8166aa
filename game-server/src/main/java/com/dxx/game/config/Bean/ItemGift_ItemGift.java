
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ItemGift_ItemGift {
    public ItemGift_ItemGift(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        Type = _buf.get("Type").getAsInt();
        Rewards = _buf.get("Rewards").getAsString();
        seconds = _buf.get("seconds").getAsInt();
    }

    public static ItemGift_ItemGift deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ItemGift_ItemGift(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 类型<br/>1.开出固定道具<br/>2.挂机金币<br/>3.挂机粉尘<br/>4.随机宝箱
     */
    public final int Type;
    /**
     * 奖励<br/>1: 道具类型,道具ID，数量|...<br/>2:<br/>3:<br/>4:道具类型, 道具ID，数量，权重|...
     */
    public final String Rewards;
    /**
     * 挂机类道具时间(秒)
     */
    public final int seconds;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "Type:" + Type + ","
        + "Rewards:" + Rewards + ","
        + "seconds:" + seconds + ","
        + "}";
    }
}


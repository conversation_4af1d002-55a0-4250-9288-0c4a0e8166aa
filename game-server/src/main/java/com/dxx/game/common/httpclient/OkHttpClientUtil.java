package com.dxx.game.common.httpclient;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.server.context.ResponseContext;
import lombok.NonNull;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;
import java.util.Objects;

public class OkHttpClientUtil {
    private static final Logger log = LoggerFactory.getLogger(OkHttpClientUtil.class);

    private static OkHttpClient okHttpClient;

    public static void setOkHttpClient(OkHttpClient client) {
        okHttpClient = client;
    }

    /**
     * GET Method begin---------------------------------
     */

    public static <T> T get(@NonNull String url, Class<T> clasz) {
        return get(url, null, null, clasz);
    }

    public static void get(@NonNull String url, Callback callback) {
        get(url, null, null, callback);
    }

    public static <T> T get(@NonNull String url, Map<String, String> queryParameter, Class<T> clasz) {
        return get(url, null, queryParameter, clasz);
    }

    public static void get(@NonNull String url, Map<String, String> queryParameter, Callback callback) {
        get(url, null, queryParameter, callback);
    }

    public static <T> T get(@NonNull String url, Map<String, String> headerParameter, Map<String, String> queryParameter, Class<T> clasz) {
        Request request = processGetParameter(url, headerParameter, queryParameter);

        try (Response resp = okHttpClient.newCall(request).execute()) {
            return processResponse(resp, clasz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void get(@NonNull String url, Map<String, String> headerParameter, Map<String, String> queryParameter, Callback callback) {
        Request request = processGetParameter(url, headerParameter, queryParameter);
        okHttpClient.newCall(request).enqueue(callback);
    }

    private static Request processGetParameter(String url, Map<String, String> headerParameter, Map<String, String> queryParameter) {
        Request.Builder builder = new Request.Builder();
        if (!isEmptyMap(headerParameter)) {
            builder.headers(Headers.of(headerParameter));
        }
        if (isEmptyMap(queryParameter)) {
            builder.url(url);
        } else {
            boolean hasQuery = false;
            try {
                String query = new URL(url).getQuery();
                if (!StringUtils.isEmpty(query)) {
                    hasQuery = true;
                }
            } catch (MalformedURLException e) {
                throw new RuntimeException("url is illegal");
            }
            StringBuilder sb = new StringBuilder(url);
            if (!hasQuery) {
                sb.append("?1=1");
            }
            queryParameter.forEach((k, v) -> {
                sb.append("&").append(k).append("=").append(v);
            });
            builder.url(sb.toString());
        }
        return builder.build();
    }

    /**
     * POST Method With JSON begin---------------------------------
     */

    public static <T> T postJson(@NonNull String url, Class<T> clasz) {
        return postJson(url, null, null, clasz);
    }

    public static void postJson(@NonNull String url, Callback callback) {
        postJson(url, null, null, callback);
    }

    public static <T> T postJson(@NonNull String url, Map<String, String> headerParameter, Class<T> clasz) {
        return postJson(url, headerParameter, null, clasz);
    }

    public static void postJson(@NonNull String url, Object bodyObj, Callback callback) {
        postJson(url, null, bodyObj, callback);
    }

    public static <T> T postJsonWithOutHeader(@NonNull String url, Object bodyObj, Class<T> clasz) {
        return postJson(url, null, bodyObj, clasz);
    }

    public static void postJson(@NonNull String url, Map<String, String> headerParameter, Callback callback) {
        postJson(url, headerParameter, null, callback);
    }

    public static String postJson(String url, Map<String, String> headerParameter, Object bodyObj) {
        return postJson(url, headerParameter, bodyObj, String.class);
    }

    public static <T> T postJson(@NonNull String url, Map<String, String> headerParameter, Object bodyObj, Class<T> clasz) {
        Request request = processPostJsonParameter(url, headerParameter, bodyObj);
        try (Response resp = okHttpClient.newCall(request).execute();) {
            return processResponse(resp, clasz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public static void postJson(@NonNull String url, Map<String, String> headerParameter, Object bodyObj, Callback callback) {
        Request request = processPostJsonParameter(url, headerParameter, bodyObj);
        okHttpClient.newCall(request).enqueue(callback);
    }

    public static <T> T putJson(String url, Map<String, String> headerParameter, Object bodyObj, Class<T> clasz) {
        Request request = processPutJsonParameter(url, headerParameter, bodyObj);
        try (Response resp = okHttpClient.newCall(request).execute();) {
            return processResponse(resp, clasz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void putJson(String url, Map<String, String> headerParameter, Object bodyObj, Callback callback) {
        Request request = processPutJsonParameter(url, headerParameter, bodyObj);
        okHttpClient.newCall(request).enqueue(callback);
    }

    private static Request processPostJsonParameter(String url, Map<String, String> headerParameter, Object bodyObj) {
        Request.Builder builder = new Request.Builder().url(url);
        if(!Objects.isNull(bodyObj)) {
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONObject.toJSONString(bodyObj));
            builder.post(body);
        } else {
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), "{}");
            builder.post(body);
        }
        if (!isEmptyMap(headerParameter)) {
            builder.headers(Headers.of(headerParameter));
        }
        return builder.build();
    }

    private static Request processPutJsonParameter(String url, Map<String, String> headerParameter, Object bodyObj) {
        Request.Builder builder = new Request.Builder().url(url);
        if(!Objects.isNull(bodyObj)) {
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONObject.toJSONString(bodyObj));
            builder.put(body);
        } else {
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), "{}");
            builder.put(body);
        }
        if (!isEmptyMap(headerParameter)) {
            builder.headers(Headers.of(headerParameter));
        }
        return builder.build();
    }

    /**
     * POST Method With Form begin---------------------------------
     */

    public static <T> T postForm(@NonNull String url, Class<T> clasz) {
        return postForm(url, null, null, clasz);
    }

    public static void postForm(@NonNull String url, Callback callback) {
        postForm(url, null, null, callback);
    }

//    public static <T> T postForm(@NonNull String url, Map<String, String> headerParameter, Class<T> clasz) {
//        return postForm(url, headerParameter, null, clasz);
//    }

    public static void postForm(@NonNull String url, Map<String, String> headerParameter, Callback callback) {
        postForm(url, headerParameter, null, callback);
    }

    public static <T> T postForm(@NonNull String url, Map<String, String> parameters, Class<T> clasz) {
        return postForm(url, null, parameters, clasz);
    }

    public static <T> T postForm(@NonNull String url, Map<String, String> headerParameter, Map<String, String> parameters, Class<T> clasz) {
        Request request = processPostFormParameter(url, headerParameter, parameters);
        try (Response resp = okHttpClient.newCall(request).execute();) {
            return processResponse(resp, clasz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public static void postForm(@NonNull String url, Map<String, String> headerParameter, Map<String, String> parameters, Callback callback) {
        Request request = processPostFormParameter(url, headerParameter, parameters);
        okHttpClient.newCall(request).enqueue(callback);
    }

    private static Request processPostFormParameter(String url, Map<String, String> headerParameter, Map<String, String> parameters) {
        Request.Builder builder = new Request.Builder().url(url);
        if(!Objects.isNull(parameters)) {
            FormBody.Builder formBuilder = new FormBody.Builder();
            parameters.forEach((k, v) -> {
                formBuilder.add(k, v);
            });
            builder.post(formBuilder.build());
        }
        if (!isEmptyMap(headerParameter)) {
            builder.headers(Headers.of(headerParameter));
        }
        return builder.build();
    }

    @SuppressWarnings("unchecked")
    private static <T> T processResponse(Response resp, Class<T> clasz) throws IOException {
        if (resp.isSuccessful()) {
            if (Objects.equals(Void.class, clasz)) {
                return null;
            }
            String respStr = resp.body().string();
            if(Objects.equals(String.class, clasz)) {
                return (T)respStr;
            }
            return JSONObject.parseObject(respStr, clasz);
        } else {
            int code = resp.code();
            assert resp.body() != null;
            String respBody = resp.body().string();
            ResponseContext.setOkHttpStatusCode(code);
            ResponseContext.setOkHttpBody(respBody);
            log.error("processResponse failed. httpCode:{}, httpBody:{}", code, respBody);
        }
        return null;
    }

    private static boolean isEmptyMap(Map<? extends Object, ? extends Object> map) {
        return Objects.isNull(map) || map.isEmpty();
    }

    public static InputStream getInputStream(String url) throws IOException {
        Request request = new Request.Builder().url(url).build();

        Response response = okHttpClient.newCall(request).execute();
        InputStream inputStream = response.body().byteStream();

        return inputStream;
    }

    /**
     * 下载文件
     * @param url
     * @param path
     */
    public static File downLoadFile(String url, String path) throws IOException {
        Request request = new Request.Builder().url(url).build();

        Response response = okHttpClient.newCall(request).execute();

        String dispositionHeader = response.header("Content-Disposition");
        if (dispositionHeader == null) {
            return null;
        }

        String fileName = getHeaderFileName(response);
        path = path + "/" + fileName;

        File file = new File(path);
        InputStream inputStream = response.body().byteStream();
        FileOutputStream fileOutputStream = new FileOutputStream(file);
        int len;
        byte[] bytes = new byte[4096];
        while ((len = inputStream.read(bytes)) != -1) {
            fileOutputStream.write(bytes, 0, len);
        }
        fileOutputStream.flush();
        inputStream.close();
        fileOutputStream.close();

        log.info("down load file success {}", path);
        return file;
    }

    private static String getHeaderFileName(Response response) {
        String dispositionHeader = response.header("Content-Disposition");
        dispositionHeader.replace("attachment;filename=", "");
        dispositionHeader.replace("filename*=utf-8", "");
        String[] strings = dispositionHeader.split("; ");
        if (strings.length > 1) {
            dispositionHeader = strings[1].replace("filename=", "");
            dispositionHeader = dispositionHeader.replace("\"", "");
            return dispositionHeader;
        }
        return "";
    }

    public static <T> T uploadImageToWeixin(String url, File file, Class<T> clasz) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("media", file.getName(), RequestBody.create(MediaType.parse("image/jpeg"), file))
                .build();

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response resp = okHttpClient.newCall(request).execute();) {
            return processResponse(resp, clasz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean downLoadImage(String url, File outputFile) {
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            InputStream inputStream = response.body().byteStream();
            OutputStream outputStream = new FileOutputStream(outputFile);
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
            inputStream.close();
            outputStream.close();
            return true;
        } catch (Exception e) {
            log.error("downLoadImageError, e:", e);
            return false;
        }
    }

}




















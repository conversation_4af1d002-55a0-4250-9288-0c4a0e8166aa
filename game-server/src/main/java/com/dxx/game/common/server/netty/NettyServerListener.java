package com.dxx.game.common.server.netty;

import com.dxx.game.common.utils.ThreadPoolUtils;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollChannelOption;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.epoll.EpollServerSocketChannel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.stereotype.Component;

import com.dxx.game.common.server.config.NettyServerConfig;
import com.dxx.game.common.server.handler.HttpRequestIoHandler;

import java.util.List;

import jakarta.annotation.Resource;

/**
 * Netty服务器监听器
 */
@Component
public class NettyServerListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(NettyServerListener.class);

    private ServerBootstrap serverBootstrap = new ServerBootstrap();
    private EventLoopGroup boss = new NioEventLoopGroup();
    private EventLoopGroup work = new NioEventLoopGroup();
    private static final DefaultEventExecutorGroup executor = new DefaultEventExecutorGroup((Runtime.getRuntime().availableProcessors() * 8), new DefaultThreadFactory("eventExecutorGroup"));

    @Resource
    private NettyServerConfig nettyConfig;

    public void close() {

        //优雅退出
        boss.shutdownGracefully();
        LOGGER.info("boss shutdownGracefully success....");

        work.shutdownGracefully();
        LOGGER.info("work shutdownGracefully success....");

        LOGGER.info("Server shutdown succeeded....");
    }

    /**
     * 开启及服务线程
     */
    public void start(ApplicationArguments args) {

        int port = nettyConfig.getPort();

        List<String> appPort = args.getOptionValues("app.port");
        if (appPort != null && appPort.size() > 0) {
            port = Integer.parseInt(appPort.get(0));
        }

        // linux下使用epoll 端口复用
        if (Epoll.isAvailable()) {
            boss = new EpollEventLoopGroup();
            work = new EpollEventLoopGroup();
        }

        serverBootstrap.group(boss, work)
                .channel(Epoll.isAvailable() ? EpollServerSocketChannel.class : NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 2048)
                .option(Epoll.isAvailable() ? EpollChannelOption.SO_REUSEADDR : ChannelOption.SO_REUSEADDR, true)
                .childOption(ChannelOption.TCP_NODELAY,true);
        if (Epoll.isAvailable()) {
            serverBootstrap.option(EpollChannelOption.SO_REUSEPORT, true);
        }

        if (LOGGER.isDebugEnabled()) {
            serverBootstrap.handler(new LoggingHandler(LogLevel.INFO));
        }
        try {
            //设置事件处理
            serverBootstrap.childHandler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel ch) {
                    ChannelPipeline pipeline = ch.pipeline();
                    pipeline.addLast(new HttpServerCodec());
                    pipeline.addLast(new HttpObjectAggregator(16*1024*1024));
                    pipeline.addLast(executor, "handler", new HttpRequestIoHandler());
//                    pipeline.addLast("handler", new HttpRequestIoHandler());
                }
            });
            LOGGER.info("netty httpserver start success, port : [{}]", port);
            ChannelFuture f = serverBootstrap.bind(port).sync();
            f.channel().closeFuture().sync();
        } catch (InterruptedException e) {
            LOGGER.info("[出现异常] 释放资源");
            boss.shutdownGracefully();
            work.shutdownGracefully();
        }
    }
}

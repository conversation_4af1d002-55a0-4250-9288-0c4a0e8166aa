package com.dxx.game.common.utils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * BigIntgerUtil
 * <AUTHOR>
 *
 */
public class MaskUtil {
	
	private static final BigInteger defaultValue = BigInteger.valueOf(1L);
	
	private static final long defaultLongValue = 1L;

	/**
	 * 标记位是否为1
	 * @param mask
	 * @param position
	 * @return
	 */
	public static boolean isTrue(BigInteger mask, int position) {
		if (mask.equals(BigInteger.valueOf(0L))) {
			return false;
		}
		
		BigInteger shiftLeftMask = defaultValue.shiftLeft(position);
		return mask.and(shiftLeftMask).equals(shiftLeftMask);
	}
	
	/**
	 * 标记位是否为1
	 * @param mask
	 * @param position
	 * @return
	 */
	public static boolean isTrue(long mask, int position) {
		if (mask == 0L) {
			return false;
		}
		
		long leftMask = defaultLongValue << position;
		return (mask & leftMask) == leftMask;
	}
	
	/**
	 * 标记位设置成1
	 * @param mask
	 * @param position
	 * @return
	 */
	public static BigInteger setMask(BigInteger mask, int position) {
		return mask.or(defaultValue.shiftLeft(position));
	}
	
	/**
	 * 标记位设置成1
	 * @param mask
	 * @param position
	 * @return
	 */
	public static long setMask(long mask, int position) {
		return mask | (defaultLongValue << position);
	}

	/**
	 * 设置位置的值
	 * @param mask
	 * @param position
	 * @return
	 */
	public static long resetMask(long mask, int position) {
		return mask ^ (defaultLongValue << position);
	}

	public static String resetMask(String mask, int position) {
		BigInteger bigInteger = new BigInteger(mask);
		return resetMask(bigInteger, position).toString();
	}

	public static BigInteger resetMask(BigInteger mask, int position) {
		return mask.xor(defaultValue.shiftLeft(position));
	}

	/**
	 * 返回标记位为1的位置列表
	 * @param mask
	 * @param positionList
	 * @return
	 */
	public static List<Integer> getTrueList(BigInteger mask, List<Integer> positionList) {
		List<Integer> result = new ArrayList<Integer>(positionList.size());
		for (int i = 0, length = positionList.size(); i < length; i ++) {
			int position = positionList.get(i);
			if (isTrue(mask, position)) {
				result.add(position);
			}
		}
		return result;
	}
	
	/**
	 * 返回标记位为1的位置列表
	 * @param mask
	 * @param positionList
	 * @return
	 */
	public static List<Integer> getTrueList(BigInteger mask, Set<Integer> positionList) {
		List<Integer> result = new ArrayList<Integer>(positionList.size());
		for (Integer position : positionList) {
			if (isTrue(mask, position)) {
				result.add(position);
			}
		}
		return result;
	}
	
	
	/**
	 * 返回标记位为1的位置列表
	 * @param mask
	 * @param positionList
	 * @return
	 */
	public static List<Integer> getTrueList(long mask, List<Integer> positionList) {
		List<Integer> result = new ArrayList<Integer>(positionList.size());
		for (int i = 0, length = positionList.size(); i < length; i ++) {
			int position = positionList.get(i);
			if (isTrue(mask, position)) {
				result.add(position);
			}
		}
		return result;
	}
	
	/**
	 * 返回标记位为1的位置列表
	 * @param mask
	 * @param positionList
	 * @return
	 */
	public static List<Integer> getTrueList(long mask, Set<Integer> positionList) {
		List<Integer> result = new ArrayList<Integer>(positionList.size());
		for (Integer position : positionList) {
			if (isTrue(mask, position)) {
				result.add(position);
			}
		}
		return result;
	}

	public static void main(String[] args) {
		BigInteger mask = new BigInteger("22");
		BigInteger test = setMask(mask, 63);
		System.out.println(test);
	}
}









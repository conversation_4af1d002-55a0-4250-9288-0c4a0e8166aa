
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Guild_guildDonation {
    public Guild_guildDonation(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Count = _buf.get("Count").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("GuildLevel").getAsJsonArray(); GuildLevel = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  GuildLevel.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("ChapterId").getAsJsonArray(); ChapterId = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  ChapterId.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
    }

    public static Guild_guildDonation deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Guild_guildDonation(_buf);
    }

    /**
     * 道具ID
     */
    public final int ID;
    /**
     * 道具数量
     */
    public final int Count;
    /**
     * 需要的公会等级
     */
    public final java.util.List<Integer> GuildLevel;
    /**
     * 解锁章节<br/>最小章节｜最大章节
     */
    public final java.util.List<Integer> ChapterId;
    /**
     * 捐献后获得的奖励<br/>空代表没有
     */
    public final java.util.List<String> Reward;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Count:" + Count + ","
        + "GuildLevel:" + GuildLevel + ","
        + "ChapterId:" + ChapterId + ","
        + "Reward:" + Reward + ","
        + "}";
    }
}


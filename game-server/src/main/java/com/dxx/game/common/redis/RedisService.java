package com.dxx.game.common.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class RedisService {
    @Autowired
    public RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ValueOperations<String, Object> valueOperations;

    /**
     * 默认过期时长，单位：秒
     */
    public static final long DEFAULT_EXPIRE = 60 * 60 * 24;

    /**
     * 不设置过期时长
     */
    public static final long NOT_EXPIRE = -1;

    private final static DefaultRedisScript<String> QUERY_IP_LUA_SCRIPT = new DefaultRedisScript<>(
            "return redis.call('ZRANGEBYSCORE', KEYS[1],  ARGV[1], '+inf', 'LIMIT', 0, 1)"
            , String.class
    );


    public boolean existsKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 重名名key，如果newKey已经存在，则newKey的原值被覆盖
     *
     * @param oldKey
     * @param newKey
     */
    public void renameKey(String oldKey, String newKey) {
        redisTemplate.rename(oldKey, newKey);
    }

    /**
     * newKey不存在时才重命名
     *
     * @param oldKey
     * @param newKey
     * @return 修改成功返回true
     */
    public boolean renameKeyNotExist(String oldKey, String newKey) {
        return redisTemplate.renameIfAbsent(oldKey, newKey);
    }

    /**
     * 删除key
     *
     * @param key
     */
    public boolean deleteKey(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除多个key
     *
     * @param keys
     */
    public void deleteKey(String... keys) {
        Set<String> kSet = Stream.of(keys).map(k -> k).collect(Collectors.toSet());
        redisTemplate.delete(kSet);
    }

    /**
     * 删除Key的集合
     *
     * @param keys
     */
    public void deleteKey(Collection<String> keys) {
        Set<String> kSet = keys.stream().map(k -> k).collect(Collectors.toSet());
        redisTemplate.delete(kSet);
    }

    /**
     * 设置key的生命周期
     *
     * @param key
     * @param time
     * @param timeUnit
     */
    public void expireKey(String key, long time, TimeUnit timeUnit) {
        redisTemplate.expire(key, time, timeUnit);
    }

    /**
     * 指定key在指定的日期过期
     *
     * @param key
     * @param date
     */
    public void expireKeyAt(String key, Date date) {
        redisTemplate.expireAt(key, date);
    }

    /**
     * 查询key的生命周期
     *
     * @param key
     * @param timeUnit
     * @return
     */
    public long getKeyExpire(String key, TimeUnit timeUnit) {
        return redisTemplate.getExpire(key, timeUnit);
    }

    /**
     * 将key设置为永久有效
     *
     * @param key
     */
    public void persistKey(String key) {
        redisTemplate.persist(key);
    }


    /**
     * 对指定key赋初值
     */
    public Boolean setIfAbsent(String key, Long value) {
        return redisTemplate.opsForValue().setIfAbsent(key, ""+value);
    }

    /**
     * 将key对应的值原子性加一
     * @param key
     * @return
     */
    public long incrBy(String key) {
        return redisTemplate.opsForValue().increment(key, 1);
    }

    public long incrBy(String key, long value) {
        return redisTemplate.opsForValue().increment(key, value);
    }

    /**
     * 为哈希表中的字段值加上指定增量值
     * @param key
     * @param hKey
     * @return
     */
    public long hIncrBy(String key, String hKey) {
        return redisTemplate.opsForHash().increment(key, hKey, 1);
    }

    public long hIncrBy(String key, String hKey, int delta) {
        return redisTemplate.opsForHash().increment(key, hKey, delta);
    }

    /**
     * 哈希表存储数据
     * @param key
     * @param hKey
     * @param value
     */
    public void hSet(String key, String hKey, String value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    public void hDel(String key, String... hKey) {
        redisTemplate.opsForHash().delete(key, hKey);
    }

    public void hSetAll(String key, Map<String, String> values) {
        redisTemplate.opsForHash().putAll(key, values);
    }

    /**
     * 哈希表获取数据
     * @param key
     * @param hKey
     * @return
     */
    public String hGet(String key, String hKey) {
        return (String) redisTemplate.opsForHash().get(key, hKey);
    }

    /**
     * 获取哈希表所有数据
     * @param key
     * @return
     */
    public Map<Object, Object> hGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    public List<Object> hmGetAll(String key, List<Object> keys) {
        return redisTemplate.opsForHash().multiGet(key, keys);
    }

    public long hLen(String key) {
        return redisTemplate.opsForHash().size(key);
    }

    public boolean hExists(String key, String hKey) {
        return redisTemplate.opsForHash().hasKey(key, hKey);
    }

    /**
     * 设置指定 key 的值
     * @param key
     * @param value
     */
    public void set(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, int value) {
        redisTemplate.opsForValue().set(key, String.valueOf(value));
    }

    public void set(String key, long value) {
        redisTemplate.opsForValue().set(key, String.valueOf(value));
    }

    public void set(String key, String value, long seconds) {
        redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
    }
    /**
     * 获取指定 key 的值
     * @param key
     * @return
     */
    public String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public List<String> getAll(List<String> keys) {
        return redisTemplate.opsForValue().multiGet(keys);
    }

    public void setObject(String key, Object value) {
        valueOperations.set(key, value);
    }

    public void setObject(String key, Object value, int expire) {
        valueOperations.set(key, value);
        this.expireKey(key, expire, TimeUnit.SECONDS);
    }

    public Object getObject(String key) {
        return valueOperations.get(key);
    }

    public int getIntValue(String key) {
        String value = this.get(key);
        if (value == null) {
            return 0;
        }
        return Integer.parseInt(value);
    }

    public long getLongValue(String key) {
        String value = this.get(key);
        if (value == null) {
            return 0;
        }
        return Long.parseLong(value);
    }

    public void deleteKeysByPrefix(final String prefix) {
        // 使用ScanOptions构建匹配指定前缀的模式
        ScanOptions scanOptions = ScanOptions.scanOptions().match(prefix + "*").count(100).build();

        // 使用RedisCallback执行SCAN和DEL命令
        redisTemplate.execute(new RedisCallback<Object>() {
            public Object doInRedis(RedisConnection connection) {
                Cursor<byte[]> cursor = connection.scan(scanOptions);

                // 存储要删除的key列表
                List<byte[]> keysToDelete = new ArrayList<>();

                while (cursor.hasNext()) {
                    byte[] key = cursor.next();
                    keysToDelete.add(key);
                }

                // 执行批量删除操作
                if (!keysToDelete.isEmpty()) {
                    connection.del(keysToDelete.toArray(new byte[keysToDelete.size()][]));
                }

                return null;
            }
        });
    }

    /**
     *  Set sadd
     * @param key
     * @param value
     */
    public void sAdd(String key, String... value) {
        redisTemplate.opsForSet().add(key, value);
    }

    /**
     * Set 移除一条数据
     * @param key
     * @param value
     */
    public void sRemove(String key, Object... value) {
        redisTemplate.opsForSet().remove(key, value);
    }

    /**
     * 返回集合中的所有成员
     * @param key
     * @return
     */
    public Set<String> sMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 集合中是否存在某个值
     * @param key
     * @param value
     * @return
     */
    public boolean sisMember(String key, String value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * srandmember
     * 从集合中随机 count 个值
     * @param key
     * @param count
     * @return
     */
    public List<String> sRandMember(String key, int count) {
        return redisTemplate.opsForSet().randomMembers(key, count);
    }

    /**
     * zAdd
     * @param key
     * @param member
     * @param score
     */
    public void zAdd(String key, String member, double score) {
        redisTemplate.opsForZSet().add(key, member, score);
    }

    /**
     * zBatchAdd
     * @param key
     * @param tuples
     */
    public Long zBatchAdd(String key, Set<ZSetOperations.TypedTuple<String>> tuples) {
        return redisTemplate.opsForZSet().add(key, tuples);
    }

    /**
     * zReverseRange
     * @param key
     * @param start
     * @param end
     * @return
     */
    public Set<String> zReverseRange(String key, int start, int end) {
        return redisTemplate.opsForZSet().reverseRange(key, start, end);
    }

    /**
     * zReverseRangeWithScores
     * @param key
     * @param start
     * @param end
     * @return
     */
    public Set<ZSetOperations.TypedTuple<String>> zReverseRangeWithScores(String key, int start, int end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
    }

    /**
     * zRank
     * @param key
     * @param member
     * @return
     */
    public Long zRank(String key, String member) {
        return redisTemplate.opsForZSet().reverseRank(key, member);
    }

    /**
     * zCard
     * @param key
     * @return
     */
    public Long zCard(String key) {
        return redisTemplate.opsForZSet().zCard(key);
    }

    /**
     * zCount
     * @param key
     * @param min
     * @param max
     * @return
     */
    public Long zCount(String key, double min, double max) {
        return redisTemplate.opsForZSet().count(key, min, max);
    }

    public Set<String> zRange(String key, int rank) {
        return redisTemplate.opsForZSet().range(key, rank, rank);
    }

    public Set<String> zRange(String key, int start, int end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    public Set<ZSetOperations.TypedTuple<String>> zReverseRangeWithScores(String key, int rank) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, rank, rank);
    }

    public long zScore(String key, String member) {
        Double result = redisTemplate.opsForZSet().score(key, member);
        if (result == null) {
            return 0;
        }
        return result.longValue();
    }

    public double zScoreWithDouble(String key, String member) {
        Double result = redisTemplate.opsForZSet().score(key, member);
        if (result == null) {
            return 0;
        }
        return result;
    }

    /**
     * zIncrementScore
     * ZINCRBY
     * 有序集合中对指定成员的分数加上增量 increment
     * @param key
     * @param member
     * @param score
     */
    public void zIncrementScore(String key, String member, double score) {
        redisTemplate.opsForZSet().incrementScore(key, member, score);
    }

    /**
     * 移除一条数据
     * @param key
     * @param member
     */
    public Long zRem(String key, String member) {
        return redisTemplate.opsForZSet().remove(key, member);
    }

    public Long zRemoveRange(String key, int start, int end) {
        return redisTemplate.opsForZSet().removeRange(key, start, start);
    }

    public Set<String> keys(String key) {
        return redisTemplate.keys(key);
    }

    public void delete(Set<String> keys) {
        redisTemplate.delete(keys);
    }

    public String rmqCreatGroup(String key, String group) {
        return redisTemplate.opsForStream().createGroup(key, group);
    }

    public void rmqAdd(String key, Map<String, String> value) {
        redisTemplate.opsForStream().add(key, value);
    }

    public void rmqAck(String key, String group, RecordId messageId) {
        redisTemplate.opsForStream().acknowledge(key, group, messageId);
    }

    public void rmqDelete(MapRecord<String, String, String> record) {
        redisTemplate.opsForStream().delete(record);
    }

    public void rmqDelete(String key, String group, String messageId) {
        redisTemplate.opsForStream().delete(key, group, messageId);
    }

//    public void test() {
//        List<String> keys = new ArrayList<>();
//        keys.add("aa");
//        keys.add("bb");
//        keys.add("dd");
//        RedisCallback callback = new RedisCallback() {
//            @Override
//            public Object doInRedis(RedisConnection redisConnection) throws DataAccessException {
//                for (String key : keys) {
//                    redisConnection.hGetAll(key.getBytes());
//                }
//                return null;
//            }
//        };
//        List<Object> result = redisTemplate.executePipelined(callback);
//        for (Object obj : result) {
//            Map<String, Object> map = (Map<String, Object>) obj;
//            System.out.println(obj);
//        }
//        System.out.println(result);
//
//        List<String> keys2 = new ArrayList<>();
//        keys2.add("aa");
//        keys2.add("bb");
//        keys2.add("dd");
//
//        redisTemplate.executePipelined(new RedisCallback<String>() {
//            @Override
//            public String doInRedis(RedisConnection connection) throws DataAccessException {
//                for (int i=0;i<keys2.size();i++) {
//                    connection.hSet(keys2.get(i).getBytes(),keys2.get(i).getBytes(), keys2.get(i).getBytes());
//                }
//                return null;
//            }
//        });
//    }

//    public List<Object> executePipelined(RedisCallback<String> cl) {
//        return redisTemplate.executePipelined(cl);
//    }

    public List<Object> executePipelined(SessionCallback<String> cl) {
        return redisTemplate.executePipelined(cl);
    }

    /**
     * 查询IP所属国家
     * @param key
     * @param longIp
     * @return
     */
    public String queryIp(String key, String longIp)  {
        return redisTemplate.execute(QUERY_IP_LUA_SCRIPT, Collections.singletonList(key), longIp);
    }


    /**
     * redis发布消息
     * @param channel
     * @param message
     */
    public void publishMessage(String channel, String message) {
        redisTemplate.convertAndSend(channel, message);
    }

    public List<String> listRange(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    public void listLAdd(String key, String data) {
        redisTemplate.opsForList().leftPush(key, data);
    }

    public String listRPop(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    public Long listCount(String key) {
        return redisTemplate.opsForList().size(key);
    }

    public void listRemove(String key, String data) {
        redisTemplate.opsForList().remove(key, 0, data);
    }
}

package com.dxx.game.modules.task.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/3 18:49
 */
@Data
public class TaskProcess {
    private int taskType;
    private int taskSubType;
    private long count;

    public static TaskProcess valueOf(int taskType, int taskSubType, long count) {
        TaskProcess taskProcess = new TaskProcess();
        taskProcess.setTaskType(taskType);
        taskProcess.setTaskSubType(taskSubType);
        taskProcess.setCount(count);
        return taskProcess;
    }
}

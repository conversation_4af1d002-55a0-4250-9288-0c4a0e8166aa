{"ActivityGetListRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "ActivityGetListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "ActivityInfoDto", "paramName": "activityInfos", "desc": " 正在开启的活动", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "refTime", "desc": " 每日数据重置时间", "isCustom": false}, {"rule": "map", "type": "map<uint32,TaskListDto>", "paramName": "taskMap", "desc": " 通用活动任务(key:活动ID, value:任务数据)", "isCustom": true}, {"rule": "map", "type": "map<uint32,ShopListDto>", "paramName": "shopMap", "desc": " 通用活动商店(key:活动ID, value:商店数据)", "isCustom": true}, {"rule": "Proto.SevenDayTask.SevenDayDto", "type": "Proto.SevenDayTask.SevenDayDto", "paramName": "sevenDayTaskDto", "desc": " 新号7日活动", "isCustom": true}, {"rule": "Proto.SignIn.SignInData", "type": "Proto.SignIn.SignInData", "paramName": "signInData", "desc": " 签到数据", "isCustom": true}, {"rule": "Proto.Fishing.FishingDto", "type": "Proto.Fishing.FishingDto", "paramName": "fishingDto", "desc": " 钓鱼活动", "isCustom": true}, {"rule": "Proto.Flip.FlipDto", "type": "Proto.Flip.FlipDto", "paramName": "flipDto", "desc": " 推箱子", "isCustom": true}, {"rule": "Proto.Dive.DiveDto", "type": "Proto.Dive.DiveDto", "paramName": "diveDto", "desc": " 潜水", "isCustom": true}, {"rule": "list", "type": "ConsumeDto", "paramName": "consumeDtos", "desc": " 通用消耗活动", "isCustom": true}, {"rule": "Proto.Power.PowerDto", "type": "Proto.Power.PowerDto", "paramName": "powerDto", "desc": " 战力排行", "isCustom": true}], "ActivityGetTaskRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "id", "desc": " 活动id", "isCustom": false}], "ActivityGetTaskResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "map", "type": "map<uint32,TaskListDto>", "paramName": "taskMap", "desc": "", "isCustom": true}], "ActivityTaskRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 活动id", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "taskId", "desc": " 任务id", "isCustom": false}], "ActivityTaskRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "Proto.Common.ActivityTaskDto", "paramName": "dtos", "desc": "", "isCustom": true}], "ActivityGetShopRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "id", "desc": " 活动id", "isCustom": false}], "ActivityGetShopResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "map", "type": "map<uint32,ShopListDto>", "paramName": "shopMap", "desc": "", "isCustom": true}], "ActivityShopExchangeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 活动id", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "shopId", "desc": " 配置id", "isCustom": false}], "ActivityShopExchangeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.ActivityShopDto", "type": "Proto.Common.ActivityShopDto", "paramName": "shop", "desc": "", "isCustom": true}], "ActivityGetRankRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 活动id", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "page", "desc": "", "isCustom": false}], "ActivityGetRankResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "Proto.Common.RankDto", "paramName": "rank", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "ownerRank", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "ownerScore", "desc": "", "isCustom": false}], "ActivityInfoDto": [{"rule": "uint32", "type": "uint32", "paramName": "activityId", "desc": " 配置id", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "startTime", "desc": " 活动开始时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "endTime", "desc": " 活动结束时间", "isCustom": false}], "ConsumeDto": [{"rule": "uint32", "type": "uint32", "paramName": "activityId", "desc": " 活动ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "completeCount", "desc": " 已达成次数", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "finishList", "desc": " 已完成的条目", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "round", "desc": " 轮数", "isCustom": false}], "TaskListDto": [{"rule": "list", "type": "Proto.Common.ActivityTaskDto", "paramName": "tasks", "desc": "", "isCustom": true}], "ShopListDto": [{"rule": "list", "type": "Proto.Common.ActivityShopDto", "paramName": "shop", "desc": "", "isCustom": true}], "RChapterCombatReq": [{"rule": "int32", "type": "int32", "paramName": "chapterId", "desc": " 章节ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "waveIndex", "desc": " 关卡ID", "isCustom": false}, {"rule": "Proto.Common.BattleUnitDto", "type": "Proto.Common.BattleUnitDto", "paramName": "unit", "desc": "", "isCustom": true}, {"rule": "list", "type": "int32", "paramName": "skills", "desc": " 技能", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "seed", "desc": " 随机种子", "isCustom": false}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "units", "desc": " 战斗单位信息", "isCustom": true}], "RChapterCombatResp": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "chapterId", "desc": " 章节ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "waveIndex", "desc": " 关卡ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "result", "desc": " 0:lose 1:win", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "seed", "desc": " 随机种子", "isCustom": false}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "startUnits", "desc": " 战斗开始单位信息", "isCustom": true}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "endUnits", "desc": " 战斗结束单位信息", "isCustom": true}, {"rule": "int64", "type": "int64", "paramName": "power", "desc": "", "isCustom": false}], "RGuildBossCombatReq": [{"rule": "int32", "type": "int32", "paramName": "bossId", "desc": " bossId", "isCustom": false}, {"rule": "Proto.Common.BattleUnitDto", "type": "Proto.Common.BattleUnitDto", "paramName": "unit", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "seed", "desc": " 随机种子", "isCustom": false}], "RGuildBossCombatResp": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "seed", "desc": " 随机种子", "isCustom": false}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "startUnits", "desc": " 战斗开始单位信息", "isCustom": true}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "endUnits", "desc": " 战斗结束单位信息", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "damage", "desc": " 伤害量", "isCustom": false}], "RpcPowerReq": [{"rule": "Proto.Common.BattleUnitDto", "type": "Proto.Common.BattleUnitDto", "paramName": "unit", "desc": "", "isCustom": true}], "RpcPowerResp": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "result", "desc": "", "isCustom": false}], "ChatShowItemRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "itemType", "desc": " 物品类型(道具表类型)", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 物品唯一ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "groupId", "desc": " 群组ID", "isCustom": false}], "ChatShowItemResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "CommonParams": [{"rule": "string", "type": "string", "paramName": "accountId", "desc": " 用户渠道唯一ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "version", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "deviceId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "accessToken", "desc": " 登录时服务器返回的令牌数据", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "transId", "desc": " 接口请求序列号 (客户端自增)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "serverId", "desc": " 分服id", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "languageMark", "desc": " 手机语言标识", "isCustom": false}], "UserLevel": [{"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " 等级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "exp", "desc": " 当前经验", "isCustom": false}], "UserCurrency": [{"rule": "int64", "type": "int64", "paramName": "coins", "desc": " 金币", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "diamonds", "desc": " 钻石", "isCustom": false}], "RewardDto": [{"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1 资源, 3 装备", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "configId", "desc": " 配置表ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 数量", "isCustom": false}], "ItemDto": [{"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 数据库唯一ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "itemId", "desc": " 配置表ID", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "count", "desc": " 数量", "isCustom": false}], "UpdateUserCurrency": [{"rule": "bool", "type": "bool", "paramName": "isChange", "desc": " 是否有更新", "isCustom": false}, {"rule": "UserCurrency", "type": "UserCurrency", "paramName": "userCurrency", "desc": " 更新后的数据", "isCustom": true}], "UpdateUserLevel": [{"rule": "bool", "type": "bool", "paramName": "isChange", "desc": " 是否有更新", "isCustom": false}, {"rule": "UserLevel", "type": "UserLevel", "paramName": "userLevel", "desc": " 用户等级经验", "isCustom": true}, {"rule": "list", "type": "<PERSON><PERSON><PERSON><PERSON>", "paramName": "levelUpReward", "desc": " 升级奖励", "isCustom": true}], "UpdateTransId": [{"rule": "bool", "type": "bool", "paramName": "isChange", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "transId", "desc": " 接口请求序列号", "isCustom": false}], "AdInfoDto": [{"rule": "uint32", "type": "uint32", "paramName": "adId", "desc": " 广告ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "leftCount", "desc": " 剩余次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxCount", "desc": " 最大次数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "cdTS", "desc": " cd时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "rTS", "desc": " 整体次数刷新时间", "isCustom": false}], "UserInfoDto": [{"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": " 昵称", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar", "desc": " 头像", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": " 头像框", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " 等级", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "power", "desc": " 战力", "isCustom": false}], "UserVitality": [{"rule": "int32", "type": "int32", "paramName": "value", "desc": " 当前体力", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "maxValue", "desc": " 最大体力", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "ts", "desc": " 最后一次恢复体力时间戳", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "buyCount", "desc": " 购买体力次数", "isCustom": false}], "UpdateUserVitality": [{"rule": "bool", "type": "bool", "paramName": "isChange", "desc": " 是否有改变", "isCustom": false}, {"rule": "UserVitality", "type": "UserVitality", "paramName": "vitality", "desc": "", "isCustom": true}], "CommonData": [{"rule": "UpdateTransId", "type": "UpdateTransId", "paramName": "updateTransId", "desc": " 更新接口请求序列号", "isCustom": true}, {"rule": "UpdateUserCurrency", "type": "UpdateUserCurrency", "paramName": "updateUserCurrency", "desc": " 更新货币", "isCustom": true}, {"rule": "list", "type": "<PERSON><PERSON><PERSON><PERSON>", "paramName": "reward", "desc": " 获得的奖励 - 展示用", "isCustom": true}, {"rule": "UpdateUserLevel", "type": "UpdateUserLevel", "paramName": "updateUserLevel", "desc": " 更新的等级和经验", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "taskRedPoint", "desc": " 任务红点-是否有完成的任务", "isCustom": false}, {"rule": "list", "type": "EquipmentDto", "paramName": "equipment", "desc": " 更新的装备列表", "isCustom": true}, {"rule": "list", "type": "ItemDto", "paramName": "items", "desc": " 更新的道具列表", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "guildTaskRedPoint", "desc": " 公会红点", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "deleteEquipRowIds", "desc": " 删除的装备唯一ID", "isCustom": false}, {"rule": "list", "type": "HeroDto", "paramName": "heros", "desc": " 英雄列表", "isCustom": true}, {"rule": "UpdateUserVipLevel", "type": "UpdateUserVipLevel", "paramName": "updateUserVipLevel", "desc": " 更新的vip等级和经验", "isCustom": true}, {"rule": "list", "type": "TaskDto", "paramName": "updateTasks", "desc": " 更新的任务数据", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "sevenDayTaskRedPoint", "desc": " 新手七日任务活动红点", "isCustom": false}, {"rule": "list", "type": "SevenDayTaskDto", "paramName": "sevenDayTaskDto", "desc": " 七日任务", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "battlePassScore", "desc": " 通行证积分", "isCustom": false}, {"rule": "list", "type": "ActivityTaskDto", "paramName": "activityTasks", "desc": " 活动任务", "isCustom": true}, {"rule": "map", "type": "map<uint32,Proto.Common.LongArray>", "paramName": "formations", "desc": " 保存的所有阵型信息", "isCustom": true}, {"rule": "UpdateUserVitality", "type": "UpdateUserVitality", "paramName": "updateUserVitality", "desc": " 体力信息", "isCustom": true}, {"rule": "list", "type": "AdInfoDto", "paramName": "adInfo", "desc": " 广告数据", "isCustom": true}], "ErrorMsg": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "msg", "desc": "", "isCustom": false}], "TowerRankDto": [{"rule": "int64", "type": "int64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "avatar", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "power", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "tower", "desc": " 配置id", "isCustom": false}], "RankDto": [{"rule": "int64", "type": "int64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "avatar", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "power", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "score", "desc": "", "isCustom": false}], "CombatUnitDto": [{"rule": "int64", "type": "int64", "paramName": "rowId", "desc": " hero rowId", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "instanceID", "desc": " 实例化ID", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "hp", "desc": " 血量", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "energy", "desc": " 怒气", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "hpPercent", "desc": " 血量万分比", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "rechargePercent", "desc": " 能量万分比", "isCustom": false}], "IAPDto": [{"rule": "IAPGiftPackDto", "type": "IAPGiftPackDto", "paramName": "giftPack", "desc": " 日周月礼包", "isCustom": true}, {"rule": "map", "type": "map<uint32,IAPMonthCardDto>", "paramName": "monthCardMap", "desc": " 内购月卡信息", "isCustom": true}, {"rule": "IAPBattlePassDto", "type": "IAPBattlePassDto", "paramName": "battlePassInfo", "desc": " 通行证信息", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "firstRechargeReward", "desc": " 是否已领取首冲奖励", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "totalRecharge", "desc": " 累充金额", "isCustom": false}, {"rule": "map", "type": "map<uint32,uint64>", "paramName": "buyOpenServerGiftId", "desc": " 已购买的开服礼包列表", "isCustom": true}, {"rule": "map", "type": "map<uint32,uint64>", "paramName": "chapterGiftTime", "desc": " 章节礼包购买时限", "isCustom": true}, {"rule": "LevelFundDto", "type": "LevelFundDto", "paramName": "levelFund", "desc": " 基金", "isCustom": true}], "LevelFundDto": [{"rule": "list", "type": "uint32", "paramName": "buyLevelFundGroupId", "desc": " 已购买的基金组", "isCustom": false}, {"rule": "map", "type": "map<uint32,IntegerArray>", "paramName": "levelFundReward", "desc": " 已领取的基金 key-groupId  value-已领取的id列表", "isCustom": true}], "IAPGiftPackDto": [{"rule": "map", "type": "map<uint32,uint32>", "paramName": "packsBuyCount", "desc": " 礼包购买次数", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "packsResetTimeDay", "desc": " 日礼包下次重置时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "packsResetTimeWeek", "desc": " 周礼包下次重置时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "packsResetTimeMonth", "desc": " 月礼包下次重置时间", "isCustom": false}], "IAPMonthCardDto": [{"rule": "uint32", "type": "uint32", "paramName": "configId", "desc": " 月卡ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "lastCount", "desc": " 月卡剩余次数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "lastRewardTime", "desc": " 月卡最后领取奖励信息", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "canReward", "desc": " 是否可以领取 1-可以领取 0-不可领取", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "nextRewardTime", "desc": " 下次领取时间", "isCustom": false}], "IAPBattlePassDto": [{"rule": "uint32", "type": "uint32", "paramName": "battlePassId", "desc": " 通行证ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "buy", "desc": " 是否已购买 1-已购买 0-未购买", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "score", "desc": " 通行证积分", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "freeRewardIdList", "desc": " 已领取的免费奖励ID", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "battlePassRewardIdList", "desc": " 已领取的付费奖励ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "canRewardFinalCount", "desc": " 可以领取的最终奖励次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "rewardFinalCount", "desc": " 循环奖励已领取次数", "isCustom": false}], "UpdateUserVipLevel": [{"rule": "bool", "type": "bool", "paramName": "isChange", "desc": " 是否有更新", "isCustom": false}, {"rule": "UserVipLevel", "type": "UserVipLevel", "paramName": "userVipLevel", "desc": " 用户VIP等级经验", "isCustom": true}], "UserVipLevel": [{"rule": "uint32", "type": "uint32", "paramName": "vipLevel", "desc": " VIP等级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "vipExp", "desc": " VIP经验", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "rewardId", "desc": " 已领取的ID列表", "isCustom": false}], "IntegerArray": [{"rule": "list", "type": "int32", "paramName": "integerArray", "desc": "", "isCustom": false}], "Uint32List": [{"rule": "list", "type": "uint32", "paramName": "values", "desc": "", "isCustom": false}], "HeroDto": [{"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 数据库唯一ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "heroId", "desc": " 配置表ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " 等级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "exp", "desc": " 经验", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "star", "desc": " 星级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "quality", "desc": " 品质", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "advance", "desc": " 进阶等级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "skinConfigId", "desc": " skin配置Id", "isCustom": false}], "EquipmentDto": [{"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 数据库唯一ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "equipId", "desc": " 配置表ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " 等级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "exp", "desc": " 经验", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "quality", "desc": " 品质", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 穿戴hero", "isCustom": false}], "EquipmentStatsDto": [{"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 数据库唯一ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "equipId", "desc": " 配置表ID", "isCustom": false}, {"rule": "list", "type": "string", "paramName": "attrs", "desc": " 词条属性", "isCustom": false}], "BattleUserDto": [{"rule": "int64", "type": "int64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "serverId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "level", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "avatar", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "power", "desc": "", "isCustom": false}, {"rule": "BattleUnitDto", "type": "BattleUnitDto", "paramName": "unit", "desc": "", "isCustom": true}], "BattleUnitDto": [{"rule": "int64", "type": "int64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "list", "type": "Proto.Common.EquipmentDto", "paramName": "equips", "desc": "", "isCustom": true}, {"rule": "map", "type": "map<int32,FormationDto>", "paramName": "formations", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "guildTechLv", "desc": "", "isCustom": false}, {"rule": "list", "type": "int32", "paramName": "skinConfigIds", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "vipLevel", "desc": " VIP等级", "isCustom": false}, {"rule": "list", "type": "Proto.Common.DIntInt", "paramName": "heroBookScoreCounts", "desc": " 英雄图鉴已加积分次数", "isCustom": true}, {"rule": "list", "type": "Proto.Common.DIntInt", "paramName": "heroBondCounts", "desc": " 英雄羁绊已领取次数", "isCustom": true}], "FormationDto": [{"rule": "list", "type": "Proto.Common.HeroDto", "paramName": "heroes", "desc": "", "isCustom": true}], "PVPRecordDto": [{"rule": "BattleUserDto", "type": "BattleUserDto", "paramName": "ownerUser", "desc": "", "isCustom": true}, {"rule": "BattleUserDto", "type": "BattleUserDto", "paramName": "otherUser", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "result", "desc": " 0:lose 1:win", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "seed", "desc": " 随机种子", "isCustom": false}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "startUnits", "desc": " 战斗开始单位信息", "isCustom": true}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "endUnits", "desc": " 战斗结束单位信息", "isCustom": true}, {"rule": "int64", "type": "int64", "paramName": "reportRowId", "desc": " 战报rowId", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "time", "desc": "", "isCustom": false}], "ActivityTaskDto": [{"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 任务ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "activityId", "desc": " 活动ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "process", "desc": " 任务进度", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isReceive", "desc": " 奖励是否领取", "isCustom": false}], "ActivityShopDto": [{"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " configId", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "activityId", "desc": " 活动ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 兑换次数", "isCustom": false}], "RewardDtoListDto": [{"rule": "list", "type": "<PERSON><PERSON><PERSON><PERSON>", "paramName": "rewardDtos", "desc": "", "isCustom": true}], "DIntInt": [{"rule": "int32", "type": "int32", "paramName": "key", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "val", "desc": "", "isCustom": false}], "DLongInt": [{"rule": "int64", "type": "int64", "paramName": "key", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "val", "desc": "", "isCustom": false}], "TaskDto": [{"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 配置表ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "process", "desc": " 进度", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "is<PERSON><PERSON><PERSON>", "desc": " 是否完成", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isReceive", "desc": " 是否领取奖励", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "taskType", "desc": " 任务类型(1每日, 2每周, 3成就)", "isCustom": false}], "SevenDayTaskDto": [{"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 任务ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "process", "desc": " 任务进度", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "is<PERSON><PERSON><PERSON>", "desc": " 是否完成", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isReceive", "desc": " 奖励是否领取", "isCustom": false}], "LongArray": [{"rule": "list", "type": "int64", "paramName": "longArray", "desc": "", "isCustom": false}], "BattleUnitCacheDto": [{"rule": "int64", "type": "int64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "map", "type": "map<int32,LongArray>", "paramName": "formations", "desc": "", "isCustom": true}, {"rule": "list", "type": "Proto.Common.EquipmentDto", "paramName": "equips", "desc": "", "isCustom": true}, {"rule": "list", "type": "Proto.Common.HeroDto", "paramName": "heroes", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "guildTechLv", "desc": "", "isCustom": false}, {"rule": "list", "type": "int32", "paramName": "skinConfigIds", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "vipLevel", "desc": " VIP等级", "isCustom": false}, {"rule": "list", "type": "Proto.Common.DIntInt", "paramName": "heroBookScoreCounts", "desc": " 英雄图鉴已加积分次数", "isCustom": true}, {"rule": "list", "type": "Proto.Common.DIntInt", "paramName": "heroBondCounts", "desc": " 英雄羁绊已领取次数", "isCustom": true}], "DevelopLoginRequest": [{"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": "", "isCustom": false}], "DevelopLoginResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int64", "type": "int64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "dbIdx", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "tableIdx", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "coins", "desc": "", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "diamonds", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "chapterId", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "loginType", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "exp", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extra", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "missionId", "desc": "", "isCustom": false}], "DevelopChangeResourceRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "resType", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "resNum", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "itemId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "otherData", "desc": "", "isCustom": false}], "DevelopChangeResourceResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": " 通用返回", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "respData", "desc": "", "isCustom": false}], "DevelopToolsRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "params", "desc": "", "isCustom": false}], "DevelopToolsResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "respData", "desc": "", "isCustom": false}], "DiveOnOpenRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "DiveOnOpenResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "DiveDto", "type": "DiveDto", "paramName": "diveDto", "desc": "", "isCustom": true}], "DiveBuyItemRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "buyCount", "desc": " 购买数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "butItemId", "desc": " 道具ID", "isCustom": false}], "DiveBuyItemResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "DiveAccRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "accType", "desc": " 1=个人进度 2=公会进度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "need", "desc": " 需要进度", "isCustom": false}], "DiveAccRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "DiveDto", "type": "DiveDto", "paramName": "diveDto", "desc": "", "isCustom": true}], "DiveShineRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "depth", "desc": " 深度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 行坐标 从0开始", "isCustom": false}], "DiveShineResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "DiveGridDto", "type": "DiveGridDto", "paramName": "<PERSON><PERSON><PERSON>", "desc": " 照亮格子信息", "isCustom": true}, {"rule": "DiveGridDto", "type": "DiveGridDto", "paramName": "sharkGrid", "desc": " 鲨鱼逃窜的格子", "isCustom": true}, {"rule": "DiveGridDto", "type": "DiveGridDto", "paramName": "varec<PERSON><PERSON>", "desc": " 海藻中心格子", "isCustom": true}, {"rule": "DiveDto", "type": "DiveDto", "paramName": "diveDto", "desc": " 同步数据", "isCustom": true}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}], "DiveUsePropRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "depth", "desc": " 深度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 行坐标 从0开始", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "itemType", "desc": " 0=手电筒 1=炸弹", "isCustom": false}], "DiveUsePropResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "DiveGridDto", "paramName": "<PERSON><PERSON><PERSON>", "desc": " 照亮格子信息", "isCustom": true}, {"rule": "list", "type": "DiveGridDto", "paramName": "sharkGrid", "desc": " 鲨鱼逃窜的格子", "isCustom": true}, {"rule": "list", "type": "DiveGridDto", "paramName": "varec<PERSON><PERSON>", "desc": " 海藻中心格子", "isCustom": true}, {"rule": "DiveDto", "type": "DiveDto", "paramName": "diveDto", "desc": " 同步数据", "isCustom": true}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}], "DiveAllAccRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "DiveAllAccRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "needs", "desc": "", "isCustom": false}, {"rule": "DiveDto", "type": "DiveDto", "paramName": "dto", "desc": "", "isCustom": true}], "DiveDto": [{"rule": "uint32", "type": "uint32", "paramName": "activityId", "desc": " 活动ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "startTimestamp", "desc": " 开启时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "endTimestamp", "desc": " 结束时间", "isCustom": false}, {"rule": "DiveConfigDto", "type": "DiveConfigDto", "paramName": "configDto", "desc": " 配置数据", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "userDepth", "desc": " 个人深度", "isCustom": false}, {"rule": "list", "type": "DiveAccRewardDto", "paramName": "userAccRewards", "desc": " 个人累计奖励", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "guildDepth", "desc": " 公会深度", "isCustom": false}, {"rule": "list", "type": "DiveAccRewardDto", "paramName": "guildAccRewards", "desc": " 公会累计奖励", "isCustom": true}, {"rule": "list", "type": "DiveLineDto", "paramName": "lineDtos", "desc": " 地图行数据", "isCustom": true}], "DiveConfigDto": [{"rule": "uint32", "type": "uint32", "paramName": "diveItemId", "desc": " 潜水道具", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "divePrice", "desc": " 潜水道具售价钻石", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "divePropA", "desc": " 清除列道具手电筒", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "divePropB", "desc": " 清除一片道具炸弹", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "divePrice1", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "divePrice2", "desc": "", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "exchangeItem", "desc": "", "isCustom": false}], "DiveAccRewardDto": [{"rule": "uint32", "type": "uint32", "paramName": "need", "desc": "", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "state", "desc": " 0不可领取 1已领取 2可领取", "isCustom": false}], "DiveLineDto": [{"rule": "uint32", "type": "uint32", "paramName": "depth", "desc": " 深度", "isCustom": false}, {"rule": "list", "type": "DiveGridDto", "paramName": "gridDtos", "desc": " 格子", "isCustom": true}], "DiveGridDto": [{"rule": "int32", "type": "int32", "paramName": "type", "desc": " 格子类型 -1=通路 1=气泡 2=冰块 3=连续奖励 4=海藻 5=海藻中心 6=水母泡泡 7=手电筒泡泡 8=炸弹泡泡 9=贝壳A泡泡 10=贝壳B泡泡 11特殊奖励1泡泡", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "shark", "desc": " 鲨鱼血量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "varecReward<PERSON>um", "desc": " 海藻奖励数字", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "ice", "desc": " 冰块血量", "isCustom": false}, {"rule": "Proto.Common.Reward<PERSON>to", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "depth", "desc": " 深度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 行坐标 从0开始", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "light", "desc": " 0=暗 1=亮", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "varecClick", "desc": " 海藻false=未点击 true=点击过", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "rwardNum", "desc": " 海藻奖励数字", "isCustom": false}], "EquipStrengthRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 待强化装备rowId", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "equipRowIds", "desc": " 选中的装备rowId", "isCustom": false}, {"rule": "map", "type": "map<uint32,uint32>", "paramName": "useItems", "desc": " 选中的强化道具 key:configId, value:count", "isCustom": true}], "EquipStrengthResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "delEquipRowIds", "desc": " 删除的装备RowId", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipComposeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "EquipComposeData", "paramName": "composeData", "desc": " 合成的数据列表", "isCustom": true}], "EquipComposeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "int64", "paramName": "delEquipRowId", "desc": " 删除的装备RowID", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipUpgradeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 升级的装备RowId", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 升级次数", "isCustom": false}], "EquipUpgradeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipDressRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": " 装备rowId", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 穿戴hero", "isCustom": false}], "EquipDressResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": "装备rowId", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipLevelResetRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": " 装备rowIds", "isCustom": false}], "EquipLevelResetResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipQualityDownRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": " 装备唯一id", "isCustom": false}], "EquipQualityDownResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipOffRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": " 装备rowId", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " hero", "isCustom": false}], "EquipOffResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": "装备rowId", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipReplaceRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 装备rowId", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " hero", "isCustom": false}], "EquipReplaceResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "EquipComposeData": [{"rule": "uint64", "type": "uint64", "paramName": "mainRowId", "desc": " 主装备ID", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "rowIds", "desc": " 材料装备ID", "isCustom": false}], "FishingOnOpenRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "FishingOnOpenResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "FishingDto", "type": "FishingDto", "paramName": "fish", "desc": "", "isCustom": true}], "FishingCastRodRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "baitNum", "desc": " 消耗鱼饵数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "eval", "desc": " 0:Normal 1:Good 2:Perfect", "isCustom": false}], "FishingCastRodResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "fishIds", "desc": " 鱼ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "nextRebornDiamond", "desc": " 下次复活需要钻石", "isCustom": false}], "FishingReelInRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "catch", "desc": " 是否钓上来鱼 false=断线", "isCustom": false}], "FishingReelInResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "FishDto", "paramName": "fishDtos", "desc": " 鱼", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "weight", "desc": " 总重量", "isCustom": false}, {"rule": "FishingDto", "type": "FishingDto", "paramName": "FishingDto", "desc": " 钓鱼活动数", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "unlockRod", "desc": " 解锁的鱼竿", "isCustom": false}], "FishingBuyBaitRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "buyNum", "desc": " 购买数量", "isCustom": false}], "FishingBuyBaitResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "FishingRebornRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "isAd", "desc": " 看广告", "isCustom": false}], "FishingRebornResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "nextRebornDiamond", "desc": " 下次复活需要钻石", "isCustom": false}], "FishingDto": [{"rule": "uint32", "type": "uint32", "paramName": "accWeight", "desc": " 累计已钓KG", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "curRod", "desc": " 当前鱼竿", "isCustom": false}, {"rule": "FishingConfigDto", "type": "FishingConfigDto", "paramName": "configDto", "desc": " 配置", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "nextRebornDiamond", "desc": " 下次复活需要钻石", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isThrow", "desc": " 是否已抛竿", "isCustom": false}], "FishingConfigDto": [{"rule": "uint32", "type": "uint32", "paramName": "baitItemId", "desc": " 鱼饵道具ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "pointItemId", "desc": " 积分道具ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "lineItemId", "desc": " 断了的线道具ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "baitPrice", "desc": " 鱼饵钻石售价", "isCustom": false}], "FishDto": [{"rule": "uint32", "type": "uint32", "paramName": "fishId", "desc": " 鱼ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "weight", "desc": " 鱼重量", "isCustom": false}], "FlipOnOpenRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "FlipOnOpenResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "FlipDto", "type": "FlipDto", "paramName": "flipDto", "desc": "", "isCustom": true}], "FlipAccRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "need", "desc": " 需要线索数", "isCustom": false}], "FlipAccRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "clueNum", "desc": " 已获线索数量", "isCustom": false}, {"rule": "list", "type": "AccClueRewardDto", "paramName": "accRewards", "desc": " 累计奖励", "isCustom": true}], "FlipBuyStepRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "buyNum", "desc": " 购买数量", "isCustom": false}], "FlipBuyStepResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "FlipShowGridRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "isItem", "desc": " 是否使用全开道具", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 1.红 2.蓝 3.绿 4.紫 11.全开格子 12.部分开格子", "isCustom": false}], "FlipShowGridResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "showGrids", "desc": " 展示的格子", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "mapGrids", "desc": " 所有格子", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "accSteps", "desc": " 累计消耗步数", "isCustom": false}], "FlipRewardGridRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 6.特殊奖励格子 7.步数格子 13.收集红 14.收集蓝 15.收集绿 16.收集紫", "isCustom": false}], "FlipRewardGridResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "showGrids", "desc": " 展示的格子", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "activeGrids", "desc": " 激活的格子", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "mapGrids", "desc": " 所有格子", "isCustom": true}, {"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "impasseShowGrid", "desc": " 死局时翻开的格子", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "accSteps", "desc": " 累计消耗步数", "isCustom": false}], "FlipClueGridRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 5.线索格子", "isCustom": false}], "FlipClueGridResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "clueNum", "desc": " 线索数量", "isCustom": false}, {"rule": "list", "type": "AccClueRewardDto", "paramName": "accRewards", "desc": " 累计奖励", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "mapGrids", "desc": " 所有格子(新地图)", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "hasSpecialRewards", "desc": " 地图是否有特殊奖励", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "accSteps", "desc": " 累计消耗步数", "isCustom": false}, {"rule": "list", "type": "FlipGridDto", "paramName": "oldMapGrids", "desc": " 所有格子(旧地图)", "isCustom": true}], "FlipBombGridRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 8.炸弹格子 9.横向炸弹格子 10.纵向炸弹格子", "isCustom": false}], "FlipBombGridResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "FlipBombGridDto", "paramName": "bombGrids", "desc": " 1.爆炸数据(8.炸弹格子 9.横向炸弹格子 10.纵向炸弹格子)", "isCustom": true}, {"rule": "list", "type": "FlipBombRewardGridDto", "paramName": "rewardGrids", "desc": " 2.爆炸奖励格数据(6.特殊奖励格子 7.步数格子)", "isCustom": true}, {"rule": "list", "type": "FlipBombAllShowGridDto", "paramName": "allShowGrids", "desc": " 3.全开格数据(11.全开格子 12.部分开格子)", "isCustom": true}, {"rule": "list", "type": "FlipBombStoneGridDto", "paramName": "stoneGrids", "desc": " 4.石头格数据(1.红 2.蓝 3.绿 4.紫)", "isCustom": true}, {"rule": "list", "type": "FlipBombCollectGridDto", "paramName": "collectGrids", "desc": " 5.收集格数据(13.收集红 14.收集蓝 15.收集绿 16.收集紫)", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "mapGrids", "desc": " 所有格子", "isCustom": true}, {"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "impasseShowGrid", "desc": " 死局时翻开的格子", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "accSteps", "desc": " 累计消耗步数", "isCustom": false}], "FlipMapFindSpecialRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "FlipMapFindSpecialResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "hasSpecialRewards", "desc": " 地图是否有特殊奖励", "isCustom": false}], "FlipAllAccRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "FlipAllAccRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "needs", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "clueNum", "desc": " 已获线索数量", "isCustom": false}, {"rule": "list", "type": "AccClueRewardDto", "paramName": "accRewards", "desc": " 累计奖励", "isCustom": true}], "FlipBombGridDto": [{"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "bombSource", "desc": " 炸弹(8.炸弹格子 9.横向炸弹格子 10.纵向炸弹格子)", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "showGrids", "desc": " 打开的格子", "isCustom": true}], "FlipBombRewardGridDto": [{"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "rewardSource", "desc": " 奖励格(6.特殊奖励格子 7.步数格子)", "isCustom": true}, {"rule": "Proto.Common.Reward<PERSON>to", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}], "FlipBombAllShowGridDto": [{"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "showSource", "desc": " 全开格(11.全开格子 12.部分开格子)", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "showGrids", "desc": " 打开的格子", "isCustom": true}], "FlipBombStoneGridDto": [{"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "source", "desc": " 来源(8.炸弹格子 9.横向炸弹格子 10.纵向炸弹格子 13.收集红 14.收集蓝 15.收集绿 16.收集紫)", "isCustom": true}, {"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "self", "desc": " 石头格", "isCustom": true}, {"rule": "Proto.Common.Reward<PERSON>to", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}], "FlipBombCollectGridDto": [{"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "collectSource", "desc": " 收集格(13.收集红 14.收集蓝 15.收集绿 16.收集紫)", "isCustom": true}, {"rule": "list", "type": "FlipCollectEffectDto", "paramName": "effects", "desc": " 收集效果", "isCustom": true}], "FlipCollectEffectDto": [{"rule": "FlipGridDto", "type": "FlipGridDto", "paramName": "<PERSON><PERSON><PERSON><PERSON>", "desc": " 石头格", "isCustom": true}, {"rule": "Proto.Common.Reward<PERSON>to", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}], "FlipDto": [{"rule": "uint32", "type": "uint32", "paramName": "activityId", "desc": " 活动ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "clueNum", "desc": " 线索数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "serverClueNum", "desc": " 全服线索数量", "isCustom": false}, {"rule": "list", "type": "AccClueRewardDto", "paramName": "accRewards", "desc": " 累计奖励", "isCustom": true}, {"rule": "list", "type": "FlipGridDto", "paramName": "grids", "desc": " 所有格子", "isCustom": true}, {"rule": "FlipConfigDto", "type": "FlipConfigDto", "paramName": "config", "desc": " 配置数据", "isCustom": true}], "AccClueRewardDto": [{"rule": "uint32", "type": "uint32", "paramName": "need", "desc": "", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "state", "desc": " 0不可领取 1已领取 2可领取", "isCustom": false}], "FlipGridDto": [{"rule": "uint32", "type": "uint32", "paramName": "index", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "status", "desc": " 0盖着 1翻开 2激活", "isCustom": false}, {"rule": "Proto.Common.Reward<PERSON>to", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewardDto", "desc": " 奖励", "isCustom": true}], "FlipConfigDto": [{"rule": "uint32", "type": "uint32", "paramName": "stepItemId", "desc": " 步数道具ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "clueItemId", "desc": " 线索道具ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "step<PERSON><PERSON>", "desc": " 步数钻石价格", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "stones", "desc": " 四种石头兑换物ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "allShowItemId", "desc": " 主动使用的全开道具ID", "isCustom": false}], "GuildGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isJoined", "desc": " 是否加入公会", "isCustom": false}, {"rule": "GuildDetailInfoDto", "type": "GuildDetailInfoDto", "paramName": "guildDetailInfoDto", "desc": " 自己的公会信息-已加入时返回", "isCustom": true}, {"rule": "GuildFeaturesDto", "type": "GuildFeaturesDto", "paramName": "guildFeaturesDto", "desc": " 公会功能数据-已加入时返回", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "isLevelUp", "desc": " 公会是否升级", "isCustom": false}, {"rule": "BeKickedOutDto", "type": "BeKickedOutDto", "paramName": "beKickedOutDto", "desc": " 被踢出公会信息", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "donationReward", "desc": " 捐赠-自动领取的道具", "isCustom": true}], "GuildCreateRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "guildName", "desc": " 公会名称", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildIntro", "desc": " 公会简介(宣言)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "guildIcon", "desc": " 公会图标", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "guildIconBg", "desc": " 公会图标背景", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyType", "desc": " 申请类型(0自由加入,1审批加入)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyCondition", "desc": " 申请加入条件", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "language", "desc": " 语言配置ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildNotice", "desc": " 公会公告", "isCustom": false}], "GuildCreateResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildDetailInfoDto", "type": "GuildDetailInfoDto", "paramName": "guildDetailInfoDto", "desc": " 自己的公会信息", "isCustom": true}, {"rule": "GuildFeaturesDto", "type": "GuildFeaturesDto", "paramName": "guildFeaturesDto", "desc": " 公会功能数据", "isCustom": true}], "GuildSearchRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 0-获取推荐列表, 1-根据公会名称或id查询,2-默认的排行榜规则(目前按照等级)", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "value", "desc": " 公会名称或者id", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isOnlyJoinable", "desc": " 是否只显示可以加入的公会", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "excludeGuildIds", "desc": " 已返回的客户端公会ID列表", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "pageIndex", "desc": " 页码从1开始(只有type = 2时生效)", "isCustom": false}], "GuildSearchResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "GuildInfoDto", "paramName": "guildInfoDtos", "desc": " 公会列表", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildGetDetailRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": " 公会ID", "isCustom": false}], "GuildGetDetailResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "GuildDetailInfoDto", "type": "GuildDetailInfoDto", "paramName": "guildDetailInfoDto", "desc": " 公会详细信息", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildGetMemberListRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": " 公会ID", "isCustom": false}], "GuildGetMemberListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "GuildMemberInfoDto", "paramName": "guildMemberInfoDtos", "desc": " 公会成员列表", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildModifyRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "guildName", "desc": " 公会名称", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildIntro", "desc": " 公会简介(宣言)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "guildIcon", "desc": " 公会图标", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "guildIconBg", "desc": " 公会图标背景", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyType", "desc": " 申请类型(0自由加入,1审批加入)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyCondition", "desc": " 申请加入条件", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isModifyGuildIntro", "desc": " 是否更新了公会简介", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "language", "desc": " 语言配置ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildNotice", "desc": " 公会公告", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isModifyGuildNotice", "desc": " 是否更新了公会公告", "isCustom": false}], "GuildModifyResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildInfoDto", "type": "GuildInfoDto", "paramName": "guildInfoDto", "desc": " 自己的公会信息", "isCustom": true}], "GuildDismissRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildDismissResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildApplyJoinRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": " 公会ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "language", "desc": " 当前用户语言ID", "isCustom": false}], "GuildApplyJoinResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "GuildDetailInfoDto", "type": "GuildDetailInfoDto", "paramName": "guildDetailInfoDto", "desc": " 公会详细信息-公会是自由加入类型时返回", "isCustom": true}, {"rule": "GuildFeaturesDto", "type": "GuildFeaturesDto", "paramName": "guildFeaturesDto", "desc": " 公会功能数据", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildCancelApplyRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": " 公会ID", "isCustom": false}], "GuildCancelApplyResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildAutoJoinRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildAutoJoinResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "GuildDetailInfoDto", "type": "GuildDetailInfoDto", "paramName": "guildDetailInfoDto", "desc": "", "isCustom": true}, {"rule": "GuildFeaturesDto", "type": "GuildFeaturesDto", "paramName": "guildFeaturesDto", "desc": " 公会功能数据", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildGetApplyListRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildGetApplyListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "GuildMemberInfoDto", "paramName": "applyList", "desc": " 申请列表", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildAgreeJoinRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "userIds", "desc": " 同意加入的用户ID列表", "isCustom": false}], "GuildAgreeJoinResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "GuildMemberInfoDto", "paramName": "guildMemberInfoDto", "desc": " 新加入的公会成员信息", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "joinOtherGuildUserIds", "desc": " 已加入了其他公会的用户ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyCount", "desc": " 申请数量", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildRefuseJoinRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "userIds", "desc": " 拒绝的用户ID列表", "isCustom": false}], "GuildRefuseJoinResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyCount", "desc": " 申请数量", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildKickOutRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": " 被踢的用户ID", "isCustom": false}], "GuildKickOutResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "members", "desc": " 公会成员人数", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildLeaveRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildLeaveResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildUpPositionRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "position", "desc": " 2副会长,3管理,4成员", "isCustom": false}], "GuildUpPositionResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "GuildMemberInfoDto", "type": "GuildMemberInfoDto", "paramName": "guildMemberInfoDto", "desc": " 修改职位的成员信息", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildTransferPresidentRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": "", "isCustom": false}], "GuildTransferPresidentResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "GuildMemberInfoDto", "paramName": "guildMemberInfoDtos", "desc": " 双方的成员信息", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildGetFeaturesInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildGetFeaturesInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "GuildFeaturesDto", "type": "GuildFeaturesDto", "paramName": "guildFeaturesDto", "desc": " 公会功能数据", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildLevelUpRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildLevelUpResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildUpdateInfoDto", "type": "GuildUpdateInfoDto", "paramName": "guildUpdateInfo", "desc": " 公会数据更新", "isCustom": true}, {"rule": "list", "type": "GuildTaskDto", "paramName": "tasks", "desc": " 所有任务数据", "isCustom": true}, {"rule": "list", "type": "GuildShopDto", "paramName": "dailyShop", "desc": " 所有每日商店", "isCustom": true}, {"rule": "list", "type": "GuildShopDto", "paramName": "weeklyShop", "desc": " 所有每周商店", "isCustom": true}], "GuildSignInRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildSignInResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuilSignInDto", "type": "GuilSignInDto", "paramName": "signInDto", "desc": " 签到数据", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "userDailyActive", "desc": " 用户日活跃", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "userWeeklyActive", "desc": " 用户周活跃", "isCustom": false}, {"rule": "GuildUpdateInfoDto", "type": "GuildUpdateInfoDto", "paramName": "guildUpdateInfo", "desc": " 公会数据更新", "isCustom": true}, {"rule": "list", "type": "GuildTaskDto", "paramName": "tasks", "desc": " 更新的任务数据", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "signInRecord", "desc": " 自己新增的签到记录(json格式包含的字段:userId,nickName,itemId(签到使用的道具ID(0免费)),timestamp})", "isCustom": false}], "GuildShopBuyRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1:每日, 2每周", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "shopId", "desc": "", "isCustom": false}], "GuildShopBuyResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildShopDto", "type": "GuildShopDto", "paramName": "guildShopDto", "desc": " 购买之后的数据", "isCustom": true}, {"rule": "list", "type": "GuildTaskDto", "paramName": "tasks", "desc": " 更新的任务数据", "isCustom": true}], "GuildShopRefreshRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1:每日, 2每周", "isCustom": false}], "GuildShopRefreshResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型  1:每日, 2每周", "isCustom": false}, {"rule": "list", "type": "GuildShopDto", "paramName": "shopDto", "desc": " 对应类型的商店数据", "isCustom": true}], "GuildTaskRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "taskId", "desc": "", "isCustom": false}], "GuildTaskRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildTaskDto", "type": "GuildTaskDto", "paramName": "updateTaskDto", "desc": " 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "deleteTaskDtoId", "desc": " 删除的任务ID", "isCustom": false}, {"rule": "list", "type": "GuildTaskDto", "paramName": "tasks", "desc": " 领取任务奖励触发其他任务状态变化", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "userDailyActive", "desc": " 用户日活跃", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "userWeeklyActive", "desc": " 用户周活跃", "isCustom": false}, {"rule": "GuildUpdateInfoDto", "type": "GuildUpdateInfoDto", "paramName": "guildUpdateInfo", "desc": " 公会数据更新", "isCustom": true}], "GuildTaskRefreshRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "taskId", "desc": " 任务ID", "isCustom": false}], "GuildTaskRefreshResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildTaskDto", "type": "GuildTaskDto", "paramName": "guildTask", "desc": " 新任务数据", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "taskRefreshCount", "desc": " 任务剩余免费刷新次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "taskRefreshCost", "desc": " 任务刷新超过免费次数后需要的钻石数量", "isCustom": false}], "GuildGetMessageRecordsRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "msgId", "desc": " 最大消息ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "pageIndex", "desc": " 页数从1开始(每页20条,超出配置的值直接返回空数组)", "isCustom": false}], "GuildGetMessageRecordsResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "repeatedGuildPushMessageDtoRecords=2;//消息记录": [{"rule": "list", "type": "GuildPushMessageDto", "paramName": "messageRecords", "desc": " 消息记录", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildDonationReqItemRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "itemId", "desc": "", "isCustom": false}], "GuildDonationReqItemResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildDonationDto", "type": "GuildDonationDto", "paramName": "guildDonationDto", "desc": "", "isCustom": true}], "GuildDonationSendItemRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "msgId", "desc": " 消息ID", "isCustom": false}], "GuildDonationSendItemResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "GuildDonationDto", "type": "GuildDonationDto", "paramName": "guildDonationDto", "desc": "", "isCustom": true}], "GuildDonationReceiveRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "msgId", "desc": " 消息ID", "isCustom": false}], "GuildDonationReceiveResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "receiveCount", "desc": " 已领取数量", "isCustom": false}], "GuildDonationGetRecordsRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "msgId", "desc": " 最大消息ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "pageIndex", "desc": " 页数从1开始", "isCustom": false}], "GuildDonationGetRecordsResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "string", "paramName": "records", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildDonationGetOperationRecordsRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "msgId", "desc": " 最大消息ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "pageIndex", "desc": " 页数从1开始", "isCustom": false}], "GuildDonationGetOperationRecordsResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "string", "paramName": "records", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "GuildBossBattleRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 挑战类型", "isCustom": false}], "GuildBossBattleResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "challengeCnt", "desc": " 剩余挑战次数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "damage", "desc": " 本次伤害", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "totalDamage", "desc": " 总伤害", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "seed", "desc": " 随机种子", "isCustom": false}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "startUnits", "desc": " 战斗开始单位信息", "isCustom": true}, {"rule": "list", "type": "Proto.Common.CombatUnitDto", "paramName": "endUnits", "desc": " 战斗结束单位信息", "isCustom": true}], "GuildBossBattleGRankRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildBossBattleGRankResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "GuildSimpleDto", "paramName": "dtos", "desc": "", "isCustom": true}], "GuildTechUpgradeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "GuildTechUpgradeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "lv", "desc": "", "isCustom": false}], "GuildInfoDto": [{"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": " 公会id", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildName", "desc": " 公会名称", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildIntro", "desc": " 公会简介(宣言)", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "guildIcon", "desc": " 公会图标", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "guildIconBg", "desc": " 公会图标背景", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "members", "desc": " 公会人数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxMembers", "desc": " 公会最大人数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "active", "desc": " 公会活跃度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " 公会等级", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyType", "desc": " 申请类型(0:自由加入,1:审批加入)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "applyCondition", "desc": " 申请加入条件", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isApply", "desc": " 是否已申请", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "language", "desc": " 语言配置ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildNotice", "desc": " 公会公告", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "guildPresidentUserId", "desc": " 当前会长用户ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildPresidentNickName", "desc": " 当前会长昵称", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "exp", "desc": " 公会经验", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "totalPower", "desc": " 工会总战力", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "imGroupId", "desc": " 群聊ID", "isCustom": false}], "GuildSimpleDto": [{"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildName", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "power", "desc": " 公会总战力", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "damage", "desc": " 总伤害", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": "  公会等级", "isCustom": false}], "GuildMemberInfoDto": [{"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": " 用户ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": " 昵称", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar", "desc": " 头像ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": " 头像框ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " 等级", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "activeTime", "desc": " 最近一次活跃时间", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "position", "desc": " 职位(1会长,2副会长,3管理,4成员)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "chapterId", "desc": " 当前章节ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "atk", "desc": " 攻击力", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "hp", "desc": " 血量", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "battlePower", "desc": " 战斗力", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "applyTime", "desc": " 申请时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "joinTime", "desc": " 加入时间", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "dailyActive", "desc": " 每日活跃度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "weekActive", "desc": " 每周活跃度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "heroConfigId", "desc": " 出战英雄配置ID(排行榜列表前3有值)", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "skinItemConfigId", "desc": " 英雄皮肤道具ID(排行榜列表3有值)", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "equipIds", "desc": " 穿戴的装备ID(排行榜列表3有值)", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isOnline", "desc": "", "isCustom": false}], "GuildDetailInfoDto": [{"rule": "GuildInfoDto", "type": "GuildInfoDto", "paramName": "guildInfoDto", "desc": " 公会信息", "isCustom": true}, {"rule": "list", "type": "GuildMemberInfoDto", "paramName": "guildMemberInfoDtos", "desc": " 公会成员列表", "isCustom": true}], "GuildTaskDto": [{"rule": "uint32", "type": "uint32", "paramName": "taskId", "desc": " 任务ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "progress", "desc": " 当前进度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "need", "desc": " 需要的值", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewards", "desc": " 奖励", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "is<PERSON><PERSON><PERSON>", "desc": " 是否完成", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isReceive", "desc": " 是否已领取", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "languageId", "desc": " 任务多语言ID", "isCustom": false}], "GuildShopDto": [{"rule": "uint32", "type": "uint32", "paramName": "shopId", "desc": " 商店ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "position", "desc": " 位置", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 已购买次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "limit", "desc": " 限购次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "needItemId", "desc": " 购买需要的道具ID(0代表免费)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "needItemCount", "desc": " 购买需要的道具数量", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewards", "desc": " 购买的道具", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "discount", "desc": " 折扣", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "freeCnt", "desc": " 每日最大完全免费次数(不需要看广告)", "isCustom": false}], "GuilSignInDto": [{"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 已签到次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "limit", "desc": " 每日签到上限", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "needItemId", "desc": " 消耗的道具ID(0免费)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "needItemCount", "desc": " 消耗的道具数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "diamonds", "desc": " 消耗的钻石数量(0免费)", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewards", "desc": " 签到奖励", "isCustom": true}], "GuildFeaturesDto": [{"rule": "list", "type": "GuildTaskDto", "paramName": "tasks", "desc": " 任务", "isCustom": true}, {"rule": "list", "type": "GuildShopDto", "paramName": "dailyShop", "desc": " 每日商店", "isCustom": true}, {"rule": "list", "type": "GuildShopDto", "paramName": "weeklyShop", "desc": " 每周商店", "isCustom": true}, {"rule": "GuilSignInDto", "type": "GuilSignInDto", "paramName": "signInDto", "desc": " 每日签到数据", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "dailyRefreshTime", "desc": " 每日数据刷新时间戳", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "weeklyRefreshTime", "desc": " 每周数据刷新时间戳", "isCustom": false}, {"rule": "GuildBossInfoDto", "type": "GuildBossInfoDto", "paramName": "guildBossInfo", "desc": " 公会boss数据", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "taskRefreshCount", "desc": " 任务剩余免费刷新次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxTaskRefreshCount", "desc": " 任务最大免费刷新次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "taskRefreshCost", "desc": " 任务刷新超过免费次数后需要的钻石数量", "isCustom": false}, {"rule": "list", "type": "string", "paramName": "signInRecords", "desc": " 签到记录(json格式包含的字段:userId,nickName,itemId(签到使用的道具ID(0免费)),timestamp})", "isCustom": false}, {"rule": "GuildDonationDto", "type": "GuildDonationDto", "paramName": "donationDto", "desc": " 捐赠数据", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "applyCount", "desc": " 申请数量", "isCustom": false}], "GuildUpdateInfoDto": [{"rule": "uint32", "type": "uint32", "paramName": "active", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "exp", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxMembers", "desc": "", "isCustom": false}], "BeKickedOutDto": [{"rule": "uint64", "type": "uint64", "paramName": "guildId", "desc": " 公会ID(此值=0代表没有消息)", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildName", "desc": " 公会名称", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "fromUserId", "desc": " 发起人用户ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "fromUserNickName", "desc": " 发起人昵称", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "fromUserPosition", "desc": " 发起人职位", "isCustom": false}], "GuildPushMessageDto": [], "uint32Type=1;//消息类型": [{"rule": "uint32", "type": "uint32", "paramName": "messageType", "desc": " 消息类型", "isCustom": false}], "stringContent=2;//消息内容(json不同消息类型可能不同)": [{"rule": "string", "type": "string", "paramName": "messageContent", "desc": " 消息内容(json不同消息类型可能不同)", "isCustom": false}], "GuildBossInfoDto": [{"rule": "uint32", "type": "uint32", "paramName": "challengeCnt", "desc": " 剩余挑战次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "buyCntByDiamonds", "desc": " 今日已购买次数(钻石)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxBuyCntByDiamonds", "desc": " 每日最大可购买次数(钻石)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "buyCntCostByDiamonds", "desc": " 购买挑战次数消耗(钻石)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "buyCntByCoins", "desc": " 今日已购买次数(金币)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxBuyCntByCoins", "desc": " 每日最大可购买次数(金币)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "buyCntCostByCoins", "desc": " 购买挑战次数消耗(金币)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "challengeCntRecoverySeconds", "desc": " 公会boss挑战次数恢复间隔", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "challengeCntRecoveryPerTime", "desc": " 公会boss挑战次数每次恢复几点", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "nextChallengeCntRecoveryTime", "desc": " 下次挑战次数恢复时间(当前次数>=恢复次数上限忽略此值)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxRecoveryCnt", "desc": " 累计最大挑战次数上限", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "totalPersonalDamage", "desc": " 个人总伤害", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "totalGuildDamage", "desc": " 公会总伤害", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "killBossCnt", "desc": " 击杀boss数量", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "bossRefreshTimestamp", "desc": " boss下次刷新时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "dailyRefreshTimestamp", "desc": " 每日数据刷新时间", "isCustom": false}, {"rule": "list", "type": "GuildBossConfigDto", "paramName": "bossConfig", "desc": " boss数据", "isCustom": true}, {"rule": "list", "type": "GuildBossTaskDto", "paramName": "bossTask", "desc": " boss任务数据", "isCustom": true}, {"rule": "list", "type": "GuildBossKillBoxDto", "paramName": "killBox", "desc": " boss击杀宝箱数据", "isCustom": true}, {"rule": "list", "type": "string", "paramName": "challengeRecords", "desc": " 公会成员挑战记录(json格式包含的字段:userId,nickName,damage,timestamp})", "isCustom": false}], "GuildBossConfigDto": [{"rule": "uint32", "type": "uint32", "paramName": "bossId", "desc": " bossId - 配置表ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "maxHp", "desc": " 最大血量", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "nowHp", "desc": " 当前血量(0代表被击杀)", "isCustom": false}], "GuildBossTaskDto": [{"rule": "uint32", "type": "uint32", "paramName": "taskId", "desc": " 任务id-配置表ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "need", "desc": " 需要的进度", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "progress", "desc": " 当前进度", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewards", "desc": " 奖励", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "is<PERSON><PERSON><PERSON>", "desc": " 是否完成", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isReceive", "desc": " 是否已领取", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "languageId", "desc": " 多语言ID", "isCustom": false}], "GuildBossKillBoxDto": [{"rule": "uint32", "type": "uint32", "paramName": "boxId", "desc": " 宝箱ID-配置表ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "need", "desc": " 需要的击杀数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "progress", "desc": " 当前击杀数量", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewards", "desc": " 奖励", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "is<PERSON><PERSON><PERSON>", "desc": " 是否完成", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isReceive", "desc": " 是否已领取", "isCustom": false}], "GuildBossRankDto": [{"rule": "GuildMemberInfoDto", "type": "GuildMemberInfoDto", "paramName": "guildMemberInfo", "desc": " 公会成员数据", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "damage", "desc": " 伤害值(0未参与)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "rank", "desc": " 排名(0未上榜)", "isCustom": false}], "GuildDonationDto": [{"rule": "uint64", "type": "uint64", "paramName": "requestItemTimestamp", "desc": " 下次可请求道具时间戳", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "donationItemCount", "desc": " 本周获得的互助奖励数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "donationMaxItemCount", "desc": " 每周互助奖励最大数量", "isCustom": false}], "HeroUpgradeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 英雄唯一ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 升级次数", "isCustom": false}], "HeroUpgradeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroAdvanceRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 英雄唯一ID", "isCustom": false}], "HeroAdvanceResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroStarRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 英雄唯一ID", "isCustom": false}], "HeroStarResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroResetRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 英雄唯一ID", "isCustom": false}], "HeroResetResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroBookScoreRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 英雄唯一ID", "isCustom": false}], "HeroBookScoreResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroBookRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "rewardId", "desc": " 领奖配置id", "isCustom": false}], "HeroBookRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroReplaceSkinRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "heroRowId", "desc": " 英雄RowId", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "skinConfigId", "desc": " skin配置id", "isCustom": false}], "HeroReplaceSkinResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroBondLevelUpRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "heroBondConfigId", "desc": " 英雄羁绊配表Id", "isCustom": false}, {"rule": "list", "type": "uint64", "paramName": "heroRowIds", "desc": " 英雄羁绊RowIds", "isCustom": false}], "HeroBondLevelUpResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "HeroLosslessRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "fType", "desc": " 阵容类型", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "fIndex", "desc": " 阵容下标", "isCustom": false}, {"rule": "int64", "type": "int64", "paramName": "heroRowId", "desc": " 替换英雄rowId", "isCustom": false}], "HeroLosslessResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "IMLoginRequest": [{"rule": "string", "type": "string", "paramName": "accessToken", "desc": "", "isCustom": false}], "IMLoginResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMJoinGroupRequest": [{"rule": "list", "type": "string", "paramName": "groupId", "desc": " 要加入的分组ID", "isCustom": false}], "IMJoinGroupResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMQuitGroupRequest": [{"rule": "list", "type": "string", "paramName": "groupId", "desc": " 要退出的分组ID", "isCustom": false}], "IMQuitGroupResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMHeartBeatRequest": [], "IMHeartBeatResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMGroupChatRequest": [{"rule": "string", "type": "string", "paramName": "groupId", "desc": " groupId", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "content", "desc": " 聊天内容", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "language", "desc": " 客户端语言", "isCustom": false}], "IMGroupChatResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMPrivateChatRequest": [{"rule": "string", "type": "string", "paramName": "targetId", "desc": " 目标用户ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "content", "desc": " 聊天内容", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "language", "desc": " 客户端语言", "isCustom": false}], "IMPrivateChatResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMGroupMessageRecordRequest": [{"rule": "string", "type": "string", "paramName": "groupId", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "msgSeq", "desc": " 最后一条消息序列号(0代表从最后一条开始获取)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "msgCount", "desc": " 每次拉取消息数量(最大50)", "isCustom": false}], "IMGroupMessageRecordResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "repeatedIMMessageRecordRecord=2;": [{"rule": "list", "type": "IMMessageRecord", "paramName": "messageRecord", "desc": "", "isCustom": true}], "IMPrivateListRequest": [{"rule": "uint32", "type": "uint32", "paramName": "limit", "desc": " 获取数量(最大值100)", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "lastEvaluatedKey", "desc": " response返回的数据， 有这个值代表还有数据没拉取完", "isCustom": false}], "IMPrivateListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "IMPrivateUser", "paramName": "privateUser", "desc": " 私聊用户数据", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "lastEvaluatedKey", "desc": " 有这个值代表还有数据没拉取完, 下次请求时传给服务器", "isCustom": false}], "IMPrivateChatRecordRequest": [{"rule": "string", "type": "string", "paramName": "targetId", "desc": " 目标ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "msgSeq", "desc": " 最后一条消息序列号(0代表从最后一条开始获取)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "msgCount", "desc": " 每次拉取消息数量(最大50)", "isCustom": false}], "IMPrivateChatRecordResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMGetBlackListRequest": [], "IMGetBlackListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "IMBlackUser", "paramName": "blackUser", "desc": "", "isCustom": true}], "IMAddToBlackListRequest": [{"rule": "list", "type": "string", "paramName": "targetId", "desc": "", "isCustom": false}], "IMAddToBlackListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMRemoveFromBlackListRequest": [{"rule": "list", "type": "string", "paramName": "targetId", "desc": "", "isCustom": false}], "IMRemoveFromBlackListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}], "IMChatTextTranslateRequest": [{"rule": "string", "type": "string", "paramName": "groupId", "desc": " 分组ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "privateTargetId", "desc": " 私聊目标ID(与groupId互斥， 两个参数只能传一个)", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "msgSeq", "desc": " 消息序列号", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "targetLanguage", "desc": " 语言", "isCustom": false}], "IMChatTextTranslateResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "translateMsg", "desc": " 翻译后的内容", "isCustom": false}], "IMLoginRepeatMessage": [], "IMReconnectMessage": [], "IMPushMessage": [], "IMErrorMessage": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": " 错误码", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "msg", "desc": " 错误消息", "isCustom": false}], "IMMessageContent{": [{"rule": "string", "type": "string", "paramName": "content", "desc": " 消息内容", "isCustom": false}], "uint32Type=2;//消息类型": [{"rule": "uint32", "type": "uint32", "paramName": "messageType", "desc": " 消息类型", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "chatText", "desc": " 聊天记录", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "customData", "desc": " 自定义数据", "isCustom": false}], "IMMessageRecord{": [], "IMMessageContentContent=1;//内容": [{"rule": "IMMessageContent", "type": "IMMessageContent", "paramName": "messageContent", "desc": " 内容", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "msgSender", "desc": " 发送者", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "msgSeq", "desc": " 消息序列号", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "createTime", "desc": " 消息时间", "isCustom": false}], "IMPrivateUser{": [{"rule": "uint64", "type": "uint64", "paramName": "msgSeq", "desc": " 消息序列号", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "lastMsg", "desc": " 最后一条消息", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "lastMsgTime", "desc": " 最后一条消息时间", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "senderId", "desc": " 发送者", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "targetId", "desc": " 目标", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "userInfo", "desc": " 目标用户信息json(userId, nick<PERSON>ame, avatar, avatarFrame)", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "onlineTime", "desc": " (0代表当前在线， >0 代表离线时间)", "isCustom": false}], "IMBlackUser{": [{"rule": "string", "type": "string", "paramName": "userInfo", "desc": " 用户数据", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "addTimestamp", "desc": " 添加时间", "isCustom": false}], "MessageType{": [{"rule": "DEFAULT", "type": "DEFAULT", "paramName": "=", "desc": "", "isCustom": true}, {"rule": "GUILD_JOIN_SUCCESS", "type": "GUILD_JOIN_SUCCESS", "paramName": "=", "desc": " 自己成功加入公会(提交审批的)", "isCustom": true}, {"rule": "GUILD_USER_JOIN", "type": "GUILD_USER_JOIN", "paramName": "=", "desc": " 有人加入公会(处理公会人数)", "isCustom": true}, {"rule": "GUILD_USER_LEAVE", "type": "GUILD_USER_LEAVE", "paramName": "=", "desc": " 有人退出公会(处理公会人数)", "isCustom": true}, {"rule": "GUILD_POSITION_CHANGE", "type": "GUILD_POSITION_CHANGE", "paramName": "=", "desc": " 自己职位变动", "isCustom": true}, {"rule": "GUILD_BE_KICKED_OUT", "type": "GUILD_BE_KICKED_OUT", "paramName": "=", "desc": " 自己被踢出公会", "isCustom": true}, {"rule": "GUILD_USER_BE_KICKED_OUT", "type": "GUILD_USER_BE_KICKED_OUT", "paramName": "=", "desc": " 有人被踢出(处理公会人数)", "isCustom": true}, {"rule": "GUILD_INFO_MODIFY", "type": "GUILD_INFO_MODIFY", "paramName": "=", "desc": " 公会公告, 公会名称, 公会图标修改", "isCustom": true}, {"rule": "GUILD_DONATION", "type": "GUILD_DONATION", "paramName": "=", "desc": " 捐赠", "isCustom": true}, {"rule": "GUILD_DONATION_CHANGE_ITEM_COUNT", "type": "GUILD_DONATION_CHANGE_ITEM_COUNT", "paramName": "=", "desc": " 捐赠收到的道具数量变化", "isCustom": true}, {"rule": "GUILD_DONATION_DELETE", "type": "GUILD_DONATION_DELETE", "paramName": "=", "desc": " 捐赠移除数据", "isCustom": true}, {"rule": "GUILD_APPLY_JOIN", "type": "GUILD_APPLY_JOIN", "paramName": "=", "desc": " 有人申请公会推送", "isCustom": true}, {"rule": "GUILD_TRANSFER_PRESIDENT", "type": "GUILD_TRANSFER_PRESIDENT", "paramName": "=", "desc": " 转让会长", "isCustom": true}, {"rule": "GUILD_CHECK_TRANSFER_PRESIDENT", "type": "GUILD_CHECK_TRANSFER_PRESIDENT", "paramName": "=", "desc": " 会长长时间未登录自动转让", "isCustom": true}, {"rule": "CHAT_GUILD", "type": "CHAT_GUILD", "paramName": "=", "desc": " 公会聊天", "isCustom": true}, {"rule": "CHAT_WORLD_SERVER", "type": "CHAT_WORLD_SERVER", "paramName": "=", "desc": " 本服聊天", "isCustom": true}, {"rule": "CHAT_PRIVATE", "type": "CHAT_PRIVATE", "paramName": "=", "desc": " 私聊", "isCustom": true}, {"rule": "CHAT_CROSS_SERVER", "type": "CHAT_CROSS_SERVER", "paramName": "=", "desc": " 跨服聊天", "isCustom": true}, {"rule": "CHAT_GLOBAL", "type": "CHAT_GLOBAL", "paramName": "=", "desc": " 全服聊天", "isCustom": true}, {"rule": "CHAT_SHOW_ITEM", "type": "CHAT_SHOW_ITEM", "paramName": "=", "desc": " 聊天展示物品", "isCustom": true}, {"rule": "SYSTEM_PUSH", "type": "SYSTEM_PUSH", "paramName": "=", "desc": "", "isCustom": true}], "ItemUseRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "rowId", "desc": " 道具rowID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "count", "desc": " 使用数量", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "index", "desc": " 道具类型 = 5 自选礼包时 选择的道具索引", "isCustom": false}], "ItemUseResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "MissionGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "MissionGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "MainMission", "type": "MainMission", "paramName": "mainMission", "desc": "", "isCustom": true}], "MissionStartRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "MissionStartDto", "type": "MissionStartDto", "paramName": "startDto", "desc": "", "isCustom": true}], "MissionStartResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "MissionEndRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "MissionEndDto", "type": "MissionEndDto", "paramName": "endDto", "desc": "", "isCustom": true}], "MissionEndResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "MainMission", "type": "MainMission", "paramName": "mainMission", "desc": "", "isCustom": true}], "MissionGetHangUpItemsRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "MissionGetHangUpItemsResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "reward", "desc": " 奖励", "isCustom": true}], "MissionReceiveHangUpItemsRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "MissionReceiveHangUpItemsResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "MissionQuickHangUpRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "MissionQuickHangUpResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "MissionStartDto": [{"rule": "MissionType", "type": "MissionType", "paramName": "missionType", "desc": " 关卡类型", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "missionId", "desc": " 关卡ID", "isCustom": false}], "MissionEndDto": [{"rule": "MissionType", "type": "MissionType", "paramName": "missionType", "desc": " 关卡类型", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "missionId", "desc": " 关卡ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "startTransId", "desc": " 开始战斗时传的transId", "isCustom": false}, {"rule": "bool", "type": "bool", "paramName": "isWin", "desc": " 是否胜利", "isCustom": false}, {"rule": "list", "type": "Proto.Common.Reward<PERSON>to", "paramName": "rewards", "desc": " 战斗结算奖励", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "extar", "desc": " 额外参数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "damage", "desc": " 伤害值", "isCustom": false}], "MainMission": [{"rule": "uint32", "type": "uint32", "paramName": "missionId", "desc": " 最远未完成的关卡ID，会超出上限", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "wave", "desc": " 波次", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "claimedRewardId", "desc": " 已领取的章节奖励ID，不超上限", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "enterCount", "desc": " 进入章节次数", "isCustom": false}], "MissionType{": [{"rule": "MAIN", "type": "MAIN", "paramName": "=", "desc": " 主线章节", "isCustom": true}], "PayInAppPurchaseRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "channelId", "desc": " 渠道id", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "purchaseId", "desc": " IAP->Purchase表ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "receiptData", "desc": " 支付收据信息，用户服务端验证", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extraInfo", "desc": " 附加参数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "preOrderId", "desc": " 预下单ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "currency", "desc": " 内购支付货币类型", "isCustom": false}], "PayInAppPurchaseResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": " 通用返回数据", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "IAPTransID", "desc": " 验证后返回的交易ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "rechargeId", "desc": " 购买的钻石商品配置表ID", "isCustom": false}, {"rule": "map", "type": "map<uint32,uint32>", "paramName": "rechargeIds", "desc": " 购买数量", "isCustom": true}, {"rule": "Proto.Common.IAPDto", "type": "Proto.Common.IAPDto", "paramName": "iapInfo", "desc": " 内购信息", "isCustom": true}], "PayPreOrderRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "purchaseId", "desc": " IAP->Purchase表ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extraInfo", "desc": " 附加参数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "preOrderId", "desc": " 预下单ID (时间戳单位毫秒)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "channelId", "desc": " 下单渠道ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "androidPackageName", "desc": " 安卓包名", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "kuaiShouChannelID", "desc": " 快手下单渠道ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "yybType", "desc": " 应用宝登录类型(1 QQ, 2 WX)", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "yybOpenId", "desc": " openid", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "yyb<PERSON><PERSON><PERSON>", "desc": " payToken", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "yybPf", "desc": " pf", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "yybPfKey", "desc": " pfkey", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "douyinRiskControlInfo", "desc": " 抖音RiskControlInfo", "isCustom": false}], "PayPreOrderResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "preOrderId", "desc": " 预下单ID, 通知发货时需要回传", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "notifyUrl", "desc": " 支付回调地址", "isCustom": false}, {"rule": "WeChatOrderDto", "type": "WeChatOrderDto", "paramName": "weChatOrderDto", "desc": " 微信下单返回信息", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "aliOrderData", "desc": " 支付宝下单返回信息", "isCustom": false}, {"rule": "UcOrderDto", "type": "UcOrderDto", "paramName": "ucOrderDto", "desc": " uc下单返回值", "isCustom": true}, {"rule": "BiliOrderDto", "type": "BiliOrderDto", "paramName": "biliOrderDto", "desc": " bilibili下单返回值", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "passBackParams", "desc": " 支付回调透传参数", "isCustom": false}, {"rule": "KuaiShouOrderDto", "type": "KuaiShouOrderDto", "paramName": "kusiShouOrderDto", "desc": " 快手下单返回数据", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "cpOrderId", "desc": " cp订单号", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "amount", "desc": " 订单价格单位分", "isCustom": false}, {"rule": "VivoOrderDto", "type": "VivoOrderDto", "paramName": "vivoOrderDto", "desc": " vivo 订单数据", "isCustom": true}, {"rule": "YybOrderDto", "type": "YybOrderDto", "paramName": "yybOrderDto", "desc": " 应用宝下单数据", "isCustom": true}, {"rule": "DouYinOrderDto", "type": "DouYinOrderDto", "paramName": "douYinOrderDto", "desc": " 抖音下单数据", "isCustom": true}, {"rule": "WeChatMiniGameOrderDto", "type": "WeChatMiniGameOrderDto", "paramName": "weChatMiniGameOrderDto", "desc": " 微信小游戏下单", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "PayOnUnityRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "purchaseId", "desc": " IAP->Purchase表ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extraInfo", "desc": " 附加参数", "isCustom": false}], "PayOnUnityResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "PayInAppPurchaseResponse", "type": "PayInAppPurchaseResponse", "paramName": "payInAppPurchaseResponse", "desc": "", "isCustom": true}], "WeChatOrderDto": [{"rule": "string", "type": "string", "paramName": "prePayId", "desc": " 微信返回的支付交易会话ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "nonceStr", "desc": " 随机字符串", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "timestamp", "desc": " 时间戳", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "sign", "desc": " 签名", "isCustom": false}], "UcOrderDto": [{"rule": "string", "type": "string", "paramName": "callbackInfo", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "amount", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "notifyUrl", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "cpOrderId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "accountId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "signType", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "sign", "desc": "", "isCustom": false}], "BiliOrderDto": [{"rule": "string", "type": "string", "paramName": "uid", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "role", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "notifyUrl", "desc": " 回调地址", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "outTradeNo", "desc": " cp订单号", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "totalFee", "desc": " 支付金额 单位:分", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extensionInfo", "desc": " 透传参数", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "orderSign", "desc": " 订单签名", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "gameMoney", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "subject", "desc": " 商品名称", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "body", "desc": " 商品描述", "isCustom": false}], "KuaiShouOrderDto": [{"rule": "string", "type": "string", "paramName": "channelId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "userIp", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "appId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "productId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "productName", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "productDesc", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "productNum", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "price", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "serverId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "serverName", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "roleId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "roleLevel", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "orderId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "payNotifyUrl", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extension", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "coinName", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "sign", "desc": "", "isCustom": false}], "VivoOrderDto": [{"rule": "string", "type": "string", "paramName": "appId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "cpOrderNumber", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "productName", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "productDesc", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "orderAmount", "desc": " 单位为分", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "vivoSignature", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extuid", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "notifyUrl", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "expireTime", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "level", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "vip", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "balance", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "party", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "roleId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "serverName", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extInfo", "desc": "", "isCustom": false}], "YybOrderDto": [{"rule": "string", "type": "string", "paramName": "zoneId", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "goodsTokenUrl", "desc": "", "isCustom": false}], "DouYinOrderDto": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "sdkParam", "desc": "", "isCustom": false}], "string=3;": [{"rule": "string", "type": "string", "paramName": "message", "desc": "", "isCustom": false}], "WeChatMiniGameOrderDto{": [{"rule": "string", "type": "string", "paramName": "signData", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "paySig", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "signature", "desc": "", "isCustom": false}], "PowerOnOpenRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "PowerOnOpenResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "PowerDto", "type": "PowerDto", "paramName": "powerDto", "desc": "", "isCustom": true}], "PowerRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "configId", "desc": " 配置id", "isCustom": false}], "PowerRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "PowerDto": [{"rule": "int32", "type": "int32", "paramName": "activityId", "desc": " 活动id", "isCustom": false}, {"rule": "list", "type": "int32", "paramName": "rewardId", "desc": " 已领奖配置id", "isCustom": false}], "UserGetLastLoginRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "UserGetLastLoginResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "RoleDetailDto", "paramName": "roleList=2;", "desc": "角色列表", "isCustom": true}, {"rule": "map", "type": "map<uint32,ZoneInfoDto>", "paramName": "serverList", "desc": "zoneId->ZoneInfoDto", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "FindServerListRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "groupId", "desc": "", "isCustom": false}], "FindServerListResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "ZoneInfoDto", "type": "ZoneInfoDto", "paramName": "serverInfoDto=2;", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "RoleDetailDto": [{"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "groupId", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "serverId", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "power", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "距离最近一次登录的时间差", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": "", "isCustom": false}], "ZoneInfoDto": [{"rule": "uint32", "type": "uint32", "paramName": "maxServer", "desc": "当前大区已开放最大服务器", "isCustom": false}, {"rule": "map", "type": "map<uint32,ServerGroupDto>", "paramName": "serverList", "desc": "group->ServerInfoDto", "isCustom": true}], "ServerGroupDto": [{"rule": "uint32", "type": "uint32", "paramName": "group", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "startServer", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "endServer", "desc": "", "isCustom": false}, {"rule": "list", "type": "ServerInfoDto", "paramName": "serverInfoDto=4;", "desc": "", "isCustom": true}], "ServerInfoDto": [{"rule": "uint32", "type": "uint32", "paramName": "serverId", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "status", "desc": "0空状态1新2爆满", "isCustom": false}], "SevenDayTaskGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "SevenDayTaskGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "SevenDayDto", "type": "SevenDayDto", "paramName": "sevenDayDto", "desc": " 活动数据", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "SevenDayTaskRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "taskId", "desc": " 任务ID", "isCustom": false}], "SevenDayTaskRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": " 通用返回", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "active", "desc": " 更新的活跃度", "isCustom": false}, {"rule": "list", "type": "Proto.Common.SevenDayTaskDto", "paramName": "updateTaskDto", "desc": " 更新的活动数据, 比如领取任务奖励会触发其他任务状态", "isCustom": true}], "SevenDayTaskActiveRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "configId", "desc": " 配置表ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "selectIdx", "desc": " 自选礼包选择的道具索引", "isCustom": false}], "SevenDayTaskActiveRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": " 通用返回", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "activeLog", "desc": " 活跃度奖励领取记录", "isCustom": false}, {"rule": "list", "type": "Proto.Common.SevenDayTaskDto", "paramName": "updateTaskDto", "desc": " 更新的活动数据, 比如领取任务奖励会触发其他任务状态", "isCustom": true}], "SevenDayDto": [{"rule": "uint32", "type": "uint32", "paramName": "days", "desc": " 开启了第几天的任务", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "taskEndTimestamp", "desc": " 任务结束时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "endTimestamp", "desc": " 活动结束时间(任务+领取奖励)", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "activeLog", "desc": " 活跃度奖励领取记录", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "active", "desc": " 当前活跃度", "isCustom": false}, {"rule": "list", "type": "Proto.Common.SevenDayTaskDto", "paramName": "tasks", "desc": " 任务数据", "isCustom": true}], "ShopGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "ShopGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "IntegralShopDto", "paramName": "integralShops", "desc": " 积分商店", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "refreshTime", "desc": " 跨天刷新时间戳", "isCustom": false}, {"rule": "map", "type": "map<uint32,uint32>", "paramName": "rechargeIds", "desc": " 已购买的钻石商品配置表ID", "isCustom": true}, {"rule": "list", "type": "ShopGachaDataDto", "paramName": "shopGachaDataDtos", "desc": " 抽卡卡池数据", "isCustom": true}, {"rule": "map", "type": "map<uint32,Proto.Common.Uint32List>", "paramName": "wishData", "desc": " 许愿数据", "isCustom": true}, {"rule": "Proto.Common.IAPDto", "type": "Proto.Common.IAPDto", "paramName": "iapInfo", "desc": " 内购信息", "isCustom": true}], "ShopDoGachaRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "gachaId", "desc": " 卡池ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "costType", "desc": " 消耗类型 0:货币, 1:抽卡卷", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "gachaType", "desc": " 类型 0:单抽, 1:十连抽", "isCustom": false}], "ShopDoGachaResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "gacha<PERSON><PERSON>nt", "desc": " 对应卡池的今日抽卡次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "gacha<PERSON>ree<PERSON>ount", "desc": " 对应卡池的今日免费抽卡次数", "isCustom": false}], "ShopIntegralGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "ShopIntegralGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "list", "type": "IntegralShopDto", "paramName": "integralShops", "desc": " 商店列表", "isCustom": true}], "ShopIntegralRefreshRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "shopConfigId", "desc": " 商店配置ID", "isCustom": false}], "ShopIntegralRefreshResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "IntegralShopDto", "type": "IntegralShopDto", "paramName": "integralShop", "desc": " 商店数据", "isCustom": true}], "ShopIntegralBuyItemRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "shopConfigId", "desc": " 商店配置ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "goodsConfigId", "desc": " 商品配置ID", "isCustom": false}], "ShopIntegralBuyItemResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "shopConfigId", "desc": " 商店配置ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "goodsConfigId", "desc": " 商品配置ID", "isCustom": false}], "ShopGacheWishRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "gachaId", "desc": " 卡池ID", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "heroIds", "desc": " 许愿的英雄列表", "isCustom": false}], "ShopGacheWishResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "map", "type": "map<uint32,Proto.Common.Uint32List>", "paramName": "wishData", "desc": " 许愿数据", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "ShopFreeIAPItemRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "purchaseId", "desc": " IAP->Purchase表ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "extraInfo", "desc": " 附加参数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "extraType", "desc": " 0:正常 1:活动", "isCustom": false}], "ShopFreeIAPItemResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "map", "type": "map<uint32,uint32>", "paramName": "rechargeIds", "desc": " 已购买的钻石商品配置表ID", "isCustom": true}, {"rule": "Proto.Common.IAPDto", "type": "Proto.Common.IAPDto", "paramName": "iapInfo", "desc": " 内购信息", "isCustom": true}], "BattlePassGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "BattlePassGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.IAPBattlePassDto", "type": "Proto.Common.IAPBattlePassDto", "paramName": "battlePass", "desc": "", "isCustom": true}], "BattlePassRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "battlePassRewardIdList", "desc": "", "isCustom": false}], "BattlePassRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.IAPBattlePassDto", "type": "Proto.Common.IAPBattlePassDto", "paramName": "battlePassDto", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "BattlePassChangeScoreRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "addScore", "desc": "", "isCustom": false}], "BattlePassChangeScoreResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.IAPBattlePassDto", "type": "Proto.Common.IAPBattlePassDto", "paramName": "battlePassDto", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "BattlePassFinalRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "BattlePassFinalRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.IAPBattlePassDto", "type": "Proto.Common.IAPBattlePassDto", "paramName": "battlePassDto", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "MonthCardGetRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "monthCardId", "desc": " 月卡ID", "isCustom": false}], "MonthCardGetRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.IAPDto", "type": "Proto.Common.IAPDto", "paramName": "iapInfo", "desc": " 内购信息", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "VIPLevelRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "level", "desc": " vip等级", "isCustom": false}], "VIPLevelRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "LevelFundGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "LevelFundGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.LevelFundDto", "type": "Proto.Common.LevelFundDto", "paramName": "levelFund", "desc": "", "isCustom": true}], "LevelFundRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "levelFundRewardId", "desc": "", "isCustom": false}], "LevelFundRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.LevelFundDto", "type": "Proto.Common.LevelFundDto", "paramName": "levelFund", "desc": "", "isCustom": true}], "FirstRechargeRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "FirstRechargeRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "bool", "type": "bool", "paramName": "firstRechargeReward", "desc": " 是否已领取首冲奖励", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "totalRecharge", "desc": " 累充金额", "isCustom": false}], "ShopGachaDataDto": [{"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 卡池ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "openType", "desc": " 开启类型", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "openTimestamp", "desc": " 开启时间戳", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "endTimestamp", "desc": " 结束时间戳", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "gacha<PERSON><PERSON>nt", "desc": " 今日抽卡次数", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "gacha<PERSON>ree<PERSON>ount", "desc": " 今日免费抽卡次数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "refreshTimestamp", "desc": " 抽卡刷新时间戳", "isCustom": false}], "IntegralShopItemDto": [{"rule": "uint32", "type": "uint32", "paramName": "configId", "desc": " 配置表ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型", "isCustom": false}, {"rule": "Proto.Common.Reward<PERSON>to", "type": "Proto.Common.Reward<PERSON>to", "paramName": "reward", "desc": " 道具配置", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "priceType", "desc": " 价格类型(item表ID)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "price", "desc": " 价格", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "discount", "desc": " 折扣(百分比)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "sort", "desc": " 排序", "isCustom": false}], "IntegralShopDto": [{"rule": "uint32", "type": "uint32", "paramName": "shopConfigId", "desc": "", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "goodsConfigId", "desc": "", "isCustom": false}, {"rule": "list", "type": "uint32", "paramName": "buyConfigId", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "round", "desc": " 刷新次数", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "resetTimestamp", "desc": " 数据重置时间", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "maxRound", "desc": " 最大刷新次数", "isCustom": false}], "SignInGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "SignInGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "SignInData", "type": "SignInData", "paramName": "signInData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "SignInDoSignRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "SignInDoSignResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "SignInData", "type": "SignInData", "paramName": "signInData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "SignInData": [{"rule": "bool", "type": "bool", "paramName": "isCanSignIn", "desc": " 是否可以签到", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "timestamp", "desc": " 下次签到时间戳(isCanSignIn == true 时 此时间戳返回的是0)", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "log", "desc": " 签到记录 (1 - 7), 一天都没签到过 = 0", "isCustom": false}, {"rule": "list", "type": "Proto.Common.RewardDtoListDto", "paramName": "rewardDtoList", "desc": " 签到奖励二维列表", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "configId", "desc": " 配置表ID", "isCustom": false}], "TaskGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "TaskGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Tasks", "type": "Tasks", "paramName": "tasks", "desc": " 任务列表", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "dailyTaskActive", "desc": " 日常活跃度", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "weeklyTaskActive", "desc": " 周常活跃度", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "dailyTaskResetTime", "desc": " 日常任务刷新时间戳", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "dailyTaskRewardLog", "desc": " 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "weeklyTaskRewardLog", "desc": " 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始", "isCustom": false}], "TaskRewardDailyRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 任务列表返回的ID", "isCustom": false}], "TaskRewardDailyResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "activeDaily", "desc": " 活跃度-日", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "activeWeekly", "desc": " 活跃度-周", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.TaskDto", "type": "Proto.Common.TaskDto", "paramName": "updateTaskDto", "desc": " 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增", "isCustom": true}, {"rule": "Tasks", "type": "Tasks", "paramName": "tasks", "desc": " 领取任务奖励触发其他任务状态变化", "isCustom": true}], "TaskRewardAchieveRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 任务列表返回的ID", "isCustom": false}], "TaskRewardAchieveResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.TaskDto", "type": "Proto.Common.TaskDto", "paramName": "updateTaskDto", "desc": " 更新的任务数据,根据id判断， 如果之前存在ID则更新，不存在则是新增", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "deleteTaskDtoId", "desc": " 删除的任务ID", "isCustom": false}, {"rule": "Tasks", "type": "Tasks", "paramName": "tasks", "desc": " 领取任务奖励触发其他任务状态变化", "isCustom": true}], "TaskActiveRewardRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1每日，2每周", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "id", "desc": " 对应的活跃度配置表ID", "isCustom": false}], "TaskActiveRewardResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1每日，2每周", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "rewardLog", "desc": " 活跃度奖励领取记录", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Tasks", "type": "Tasks", "paramName": "tasks", "desc": " 领取任务奖励触发其他任务状态变化", "isCustom": true}], "TaskActiveRewardAllRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1每日，2每周", "isCustom": false}], "TaskActiveRewardAllResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "type", "desc": " 类型 1每日，2每周", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Tasks", "type": "Tasks", "paramName": "tasks", "desc": " 领取任务奖励触发其他任务状态变化", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "rewardLog", "desc": " 活跃度奖励领取记录", "isCustom": false}], "Tasks": [{"rule": "list", "type": "Proto.Common.TaskDto", "paramName": "dailyTask", "desc": " 日常", "isCustom": true}, {"rule": "list", "type": "Proto.Common.TaskDto", "paramName": "achievements", "desc": " 成就", "isCustom": true}], "UserLoginRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "channelId", "desc": " 渠道编号 (0 unity, 1 ios, 2 google)", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "accountId2", "desc": " ios teamPlayerID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "verification", "desc": " 账号验证信息", "isCustom": false}], "UserLoginResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "accessToken", "desc": " 登录令牌", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "timestamp", "desc": " 服务器当前时间戳", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": " 用户ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "transId", "desc": " 接口请求序列号", "isCustom": false}, {"rule": "Proto.Common.UserCurrency", "type": "Proto.Common.UserCurrency", "paramName": "userCurrency", "desc": " 货币 金币 & 钻石", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "systemMask", "desc": " 系统掩码(客户端转换成bigint使用)", "isCustom": false}, {"rule": "Proto.Common.UserLevel", "type": "Proto.Common.UserLevel", "paramName": "userLevel", "desc": " 玩家等级", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "guideMask", "desc": " 新手引导步骤", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "registerTimestamp", "desc": " 注册时间", "isCustom": false}, {"rule": "list", "type": "Proto.Common.ItemDto", "paramName": "items", "desc": " 道具列表", "isCustom": true}, {"rule": "list", "type": "Proto.Common.EquipmentDto", "paramName": "equipments", "desc": " 装备列表", "isCustom": true}, {"rule": "Proto.Common.UserInfoDto", "type": "Proto.Common.UserInfoDto", "paramName": "userInfoDto", "desc": " 用户信息", "isCustom": true}, {"rule": "list", "type": "Proto.Common.HeroDto", "paramName": "heros", "desc": " 英雄列表", "isCustom": true}, {"rule": "Proto.Common.UserVipLevel", "type": "Proto.Common.UserVipLevel", "paramName": "userVipLevel", "desc": " 玩家VIP等级", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "openModelIdList", "desc": " 已经开启的模块列表", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "chapterRecordInfo", "desc": " 章节记录信息", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "timeZone", "desc": " 服务器当前时区", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "serverId", "desc": " 服务器ID", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "openServerTime", "desc": " 开服时间", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "openServerResetTime", "desc": " 开服时间00:00:00", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "ServerIMGroupId", "desc": " IM-本服ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildServerIMGroupId", "desc": " IM-工会分组ID", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "crossServerIMGroupId", "desc": " IM-跨服分组ID(空代表没有)", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "globalIMGroupId", "desc": " IM-全服分组ID(空代表没有)", "isCustom": false}, {"rule": "Proto.Mission.MainMission", "type": "Proto.Mission.MainMission", "paramName": "mainMission", "desc": " 主线章节数据", "isCustom": true}, {"rule": "list", "type": "Proto.Common.AdInfoDto", "paramName": "adInfo", "desc": " 广告相关数据", "isCustom": true}], "UserGetInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "UserGetInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.UserCurrency", "type": "Proto.Common.UserCurrency", "paramName": "userCurrency", "desc": " 货币 金币 & 钻石", "isCustom": true}, {"rule": "Proto.Common.UserLevel", "type": "Proto.Common.UserLevel", "paramName": "userLevel", "desc": " 玩家等级", "isCustom": true}, {"rule": "Proto.Mission.MainMission", "type": "Proto.Mission.MainMission", "paramName": "mainMission", "desc": " 主线关卡数据", "isCustom": true}, {"rule": "list", "type": "Proto.Common.ItemDto", "paramName": "items", "desc": " 道具列表", "isCustom": true}, {"rule": "list", "type": "Proto.Common.EquipmentDto", "paramName": "equipments", "desc": " 装备列表", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "transId", "desc": " 接口请求序列号", "isCustom": false}], "UserHeartbeatRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}], "UserHeartbeatResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "serverTimestamp", "desc": "", "isCustom": false}], "UserUpdateSystemMaskRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "position", "desc": " 第几位", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "value", "desc": " 值 只能是 0 or 1", "isCustom": false}], "UserUpdateSystemMaskResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "systemMask", "desc": " 系统掩码", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "UserUpdateGuideMaskRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "guideMask", "desc": "", "isCustom": false}], "UserUpdateGuideMaskResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "guideMask", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "UserCancelAccountRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "token", "desc": "", "isCustom": false}], "UserCancelAccountResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "UserUpdateInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": " 昵称", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar", "desc": " 头像ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": " 头像框ID", "isCustom": false}], "UserUpdateInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}, {"rule": "Proto.Common.UserInfoDto", "type": "Proto.Common.UserInfoDto", "paramName": "userInfoDto", "desc": " 用户信息", "isCustom": true}], "UserGetOtherPlayerInfoRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint64", "paramName": "otherUserIds", "desc": " 其他玩家用户ID列表", "isCustom": false}], "UserGetOtherPlayerInfoResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "list", "type": "PlayerInfoDto", "paramName": "playerInfos", "desc": " 玩家信息", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "UserGetBattleReportRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint64", "type": "uint64", "paramName": "reportId", "desc": " 战报rowId", "isCustom": false}], "UserGetBattleReportResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.PVPRecordDto", "type": "Proto.Common.PVPRecordDto", "paramName": "record", "desc": " 战报", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "PlayerInfoDto": [{"rule": "string", "type": "string", "paramName": "nick<PERSON><PERSON>", "desc": " 昵称", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "userId", "desc": " 用户ID", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar", "desc": " 头像", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "avatar<PERSON><PERSON><PERSON>", "desc": " 头像框", "isCustom": false}, {"rule": "uint64", "type": "uint64", "paramName": "lastLoginTimestamp", "desc": " 最后登录时间戳", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildName", "desc": " 公会名称", "isCustom": false}, {"rule": "string", "type": "string", "paramName": "guildId", "desc": " 公会ID", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "chapterId", "desc": " 章节数", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "waveIndex", "desc": " 波数", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "power", "desc": " 战力", "isCustom": false}, {"rule": "", "type": "", "desc": "repeated Proto.Common.EquipmentDto equipments = 11;           ", "isCustom": true}, {"rule": "", "type": "", "desc": "Proto.Common.HeroDto hero = 12;                               ", "isCustom": true}, {"rule": "", "type": "", "desc": "repeated Proto.Common.HeroDto formationHero = 14;            ", "isCustom": true}, {"rule": "int32", "type": "int32", "paramName": "guildIcon", "desc": "", "isCustom": false}, {"rule": "int32", "type": "int32", "paramName": "guildIconBg", "desc": "", "isCustom": false}, {"rule": "", "type": "", "desc": "repeated uint64 formationRowIds = 17;                         ", "isCustom": true}], "UserOpenModelRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "list", "type": "uint32", "paramName": "modelIds", "desc": "", "isCustom": false}], "UserOpenModelResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "UserSetFormationByTypeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "formationType", "desc": "", "isCustom": false}, {"rule": "Proto.Common.LongArray", "type": "Proto.Common.LongArray", "paramName": "formation", "desc": "", "isCustom": true}], "UserSetFormationByTypeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}], "UserGetFormationByTypeRequest": [{"rule": "Proto.Common.CommonParams", "type": "Proto.Common.CommonParams", "paramName": "commonParams", "desc": "", "isCustom": true}, {"rule": "uint32", "type": "uint32", "paramName": "formationType", "desc": "", "isCustom": false}], "UserGetFormationByTypeResponse": [{"rule": "int32", "type": "int32", "paramName": "code", "desc": "", "isCustom": false}, {"rule": "uint32", "type": "uint32", "paramName": "formationType", "desc": "", "isCustom": false}, {"rule": "Proto.Common.LongArray", "type": "Proto.Common.LongArray", "paramName": "formation", "desc": "", "isCustom": true}, {"rule": "Proto.Common.CommonData", "type": "Proto.Common.CommonData", "paramName": "commonData", "desc": "", "isCustom": true}]}
package com.dxx.game.modules.pay.handler;

import com.dxx.game.dto.PayProto;
import org.springframework.beans.factory.annotation.Autowired;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.PayProto.*;
import com.dxx.game.consts.MsgReqCommand;
import com.google.protobuf.Message;
import com.dxx.game.modules.pay.service.PayService;

@ApiHandler
public class PayHandler {

    @Autowired
    private PayService payService;

    @ApiMethod(command = MsgReqCommand.PayPreOrderRequest, name = "内购-预下单", collectIp = true)
    public Result<PayProto.PayPreOrderResponse> preOrder(Message msg) {
        PayProto.PayPreOrderRequest params = (PayProto.PayPreOrderRequest) msg;
        return payService.preOrderAction(params);
    }

    @ApiMethod(command = MsgReqCommand.PayInAppPurchaseRequest, name = "内购", skipLoginStateCheck = true)
    public Result<PayInAppPurchaseResponse> inAppPurchase(Message msg) {
        PayInAppPurchaseRequest params = (PayInAppPurchaseRequest) msg;
        return payService.inAppPurchase(params);
    }

    @ApiMethod(command = MsgReqCommand.PayOnUnityRequest, name = "内购-unity内测试")
    public Result<PayOnUnityResponse> onUnity(Message msg) {
        PayOnUnityRequest params = (PayOnUnityRequest) msg;
        return payService.payOnUnity(params);
    }

}
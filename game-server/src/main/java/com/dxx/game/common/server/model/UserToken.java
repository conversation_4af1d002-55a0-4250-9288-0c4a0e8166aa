package com.dxx.game.common.server.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @authoer: lsc
 * @createDate: 2022/7/31
 * @description:
 */
@Getter
@Setter
@ToString
public class UserToken {
    private int serverId;               // 服务器ID
    private String serverIMGroupId;        // 服务器IM群ID
    private long userId;                // 用户ID
    private long loginTM;               // 登录时间戳
    private String accountId;           // 账号ID
    private String accountKey;
    private String loginDeviceId;       // 设备ID
    private int channelId;              // 渠道ID
    private long registerTime;          // 注册时间
    private String packageId;           // 分包ID
    private long serverOpenTime;        // 开服时间
}

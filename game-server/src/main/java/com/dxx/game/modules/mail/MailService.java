package com.dxx.game.modules.mail;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/10 17:44
 */
public interface MailService {

    boolean createMail(long userId, String mailTempLateId, Map<String, String> params, List<List<Integer>> rewards);

    boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards);

    boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards, String source);

    boolean createMail(long userId, String mailTemplateId, Object uniqueId, Map<String, String> params, List<List<Integer>> rewards);

    boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards, String source, int activityId);
    boolean createMail(long userId, String mailTemplateId, Object uniqueId, Map<String, String> params, List<List<Integer>> rewards, String source, int activityId);
    boolean createMailAsync(long userId, String mailTempLateId, Map<String, String> params, List<List<Integer>> rewards);

    boolean createMailAsync(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards);

    boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards,
                       Map<String, String> params, boolean async, String source, int activityId);

    long getUniqueId();
}

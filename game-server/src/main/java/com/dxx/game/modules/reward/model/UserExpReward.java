package com.dxx.game.modules.reward.model;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * 用户经验奖励
 * <AUTHOR>
 * @date 2020-04-24 15:55
 */
public class UserExpReward {

    /**
     * 当前等级
     */
    private int currentLevel = 0;

    /**
     * 当前经验
     */
    private int currentExp = 0;

    public static UserExpReward valueOf(int currentLevel, int currExp) {
        UserExpReward userExpReward = new UserExpReward();
        userExpReward.setCurrentLevel(currentLevel);
        userExpReward.setCurrentExp(currExp);
        return userExpReward;
    }

    public int getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(int currentLevel) {
        this.currentLevel = currentLevel;
    }

    public int getCurrentExp() {
        return currentExp;
    }

    public void setCurrentExp(int currentExp) {
        this.currentExp = currentExp;
    }
}

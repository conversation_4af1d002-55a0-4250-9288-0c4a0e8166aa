package com.dxx.game.common.aws.dynamodb.transaction;

import io.netty.util.concurrent.FastThreadLocal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * @author: lsc
 * @createDate: 2023/12/1
 * @description:
 */
public class DynamoDBTransactionSyncManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(DynamoDBTransactionSyncManager.class);

    private static final FastThreadLocal<Set<DynamoDBTransactionCallback>> transactionCallbacks = new FastThreadLocal<>();

    /**
     * 注册对象
     * @param synchronization
     */
    public static void registerTransactionCallback(DynamoDBTransactionCallback synchronization) {
        if (transactionCallbacks.get() == null) {
            transactionCallbacks.set(new LinkedHashSet<>());
        }
        transactionCallbacks.get().add(synchronization);
    }

    public static void doAfterCommit() {
        if (transactionCallbacks.get() != null) {
            for (DynamoDBTransactionCallback callback : transactionCallbacks.get()) {
                try {
                    callback.afterCommit();
                } catch (Exception e) {
                    LOGGER.error("afterCommitError, e:", e);
                }
            }
            clear();
        }
    }

    public static void clear() {
        transactionCallbacks.remove();
    }
}

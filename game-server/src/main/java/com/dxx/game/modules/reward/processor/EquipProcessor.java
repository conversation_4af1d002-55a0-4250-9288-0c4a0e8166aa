package com.dxx.game.modules.reward.processor;


import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.equip.EquipEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.repository.EquipDao;
import com.dxx.game.dao.dynamodb.repository.HeroDao;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.EquipReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 装备处理器
 * <AUTHOR>
 * @date 2019-12-17 17:27
 */
@Component
public class EquipProcessor implements RewardProcessor {
	
	private static final Logger logger = LoggerFactory.getLogger(EquipProcessor.class);
	
	@Autowired
	private EquipService equipService;
	@Autowired
	private GameConfigManager gameConfigManager;
	@Autowired
	private EquipDao equipDao;
	@Autowired
	private HeroDao heroDao;

	@Override
	public RewardType getType() {
		return RewardType.EQUIP;
	}

	@Override
	public RewardResourceType getRewardResourceType() {
		return RewardResourceType.NONE;
	}

	@Override
	public RewardAction tryReward(long userId, Reward reward) {
		int resultCode = ErrorCode.SUCCESS;
		int addCount = reward.getCount();
		if (addCount <= 0) {
			EquipReward equipReward=(EquipReward)reward;
			Equip equip = equipDao.getByRowId(userId, equipReward.getRowId());
			if(equip==null) {
				// 删除装备由具体业务处理
				resultCode = ErrorCode.EQUIP_IS_NOT_FOUND;
			}
		} else {
			EquipEntity equipEntity = gameConfigManager.getEquipConfig().getEquip().get(reward.getConfigId());
			if (equipEntity == null) {
				logger.error("equip config id not exist:{}", reward.getConfigId());
				resultCode = ErrorCode.CONFIG_NOT_EXIST;
			}
		}
		return simpleRewardAction(reward, resultCode);
	}

	@Override
	public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
		if (rewardAction.isFailed()) {
			return null;
		}
		EquipReward reward = (EquipReward) rewardAction.getReward();
		int addCount = reward.getCount();

		RewardResult<List<Equip>> result = new RewardResult<List<Equip>>(this.getType());
		if(reward.getCount()>0) {
			List<Equip> addEquips = new ArrayList<Equip>(addCount);
			// 新增装备
			for (int i = 0; i < addCount; i ++) {
				Equip equip = equipService.createEquip(userId, reward.getConfigId(), reward.getLevel());
				if (equip == null) {
					logger.error("equip: create failed. userId: {}" , userId);
					return null;
				}
				addEquips.add(equip);
			}


			result.setActualCount(1);
			result.setCurrent(addEquips);
		} else {
			Equip equip = equipDao.getByRowId(userId, reward.getRowId());

			// 删除装备
			equipDao.delete(equip);
			result.setActualCount(1);
			result.setCurrent(new ArrayList<>());
		}
		return result;
	}

}




















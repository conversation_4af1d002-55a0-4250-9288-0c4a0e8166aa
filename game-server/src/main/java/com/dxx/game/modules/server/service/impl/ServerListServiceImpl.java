package com.dxx.game.modules.server.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.error.GameError;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.serverrange.ServerGropEntity;
import com.dxx.game.config.entity.serverrange.ServerRangeEntity;
import com.dxx.game.config.entity.serverrange.ServerWarEntity;
import com.dxx.game.consts.WarType;
import com.dxx.game.dao.dynamodb.model.Account;
import com.dxx.game.dao.dynamodb.model.Server;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.AccountDao;
import com.dxx.game.dao.dynamodb.repository.ServerDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.redis.ServerInfoRedisDao;
import com.dxx.game.dto.ServerListProto;
import com.dxx.game.modules.server.ServerListConstants;
import com.dxx.game.modules.server.info.ServerDescInfo;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dxx.game.dto.ServerListProto.ServerGroupDto;
import com.dxx.game.dto.ServerListProto.ServerGroupDto.Builder;
import com.dxx.game.dto.ServerListProto.ServerInfoDto;
import com.dxx.game.dto.ServerListProto.ZoneInfoDto;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class ServerListServiceImpl implements ServerListService {
    @Resource
    private GameConfigManager gameConfigManager;
    @Autowired
    private RedisService redisService;
    @Resource
    private UserDao userDao;
    @Resource
    private AccountDao accountDao;
    @Resource
    private ServerDao serverDao;
    @Resource
    private ServerInfoRedisDao serverInfoRedisDao;
    @Resource
    private UserService userService;

    @Override
    public int generateServerId(String clientMark) {
        // 解析客户端标识，获取服务器范围实体
        ServerRangeEntity serverEntity = this.parseLanguageMark(clientMark);
        final int finalMark = serverEntity.getId();

        // 获取当前服务器ID
        int serverId = 0;
        for (int i = 0; i < 10; i ++) {
            serverId = serverInfoRedisDao.getCurServerId(finalMark);
            if (serverId == 0) {
                log.info("serverId is 0, will open new server, mark:{}", clientMark);
                // init zone
                serverId = serverEntity.getRange().get(0);
                serverInfoRedisDao.initZone(serverId, finalMark);
            }
            var serverInfo = serverInfoRedisDao.getServerInfo(serverId);
            if (serverInfo == null) {
                log.info("serverInfo is null, will open new server, mark:{}", clientMark);
                openServer(serverId, finalMark);
                continue;
            }

            // 判断是否需要开新服（基于开服天数和人数条件）
            boolean isEligibleForNewServer =
                    serverInfo.getOpenTime() + serverEntity.getConditionDay() * DateUtils.SECONDS_PRE_DAY < DateUtils.getUnixTime() &&
                            serverInfo.getCount() > serverEntity.getConditionCountMin();
            if (isEligibleForNewServer) {
                // 开服天数大于等于开服天数，则开新服
                log.warn("serverId:{} is meet open day, will open new server, mark:{}", serverId, clientMark);
                openServer(serverId + 1, finalMark);
                continue;
            }

            var count = serverInfoRedisDao.addUserCount(serverId, 1);
            if (count > serverEntity.getConditionCountMax()) {
                // 总数大于等于最大人数，不考虑开服天数，直接开新服
                log.warn("serverId:{} is meet max count, will open new server, mark:{}", serverId, clientMark);
//            serverInfoRedisDao.addCount(serverId,-1);
                openServer(serverId + 1, finalMark);
                continue;
            }

            return serverId;
        }
        throw new GameError("serverId not found");
    }

    private void openServer(int serverId, final int finalMark) {
        long currentTime = DateUtils.getUnixTime();
        if (serverInfoRedisDao.tryOpenServer(serverId, finalMark)) {
            log.info("open new server, serverId:{}", serverId);
            // 创建 IM 群组，创建失败，也不能影响开服
            var server = serverDao.getServer(serverId);
            if (server == null) {
                server = new Server();
                server.setServerId(serverId);
                server.setOpenTime(currentTime);
                server.setCreateTime(currentTime);
                server.setUpdateTime(currentTime);
                serverDao.insertNow(server);
            }
        }
    }

    @Override
    public int getMarkId(String mark) {
        return this.parseLanguageMark(mark).getId();
    }

    @Override
    public int getZoneIdByServerId(int serverId) {
        for (Map.Entry<Integer, ServerRangeEntity> entry : gameConfigManager.getServerRangeConfig().getServerRange().entrySet()) {
            if (serverId >= entry.getValue().getRange().get(0)  && entry.getValue().getRange().get(1) >= serverId) {
                return entry.getValue().getId();
            }
        }
        return 0;
    }

    /**
     * return 配表 serverList 大区 ID，如 1-日本，2-韩国
     */
    public int getMarkIdByGroupId(int groupId) {
        ServerGropEntity groupEntity = gameConfigManager.getServerRangeConfig().getServerGropEntity(groupId);
        int serverId = groupEntity.getRange().get(0) + 1;
        return this.getServerConfig(serverId).getId();
    }


    @Override
    public long getOpenServerTime(int serverId) {
        // 由于活动开启可能调用比较多，加个缓存
        long time = RequestContext.getServerOpenTime(serverId);
        if (time == 0) {
            time = serverInfoRedisDao.getServerOpenTime(serverId);
        }
        RequestContext.putServerOpenTime(serverId, time);
        return time;
    }

    @Override
    public void onUserCreate(int serverId) {
        serverInfoRedisDao.addUserCount(serverId, 1);
    }

    private ServerRangeEntity parseLanguageMark(String languageMark) {
        if (StringUtils.isEmpty(languageMark)) {
            return gameConfigManager.getServerListMap().get(ServerListConstants.SERVER_MARK_GLOBAL);
        }
        ServerRangeEntity result = gameConfigManager.getServerListMap().get(languageMark.trim());
        return result == null ? gameConfigManager.getServerListMap().get(ServerListConstants.SERVER_MARK_GLOBAL) : result;
    }

    @Override
    @DynamoDBTransactional
    public Result<ServerListProto.UserGetLastLoginResponse> userGetLastLoginRequest(ServerListProto.UserGetLastLoginRequest params) {
        User user = RequestContext.getUser();
        Account account = accountDao.getItem(user.getAccountKey());
        List<Long> userIdList = account.getServerUserIdMap().values().stream().collect(Collectors.toList());
        List<User> userList = new ArrayList<>(userDao.getByUserIds(userIdList)
                .values());
        ServerListProto.UserGetLastLoginResponse.Builder resp = ServerListProto.UserGetLastLoginResponse.newBuilder();
        Map<Long, Long> powerMap = userService.getPower(userIdList);
        List<Integer> serverIds = new ArrayList<>();
        userList.forEach(temp -> {
            int groupId = getGroupId(temp.getServerId());
            ServerListProto.RoleDetailDto.Builder tempDto = ServerListProto.RoleDetailDto.newBuilder();
            tempDto.setNickName(Optional.ofNullable(temp.getNickName()).orElse(""));
            tempDto.setAvatar(Optional.ofNullable(temp.getAvatar()).orElse(0));
            tempDto.setAvatarFrame(Optional.ofNullable(temp.getAvatarFrame()).orElse(0));
            tempDto.setServerId(temp.getServerId());
            tempDto.setPower(powerMap.get(temp.getUserId()));
            tempDto.setGroupId(groupId);
            tempDto.setLastLoginPass(DateUtils.getUnixTime() - temp.getLoginTimestamp());
            tempDto.setUserId(temp.getUserId());
            resp.addRoleList(tempDto);
            serverIds.add(temp.getServerId());
        });
        List<ServerInfoRedisDao.ServerInfo> serverList = serverInfoRedisDao.getServerList(serverIds);
        var zoneMap = new HashMap<Integer, ServerListProto.ZoneInfoDto.Builder>();
        getZoneDtoBuilder(zoneMap, serverList);
        var zoneId = this.getMarkId(params.getCommonParams().getLanguageMark());
        zoneMap.computeIfAbsent(zoneId, k -> getZoneDtoBuilder(zoneId));
        zoneMap.forEach((k, v) -> {
            resp.putServerList(k, v.build());
        });
        return Result.Success(resp.build());
    }

    private void getZoneDtoBuilder(Map<Integer, ZoneInfoDto.Builder> zoneMap, List<ServerInfoRedisDao.ServerInfo> serverList) {
        Map<Integer, ServerGroupDto.Builder> groupMap = new HashMap<>();
        for (ServerInfoRedisDao.ServerInfo serverInfo : serverList) {
            var groupDto = groupMap.computeIfAbsent(this.getGroupId(serverInfo.getServerId()), this::getServerGroupDtoBuilder);
            ServerInfoDto.Builder serverDto = ServerInfoDto.newBuilder();
            serverDto.setServerId(serverInfo.getServerId());
            serverDto.setStatus(serverInfo.getStatus());
            groupDto.addServerInfoDto(serverDto);
        }
        for (Builder groupDto : groupMap.values()) {
            var zoneDto = zoneMap.computeIfAbsent(getMarkIdByGroupId(groupDto.getGroup()), this::getZoneDtoBuilder);
            zoneDto.putServerList(groupDto.getGroup(), groupDto.build());
        }
    }

    private ZoneInfoDto.Builder getZoneDtoBuilder(int zoneId) {
        var zoneDto = ZoneInfoDto.newBuilder();
        var curServerId = serverInfoRedisDao.getCurServerId(zoneId);
        curServerId = curServerId == 0 ? gameConfigManager.getServerRangeConfig().getServerRangeEntity(zoneId).getRange().get(0) : curServerId;
        zoneDto.setMaxServer(curServerId);
        return zoneDto;
    }

    private ServerGroupDto.Builder getServerGroupDtoBuilder(int groupId) {
        ServerGropEntity groupConfig = gameConfigManager.getServerRangeConfig().getServerGropEntity(groupId);
        var groupDto = ServerGroupDto.newBuilder();
        groupDto.setGroup(groupId);
        groupDto.setStartServer(groupConfig.getRange().get(0));
        groupDto.setEndServer(groupConfig.getRange().get(1));
        return groupDto;
    }

    private ServerListProto.ZoneInfoDto.Builder getZoneInfoDto(Integer zoneId, Map<Integer, List<ServerDescInfo>> groupMap) {
        int curServerId = getCurServerId(zoneId);
        ServerListProto.ZoneInfoDto.Builder zoneInfoDto = ServerListProto.ZoneInfoDto.newBuilder();
        zoneInfoDto.setMaxServer(curServerId == 0
                ? gameConfigManager.getServerRangeConfig().getServerRangeEntity(zoneId).getRange().get(0) : curServerId);
        groupMap.forEach((groupId, serverList) -> {
            ServerGropEntity groupConfig = gameConfigManager.getServerRangeConfig().getServerGropEntity(groupId);
            ServerListProto.ServerGroupDto.Builder groupDto = ServerListProto.ServerGroupDto.newBuilder();
            groupDto.setGroup(groupId);
            groupDto.setStartServer(groupConfig.getRange().get(0));
            groupDto.setEndServer(groupConfig.getRange().get(1));
            serverList.forEach(tempServer -> {
                ServerListProto.ServerInfoDto.Builder serverDto = ServerListProto.ServerInfoDto.newBuilder();
                serverDto.setServerId(tempServer.getServerId());
                serverDto.setStatus(tempServer.getStatus());
                groupDto.addServerInfoDto(serverDto);
            });
            zoneInfoDto.putServerList(groupId, groupDto.build());
        });
        return zoneInfoDto;
    }

    private int getCurServerId(Integer zoneId) {
        return serverInfoRedisDao.getCurServerId(zoneId);
    }


    @Override
    public void checkStatus(int serverId) {
        var serverInfo = serverInfoRedisDao.getServerInfo(serverId);
        if (serverInfo.getStatus() == ServerListConstants.SERVER_STATUS_FULL) {
            return;
        }
        ServerRangeEntity config = getServerConfig(serverId);
        if (config == null) {
            return;
        }
        if (serverInfo.getOpenTime() == 0) {
            return;
        }

        int oldStatus = serverInfo.getStatus();
        if (serverInfo.getStatus() == ServerListConstants.SERVER_STATUS_NEW) {
            if (DateUtils.getUnixTime() - serverInfo.getOpenTime() > config.getStatusNew().get(0) * DateUtils.SECONDS_PRE_DAY) {
                // 开服大于配置天数，则为老服
                serverInfo.setStatus(ServerListConstants.SERVER_STATUS_NONE);
            }
            if (serverInfo.getCount() >= config.getStatusNew().get(1)) {
                // 人数大于等于配置人数，则为老服
                serverInfo.setStatus(ServerListConstants.SERVER_STATUS_NONE);
            }
        }

        if (DateUtils.getUnixTime() - serverInfo.getOpenTime() >= config.getStatusFull().get(0) * DateUtils.SECONDS_PRE_DAY
                || serverInfo.getCount() >= config.getStatusFull().get(1)) {
            // 开服大于等于配置天数或人数大于等于配置人数，则为爆满
            serverInfo.setStatus(ServerListConstants.SERVER_STATUS_FULL);
        }

        if (oldStatus != serverInfo.getStatus()) {
            serverInfoRedisDao.updateInfo(serverId, ServerInfoRedisDao.SERVER_STATUS, serverInfo.getStatus());
        }
    }

    @Override
    @DynamoDBTransactional
    public Result<ServerListProto.FindServerListResponse> findServerListRequest(ServerListProto.FindServerListRequest params) {
        ServerListProto.FindServerListResponse.Builder resp = ServerListProto.FindServerListResponse.newBuilder();
        var zoneId = this.getMarkIdByGroupId(params.getGroupId());
        var zoneDto = getZoneDtoBuilder(zoneId);
        var serverList = getServerList(params.getGroupId(), zoneDto.getMaxServer());
        var zoneMap = new HashMap<Integer, ZoneInfoDto.Builder>();
        getZoneDtoBuilder(zoneMap, serverList);
        resp.setServerInfoDto(zoneDto.build());
        return Result.Success(resp.build());
    }

    private List<ServerInfoRedisDao.ServerInfo> getServerList(int groupId, int zoneMaxId) {
        // 通过表可以获得有哪些serverid在组里
        //通过最大serverid，可以获得当前组到了什么id
        var groupEntity = gameConfigManager.getServerRangeConfig().getServerGropEntity(groupId);
        var minServerId = groupEntity.getRange().get(0);
        var maxServerId = Math.min(groupEntity.getRange().get(1), zoneMaxId);
        var serverIdList = IntStream.range(minServerId, maxServerId + 1).boxed().collect(Collectors.toList());
        return serverInfoRedisDao.getServerList(serverIdList);
    }


    private int getGroupId(int serverId) {
        Optional<ServerGropEntity> opt = gameConfigManager.getServerRangeConfig().getServerGrop()
                .values().stream().filter(temp -> serverId >= temp.getRange().get(0) && serverId <= temp.getRange().get(1)).findAny();
        return opt.map(ServerGropEntity::getId).orElse(0);
    }

    private ServerRangeEntity getServerConfig(int serverId) {
        Optional<ServerRangeEntity> opt = gameConfigManager.getServerRangeConfig().getServerRange().values().stream().
                filter(temp -> temp.getRange().get(0) <= serverId
                        && temp.getRange().get(1) >= serverId).findAny();
        return opt.isPresent() ? opt.get() : null;
    }

    /**
     * 本服的战区ID
     */
    @Override
    public int getServerWarZoneId(int serverId) {
        return genWarZoneId(serverId);
    }

    private static final int WAR_ID_MULTIPLIER = 1_000_000;

    private int genWarZoneId(int serverId) {
        ServerRangeEntity range = getServerConfig(serverId);
        ServerWarEntity war = gameConfigManager.getServerRangeConfig().getServerWarEntity(range.getId());
        int index = serverId - range.getRange().get(0) + 1;
        int warSize = war.getWar1();
        int warIndexId = (int) Math.ceil((double) index / warSize);
        int firstServerId = range.getRange().get(0) + (warIndexId - 1) * warSize;
        return WAR_ID_MULTIPLIER * WarType.DEFAULT_WAR_TYPE + firstServerId;
    }

    /**
     * 以战区分组中第一个服开服时间为主
     */
    @Override
    public Long getServerWarZoneOpenTime(int warType, int serverId) {
        return getServerWarZoneOpenTimeByWarZoneId(warType, getServerWarZoneId(serverId));
    }

    @Override
    public Long getServerWarZoneOpenTimeByWarZoneId(int warType, int warId) {
        return getOpenServerTime(getServerWarFirstServerId(warType, warId));
    }

    private int getServerWarFirstServerId(int warType, int warId) {
        return warId - (warType * WAR_ID_MULTIPLIER);
    }
}

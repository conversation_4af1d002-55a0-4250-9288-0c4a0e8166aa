package com.dxx.game.modules.guild.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dto.GuildProto.*;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
public interface GuildService {

    /**
     * 获取公会信息
     * @param params
     * @return
     */
    Result<GuildGetInfoResponse> getInfoAction(GuildGetInfoRequest params);

    /**
     * 创建公会
     * @param params
     * @return
     */
    Result<GuildCreateResponse> createAction(GuildCreateRequest params);

    /**
     * 搜索公会
     * @param params
     * @return
     */
    Result<GuildSearchResponse> searchAction(GuildSearchRequest params);

    /**
     * 查看公会详细信息
     * @param params
     * @return
     */
    Result<GuildGetDetailResponse> getDetailAction(GuildGetDetailRequest params);

    /**
     * 获取成员列表
     * @param params
     * @return
     */
    Result<GuildGetMemberListResponse> getMemberListAction(GuildGetMemberListRequest params);

    /**
     * 修改公会信息
     * @param params
     * @return
     */
    Result<GuildModifyResponse> modifyAction(GuildModifyRequest params);

    /**
     * 解散公会
     * @param params
     * @return
     */
    Result<GuildDismissResponse> dismissAction(GuildDismissRequest params);

    /**
     * 申请加入公会
     * @param params
     * @return
     */
    Result<GuildApplyJoinResponse> applyJoinAction(GuildApplyJoinRequest params);

    /**
     * 取消申请
     * @param params
     * @return
     */
    Result<GuildCancelApplyResponse> cancelApplyAction(GuildCancelApplyRequest params);

    /**
     * 获取申请列表
     * @param params
     * @return
     */
    Result<GuildGetApplyListResponse> getApplyListAction(GuildGetApplyListRequest params);

    /**
     * 同意加入
     * @param params
     * @return
     */
    Result<GuildAgreeJoinResponse> agreeJoinAction(GuildAgreeJoinRequest params);

    /**
     * 拒绝加入
     * @param params
     * @return
     */
    Result<GuildRefuseJoinResponse> refusedJoinAction(GuildRefuseJoinRequest params);

    /**
     * 踢人
     * @param params
     * @return
     */
    Result<GuildKickOutResponse> kickOutAction(GuildKickOutRequest params);

    /**
     * 离开公会
     * @param params
     * @return
     */
    Result<GuildLeaveResponse> leaveAction(GuildLeaveRequest params);

    /**
     * 给人职位
     * @param params
     * @return
     */
    Result<GuildUpPositionResponse> upPositionAction(GuildUpPositionRequest params);

    /**
     * 转让会长
     * @param params
     * @return
     */
    Result<GuildTransferPresidentResponse> transferPresidentAction(GuildTransferPresidentRequest params);


    /**
     * 自动加入公会
     * @param params
     * @return
     */
    Result<GuildAutoJoinResponse> autoJoinAction(GuildAutoJoinRequest params);

    /**
     * 获取公会功能数据
     * @param params
     * @return
     */
    Result<GuildGetFeaturesInfoResponse> getFeaturesInfoAction(GuildGetFeaturesInfoRequest params);

    /**
     * 公会升级
     * @param params
     * @return
     */
    Result<GuildLevelUpResponse> guildLevelUpAction(GuildLevelUpRequest params);

    /**
     * 获取消息记录
     * @param params
     * @return
     */
    Result<GuildGetMessageRecordsResponse> getMessageRecordsAction(GuildGetMessageRecordsRequest params);

    /**
     * 随机创建公会
     * @param num
     */
    void devCreateRandomGuild(int num, int serverId);
    /**
     * 更新power
     * @param userId
     * @param power
     */
    boolean updateGuildPower(long userId,long power);

    /**
     * 夸天检测数据
     * @param guild
     */
    void detectAcrossDaysData(Guild guild, GuildUser guildUser);

    Result<GuildTechUpgradeResponse> techUpgrade(GuildTechUpgradeRequest params);

    // 获取玩家工会ID
    long getGuildId(long userId);
}

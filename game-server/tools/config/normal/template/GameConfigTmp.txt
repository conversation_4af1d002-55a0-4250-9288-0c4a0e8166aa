package ${pacakge};

import java.util.concurrent.ConcurrentSkipListMap;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.config.game.annotation.Resource;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
${imports}

@Slf4j
@Getter
@ToString
@Resource(name="${resource}")
public class ${className} extends GameConfiguration<$4> {
	
${fields}

${getters}

}

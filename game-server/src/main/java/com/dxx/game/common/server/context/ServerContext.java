package com.dxx.game.common.server.context;

import com.dxx.game.common.config.game.event.RefreshConfigEvent;
import com.dxx.game.common.server.consts.ServerType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2024/7/12
 * @description:
 */
public class ServerContext {

    public static ServerType serverType;

    public static void setServerType(ServerType value) {
        ServerContext.serverType = value;
    }

    public static ServerType getServerType() {
        return ServerContext.serverType;
    }
}

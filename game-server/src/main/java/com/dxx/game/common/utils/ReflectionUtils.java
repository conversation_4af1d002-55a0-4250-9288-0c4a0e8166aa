package com.dxx.game.common.utils;

import lombok.SneakyThrows;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.lang.reflect.Field;

/**
 * @author: lsc
 * @createDate: 2025/3/15
 * @description:
 */
public class ReflectionUtils {

    public static Field findField(Class<?> clazz, String name) {
        for (Field declaredField : clazz.getDeclaredFields()) {
            if (declaredField.getName().equalsIgnoreCase(name)) {
                return declaredField;
            }
        }
        return null;
    }

    @SneakyThrows
    public static <T, V> void setFieldValue(T target, Field field, V value) {
        field.setAccessible(true);
        field.set(target, value);
    }
}

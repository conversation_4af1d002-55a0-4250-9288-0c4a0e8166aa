# 使用说明

## 环境准备
1. **安装 Node.js**  
   请确保已安装 [Node.js](https://nodejs.org/) 环境。

2. **安装依赖**  
   在当前项目目录下执行以下命令安装所需扩展：
   ```bash
   npm install
   ```

## 目录结构
| 目录 | 说明 |
| ------ | ------ |
| api | 接口调试		-- 启动命令 node api/servers/server.js   |
| bat | windows 脚本 |
| config | 生成配置文件    |
| db | 数据库相关     |
| gm | GM后台需要上传的配置文件   |
| proto | 协议相关   |
| server | 服务器脚本   |
| shell | unix 脚本 |

## 使用生成proto 之前要创建proto协议文件夹软连接
```bash
# windows命令
# mklink /J 目标文件夹 源文件夹  
# proto 软连示例:
mklink /J D:\dxx_common\Framework2-Server\game-server\tools\proto\proto D:\dxx_common\Framework2-Client\UnityProject\Tools\ProtoTools\Proto

MacOS
ln -s /Users/<USER>/Work/dxx/project/vow/client/ToBeMonsterKing/UnityProject/Tools/ProtoTools/Proto /Users/<USER>/Work/dxx/project/vow/VowRpg-Game/game-server/tools/proto/proto 
```

## config 连到客户端LubanTools目录
``` bash
Windows
mklink /J D:\dxx_common\Framework2\UnityProject\Tools\LubanTools D:\dxx_common\Framework2-Client\UnityProject\Tools\ProtoTools\Proto

MacOs
ln -s /Users/<USER>/Documents/dxx/client/Framework2/UnityProject/Tools/LubanTools luban
ln -s /Volumes/Pink/Work/Dxx/Project/Framework2/Client/Framework2/UnityProject/Tools/LubanTools luban

```
## db启动
```
java -D"java.library.path=./DynamoDBLocal_lib" -jar DynamoDBLocal.jar -sharedDb -port 8010 -dbPath D:/db/framework2
```

## grpc
运行build.bat生成grpc接口. 复制xxxGrpc到工程dto目录
运行服务器D:\project\rpg_round\client\rpg_round2\BattleServer\StartServer
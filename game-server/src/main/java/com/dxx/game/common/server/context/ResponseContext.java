package com.dxx.game.common.server.context;

import com.dxx.game.dto.CommonProto;
import io.netty.util.concurrent.FastThreadLocal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 响应上下文，用于管理线程本地变量的存储和操作。
 * 使用 FastThreadLocal 提供高效的线程本地存储。
 *
 * <AUTHOR>
 * @date 2019-12-12 11:52
 */
public class ResponseContext {

    // -------------------- 任务相关 --------------------

    /** 升级奖励结果集 */
    private static final FastThreadLocal<Object> levelUpRewardResultSet = new FastThreadLocal<>();


    /** 任务领取奖励时触发其他完成任务列表 */
    private static final FastThreadLocal<Map<Integer, List<Integer>>> taskDoneInfo = new FastThreadLocal<>();

    /** 更新任务之后的任务数据 */
    private static final FastThreadLocal<Object> taskData = new FastThreadLocal<>();


    // -------------------- 开服嘉年华相关 --------------------

    /** 开服嘉年华任务红点 */
    private static final FastThreadLocal<Boolean> openServiceCarnivalTaskRedPoint = new FastThreadLocal<>();

    /** 开服嘉年华任务领取奖励/领取活跃度奖励触发的其他任务进度 */
    private static final FastThreadLocal<List<Integer>> openServiceCarnivalTaskData = new FastThreadLocal<>();

    // -------------------- 公会任务相关 --------------------

    /** 公会任务数据 */
    private static final FastThreadLocal<List<Integer>> guildTaskData = new FastThreadLocal<>();

    // -------------------- 冻结时间 --------------------

    /** 冻结时间 */
    private static final FastThreadLocal<Long> frozeTime = new FastThreadLocal<>();

    // -------------------- HTTP相关 --------------------

    /** HTTP状态码 */
    private static final FastThreadLocal<Integer> okHttpStatusCode = new FastThreadLocal<>();

    /** HTTP响应体 */
    private static final FastThreadLocal<String> okHttpBody = new FastThreadLocal<>();

    // -------------------- 通用通知对象 --------------------

    /** 通用通知对象 */
    private static final FastThreadLocal<Map<String, Consumer<CommonProto.CommonData.Builder>>> fillCommonObject = new FastThreadLocal<>();

    // -------------------- 方法实现 --------------------

    // 升级奖励结果集
    public static void setLevelUpRewardResultSet(Object resultSet) {
        levelUpRewardResultSet.set(resultSet);
    }

    public static Object getLevelUpRewardResultSet() {
        return levelUpRewardResultSet.get();
    }

    // 任务完成信息
    public static void setTaskDoneInfo(int taskType, int taskId) {
        if (taskDoneInfo.get() == null) {
            taskDoneInfo.set(new HashMap<>());
        }
        taskDoneInfo.get().computeIfAbsent(taskType, k -> new ArrayList<>()).add(taskId);
    }

    public static Map<Integer, List<Integer>> getTaskDoneInfo() {
        return taskDoneInfo.get();
    }

    // 更新任务数据
    public static void setTaskData(Object obj) {
        taskData.set(obj);
    }

    public static Object getTaskData() {
        return taskData.get();
    }

    // 开服嘉年华任务
    public static void setOpenServiceCarnivalTaskRedPoint(boolean value) {
        openServiceCarnivalTaskRedPoint.set(value);
    }

    public static Boolean getOpenServiceCarnivalTaskRedPoint() {
        return openServiceCarnivalTaskRedPoint.get();
    }

    public static void addOpenServiceCarnivalTaskIds(List<Integer> taskIds) {
        if (openServiceCarnivalTaskData.get() == null) {
            openServiceCarnivalTaskData.set(new ArrayList<>());
        }
        openServiceCarnivalTaskData.get().addAll(taskIds);
    }

    public static List<Integer> getOpenServiceCarnivalTaskIds() {
        return openServiceCarnivalTaskData.get();
    }


    public static void setGuildTaskIds(List<Integer> taskIds) {
        if (guildTaskData.get() == null) {
            guildTaskData.set(new ArrayList<>());
        }
        guildTaskData.get().addAll(taskIds);
    }

    public static List<Integer> getGuildTaskIds() {
        return guildTaskData.get();
    }

    // 冻结时间
    public static void setFrozeTime(long time) {
        frozeTime.set(time);
    }

    public static long getFrozeTime() {
        return frozeTime.get();
    }

    // HTTP相关
    public static void setOkHttpStatusCode(int value) {
        okHttpStatusCode.set(value);
    }

    public static Integer getOkHttpStatusCode() {
        return okHttpStatusCode.get();
    }

    public static void setOkHttpBody(String value) {
        okHttpBody.set(value);
    }

    public static String getOkHttpBody() {
        return okHttpBody.get();
    }

    // 通用通知对象
    public static Map<String, Consumer<CommonProto.CommonData.Builder>> getFillCommonObject() {
        return fillCommonObject.get();
    }

    public static void addFillCommon(String key, Consumer<CommonProto.CommonData.Builder> msgConsumer) {
        Map<String, Consumer<CommonProto.CommonData.Builder>> notifyCommonMap = fillCommonObject.get();
        if (notifyCommonMap == null) {
            notifyCommonMap = new HashMap<>();
            fillCommonObject.set(notifyCommonMap);
        }
        notifyCommonMap.put(key, msgConsumer);
    }

    // 清理所有线程本地变量
    public static void clear() {
        levelUpRewardResultSet.remove();
        taskDoneInfo.remove();
        taskData.remove();
        openServiceCarnivalTaskRedPoint.remove();
        openServiceCarnivalTaskData.remove();
        guildTaskData.remove();
        frozeTime.remove();
        okHttpStatusCode.remove();
        okHttpBody.remove();
        fillCommonObject.remove();
    }
}

package com.dxx.game.modules.user.model;

import lombok.Data;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
@Data
public class UserInfoModel {

    private String nickName;
    private int avatar;
    private int avatarFrame;
    private long activeTM;
    private int level;
    private int chapterId;
    private long power;


    public static UserInfoModel valueOf(String nickName, int avatar, int avatarFrame, int level,
                                        long activeTM, int chapterId,long power) {
        UserInfoModel model = new UserInfoModel();
        model.nickName = nickName;
        model.avatar = avatar;
        model.avatarFrame = avatarFrame;
        model.activeTM = activeTM;
        model.level = level;
        model.chapterId = chapterId;
        model.power=power;
        return model;
    }
}

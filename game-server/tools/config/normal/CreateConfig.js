var xlsx = require('node-xlsx');
var path = require("path");
var fs = require("fs");
var rp = require('request-promise');
var request = require("request");
// excel，json文件路径
var excelPath = path.join(__dirname, "/xlsx");
var jsonPath = path.join(__dirname, "../../../gameconf");

var commonConfigPackage = "com.dxx.game.common.config.game";
var gameConfigPackage = "com.dxx.game.config";
var convertPackage = "import " + commonConfigPackage + ".converter";
var entityPackage = gameConfigPackage + ".entity";
var objectPackage = gameConfigPackage + ".object";

var entityTmp = path.join(__dirname, "./template/EntityTmp.txt");
var configTmp = path.join(__dirname, "./template/GameConfigTmp.txt");
var baseConfigManagerTmp = path.join(__dirname, "./template/BaseConfigManagerTmp.txt");

// 生成文件路径
var configPath = path.join(__dirname, "../../../src/main/java/com/dxx/game/config/object");
var entityPath = path.join(__dirname, "../../../src/main/java/com/dxx/game/config/entity");
var tableStructureConfig = path.join(__dirname,"./TableStructure.json");
var makeManagerFile = path.join(__dirname,"../../../src/main/java/com/dxx/game/config/BaseConfigManager.java");
var createConfigs = [];

var dataType = {
	'int':'int',
	'bool':'int',
	'string':'String',
	'#string':'String',
    'float': 'float',
    'carr' : 'List<String>',
    'carr_int' : 'List<Integer>',
    'cobj': 'Map<Integer, Integer>',
    'carr2' :'List<List<Integer>>',
    'long' : 'long',
    'carr_string' : 'List<String>',
    'int[]': 'List<Integer>',
    'int[][]': 'List<List<Integer>>',
    'string[]': 'List<String>',
    'string[][]': 'List<List<String>>',
    'float[]': 'List<Float>',
    'float[][]': 'List<List<Float>>',
    'long[]': 'List<Long>'
};

var construct = {
	'carr': 'new ArrayList<String>()',
    'carr_int': 'new ArrayList<Integer>()',
    'cobj': 'new HashMap<Integer, Integer>()',
    'carr2': 'new ArrayList<>()',
    'carr_string': 'new ArrayList<>()',
    'int[]': 'new ArrayList<Integer>()',
    'int[][]': 'new ArrayList<>()',
    'string[]': 'new ArrayList<String>()',
    'string[][]': 'new ArrayList<>()',
    'float[]': 'new ArrayList<>()',
    'float[][]': 'new ArrayList<>()',
    'long[]': 'new ArrayList<>()'
}

var convertType = {
	"carr": "ListStringConverter",
	"carr_int": "ListIntConverter",
	"int[]":"ListIntConverter",
	"cobj":"MapConverter",
	"int[][]":"MatrixIntegerConverter",
	"string[][]":"MatrixStringConverter",
	"float[][]":"MatrixFloatConverter",
	"carr_string":"ListStringConverter",
	"string[]":"ListStringConverter",
	"float[]":"ListFloatConverter",
	"long[]": "ListLongConverter"
};

var baseType = ['int', 'string', 'float', 'long', '#string'];
var configMapKeyTypes = {'int': 'Integer', 'long': 'Long', 'string': 'String'};

// 读取配置文件
const conifg = fs.readFileSync(path.join(__dirname, "../../config.json"), 'utf-8');
const conifgObj = JSON.parse(conifg);
const configGameId = conifgObj["config_game_id"];



handler();

async function handler() {
	// 下载配置文件
	await downLoadTableStructureJson();
	// 生成配置表实体类
	createConfigEntitysFromJson();
	// 生成配置表manager
	createBaseConfigManager();

	// 生成item.json
	makeItemJson();
}



// 下载表结构配置文件
async function downLoadTableStructureJson() {
	var url = "http://config.gorilla10.com/export/exportStructure?key=a5f0a9b3c9e1c7e0&gameId=" + configGameId;
	var response = await rp({resolveWithFullResponse: true, method:'POST', uri:url});
	var statusCode = response.statusCode;
	if (statusCode != 200) {
		console.log('\x1B[31m%s\x1B[0m', "TableStructure.json 下载失败, statusCode:" + statusCode + ", url:" + url)
		process.exit(1);
	}

	var fd = fs.openSync(tableStructureConfig,'w');
	fs.writeSync(fd, response.body, 0, "utf-8");
	fs.closeSync(fd);

}

// 根据json配置生成配置表实体类
function createConfigEntitysFromJson() {
	var logPath = path.join(__dirname, "./data/modifyfile.txt");
	var exists = fs.existsSync(logPath);
	var modifyFiles = [];
    if (exists) {
    	modifyFiles = JSON.parse(fs.readFileSync(path.join(__dirname, "./data/modifyfile.txt"),'utf-8'));
    }
	
	var tableStruct = fs.readFileSync(tableStructureConfig,'utf-8');
	tableStructObj = JSON.parse(tableStruct);
	var fileNames = Object.keys(tableStructObj).sort();

	for (var i = 0; i < fileNames.length; i ++) {
		var fileName = fileNames[i];
		// 最近有改动的json配置
		if (modifyFiles.indexOf(fileName) >= 0) {
			var tables = tableStructObj[fileName];
			var tableNames = Object.keys(tables).sort();
			for (var j = 0; j < tableNames.length; j ++) {
				var tableName = tableNames[j];
				var columns = tables[tableName];
				createJavaConfigEntityClass(fileName, tableName, columns);

			}

			createJavaConfigObjectClass(fileName, tables);

			console.log('\x1B[32m' + fileName + " make success.\x1B[0m");
		}

		createConfigs.push(fileName);
	}
}

// 创建java实体类
function createJavaConfigEntityClass(packageName, tableName, columns) {
	var index = 0;
	var fields = "";

	for (var fieldName in columns) {
		var type = columns[fieldName]['type'];
		var desc = columns[fieldName]['desc'];


		// 注释
		var desc = replaceAll("\n", "\n\t * ", desc);
		// 字段类型
		var fieldType = dataType[type];
		var converter = "DefaultFieldConverter";
		if (baseType.indexOf(type) < 0) {
			converter = convertType[type];
		}

		// 生成字段
		var field = `
	/**
	 * ${desc}
	 */\n`;
	 	if (converter != "") {
	 		field += `\t@FieldMeta(index = ${index}, converter = ${converter}.class)`;
	 	} else {
	 		field += `\t@FieldMeta(index = ${index})`;
	 	}
	 
	 	field += `\n\tprivate ${fieldType} ${fieldName};`;

		fields += field;


		index ++;
	}

	var className = ucfirst(makeFieldName(tableName)) + "Entity";
	packageName = replaceAll("_", "", packageName.toLowerCase());
	var package = `com.dxx.game.config.entity.${packageName};`;

	var imports = "import java.util.List;\n";
	imports += "import com.dxx.game.common.config.game.converter.*;";


	// 替换内容
	var content = fs.readFileSync(entityTmp,'utf-8');
	content = content.replace("${package}", package);
	content = content.replace("${imports}", imports);
	content = content.replace("${className}", className);
	content = content.replace("${fields}", fields);
	
	
	// 创建配置文件实体类文件夹
	var entityDir = entityPath + "/" + replaceAll("_", "", packageName.toLowerCase());
	var exist = fs.existsSync(entityDir);

	if (!exist) {
		fs.mkdirSync(entityDir);
	}

	var fd = fs.openSync(entityDir + "/" + className + ".java",'w');
	fs.writeSync(fd, content, 0, "utf-8");
	fs.closeSync(fd);
}


// 创建配置表访问对象
function createJavaConfigObjectClass(fileName, tables) {
	fileName = ucfirst(fileName);

	// 字段
	var fields = "";
	// 导入的包
	var imports = "";
	// getter 方法
	var getters = "";

	var tableNames = Object.keys(tables).sort();
	for (var i = 0; i < tableNames.length; i ++) {
		var tableName = tableNames[i];
		var columns = tables[tableName];
		var keyColumn;
		for (var fieldName in columns) {
			if (columns[fieldName].hasOwnProperty("isKey")) {
				keyColumn = columns[fieldName];
				break;
			}
		}

		// 类名称
		var entityName = ucfirst(makeFieldName(tableName)) + "Entity";
		// 属性字段名称
		var fieldName = lcfirst(tableName);
		// get 方法参数类型
		var getFuncParamType = keyColumn['type'];
		if (getFuncParamType == "string") {
			getFuncParamType = "String";
		} else if (getFuncParamType == "xint") {
			getFuncParamType = "int"
		}


		var mapKey = configMapKeyTypes[keyColumn['type']];;
		
		// 属性
		var field = `    private ConcurrentSkipListMap<${mapKey}, ${entityName}> ${fieldName} = new ConcurrentSkipListMap<>();\n`;
		fields += field;

		// getter方法
		var getter = `
    public ${entityName} get${entityName}(${getFuncParamType} id) {
		if (!${fieldName}.containsKey(id)) {
			log.error("${entityName} config not found ----> id:{}", id);
		}
	    return ${fieldName}.get(id);
	}\n`;
		getters += getter;

		
		var filePackage = replaceAll("_", "", fileName.toLowerCase());
		var importClass = `import ${entityPackage}.${filePackage}.${entityName};\n`;

		imports += importClass;

	}

	

	var content = fs.readFileSync(configTmp,'utf-8');
	content = content.replace("${pacakge}", objectPackage);
	content = content.replace("${imports}", imports);
	content = content.replace("${resource}", makeFieldName(fileName));
	content = content.replace("${className}", makeFieldName(fileName) + "Config").replace("$4", makeFieldName(fileName) + "Config");
	content = content.replace("${fields}", fields);
	content = content.replace("${getters}", getters);


	var fd = fs.openSync(configPath + "/" + makeFieldName(fileName) + "Config.java",'w');
	fs.writeSync(fd, content, 0, "utf-8");
	fs.closeSync(fd);
}

function createBaseConfigManager() {

	var content = fs.readFileSync(baseConfigManagerTmp,'utf-8');

	var imports = "";
	var fields = "";

	for (var i = 0; i < createConfigs.length; i ++) {
		var configName = createConfigs[i] + "Config";

		var importClass = `import com.dxx.game.config.object.${configName};\n`
		imports += importClass;

		var fieldName = lcfirst(configName);
		var field = `
	@Autowired
	private ${configName} ${fieldName};`;

		fields += field;

	}

	content = content.replace("${imports}", imports);
	content = content.replace("${fields}", fields);

	var fd = fs.openSync(makeManagerFile,'w');
	fs.writeSync(fd, content, 0, "utf-8");
	fs.closeSync(fd);
}



// import com.dxx.game.config.object.DropConfig;


function ucfirst(value) {
	return value.substring(0, 1).toUpperCase() + value.substring(1)
}

function lcfirst(value) {
	return value.substring(0, 1).toLowerCase() + value.substring(1)	
}

function makeFieldName(name) {
	name = name.split("_");
	var result = "";
	for (var i = 0; i < name.length; i ++) {
		var n = name[i];
		if (i > 0) {
			n = ucfirst(n);
		}
		result += n;
	}
	
	return result;
}

function checkIsNumber(val){

    var regPos = /^\d+(\.\d+)?$/; //非负浮点数
    var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
    if(regPos.test(val) || regNeg.test(val)){
        return true;
    }else{
        return false;
    }
}

function checkIsFloat(val) {
	var test = val + "";
	return test.indexOf(".") >= 0;
}

function jsonExist(fileName) {
	var jsonFile = jsonPath + "/" + fileName + ".json";
	return fs.existsSync(jsonFile);
}


function makeItemJson() {
	var items = fs.readFileSync(jsonPath + "/Item.json",'utf-8');

	var itemsObj = JSON.parse(items);

	var result = {};
	var result_wiki = {};
	itemsObj = itemsObj['Item'];
	for (var key in itemsObj) {
		result[key] = {};
		result[key]['name'] = itemsObj[key]['note'];
		result[key]['itemType'] = itemsObj[key]['itemType'];

		result_wiki[key] = {};
		result_wiki[key]['note'] = itemsObj[key]['note'];
        		result_wiki[key]['itemType'] = itemsObj[key]['itemType'];

	}

	var configPath = path.join(__dirname, "../../api/config/Item.json");

	var jsonContent = JSON.stringify(result_wiki);
	var fd = fs.openSync(configPath,'w');
	fs.writeSync(fd, jsonContent, 0, "utf-8");
	fs.closeSync(fd);


	var configPath = path.join(__dirname, "../../gm/Item.json");

	var jsonContent = JSON.stringify(result);
	var fd = fs.openSync(configPath,'w');
	fs.writeSync(fd, jsonContent, 0, "utf-8");
	fs.closeSync(fd);
}

function replaceAll(find, replace, str) {
	var find = find.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
	return str.replace(new RegExp(find, 'g'), replace);
}

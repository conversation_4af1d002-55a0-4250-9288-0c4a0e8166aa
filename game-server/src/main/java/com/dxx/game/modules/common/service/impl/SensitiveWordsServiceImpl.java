package com.dxx.game.modules.common.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.modules.common.service.SensitiveWordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
@Slf4j
@Service
public class SensitiveWordsServiceImpl implements SensitiveWordsService {

    private static final String CONTEXT_TYPE_CHAT = "chat";
    private static final String CONTEXT_TYPE_NICK = "nick";

    @Resource
    private GameConfigManager gameConfigManager;

    @Override
    public boolean checkIsLegal(String words) {
        if (this.checkSurrogatePair(words) || this.containsSpecialOrUnassignedCharacters(words)) {
            return false;
        }
        String response = this.detectText(CONTEXT_TYPE_NICK, words);
        JSONObject respObj = JSONObject.parseObject(response);
        int code = respObj.getIntValue("code");
        if (code != 0) {
            log.error("checkIsLegal error, words:{}, response:{}", words, response);
            return false;
        }
        JSONObject respData = respObj.getJSONObject("data");
        String suggestion = respData.getString("suggestion");
        if (!suggestion.equals("pass")) {
            log.error("checkIsLegal failed, words:{}, response:{}", words, response);
            return false;
        }
        return true;
    }

    @Override
    public String replaceWords(String words) {
        String response = this.detectText(CONTEXT_TYPE_CHAT, words);
        JSONObject respObj = JSONObject.parseObject(response);
        int code = respObj.getIntValue("code");
        if (code != 0) {
            log.error("replaceWords error, words:{}, response:{}", words, response);
            return "";
        }
        return respObj.getJSONObject("data").getString("context");
    }

    private String detectText(String contextType, String text) {
        String url = gameConfigManager.getSensitiveWordsUrl();
//        url = "https://gtf.ai.xingzheai.cn/v5.0/game_chat_ban/detect_text";
        String token = gameConfigManager.getSensitiveWordsToken();
        Map<String, String> postData = new HashMap<String, String>();
        postData.put("data_id", UUID.randomUUID().toString());
        if (RequestContext.getUserId() != null) {
            postData.put("user_id", String.valueOf(RequestContext.getUserId()));
        }
        postData.put("context_type", contextType);
        postData.put("context", text);
        postData.put("token", token);
        return OkHttpClientUtil.postJsonWithOutHeader(url, postData, String.class);
    }

    private boolean checkSurrogatePair(String str) {
        // "𓆡𓆞𓆜"
        // 这些字符被称为"代理对"（Surrogate Pairs），因为它们位于基本多文种平面之外，使用两个char值表示
        // 使用Character.isSurrogatePair(char high, char low)来确定相邻的两个char值是否形成一个有效的代理对。或者使用Character.isHighSurrogate(char ch)和Character.isLowSurrogate(char ch)来分别检查高代理和低代理字符
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (Character.isHighSurrogate(ch) && i + 1 < str.length()) {
                char nextCh = str.charAt(i + 1);
                if (Character.isLowSurrogate(nextCh)) {
                    return true;
                    // 输出代理对
//                    System.out.println("Found surrogate pair: " + ch + nextCh);
//                    // 将代理对转换为码点
//                    int codePoint = Character.toCodePoint(ch, nextCh);
//                    System.out.println("Code point: " + Integer.toHexString(codePoint));
//                    i++; // 跳过下一个字符，因为它是代理对的一部分
                }
            }
        }


        return false;
    }

    // 检测字符串中是否包含未分配的 Unicode
    private boolean containsSpecialOrUnassignedCharacters(String str) {
        Pattern pattern = Pattern.compile("[\\p{Cn}\\p{Cf}]");
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            log.error("Contains unassigned character(s), str:{}, match:{}", str, matcher.group());
            return true;
        }
        return false;
    }
}

package com.dxx.game.modules.common.service.impl;

import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.modules.common.service.RandomService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@Service
public class RandomServiceImpl implements RandomService {

    @Override
    public List<Integer> randConfig(List<List<Integer>> configs) {
        int totalWeight = this.calcTotalWeight(configs);
        int randValue = RandomUtil.betweenValue(1, totalWeight);
        return this.randConfig(configs, randValue);
    }

    @Override
    public int randConfigIdx(List<Integer> configs) {
        int totalWeight = 0;
        for (Integer c : configs) {
            totalWeight += c;
        }
        int randValue = RandomUtil.betweenValue(1, totalWeight);
        for (int i = 0; i < configs.size(); i++) {
            if (configs.get(i) >= randValue) {
                return i;
            }
            randValue -= configs.get(i);
        }
        return 0;
    }

    @Override
    public int randConfigIdxFromNestedList(List<List<Integer>> configs) {
        int totalWeight = this.calcTotalWeight(configs);
        int randValue = RandomUtil.betweenValue(1, totalWeight);
        int idx = -1;
        for (int i = 0; i < configs.size(); i ++) {
            List<Integer> config = configs.get(i);
            int weight = config.get(config.size() - 1);
            if (weight >= randValue) {
                idx = i;
                break;
            }
            randValue -= weight;
        }
        return idx;
    }

    public List<Integer> randConfigByIndex(List<List<Integer>> configs, int weightIndex) {
        int totalWeight = this.calcTotalWeight(configs, weightIndex);
        int randValue = RandomUtil.betweenValue(1, totalWeight);
        return this.randConfig(configs, randValue, weightIndex);
    }

    /**
     * 计算总权重
     * @param dropConfig
     * @return
     */
    private int calcTotalWeight(List<List<Integer>> dropConfig) {
        int result = 0;
        for (List<Integer> config : dropConfig) {
            int w = config.get(config.size() - 1);
            result += w;
        }
        return result;
    }

    private int calcTotalWeight(List<List<Integer>> dropConfig, int weightIndex) {
        int result = 0;
        for (List<Integer> config : dropConfig) {
            int w = config.get(weightIndex);
            result += w;
        }
        return result;
    }

    /**
     * 根据权重查找配置
     * @param dropConfig
     * @param randValue
     * @return
     */
    private List<Integer> randConfig(List<List<Integer>> dropConfig, int randValue) {
        List<Integer> result = null;
        for (List<Integer> config : dropConfig) {
            int weight = config.get(config.size() - 1);
            if (weight >= randValue) {
                result = config;
                break;
            }
            randValue -= weight;
        }
        return result;
    }

    private List<Integer> randConfig(List<List<Integer>> dropConfig, int randValue, int weightIndex) {
        List<Integer> result = null;
        for (List<Integer> config : dropConfig) {
            int weight = config.get(weightIndex);
            if (weight >= randValue) {
                result = config;
                break;
            }
            randValue -= weight;
        }
        return result;
    }
}

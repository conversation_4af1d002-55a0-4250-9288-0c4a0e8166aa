package com.dxx.game.modules.activity.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.activity.Power;
import com.dxx.game.dto.PowerProto;

public interface PowerService {
    Result<PowerProto.PowerOnOpenResponse> onOpen(PowerProto.PowerOnOpenRequest params);

    void syncPower(User user, long maxPower);

    Result<PowerProto.PowerRewardResponse> reward(PowerProto.PowerRewardRequest params);
}

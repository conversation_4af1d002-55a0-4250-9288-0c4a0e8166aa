package com.dxx.game.modules.common.support;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.util.RequestIdUtil;
import com.dxx.game.dto.CommonProto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @author: lsc
 * @createDate: 2025/6/11
 * @description:
 */
@XRayEnabled
@Slf4j
@Component
public class CommonDataManager {


    public CommonProto.CommonData.Builder fillCommonData(CommonProto.CommonData.Builder builder) {
        RequestIdUtil.fillTransId(builder);

        Optional.ofNullable(ResponseContext.getFillCommonObject()).ifPresent(notifies -> notifies.forEach((key, value) -> this.fillCommonData(value, builder)));

        return builder;
    }

    public void fillCommonData(Consumer<CommonProto.CommonData.Builder> changeValue, CommonProto.CommonData.Builder builder) {
        changeValue.accept(builder);
    }

}

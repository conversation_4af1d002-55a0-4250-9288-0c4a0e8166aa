package com.dxx.game.modules.guild.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.modules.guild.service.GuildShopService;
import com.google.protobuf.Message;
import com.dxx.game.dto.GuildProto.*;
import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@ApiHandler
public class GuildShopHandler {

    @Resource
    private GuildShopService guildShopService;

    @ApiMethod(command = MsgReqCommand.GuildShopBuyRequest, name = "公会-商店-购买")
    public Result<GuildShopBuyResponse> guildShopBuy(Message msg) {
        GuildShopBuyRequest params = (GuildShopBuyRequest)msg;
        return guildShopService.guildShopBuyAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildShopRefreshRequest, name = "公会-商店-刷新")
    public Result<GuildShopRefreshResponse> refresh(Message msg) {
        GuildShopRefreshRequest params = (GuildShopRefreshRequest)msg;
        return guildShopService.refreshAction(params);
    }
}

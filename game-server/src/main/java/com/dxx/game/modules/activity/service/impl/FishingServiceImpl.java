package com.dxx.game.modules.activity.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.eventfishing.FishEntity;
import com.dxx.game.config.entity.eventfishing.FishingBaseEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.ActivityType;
import com.dxx.game.consts.MailSourceType;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.activity.Fishing;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dto.FishingProto;
import com.dxx.game.modules.activity.base.ActivityLifeCycle;
import com.dxx.game.modules.activity.base.ActivityManager;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.dxx.game.modules.activity.service.FishingService;
import com.dxx.game.modules.common.service.RandomService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FishingServiceImpl implements FishingService, ActivityLifeCycle<Fishing, FishingProto.FishingDto> {
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private RandomService randomService;
    @Autowired
    private ItemDao itemDao;
    @Autowired
    private MailService mailService;
    @Resource
    private ActivityBaseDao.FishingDao fishingDao;
    @Resource
    private RedisLock redisLock;

    @Override
    public ActivityMetaData<Fishing, FishingProto.FishingDto> buildMetaData() {
        return ActivityMetaData
                .<Fishing, FishingProto.FishingDto>valueOf(ActivityType.Fish)
                .withDao(fishingDao);
    }

    @Override
    public void initializeModel(long userId, Fishing model) {
        Fishing.FishingModel fishingModel = new Fishing.FishingModel();
        fishingModel.setWeights(0);
        fishingModel.setUnlockRods(Lists.newArrayList());
        fishingModel.setHandbookRewards(Lists.newArrayList());
        fishingModel.setHandbooks(Lists.newArrayList());
        fishingModel.setCatches(Maps.newHashMap());
        fishingModel.setBitingFishs(Lists.newArrayList());
        fishingModel.setCastBaitCount(0);

        // 鱼池
        fishingModel.setCatches(Maps.newHashMap());
        model.setFishing(fishingModel);
        model.setIsFirst(true);

        FishingBaseEntity fishingBaseEntity = getFishBaseEntity(model.getActivityId());
        if(fishingBaseEntity != null) {
            unlockRod(fishingBaseEntity.getFishRod(), model);    // 解锁鱼竿
        }
    }

    @Override
    public FishingProto.FishingDto buildDto(Fishing model) {
        FishingProto.FishingDto.Builder dto = FishingProto.FishingDto.newBuilder();
        dto.setAccWeight(model.getFishing().getWeights());
        dto.setCurRod(getCurRod(model));

        dto.setIsThrow(!model.getFishing().getBitingFishs().isEmpty());

        FishingBaseEntity fishingBaseEntity = getFishBaseEntity(model.getActivityId());
        if(fishingBaseEntity == null) {
            return dto.build();
        }
        dto.setConfigDto(buildConfigDto(fishingBaseEntity));

        return dto.build();
    }

    @DynamoDBTransactional
    @Override
    public Result<FishingProto.FishingOnOpenResponse> onOpen(FishingProto.FishingOnOpenRequest params) {
        long userId = RequestContext.getUserId();

        Fishing fishing = fetchModel(userId);
        if (fishing == null) {
            return Result.Error(ErrorCode.ACTIVITY_NOT_OPEN);
        }

        FishingBaseEntity fishBaseEntity = getFishBaseEntity(fishing.getActivityId());
        if(fishBaseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        List<List<Integer>> rewards = Lists.newArrayList();

        if (fishing.getIsFirst()) {
            fishing.setIsFirst(false);
            // 初始给鱼饵
            rewards.add(Lists.newArrayList(fishBaseEntity.getFishBaitItem(), fishBaseEntity.getDefaultFishBait()));

            updateModel(fishing);
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
        }

        FishingProto.FishingOnOpenResponse.Builder response = FishingProto.FishingOnOpenResponse.newBuilder();
        if(rewardResultSet != null) {
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        }
        response.setFish(buildDto(fishing));
        return Result.Success(response.build());
    }


    @DynamoDBTransactional
    @Override
    public Result<FishingProto.FishingCastRodResponse> castRodAction(FishingProto.FishingCastRodRequest params) {
        long userId = RequestContext.getUserId();
        int baitNum = params.getBaitNum();
        if (baitNum <= 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int eval = params.getEval();
        if (eval < 0 || eval > 2) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Fishing fishing = fetchModel(userId);
        if (fishing == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Fishing.FishingModel model = fishing.getFishing();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FishingBaseEntity fishBaseEntity = getFishBaseEntity(fishing.getActivityId());
        if (null == fishBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        int fishType = fishBaseEntity.getFishType();

        RewardResultSet rewardResultSet = rewardService.executeReward(userId, Lists.newArrayList(fishBaseEntity.getFishBaitItem(), -baitNum));
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        FishingProto.FishingCastRodResponse.Builder resp = FishingProto.FishingCastRodResponse.newBuilder();

        List<Integer> fishIds = model.getBitingFishs();
        if (fishIds == null) {
            fishIds = Lists.newArrayList();
        }
        if (fishIds.size() != baitNum) {
            fishIds.clear();
            Map<Integer, Integer> catches = Maps.newHashMap(model.getCatches());
            List<Integer> areaFishIds = gameConfigManager.getFishIdByFishType().get(fishType);
            for (int i = 0; i < baitNum; i++) {
                if (isFishClear(fishType, catches)) {
                    refreshCaches(fishType, catches);
                }

                int fishId = nextFishId(eval, fishBaseEntity, catches, areaFishIds);
                fishIds.add(fishId);
                catches.put(fishId, catches.getOrDefault(fishId, 0) + 1);
            }

            model.setBitingFishs(fishIds);
        }
        model.setCastBaitCount(baitNum);
        model.setRebornCount(0);

        updateModel(fishing);

        resp.addAllFishIds(fishIds);
        resp.setNextRebornDiamond(getNextRebonDiamond(fishBaseEntity, fishing));
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(resp.build());
    }



    @DynamoDBTransactional
    @Override
    public Result<FishingProto.FishingReelInResponse> reelIn(FishingProto.FishingReelInRequest params) {
        long userId = RequestContext.getUserId();
        User user = RequestContext.getUser();

        Fishing fishing = fetchModel(userId);
        if (fishing == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Fishing.FishingModel model = fishing.getFishing();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FishingBaseEntity fishBaseEntity = getFishBaseEntity(fishing.getActivityId());
        if (null == fishBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        boolean catched = params.getCatch();
        int baitCount = model.getCastBaitCount();
        if (baitCount <= 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<Integer> fishIds = model.getBitingFishs();
        if (catched && (fishIds == null || fishIds.isEmpty())) {
            return Result.Error(ErrorCode.FISHING_NOT_CAST_ROD);
        }

        FishingProto.FishingReelInResponse.Builder resp = FishingProto.FishingReelInResponse.newBuilder();

        List<List<Integer>> rewards = Lists.newArrayList();

        // 上钩
        if (catched) {
            for (int fishId : fishIds) {
                FishEntity fishEntity = gameConfigManager.getEventFishingConfig().getFishEntity(fishId);
                if (null == fishEntity) {
                    return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
                }

                // 处理上钩的鱼
                handleCacheFish(fishBaseEntity.getFishType(), fishing, fishEntity, rewards, resp);
            }
            int score = fishBaseEntity.getPoint() * resp.getWeight() / 100;
            rewards.add(Lists.newArrayList(fishBaseEntity.getFishPointItem(), score));

            // 排行榜
            ActivityManager.getInstance().incRank(userId, fishing.getActivityId(), resp.getWeight());
        }

        // 断线
        if (!catched) {
            rewards.add(Lists.newArrayList(fishBaseEntity.getFishLineItem(),  baitCount));
        }

        model.getBitingFishs().clear();
        model.setCastBaitCount(0);
        model.setRebornCount(0);
        updateModel(fishing);

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        FishingProto.FishingDto fishingDto = buildDto(fishing);

        resp.setFishingDto(fishingDto);
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FishingProto.FishingBuyBaitResponse> buyBait(FishingProto.FishingBuyBaitRequest params) {
        long userId = RequestContext.getUserId();

        Integer buyNum = params.getBuyNum();
        if (buyNum <= 0 || buyNum >= 200) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Fishing fishing = fetchModel(userId);
        if (fishing == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Fishing.FishingModel model = fishing.getFishing();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FishingBaseEntity fishBaseEntity = getFishBaseEntity(fishing.getActivityId());
        if (null == fishBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        List<List<Integer>> rewards = Lists.newArrayList();
        rewards.add(Lists.newArrayList(fishBaseEntity.getFishBaitItem(), buyNum));
        rewards.add(Lists.newArrayList(RewardResourceType.DIAMONDS.getValue(), -buyNum * fishBaseEntity.getFishBaitPrice()));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        FishingProto.FishingBuyBaitResponse.Builder resp = FishingProto.FishingBuyBaitResponse.newBuilder();
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FishingProto.FishingRebornResponse> reborn(FishingProto.FishingRebornRequest params) {
        long userId = RequestContext.getUserId();

        Fishing fishing = fetchModel(userId);
        if (fishing == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Fishing.FishingModel model = fishing.getFishing();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FishingBaseEntity fishBaseEntity = getFishBaseEntity(fishing.getActivityId());
        if (null == fishBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        if (model.getCastBaitCount() <= 0) {
            return Result.Error(ErrorCode.FISHING_NOT_CAST_ROD);
        }

        FishingProto.FishingRebornResponse.Builder resp = FishingProto.FishingRebornResponse.newBuilder();

        if (!params.getIsAd()) {
            int diamond = getNextRebonDiamond(fishBaseEntity, fishing);
            RewardResultSet rewardResultSet = rewardService.executeReward(userId, Lists.newArrayList(RewardResourceType.DIAMONDS.getValue(), -diamond));
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }

            resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        } else {
            resp.setCommonData(CommonHelper.buildCommonData());
        }

        model.setRebornCount(model.getRebornCount() + 1);
        updateModel(fishing);

        resp.setNextRebornDiamond(getNextRebonDiamond(fishBaseEntity, fishing));

        return Result.Success(resp.build());
    }

    @Override
    public void onActivityEnd(Fishing model) {
        this.handleExchangeMail(model);
    }

    private void handleExchangeMail(Fishing model) {
        if (model.getIsMail()) {
            return;
        }
        long userId = model.getUserId();
        FishingBaseEntity fishBaseEntity = getFishBaseEntity(model.getActivityId());
        if(fishBaseEntity == null) {
            log.error("fishBaseEntity is null, eventId={}", model.getActivityId());
            return;
        }

        // 清理的道具ID
        List<List<Integer>> mailItems = fishBaseEntity.getMailItems();
        List<Integer> clearItemIds = mailItems.stream().map(l -> l.get(0)).collect(Collectors.toList());
        clearItemIds.addAll(gameConfigManager.getEventFishingConfig().getFish().keySet());
        clearItemIds.addAll(gameConfigManager.getEventFishingConfig().getFishRod().keySet());
        Map<Integer, Integer> exchangCoins = mailItems.stream().collect(Collectors.toMap(l -> l.get(0), l -> l.get(1)));

        int coins = 0;

        // 删除道具
        List<Item> clearItems = itemDao.queryByItemIds(userId, clearItemIds);
        for (Item clearItem : clearItems) {
            if (clearItem.getCount() > 0) {
                // 换成金币
                Integer ratio = exchangCoins.get(clearItem.getItemId());
                if (ratio != null) {
                    int exchangeNum = clearItem.getCount() * ratio;
                    coins += Math.max(0, exchangeNum);
                }
            }
            itemDao.delete(clearItem);  // 清空
            log.info("FishingDeleteItem activityId={} itemId={} count={}", model.getActivityId(), clearItem.getItemId(), clearItem.getCount());
        }

        // 邮件奖励
        List<List<Integer>> rewards = new ArrayList<>();
        if (coins > 0) {
            rewards.add(Lists.newArrayList(RewardResourceType.COINS.getValue(), coins));
        }

        if (!rewards.isEmpty()) {
            String mailUniqueId = userId + "_fishing_" + model.getActivityId() + "_" + model.getMailUniqueId();
            if (redisLock.lockWithOutRetry(mailUniqueId, "1", 3000)) {
                String tmpId = gameConfigManager.getFishMail(fishBaseEntity);
                boolean flag = mailService.createMail(userId, tmpId, mailUniqueId, rewards, MailSourceType.FISHING, model.getActivityId());
                log.info("sendFishingMail userId:{}, rewards:{}", userId, rewards);
                if (!flag) {
                    log.error("sendFishingMail error, userId:{}, rewards:{}", userId, rewards);
                    return;
                }

            }
        }
        model.setIsMail(true);
        updateModel(model);
    }

    private void handleCacheFish(int fishType, Fishing model, FishEntity fishEntity, List<List<Integer>> rewards, FishingProto.FishingReelInResponse.Builder resp) {
        int fishId = fishEntity.getId();
        int min = fishEntity.getWeightFloat().get(0);
        int max = fishEntity.getWeightFloat().get(1);
        int weightFloat = RandomUtil.betweenValue(min, max);
        int weight = Math.round(fishEntity.getWeight() * weightFloat / 100f);

        FishingProto.FishDto.Builder fishBuilder = FishingProto.FishDto.newBuilder();
        fishBuilder.setFishId(fishId);
        fishBuilder.setWeight(weight);
        resp.addFishDtos(fishBuilder);
        resp.setWeight(resp.getWeight() + weight);

        rewards.add(Lists.newArrayList(fishId, 1));

        // 普通奖励
//        for (int dropId : fishEntity.getDropID()) {
//            List<List<Integer>> dropReward = dropService.getRewardConfigByDropId(dropId);
//            rewards.addAll(dropReward);
//        }

        // 增加重量
        model.getFishing().setWeights(model.getFishing().getWeights() + weight);

        Map<Integer, Integer> catches = model.getFishing().getCatches();
        int catchNum = catches.getOrDefault(fishId, 0);
        catches.put(fishId, catchNum + 1);

        if (isFishClear(fishType, catches)) {
            refreshCaches(fishType, catches);
        }
    }

    private int getArea(int eventId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(eventId);
        if(eventEntity == null) {
            log.error("cant find area, eventId={}", eventId);
            return 0;
        }

        return eventEntity.getSubType1();
    }

    private FishingBaseEntity getFishBaseEntity(int eventId) {
        int area = getArea(eventId);
        if(area == 0) {
            return null;
        }

        return gameConfigManager.getEventFishingConfig().getFishingBaseEntity(area);
    }

    private FishingProto.FishingConfigDto buildConfigDto(FishingBaseEntity baseEntity) {
        FishingProto.FishingConfigDto.Builder result = FishingProto.FishingConfigDto.newBuilder();
        result.setBaitItemId(baseEntity.getFishBaitItem());
        result.setPointItemId(baseEntity.getFishPointItem());
        result.setLineItemId(baseEntity.getFishLineItem());
        result.setBaitPrice(baseEntity.getFishBaitPrice());
        return result.build();
    }

    private int getCurRod(Fishing model) {
        Optional<Integer> optional = model.getFishing().getUnlockRods().stream().max(Integer::compareTo);
        return optional.get();
    }

    private void unlockRod(int rod, Fishing model) {
        if (!model.getFishing().getUnlockRods().contains(rod))
            model.getFishing().getUnlockRods().add(rod);
    }

    private boolean isFishClear(int fishType, Map<Integer, Integer> catches) {
        for (int fishId : gameConfigManager.getFishIdByFishType().get(fishType)) {
            FishEntity fishEntity = gameConfigManager.getEventFishingConfig().getFishEntity(fishId);
            int num = fishEntity.getNumber();
            int catchNum = catches.getOrDefault(fishId, 0);
            if (catchNum < num) {
                return false;
            }
        }

        return true;
    }

    private void refreshCaches(int fishType, Map<Integer, Integer> catches) {
        for (int fishId : gameConfigManager.getFishIdByFishType().get(fishType)) {
            FishEntity fishEntity = gameConfigManager.getEventFishingConfig().getFishEntity(fishId);
            int num = fishEntity.getNumber();
            int catchNum = catches.getOrDefault(fishId, 0);
            if (catchNum <= num)
                catches.remove(fishId);
            else
                catches.put(fishId, catchNum - num);
        }
    }

    private int nextFishId(int eval, FishingBaseEntity fishBaseEntity, Map<Integer, Integer> catches, List<Integer> fishIds) {
        Map<Integer, Integer> leftFishMap = Maps.newHashMap();

        for (Integer fishId : fishIds) {
            FishEntity fishEntity = gameConfigManager.getEventFishingConfig().getFishEntity(fishId);
            int num = fishEntity.getNumber();
            Integer catchNum = catches.get(fishId);
            if (catchNum != null)
                num -= catchNum;

            if (num > 0)
                leftFishMap.put(fishId, num);
        }

        List<List<Integer>> weightPool = Lists.newArrayList();
        leftFishMap.forEach( (fishId, leftNum) -> {
            FishEntity fishEntity = gameConfigManager.getEventFishingConfig().getFishEntity(fishId);
            if (eval > 0) {
                if (fishEntity.getType() == 2 || fishEntity.getType() == 3) {
                    int fishUp = fishBaseEntity.getFishUp().get(eval == 1 ? 0 : 1);
                    weightPool.add(Lists.newArrayList(fishId, leftNum * fishUp));
                } else {
                    weightPool.add(Lists.newArrayList(fishId, leftNum * 100));
                }
            } else {
                weightPool.add(Lists.newArrayList(fishId, leftNum));
            }
        });

        int nextFishId = randomService.randConfig(weightPool).get(0);

        return nextFishId;
    }

    private int getNextRebonDiamond(FishingBaseEntity fishBaseEntity, Fishing model) {
        int nextRebornCount = model.getFishing().getRebornCount() + 1;
        List<List<Integer>> fishFailRevive = fishBaseEntity.getFishFailRevive();
        for (List<Integer> list : fishFailRevive) {
            int min = list.get(0);
            int max = list.get(1);
            int count = list.get(2);

            if (nextRebornCount >= min && nextRebornCount <= max) {
                return count;
            }
        }

        return Iterables.getLast(fishFailRevive).get(2);
    }
}

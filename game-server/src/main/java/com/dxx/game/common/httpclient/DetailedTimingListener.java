package com.dxx.game.common.httpclient;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Nullable;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.List;

public class DetailedTimingListener extends EventListener {
    private static final Logger log = LoggerFactory.getLogger(DetailedTimingListener.class);

    private final boolean enableAllLog;

    private long callStartNanos;
    private long dnsStartNanos, dnsEndNanos;
    private long connectStartNanos, connectEndNanos, connectFailedNanos;
    private long secureConnectStartNanos, secureConnectEndNanos;
    private long connectionAcquiredNanos, connectionReleasedNanos;
    private long requestHeadersStartNanos, requestHeadersEndNanos;
    private long requestBodyStartNanos, requestBodyEndNanos;
    private long responseHeadersStartNanos, responseHeadersEndNanos;
    private long responseBodyStartNanos, responseBodyEndNanos;


    public DetailedTimingListener(boolean enableAllLog) {
        this.enableAllLog = enableAllLog;
    }

    @Override
    public void secureConnectStart(Call call) {
        secureConnectStartNanos = System.nanoTime();
    }

    @Override
    public void secureConnectEnd(Call call, @Nullable Handshake handshake) {
        secureConnectEndNanos = System.nanoTime();
    }


    @Override
    public void callStart(Call call) {
        callStartNanos = System.nanoTime();
    }

    @Override
    public void dnsStart(Call call, String domainName) {
        dnsStartNanos = System.nanoTime();
    }

    @Override
    public void dnsEnd(Call call, String domainName, List<InetAddress> inetAddressList) {
        dnsEndNanos = System.nanoTime();
    }

    @Override
    public void connectStart(Call call, InetSocketAddress inetSocketAddress, Proxy proxy) {
        connectStartNanos = System.nanoTime();
    }

    @Override
    public void connectEnd(Call call, InetSocketAddress inetSocketAddress, Proxy proxy, Protocol protocol) {
        connectEndNanos = System.nanoTime();
    }

    @Override
    public void requestHeadersStart(Call call) {
        requestHeadersStartNanos = System.nanoTime();
    }

    @Override
    public void requestHeadersEnd(Call call, Request request) {
        requestHeadersEndNanos = System.nanoTime();
    }

    @Override
    public void responseHeadersStart(Call call) {
        responseHeadersStartNanos = System.nanoTime();
    }

    @Override
    public void responseHeadersEnd(Call call, Response response) {
        responseHeadersEndNanos = System.nanoTime();
    }

    @Override
    public void responseBodyStart(Call call) {
        responseBodyStartNanos = System.nanoTime();
    }

    @Override
    public void responseBodyEnd(Call call, long bytesRead) {
        responseBodyEndNanos = System.nanoTime();
    }

    @Override
    public void connectFailed(Call call, InetSocketAddress inetSocketAddress, Proxy proxy, Protocol protocol, IOException ioe) {
        connectFailedNanos = System.nanoTime();
    }

    @Override
    public void requestBodyStart(Call call) {
        requestBodyStartNanos = System.nanoTime();
    }

    @Override
    public void requestBodyEnd(Call call, long byteCount) {
        requestBodyEndNanos = System.nanoTime();
    }

    @Override
    public void connectionAcquired(Call call, Connection connection) {
        connectionAcquiredNanos = System.nanoTime();
    }

    @Override
    public void connectionReleased(Call call, Connection connection) {
        connectionReleasedNanos = System.nanoTime();
    }

    @Override
    public void callEnd(Call call) {
        long callEndNanos = System.nanoTime();
        printSummary(callEndNanos);
    }

    @Override
    public void callFailed(Call call, IOException ioe) {
        long callFailedNanos = System.nanoTime();
        printSummary(callFailedNanos);
    }


    private void printSummary(long endNanos) {
        StringBuilder sb = new StringBuilder();
        sb.append("callDuration: [").append((endNanos - callStartNanos) / 1e6d).append(" ms]");

        if (dnsEndNanos > 0) {
            sb.append(", dnsDuration: [").append((dnsEndNanos - dnsStartNanos) / 1e6d).append(" ms]");
        }
        if (connectEndNanos > 0 || connectFailedNanos > 0) {
            if (secureConnectEndNanos > 0) {
                sb.append(", secureConnectDuration: [").append((secureConnectEndNanos - secureConnectStartNanos) / 1e6d).append(" ms]");
            }
            if (connectFailedNanos > 0) {
                sb.append(", connectFailedDuration: [").append((connectFailedNanos - connectStartNanos) / 1e6d).append(" ms]");
            } else {
                sb.append(", connectDuration: [").append((connectEndNanos - connectStartNanos) / 1e6d).append(" ms]");
            }
        }
        if (connectionReleasedNanos > 0) {
            sb.append(", connectionHeldDuration: [").append((connectionReleasedNanos - connectionAcquiredNanos) / 1e6d).append(" ms]");
        }
        if (requestHeadersEndNanos > 0) {
            sb.append(", requestHeadersDuration: [").append((requestHeadersEndNanos - requestHeadersStartNanos) / 1e6d).append(" ms]");
        }
        if (requestBodyEndNanos > 0) {
            sb.append(", requestBodyDuration: [").append((requestBodyEndNanos - requestBodyStartNanos) / 1e6d).append(" ms]");
        }
        if (responseHeadersEndNanos > 0) {
            sb.append(", responseHeadersDuration: [").append((responseHeadersEndNanos - responseHeadersStartNanos) / 1e6d).append(" ms]");
        }
        if (responseBodyEndNanos > 0) {
            sb.append(", responseBodyDuration: [").append((responseBodyEndNanos - responseBodyStartNanos) / 1e6d).append(" ms]");
        }

        if (enableAllLog) {
            log.info(sb.toString());
        }
    }
}
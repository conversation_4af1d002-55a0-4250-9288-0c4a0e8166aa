package com.dxx.game.modules.guild.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBWriteType;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guild.GuildLevelEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.*;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.guild.*;
import com.dxx.game.dao.dynamodb.repository.guild.opensearch.GuildOpenSearchDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dao.redis.guild.GuildRedisDao;
import com.dxx.game.dto.GuildProto.*;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.guild.consts.GuildPosition;
import com.dxx.game.modules.guild.consts.GuildPower;
import com.dxx.game.modules.guild.consts.GuildShopType;
import com.dxx.game.modules.guild.consts.GuildState;
import com.dxx.game.modules.guild.service.*;
import com.dxx.game.modules.guild.support.GuildFeaturesSupport;
import com.dxx.game.modules.guild.support.GuildSupport;
import com.dxx.game.modules.im.IMGroupIdGenerator;
import com.dxx.game.modules.im.service.IMService;
import com.dxx.game.modules.message.service.GuildMessageService;
import com.dxx.game.modules.rank.support.RankHelper;
import com.dxx.game.modules.reward.model.ResourceReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Expression;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
@Slf4j
@Service
public class GuildServiceImpl implements GuildService {

    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GuildSupport guildSupport;
    @Resource
    private RedisLock redisLock;
    @Resource
    private RewardService rewardService;
    @Resource
    private GuildApplyDao guildApplyDao;
    @Resource
    private UserDao userDao;
    @Resource
    private GuildRedisDao guildRedisDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserService userService;
    @Resource
    private GuildSignInService guildSignInService;
    @Resource
    private GuildTaskService guildTaskService;
    @Resource
    private GuildShopService guildShopService;
    @Resource
    private GuildFeaturesSupport guildFeaturesSupport;
    @Resource
    private GuildUserActiveRecordDao guildUserActiveRecordDao;
    @Resource
    private GuildMessageService guildMessageService;
    @Resource
    private GuildMessageDao guildMessageDao;
    @Resource
    private GuildBossService guildBossService;
    @Resource
    private GuildDonationService guildDonationService;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private RedisService redisService;
    @Autowired
    private GuildOpenSearchDao guildOpenSearchDao;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private IMService imService;

//    @Resource
//    private IMService imService;

//    @PostConstruct
//    public void del() {
//        List<Guild> guilds = guildOpenSearchDao.queryAllGuilds();
//        for (Guild guild : guilds) {
//            List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guild.getGuildId());
//            for (GuildUser guildUser : guildUsers) {
//                guildUserDao.delete(guildUser);
//            }
//            guildDao.delete(guild);
//        }
//        guildOpenSearchDao.deleteIndex();
//        guildOpenSearchDao.createIndex();
//    }

    @DynamoDBTransactional
    @Override
    public Result<GuildGetInfoResponse> getInfoAction(GuildGetInfoRequest params) {

        long userId = RequestContext.getUserId();

        GuildGetInfoResponse.Builder response = GuildGetInfoResponse.newBuilder();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser != null && guildUser.getGuildId() > 0) {
            Guild guild = guildDao.getByGuildId(guildUser.getGuildId());

            int guildStatus = guildSupport.guildStatus(guild);
            if (guildStatus != ErrorCode.SUCCESS) {
                guildUser.setGuildId(0L);
                guildUserDao.updateGuildId(guildUser);
            } else {
                response.setIsJoined(true);

                List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guild.getGuildId());
                // 会长转让
                checkTransferPresident(userId, guild, guildUsers);

                response.setGuildDetailInfoDto(guildSupport.buildGuildDetailInfoDto(guild));

                // 避免数据异常 修复公会人数
                int membersCount = response.getGuildDetailInfoDto().getGuildMemberInfoDtosCount();
                if (guild.getGuildMembersCount() != membersCount) {
                    guild.setGuildMembersCount(membersCount);
                    guildDao.updateGuildMembersCount(guild);
                }
                guildRedisDao.updateGuildMemberCount(guild.getGuildId(), membersCount);

                int guildVicePresidentCount = 0;
                int guildManagerCount = 0;
                for (GuildMemberInfoDto memberInfoDto : response.getGuildDetailInfoDto().getGuildMemberInfoDtosList()) {
                    if (memberInfoDto.getPosition() == GuildPosition.VICE_PRESIDENT) {
                        guildVicePresidentCount++;
                    }
                    if (memberInfoDto.getPosition() == GuildPosition.MANAGER) {
                        guildManagerCount++;
                    }
                }
                if (guild.getGuildVicePresidentCount() != guildVicePresidentCount) {
                    guild.setGuildVicePresidentCount(guildVicePresidentCount);
                    guildDao.updateGuildVicePresidentCount(guild);
                }
                if (guild.getGuildManagerCount() != guildManagerCount) {
                    guild.setGuildManagerCount(guildManagerCount);
                    guildDao.updateGuildManagerCount(guild);
                }

                // 更新每日活跃度的值
                if (DateUtils.getUnixTime() >= guild.getGuildDayActiveTime()) {
                    // 为了防止并发需要加个锁
                    String lockKey = "update_guild_day_active_lock";
                    if (redisLock.lockWithOutRetry(lockKey, String.valueOf(userId), 30000)) {
                        guild.setGuildDayActive(0);
                        guild.setGuildDayActiveTime(DateUtils.getSystemResetTime());
                        guildDao.updateGuildDayActive(guild);
                    }
                }

                // 跨天更新数据
                this.checkGuildFeaturesData(guild, guildUser);
                response.setGuildFeaturesDto(guildFeaturesSupport.buildGuildFeaturesDto(guild, guildUser));

                if (guildUser.getGuildLevelRecord() == null) {
                    guildUser.setGuildLevelRecord(1);
                }

                // 公会是否升级
                if (guild.getGuildLevel() > guildUser.getGuildLevelRecord()) {
                    guildUser.setGuildLevelRecord(guild.getGuildLevel());
                    guildUserDao.updateGuildLevelRecord(guildUser);
                    response.setIsLevelUp(true);
                }

                // 获取自动领取捐赠道具
                List<List<Integer>> donationItems = guildDonationService.getReceiveItems(guildUser.getGuildId(), guildUser.getUserId());
                if (donationItems != null && !donationItems.isEmpty()) {
                    RewardResultSet rewardResultSet = rewardService.executeRewards(userId, donationItems);
                    if (rewardResultSet.isFailed()) {
                        return Result.Error(rewardResultSet.getResultCode());
                    }
                    response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
                    response.addAllDonationReward(response.getCommonData().getRewardList());
                }
            }
        }

        // 是否有被踢出公会的信息
        if (guildUser != null && !StringUtils.isEmpty(guildUser.getBeKickedOutInfo())) {
            JSONObject jsonObject = JSONObject.parseObject(guildUser.getBeKickedOutInfo());
            BeKickedOutDto.Builder beKickedOutDtoBuilder = BeKickedOutDto.newBuilder();
            beKickedOutDtoBuilder.setGuildId(jsonObject.getLongValue("guildId"));
            beKickedOutDtoBuilder.setGuildName(jsonObject.getString("guildName"));
            beKickedOutDtoBuilder.setFromUserId(jsonObject.getLongValue("fromUserId"));
            beKickedOutDtoBuilder.setFromUserNickName(jsonObject.getString("fromUserNickName"));
            beKickedOutDtoBuilder.setFromUserPosition(jsonObject.getIntValue("fromUserPosition"));
            guildUser.setBeKickedOutInfo("");
            guildUserDao.updateBeKickedOutInfo(guildUser);
            response.setBeKickedOutDto(beKickedOutDtoBuilder.build());
        }

        return Result.Success(response.build());
    }

    private void checkTransferPresident(long userId, Guild guild, List<GuildUser> guildUsers) {
        try {
            long presidentUserId = guild.getGuildPresidentUserId();
            if (userId == presidentUserId) {
                return;
            }

            if(!canTransfer(guild.getGuildId())) {
                return;
            }

            // 1. 公会只有一个人
            if (presidentUserId > 0 && guildUsers.size() > 1) {
                User user = userDao.getItemWithoutCache(presidentUserId);
                int seconds = 7 * DateUtils.SECONDS_PRE_DAY;
                long nowTime = DateUtils.getUnixTime();
                if (user != null && nowTime - user.getLoginTimestamp() >= seconds) {
                    // 会长当前在线不处理
//                    if (userDao.isOnline(presidentUserId)) {
//                        return;
//                    }
                    // 需要转移会长
                    String lockKey = "check_transfer_president_lock:" + guild.getGuildId();
                    if (redisLock.lockWithOutRetry(lockKey, String.valueOf(presidentUserId), 10000)) {
                        // 被转让用户ID 都没变更 给当前玩家
                        long transferUserId = userId;
                        
                        // 副会长 周活排序
                        List<GuildUser> vp = guildUsers.stream().filter(v -> v.getPosition() == GuildPosition.VICE_PRESIDENT).collect(Collectors.toList());
                        if(!vp.isEmpty()) {
                            vp.sort((p1, p2) -> Integer.compare(p2.getWeeklyActive(), p1.getWeeklyActive()));
                            GuildUser vu = vp.get(0);
                            if(vu.getWeeklyActive() != null && vu.getWeeklyActive() > 0) {
                                // 职位变更
                                transferUserId = vu.getUserId();
                            }
                        }

                        // 管理 周活排序
                        List<GuildUser> mp = guildUsers.stream().filter(v -> v.getPosition() == GuildPosition.MANAGER).collect(Collectors.toList());
                        if(!mp.isEmpty()) {
                            mp.sort((p1, p2) -> Integer.compare(p2.getWeeklyActive(), p1.getWeeklyActive()));
                            GuildUser mu = mp.get(0);
                            if(mu.getWeeklyActive() != null && mu.getWeeklyActive() > 0) {
                                // 职位变更
                                transferUserId = mu.getUserId();
                            }
                        }

                        // 成员 周活排序
                        List<GuildUser> mep = guildUsers.stream().filter(v -> v.getPosition() == GuildPosition.MEMBER).collect(Collectors.toList());
                        if(!mep.isEmpty()) {
                            mep.sort((p1, p2) -> Integer.compare(p2.getWeeklyActive(), p1.getWeeklyActive()));
                            GuildUser meu = mep.get(0);
                            if(meu.getWeeklyActive() != null && meu.getWeeklyActive() > 0) {
                                // 职位变更
                                transferUserId = meu.getUserId();
                            }
                        }

                        Map<Long, GuildUser> guildUserMap = guildUsers.stream().collect(Collectors.toMap(GuildUser::getUserId, guildUser -> guildUser));
                        GuildUser transferUser = guildUserMap.get(transferUserId);
                        GuildUser resetUser = guildUserMap.get(presidentUserId);
                        if (resetUser == null) {
                            return;
                        }

                        // 更新双方职位
                        transferUser.setPosition(GuildPosition.PRESIDENT);
                        resetUser.setPosition(GuildPosition.MEMBER);

                        guildUserDao.updatePosition(transferUser);
                        guildUserDao.updatePosition(resetUser);

                        // 更新公会会长用户ID
                        guild.setGuildPresidentUserId(transferUserId);
                        guildDao.updateGuildPresidentUserId(guild);

                        saveFrequency(guild.getGuildId());

                        // 发通知
                        User transferUserInfo = userDao.getItemWithoutCache(transferUserId);
                        String transferUserNickName = Optional.ofNullable(transferUserInfo.getNickName()).orElse("");
                        guildMessageService.publishTransferPresidentByLoginTime(guild.getGuildId(),  transferUserId, transferUserNickName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("transfer guild president error", e);
        }
    }

    private boolean canTransfer(long guildId) {
        String k = RedisKeys.GUILD_TRAN_FAST_KEY + guildId;
        long frequency = redisService.getLongValue(k);
        return DateUtils.getUnixTime() > frequency;
    }

    private void saveFrequency(long guildId) {
        String k = RedisKeys.GUILD_TRAN_FAST_KEY + guildId;
        int frequency = 7 * DateUtils.SECONDS_PRE_DAY;

        redisService.set(k, DateUtils.getUnixTime() + frequency);
        redisService.expireKey(k, frequency, TimeUnit.SECONDS);
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildCreateResponse> createAction(GuildCreateRequest params) {

        long userId = RequestContext.getUserId();
        User user = userService.getUser(userId);

        String lockKey = this.getJoinGuildLockKey(userId);
        if (!redisLock.lock(lockKey, String.valueOf(userId))) {
            // 加锁失败，返回请求重复，让客户端重新请求
            return Result.Error(ErrorCode.REQUEST_DATA_DUPLICATE);
        }

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser != null && guildUser.getGuildId() > 0) {
            return Result.Error(ErrorCode.GUILD_JOINED);
        }

        String guildName = params.getGuildName();
        // 避免昵称重复
        String nameId = CryptUtil.md5(guildName);
        if (!redisLock.lockWithOutRetry(nameId, nameId, 30000)) {
            return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
        }

        // 判断昵称是否重复
        Guild guild = guildDao.getByName(guildName);
        if (guild != null) {
            return Result.Error(ErrorCode.GUILD_NAME_REPEAT);
        }

        // 判断语言配置是否存在
        if (!guildSupport.checkLanguageConfig(params.getLanguage())) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 判断公会图标是否存在
        int guildIcon = params.getGuildIcon();
        int guildIconBg = params.getGuildIconBg();
        if (!guildSupport.checkGuildIcon(guildIcon, guildIconBg)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 检测公会昵称
        int checkCode = guildSupport.checkGuildName(guildName);
        if (checkCode != ErrorCode.SUCCESS) {
            return Result.Error(checkCode);
        }

        String guildIntro = params.getGuildIntro();
        checkCode = guildSupport.checkGuildIntro(guildIntro);
        if (checkCode != ErrorCode.SUCCESS) {
            return Result.Error(checkCode);
        }

        String guildNotice = params.getGuildNotice();
        checkCode = guildSupport.checkGuildNotice(guildNotice);
        if (checkCode != ErrorCode.SUCCESS) {
            return Result.Error(checkCode);
        }


        // 消耗钻石
        int needDiamonds = gameConfigManager.getGuildConfig().getGuildConst().get(101).getTypeInt();
        RewardResultSet costResultSet = null;
        if (needDiamonds > 0) {
            ResourceReward costReward = ResourceReward.valueOf(RewardResourceType.DIAMONDS, -needDiamonds);
            costResultSet = rewardService.executeReward(userId, costReward);
            if (costResultSet.isFailed()) {
                return Result.Error(costResultSet.getResultCode());
            }
        }

        // 新建公会
        Guild newGuild = this.buildNewGuild(userId, guildName, guildIntro, guildIcon, guildIconBg, params.getApplyType(), params.getApplyCondition(), params.getLanguage(), guildNotice, user.getServerId());

        guildRedisDao.updateGuildMemberCount(newGuild.getGuildId(), 1);

        if (guildUser == null) {
            guildUser = new GuildUser();
            guildUser.setUserId(userId);
            guildUser.setGuildId(0L);
            guildUserDao.setPrimaryKey(guildUser);
        }
        guildUser.setGuildLevelRecord(1);
        guildUser.setJoinTime(DateUtils.getUnixTime());
        guildUser.setGuildId(newGuild.getGuildId());
        guildUser.setPosition(GuildPosition.PRESIDENT);

        // 删除之前的申请记录
        if (guildUser.getApplyGuildIds() != null && !guildUser.getApplyGuildIds().isEmpty()) {
            guildApplyDao.batchDelete(guildUser.getApplyGuildIds(), userId);
            guildUser.setApplyGuildIds(new ArrayList<>());
        }
        this.checkGuildFeaturesData(newGuild, guildUser);
        // 插入数据
        guildDao.insert(newGuild);
        guildUserDao.insert(guildUser);
        //更新工会战力
        guildDao.updatePowerInfo(newGuild.getGuildId(), userId, userService.getPower(userId));

        GuildCreateResponse.Builder response = GuildCreateResponse.newBuilder();
        response.setGuildDetailInfoDto(guildSupport.buildGuildDetailInfoDto(newGuild, Collections.singletonList(guildUser)));
        response.setCommonData(CommonHelper.buildCommonData(costResultSet));
        response.setGuildFeaturesDto(guildFeaturesSupport.buildGuildFeaturesDto(newGuild, guildUser));
        return Result.Success(response.build());

    }

    @DynamoDBTransactional
    @Override
    public Result<GuildSearchResponse> searchAction(GuildSearchRequest params) {
        long userId = RequestContext.getUserId();
        int serverId = userDao.getByUserId(userId).getServerId();

        boolean isOnlyJoinable = params.getIsOnlyJoinable();
        List<Long> excludeGuildIds = new ArrayList<>(params.getExcludeGuildIdsList());

        int condition = 0;
        if (isOnlyJoinable) {       // 只获取可以加入的公会
            condition = guildSupport.getConditionValue(userId);
        }

        List<Guild> guilds = null;
        if (params.getType() == 0) {
            guilds = guildSupport.getRecommendList(isOnlyJoinable, condition, excludeGuildIds, serverId);
        } else if (params.getType() == 1) {
            String value = params.getValue();
            if (StringUtils.isEmpty(value) || value.length() >= 50) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            // 根据名称查询
            guilds = guildDao.searchByName(value, null, isOnlyJoinable, condition, serverId);
            List<Long> guildIds = guildSupport.getGuildIds(guilds);

            // 查询文本是数字根据公会id查询一次
            if (value.matches("[0-9]+")) {
                long searchGuildId = Long.parseLong(value);
                if (!guildIds.contains(searchGuildId)) {
                    Guild guild = guildDao.getByGuildId(searchGuildId);
                    if (guild != null) {
                        if (!isOnlyJoinable // 如果 isOnlyJoinable 为 false
                                || ((guild.getGuildApplyCondition() == 0 || condition > guild.getGuildApplyCondition()) // guildApplyCondition 为 0 或者 condition 大于 guildApplyCondition
                                && guild.getGuildMembersCount() > 0 // guildMembersCount 大于 0
                                && guild.getGuildMaxMembersCount() > guild.getGuildMembersCount())
                                && guild.getServerId() == serverId) { // guildMaxMembersCount 大于 guildMembersCount
                            guilds.add(guild); // 添加到结果集
                        }
                    }
                }
            }
        } else if (params.getType() == 2) {
            int limit = gameConfigManager.getGuildConfig().getGuildConstEntity(110).getTypeInt();
            int maxPage = gameConfigManager.getGuildConfig().getGuildConstEntity(112).getTypeInt();
            int pageIndex = params.getPageIndex();
            if (pageIndex == 0) {
                pageIndex = 1;
            }
            int from = (pageIndex - 1) * limit;
            if (maxPage >= from) {
                guilds = guildDao.getRankList(from, limit);
            }
        }


        GuildSearchResponse.Builder response = GuildSearchResponse.newBuilder();
        if (guilds != null && !guilds.isEmpty()) {
            guilds = guilds.stream().filter(temp -> temp.getGuildIsDissolved() == 0).collect(Collectors.toList());
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            List<GuildInfoDto> guildInfoDtos = guildSupport.buildGuildInfoDtoList(guilds, guildUser);
            response.addAllGuildInfoDtos(guildInfoDtos);
        }
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildGetDetailResponse> getDetailAction(GuildGetDetailRequest params) {
        long guildId = params.getGuildId();
        if (guildId <= 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildId);
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
//        if (guildStatus != ErrorCode.SUCCESS) {
//            return Result.Error(guildStatus);
//        }
        GuildGetDetailResponse.Builder response = GuildGetDetailResponse.newBuilder();
        response.setGuildDetailInfoDto(guildSupport.buildGuildDetailInfoDto(guild));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildGetMemberListResponse> getMemberListAction(GuildGetMemberListRequest params) {
        long guildId = params.getGuildId();
        if (guildId <= 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildId);
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        GuildGetMemberListResponse.Builder response = GuildGetMemberListResponse.newBuilder();
        response.addAllGuildMemberInfoDtos(guildSupport.getGuildMemberInfoDtoList(guild.getGuildId()));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildModifyResponse> modifyAction(GuildModifyRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        // 判断职位
        if (guildUser == null || guildUser.getGuildId() == 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }
        // 检测权限
        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.MODIFY)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }

        // 判断语言配置是否存在
        if (!guildSupport.checkLanguageConfig(params.getLanguage())) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 判断公会图标是否存在
        int guildIcon = params.getGuildIcon();
        int guildIconBg = params.getGuildIconBg();
        if (!guildSupport.checkGuildIcon(guildIcon, guildIconBg)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 是否推送公会修改公会信息消息
        boolean isPushGuildModifyMessage = false;

        String guildName = params.getGuildName().trim();
        RewardResultSet costResultSet = null;
        // 判断名称是否修改
        if (!StringUtils.isEmpty(guildName) && !guild.getGuildName().equals(guildName)) {
            // 消耗钻石
            int needDiamonds = gameConfigManager.getGuildConfig().getGuildConst().get(102).getTypeInt();
            if (needDiamonds > 0) {
                ResourceReward costReward = ResourceReward.valueOf(RewardResourceType.DIAMONDS, -needDiamonds);
                costResultSet = rewardService.executeReward(userId, costReward);
                if (costResultSet.isFailed()) {
                    return Result.Error(costResultSet.getResultCode());
                }
            }

            // 避免昵称重复
            String nameId = CryptUtil.md5(guildName);
            if (!redisLock.lockWithOutRetry(nameId, nameId, 30000)) {
                return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
            }

            // 判断昵称是否重复
            Guild findGuild = guildDao.getByName(guildName);
            if (findGuild != null) {
                return Result.Error(ErrorCode.GUILD_NAME_REPEAT);
            }

            // 检测公会昵称
            int checkCode = guildSupport.checkGuildName(guildName);
            if (checkCode != ErrorCode.SUCCESS) {
                return Result.Error(checkCode);
            }

            guild.setGuildName(guildName);

            isPushGuildModifyMessage = true;
        }

        if (params.getIsModifyGuildIntro()) {
            String guildIntro = params.getGuildIntro();
            int checkCode = guildSupport.checkGuildIntro(guildIntro);
            if (checkCode != ErrorCode.SUCCESS) {
                return Result.Error(checkCode);
            }
            guild.setGuildIntro(guildIntro);
            isPushGuildModifyMessage = true;
        }

        if (params.getIsModifyGuildNotice()) {
            String guildNotice = params.getGuildNotice();
            int checkCode = guildSupport.checkGuildNotice(guildNotice);
            if (checkCode != ErrorCode.SUCCESS) {
                return Result.Error(checkCode);
            }
            guild.setGuildNotice(guildNotice);
            isPushGuildModifyMessage = true;
        }

        if (params.getGuildIcon() > 0 && params.getGuildIcon() != guild.getGuildIcon()) {
            guild.setGuildIcon(params.getGuildIcon());
            isPushGuildModifyMessage = true;
        }
        if (params.getGuildIconBg() > 0 && params.getGuildIconBg() != guild.getGuildIconBg()) {
            guild.setGuildIconBg(params.getGuildIconBg());
            isPushGuildModifyMessage = true;
        }

        guild.setGuildApplyType(params.getApplyType());
        guild.setGuildApplyCondition(params.getApplyCondition());
        guild.setGuildLanguage(params.getLanguage());
        guildDao.updateGuildInfo(guild);

        // 推送公会信息修改
        if (isPushGuildModifyMessage) {
            guildMessageService.pushGuildInfoModify(guild);
        }


        GuildModifyResponse.Builder response = GuildModifyResponse.newBuilder();
        response.setGuildInfoDto(guildSupport.buildGuildInfoDto(guild));
        response.setCommonData(CommonHelper.buildCommonData(costResultSet));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildDismissResponse> dismissAction(GuildDismissRequest params) {
        long userId = RequestContext.getUserId();
        int serverId = RequestContext.getUser().getServerId();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        // 验证权限
        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.DISMISS)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        if (guild != null) {
            long guildId = guild.getGuildId();
            // 更新解散公会标记
            guild.setGuildName(guild.getGuildName() + "-dismiss-" + DateUtils.getUnixTime());
            guild.setGuildMembersCount(0);
            guild.setGuildApplyJoinedCount(0);
            guild.setGuildIsDissolved(GuildState.DISMISS);
            guildDao.updateIsDissolved(guild);
            guildDao.updateGuildMembersCount(guild);
            guildDao.updateGuildApplyJoinedCount(guild);
            guildDao.updateGuildName(guild);

            // 更新redis 数据
            guildRedisDao.removeGuildMember(guild.getGuildId());

            // 更新所有公会成员数据
            guildUser.setGuildId(0L);
            guildUser.setPosition(GuildPosition.MEMBER);
            guildUser.setJoinTime(0L);
            guildUserDao.updateJoinTime(guildUser);
            guildUserDao.updateGuildId(guildUser);
            guildUserDao.updatePosition(guildUser);

            List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guild.getGuildId());
            List<Long> userIds = new ArrayList<>();
            for (GuildUser user : guildUsers) {
                user.setGuildId(0L);
                user.setPosition(GuildPosition.MEMBER);
                guildUser.setJoinTime(0L);
                guildUserDao.updateJoinTime(guildUser);
                guildUserDao.updateGuildId(user);
                guildUserDao.updatePosition(user);

                userIds.add(user.getUserId());
            }

            // 通知离开分组
            imService.leaveGroup(userIds, IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guildId));

            // 删除当天工会boss排名
            String key = RankHelper.getGuildBossKey(serverId, DateUtils.getSystemResetTime());
            RankHelper.removeRank(key, guild.getGuildId());

            //工会战力
            guildDao.deletePowerInfo(guild.getGuildId());
        }
        GuildDismissResponse.Builder response = GuildDismissResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildApplyJoinResponse> applyJoinAction(GuildApplyJoinRequest params) {
        long userId = RequestContext.getUserId();
        int serverId = RequestContext.getUser().getServerId();

        String lockKey = this.getJoinGuildLockKey(userId);

        long guildId = params.getGuildId();

        // 判断公会状态
        Guild guild = guildDao.getByGuildId(guildId);
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        if (serverId != guild.getServerId()) {
            return Result.Error(ErrorCode.GUILD_NOT_EXIST);
        }

        if (guild.getGuildApplyType() == 0 && !redisLock.lock(lockKey, String.valueOf(userId))) {
            // 加锁失败，返回请求重复，让客户端重新请求
            return Result.Error(ErrorCode.REQUEST_DATA_DUPLICATE);
        }

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser != null && guildUser.getGuildId() > 0) {
            return Result.Error(ErrorCode.GUILD_JOINED);
        }
        User user = userService.getUser(userId);

        // 成员数量已满
        if (guild.getGuildMembersCount() >= guild.getGuildMaxMembersCount()) {
            return Result.Error(ErrorCode.GUILD_MAX_MEMBER_ERROR);
        }

        // 判断入会条件
        if (guild.getGuildApplyCondition() > 0) {
            int condition = guildSupport.getConditionValue(userId);
            if (guild.getGuildApplyCondition() >= condition) {
                return Result.Error(ErrorCode.GUILD_APPLY_CONDITION_ERROR);
            }
        }

        GuildApplyJoinResponse.Builder response = GuildApplyJoinResponse.newBuilder();
        if (guild.getGuildApplyType() == 0) {

            // 公会成员数量+1(防止并发使用redis原子操作)
            int guildMemberCount = guildRedisDao.incrGuildMemberCount(guildId, 1);
            // 公会人数已满
            if (guildMemberCount > guild.getGuildMaxMembersCount()) {
                guildRedisDao.updateGuildMemberCount(guildId, guild.getGuildMaxMembersCount());
                return Result.Error(ErrorCode.GUILD_MAX_MEMBER_ERROR);
            }

            // 更新用户数据
            guildUser = this.updateGuildUserWhenJoin(userId, guild, guildUser);
            // 更新公会人数
            guildDao.updateGuildMembersCount(guild, 1);

            GuildDetailInfoDto guildDetailInfoDto = guildSupport.buildGuildDetailInfoDto(guild, Collections.singletonList(guildUser));
            response.setGuildDetailInfoDto(guildDetailInfoDto);
            response.setGuildFeaturesDto(guildFeaturesSupport.buildGuildFeaturesDto(guild, guildUser));

            // 推送有人加入公会
            List<GuildMemberInfoDto> guildMemberInfoDtos = guildSupport.getGuildMemberInfoDtoList(Collections.singletonList(guildUser));
            guildMessageService.pushUserJoin(guild.getGuildId(), guildMemberInfoDtos);
            //更新工会战力
            guildDao.updatePowerInfo(guild.getGuildId(), user.getUserId(), userService.getPower(userId));

        } else {
            List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guildId);

            if (guildUser == null) {
                guildUser = new GuildUser();
                guildUser.setUserId(userId);
                guildUser.setGuildId(0L);
                guildUserDao.setPrimaryKey(guildUser);
            }

            if (guildUser.getApplyGuildIds() == null) {
                guildUser.setApplyGuildIds(new ArrayList<>());
            }

            int maxCount = gameConfigManager.getGuildConfig().getGuildConstEntity(111).getTypeInt();
            if (guildUser.getApplyGuildIds().size() >= maxCount) {
                return Result.Error(ErrorCode.GUILD_USER_APPLY_COUNT_MAX);
            }
            // 判断是否已申请
            if (guildUser.getApplyGuildIds().contains(guildId)) {
                return Result.Error(ErrorCode.GUILD_APPLIED);
            }

            // 最大申请数量 = 最大成员数量 * 2
            int guildApplyJoinedCount = guild.getGuildApplyJoinedCount();
            if (guildApplyJoinedCount >= guild.getGuildMaxMembersCount() * 2) {
                return Result.Error(ErrorCode.GUILD_APPLY_COUNT_MAX);
            }

            // 更新数据
            guild.setGuildApplyJoinedCount(guild.getGuildApplyJoinedCount() + 1);
            guildDao.updateGuildApplyJoinedCount(guild);

            guildUser.getApplyGuildIds().add(guildId);

            guildUserDao.insert(guildUser);

            GuildApply guildApply = new GuildApply();
            guildApply.setGuildId(guildId);
            guildApply.setUserId(userId);
            guildApply.setApplyTime(DateUtils.getUnixTime());
            guildApplyDao.setPrimaryKey(guildApply);
            guildApplyDao.insert(guildApply);

            guildMessageService.publishDonationApply(guildId, guild.getGuildApplyJoinedCount()
                    , guildSupport.getGuildMemberInfoDtoList(List.of(guildUser))
                    , getPowerUserList(guildUsers, GuildPower.AGREE_JOIN));
        }
        return Result.Success(response.build());
    }

    private List<Long> getPowerUserList(List<GuildUser> guildUsers, int powerType) {
        return guildUsers.stream().filter(temp -> guildSupport
                        .checkPowerIsAvailable(temp.getPosition(), powerType))
                .map(temp -> temp.getUserId())
                .collect(Collectors.toList());
    }


    @DynamoDBTransactional
    @Override
    public Result<GuildCancelApplyResponse> cancelApplyAction(GuildCancelApplyRequest params) {
        long userId = RequestContext.getUserId();
        long guildId = params.getGuildId();

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (guildUser.getApplyGuildIds() != null && guildUser.getApplyGuildIds().contains(guildId)) {
            guildUser.getApplyGuildIds().remove(guildId);
            guildUserDao.updateApplyGuildId(guildUser);
        }

        GuildApply guildApply = guildApplyDao.getGuildApply(guildId, userId);
        if (guildApply != null) {
            // 删除数据
            guildApplyDao.delete(guildApply);
        }

        // 更新申请数量
        Guild guild = guildDao.getByGuildId(guildId);
        if (guild != null && guild.getGuildIsDissolved() == GuildState.NORMAL) {
            guild.setGuildApplyJoinedCount(guild.getGuildApplyJoinedCount() - 1);
            if (guild.getGuildApplyJoinedCount() >= 0) {
                guildDao.updateGuildApplyJoinedCount(guild);
            }
            guildMessageService.publishDonationApply(guildId, guild.getGuildApplyJoinedCount()
                    , guildSupport.getGuildMemberInfoDtoList(List.of(guildUser))
                    , getPowerUserList(guildUserDao.getAllByGuildId(guildId), GuildPower.AGREE_JOIN));
        }

        GuildCancelApplyResponse.Builder response = GuildCancelApplyResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildTechUpgradeResponse> techUpgrade(GuildTechUpgradeRequest params) {
        long userId = RequestContext.getUserId();

        UserExtend extend = userExtendDao.getByUserId(userId);
        List<List<Integer>> cost = gameConfigManager.getGuildTechLvCost().get(extend.getGuildTechLv() + 1);
        if(cost == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        RewardResultSet costResultSet = rewardService.executeCosts(userId, cost);
        if (costResultSet.isFailed()) {
            return Result.Error(costResultSet.getResultCode());
        }

        extend.setGuildTechLv(extend.getGuildTechLv() + 1);

        userExtendDao.updateGuildTechLv(extend);

//        battleRedisDao.updateGuildTechLv(userId, extend.getGuildTechLv());
        battleRedisDao.removeData(userId);

        GuildTechUpgradeResponse.Builder response = GuildTechUpgradeResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet));
        response.setLv(extend.getGuildTechLv());
        return Result.Success(response.build());
    }

    @Override
    public long getGuildId(long userId) {
        long guildId = userInfoRedisDao.getGuildId(userId);
        if (guildId == 0) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                guildId = guildUser.getGuildId();
                userInfoRedisDao.updateGuildId(userId, guildId);
            }
        }
        return guildId;
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildGetApplyListResponse> getApplyListAction(GuildGetApplyListRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.AGREE_JOIN)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }

        List<GuildApply> guildApplies = guildApplyDao.getGuildApplyListByGuildId(guildUser.getGuildId());
        List<Long> userIds = new ArrayList<>();
        Map<Long, Long> applyTime = new HashMap<>();
        for (GuildApply apply : guildApplies) {
            userIds.add(apply.getUserId());
            applyTime.put(apply.getUserId(), apply.getApplyTime());
        }

        GuildGetApplyListResponse.Builder response = GuildGetApplyListResponse.newBuilder();
        if (!userIds.isEmpty()) {
            List<GuildUser> guildUsers = guildUserDao.getListByUserIds(userIds);
            for (GuildUser gUser : guildUsers) {
                gUser.setApplyTime(applyTime.get(gUser.getUserId()));
            }
            response.addAllApplyList(guildSupport.getGuildMemberInfoDtoList(guildUsers));
        }
        // 校正数据
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        if (guild.getGuildApplyJoinedCount() != response.getApplyListCount()) {
            guild.setGuildApplyJoinedCount(response.getApplyListCount());
            guildDao.updateGuildApplyJoinedCount(guild);
        }

        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildAgreeJoinResponse> agreeJoinAction(GuildAgreeJoinRequest params) {
        long userId = RequestContext.getUserId();
        List<Long> agreeUserIds = params.getUserIdsList();
        if (agreeUserIds.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        agreeUserIds = agreeUserIds.stream().distinct().collect(Collectors.toList());
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        User user = userService.getUser(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        // 判断权限
        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.AGREE_JOIN)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        GuildAgreeJoinResponse.Builder response = GuildAgreeJoinResponse.newBuilder();
        // 查询申请数据
        List<GuildApply> guildApplyList = guildApplyDao.getListByUserIds(guildUser.getGuildId(), agreeUserIds);
        if (guildApplyList.isEmpty()) {
            // 申请数据为空 提示已加入其他公会
            response.addAllJoinOtherGuildUserIds(agreeUserIds);
            return Result.Success(response.build());
        }

        List<GuildUser> successUsers = new ArrayList<>();
        List<GuildUser> agreeUsers = guildUserDao.getListByUserIds(agreeUserIds);

        Map<Long, GuildApply> applyHashMap = guildApplyList.stream().collect(Collectors.toMap(GuildApply::getUserId, guildApply -> guildApply));

        int guildMemberCount = 0;
        boolean isGuildMemberMax = false;

        List<Long> pushJoinSuccessUserIds = new ArrayList<>();

        for (GuildUser agreeUser : agreeUsers) {
            GuildApply guildApply = applyHashMap.getOrDefault(agreeUser.getUserId(), null);
            // 判断用户申请数据是否合法
            if (guildApply == null) {
                continue;
            }

            // 此用户已加入其他公会
            if (agreeUser.getGuildId() > 0) {
                guildApplyDao.deleteNow(guildApply);
                response.addJoinOtherGuildUserIds(agreeUser.getUserId());
                // 移除已申请的数据
                guildApplyDao.delete(guildApply);
                continue;
            }

            String lockKey = this.getJoinGuildLockKey(agreeUser.getUserId());
            // 其他线程正在处理当前用户加入公会的请求, 直接返回此用户已加入其他公会
            if (!redisLock.lockWithOutRetry(lockKey, String.valueOf(userId))) {
                guildApplyDao.deleteNow(guildApply);
                if (agreeUser.getApplyGuildIds() != null && agreeUser.getApplyGuildIds().contains(guild.getGuildId())) {
                    agreeUser.getApplyGuildIds().remove(guild.getGuildId());
                    guildUserDao.updateApplyGuildIdNow(agreeUser);
                }
                response.addJoinOtherGuildUserIds(agreeUser.getUserId());
                continue;
            }

            // 判断公会人数
            // 公会成员数量+1
            guildMemberCount = guildRedisDao.incrGuildMemberCount(guild.getGuildId(), 1);
            // 公会人数已满
            if (guildMemberCount > guild.getGuildMaxMembersCount()) {
                guildRedisDao.updateGuildMemberCount(guild.getGuildId(), guild.getGuildMaxMembersCount());
                isGuildMemberMax = true;
                break;
            }

            // 更新用户数据
            agreeUser = this.updateGuildUserWhenJoin(agreeUser.getUserId(), guild, agreeUser);

            successUsers.add(agreeUser);
            guildApplyDao.delete(guildApply);

            pushJoinSuccessUserIds.add(agreeUser.getUserId());
        }

        // 公会人数上限
        if (isGuildMemberMax && successUsers.size() == 0) {
            return Result.Error(ErrorCode.GUILD_APPLY_COUNT_MAX);
        }

        // 更新公会成员数量
        guildDao.updateGuildMembersCount(guild, successUsers.size());

        List<GuildMemberInfoDto> guildMemberInfoDtos = guildSupport.getGuildMemberInfoDtoList(successUsers);
        response.addAllGuildMemberInfoDto(guildMemberInfoDtos);

        // 更新申请人数数量
        guild.setGuildApplyJoinedCount(guild.getGuildApplyJoinedCount() - agreeUserIds.size());
        if (guild.getGuildApplyJoinedCount() <= 0) {
            guild.setGuildApplyJoinedCount(0);
        }
        guildDao.updateGuildApplyJoinedCount(guild);
        response.setApplyCount(guild.getGuildApplyJoinedCount());
        // 推送消息给客户端
        if (!pushJoinSuccessUserIds.isEmpty()) {
            guildMessageService.pushJoinSuccess(guild.getGuildId(), guild.getGuildName(), pushJoinSuccessUserIds);
            guildMessageService.pushUserJoin(guild.getGuildId(), guildMemberInfoDtos);
        }

        List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guild.getGuildId());
        guildMessageService.publishDonationApply(guild.getGuildId(), guild.getGuildApplyJoinedCount()
                , guildSupport.getGuildMemberInfoDtoList(agreeUsers)
                , getPowerUserList(guildUsers, GuildPower.AGREE_JOIN));

        //更新工会战力
        guildDao.updatePowerInfo(guild.getGuildId(), user.getUserId(), userService.getPower(userId));

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildRefuseJoinResponse> refusedJoinAction(GuildRefuseJoinRequest params) {
        long userId = RequestContext.getUserId();
        List<Long> refuseUserIds = params.getUserIdsList();
        if (refuseUserIds.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        // 判断权限
        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.AGREE_JOIN)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        List<GuildUser> refuseGuildUsers = guildUserDao.getListByUserIds(refuseUserIds);
        for (GuildUser gUser : refuseGuildUsers) {
            // 删除用户申请记录
            if (gUser.getApplyGuildIds() != null && !gUser.getApplyGuildIds().isEmpty()) {
                gUser.getApplyGuildIds().remove(guild.getGuildId());
                guildUserDao.updateApplyGuildId(gUser);
            }
        }

        // 删除公会申请记录
        List<GuildApply> guildApplyList = guildApplyDao.getListByUserIds(guildUser.getGuildId(), refuseUserIds);
        for (GuildApply guildApply : guildApplyList) {
            guildApplyDao.delete(guildApply);
        }

        guild.setGuildApplyJoinedCount(guild.getGuildApplyJoinedCount() - refuseUserIds.size());
        if (guild.getGuildApplyJoinedCount() <= 0) {
            guild.setGuildApplyJoinedCount(0);
        }
        guildDao.updateGuildApplyJoinedCount(guild);
        guildMessageService.publishDonationApply(guild.getGuildId(), guild.getGuildApplyJoinedCount()
                , guildSupport.getGuildMemberInfoDtoList(refuseGuildUsers)
                , getPowerUserList(guildUserDao.getAllByGuildId(guild.getGuildId()), GuildPower.AGREE_JOIN));

        GuildRefuseJoinResponse.Builder response = GuildRefuseJoinResponse.newBuilder();
        response.setApplyCount(guild.getGuildApplyJoinedCount());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildKickOutResponse> kickOutAction(GuildKickOutRequest params) {

        long userId = RequestContext.getUserId();
        long kickOutUserId = params.getUserId();
        if (userId == kickOutUserId) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        User user = userService.getUser(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        // 判断权限
        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.KICK_OUT)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        GuildUser kickOutUser = guildUserDao.getByUserId(kickOutUserId);
        if (kickOutUser == null) {
            return Result.Error(ErrorCode.GUILD_USER_NOT_EXIST);
        }

        // 是否同一公会
        if (!guild.getGuildId().equals(kickOutUser.getGuildId())) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 判断权限
        if (guildUser.getPosition() >= kickOutUser.getPosition()) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }

        // 如果是副会长, 更新副会长数量
        if (kickOutUser.getPosition() == GuildPosition.VICE_PRESIDENT) {
            guildDao.updateGuildVicePresidentCount(guild, -1, 0);
        }

        // 如果是管理，更新管理数量
        if (kickOutUser.getPosition() == GuildPosition.MANAGER) {
            guildDao.updateGuildManagerCount(guild, -1, 0);
        }

        // 更新公会成员数量
        guildRedisDao.incrGuildMemberCount(guild.getGuildId(), -1);
        guildDao.updateGuildMembersCount(guild, -1);

        // 记录数据
        guildUserActiveRecordDao.saveRecord(kickOutUser);

        // 更新数据
        kickOutUser.setGuildId(0L);
        kickOutUser.setJoinTime(0L);
        kickOutUser.setApplyTime(0L);
        kickOutUser.setPosition(GuildPosition.MEMBER);
        kickOutUser.setActive(0);
        kickOutUser.setDailyActive(0);
        kickOutUser.setWeeklyActive(0);

        guildUserDao.updateGuildId(kickOutUser);
        guildUserDao.updateJoinTime(kickOutUser);
        guildUserDao.updateApplyTime(kickOutUser);
        guildUserDao.updatePosition(kickOutUser);
        guildUserDao.updateAllActive(kickOutUser);

        // 通知im离开
        imService.leaveGroup(userId, IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guild.getGuildId()));

        // 更新被踢出公会的信息
        User fromUser = userDao.getByUserId(userId);
        String fromUserNickName = Optional.ofNullable(fromUser.getNickName()).orElse("");
        JSONObject beKickedOutInfoObj = new JSONObject();
        beKickedOutInfoObj.put("guildId", guild.getGuildId());
        beKickedOutInfoObj.put("guildName", guild.getGuildName());
        beKickedOutInfoObj.put("fromUserId", userId);
        beKickedOutInfoObj.put("fromUserNickName", fromUserNickName);
        beKickedOutInfoObj.put("fromUserPosition", guildUser.getPosition());
        kickOutUser.setBeKickedOutInfo(beKickedOutInfoObj.toJSONString());
        guildUserDao.updateBeKickedOutInfo(kickOutUser);

        // 推送有人被踢出公会消息
        guildMessageService.pushKickedOut(guildUser.getGuildId(), kickOutUserId, userId, guildUser.getPosition());
        guildMessageService.pushUserBeKickedOut(guild.getGuildId(), kickOutUserId, userId, guildUser.getPosition());

        // 移除捐赠请求道具数据
        guildDonationService.removeDonationRequestItems(guild.getGuildId(), kickOutUser.getUserId());

        //更新工会战力
        guildDao.removePowerInfo(guild.getGuildId(), user.getUserId());

        GuildKickOutResponse.Builder response = GuildKickOutResponse.newBuilder();
        response.setMembers(guild.getGuildMembersCount());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildLeaveResponse> leaveAction(GuildLeaveRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        User user = userService.getUser(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }

        // 会长不允许离开
        if (guildUser.getPosition() == GuildPosition.PRESIDENT) {
            return Result.Error(ErrorCode.GUILD_TRANSFER_PRESIDENT_FIRST);
        }

        if (guildUser.getGuildId() > 0) {
            long guildId = guildUser.getGuildId();
            Guild guild = guildDao.getByGuildId(guildId);
            int guildStatus = guildSupport.guildStatus(guild);
            if (guildStatus != ErrorCode.SUCCESS) {
                return Result.Error(guildStatus);
            }

            // 如果是副会长, 更新副会长数量
            if (guildUser.getPosition() == GuildPosition.VICE_PRESIDENT) {
                guildDao.updateGuildVicePresidentCount(guild, -1, 0);
            }

            if (guildUser.getPosition() == GuildPosition.MANAGER) {
                guildDao.updateGuildManagerCount(guild, -1, 0);
            }

            // 更新公会成员数量
            guildRedisDao.incrGuildMemberCount(guild.getGuildId(), -1);
            guildDao.updateGuildMembersCount(guild, -1);

            // 记录数据
            guildUserActiveRecordDao.saveRecord(guildUser);

            // 更新数据
            guildUser.setGuildId(0L);
            guildUser.setJoinTime(0L);
            guildUser.setApplyTime(0L);
            guildUser.setActive(0);
            guildUser.setDailyActive(0);
            guildUser.setWeeklyActive(0);
            guildUser.setPosition(GuildPosition.MEMBER);

            guildUserDao.updateGuildId(guildUser);
            guildUserDao.updateJoinTime(guildUser);
            guildUserDao.updateApplyTime(guildUser);
            guildUserDao.updatePosition(guildUser);
            guildUserDao.updateActive(guildUser);
            guildUserDao.updateDailyActive(guildUser);
            guildUserDao.updateWeeklyActive(guildUser);

            // 通知im离开
            imService.leaveGroup(userId, IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guildId));


            // 推送有人退出公会消息
            guildMessageService.pushUserLeave(guildId, guildUser.getUserId());

            // 移除捐赠请求道具数据
            guildDonationService.removeDonationRequestItems(guildId, guildUser.getUserId());

            //更新工会战力
            guildDao.removePowerInfo(guild.getGuildId(), user.getUserId());
        }

        GuildLeaveResponse.Builder response = GuildLeaveResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildUpPositionResponse> upPositionAction(GuildUpPositionRequest params) {
        long userId = RequestContext.getUserId();
        long upPositionUserId = params.getUserId();
        if (userId == upPositionUserId) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        int position = params.getPosition();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        // 判断权限
        int checkPower = 0;
        if (position == 2) {
            // 任命副会长
            checkPower = GuildPower.CHANGE_VICE_PRESIDENT;
        } else if (position == 3 || position == 4) {
            // 任命管理
            checkPower = GuildPower.CHANGE_MANAGER;
        }

        // 判断权限
        if (checkPower == 0 || !guildSupport.checkPowerIsAvailable(guildUser.getPosition(), checkPower)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }

        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        GuildUser upPositionUser = guildUserDao.getByUserId(upPositionUserId);
        if (upPositionUser == null) {
            return Result.Error(ErrorCode.GUILD_USER_NOT_EXIST);
        }
        // 是否同一公会
        if (!guild.getGuildId().equals(upPositionUser.getGuildId())) {
            return Result.Error(ErrorCode.GUILD_USER_NOT_EXIST);
        }
        // 判断权限
        if (guildUser.getPosition() >= upPositionUser.getPosition()) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }

        if (upPositionUser.getPosition() != position) {

            if (position == GuildPosition.VICE_PRESIDENT) {   // 副会长数量是否足够
                int maxCnt = guildSupport.getGuildMaxMemberCount(guild.getGuildLevel(), GuildPosition.VICE_PRESIDENT);
                if (guild.getGuildVicePresidentCount() >= maxCnt) {
                    return Result.Error(ErrorCode.GUILD_POSITION_COUNT_ERROR);
                }
                guildDao.updateGuildVicePresidentCount(guild, 1, maxCnt);
            } else if (position == GuildPosition.MANAGER) {   // 管理人数是否足够
                int maxCnt = guildSupport.getGuildMaxMemberCount(guild.getGuildLevel(), GuildPosition.MANAGER);
                if (guild.getGuildManagerCount() >= maxCnt) {
                    return Result.Error(ErrorCode.GUILD_POSITION_MANAGER_COUNT_ERROR);
                }
                guildDao.updateGuildManagerCount(guild, 1, maxCnt);
            }
            int oldPosition = upPositionUser.getPosition();
            // 之前是副会长则公会副会长人数-1
            if (upPositionUser.getPosition() == GuildPosition.VICE_PRESIDENT) {
                guildDao.updateGuildVicePresidentCount(guild, -1, 0);
            }

            // 之前是管理人数 - 1
            if (upPositionUser.getPosition() == GuildPosition.MANAGER) {
                guildDao.updateGuildManagerCount(guild, -1, 0);
            }

            upPositionUser.setPosition(position);
            guildUserDao.updatePosition(upPositionUser);

            // 推送自己职位发生变化
            guildMessageService.pushPositionChange(guildUser.getGuildId(), upPositionUser.getUserId(), userId, guildUser.getPosition(), oldPosition, upPositionUser.getPosition());
        }


        GuildUpPositionResponse.Builder response = GuildUpPositionResponse.newBuilder();
        response.setGuildMemberInfoDto(guildSupport.getGuildMemberInfoDtoList(Collections.singletonList(upPositionUser)).get(0));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildTransferPresidentResponse> transferPresidentAction(GuildTransferPresidentRequest params) {
        long userId = RequestContext.getUserId();
        long transferUserId = params.getUserId();
        if (userId == transferUserId) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        // 权限是否足够
        if (guildUser.getPosition() != GuildPosition.PRESIDENT) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        // 是否同一公会
        GuildUser transferUser = guildUserDao.getByUserId(transferUserId);
        if (transferUser == null) {
            return Result.Error(ErrorCode.GUILD_USER_NOT_EXIST);
        }
        int oldPosition = transferUser.getPosition();
        if (!guild.getGuildId().equals(transferUser.getGuildId())) {
            return Result.Error(ErrorCode.GUILD_USER_NOT_EXIST);
        }

        // 如果是副会长, 更新副会长数量
        if (transferUser.getPosition() == GuildPosition.VICE_PRESIDENT) {
            guildDao.updateGuildVicePresidentCount(guild, -1, 0);
        }
        if (transferUser.getPosition() == GuildPosition.MANAGER) {
            guildDao.updateGuildManagerCount(guild, -1, 0);
        }

        // 更新数据
        transferUser.setPosition(GuildPosition.PRESIDENT);
        guildUserDao.updatePosition(transferUser);

        guildUser.setPosition(GuildPosition.MEMBER);
        guildUserDao.updatePosition(guildUser);

        guild.setGuildPresidentUserId(transferUserId);
        guildDao.updateGuildPresidentUserId(guild);

        GuildTransferPresidentResponse.Builder response = GuildTransferPresidentResponse.newBuilder();
        response.addAllGuildMemberInfoDtos(guildSupport.getGuildMemberInfoDtoList(Lists.newArrayList(guildUser, transferUser)));

        // 推送自己职位发生变化
        guildMessageService.pushPositionChange(guildUser.getGuildId(), transferUserId, userId, guildUser.getPosition(), oldPosition, transferUser.getPosition());

        return Result.Success(response.build());
    }


    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<GuildAutoJoinResponse> autoJoinAction(GuildAutoJoinRequest params) {
        long userId = RequestContext.getUserId();
        int serverId = RequestContext.getUser().getServerId();

        String lockKey = this.getJoinGuildLockKey(userId);
        if (!redisLock.lock(lockKey, String.valueOf(userId))) {
            // 加锁失败，返回请求重复，让客户端重新请求
            return Result.Error(ErrorCode.REQUEST_DATA_DUPLICATE);
        }
        // 是否已加入公会
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        User user = userService.getUser(userId);
        if (guildUser != null && guildUser.getGuildId() > 0) {
            return Result.Error(ErrorCode.GUILD_JOINED);
        }

        int condition = guildSupport.getConditionValue(userId);
        List<Guild> guilds = guildDao.getAutoJoinGuilds(condition, serverId);

        // 没有满足条件可以自动加入的公会
        if (guilds.isEmpty()) {
            return Result.Error(ErrorCode.GUILD_AUTO_JOIN_NOT_FOUND);
        }

        Guild findGuild = null;
        int findGuildMemberCount = 0;
        for (Guild guild : guilds) {
            long guildId = guild.getGuildId();
            // 公会成员数量+1(防止并发使用redis原子操作)
            findGuildMemberCount = guildRedisDao.incrGuildMemberCount(guildId, 1);
            // 公会人数已满
            if (findGuildMemberCount > guild.getGuildMaxMembersCount()) {
                guildRedisDao.updateGuildMemberCount(guildId, guild.getGuildMaxMembersCount());
                continue;
            }
            findGuild = guild;
            break;
        }

        // 没有满足条件可以自动加入的公会
        if (findGuild == null) {
            return Result.Error(ErrorCode.GUILD_AUTO_JOIN_NOT_FOUND);
        }

        // 更新用户数据
        guildUser = this.updateGuildUserWhenJoin(userId, findGuild, guildUser);
        // 更新公会人数
        guildDao.updateGuildMembersCount(findGuild, 1);

        //更新工会战力
        guildDao.updatePowerInfo(findGuild.getGuildId(), user.getUserId(), userService.getPower(userId));


        GuildDetailInfoDto guildDetailInfoDto = guildSupport.buildGuildDetailInfoDto(findGuild, Collections.singletonList(guildUser));
        GuildAutoJoinResponse.Builder response = GuildAutoJoinResponse.newBuilder();
        response.setGuildDetailInfoDto(guildDetailInfoDto);
        response.setGuildFeaturesDto(guildFeaturesSupport.buildGuildFeaturesDto(findGuild, guildUser));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildGetFeaturesInfoResponse> getFeaturesInfoAction(GuildGetFeaturesInfoRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }
        this.checkGuildFeaturesData(guild, guildUser);
        GuildGetFeaturesInfoResponse.Builder response = GuildGetFeaturesInfoResponse.newBuilder();
        response.setGuildFeaturesDto(guildFeaturesSupport.buildGuildFeaturesDto(guild, guildUser));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildLevelUpResponse> guildLevelUpAction(GuildLevelUpRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        if (!guildSupport.checkPowerIsAvailable(guildUser.getPosition(), GuildPower.LEVEL_UP)) {
            return Result.Error(ErrorCode.GUILD_POSITION_ERROR);
        }
        long guildId = guildUser.getGuildId();

        String lockKey = "guild_level_up:" + guildId;
        if (!redisLock.lock(lockKey, String.valueOf(userId))) {
            // 加锁失败，返回请求重复，让客户端重新请求
            return Result.Error(ErrorCode.REQUEST_DATA_DUPLICATE);
        }

        Guild guild = guildDao.getByGuildId(guildId);
        // 判断公会状态
        int guildStatus = guildSupport.guildStatus(guild);
        if (guildStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildStatus);
        }

        int maxLevel = gameConfigManager.getGuildConfig().getGuildLevel().size();
        if (guild.getGuildLevel() < maxLevel) {
            int fromLevel = guild.getGuildLevel();
            int toLevel = fromLevel;
            int toExp = guild.getGuildExp();

            GuildLevelEntity guildLevelEntity = gameConfigManager.getGuildConfig().getGuildLevelEntity(toLevel);
            if (toExp >= guildLevelEntity.getExp()) {
                toLevel++;
                toExp -= guildLevelEntity.getExp();
            }
//
//        while (true) {
//            if (toLevel >= maxLevel) {
//                break;
//            }
//            GuildLevelEntity guildLevelEntity = gameConfigManager.getGuildConfig().getGuildLevelEntity(toLevel);
//            int needExp = guildLevelEntity.getExp();
//            if (needExp > toExp) {
//                break;
//            }
//            toLevel ++;
//            toExp -= needExp;
//        }

            if (toLevel > fromLevel) {
                guildLevelEntity = gameConfigManager.getGuildConfig().getGuildLevelEntity(toLevel);
                guild.setGuildMaxMembersCount(guildLevelEntity.getMaxMemberCount());
                guildDao.updateGuildMaxMembersCount(guild);

                guild.setGuildLevel(toLevel);
                guild.setGuildExp(toExp);

                guildDao.updateGuildLevel(guild);
                guildDao.updateGuildExp(guild);

                guildUser.setGuildLevelRecord(toLevel);
                guildUserDao.updateGuildLevelRecord(guildUser);

                // 推送公会等级变化
                guildMessageService.pushGuildInfoModify(guild);

                // 是否有新增的任务
                guildTaskService.checkNewTask(guild, guildUser);
                // 是否有新增的商品
                guildShopService.checkNewShop(GuildShopType.SHOP_DAILY, guild, guildUser);
                guildShopService.checkNewShop(GuildShopType.SHOP_WEEKLY, guild, guildUser);
            }
        }


        GuildUpdateInfoDto guildUpdateInfoDto = guildFeaturesSupport.buildGuildUpdateInfo(guildId);
        GuildLevelUpResponse.Builder response = GuildLevelUpResponse.newBuilder();
        response.setGuildUpdateInfo(guildUpdateInfoDto);
        response.addAllTasks(guildTaskService.buildTaskDtoList(guildUser));
        response.addAllDailyShop(guildShopService.buildShopDtoList(GuildShopType.SHOP_DAILY, guildUser));
        response.addAllWeeklyShop(guildShopService.buildShopDtoList(GuildShopType.SHOP_WEEKLY, guildUser));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildGetMessageRecordsResponse> getMessageRecordsAction(GuildGetMessageRecordsRequest params) {
        long userId = RequestContext.getUserId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        long msgId = params.getMsgId();
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        GuildGetMessageRecordsResponse.Builder response = GuildGetMessageRecordsResponse.newBuilder();
        int pageIndex = params.getPageIndex();
        int maxPage = gameConfigManager.getGuildConfig().getGuildConstEntity(125).getTypeInt();
        if (maxPage >= pageIndex) {
            int limit = gameConfigManager.getGuildConfig().getGuildConstEntity(124).getTypeInt();
            List<GuildMessage> guildMessageList = guildMessageDao.queryMessage(guildUser.getGuildId(), msgId, limit);
            for (GuildMessage guildMessage : guildMessageList) {
                GuildPushMessageDto.Builder pushMessageDto = GuildPushMessageDto.newBuilder();
                if (guildMessage.getMessageType() == null) {
                    continue;
                }
                pushMessageDto.setMessageType(guildMessage.getMessageType());
                if (guildMessage.getRequestItem() != null) {
                    // 消息已过期
                    if (DateUtils.getUnixTime() >= guildMessage.getRequestItem().getExpiredTM()) {
                        continue;
                    }
                    // 捐赠数据
                    if (guildMessage.getRequestItem().getDonorUserId() != null && guildMessage.getRequestItem().getDonorUserId().contains(userId)) {
                        guildMessage.getRequestItem().setDonated(true);
                    }

                    pushMessageDto.setMessageContent(guildMessage.getRequestItem().toJsonString());
                } else {
                    pushMessageDto.setMessageContent(guildMessage.getMessageContent());
                }

                response.addMessageRecords(pushMessageDto.build());
            }
        }
        return Result.Success(response.build());
    }

    @Override
    public void devCreateRandomGuild(int num, int serverId) {
        long now = System.currentTimeMillis();
        for (int i = 0; i < num; i++) {
            String key = now + "_" + i;
            User newUser = userService.DevCreateRobot(serverId);

            Guild newGuild = this.buildNewGuild(newUser.getUserId(), key, key, 0, 0, 0, 0, 0, key, serverId);

            int level = RandomUtil.betweenValue(1, 5);
            newGuild.setGuildLevel(level);

            guildRedisDao.updateGuildMemberCount(newGuild.getGuildId(), 1);
            GuildUser guildUser = new GuildUser();
            guildUser.setUserId(newUser.getUserId());
            guildUser.setGuildId(0L);
            guildUserDao.setPrimaryKey(guildUser);
            guildUser.setJoinTime(DateUtils.getUnixTime());
            guildUser.setGuildId(newGuild.getGuildId());
            guildUser.setPosition(GuildPosition.PRESIDENT);

            // 插入数据
            guildDao.insert(newGuild);
            guildUserDao.insert(guildUser);
        }
    }

    // 新建公会对象
    private Guild buildNewGuild(long userId, String guildName, String guildIntro, int guildIcon, int guildIconBg, int applyType, int applyCondition, int language, String guildNotice, int serverId) {
        Guild newGuild = new Guild();
        long guildId = guildSupport.getGenerateId();
        newGuild.setGuildId(guildId);
        guildDao.setPrimaryKey(newGuild);
        newGuild.setGuildName(guildName);
        newGuild.setGuildIntro(guildIntro);
        newGuild.setGuildIcon(guildIcon);
        newGuild.setGuildIconBg(guildIconBg);
        newGuild.setGuildApplyType(applyType);
        newGuild.setGuildApplyCondition(applyCondition);
        newGuild.setGuildMembersCount(1);
        int initMemberCount = gameConfigManager.getGuildConfig().getGuildLevelEntity(1).getMaxMemberCount();
        newGuild.setGuildMaxMembersCount(initMemberCount);
        newGuild.setGuildLevel(1);
        newGuild.setGuildExp(0);
        newGuild.setGuildCreateTime(DateUtils.getUnixTime());
        newGuild.setGuildActive(0);
        newGuild.setGuildDayActive(0);
        newGuild.setGuildDayActiveTime(DateUtils.getSystemResetTime());
        newGuild.setGuildIsDissolved(GuildState.NORMAL);
        newGuild.setGuildApplyJoinedCount(0);
        newGuild.setGuildLanguage(language);
        newGuild.setGuildVicePresidentCount(0);
        newGuild.setGuildNotice(guildNotice);
        newGuild.setGuildVicePresidentCount(0);
        newGuild.setGuildManagerCount(0);
        newGuild.setGuildPresidentUserId(userId);
        newGuild.setServerId(serverId);
        return newGuild;
    }

    // 用户自动加入/申请加入不需要审批的公会时更新数据
    private GuildUser updateGuildUserWhenJoin(long userId, Guild guild, GuildUser guildUser) {
        boolean isNewUser = false;
        if (guildUser == null) {
            guildUser = new GuildUser();
            guildUser.setUserId(userId);
            guildUser.setGuildId(0L);
            guildUserDao.setPrimaryKey(guildUser);
            isNewUser = true;
        }
        guildUser.setGuildLevelRecord(guild.getGuildLevel());
        guildUser.setGuildId(guild.getGuildId());
        guildUser.setJoinTime(DateUtils.getUnixTime());
        guildUser.setPosition(GuildPosition.MEMBER);

        // 是否有之前此公会的活跃度记录
        this.checkActiveRecordWhenJoin(guildUser);

        imService.joinGroup(userId, IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guild.getGuildId()));


        if (isNewUser) {
            guildUserDao.insert(guildUser);
        } else {
            // 移除已申请的数据
            if (!CollectionUtils.isNullOrEmpty(guildUser.getApplyGuildIds())) {
                guildApplyDao.batchDelete(guildUser.getApplyGuildIds(), userId);
            }

            guildUser.setApplyGuildIds(new ArrayList<>());
            guildUserDao.updateGuildId(guildUser);
            guildUserDao.updatePosition(guildUser);
            guildUserDao.updateJoinTime(guildUser);
            guildUserDao.updateApplyGuildId(guildUser);
            guildUserDao.updateGuildLevelRecord(guildUser);

            // 更新条件
            Expression updateCondition = Expression.builder().expression("#guildId = :guildId").putExpressionName("#guildId", "guildId").putExpressionValue(":guildId", DynamoDBConvertUtil.buildAttributeValue(0)).build();
            guildUser.addUpdateCondition(updateCondition);
        }
        // 更新公会功能数据
        this.checkGuildFeaturesData(guild, guildUser);
        return guildUser;

    }

    // 加入公会redis锁的key
    private String getJoinGuildLockKey(long userId) {
        return "lock_join_guild_user:" + userId;
    }

    // 检测公会功能相关数据是否更新
    private void checkGuildFeaturesData(Guild guild, GuildUser guildUser) {
        // 夸天检测数据
        this.detectAcrossDaysData(guild, guildUser);

        // 刷新公会boss数据
        guildBossService.checkBossData(guild);
        guildBossService.checkUserData(guild, guildUser);

        // 更新用户参与公会boss的公会ID
        if (guildUser.getGuildBossModel().getGuildId() != guild.getGuildId()) {
            guildUser.getGuildBossModel().setGuildId(guild.getGuildId());
            guildUserDao.updateGuildBossModel(guildUser);
        }


        if (guildUser.getTaskRefreshCount() == null) {
            guildUser.setTaskRefreshCount(0);
            guildUserDao.updateGuildTaskRefreshCount(guildUser);
        }

        if (guildUser.getReqItemTM() == null || guildUser.getDonationItemCount() == null) {
            guildUser.setReqItemTM(0L);
            guildUser.setDonationItemCount(0);
            guildUserDao.updateDonation(guildUser);
        }

        // 是否有新增的任务
        guildTaskService.checkNewTask(guild, guildUser);
        // 是否有新增的商品
        guildShopService.checkNewShop(GuildShopType.SHOP_DAILY, guild, guildUser);
        guildShopService.checkNewShop(GuildShopType.SHOP_WEEKLY, guild, guildUser);
    }

    private void checkGuildFeaturesDataWiki(Guild guild, GuildUser guildUser) {
        long now = DateUtils.getUnixTime();
        if (guildUser.getActive() == null) {
            guildUser.setActive(0);
            guildUserDao.updateActive(guildUser);
        }

        if (guild.getGuildBossTime() == null || DateUtils.getSundayEndTimestamp() != guild.getGuildBossTime()) {
            guild.setGuildBossTime(DateUtils.getSundayEndTimestamp());
            guild.setGuildBossOpenId(guildBossService.getBossOpenId());
            guildDao.updateGuildBossInfo(guild);
        }
        guildBossService.checkUserData(guild, guildUser);

        // 刷新每日数据
        if (guildUser.getDailyTM() == null || now >= guildUser.getDailyTM()) {
            guildSignInService.refreshData(guildUser);
            guildTaskService.refreshTask(guild, guildUser);
            guildShopService.refreshShop(GuildShopType.SHOP_DAILY, guild, guildUser);
            guildUser.setDailyActive(0);
            guildUser.setDailyTM(DateUtils.getSystemResetTime());

            guildUserDao.updateDailyActive(guildUser);
            guildUserDao.updateDailyTM(guildUser);
            guildUserDao.updateDonation(guildUser);
        }

        // 刷新每周数据
        if (guildUser.getWeeklyTM() == null || now >= guildUser.getWeeklyTM()) {
            guildShopService.refreshShop(GuildShopType.SHOP_WEEKLY, guild, guildUser);
            guildUser.setWeeklyActive(0);
            guildUser.setWeeklyTM(DateUtils.getSundayEndTimestamp());
            guildUser.setDonationItemCount(0);

            guildUserDao.updateDonation(guildUser);
            guildUserDao.updateWeeklyActive(guildUser);
            guildUserDao.updateWeeklyTM(guildUser);

        }

        // 更新用户参与公会boss的公会ID
        if (guildUser.getGuildBossModel().getGuildId() != guild.getGuildId()) {
            guildUser.getGuildBossModel().setGuildId(guild.getGuildId());
            guildUserDao.updateGuildBossModel(guildUser);
        }


        if (guildUser.getTaskRefreshCount() == null) {
            guildUser.setTaskRefreshCount(0);
            guildUserDao.updateGuildTaskRefreshCount(guildUser);
        }

        if (guildUser.getReqItemTM() == null || guildUser.getDonationItemCount() == null) {
            guildUser.setReqItemTM(0L);
            guildUser.setDonationItemCount(0);
            guildUserDao.updateDonation(guildUser);
        }

        // 是否有新增的任务
        guildTaskService.checkNewTask(guild, guildUser);
        // 是否有新增的商品
        guildShopService.checkNewShop(GuildShopType.SHOP_DAILY, guild, guildUser);
        guildShopService.checkNewShop(GuildShopType.SHOP_WEEKLY, guild, guildUser);
    }

    // 加入公会时检测之前的记录
    private void checkActiveRecordWhenJoin(GuildUser guildUser) {
        GuildUserActiveRecord record = guildUserActiveRecordDao.getByGuildId(guildUser.getUserId(), guildUser.getGuildId());
        if (record != null) {
            guildUser.setActive(record.getActive());
            // 有记录并且未到重置时间
            if (record.getDailyTM() > DateUtils.getUnixTime()) {
                guildUser.setDailyActive(record.getDailyActive());
            } else {
                guildUser.setDailyActive(0);
            }
            // 有记录并且未到重置时间
            if (record.getWeeklyTM() > DateUtils.getUnixTime()) {
                guildUser.setWeeklyActive(record.getWeeklyActive());
            } else {
                guildUser.setWeeklyActive(0);
            }
        } else {
            guildUser.setActive(0);
            guildUser.setDailyActive(0);
            guildUser.setWeeklyActive(0);
        }
        guildUserDao.updateAllActive(guildUser);
    }

    // 更新工会战力
    public boolean updateGuildPower(long userId, long power) {
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser == null) {
            return false;
        }
        guildDao.updatePowerInfo(guildUser.getGuildId(), guildUser.getUserId(), power);
        return true;
    }

    @Override
    public void detectAcrossDaysData(Guild guild, GuildUser guildUser) {
        long now = DateUtils.getUnixTime();
        if (guildUser.getActive() == null) {
            guildUser.setActive(0);
            guildUserDao.updateActive(guildUser);
        }

        // 刷新每日数据
        if (guildUser.getDailyTM() == null || now >= guildUser.getDailyTM()) {
            guildSignInService.refreshData(guildUser);
            guildTaskService.refreshTask(guild, guildUser);
            guildShopService.refreshShop(GuildShopType.SHOP_DAILY, guild, guildUser);
            guildUser.setDailyActive(0);
            guildUser.setDailyTM(DateUtils.getSystemResetTime());

            guildUserDao.updateDailyActive(guildUser);
            guildUserDao.updateDailyTM(guildUser);
            guildUserDao.updateDonation(guildUser);
        }

        // 刷新每周数据
        if (guildUser.getWeeklyTM() == null || now >= guildUser.getWeeklyTM()) {
            guildShopService.refreshShop(GuildShopType.SHOP_WEEKLY, guild, guildUser);
            guildUser.setWeeklyActive(0);
            guildUser.setWeeklyTM(DateUtils.getSundayEndTimestamp());
            guildUser.setDonationItemCount(0);

            guildUserDao.updateDonation(guildUser);
            guildUserDao.updateWeeklyActive(guildUser);
            guildUserDao.updateWeeklyTM(guildUser);

        }
    }
}

package com.dxx.game.common.server.context;

import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.CommonProto.CommonParams;

import com.google.protobuf.Message;
import io.netty.util.concurrent.FastThreadLocal;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请求上下文
 * <AUTHOR>
 * @date 2019-12-11 14:35
 */
public class RequestContext {

	/** 核心请求参数 **/
	private static FastThreadLocal<CommonParams> localCommonParams = new FastThreadLocal<CommonParams>();
	/** 客户端请求transId **/
	private static FastThreadLocal<String> localRequestId = new FastThreadLocal<>();
	/** userId **/
	private static FastThreadLocal<Long> localUserId = new FastThreadLocal<Long>();
	/** deviceId **/
	private static FastThreadLocal<String> localDeviceId = new FastThreadLocal<>();
	private static FastThreadLocal<String> localAccountId = new FastThreadLocal<>();
	/** 存储user entity **/
	private static FastThreadLocal<User> localUser = new FastThreadLocal<User>();

	/** 当前请求的接口ID **/
	private static FastThreadLocal<Short> localCommand = new FastThreadLocal<Short>();

	private static FastThreadLocal<String> newUserFrom = new FastThreadLocal<>();

	/** 当前请求的客户端IP **/
	private static FastThreadLocal<String> localClientIp = new FastThreadLocal<String>();
	/** 配置版本 */
	private static FastThreadLocal<String> configVersion = new FastThreadLocal<>();

	/** 客户端系统 **/
	private static FastThreadLocal<String> localOs = new FastThreadLocal<>();

	/** 客户端参数 */
	private static FastThreadLocal<Message> localClientParams = new FastThreadLocal<>();

	/** 国家代码 */
	private static FastThreadLocal<String> countryCode = new FastThreadLocal<>();

	private static FastThreadLocal<String> douYinSdkOpenId = new FastThreadLocal<>();
	private static FastThreadLocal<Map<Integer, Long>> localServerOpenTime = new FastThreadLocal<>();

	public static void setUserId(Long userId) {
		localUserId.set(userId);
	}

	public static Long getUserId() {
		return localUserId.get();
	}

	public static void setCommonParams(CommonParams commonParams) {
		localCommonParams.set(commonParams);
	}

	public static CommonParams getCommonParams() {
		return localCommonParams.get();
	}

	public static void setUser(User user) {
		localUser.set(user);
	}

	public static User getUser() {
		return localUser.get();
	}

	public static void setCommand(Short command) {
		localCommand.set(command);
	}

	public static Short getCommand() {
		return localCommand.get();
	}

	public static void setClientIp(String clientIp) {
		localClientIp.set(clientIp);
	}

	public static String getClientIp() {
		return localClientIp.get();
	}


	public static void setConfigVersion(String version) {
		configVersion.set(version);
	}

	public static String getConfigVersion() {
		return configVersion.get();
	}

	public static void setOs(String os) {
		localOs.set(os);
	}

	public static String getOs() {
		return localOs.get();
	}

	public static void setCountryCode(String value) {
		countryCode.set(value);
	}

	public static String getCountryCode() {
		return countryCode.get();
	}

	public static void setRequestId(String value) {
		localRequestId.set(value);
	}

	public static String getRequestId() {
		return localRequestId.get();
	}

	public static void setLocalClientParams(Message params) {
		localClientParams.set(params);
	}
	public static Message getClientParams() {
		return localClientParams.get();
	}

	public static void setDeviceId(String deviceId) {
		localDeviceId.set(deviceId);
	}
	public static String getDeviceId() {
		return localDeviceId.get();
	}

	public static void setAccountId(String accountId) {
		localAccountId.set(accountId);
	}
	public static String getAccountId() {
		return localAccountId.get();
	}

	private static final FastThreadLocal<Map<Long, UserThreadLocal>> userContexts = new FastThreadLocal<>();
	@Getter
	@Setter
	public static class UserThreadLocal {
		private long transId;
		private long userId;
	}

	public static UserThreadLocal getUserContext(long userId) {
		if (userContexts.get() == null) {
			// 使用 ConcurrentHashMap 以便在多线程环境下安全地进行并发访问。
			userContexts.set(new ConcurrentHashMap<>());
		}
		Map<Long, UserThreadLocal> contextMap = userContexts.get();
		return contextMap.computeIfAbsent(userId, id -> {
			UserThreadLocal user = new UserThreadLocal();
			user.userId = id;
			return user;
		});
	}

	public static void setNewUserFrom(String value) {
		newUserFrom.set(value);
	}

	public static String getNewUserFrom() {
		return newUserFrom.get();
	}

	public static void setDouYinSdkOpenId(String value) {
		douYinSdkOpenId.set(value);
	}

	public static String getDouYinSdkOpenId() {
		return douYinSdkOpenId.get();
	}

	public static void putServerOpenTime(int serverId, long time) {
		if (localServerOpenTime.get() == null) {
			localServerOpenTime.set(new HashMap<>());
		}
		localServerOpenTime.get().put(serverId, time);
	}

	public static long getServerOpenTime(int serverId) {
		if (localServerOpenTime.get() == null) {
			return 0;
		}
		return localServerOpenTime.get().getOrDefault(serverId, 0L);
	}

	public static void clear() {
		localUser.remove();
		localCommonParams.remove();
		localUserId.remove();
		localCommand.remove();
		localClientIp.remove();
		configVersion.remove();
		localOs.remove();
		localRequestId.remove();
		localClientParams.remove();
		localDeviceId.remove();
		localAccountId.remove();
		userContexts.remove();
		newUserFrom.remove();
		douYinSdkOpenId.remove();
		localServerOpenTime.remove();
	}
}

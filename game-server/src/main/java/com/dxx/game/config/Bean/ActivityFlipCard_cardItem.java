
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFlipCard_cardItem {
    public ActivityFlipCard_cardItem(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        cardType = _buf.get("cardType").getAsInt();
        collectItemId = _buf.get("collectItemId").getAsInt();
        atlasId = _buf.get("atlasId").getAsInt();
        iconName = _buf.get("iconName").getAsString();
        whiteName = _buf.get("whiteName").getAsString();
        collectColor = _buf.get("collectColor").getAsString();
        ActivateSound = _buf.get("ActivateSound").getAsInt();
        ActivateEffect = _buf.get("ActivateEffect").getAsString();
    }

    public static ActivityFlipCard_cardItem deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFlipCard_cardItem(_buf);
    }

    /**
     * ID
     */
    public final int id;
    /**
     * 所属类型
     */
    public final int cardType;
    /**
     * 收集品id
     */
    public final int collectItemId;
    /**
     * 图集
     */
    public final int atlasId;
    /**
     * icon名
     */
    public final String iconName;
    /**
     * 白色剪影图
     */
    public final String whiteName;
    /**
     * 收集颜色
     */
    public final String collectColor;
    /**
     * 解锁时的音效
     */
    public final int ActivateSound;
    /**
     * 解锁时的效果（subType....:EffectType）
     */
    public final String ActivateEffect;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "cardType:" + cardType + ","
        + "collectItemId:" + collectItemId + ","
        + "atlasId:" + atlasId + ","
        + "iconName:" + iconName + ","
        + "whiteName:" + whiteName + ","
        + "collectColor:" + collectColor + ","
        + "ActivateSound:" + ActivateSound + ","
        + "ActivateEffect:" + ActivateEffect + ","
        + "}";
    }
}


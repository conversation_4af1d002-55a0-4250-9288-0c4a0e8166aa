package com.dxx.game.consts;

public class MsgReqCommand {

	public static final short DevelopLoginRequest = 9001;
	public static final short DevelopLoginResponse = 9002;
	public static final short DevelopChangeResourceRequest = 9003;
	public static final short DevelopChangeResourceResponse = 9004;
	public static final short DevelopToolsRequest = 9005;
	public static final short DevelopToolsResponse = 9006;
	public static final short ErrorMsg = 9999;
	public static final short UserLoginRequest = 10101;
	public static final short UserLoginResponse = 10102;
	public static final short UserGetInfoRequest = 10103;
	public static final short UserGetInfoResponse = 10104;
	public static final short UserHeartbeatRequest = 10105;
	public static final short UserHeartbeatResponse = 10106;
	public static final short UserUpdateSystemMaskRequest = 10107;
	public static final short UserUpdateSystemMaskResponse = 10108;
	public static final short UserUpdateGuideMaskRequest = 10109;
	public static final short UserUpdateGuideMaskResponse = 10110;
	public static final short UserCancelAccountRequest = 10111;
	public static final short UserCancelAccountResponse = 10112;
	public static final short UserUpdateInfoRequest = 10113;
	public static final short UserUpdateInfoResponse = 10114;
	public static final short UserGetOtherPlayerInfoRequest = 10115;
	public static final short UserGetOtherPlayerInfoResponse = 10116;
	public static final short UserGetBattleReportRequest = 10117;
	public static final short UserGetBattleReportResponse = 10118;
	public static final short UserOpenModelRequest = 10119;
	public static final short UserOpenModelResponse = 10120;
	public static final short UserSetFormationByTypeRequest = 10121;
	public static final short UserSetFormationByTypeResponse = 10122;
	public static final short UserGetFormationByTypeRequest = 10123;
	public static final short UserGetFormationByTypeResponse = 10124;
	public static final short UserGetLastLoginRequest = 10141;
	public static final short UserGetLastLoginResponse = 10142;
	public static final short FindServerListRequest = 10143;
	public static final short FindServerListResponse = 10144;
	public static final short ItemUseRequest = 10201;
	public static final short ItemUseResponse = 10202;
	public static final short MissionGetInfoRequest = 10301;
	public static final short MissionGetInfoResponse = 10302;
	public static final short MissionStartRequest = 10303;
	public static final short MissionStartResponse = 10304;
	public static final short MissionEndRequest = 10305;
	public static final short MissionEndResponse = 10306;
	public static final short MissionGetHangUpItemsRequest = 10307;
	public static final short MissionGetHangUpItemsResponse = 10308;
	public static final short MissionReceiveHangUpItemsRequest = 10309;
	public static final short MissionReceiveHangUpItemsResponse = 10310;
	public static final short MissionQuickHangUpRequest = 10311;
	public static final short MissionQuickHangUpResponse = 10312;
	public static final short PayInAppPurchaseRequest = 10401;
	public static final short PayInAppPurchaseResponse = 10402;
	public static final short PayPreOrderRequest = 10403;
	public static final short PayPreOrderResponse = 10404;
	public static final short PayOnUnityRequest = 10405;
	public static final short PayOnUnityResponse = 10406;
	public static final short TaskGetInfoRequest = 10501;
	public static final short TaskGetInfoResponse = 10502;
	public static final short TaskRewardDailyRequest = 10503;
	public static final short TaskRewardDailyResponse = 10504;
	public static final short TaskRewardAchieveRequest = 10505;
	public static final short TaskRewardAchieveResponse = 10506;
	public static final short TaskActiveRewardRequest = 10507;
	public static final short TaskActiveRewardResponse = 10508;
	public static final short TaskActiveRewardAllRequest = 10509;
	public static final short TaskActiveRewardAllResponse = 10510;
	public static final short ShopGetInfoRequest = 10601;
	public static final short ShopGetInfoResponse = 10602;
	public static final short ShopDoGachaRequest = 10603;
	public static final short ShopDoGachaResponse = 10604;
	public static final short ShopIntegralGetInfoRequest = 10605;
	public static final short ShopIntegralGetInfoResponse = 10606;
	public static final short ShopIntegralRefreshRequest = 10607;
	public static final short ShopIntegralRefreshResponse = 10608;
	public static final short ShopIntegralBuyItemRequest = 10609;
	public static final short ShopIntegralBuyItemResponse = 10610;
	public static final short ShopGacheWishRequest = 10611;
	public static final short ShopGacheWishResponse = 10612;
	public static final short ShopFreeIAPItemRequest = 10613;
	public static final short ShopFreeIAPItemResponse = 10614;
	public static final short BattlePassGetInfoRequest = 10631;
	public static final short BattlePassGetInfoResponse = 10632;
	public static final short BattlePassRewardRequest = 10633;
	public static final short BattlePassRewardResponse = 10634;
	public static final short BattlePassChangeScoreRequest = 10635;
	public static final short BattlePassChangeScoreResponse = 10636;
	public static final short BattlePassFinalRewardRequest = 10637;
	public static final short BattlePassFinalRewardResponse = 10638;
	public static final short MonthCardGetRewardRequest = 10651;
	public static final short MonthCardGetRewardResponse = 10652;
	public static final short VIPLevelRewardRequest = 10661;
	public static final short VIPLevelRewardResponse = 10662;
	public static final short LevelFundGetInfoRequest = 10671;
	public static final short LevelFundGetInfoResponse = 10672;
	public static final short LevelFundRewardRequest = 10673;
	public static final short LevelFundRewardResponse = 10674;
	public static final short FirstRechargeRewardRequest = 10675;
	public static final short FirstRechargeRewardResponse = 10676;
	public static final short HeroUpgradeRequest = 10701;
	public static final short HeroUpgradeResponse = 10702;
	public static final short HeroAdvanceRequest = 10703;
	public static final short HeroAdvanceResponse = 10704;
	public static final short HeroStarRequest = 10705;
	public static final short HeroStarResponse = 10706;
	public static final short HeroResetRequest = 10707;
	public static final short HeroResetResponse = 10708;
	public static final short HeroBookScoreRequest = 10709;
	public static final short HeroBookScoreResponse = 10710;
	public static final short HeroBookRewardRequest = 10711;
	public static final short HeroBookRewardResponse = 10712;
	public static final short HeroReplaceSkinRequest = 10713;
	public static final short HeroReplaceSkinResponse = 10714;
	public static final short HeroBondLevelUpRequest = 10715;
	public static final short HeroBondLevelUpResponse = 10716;
	public static final short HeroLosslessRequest = 10717;
	public static final short HeroLosslessResponse = 10718;
	public static final short EquipStrengthRequest = 10801;
	public static final short EquipStrengthResponse = 10802;
	public static final short EquipComposeRequest = 10803;
	public static final short EquipComposeResponse = 10804;
	public static final short EquipUpgradeRequest = 10805;
	public static final short EquipUpgradeResponse = 10806;
	public static final short EquipDressRequest = 10807;
	public static final short EquipDressResponse = 10808;
	public static final short EquipLevelResetRequest = 10809;
	public static final short EquipLevelResetResponse = 10810;
	public static final short EquipQualityDownRequest = 10811;
	public static final short EquipQualityDownResponse = 10812;
	public static final short EquipOffRequest = 10813;
	public static final short EquipOffResponse = 10814;
	public static final short EquipReplaceRequest = 10815;
	public static final short EquipReplaceResponse = 10816;
	public static final short ActivityGetListRequest = 10901;
	public static final short ActivityGetListResponse = 10902;
	public static final short ActivityGetTaskRequest = 10903;
	public static final short ActivityGetTaskResponse = 10904;
	public static final short ActivityTaskRewardRequest = 10905;
	public static final short ActivityTaskRewardResponse = 10906;
	public static final short ActivityGetShopRequest = 10907;
	public static final short ActivityGetShopResponse = 10908;
	public static final short ActivityShopExchangeRequest = 10909;
	public static final short ActivityShopExchangeResponse = 10910;
	public static final short ActivityGetRankRequest = 10911;
	public static final short ActivityGetRankResponse = 10912;
	public static final short SignInGetInfoRequest = 11101;
	public static final short SignInGetInfoResponse = 11102;
	public static final short SignInDoSignRequest = 11103;
	public static final short SignInDoSignResponse = 11104;
	public static final short SevenDayTaskGetInfoRequest = 11301;
	public static final short SevenDayTaskGetInfoResponse = 11302;
	public static final short SevenDayTaskRewardRequest = 11303;
	public static final short SevenDayTaskRewardResponse = 11304;
	public static final short SevenDayTaskActiveRewardRequest = 11305;
	public static final short SevenDayTaskActiveRewardResponse = 11306;
	public static final short FishingOnOpenRequest = 11401;
	public static final short FishingOnOpenResponse = 11402;
	public static final short FishingCastRodRequest = 11403;
	public static final short FishingCastRodResponse = 11404;
	public static final short FishingReelInRequest = 11405;
	public static final short FishingReelInResponse = 11406;
	public static final short FishingBuyBaitRequest = 11407;
	public static final short FishingBuyBaitResponse = 11408;
	public static final short FishingRebornRequest = 11409;
	public static final short FishingRebornResponse = 11410;
	public static final short FlipOnOpenRequest = 11501;
	public static final short FlipOnOpenResponse = 11502;
	public static final short FlipAccRewardRequest = 11503;
	public static final short FlipAccRewardResponse = 11504;
	public static final short FlipBuyStepRequest = 11505;
	public static final short FlipBuyStepResponse = 11506;
	public static final short FlipShowGridRequest = 11507;
	public static final short FlipShowGridResponse = 11508;
	public static final short FlipRewardGridRequest = 11509;
	public static final short FlipRewardGridResponse = 11510;
	public static final short FlipClueGridRequest = 11511;
	public static final short FlipClueGridResponse = 11512;
	public static final short FlipBombGridRequest = 11513;
	public static final short FlipBombGridResponse = 11514;
	public static final short FlipMapFindSpecialRequest = 11515;
	public static final short FlipMapFindSpecialResponse = 11516;
	public static final short FlipAllAccRewardRequest = 11517;
	public static final short FlipAllAccRewardResponse = 11518;
	public static final short DiveOnOpenRequest = 11601;
	public static final short DiveOnOpenResponse = 11602;
	public static final short DiveBuyItemRequest = 11603;
	public static final short DiveBuyItemResponse = 11604;
	public static final short DiveAccRewardRequest = 11605;
	public static final short DiveAccRewardResponse = 11606;
	public static final short DiveShineRequest = 11607;
	public static final short DiveShineResponse = 11608;
	public static final short DiveUsePropRequest = 11609;
	public static final short DiveUsePropResponse = 11610;
	public static final short DiveAllAccRewardRequest = 11611;
	public static final short DiveAllAccRewardResponse = 11612;
	public static final short PowerOnOpenRequest = 11701;
	public static final short PowerOnOpenResponse = 11702;
	public static final short PowerRewardRequest = 11703;
	public static final short PowerRewardResponse = 11704;
	public static final short GuildGetInfoRequest = 30101;
	public static final short GuildGetInfoResponse = 30102;
	public static final short GuildCreateRequest = 30103;
	public static final short GuildCreateResponse = 30104;
	public static final short GuildSearchRequest = 30105;
	public static final short GuildSearchResponse = 30106;
	public static final short GuildGetDetailRequest = 30107;
	public static final short GuildGetDetailResponse = 30108;
	public static final short GuildGetMemberListRequest = 30109;
	public static final short GuildGetMemberListResponse = 30110;
	public static final short GuildModifyRequest = 30111;
	public static final short GuildModifyResponse = 30112;
	public static final short GuildDismissRequest = 30113;
	public static final short GuildDismissResponse = 30114;
	public static final short GuildApplyJoinRequest = 30115;
	public static final short GuildApplyJoinResponse = 30116;
	public static final short GuildCancelApplyRequest = 30117;
	public static final short GuildCancelApplyResponse = 30118;
	public static final short GuildAutoJoinRequest = 30119;
	public static final short GuildAutoJoinResponse = 30120;
	public static final short GuildGetApplyListRequest = 30121;
	public static final short GuildGetApplyListResponse = 30122;
	public static final short GuildAgreeJoinRequest = 30123;
	public static final short GuildAgreeJoinResponse = 30124;
	public static final short GuildRefuseJoinRequest = 30125;
	public static final short GuildRefuseJoinResponse = 30126;
	public static final short GuildKickOutRequest = 30127;
	public static final short GuildKickOutResponse = 30128;
	public static final short GuildLeaveRequest = 30129;
	public static final short GuildLeaveResponse = 30130;
	public static final short GuildUpPositionRequest = 30131;
	public static final short GuildUpPositionResponse = 30132;
	public static final short GuildTransferPresidentRequest = 30133;
	public static final short GuildTransferPresidentResponse = 30134;
	public static final short GuildGetFeaturesInfoRequest = 30135;
	public static final short GuildGetFeaturesInfoResponse = 30136;
	public static final short GuildLevelUpRequest = 30137;
	public static final short GuildLevelUpResponse = 30138;
	public static final short GuildSignInRequest = 30141;
	public static final short GuildSignInResponse = 30142;
	public static final short GuildShopBuyRequest = 30151;
	public static final short GuildShopBuyResponse = 30152;
	public static final short GuildShopRefreshRequest = 30153;
	public static final short GuildShopRefreshResponse = 30154;
	public static final short GuildTaskRewardRequest = 30161;
	public static final short GuildTaskRewardResponse = 30162;
	public static final short GuildTaskRefreshRequest = 30163;
	public static final short GuildTaskRefreshResponse = 30164;
	public static final short GuildGetMessageRecordsRequest = 30171;
	public static final short GuildGetMessageRecordsResponse = 30172;
	public static final short GuildDonationReqItemRequest = 30201;
	public static final short GuildDonationReqItemResponse = 30202;
	public static final short GuildDonationSendItemRequest = 30203;
	public static final short GuildDonationSendItemResponse = 30204;
	public static final short GuildDonationReceiveRequest = 30205;
	public static final short GuildDonationReceiveResponse = 30206;
	public static final short GuildDonationGetRecordsRequest = 30207;
	public static final short GuildDonationGetRecordsResponse = 30208;
	public static final short GuildDonationGetOperationRecordsRequest = 30209;
	public static final short GuildDonationGetOperationRecordsResponse = 30210;
	public static final short GuildBossBattleRequest = 30211;
	public static final short GuildBossBattleResponse = 30212;
	public static final short GuildBossBattleGRankRequest = 30213;
	public static final short GuildBossBattleGRankResponse = 30214;
	public static final short GuildTechUpgradeRequest = 30501;
	public static final short GuildTechUpgradeResponse = 30502;
	public static final short IMLoginRequest = 31101;
	public static final short IMLoginResponse = 31102;
	public static final short IMJoinGroupRequest = 31103;
	public static final short IMJoinGroupResponse = 31104;
	public static final short IMQuitGroupRequest = 31105;
	public static final short IMQuitGroupResponse = 31106;
	public static final short IMHeartBeatRequest = 31107;
	public static final short IMHeartBeatResponse = 31108;
	public static final short IMGroupChatRequest = 31109;
	public static final short IMGroupChatResponse = 31110;
	public static final short IMPrivateChatRequest = 31111;
	public static final short IMPrivateChatResponse = 31112;
	public static final short IMGroupMessageRecordRequest = 31113;
	public static final short IMGroupMessageRecordResponse = 31114;
	public static final short IMPrivateListRequest = 31115;
	public static final short IMPrivateListResponse = 31116;
	public static final short IMPrivateChatRecordRequest = 31117;
	public static final short IMPrivateChatRecordResponse = 31118;
	public static final short IMGetBlackListRequest = 31119;
	public static final short IMGetBlackListResponse = 31120;
	public static final short IMAddToBlackListRequest = 31121;
	public static final short IMAddToBlackListResponse = 31122;
	public static final short IMRemoveFromBlackListRequest = 31123;
	public static final short IMRemoveFromBlackListResponse = 31124;
	public static final short IMChatTextTranslateRequest = 31125;
	public static final short IMChatTextTranslateResponse = 31126;
	public static final short IMLoginRepeatMessage = 31202;
	public static final short IMReconnectMessage = 31204;
	public static final short IMPushMessage = 31206;
	public static final short IMErrorMessage = 31299;
	public static final short ChatShowItemRequest = 32105;
	public static final short ChatShowItemResponse = 32106;


}
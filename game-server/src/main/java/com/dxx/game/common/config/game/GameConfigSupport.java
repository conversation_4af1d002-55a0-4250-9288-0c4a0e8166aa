package com.dxx.game.common.config.game;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.s3.S3Service;
import com.dxx.game.common.config.game.event.PutConfigToS3Event;
import com.dxx.game.common.config.game.event.RefreshConfigEvent;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.consts.ServerType;
import com.dxx.game.common.server.context.ServerContext;
import com.dxx.game.config.GameConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/16 16:09
 */
@Slf4j
@Component
public class GameConfigSupport {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Value("${spring.application.env}")
    private String applicationEnv;
    @Value("${game.config.url}")
    private String gameConfigUrl;
    @Resource
    private S3Service s3Service;
    @Value("${aws.s3.game.config.bucket.name}")
    private String s3GameConfigBucketName;
    @Value("${game.config.reload.secret}")
    private String reloadConfigSecret;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RedisService redisService;

    public Map<String, Object> syncConfig(JSONObject params) {
        try {
//            if (!applicationEnv.equals("test") && !applicationEnv.equals("develop")) {
//                return null;
//            }
            //临时注掉TODO,以后找解决办法
            if (applicationEnv.equals("prod") || applicationEnv.equals("pre")) {
                return null;
            }
            if (params == null) {
                return null;
            }
            String fileIds = params.getString("fileIds");
            if (fileIds == null) {
                return null;
            }
            String key = params.getString("key");
            if (key == null || !key.equals("933080a605eee557")) {
                return null;
            }

            String[] fileIdArr = fileIds.split(",");
            List<File> files = new ArrayList<>();
            List<String> configName = new ArrayList<>();
            for (int i = 0; i < fileIdArr.length; i ++) {
                String fileId = fileIdArr[i];
                String url = gameConfigUrl + "?fileId=" + fileId + "&key=a5f0a9b3c9e1c7e0";
                File file = OkHttpClientUtil.downLoadFile(url, "gameconf");

                configName.add(file.getName());
                files.add(file);
            }

            applicationEventPublisher.publishEvent(new RefreshConfigEvent(this, configName));
            applicationEventPublisher.publishEvent(new PutConfigToS3Event(this, s3GameConfigBucketName, files));
            return new HashMap<>();
        } catch (Exception e) {
            log.error("syncConfig failed", e);
            return new HashMap<>();
        }
    }

    public Map<String, Object> reloadConfig(JSONObject params) {
        if (params == null || !params.containsKey("key") || !params.getString("key").equals(reloadConfigSecret)) {
            return null;
        }
        applicationEventPublisher.publishEvent(new RefreshConfigEvent(this));
        this.notifyServersToReloadConfig();
        return new HashMap<>();
    }

    public Map<String, Object> reloadDirtyWords(JSONObject params) {
        if (params == null || !params.containsKey("key") || !params.getString("key").equals("742a030e6236291c")) {
            return null;
        }
//        DirtyWordsService.reload();
        return new HashMap<>();
    }

    public Map<String, Object> setLogLevelDebug(JSONObject params) {
        LoggerContext content = (LoggerContext) LoggerFactory.getILoggerFactory();
        content.getLogger("ROOT").setLevel(Level.DEBUG);
        return new HashMap<>();
    }
    public Map<String, Object> setLogLevelInfo(JSONObject params) {
        LoggerContext content = (LoggerContext) LoggerFactory.getILoggerFactory();
        content.getLogger("ROOT").setLevel(Level.INFO);
        return new HashMap<>();
    }

    public void notifyServersToReloadConfig() {
        // 通知其他服务
        if (ServerContext.getServerType() == null || ServerContext.getServerType() == ServerType.GAME_SERVER) {
            redisService.publishMessage("internal_message", "reload_config");
        }
    }
}

package com.dxx.game.modules.shop.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.ShopProto;
import com.dxx.game.modules.shop.service.BattlePassService;
import com.google.protobuf.Message;
import jakarta.annotation.Resource;

/**
 * @author: lsc
 * @createDate: 2025/6/7
 * @description:
 */
@ApiHandler
public class BattlePassHandler {

    @Resource
    private BattlePassService battlePassService;

    @ApiMethod(command = MsgReqCommand.BattlePassGetInfoRequest, name = "通行证-领取奖励")
    public Result<ShopProto.BattlePassGetInfoResponse> battlePassGetInfo(Message msg) {
        ShopProto.BattlePassGetInfoRequest params = (ShopProto.BattlePassGetInfoRequest) msg;
        return battlePassService.battlePassGetInfo(params);
    }

    @ApiMethod(command = MsgReqCommand.BattlePassRewardRequest, name = "通行证-领取奖励")
    public Result<ShopProto.BattlePassRewardResponse> battlePassReward(Message msg) {
        ShopProto.BattlePassRewardRequest params = (ShopProto.BattlePassRewardRequest) msg;
        return battlePassService.battlePassReward(params);
    }

    @ApiMethod(command = MsgReqCommand.BattlePassChangeScoreRequest, name = "通行证-兑换积分")
    public Result<ShopProto.BattlePassChangeScoreResponse> battlePassChangeScore(Message msg) {
        ShopProto.BattlePassChangeScoreRequest params = (ShopProto.BattlePassChangeScoreRequest) msg;
        return battlePassService.battlePassChangeScore(params);
    }

    @ApiMethod(command = MsgReqCommand.BattlePassFinalRewardRequest, name = "通行证-领取最终奖励")
    public Result<ShopProto.BattlePassFinalRewardResponse> battlePassFinalReward(Message msg) {
        ShopProto.BattlePassFinalRewardRequest params = (ShopProto.BattlePassFinalRewardRequest) msg;
        return battlePassService.battlePassFinalReward(params);
    }
}

package com.dxx.game.modules.guild.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.modules.guild.service.GuildService;
import com.google.protobuf.Message;
import com.dxx.game.dto.GuildProto.*;
import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
@ApiHandler
public class GuildHandler {

    @Resource
    private GuildService guildService;

    @ApiMethod(command = MsgReqCommand.GuildGetInfoRequest, name = "公会-获取数据")
    public Result<GuildGetInfoResponse> getInfo(Message msg) {
        GuildGetInfoRequest params = (GuildGetInfoRequest)msg;
        return guildService.getInfoAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildCreateRequest, name = "公会-创建公会")
    public Result<GuildCreateResponse> create(Message msg) {
        GuildCreateRequest params = (GuildCreateRequest)msg;
        return guildService.createAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildSearchRequest, name = "公会-搜索")
    public Result<GuildSearchResponse> search(Message msg) {
        GuildSearchRequest params = (GuildSearchRequest)msg;
        return guildService.searchAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildGetDetailRequest, name = "公会-查看详细信息")
    public Result<GuildGetDetailResponse> getDetail(Message msg) {
        GuildGetDetailRequest params = (GuildGetDetailRequest)msg;
        return guildService.getDetailAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildGetMemberListRequest, name = "公会-获取成员列表")
    public Result<GuildGetMemberListResponse> getMemberList(Message msg) {
        GuildGetMemberListRequest params = (GuildGetMemberListRequest)msg;
        return guildService.getMemberListAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildModifyRequest, name = "公会-修改信息")
    public Result<GuildModifyResponse> modify(Message msg) {
        GuildModifyRequest params = (GuildModifyRequest)msg;
        return guildService.modifyAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildDismissRequest, name = "公会-解散")
    public Result<GuildDismissResponse> dismiss(Message msg) {
        GuildDismissRequest params = (GuildDismissRequest)msg;
        return guildService.dismissAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildApplyJoinRequest, name = "公会-申请加入")
    public Result<GuildApplyJoinResponse> applyJoin(Message msg) {
        GuildApplyJoinRequest params = (GuildApplyJoinRequest)msg;
        return guildService.applyJoinAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildCancelApplyRequest, name = "公会-取消申请")
    public Result<GuildCancelApplyResponse> cancelApply(Message msg) {
        GuildCancelApplyRequest params = (GuildCancelApplyRequest)msg;
        return guildService.cancelApplyAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildAutoJoinRequest, name = "公会-自动加入")
    public Result<GuildAutoJoinResponse> autoJoin(Message msg) {
        GuildAutoJoinRequest params = (GuildAutoJoinRequest)msg;
        return guildService.autoJoinAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildGetApplyListRequest, name = "公会-获取申请列表")
    public Result<GuildGetApplyListResponse> getApplyList(Message msg) {
        GuildGetApplyListRequest params = (GuildGetApplyListRequest)msg;
        return guildService.getApplyListAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildAgreeJoinRequest, name = "公会-同意加入")
    public Result<GuildAgreeJoinResponse> agreeJoin(Message msg) {
        GuildAgreeJoinRequest params = (GuildAgreeJoinRequest)msg;
        return guildService.agreeJoinAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildRefuseJoinRequest, name = "公会-拒绝加入")
    public Result<GuildRefuseJoinResponse> refusedJoin(Message msg) {
        GuildRefuseJoinRequest params = (GuildRefuseJoinRequest)msg;
        return guildService.refusedJoinAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildKickOutRequest, name = "公会-踢人")
    public Result<GuildKickOutResponse> kickOut(Message msg) {
        GuildKickOutRequest params = (GuildKickOutRequest)msg;
        return guildService.kickOutAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildLeaveRequest, name = "公会-离开")
    public Result<GuildLeaveResponse> leave(Message msg) {
        GuildLeaveRequest params = (GuildLeaveRequest)msg;
        return guildService.leaveAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildUpPositionRequest, name = "公会-给人职位")
    public Result<GuildUpPositionResponse> upPosition(Message msg) {
        GuildUpPositionRequest params = (GuildUpPositionRequest)msg;
        return guildService.upPositionAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildTransferPresidentRequest, name = "公会-转让会长")
    public Result<GuildTransferPresidentResponse> transferPresident(Message msg) {
        GuildTransferPresidentRequest params = (GuildTransferPresidentRequest)msg;
        return guildService.transferPresidentAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildGetFeaturesInfoRequest, name = "公会-获取公会功能信息")
    public Result<GuildGetFeaturesInfoResponse> getFeaturesInfo(Message msg) {
        GuildGetFeaturesInfoRequest params = (GuildGetFeaturesInfoRequest)msg;
        return guildService.getFeaturesInfoAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildLevelUpRequest, name = "公会-升级")
    public Result<GuildLevelUpResponse> guildLevelUp(Message msg) {
        GuildLevelUpRequest params = (GuildLevelUpRequest)msg;
        return guildService.guildLevelUpAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildGetMessageRecordsRequest, name = "公会-获取消息记录", skipRedisLock = true)
    public Result<GuildGetMessageRecordsResponse> getMessageRecords(Message msg) {
        GuildGetMessageRecordsRequest params = (GuildGetMessageRecordsRequest)msg;
        return guildService.getMessageRecordsAction(params);
    }

    @ApiMethod(command = MsgReqCommand.GuildTechUpgradeRequest, name = "公会-科技升级")
    public Result<GuildTechUpgradeResponse> techUpgrade(Message msg) {
        GuildTechUpgradeRequest params = (GuildTechUpgradeRequest)msg;
        return guildService.techUpgrade(params);
    }
}

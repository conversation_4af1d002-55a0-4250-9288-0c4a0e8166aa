package com.dxx.game.modules.gm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMManager;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import com.dxx.game.modules.gm.service.GmService;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/25 11:08
 */
@Slf4j
@Service
public class GmServiceImpl implements GmService {

    @Value("${game.gm.key}")
    private String signKey;
    @Resource
    private GameConfigManager gameConfigManager;

    @DynamoDBTransactional
    @Override
    public Map<String, Object> doHandler(JSONObject params, FullHttpRequest fullHttpRequest)  {
        Map<String, Object> result = new HashMap<String, Object>();
        log.info("gm request, params:{}", params);
        try {
            int cmd = params.getIntValue("cmd");
            String secret = params.getString("secret");
            String host = fullHttpRequest.headers().get("Host");
            if ((!gameConfigManager.isDevelop() && !gameConfigManager.isTest() && !host.startsWith("internal-"))
                    || !secret.equals(signKey)) {
                return null;
            }
            if (params.containsKey("userId")) {
                if(!isNumber(params.getString("userId"))) {
                    return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "user not exist");
                }

                long userId = params.getLongValue("userId");
                String userIdStr = params.getString("userId");
                if (String.valueOf(userId).length() != userIdStr.length()) {
                    log.error("gmServiceUserIdLengthError, params:{}", params);
                    return null;
                }
            }
            RequestContext.setCommand((short)cmd);

            AbstractGMProcessor abstractGMProcessor = GMManager.getInstance().getBaseGMFunction(cmd);
            if (abstractGMProcessor == null) {
                log.error("gmServiceCmdError, params:{}", params);
                return null;
            }
            result = abstractGMProcessor.executeProcessor(params);

            if (Integer.parseInt(result.get("code").toString()) != 0) {
                log.error("do gm request failed, request:{},  response:{}", params, result);
                DynamoDBTransactionAspectSupport.setRollBack();
            }
            return result;
        } catch (Exception e) {
            log.error("doHandler e:", e);
            DynamoDBTransactionAspectSupport.setRollBack();
            log.error("gmServiceError, params: {}, errMsg: {}", params, e.getMessage());
            return this.error(GMErrorCode.ERROR_CODE_SYSTEM_ERROR, "system error");
        } 
    }

    private Map<String, Object> error(int code, String msg) {
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("code", code);
        result.put("msg", msg);
        return result;
    }

    private final String NUMBER_PATTERN = "[0-9]+";
    private final Pattern numberPattern = Pattern.compile(NUMBER_PATTERN);

    private boolean isNumber(String str) {
        return numberPattern.matcher(str).matches();
    }
}

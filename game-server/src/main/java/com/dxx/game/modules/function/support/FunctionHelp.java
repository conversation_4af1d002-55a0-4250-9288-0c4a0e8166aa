package com.dxx.game.modules.function.support;

import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.function.FunctionEntity;
import com.dxx.game.consts.FunctionTypeEnum;
import com.dxx.game.consts.VipPermission;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserModuleBaseDao;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
public class FunctionHelp {
    public static final int FUNCTION_TYPE_CHAPTER = 1;
    public static final int FUNCTION_TYPE_TOWER = 2;
    public static final int FUNCTION_TYPE_CHAPTER_FAILED = 3;
    public static final int FUNCTION_TYPE_ITEM = 4;
    public static final int FUNCTION_TYPE_CARD = 100;
    public static final int FUNCTION_TYPE_CHAPTER_CLOSE = 999;

    @Autowired
    private UserExtendDao userExtendDao;

    @Autowired
    private GameConfigManager gameConfigManager;

    @Autowired
    private RewardService rewardService;

    @Autowired
    private UserService userService;;


    public List<Integer> getUnlockArgs(String data) {
        var list = data.split(",");
        List<Integer> reward = new ArrayList<>();
        for (String s : list) {
            reward.add(Integer.parseInt(s));
        }
        return reward;
    }

    private boolean openFunctionByVip(long userId, FunctionTypeEnum functionTypeEnum,  UserExtend userExtend) {
        var vip = functionTypeEnum.getVipPower();
        if (vip == 0) {
            return false;
        }
        VipPermission permission = VipPermission.getEnumByKey(vip);
        return userService.vipPermissionValueIntCheck(userId, permission) == 1;
    }

    private boolean openFunctionNormal(long userId, int functionId,  UserExtend userExtend) {
        FunctionEntity config = gameConfigManager.getFunctionConfig().getFunctionEntity(functionId);
        if (config.getUnlockType() == FUNCTION_TYPE_CHAPTER) {
            return userExtend.getChapterId() >= Integer.parseInt(config.getUnlockArgs());
        } else if (config.getUnlockType() == FUNCTION_TYPE_TOWER) {
            // TODO 爬塔判断
            return true;
        } else if (config.getUnlockType() == FUNCTION_TYPE_ITEM) {
            var data = getUnlockArgs(config.getUnlockArgs());
            var item = gameConfigManager.getItemConfig().getItemEntity(data.get(0));
            var r = rewardService.tryRewards(userId, List.of(item.getItemType(), data.get(0), data.get(1)));
            return r.isSuccess();
        } else if (config.getUnlockType() == FUNCTION_TYPE_CHAPTER_FAILED) {
            return true;
        } else if (config.getUnlockType() == FUNCTION_TYPE_CARD) {
            return true;
        } else if (config.getUnlockType() == FUNCTION_TYPE_CHAPTER_CLOSE) {
            return false;
        }
        return false;
    }

    public boolean openFunction(long userId, int functionId,  UserExtend userExtend) {
        FunctionTypeEnum functionTypeEnum = FunctionTypeEnum.getEnumByKey(functionId);
        if (functionTypeEnum == null) {
            return openFunctionNormal(userId, functionId, userExtend);
        }

        return openFunctionByVip(userId, functionTypeEnum, userExtend) || openFunctionNormal(userId, functionId, userExtend);
    }

    public boolean isFunctionOpen(FunctionTypeEnum functionTypeEnum, long userId) {
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        return isFunctionOpen(functionTypeEnum, userExtend);
    }

    public boolean isFunctionOpen(FunctionTypeEnum functionTypeEnum, UserExtend userExtend) {
        int functionId = functionTypeEnum.getFunctionKey();
        return userExtend.getOpenModelId().contains(functionId);
    }
}

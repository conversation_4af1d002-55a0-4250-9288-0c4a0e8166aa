package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.USER_SWAP)
public class GMUserSwap extends AbstractGMProcessor {

    @Resource
    private UserDao userDao;

    @Override
    protected Object execute(JSONObject params) {
        long oldUserId = params.getLongValue("oldUserId");
        long newUserId = params.getLongValue("newUserId");
        if (oldUserId == newUserId) {
            return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "parms error");
        }

        User oldUser = userDao.getByUserId(oldUserId);
        if (oldUser == null) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "用户不存在, userId:" + oldUserId);
        }

        User newUser = userDao.getByUserId(newUserId);
        if (newUser == null) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST, "用户不存在, userId:" + newUserId);
        }

        String oldUserDeviceId = oldUser.getDeviceId();
        String oldUserAccountId = oldUser.getAccountId();
        String newUserDeviceId = newUser.getDeviceId();
        String newUserAccountId = newUser.getAccountId();

        // 设置新账号的deveceId为老账号的deviceId
        newUser.setDeviceId(oldUserDeviceId);
        newUser.setAccountId(oldUserAccountId);
        // 设置老账号的deviceId为新账号的deviceId
        oldUser.setDeviceId(newUserDeviceId);
        oldUser.setAccountId(newUserAccountId);

        userDao.update(oldUser);
        userDao.update(newUser);

        return this.success();
    }
}

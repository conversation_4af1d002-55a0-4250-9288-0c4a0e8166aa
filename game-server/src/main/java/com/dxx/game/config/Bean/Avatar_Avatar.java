
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Avatar_Avatar {
    public Avatar_Avatar(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        name = _buf.get("name").getAsString();
        atlasId = _buf.get("atlasId").getAsInt();
        iconId = _buf.get("iconId").getAsString();
        type = _buf.get("type").getAsInt();
        colorNum = _buf.get("colorNum").getAsString();
        qualityNum = _buf.get("qualityNum").getAsString();
        isHide = _buf.get("isHide").getAsInt();
        unLockType = _buf.get("unLockType").getAsInt();
        unLockParam = _buf.get("unLockParam").getAsString();
    }

    public static Avatar_Avatar deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Avatar_Avatar(_buf);
    }

    /**
     * 头像ID<br/>（控制显示顺序）
     */
    public final int id;
    /**
     * 描述
     */
    public final String name;
    /**
     * 图集id
     */
    public final int atlasId;
    /**
     * spriteid
     */
    public final String iconId;
    /**
     * 类型<br/>1.头像<br/>2.头像框
     */
    public final int type;
    /**
     * 底框颜色
     */
    public final String colorNum;
    /**
     * 品质文字颜色
     */
    public final String qualityNum;
    /**
     * 未获得时隐藏
     */
    public final int isHide;
    /**
     * 解锁类型<br/>0、不填：不限制，默认开启<br/>1、获取英雄后解锁（参数：英雄ID)<br/>2、获得皮肤后解锁
     */
    public final int unLockType;
    /**
     * 解锁参数
     */
    public final String unLockParam;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "name:" + name + ","
        + "atlasId:" + atlasId + ","
        + "iconId:" + iconId + ","
        + "type:" + type + ","
        + "colorNum:" + colorNum + ","
        + "qualityNum:" + qualityNum + ","
        + "isHide:" + isHide + ","
        + "unLockType:" + unLockType + ","
        + "unLockParam:" + unLockParam + ","
        + "}";
    }
}


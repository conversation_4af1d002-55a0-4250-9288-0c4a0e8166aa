#!/bin/bash
cd `dirname $0`
BIN_DIR=`pwd`
cd ..


ENV=$1
SERVER_PORT=$2
MEMERY_SIZE="512m"

if [ -z "$ENV" ]; then
    ENV=$APP_ENV
fi


if [ -z "$ENV" ]; then
    # echo "ERROR:PLEASE SPEC ENV ARGS,EG. START.SH ENV SERVER_PORT"
    ENV=`sed '/spring.application.env/!d;s/.*=//' config/application.properties | tr -d '\r'`
    echo "ENV IS EMPTY USE DEFAULT ENV : "$ENV
fi


if [ -z "$SERVER_PORT" ]; then
    SERVER_PORT=`sed '/netty.port/!d;s/.*=//' config/application.properties | tr -d '\r'`
    echo "SERVER_PORT IS EMPTY USE DEFAULT PORT : "$SERVER_PORT
fi

if [[ "$ENV" = "prod" ]]; then
    MEMERY_SIZE="3072m"
fi


DEPLOY_DIR=`pwd`
CONF_DIR=$DEPLOY_DIR/config

SERVER_NAME=`sed '/spring.application.name/!d;s/.*=//' config/application.properties | tr -d '\r'`
LOGS_FILE=`sed '/logging.path/!d;s/.*=//' config/application.properties | tr -d '\r'`
JMX_PORT=`sed '/jmx.port/!d;s/.*=//' config/application.properties | tr -d '\r'`

if [ -z "$SERVER_NAME" ]; then
    SERVER_NAME=`hostname`
fi

SERVER_IDENTITY=$CONF_DIR
PIDS=`ps -ef | grep java | grep "$SERVER_IDENTITY" |awk '{print $2}'`

if [ -n "$PIDS" ]; then
    echo "ERROR: The $SERVER_NAME already started!"
    echo "PID: $PIDS"
    exit 1
fi

LOGS_DIR=""
if [ -n "$LOGS_FILE" ]; then
    LOGS_DIR=`dirname $LOGS_FILE`
else
    LOGS_DIR=$DEPLOY_DIR/logs
fi
if [ ! -d $LOGS_DIR ]; then
    mkdir -p $LOGS_DIR
fi

FAKETIME_OPTIONS=""
if { [ "$ENV" = "test" ] || [[ "$ENV" == "dev" ]]; } && [ -d "/usr/local/faketime" ]; then
  FAKETIME_OPTIONS="-agentpath:/usr/local/faketime/libfaketime -XX:+UnlockDiagnosticVMOptions -XX:DisableIntrinsic=_currentTimeMillis  -XX:CompileCommand=quiet -XX:CompileCommand=exclude,java/lang/System.currentTimeMillis -XX:CompileCommand=exclude,jdk/internal/misc/VM.getNanoTimeAdjustment"
fi

NOW_TIME=$(date "+%Y-%m-%d-%H-%M-%S")
STDOUT_FILE=$LOGS_DIR/"game_server_"$SERVER_PORT"_"$NOW_TIME"_"stdout.log

# core 日志位置
CORE_DUMP_DATE=`date +%Y%m%d%H%M%S`
CORE_DUMP_DIR=/jvm/log/core/$CORE_DUMP_DATE/core.log

JAVA_OPTS=" -Djava.awt.headless=true "
JAVA_DEBUG_OPTS=""
if [ "$3" = "debug" ]; then
    JAVA_DEBUG_OPTS=" -Djava.compiler=NONE -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 "
fi
JAVA_JMX_OPTS=""
if [ "$3" = "jmx" ]; then
    echo "Jmx port is $JMX_PORT"
    if [ -n "$JMX_PORT" ]; then
        JMX_PORT_COUNT=`netstat -tln | grep $JMX_PORT | wc -l`
        if [ $JMX_PORT_COUNT -gt 0 ]; then
            echo "ERROR: The JMX port $JMX_PORT already used!"
            exit 1
        fi
    fi
    echo "Query Internet IP"
    INTERNET_IP=`curl ip.sb`
    echo "Internet IP:$INTERNET_IP"
    JAVA_JMX_OPTS=" -Djava.rmi.server.hostname=$INTERNET_IP -Dcom.sun.management.jmxremote.port=$JMX_PORT -Dcom.sun.management.jmxremote.rmi.port=$JMX_PORT -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false "
fi

JAVA_MEM_OPTS="-server -Xms$MEMERY_SIZE -Xmx$MEMERY_SIZE -Xss512k -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512M -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=2 -XX:ConcGCThreads=2 -XX:ErrorFile=$CORE_DUMP_DIR -Xlog:gc*:file=gc.log:time,uptime,level,tags:filecount=10,filesize=50M"

echo -e "Starting the $SERVER_NAME, PORT : $SERVER_PORT ...\c"
if [ -d "/data/build" ] && [ -d "/data/app" ];  then
    exec java $FAKETIME_OPTIONS $JAVA_OPTS $JAVA_MEM_OPTS $JAVA_DEBUG_OPTS $JAVA_JMX_OPTS -jar *.jar --app.env=$ENV --app.port=$SERVER_PORT $CONF_DIR
else
    nohup java $FAKETIME_OPTIONS $JAVA_OPTS $JAVA_MEM_OPTS $JAVA_DEBUG_OPTS $JAVA_JMX_OPTS -jar *.jar --app.env=$ENV --app.port=$SERVER_PORT $CONF_DIR > $STDOUT_FILE 2>&1 &
    COUNT=0
    while [ $COUNT -lt 1 ]; do
        echo -e ".\c"
        sleep 1
        if [ -n "$SERVER_PORT" ]; then
           COUNT=`netstat -an | grep $SERVER_PORT | wc -l`
        else
            COUNT=`ps -f | grep java | grep "$DEPLOY_DIR" | awk '{print $2}' | wc -l`
        fi
        if [ $COUNT -gt 0 ]; then
            break
        fi
    done

    echo "OK!"
    PIDS=`ps -f | grep java | grep "$DEPLOY_DIR" | awk '{print $2}'`
    echo "PID: $PIDS"
    echo "STDOUT: $STDOUT_FILE"
    timeout 10 tail -f $STDOUT_FILE
    echo 0
fi



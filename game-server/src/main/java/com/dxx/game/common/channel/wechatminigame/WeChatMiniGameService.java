package com.dxx.game.common.channel.wechatminigame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameConfig;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGamePayNotifyVO;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameUnifiedOrderResult;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.CryptUtil;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * @authoer: lsc
 * @createDate: 2023/7/21
 * @description:
 */
@Slf4j
@DependsOn("okHttpClient")
@Component
public class WeChatMiniGameService implements ChannelService {

    ////////// iOS 支付流程 ////////////
    // 1.客户端跳转客服会话
    // 2.微信请求 /wechat/custom_msg 返回卡片信息
    // 3.客户端点击卡片 请求 /wechat/jsapi_pay 打开支付界面

    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private RedisService redisService;
    @Resource
    private AndroidCallBackService androidCallBackService;


    // 小游戏接口token
    private static final String REDIS_TOKEN_KEY = "wechat_mini_game_token";

    // 渠道ID
    private static final ChannelID CHANNEL_ID = ChannelID.WeChatMiniGame;

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        String data = CryptUtil.decode(verification);
        JSONObject jsonObject = JSONObject.parseObject(data);
        String openId = jsonObject.getString("openId");
        if (StringUtils.isEmpty(openId) || !accountId.equals(openId)) {
            return false;
        }
        return true;
    }

    // code换取openid
    public Map<String, Object> code2Session(JSONObject params) {
        String code = params.getString("code");
        String url = channelConfig.getWeChatMiniGameConfig().getLoginUrl();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(url);
        stringBuilder.append("?appid=").append(channelConfig.getWeChatMiniGameConfig().getAppId());
        stringBuilder.append("&secret=").append(channelConfig.getWeChatMiniGameConfig().getSecret());
        stringBuilder.append("&js_code=").append(code);
        stringBuilder.append("&grant_type=authorization_code");

        String response = OkHttpClientUtil.get(stringBuilder.toString(), String.class);

        JSONObject respObj = JSONObject.parseObject(response);
        int errCode = respObj.getIntValue("errcode");
        Map<String, Object> result = new HashMap<>();
        if (errCode != 0) {
            log.error("code2Session failed, params:{}, resp:{}", params, response);
            result.put("errCode", errCode);
            result.put("errMsg", respObj.getString("errmsg"));
            return result;
        }
        String openId = respObj.getString("openid");
        String unionid = respObj.getString("unionid");
        result.put("errCode", 0);
        result.put("openId", openId);
        result.put("unionId", unionid);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("openId", openId);
        jsonObject.put("sessionKey", respObj.getString("session_key"));
        result.put("verification", CryptUtil.encode(jsonObject.toJSONString()));       // 登录验证信息, 掉用游戏服登录接口是回传
        return result;
    }

    public String getAccessToken() {
        String accessToken = redisService.get(REDIS_TOKEN_KEY);
        if (StringUtils.isEmpty(accessToken)) {
            int loopCnt = 0;
            do {
                accessToken = this.reqAccessToken();
                loopCnt++;
            } while (loopCnt != 3 && StringUtils.isEmpty(accessToken));
        }
        return accessToken;
    }

    // 请求token
    public String reqAccessToken() {
        try {
            WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();
            String url = weChatMiniGameConfig.getTokenUrl();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(url);
            stringBuilder.append("?grant_type=client_credential");
            stringBuilder.append("&appid=").append(weChatMiniGameConfig.getAppId());
            stringBuilder.append("&secret=").append(weChatMiniGameConfig.getSecret());

            String response = OkHttpClientUtil.get(stringBuilder.toString(), String.class);

            JSONObject respObj = JSONObject.parseObject(response);
            int errCode = respObj.getIntValue("errcode");
            if (errCode != 0) {
                log.error("reqAccessToken failed, resp:{}", response);
                return null;
            }
            String token = respObj.getString("access_token");
            int expiresIn = respObj.getIntValue("expires_in");

            // redis 保存token
            redisService.set(REDIS_TOKEN_KEY, token);
            // 过期时间 - 60秒
            redisService.expireKey(REDIS_TOKEN_KEY, expiresIn - 60, TimeUnit.SECONDS);
            log.info("reqAccessToken, access_token:{}, expires_in:{}", token, expiresIn);
            return token;

        } catch (Exception e) {
            log.error("reqAccessToken error", e);
            return null;
        }
    }

    @Override
    public Object payCb(FullHttpRequest request) {
        JSONObject result = new JSONObject();
        try {
            if (request.method().equals(HttpMethod.GET)) {
                Map<String, String> params = HttpRequester.getParams(request);
                return params.get("echostr");
            }


            String body = request.content().toString(StandardCharsets.UTF_8);
            log.info("weChatMiniGame payCb body:{}", body);
            PayCbVo payCbVo = this.checkOrderState(body);
            if (payCbVo == null) {
                result.put("ErrCode", 1);
                return result;
            }
            if (payCbVo.isMock()) {
                result.put("ErrCode", 0);
                result.put("ErrMsg", "IsMock");
                return result;
            }

            int code = androidCallBackService.doDeliverGoods(payCbVo);
            if (code != AndroidPayCbErrorCode.SUCCESS) {
                log.error("payCb doDeliverGoods failed, body:{}", payCbVo);
                result.put("ErrCode", 2);
                result.put("ErrMsg", "payCb doDeliverGoods failed");
                return "failure";
            }
            result.put("ErrCode", 0);
            return result;
        } catch (Exception e) {
            result.put("ErrCode", 3);
            result.put("ErrMsg", "Unknown error");
            return result;
        }
    }


    /**
     * 下单接口
     */
    public WeChatMiniGameUnifiedOrderResult unifiedOrder(long userId, String attach, int amount, String wrapperSessionKeyStr, String productId) {


        String cpOrderId = channelConfig.getGameName() + "-" + PaymentUtils.createCpOrderId();

        boolean isWhiteUser = channelConfig.isPayWhiteList(userId);
        boolean isSandBox = channelConfig.isTest();
        WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();

        String appKey = weChatMiniGameConfig.getAppKey();
        String appKeySandBox = weChatMiniGameConfig.getAppKeySandBox();
        String offerId = weChatMiniGameConfig.getOfferId();

        if (StringUtils.isEmpty(wrapperSessionKeyStr)) {
            return null;
        }

//        String originalWrapperSessionKeyStr = analysisSessionKey(wrapperSessionKeyStr);
//        if (org.apache.commons.lang3.StringUtils.isEmpty(originalWrapperSessionKeyStr)) {
//            return null;
//        }
//        JSONObject originalWrapperSessionKey = JSON.parseObject(originalWrapperSessionKeyStr);
//        String sessionKey = originalWrapperSessionKey.getString("sessionKey");

        String sessionKey = wrapperSessionKeyStr;

        String goods = "goods";
        int buyQuantity = 1;
        int env = 0;
        String currencyType = "CNY";
        String realAppKey = appKey;

        if (isWhiteUser && isSandBox) {
            env = 1;
            realAppKey = appKeySandBox;
            log.info("unifiedOrder isWhiteUser and isSandBox, cpOrderId:{}, userId:{}", cpOrderId, userId);
        }


        productId = productId.split("com.dxx.survivor_")[1];
        productId = productId.replaceAll("_", "");
        Map<String, Object> signMap = new TreeMap<>();
        signMap.put("mode", goods);
        signMap.put("offerId", offerId);
        signMap.put("buyQuantity", buyQuantity);
        signMap.put("env", env);
        signMap.put("currencyType", currencyType);
        signMap.put("productId", productId.toLowerCase());
        signMap.put("goodsPrice", amount);
        signMap.put("outTradeNo", cpOrderId);
        signMap.put("attach", attach);

        String signData = JSON.toJSONString(signMap);
        String paySig = CryptUtil.getHmacSha256("requestMidasPaymentGameItem&" + signData, realAppKey.getBytes(StandardCharsets.UTF_8));
        String signature = CryptUtil.getHmacSha256(signData, sessionKey.getBytes(StandardCharsets.UTF_8));

        WeChatMiniGameUnifiedOrderResult result = new WeChatMiniGameUnifiedOrderResult();
        result.setSignData(signData);
        result.setPaySig(paySig);
        result.setSignature(signature);
        return result;
    }

    private PayCbVo checkOrderState(String body) {
        WeChatMiniGamePayNotifyVO payNotifyVO = JSON.parseObject(body, WeChatMiniGamePayNotifyVO.class);
        String msgType = payNotifyVO.getMsgType();
        if (!"event".equals(msgType)) {
            return null;
        }
        WeChatMiniGamePayNotifyVO.MiniGame miniGame = payNotifyVO.getMiniGame();
        if (miniGame == null) {
            log.error("miniGame is null, body:{}", body);
            return null;
        }
        if (miniGame.isMock()) {
            PayCbVo payCbVo = new PayCbVo();
            payCbVo.setMock(true);
            return payCbVo;
        }
        WeChatMiniGameConfig weChatMiniGameConfig = channelConfig.getWeChatMiniGameConfig();
        String appKey = weChatMiniGameConfig.getAppKey();
        boolean isSandBox = channelConfig.isTest();
        if (isSandBox) {
            appKey = weChatMiniGameConfig.getAppKeySandBox();
        }

        String event = payNotifyVO.getEvent();
        String payEventSig = miniGame.getPayEventSig();
        String payloadStr = miniGame.getPayloadStr();


        String sign = CryptUtil.getHmacSha256(event + "&" + payloadStr, appKey.getBytes(StandardCharsets.UTF_8));
        if (!Objects.equals(sign, payEventSig)) {
            log.error("payCb check order state failed sign error, sign:{}, body:{}", sign, body);
            return null;
        }
        WeChatMiniGamePayNotifyVO.Payload payload = JSON.parseObject(payloadStr, WeChatMiniGamePayNotifyVO.Payload.class);
        String outTradeNo = payload.getOutTradeNo();
        String orderId = payload.getWeChatPayInfo().getTransactionId();
        WeChatMiniGamePayNotifyVO.GoodsInfo goodsInfo = payload.getGoodsInfo();
        int actualPrice = goodsInfo.getActualPrice();
        String attach = goodsInfo.getAttach();

        PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(attach);
        PayCbVo payCbVo = new PayCbVo();
        payCbVo.setUserId(payBackParamsModel.getUserId());
        payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
        payCbVo.setOrderId(orderId);
        payCbVo.setCpOrderId(outTradeNo);
        payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
        payCbVo.setProductId(payBackParamsModel.getProductId());
        payCbVo.setChannelId(CHANNEL_ID.getId());
        payCbVo.setAmount(actualPrice);
        return payCbVo;

    }




    // 生成消息推送签名
    private String generateCustomMsgNotifySignature(String signData) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] hashInBytes = md.digest(signData.getBytes(StandardCharsets.UTF_8));
            return CryptUtil.byte2hex(hashInBytes).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }

    private String analysisSessionKey(String sessionKey) {
        String key = channelConfig.getOfficialConfig().getHabbyVerifyLoginKey();
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        IvParameterSpec iv = new IvParameterSpec(keyBytes);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
            byte[] original = cipher.doFinal(Base64.decodeBase64(sessionKey));
            return new String(original);
        } catch (Exception ex) {
            log.error("analysis sessionKey failed, sessionKey:{}", sessionKey, ex);
            return null;
        }
    }

    public static final int MSG_CHECK_SCENE_INFO = 1;

    public boolean msgCheck(long userId, String openid, String content, int scene) {
        String url = channelConfig.getWeChatMiniGameConfig().getMsgSecCheckUrl() + "?access_token=" + this.getAccessToken();
        Map<String, Object> postData = new HashMap<>();
        postData.put("version", 2);
        postData.put("openid", openid);
        postData.put("scene", scene);
        postData.put("content", content);

        String response = OkHttpClientUtil.postJsonWithOutHeader(url, postData, String.class);
        if (StringUtils.isEmpty(response)) {
            return false;
        }
        JSONObject respObj = JSONObject.parseObject(response);
        if (respObj.getIntValue("errcode") != 0) {
            log.error("msg_sec_check failed, userId:{}, postData:{}, resp:{}", userId, postData, response);
            return false;
        }
        String suggest = respObj.getJSONObject("result").getString("suggest");
        int label = respObj.getJSONObject("result").getIntValue("label");
        if (label != 100) {
            log.error("msg_sec_check failed, userId:{}, postData:{}, resp:{}", userId, postData, response);
            return true;
        }
        return false;

    }

}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: personalboss.proto

package com.dxx.game.dto;

public final class PersonalBossProto {
  private PersonalBossProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PersonalBossBattleRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.PersonalBoss.PersonalBossBattleRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=12001 个人boss-战斗
   * </pre>
   *
   * Protobuf type {@code Proto.PersonalBoss.PersonalBossBattleRequest}
   */
  public static final class PersonalBossBattleRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.PersonalBoss.PersonalBossBattleRequest)
      PersonalBossBattleRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PersonalBossBattleRequest.newBuilder() to construct.
    private PersonalBossBattleRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PersonalBossBattleRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PersonalBossBattleRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PersonalBossBattleRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.class, com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest other = (com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=12001 个人boss-战斗
     * </pre>
     *
     * Protobuf type {@code Proto.PersonalBoss.PersonalBossBattleRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.PersonalBoss.PersonalBossBattleRequest)
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.class, com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest build() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest buildPartial() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest result = new com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest) {
          return mergeFrom((com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest other) {
        if (other == com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.PersonalBoss.PersonalBossBattleRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.PersonalBoss.PersonalBossBattleRequest)
    private static final com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest();
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PersonalBossBattleRequest>
        PARSER = new com.google.protobuf.AbstractParser<PersonalBossBattleRequest>() {
      @java.lang.Override
      public PersonalBossBattleRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PersonalBossBattleRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PersonalBossBattleRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PersonalBossBattleRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PersonalBossBattleResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.PersonalBoss.PersonalBossBattleResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 本次伤害
     * </pre>
     *
     * <code>uint64 damage = 3;</code>
     * @return The damage.
     */
    long getDamage();

    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 4;</code>
     * @return The seed.
     */
    int getSeed();

    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getStartUnitsList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index);
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    int getStartUnitsCount();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getEndUnitsList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index);
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    int getEndUnitsCount();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index);
  }
  /**
   * <pre>
   *CMD PackageId=12002
   * </pre>
   *
   * Protobuf type {@code Proto.PersonalBoss.PersonalBossBattleResponse}
   */
  public static final class PersonalBossBattleResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.PersonalBoss.PersonalBossBattleResponse)
      PersonalBossBattleResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PersonalBossBattleResponse.newBuilder() to construct.
    private PersonalBossBattleResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PersonalBossBattleResponse() {
      startUnits_ = java.util.Collections.emptyList();
      endUnits_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PersonalBossBattleResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PersonalBossBattleResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              damage_ = input.readUInt64();
              break;
            }
            case 32: {

              seed_ = input.readInt32();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              startUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000002;
              }
              endUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.class, com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int DAMAGE_FIELD_NUMBER = 3;
    private long damage_;
    /**
     * <pre>
     * 本次伤害
     * </pre>
     *
     * <code>uint64 damage = 3;</code>
     * @return The damage.
     */
    @java.lang.Override
    public long getDamage() {
      return damage_;
    }

    public static final int SEED_FIELD_NUMBER = 4;
    private int seed_;
    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 4;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    public static final int STARTUNITS_FIELD_NUMBER = 5;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_;
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    @java.lang.Override
    public int getStartUnitsCount() {
      return startUnits_.size();
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
      return startUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index) {
      return startUnits_.get(index);
    }

    public static final int ENDUNITS_FIELD_NUMBER = 6;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_;
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    @java.lang.Override
    public int getEndUnitsCount() {
      return endUnits_.size();
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
      return endUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index) {
      return endUnits_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (damage_ != 0L) {
        output.writeUInt64(3, damage_);
      }
      if (seed_ != 0) {
        output.writeInt32(4, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        output.writeMessage(5, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        output.writeMessage(6, endUnits_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (damage_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, damage_);
      }
      if (seed_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, endUnits_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse other = (com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getDamage()
          != other.getDamage()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (!getStartUnitsList()
          .equals(other.getStartUnitsList())) return false;
      if (!getEndUnitsList()
          .equals(other.getEndUnitsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + DAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDamage());
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + getSeed();
      if (getStartUnitsCount() > 0) {
        hash = (37 * hash) + STARTUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getStartUnitsList().hashCode();
      }
      if (getEndUnitsCount() > 0) {
        hash = (37 * hash) + ENDUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getEndUnitsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=12002
     * </pre>
     *
     * Protobuf type {@code Proto.PersonalBoss.PersonalBossBattleResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.PersonalBoss.PersonalBossBattleResponse)
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.class, com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getStartUnitsFieldBuilder();
          getEndUnitsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        damage_ = 0L;

        seed_ = 0;

        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          startUnitsBuilder_.clear();
        }
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          endUnitsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse build() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse buildPartial() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse result = new com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.damage_ = damage_;
        result.seed_ = seed_;
        if (startUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.startUnits_ = startUnits_;
        } else {
          result.startUnits_ = startUnitsBuilder_.build();
        }
        if (endUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.endUnits_ = endUnits_;
        } else {
          result.endUnits_ = endUnitsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse) {
          return mergeFrom((com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse other) {
        if (other == com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getDamage() != 0L) {
          setDamage(other.getDamage());
        }
        if (other.getSeed() != 0) {
          setSeed(other.getSeed());
        }
        if (startUnitsBuilder_ == null) {
          if (!other.startUnits_.isEmpty()) {
            if (startUnits_.isEmpty()) {
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureStartUnitsIsMutable();
              startUnits_.addAll(other.startUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.startUnits_.isEmpty()) {
            if (startUnitsBuilder_.isEmpty()) {
              startUnitsBuilder_.dispose();
              startUnitsBuilder_ = null;
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
              startUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getStartUnitsFieldBuilder() : null;
            } else {
              startUnitsBuilder_.addAllMessages(other.startUnits_);
            }
          }
        }
        if (endUnitsBuilder_ == null) {
          if (!other.endUnits_.isEmpty()) {
            if (endUnits_.isEmpty()) {
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureEndUnitsIsMutable();
              endUnits_.addAll(other.endUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.endUnits_.isEmpty()) {
            if (endUnitsBuilder_.isEmpty()) {
              endUnitsBuilder_.dispose();
              endUnitsBuilder_ = null;
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
              endUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEndUnitsFieldBuilder() : null;
            } else {
              endUnitsBuilder_.addAllMessages(other.endUnits_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long damage_ ;
      /**
       * <pre>
       * 本次伤害
       * </pre>
       *
       * <code>uint64 damage = 3;</code>
       * @return The damage.
       */
      @java.lang.Override
      public long getDamage() {
        return damage_;
      }
      /**
       * <pre>
       * 本次伤害
       * </pre>
       *
       * <code>uint64 damage = 3;</code>
       * @param value The damage to set.
       * @return This builder for chaining.
       */
      public Builder setDamage(long value) {
        
        damage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次伤害
       * </pre>
       *
       * <code>uint64 damage = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDamage() {
        
        damage_ = 0L;
        onChanged();
        return this;
      }

      private int seed_ ;
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 4;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 4;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        
        seed_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_ =
        java.util.Collections.emptyList();
      private void ensureStartUnitsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(startUnits_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> startUnitsBuilder_;

      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
        if (startUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(startUnits_);
        } else {
          return startUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public int getStartUnitsCount() {
        if (startUnitsBuilder_ == null) {
          return startUnits_.size();
        } else {
          return startUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);
        } else {
          return startUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.set(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder addStartUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder addStartUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder addAllStartUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, startUnits_);
          onChanged();
        } else {
          startUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder clearStartUnits() {
        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          startUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public Builder removeStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.remove(index);
          onChanged();
        } else {
          startUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
          int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);  } else {
          return startUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getStartUnitsOrBuilderList() {
        if (startUnitsBuilder_ != null) {
          return startUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(startUnits_);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder() {
        return getStartUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 5;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getStartUnitsBuilderList() {
        return getStartUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getStartUnitsFieldBuilder() {
        if (startUnitsBuilder_ == null) {
          startUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  startUnits_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          startUnits_ = null;
        }
        return startUnitsBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_ =
        java.util.Collections.emptyList();
      private void ensureEndUnitsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(endUnits_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> endUnitsBuilder_;

      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
        if (endUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(endUnits_);
        } else {
          return endUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public int getEndUnitsCount() {
        if (endUnitsBuilder_ == null) {
          return endUnits_.size();
        } else {
          return endUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);
        } else {
          return endUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.set(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder addEndUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder addEndUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder addAllEndUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, endUnits_);
          onChanged();
        } else {
          endUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder clearEndUnits() {
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          endUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public Builder removeEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.remove(index);
          onChanged();
        } else {
          endUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
          int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);  } else {
          return endUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getEndUnitsOrBuilderList() {
        if (endUnitsBuilder_ != null) {
          return endUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(endUnits_);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder() {
        return getEndUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getEndUnitsBuilderList() {
        return getEndUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getEndUnitsFieldBuilder() {
        if (endUnitsBuilder_ == null) {
          endUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  endUnits_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          endUnits_ = null;
        }
        return endUnitsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.PersonalBoss.PersonalBossBattleResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.PersonalBoss.PersonalBossBattleResponse)
    private static final com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse();
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PersonalBossBattleResponse>
        PARSER = new com.google.protobuf.AbstractParser<PersonalBossBattleResponse>() {
      @java.lang.Override
      public PersonalBossBattleResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PersonalBossBattleResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PersonalBossBattleResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PersonalBossBattleResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.PersonalBossProto.PersonalBossBattleResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PersonalBossRankRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.PersonalBoss.PersonalBossRankRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    int getPage();
  }
  /**
   * <pre>
   *CMD PackageId=12003 个人boss-排行榜
   * </pre>
   *
   * Protobuf type {@code Proto.PersonalBoss.PersonalBossRankRequest}
   */
  public static final class PersonalBossRankRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.PersonalBoss.PersonalBossRankRequest)
      PersonalBossRankRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PersonalBossRankRequest.newBuilder() to construct.
    private PersonalBossRankRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PersonalBossRankRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PersonalBossRankRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PersonalBossRankRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              page_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.class, com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (page_ != 0) {
        output.writeInt32(2, page_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest other = (com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getPage()
          != other.getPage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=12003 个人boss-排行榜
     * </pre>
     *
     * Protobuf type {@code Proto.PersonalBoss.PersonalBossRankRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.PersonalBoss.PersonalBossRankRequest)
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.class, com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        page_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest build() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest buildPartial() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest result = new com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.page_ = page_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest) {
          return mergeFrom((com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest other) {
        if (other == com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int page_ ;
      /**
       * <code>int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.PersonalBoss.PersonalBossRankRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.PersonalBoss.PersonalBossRankRequest)
    private static final com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest();
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PersonalBossRankRequest>
        PARSER = new com.google.protobuf.AbstractParser<PersonalBossRankRequest>() {
      @java.lang.Override
      public PersonalBossRankRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PersonalBossRankRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PersonalBossRankRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PersonalBossRankRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.PersonalBossProto.PersonalBossRankRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PersonalBossRankResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.PersonalBoss.PersonalBossRankResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.RankDto> 
        getRankList();
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    com.dxx.game.dto.CommonProto.RankDto getRank(int index);
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    int getRankCount();
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.RankDtoOrBuilder> 
        getRankOrBuilderList();
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    com.dxx.game.dto.CommonProto.RankDtoOrBuilder getRankOrBuilder(
        int index);
  }
  /**
   * <pre>
   *CMD PackageId=12004
   * </pre>
   *
   * Protobuf type {@code Proto.PersonalBoss.PersonalBossRankResponse}
   */
  public static final class PersonalBossRankResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.PersonalBoss.PersonalBossRankResponse)
      PersonalBossRankResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PersonalBossRankResponse.newBuilder() to construct.
    private PersonalBossRankResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PersonalBossRankResponse() {
      rank_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PersonalBossRankResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PersonalBossRankResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rank_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RankDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              rank_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.RankDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rank_ = java.util.Collections.unmodifiableList(rank_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.class, com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int RANK_FIELD_NUMBER = 3;
    private java.util.List<com.dxx.game.dto.CommonProto.RankDto> rank_;
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.RankDto> getRankList() {
      return rank_;
    }
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.RankDtoOrBuilder> 
        getRankOrBuilderList() {
      return rank_;
    }
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    @java.lang.Override
    public int getRankCount() {
      return rank_.size();
    }
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RankDto getRank(int index) {
      return rank_.get(index);
    }
    /**
     * <code>repeated .Proto.Common.RankDto rank = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RankDtoOrBuilder getRankOrBuilder(
        int index) {
      return rank_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      for (int i = 0; i < rank_.size(); i++) {
        output.writeMessage(3, rank_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      for (int i = 0; i < rank_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, rank_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse other = (com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getRankList()
          .equals(other.getRankList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (getRankCount() > 0) {
        hash = (37 * hash) + RANK_FIELD_NUMBER;
        hash = (53 * hash) + getRankList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=12004
     * </pre>
     *
     * Protobuf type {@code Proto.PersonalBoss.PersonalBossRankResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.PersonalBoss.PersonalBossRankResponse)
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.class, com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRankFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (rankBuilder_ == null) {
          rank_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rankBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossRankResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse build() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse buildPartial() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse result = new com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (rankBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rank_ = java.util.Collections.unmodifiableList(rank_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rank_ = rank_;
        } else {
          result.rank_ = rankBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse) {
          return mergeFrom((com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse other) {
        if (other == com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (rankBuilder_ == null) {
          if (!other.rank_.isEmpty()) {
            if (rank_.isEmpty()) {
              rank_ = other.rank_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankIsMutable();
              rank_.addAll(other.rank_);
            }
            onChanged();
          }
        } else {
          if (!other.rank_.isEmpty()) {
            if (rankBuilder_.isEmpty()) {
              rankBuilder_.dispose();
              rankBuilder_ = null;
              rank_ = other.rank_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankFieldBuilder() : null;
            } else {
              rankBuilder_.addAllMessages(other.rank_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.RankDto> rank_ =
        java.util.Collections.emptyList();
      private void ensureRankIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rank_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RankDto>(rank_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.RankDto, com.dxx.game.dto.CommonProto.RankDto.Builder, com.dxx.game.dto.CommonProto.RankDtoOrBuilder> rankBuilder_;

      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RankDto> getRankList() {
        if (rankBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rank_);
        } else {
          return rankBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public int getRankCount() {
        if (rankBuilder_ == null) {
          return rank_.size();
        } else {
          return rankBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.RankDto getRank(int index) {
        if (rankBuilder_ == null) {
          return rank_.get(index);
        } else {
          return rankBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder setRank(
          int index, com.dxx.game.dto.CommonProto.RankDto value) {
        if (rankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankIsMutable();
          rank_.set(index, value);
          onChanged();
        } else {
          rankBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder setRank(
          int index, com.dxx.game.dto.CommonProto.RankDto.Builder builderForValue) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder addRank(com.dxx.game.dto.CommonProto.RankDto value) {
        if (rankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankIsMutable();
          rank_.add(value);
          onChanged();
        } else {
          rankBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder addRank(
          int index, com.dxx.game.dto.CommonProto.RankDto value) {
        if (rankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankIsMutable();
          rank_.add(index, value);
          onChanged();
        } else {
          rankBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder addRank(
          com.dxx.game.dto.CommonProto.RankDto.Builder builderForValue) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.add(builderForValue.build());
          onChanged();
        } else {
          rankBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder addRank(
          int index, com.dxx.game.dto.CommonProto.RankDto.Builder builderForValue) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder addAllRank(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.RankDto> values) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rank_);
          onChanged();
        } else {
          rankBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder clearRank() {
        if (rankBuilder_ == null) {
          rank_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public Builder removeRank(int index) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.remove(index);
          onChanged();
        } else {
          rankBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.RankDto.Builder getRankBuilder(
          int index) {
        return getRankFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.RankDtoOrBuilder getRankOrBuilder(
          int index) {
        if (rankBuilder_ == null) {
          return rank_.get(index);  } else {
          return rankBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.RankDtoOrBuilder> 
           getRankOrBuilderList() {
        if (rankBuilder_ != null) {
          return rankBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rank_);
        }
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.RankDto.Builder addRankBuilder() {
        return getRankFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.RankDto.getDefaultInstance());
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.RankDto.Builder addRankBuilder(
          int index) {
        return getRankFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.RankDto.getDefaultInstance());
      }
      /**
       * <code>repeated .Proto.Common.RankDto rank = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RankDto.Builder> 
           getRankBuilderList() {
        return getRankFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.RankDto, com.dxx.game.dto.CommonProto.RankDto.Builder, com.dxx.game.dto.CommonProto.RankDtoOrBuilder> 
          getRankFieldBuilder() {
        if (rankBuilder_ == null) {
          rankBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.RankDto, com.dxx.game.dto.CommonProto.RankDto.Builder, com.dxx.game.dto.CommonProto.RankDtoOrBuilder>(
                  rank_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rank_ = null;
        }
        return rankBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.PersonalBoss.PersonalBossRankResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.PersonalBoss.PersonalBossRankResponse)
    private static final com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse();
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PersonalBossRankResponse>
        PARSER = new com.google.protobuf.AbstractParser<PersonalBossRankResponse>() {
      @java.lang.Override
      public PersonalBossRankResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PersonalBossRankResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PersonalBossRankResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PersonalBossRankResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.PersonalBossProto.PersonalBossRankResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PersonalBossInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.PersonalBoss.PersonalBossInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=12005 个人boss-信息
   * </pre>
   *
   * Protobuf type {@code Proto.PersonalBoss.PersonalBossInfoRequest}
   */
  public static final class PersonalBossInfoRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.PersonalBoss.PersonalBossInfoRequest)
      PersonalBossInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PersonalBossInfoRequest.newBuilder() to construct.
    private PersonalBossInfoRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PersonalBossInfoRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PersonalBossInfoRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PersonalBossInfoRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.class, com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest other = (com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=12005 个人boss-信息
     * </pre>
     *
     * Protobuf type {@code Proto.PersonalBoss.PersonalBossInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.PersonalBoss.PersonalBossInfoRequest)
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.class, com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest build() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest buildPartial() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest result = new com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest) {
          return mergeFrom((com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest other) {
        if (other == com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.PersonalBoss.PersonalBossInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.PersonalBoss.PersonalBossInfoRequest)
    private static final com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest();
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PersonalBossInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<PersonalBossInfoRequest>() {
      @java.lang.Override
      public PersonalBossInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PersonalBossInfoRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PersonalBossInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PersonalBossInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PersonalBossInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.PersonalBoss.PersonalBossInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * bossId
     * </pre>
     *
     * <code>int32 bossId = 3;</code>
     * @return The bossId.
     */
    int getBossId();

    /**
     * <pre>
     * 个人排名
     * </pre>
     *
     * <code>int32 rank = 4;</code>
     * @return The rank.
     */
    int getRank();

    /**
     * <pre>
     * 刷新时间
     * </pre>
     *
     * <code>int64 refTime = 5;</code>
     * @return The refTime.
     */
    long getRefTime();

    /**
     * <pre>
     * 今日最高伤害
     * </pre>
     *
     * <code>int64 maxDamage = 6;</code>
     * @return The maxDamage.
     */
    long getMaxDamage();
  }
  /**
   * <pre>
   *CMD PackageId=12006
   * </pre>
   *
   * Protobuf type {@code Proto.PersonalBoss.PersonalBossInfoResponse}
   */
  public static final class PersonalBossInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.PersonalBoss.PersonalBossInfoResponse)
      PersonalBossInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PersonalBossInfoResponse.newBuilder() to construct.
    private PersonalBossInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PersonalBossInfoResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PersonalBossInfoResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PersonalBossInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              bossId_ = input.readInt32();
              break;
            }
            case 32: {

              rank_ = input.readInt32();
              break;
            }
            case 40: {

              refTime_ = input.readInt64();
              break;
            }
            case 48: {

              maxDamage_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.class, com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int BOSSID_FIELD_NUMBER = 3;
    private int bossId_;
    /**
     * <pre>
     * bossId
     * </pre>
     *
     * <code>int32 bossId = 3;</code>
     * @return The bossId.
     */
    @java.lang.Override
    public int getBossId() {
      return bossId_;
    }

    public static final int RANK_FIELD_NUMBER = 4;
    private int rank_;
    /**
     * <pre>
     * 个人排名
     * </pre>
     *
     * <code>int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }

    public static final int REFTIME_FIELD_NUMBER = 5;
    private long refTime_;
    /**
     * <pre>
     * 刷新时间
     * </pre>
     *
     * <code>int64 refTime = 5;</code>
     * @return The refTime.
     */
    @java.lang.Override
    public long getRefTime() {
      return refTime_;
    }

    public static final int MAXDAMAGE_FIELD_NUMBER = 6;
    private long maxDamage_;
    /**
     * <pre>
     * 今日最高伤害
     * </pre>
     *
     * <code>int64 maxDamage = 6;</code>
     * @return The maxDamage.
     */
    @java.lang.Override
    public long getMaxDamage() {
      return maxDamage_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (bossId_ != 0) {
        output.writeInt32(3, bossId_);
      }
      if (rank_ != 0) {
        output.writeInt32(4, rank_);
      }
      if (refTime_ != 0L) {
        output.writeInt64(5, refTime_);
      }
      if (maxDamage_ != 0L) {
        output.writeInt64(6, maxDamage_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (bossId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, bossId_);
      }
      if (rank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, rank_);
      }
      if (refTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, refTime_);
      }
      if (maxDamage_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, maxDamage_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse other = (com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getBossId()
          != other.getBossId()) return false;
      if (getRank()
          != other.getRank()) return false;
      if (getRefTime()
          != other.getRefTime()) return false;
      if (getMaxDamage()
          != other.getMaxDamage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + BOSSID_FIELD_NUMBER;
      hash = (53 * hash) + getBossId();
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
      hash = (37 * hash) + REFTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRefTime());
      hash = (37 * hash) + MAXDAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMaxDamage());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=12006
     * </pre>
     *
     * Protobuf type {@code Proto.PersonalBoss.PersonalBossInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.PersonalBoss.PersonalBossInfoResponse)
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.class, com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        bossId_ = 0;

        rank_ = 0;

        refTime_ = 0L;

        maxDamage_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.PersonalBossProto.internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse build() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse buildPartial() {
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse result = new com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.bossId_ = bossId_;
        result.rank_ = rank_;
        result.refTime_ = refTime_;
        result.maxDamage_ = maxDamage_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse) {
          return mergeFrom((com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse other) {
        if (other == com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getBossId() != 0) {
          setBossId(other.getBossId());
        }
        if (other.getRank() != 0) {
          setRank(other.getRank());
        }
        if (other.getRefTime() != 0L) {
          setRefTime(other.getRefTime());
        }
        if (other.getMaxDamage() != 0L) {
          setMaxDamage(other.getMaxDamage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int bossId_ ;
      /**
       * <pre>
       * bossId
       * </pre>
       *
       * <code>int32 bossId = 3;</code>
       * @return The bossId.
       */
      @java.lang.Override
      public int getBossId() {
        return bossId_;
      }
      /**
       * <pre>
       * bossId
       * </pre>
       *
       * <code>int32 bossId = 3;</code>
       * @param value The bossId to set.
       * @return This builder for chaining.
       */
      public Builder setBossId(int value) {
        
        bossId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * bossId
       * </pre>
       *
       * <code>int32 bossId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBossId() {
        
        bossId_ = 0;
        onChanged();
        return this;
      }

      private int rank_ ;
      /**
       * <pre>
       * 个人排名
       * </pre>
       *
       * <code>int32 rank = 4;</code>
       * @return The rank.
       */
      @java.lang.Override
      public int getRank() {
        return rank_;
      }
      /**
       * <pre>
       * 个人排名
       * </pre>
       *
       * <code>int32 rank = 4;</code>
       * @param value The rank to set.
       * @return This builder for chaining.
       */
      public Builder setRank(int value) {
        
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个人排名
       * </pre>
       *
       * <code>int32 rank = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRank() {
        
        rank_ = 0;
        onChanged();
        return this;
      }

      private long refTime_ ;
      /**
       * <pre>
       * 刷新时间
       * </pre>
       *
       * <code>int64 refTime = 5;</code>
       * @return The refTime.
       */
      @java.lang.Override
      public long getRefTime() {
        return refTime_;
      }
      /**
       * <pre>
       * 刷新时间
       * </pre>
       *
       * <code>int64 refTime = 5;</code>
       * @param value The refTime to set.
       * @return This builder for chaining.
       */
      public Builder setRefTime(long value) {
        
        refTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 刷新时间
       * </pre>
       *
       * <code>int64 refTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefTime() {
        
        refTime_ = 0L;
        onChanged();
        return this;
      }

      private long maxDamage_ ;
      /**
       * <pre>
       * 今日最高伤害
       * </pre>
       *
       * <code>int64 maxDamage = 6;</code>
       * @return The maxDamage.
       */
      @java.lang.Override
      public long getMaxDamage() {
        return maxDamage_;
      }
      /**
       * <pre>
       * 今日最高伤害
       * </pre>
       *
       * <code>int64 maxDamage = 6;</code>
       * @param value The maxDamage to set.
       * @return This builder for chaining.
       */
      public Builder setMaxDamage(long value) {
        
        maxDamage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 今日最高伤害
       * </pre>
       *
       * <code>int64 maxDamage = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxDamage() {
        
        maxDamage_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.PersonalBoss.PersonalBossInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.PersonalBoss.PersonalBossInfoResponse)
    private static final com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse();
    }

    public static com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PersonalBossInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<PersonalBossInfoResponse>() {
      @java.lang.Override
      public PersonalBossInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PersonalBossInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PersonalBossInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PersonalBossInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.PersonalBossProto.PersonalBossInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_PersonalBoss_PersonalBossRankRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_PersonalBoss_PersonalBossRankRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_PersonalBoss_PersonalBossRankResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_PersonalBoss_PersonalBossRankResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022personalboss.proto\022\022Proto.PersonalBoss" +
      "\032\014common.proto\"M\n\031PersonalBossBattleRequ" +
      "est\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Common" +
      ".CommonParams\"\326\001\n\032PersonalBossBattleResp" +
      "onse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030" +
      ".Proto.Common.CommonData\022\016\n\006damage\030\003 \001(\004" +
      "\022\014\n\004seed\030\004 \001(\005\022/\n\nstartUnits\030\005 \003(\0132\033.Pro" +
      "to.Common.CombatUnitDto\022-\n\010endUnits\030\006 \003(" +
      "\0132\033.Proto.Common.CombatUnitDto\"Y\n\027Person" +
      "alBossRankRequest\0220\n\014commonParams\030\001 \001(\0132" +
      "\032.Proto.Common.CommonParams\022\014\n\004page\030\002 \001(" +
      "\005\"{\n\030PersonalBossRankResponse\022\014\n\004code\030\001 " +
      "\001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Common.C" +
      "ommonData\022#\n\004rank\030\003 \003(\0132\025.Proto.Common.R" +
      "ankDto\"K\n\027PersonalBossInfoRequest\0220\n\014com" +
      "monParams\030\001 \001(\0132\032.Proto.Common.CommonPar" +
      "ams\"\230\001\n\030PersonalBossInfoResponse\022\014\n\004code" +
      "\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Commo" +
      "n.CommonData\022\016\n\006bossId\030\003 \001(\005\022\014\n\004rank\030\004 \001" +
      "(\005\022\017\n\007refTime\030\005 \001(\003\022\021\n\tmaxDamage\030\006 \001(\003B%" +
      "\n\020com.dxx.game.dtoB\021PersonalBossProtob\006p" +
      "roto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_PersonalBoss_PersonalBossBattleRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_PersonalBoss_PersonalBossBattleResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Damage", "Seed", "StartUnits", "EndUnits", });
    internal_static_Proto_PersonalBoss_PersonalBossRankRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_PersonalBoss_PersonalBossRankRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_PersonalBoss_PersonalBossRankRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Page", });
    internal_static_Proto_PersonalBoss_PersonalBossRankResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_PersonalBoss_PersonalBossRankResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_PersonalBoss_PersonalBossRankResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Rank", });
    internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_PersonalBoss_PersonalBossInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_PersonalBoss_PersonalBossInfoResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "BossId", "Rank", "RefTime", "MaxDamage", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

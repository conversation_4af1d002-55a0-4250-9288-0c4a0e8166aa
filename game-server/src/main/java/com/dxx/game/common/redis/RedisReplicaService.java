package com.dxx.game.common.redis;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 从库-读节点
 * @author: lsc
 * @createDate: 2025/3/18
 * @description:
 */
@Component
public class RedisReplicaService {

    @Resource(name = "replicaRedisTemplate")
    private RedisTemplate<String, String> replicaRedisTemplate;


    public String get(String key) {
        return replicaRedisTemplate.opsForValue().get(key);
    }

    public long getLongValue(String key) {
        String value = this.get(key);
        if (value == null) {
            return 0;
        }
        return Long.parseLong(value);
    }
}

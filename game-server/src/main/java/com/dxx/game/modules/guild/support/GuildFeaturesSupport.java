package com.dxx.game.modules.guild.support;

import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dto.GuildProto.GuildFeaturesDto;
import com.dxx.game.dto.GuildProto.GuildUpdateInfoDto;
import com.dxx.game.modules.guild.consts.GuildShopType;
import com.dxx.game.modules.guild.service.*;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/12
 * @description:
 */
@Component
public class GuildFeaturesSupport {



    @Resource
    private GuildTaskService guildTaskService;
    @Resource
    private GuildShopService guildShopService;
    @Resource
    private GuildSignInService guildSignInService;
    @Resource
    private GuildBossService guildBossService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private GuildDonationService guildDonationService;
    @Resource
    private GuildService guildService;

    @Resource
    private GuildDao guildDao;

    public GuildUpdateInfoDto buildGuildUpdateInfo(long guildId) {
        Guild guild = guildDao.getByGuildId(guildId);
        return this.buildGuildUpdateInfo(guild);
    }

    public GuildUpdateInfoDto buildGuildUpdateInfo(Guild guild) {
        GuildUpdateInfoDto.Builder guildUpdateInfo = GuildUpdateInfoDto.newBuilder();
        guildUpdateInfo.setActive(guild.getGuildActive());
        guildUpdateInfo.setExp(guild.getGuildExp());
        guildUpdateInfo.setLevel(guild.getGuildLevel());
        guildUpdateInfo.setMaxMembers(guild.getGuildMaxMembersCount());
        return guildUpdateInfo.build();
    }

    // 公会功能数据对象
    public GuildFeaturesDto buildGuildFeaturesDto(Guild guild, GuildUser guildUser) {
        GuildFeaturesDto.Builder result = GuildFeaturesDto.newBuilder();
        result.addAllTasks(guildTaskService.buildTaskDtoList(guildUser));
        result.addAllDailyShop(guildShopService.buildShopDtoList(GuildShopType.SHOP_DAILY, guildUser));
        result.addAllWeeklyShop(guildShopService.buildShopDtoList(GuildShopType.SHOP_WEEKLY, guildUser));
        result.setSignInDto(guildSignInService.buildSignInDto(guildUser));
        result.setDailyRefreshTime(guildUser.getDailyTM());
        result.setWeeklyRefreshTime(guildUser.getWeeklyTM());
        result.setGuildBossInfo(guildBossService.buildGuildBossInfoDto(guild, guildUser, true));
        result.setTaskRefreshCount(guildTaskService.getRefreshTaskFreeCount(guildUser));
        result.setMaxTaskRefreshCount(gameConfigManager.getGuildConfig().getGuildConstEntity(130).getTypeInt());
        result.setTaskRefreshCost(guildTaskService.getRefreshTaskCost(guildUser));
        result.addAllSignInRecords(guildSignInService.getSignInRecords(guild));
        result.setDonationDto(guildDonationService.buildGuildDonationDto(guildUser));
        result.setApplyCount(guild.getGuildApplyJoinedCount());
        return result.build();
    }






}

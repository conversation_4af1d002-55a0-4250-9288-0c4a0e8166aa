## 说明!
- jdk 版本 21.0.7
- Amazon Corretto 21

## 目录结构 
| 目录                    | 说明        |
|-----------------------|-----------|
| common                | 核心        |
| config                | 配置文件      |
| consts                | 常量类       |
| cron                  | 定时器server |
| dao                   | 数据库       |
| dto                   | proto协议   |
| modules               | 功能模块      |
| GameServerApplication | 项目启动类     |
| tools                 | 工具目录      |


## 新项目启动前需要的流程
1. 启动数据库并且同步表, node db/scripts/sync_dynamodb_table.js {env} env 指的是resources目录下的名称

## aws cli 配置
- pip install awscli                    // 下载工具
- aws configure --profile danke_en      // 添加配置
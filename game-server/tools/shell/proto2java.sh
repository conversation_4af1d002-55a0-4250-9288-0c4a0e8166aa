#!/bin/sh



sh ../proto/proto2java.sh

# dir_name=`pwd`

# if [ ! -d $dir_name"/../proto/data" ]; then
# 	mkdir -p $dir_name"/../proto/data"
# fi

# cur_path=$dir_name/../proto/proto/
# java_out_path=$dir_name/../../src/main/java/

# node $dir_name/../proto/filetime.js $cur_path

# for file in `ls $cur_path`
# do

# 	node $dir_name/../proto/checkmodify.js ${file%.*}
# 	if [ $? -eq 0 ];then
# 	    continue
# 	fi
# 	echo $file
	
# 	protoc --java_out=$java_out_path --proto_path=$cur_path $file
# done


# node $dir_name/../node_modules/protobufjs/bin/pbjs -t json $dir_name/../proto/proto/*.proto > proto.json
# mv proto.json $dir_name/../api/proto/
# node $dir_name/../proto/createMsgId.js $cur_path

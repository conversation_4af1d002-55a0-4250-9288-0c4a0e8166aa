package com.dxx.game.modules.guild.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.modules.guild.service.GuildSignInService;
import com.google.protobuf.Message;
import com.dxx.game.dto.GuildProto.*;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@ApiHandler
public class GuildSignInHandler {

    @Resource
    private GuildSignInService guildSignInService;

    @ApiMethod(command = MsgReqCommand.GuildSignInRequest, name = "公会-签到")
    public Result<GuildSignInResponse> signIn(Message msg) {
        GuildSignInRequest params = (GuildSignInRequest)msg;
        return guildSignInService.signInAction(params);
    }
}

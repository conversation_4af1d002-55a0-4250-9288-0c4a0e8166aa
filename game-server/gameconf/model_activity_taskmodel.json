[{"Id": 10101, "GroupType": 101, "LableType": 1, "TaskType": 8, "CountType": 1, "Need": 1, "Describe": "4201", "ProgressType": 0, "Param": [], "Reward": ["9100101,1"], "Jump": 1, "UnlockNeed": 3}, {"Id": 10102, "GroupType": 101, "LableType": 1, "TaskType": 6, "CountType": 1, "Need": 5, "Describe": "4204", "ProgressType": 0, "Param": [], "Reward": ["9100101,1"], "Jump": 2, "UnlockNeed": 73}, {"Id": 10103, "GroupType": 101, "LableType": 1, "TaskType": 7, "CountType": 1, "Need": 5, "Describe": "4206", "ProgressType": 0, "Param": [], "Reward": ["9100101,2"], "Jump": 5, "UnlockNeed": 15}, {"Id": 10104, "GroupType": 101, "LableType": 1, "TaskType": 16, "CountType": 1, "Need": 2, "Describe": "4213", "ProgressType": 0, "Param": [], "Reward": ["9100101,2"], "Jump": 16, "UnlockNeed": 30}, {"Id": 10105, "GroupType": 101, "LableType": 1, "TaskType": 5, "CountType": 1, "Need": 15, "Describe": "4211", "ProgressType": 0, "Param": [], "Reward": ["9100101,2"], "Jump": 8, "UnlockNeed": 51}, {"Id": 10111, "GroupType": 101, "LableType": 2, "TaskType": 30, "CountType": 1, "Need": 20, "Describe": "4330", "ProgressType": 0, "Param": [], "Reward": ["9100101,1"], "Jump": 19, "UnlockNeed": 58}, {"Id": 10112, "GroupType": 101, "LableType": 2, "TaskType": 30, "CountType": 1, "Need": 40, "Describe": "4330", "ProgressType": 0, "Param": [], "Reward": ["9100101,1"], "Jump": 19, "UnlockNeed": 58}, {"Id": 10113, "GroupType": 101, "LableType": 2, "TaskType": 30, "CountType": 1, "Need": 60, "Describe": "4330", "ProgressType": 0, "Param": [], "Reward": ["9100101,2"], "Jump": 19, "UnlockNeed": 58}, {"Id": 10114, "GroupType": 101, "LableType": 2, "TaskType": 30, "CountType": 1, "Need": 80, "Describe": "4330", "ProgressType": 0, "Param": [], "Reward": ["9100101,2"], "Jump": 19, "UnlockNeed": 58}, {"Id": 10115, "GroupType": 101, "LableType": 2, "TaskType": 30, "CountType": 1, "Need": 100, "Describe": "4330", "ProgressType": 0, "Param": [], "Reward": ["9100101,3"], "Jump": 19, "UnlockNeed": 58}, {"Id": 10116, "GroupType": 101, "LableType": 2, "TaskType": 27, "CountType": 0, "Need": 5, "Describe": "4327", "ProgressType": 1, "Param": [], "Reward": ["9100101,1"], "Jump": 7, "UnlockNeed": 74}, {"Id": 10117, "GroupType": 101, "LableType": 2, "TaskType": 28, "CountType": 0, "Need": 5, "Describe": "4328", "ProgressType": 1, "Param": [], "Reward": ["9100101,2"], "Jump": 7, "UnlockNeed": 74}, {"Id": 10118, "GroupType": 101, "LableType": 2, "TaskType": 27, "CountType": 0, "Need": 7, "Describe": "4327", "ProgressType": 1, "Param": [], "Reward": ["9100101,2"], "Jump": 7, "UnlockNeed": 74}, {"Id": 10119, "GroupType": 101, "LableType": 2, "TaskType": 28, "CountType": 0, "Need": 7, "Describe": "4328", "ProgressType": 1, "Param": [], "Reward": ["9100101,3"], "Jump": 7, "UnlockNeed": 74}, {"Id": 10120, "GroupType": 101, "LableType": 2, "TaskType": 27, "CountType": 0, "Need": 9, "Describe": "4327", "ProgressType": 1, "Param": [], "Reward": ["9100101,3"], "Jump": 7, "UnlockNeed": 74}, {"Id": 10121, "GroupType": 101, "LableType": 2, "TaskType": 28, "CountType": 0, "Need": 9, "Describe": "4328", "ProgressType": 1, "Param": [], "Reward": ["9100101,5"], "Jump": 7, "UnlockNeed": 74}, {"Id": 10122, "GroupType": 101, "LableType": 2, "TaskType": 29, "CountType": 1, "Need": 10, "Describe": "4329", "ProgressType": 0, "Param": [], "Reward": ["9100101,2"], "Jump": 6, "UnlockNeed": 16}, {"Id": 10123, "GroupType": 101, "LableType": 2, "TaskType": 29, "CountType": 1, "Need": 20, "Describe": "4329", "ProgressType": 0, "Param": [], "Reward": ["9100101,3"], "Jump": 6, "UnlockNeed": 16}, {"Id": 10124, "GroupType": 101, "LableType": 2, "TaskType": 29, "CountType": 1, "Need": 40, "Describe": "4329", "ProgressType": 0, "Param": [], "Reward": ["9100101,4"], "Jump": 6, "UnlockNeed": 16}, {"Id": 10125, "GroupType": 101, "LableType": 2, "TaskType": 29, "CountType": 1, "Need": 60, "Describe": "4329", "ProgressType": 0, "Param": [], "Reward": ["9100101,5"], "Jump": 6, "UnlockNeed": 16}, {"Id": 20101, "GroupType": 201, "LableType": 1, "TaskType": 8, "CountType": 1, "Need": 1, "Describe": "4201", "ProgressType": 0, "Param": [], "Reward": ["9100407,5"], "Jump": 1, "UnlockNeed": 3}, {"Id": 20102, "GroupType": 201, "LableType": 1, "TaskType": 18, "CountType": 1, "Need": 2, "Describe": "4215", "ProgressType": 0, "Param": [], "Reward": ["9100407,8"], "Jump": 13, "UnlockNeed": 72}, {"Id": 20103, "GroupType": 201, "LableType": 1, "TaskType": 1, "CountType": 1, "Need": 1, "Describe": "4205", "ProgressType": 0, "Param": [], "Reward": ["9100407,6"], "Jump": 1, "UnlockNeed": 3}, {"Id": 20104, "GroupType": 201, "LableType": 1, "TaskType": 17, "CountType": 1, "Need": 2, "Describe": "4214", "ProgressType": 0, "Param": [], "Reward": ["9100407,8"], "Jump": 17, "UnlockNeed": 31}, {"Id": 20105, "GroupType": 201, "LableType": 1, "TaskType": 12, "CountType": 1, "Need": 1, "Describe": "4207", "ProgressType": 0, "Param": [], "Reward": ["9100407,8"], "Jump": 11, "UnlockNeed": 17}, {"Id": 20111, "GroupType": 201, "LableType": 2, "TaskType": 31, "CountType": 1, "Need": 15, "Describe": "4331", "ProgressType": 0, "Param": [], "Reward": ["9100407,8"], "Jump": 20, "UnlockNeed": 58}, {"Id": 20112, "GroupType": 201, "LableType": 2, "TaskType": 31, "CountType": 1, "Need": 25, "Describe": "4331", "ProgressType": 0, "Param": [], "Reward": ["9100407,8"], "Jump": 20, "UnlockNeed": 58}, {"Id": 20113, "GroupType": 201, "LableType": 2, "TaskType": 31, "CountType": 1, "Need": 35, "Describe": "4331", "ProgressType": 0, "Param": [], "Reward": ["9100407,10"], "Jump": 20, "UnlockNeed": 58}, {"Id": 20114, "GroupType": 201, "LableType": 2, "TaskType": 31, "CountType": 1, "Need": 45, "Describe": "4331", "ProgressType": 0, "Param": [], "Reward": ["9100407,10"], "Jump": 20, "UnlockNeed": 58}, {"Id": 20115, "GroupType": 201, "LableType": 2, "TaskType": 31, "CountType": 1, "Need": 55, "Describe": "4331", "ProgressType": 0, "Param": [], "Reward": ["9100407,15"], "Jump": 20, "UnlockNeed": 58}, {"Id": 20116, "GroupType": 201, "LableType": 2, "TaskType": 36, "CountType": 1, "Need": 5, "Describe": "4336", "ProgressType": 0, "Param": [], "Reward": ["9100407,8"], "Jump": 13, "UnlockNeed": 70}, {"Id": 20117, "GroupType": 201, "LableType": 2, "TaskType": 36, "CountType": 1, "Need": 10, "Describe": "4336", "ProgressType": 0, "Param": [], "Reward": ["9100407,10"], "Jump": 13, "UnlockNeed": 70}, {"Id": 20118, "GroupType": 201, "LableType": 2, "TaskType": 36, "CountType": 1, "Need": 15, "Describe": "4336", "ProgressType": 0, "Param": [], "Reward": ["9100407,10"], "Jump": 13, "UnlockNeed": 70}, {"Id": 20119, "GroupType": 201, "LableType": 2, "TaskType": 36, "CountType": 1, "Need": 20, "Describe": "4336", "ProgressType": 0, "Param": [], "Reward": ["9100407,12"], "Jump": 13, "UnlockNeed": 70}, {"Id": 20120, "GroupType": 201, "LableType": 2, "TaskType": 36, "CountType": 1, "Need": 25, "Describe": "4336", "ProgressType": 0, "Param": [], "Reward": ["9100407,12"], "Jump": 13, "UnlockNeed": 70}, {"Id": 20121, "GroupType": 201, "LableType": 2, "TaskType": 36, "CountType": 1, "Need": 30, "Describe": "4336", "ProgressType": 0, "Param": [], "Reward": ["9100407,16"], "Jump": 13, "UnlockNeed": 70}, {"Id": 20122, "GroupType": 201, "LableType": 2, "TaskType": 22, "CountType": 1, "Need": 2, "Describe": "4322", "ProgressType": 1, "Param": [], "Reward": ["9100407,6"], "Jump": 14, "UnlockNeed": 70}, {"Id": 20123, "GroupType": 201, "LableType": 2, "TaskType": 22, "CountType": 1, "Need": 4, "Describe": "4322", "ProgressType": 1, "Param": [], "Reward": ["9100407,8"], "Jump": 14, "UnlockNeed": 70}, {"Id": 20124, "GroupType": 201, "LableType": 2, "TaskType": 22, "CountType": 1, "Need": 6, "Describe": "4322", "ProgressType": 1, "Param": [], "Reward": ["9100407,8"], "Jump": 14, "UnlockNeed": 70}, {"Id": 20125, "GroupType": 201, "LableType": 2, "TaskType": 22, "CountType": 1, "Need": 8, "Describe": "4322", "ProgressType": 1, "Param": [], "Reward": ["9100407,12"], "Jump": 14, "UnlockNeed": 70}, {"Id": 20126, "GroupType": 201, "LableType": 2, "TaskType": 22, "CountType": 1, "Need": 10, "Describe": "4322", "ProgressType": 1, "Param": [], "Reward": ["9100407,12"], "Jump": 14, "UnlockNeed": 70}, {"Id": 20127, "GroupType": 201, "LableType": 2, "TaskType": 22, "CountType": 1, "Need": 15, "Describe": "4322", "ProgressType": 1, "Param": [], "Reward": ["9100407,15"], "Jump": 14, "UnlockNeed": 70}, {"Id": 30101, "GroupType": 301, "LableType": 1, "TaskType": 8, "CountType": 1, "Need": 1, "Describe": "4201", "ProgressType": 0, "Param": [], "Reward": ["9100501,5"], "Jump": 1, "UnlockNeed": 3}, {"Id": 30102, "GroupType": 301, "LableType": 1, "TaskType": 2, "CountType": 1, "Need": 3, "Describe": "4203", "ProgressType": 0, "Param": [], "Reward": ["9100501,8"], "Jump": 2, "UnlockNeed": 5}, {"Id": 30103, "GroupType": 301, "LableType": 1, "TaskType": 19, "CountType": 1, "Need": 1, "Describe": "4216", "ProgressType": 0, "Param": [], "Reward": ["9100501,6"], "Jump": 18, "UnlockNeed": 19}, {"Id": 30104, "GroupType": 301, "LableType": 1, "TaskType": 14, "CountType": 1, "Need": 3, "Describe": "4210", "ProgressType": 0, "Param": [], "Reward": ["9100501,8"], "Jump": 12, "UnlockNeed": 2}, {"Id": 30105, "GroupType": 301, "LableType": 1, "TaskType": 11, "CountType": 1, "Need": 3, "Describe": "4208", "ProgressType": 0, "Param": [], "Reward": ["9100501,8"], "Jump": 6, "UnlockNeed": 16}, {"Id": 30111, "GroupType": 301, "LableType": 2, "TaskType": 32, "CountType": 1, "Need": 50, "Describe": "4332", "ProgressType": 0, "Param": [], "Reward": ["9100501,8"], "Jump": 21, "UnlockNeed": 58}, {"Id": 30112, "GroupType": 301, "LableType": 2, "TaskType": 32, "CountType": 1, "Need": 100, "Describe": "4332", "ProgressType": 0, "Param": [], "Reward": ["9100501,8"], "Jump": 21, "UnlockNeed": 58}, {"Id": 30113, "GroupType": 301, "LableType": 2, "TaskType": 32, "CountType": 1, "Need": 200, "Describe": "4332", "ProgressType": 0, "Param": [], "Reward": ["9100501,10"], "Jump": 21, "UnlockNeed": 58}, {"Id": 30114, "GroupType": 301, "LableType": 2, "TaskType": 32, "CountType": 1, "Need": 300, "Describe": "4332", "ProgressType": 0, "Param": [], "Reward": ["9100501,10"], "Jump": 21, "UnlockNeed": 58}, {"Id": 30115, "GroupType": 301, "LableType": 2, "TaskType": 32, "CountType": 1, "Need": 500, "Describe": "4332", "ProgressType": 0, "Param": [], "Reward": ["9100501,15"], "Jump": 21, "UnlockNeed": 58}, {"Id": 30116, "GroupType": 301, "LableType": 2, "TaskType": 23, "CountType": 0, "Need": 6, "Describe": "4323", "ProgressType": 0, "Param": [], "Reward": ["9100501,8"], "Jump": 2, "UnlockNeed": 2}, {"Id": 30117, "GroupType": 301, "LableType": 2, "TaskType": 23, "CountType": 0, "Need": 11, "Describe": "4323", "ProgressType": 0, "Param": [], "Reward": ["9100501,10"], "Jump": 2, "UnlockNeed": 2}, {"Id": 30118, "GroupType": 301, "LableType": 2, "TaskType": 23, "CountType": 0, "Need": 14, "Describe": "4323", "ProgressType": 0, "Param": [], "Reward": ["9100501,10"], "Jump": 2, "UnlockNeed": 2}, {"Id": 30119, "GroupType": 301, "LableType": 2, "TaskType": 23, "CountType": 0, "Need": 16, "Describe": "4323", "ProgressType": 0, "Param": [], "Reward": ["9100501,12"], "Jump": 2, "UnlockNeed": 2}, {"Id": 30120, "GroupType": 301, "LableType": 2, "TaskType": 23, "CountType": 0, "Need": 18, "Describe": "4323", "ProgressType": 0, "Param": [], "Reward": ["9100501,12"], "Jump": 2, "UnlockNeed": 2}, {"Id": 30121, "GroupType": 301, "LableType": 2, "TaskType": 23, "CountType": 0, "Need": 21, "Describe": "4323", "ProgressType": 0, "Param": [], "Reward": ["9100501,16"], "Jump": 2, "UnlockNeed": 2}, {"Id": 30122, "GroupType": 301, "LableType": 2, "TaskType": 10, "CountType": 1, "Need": 5, "Describe": "4310", "ProgressType": 1, "Param": [], "Reward": ["9100501,6"], "Jump": 9, "UnlockNeed": 53}, {"Id": 30123, "GroupType": 301, "LableType": 2, "TaskType": 10, "CountType": 1, "Need": 10, "Describe": "4310", "ProgressType": 1, "Param": [], "Reward": ["9100501,8"], "Jump": 9, "UnlockNeed": 53}, {"Id": 30124, "GroupType": 301, "LableType": 2, "TaskType": 10, "CountType": 1, "Need": 20, "Describe": "4310", "ProgressType": 1, "Param": [], "Reward": ["9100501,8"], "Jump": 9, "UnlockNeed": 53}, {"Id": 30125, "GroupType": 301, "LableType": 2, "TaskType": 10, "CountType": 1, "Need": 40, "Describe": "4310", "ProgressType": 1, "Param": [], "Reward": ["9100501,12"], "Jump": 9, "UnlockNeed": 53}, {"Id": 30126, "GroupType": 301, "LableType": 2, "TaskType": 10, "CountType": 1, "Need": 60, "Describe": "4310", "ProgressType": 1, "Param": [], "Reward": ["9100501,12"], "Jump": 9, "UnlockNeed": 53}, {"Id": 30127, "GroupType": 301, "LableType": 2, "TaskType": 10, "CountType": 1, "Need": 80, "Describe": "4310", "ProgressType": 1, "Param": [], "Reward": ["9100501,15"], "Jump": 9, "UnlockNeed": 53}]
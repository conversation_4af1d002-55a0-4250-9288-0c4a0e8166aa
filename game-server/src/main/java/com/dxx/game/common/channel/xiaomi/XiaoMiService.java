package com.dxx.game.common.channel.xiaomi;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.server.handler.HttpRequester;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 小米
 * <AUTHOR>
 * @date 2022/4/21 17:06
 */
@Slf4j
@Service
public class XiaoMiService implements ChannelService {

    // 渠道ID
    private static final ChannelID CHANNEL_ID = ChannelID.XiaoMi;

    private static final String MAC_NAME = "HmacSHA1";
    private static final String ENCODING = "UTF-8";

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private AndroidCallBackService androidCallBackService;


    /**
     * 支付回调接口
     */
    @Override
    public Object payCb(FullHttpRequest request) {

        Map<String, String> params = HttpRequester.getParams(request);

        // 验证appId
        String appId = params.get("appId");
        if (!appId.equals(channelConfig.getXiaoMiConfig().getAppId())) {
            return this.error(1515, "appId error");
        }

        // 验证签名
        PayCbVo payCbVo = this.checkOrderState(request, params);
        if (payCbVo == null) {
            return this.error(1525, "signature failed");
        }

        int code = androidCallBackService.doDeliverGoods(payCbVo);
        if (code != AndroidPayCbErrorCode.SUCCESS) {
            log.error("payCb doDeliverGoods failed, body:{}", payCbVo);
            return this.error(304, "doDeliverGoods failed");
        }

        return this.success();
    }

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        try {
            if (!channelConfig.getXiaoMiConfig().isVerifyLogin()) {
                return true;
            }
            JSONObject jsonObject = JSONObject.parseObject(verification);
            String uid = jsonObject.getString("uid");
            if (!uid.equals(accountId)) {
                return false;
            }
            Map<String, String> params = new HashMap<String, String>();
            params.put("appId", channelConfig.getXiaoMiConfig().getAppId());
            params.put("uid", uid);
            params.put("session", jsonObject.getString("sessionId"));

            String requestUrl = getRequestUrl(params, channelConfig.getXiaoMiConfig().getVerifySessionUrl());
            String resp = OkHttpClientUtil.get(requestUrl, String.class);
            JSONObject respObj = JSONObject.parseObject(resp);
            int code = respObj.getIntValue("errcode");
            if (code != 200) {
                log.error("xiaomi login failed, resp:{}", resp);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("xiaomi login error:", e);
            return false;
        }
    }

    /**
     * 校验订单状态
     * @param request
     * @return
     */
    private PayCbVo checkOrderState(FullHttpRequest request, Map<String, String> params) {
        String queryString = HttpRequester.getQueryString(request);
        try {
            Map<String,String> signParams = getSignParams(queryString);
            String tmpSign = this.getSign(signParams);
            String sign = params.get("signature");
            String orderStatus = params.get("orderStatus");
            if (!orderStatus.equals("TRADE_SUCCESS")) {
                log.error("payCb failed, orderStatusError:{}", orderStatus);
                return null;
            }
            boolean check = tmpSign.equals(sign);
            if (!check) {
                log.error("payCb check sign failed, queryString:{}", queryString);
                return null;
            }


            String cpUserInfo = params.get("cpUserInfo");
            String cpOrderId = params.get("cpOrderId");
            String orderId = params.get("orderId");
            int price = Integer.parseInt(params.get("payFee"));
            PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(cpUserInfo);

            PayCbVo payCbVo = new PayCbVo();
            payCbVo.setUserId(payBackParamsModel.getUserId());
            payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
            payCbVo.setOrderId(orderId);
            payCbVo.setCpOrderId(cpOrderId);
            payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
            payCbVo.setProductId(payBackParamsModel.getProductId());
            payCbVo.setChannelId(CHANNEL_ID.getId());
            payCbVo.setAmount(price);
            return payCbVo;

        } catch (Exception e) {
            log.error("payCb check order state failed, queryString:{}, e:", queryString, e);
            return null;
        }
    }

    private Map<String,String> getSignParams(String queryString) throws UnsupportedEncodingException {

        Map<String,String> result = new HashMap<String, String>();
        String[] params = queryString.split("&");
        for(String param : params){
            String[] tmp = param.split("=");
            String key = tmp[0];
            if(!"signature".equals(key)) {
                result.put(key, URLDecoder.decode(tmp[1],"UTF-8"));
            }
        }
        return result;
    }

    private String getSign(Map<String,String> params) throws Exception {
        String signString = PaymentUtils.getSortQueryString(params);
        return this.hmacSHA1Encrypt(signString, channelConfig.getXiaoMiConfig().getAppSecret());
    }

    private String hmacSHA1Encrypt(String encryptText, String encryptKey) throws Exception {
        byte[] data = encryptKey.getBytes(ENCODING);
        // 根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
        SecretKey secretKey = new SecretKeySpec(data, MAC_NAME);
        // 生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(MAC_NAME);
        // 用给定密钥初始化 Mac 对象
        mac.init( secretKey );
        byte[] text = encryptText.getBytes(ENCODING);
        // 完成 Mac 操作
        byte[] digest = mac.doFinal( text );
        return bytesToHexString(digest);
    }

    private String bytesToHexString(byte[] bytesArray) {
        if (bytesArray == null) {
            return "";
        }
        StringBuilder sBuilder = new StringBuilder();
        for (byte b : bytesArray) {
            String hv = String.format("%02x", b);
            sBuilder.append(hv);
        }
        return sBuilder.toString();
    }

    private String getRequestUrl(Map<String,String> params, String baseUrl) throws Exception{
        String signString = PaymentUtils.getSortQueryString(params);
        String signature = getSign(params);
        return baseUrl + "?" + signString + "&signature=" + signature;
    }

    // 成功返回
    private Map<String, Object> success() {
        Map<String, Object> result = new HashMap<>();
        result.put("errcode", 200);
        result.put("errMsg", "");
        return result;
    }

    // 失败返回
    private Map<String, Object> error(int code, String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("errcode", code);
        result.put("errMsg", msg);
        return result;
    }
}

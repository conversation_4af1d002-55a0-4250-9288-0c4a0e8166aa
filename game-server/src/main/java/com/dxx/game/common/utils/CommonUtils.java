package com.dxx.game.common.utils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/27 14:26
 */
public class CommonUtils {

    /**
     * 将null对象转成空字符串
     * @param str
     * @return
     */
    public static String nullToEmpty(String str) {
        if (str == null) {
            return "";
        }
        return str;
    }

    /**
     * 首字母转小写
     * @param str
     * @return
     */
    public static String lowerFirstCase(String str){
        char[] chars = str.toCharArray();
        chars[0] +=32;
        return String.valueOf(chars);
    }

    /**
     * 通用检测客户端传的数量合法范围
     */
    public static boolean checkUseCountLimit(int count) {
        return count > 0 && count <= 999;
    }

    public static <K, V> boolean isContain(Map<K, V> map, K key) {
        return map != null && map.containsKey(key);
    }
}

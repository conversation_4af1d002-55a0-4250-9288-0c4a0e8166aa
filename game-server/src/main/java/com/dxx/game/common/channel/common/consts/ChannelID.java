package com.dxx.game.common.channel.common.consts;

/**
 * <AUTHOR>
 * @date 2022/5/9 9:01 下午
 */
public enum ChannelID {
    UNITY(0, "unity"),
    APPLE(1, "apple"),
    GOO<PERSON>LE(2,"google"),
    HUAWEI_OVERSEAS(3, "huaweiOverseas"),
    HABBY_STORE(4, "habbyStore"),

    WeChat(101, "weChat"),
    <PERSON><PERSON>ay(102, "aliPay"),

    <PERSON><PERSON><PERSON><PERSON>(201, "hua<PERSON>ei"),
    YYB(202, "yyb"),
    <PERSON><PERSON><PERSON>(203, "xiaomi"),
    <PERSON><PERSON>(204, "oppo"),
    <PERSON><PERSON>(205, "vivo"),
    <PERSON>sjj(206, "ssjj"),
    UC(207, "uc"),
    <PERSON><PERSON>(208, "bili"),
    <PERSON><PERSON><PERSON><PERSON>(209, "douYin"),
    <PERSON>(210, "honor"),
    HAR<PERSON><PERSON><PERSON>(211, "harmony"),
    <PERSON>aiSHou(212, "kuai<PERSON><PERSON>"),
    <PERSON>Y<PERSON><PERSON>(213, "yybad"),

    WeChatMiniGame(301, "wx_mini"),     // 正常云支付
    WeChatJS(302, "wx_js"),             // jsapi支付
    DouYinMiniGame(401, "douyin_mini"), // 抖音小游戏
    MY_CARD(501, "myCard");

    private int id;
    private String name;

    ChannelID(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public static ChannelID valueOf(int id) {
        for (ChannelID index : ChannelID.values()) {
            if (index.getId() == id) {
                return index;
            }
        }
        return null;
    }
}

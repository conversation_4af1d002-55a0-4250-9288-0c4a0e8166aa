package com.dxx.game.modules.mail.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.item.ItemEntity;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.reward.service.RewardService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/2/10 17:44
 */
@Slf4j
@Service
public class MailServiceImpl implements MailService {

    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private CommonService commonService;

    @Override
    public boolean createMail(long userId, String mailTempLateId, Map<String, String> params, List<List<Integer>> rewards){
        return this.createMail(userId,mailTempLateId, UUID.randomUUID().toString(),rewards,params,false,"none",0);
    }

    @Override
    public boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards) {
        return this.createMail(userId, mailTemplateId, uniqueId, rewards, null, false, "none", 0);
    }

    @Override
    public boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards, String source) {
        return this.createMail(userId, mailTemplateId, uniqueId, rewards, null, false, source, 0);
    }

    @Override
    public boolean createMail(long userId, String mailTemplateId, Object uniqueId, Map<String, String> params, List<List<Integer>> rewards) {
        return this.createMail(userId, mailTemplateId, uniqueId, rewards, params, false, "none", 0);
    }

    @Override
    public boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards, String source, int activityId) {
        return this.createMail(userId, mailTemplateId, uniqueId, rewards, null, false, source, activityId);
    }

    @Override
    public boolean createMail(long userId, String mailTemplateId, Object uniqueId, Map<String, String> params, List<List<Integer>> rewards, String source, int activityId) {
        return this.createMail(userId, mailTemplateId, uniqueId, rewards, params, false, source, activityId);
    }

    @Override
    public boolean createMailAsync(long userId, String mailTempLateId, Map<String, String> params, List<List<Integer>> rewards) {
        return this.createMail(userId, mailTempLateId, UUID.randomUUID().toString(), rewards, params, true, "none", 0);

    }

    @Override
    public boolean createMailAsync(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards) {
        return this.createMail(userId, mailTemplateId, uniqueId, rewards, null, true, "none", 0);
    }

    @Override
    public boolean createMail(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards,
                              Map<String, String> params, boolean async, String source, int activityId) {

//        return this.createMailForHabby(userId, mailTemplateId, uniqueId, rewards, params, async, source, activityId);
        return this.createMailForGorilla(userId, mailTemplateId, uniqueId, rewards, params, async, source, activityId);
    }

    private boolean createMailForHabby(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards,
                              Map<String, String> params, boolean async, String source, int activityId) {
        try {
            // 整合奖励配置
            List<List<Integer>> combineConfig = rewardService.combineRewards(rewards);
            String url = gameConfigManager.getEmailServerApiUrl() + gameConfigManager.getMainConfig().getEmailApiConfig().getEmail_uri();
            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");

            JSONObject postJson = new JSONObject();

            // 接受者
            List<Map<String, Object>> users = new ArrayList<>();
            Map<String, Object> userData = new HashMap<>();
            userData.put("userId", String.valueOf(userId));
            users.add(userData);

            // 奖励
            JSONArray items = new JSONArray();
            for (List<Integer> reward : combineConfig) {
                JSONObject rewardJson = new JSONObject();

                if (reward.size() < 3) {
                    ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(reward.get(0));
                    rewardJson.put("id", String.valueOf(reward.get(0)));
                    rewardJson.put("amount", reward.get(1));
                    rewardJson.put("itemType", itemEntity.getItemType());
                } else {
                    rewardJson.put("id", String.valueOf(reward.get(1)));
                    rewardJson.put("amount", reward.get(2));
                    rewardJson.put("itemType", reward.get(0));
                }

                items.add(rewardJson);
            }

            long createTime = DateUtils.getUnixTime();
            if (gameConfigManager.isTest() || gameConfigManager.isDevelop()) {
                createTime = createTime - DateUtils.SECONDS_30_DAY;
            }
            long expireTime = DateUtils.getUnixTime() + 7 * 86400;

            String mailTransId;
            if (uniqueId instanceof Long || uniqueId instanceof  Integer) {
                long value = Long.parseLong(uniqueId.toString());
                if (value == 0) {
                    value = this.getUniqueId();
                }
                mailTransId = String.valueOf(value);
            } else {
                mailTransId = uniqueId.toString();
            }

            postJson.put("transId", mailTransId);
            postJson.put("habbySecret", gameConfigManager.getMainConfig().getEmailApiConfig().getEmail_server_api_secret());
            postJson.put("mailTemplateId", mailTemplateId);
            JSONObject customJSON = new JSONObject();
            customJSON.put("rewardSource", source);
            customJSON.put("activityId", activityId);
            postJson.put("customJSONStr", customJSON.toJSONString());

            if (params != null && !params.isEmpty()) {
                postJson.put("placeholderData", new HashMap<String, Object>(){{
                    put("en", params);
                }});
            }
            postJson.put("users", users);
            postJson.put("rewards", items);
            postJson.put("effectiveAt", DateUtils.getUTCTimeStr(createTime - 30));
            postJson.put("expireAt", DateUtils.getUTCTimeStr(expireTime));

            log.info("create-mail-data-userId:{}, tempId:{}, rewards:{}, header:{}, postJson:{}", userId, mailTemplateId, rewards, header, postJson);

            if (async) {
                OkHttpClientUtil.postJson(url, header, postJson, new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        log.error("create mail failed, userId:{}, tempId:{}, rewards:{}", userId, mailTemplateId, rewards);
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (!response.isSuccessful()) {
                            log.error("create mail failed, userId:{}, tempId:{}, rewards:{}, response:{}", userId, mailTemplateId, rewards, response.code());
                            return;
                        }
                        String resp = response.body().string();
                        if (gameConfigManager.isPrintApiLog()) {
                            log.info("sendMail, tmpId:{}, postData:{}, response:{}", mailTemplateId, postJson, response);
                        }
                        checkResponse(resp, postJson, userId);
                    }
                });
                return true;
            } else {
                String response = OkHttpClientUtil.postJson(url, header, postJson);
                if (gameConfigManager.isPrintApiLog()) {
                    log.info("sendMail, tmpId:{}, postData:{}, response:{}", mailTemplateId, postJson, response);
                }
                return this.checkResponse(response, postJson, userId);
            }
        } catch (Exception e) {
            log.error("sendMailError:", e);
            return false;
        }

    }


    private boolean createMailForGorilla(long userId, String mailTemplateId, Object uniqueId, List<List<Integer>> rewards,
                                       Map<String, String> params, boolean async, String source, int activityId) {
        try {

            GorillaEmailApiConfig.GorillaEmailApi gorillaEmailApiConfig = this.getGorillaEmailApiConfig();
            if (gorillaEmailApiConfig == null) {
                log.error("gorillaEmailApiConfig is null, env:{}", gameConfigManager.getApplicationEnv());
                return false;
            }

            // 整合奖励配置
            List<List<Integer>> combineConfig = rewardService.combineRewards(rewards);

            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");

            JSONObject postJson = new JSONObject();

            // 接受者
            List<String> userIds = new ArrayList<>();
            userIds.add(String.valueOf(userId));
            postJson.put("userIds", userIds);

            // 奖励
            JSONArray items = new JSONArray();
            for (List<Integer> reward : combineConfig) {
                JSONObject rewardJson = new JSONObject();

                if (reward.size() < 3) {
                    ItemEntity itemEntity = gameConfigManager.getItemConfig().getItemEntity(reward.get(0));
                    rewardJson.put("id", String.valueOf(reward.get(0)));
                    rewardJson.put("amount", reward.get(1));
                    rewardJson.put("itemType", itemEntity.getItemType());
                } else {
                    rewardJson.put("id", String.valueOf(reward.get(1)));
                    rewardJson.put("amount", reward.get(2));
                    rewardJson.put("itemType", reward.get(0));
                }

                items.add(rewardJson);
            }

            long createTime = DateUtils.getUnixTime();
            if (gameConfigManager.isTest() || gameConfigManager.isDevelop()) {
                createTime = createTime - DateUtils.SECONDS_30_DAY;
            }
            long expireTime = DateUtils.getUnixTime() + 7 * 86400;

            String mailTransId;
            if (uniqueId instanceof Long || uniqueId instanceof  Integer) {
                long value = Long.parseLong(uniqueId.toString());
                if (value == 0) {
                    value = this.getUniqueId();
                }
                mailTransId = String.valueOf(value);
            } else {
                mailTransId = uniqueId.toString();
            }

            postJson.put("transId", mailTransId);
            postJson.put("serviceToken", gorillaEmailApiConfig.getToken());
            postJson.put("secret", gorillaEmailApiConfig.getSecret());
            postJson.put("mailTemplateId", mailTemplateId);
            JSONObject customJSON = new JSONObject();
            customJSON.put("rewardSource", source);
            customJSON.put("activityId", activityId);
            postJson.put("customJSONStr", customJSON.toJSONString());

            if (params != null && !params.isEmpty()) {
                postJson.put("placeholderData", new HashMap<String, Object>(){{
                    put("en", params);
                }});
            }
            postJson.put("rewards", items);
            postJson.put("effectiveAt", (createTime - 30));
            postJson.put("expireAt", expireTime);

            log.info("create-mail-data-userId:{}, tempId:{}, rewards:{}, header:{}, postJson:{}", userId, mailTemplateId, rewards, header, postJson);

            String url = gorillaEmailApiConfig.getUrl() + gorillaEmailApiConfig.getUri();
            if (async) {
                OkHttpClientUtil.postJson(url, header, postJson, new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        log.error("create mail failed, userId:{}, tempId:{}, rewards:{}", userId, mailTemplateId, rewards);
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (!response.isSuccessful()) {
                            log.error("create mail failed, userId:{}, tempId:{}, rewards:{}, response:{}", userId, mailTemplateId, rewards, response.code());
                            return;
                        }
                        String resp = response.body().string();
                        if (gameConfigManager.isPrintApiLog()) {
                            log.info("sendMail, tmpId:{}, postData:{}, response:{}", mailTemplateId, postJson, response);
                        }
                        checkResponse(resp, postJson, userId);
                    }
                });
                return true;
            } else {
                String response = OkHttpClientUtil.postJson(url, header, postJson);
                if (gameConfigManager.isPrintApiLog()) {
                    log.info("sendMail, tmpId:{}, postData:{}, response:{}", mailTemplateId, postJson, response);
                }
                return this.checkResponse(response, postJson, userId);
            }
        } catch (Exception e) {
            log.error("sendMailError:", e);
            return false;
        }

    }

    @Override
    public long getUniqueId() {
        return commonService.generateId(RedisKeys.SERVER_MAIL_PRIMARY_KEY);
    }

    private boolean checkResponse(String response, JSONObject postJson, long userId) {
        JSONObject respObj = JSONObject.parseObject(response);
        int code = respObj.getIntValue("code");
        if (code != 0) {
            log.error("sendMailError, userId:{}, postJson:{}, response:{}", userId, postJson, response);
            return false;
        } else {
            JSONObject dataObj = respObj.getJSONObject("data");
            JSONArray jsonArray = dataObj.getJSONArray("fail");
            if (!jsonArray.isEmpty() && jsonArray.contains(String.valueOf(userId))) {
                log.error("sendMailError, userId:{}, postJson:{}, response:{}", userId, postJson, response);
                return false;
            }
        }
        return true;
    }

    private GorillaEmailApiConfig.GorillaEmailApi getGorillaEmailApiConfig() {
        String env = gameConfigManager.getApplicationEnv();
        if (env.equals("pre")) {
            env = "prod";
        }
        return gameConfigManager.getMainConfig().getGorillaEmailApiConfig().getGorillaEmailApiMap().get(env);
    }
}

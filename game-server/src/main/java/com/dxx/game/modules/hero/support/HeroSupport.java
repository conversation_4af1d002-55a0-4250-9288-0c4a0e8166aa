package com.dxx.game.modules.hero.support;

import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.gamemember.AdvanceEntity;
import com.dxx.game.config.entity.gamemember.LevelupEntity;
import com.dxx.game.config.entity.systemequip.UpdateLevelEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.equip.support.EquipSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class HeroSupport {

    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private EquipSupport equipSupport;
    @Autowired
    EquipService equipService;

    /**
     * 设置英雄的等级
     *
     * @param userId        用户id
     * @param hero          英雄实体
     * @param level         设置的等级
     * @param isAutoAdvance 是否自动升降等阶
     */
    public Pair<Integer, List<List<Integer>>> setHeroLevel(long userId, Hero hero, int level, boolean isAutoAdvance) {
        if (hero == null) {
            return Pair.of(ErrorCode.HERO_NOT_EXIST, Collections.emptyList());
        }

        if (level <= 0) {//英雄等级最低1级
            return Pair.of(ErrorCode.PARAMS_ERROR, Collections.emptyList());
        }
        int currentLevel = hero.getLevel();
        if (Objects.equals(currentLevel, level)) {//当前等级和设置的等级一样，不做任何操作直接返回成功
            return Pair.of(ErrorCode.SUCCESS, Collections.emptyList());
        }
        int heroAdvance = hero.getAdvance();
        int heroId = hero.getHeroId();

        if (level > currentLevel) {//设置的等级大于当前等级为升级
            AdvanceEntity advanceEntity = gameConfigManager.getGameMemberConfig().getAdvanceEntity(heroAdvance + 1);//获取当前等阶
            if (advanceEntity == null) {
                return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
            }
            int limitLevel = advanceEntity.getMaxLevel();//当前等阶可以升到的最大等级
            if (level > limitLevel && !isAutoAdvance) {
                return Pair.of(ErrorCode.PARAMS_ERROR, Collections.emptyList());
            }
            Map<Integer, Integer> costItemConfig = new HashMap<>();
            int bakCurrentLevel = currentLevel;
            while (currentLevel < level) {
                LevelupEntity levelupEntity = gameConfigManager.getGameMemberConfig().getLevelupEntity(currentLevel);
                if (levelupEntity == null) {
                    log.error("hero level up config not exist, userId: {}, heroId:{}, level:{}", userId, heroId, currentLevel);
                    return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
                }
                if (levelupEntity.getNextID() == 0) {
                    break;
                }
                if (currentLevel >= limitLevel) {//已经达到当前等阶的最大等级，需要升阶
                    advanceEntity = gameConfigManager.getGameMemberConfig().getAdvanceEntity(heroAdvance++ + 1);
                    if (advanceEntity == null) {
                        break;
                    }
                    AdvanceEntity currentAdvanceEntity = gameConfigManager.getGameMemberConfig().getAdvanceEntity(heroAdvance + 1);
                    if (currentAdvanceEntity == null) {
                        break;
                    }
                    limitLevel = currentAdvanceEntity.getMaxLevel();//当前等阶可以升到的最大等级
                    if (currentLevel >= limitLevel) {
                        break;
                    }
                    for (List<Integer> cost : advanceEntity.getAdvanceUpCost()) {
                        costItemConfig.compute(cost.get(0), (k, v) -> v == null ? Math.abs(cost.get(1)) : Math.abs(cost.get(1)) + v);
                    }
                }
                for (List<Integer> cost : levelupEntity.getLevelUpCost()) {
                    costItemConfig.compute(cost.get(0), (k, v) -> v == null ? Math.abs(cost.get(1)) : Math.abs(cost.get(1)) + v);
                }
                currentLevel++;
            }
            if (Objects.equals(currentLevel, bakCurrentLevel)) {//等级没有变化
                return Pair.of(ErrorCode.SUCCESS, Collections.emptyList());
            }
            List<List<Integer>> costConfigList = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : costItemConfig.entrySet()) {
                List<Integer> config = new ArrayList<>();
                config.add(entry.getKey());
                config.add(-entry.getValue());
                costConfigList.add(config);
            }
            hero.setLevel(currentLevel);
            hero.setAdvance(heroAdvance);

            return Pair.of(ErrorCode.SUCCESS, costConfigList);
        } else {////设置的等级小于当前等级为降级
            Map<Integer, Integer> currentLevelCostConfig = gameConfigManager.getHeroLevelUpTotalCost().get(currentLevel - 1);
            if (currentLevelCostConfig == null) {
                return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
            }
            Map<Integer, Integer> setLevelCostConfig = Collections.emptyMap();
            if (level != 1) {//不是一级有升级成本
                setLevelCostConfig = gameConfigManager.getHeroLevelUpTotalCost().get(level - 1);
                if (setLevelCostConfig == null) {
                    return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
                }
            }
            Map<Integer, Integer> salesReturnMap = new HashMap<>();//退还的消耗
            int type, cost;
            for (Map.Entry<Integer, Integer> entry : currentLevelCostConfig.entrySet()) {//升到当前等级使用的各种类型消耗
                type = entry.getKey();
                cost = entry.getValue() - setLevelCostConfig.getOrDefault(type, 0);
                if (cost <= 0) {
                    continue;
                }
                salesReturnMap.put(type, salesReturnMap.getOrDefault(type, 0) + cost);
            }
            if (isAutoAdvance) {
                int advance = 0, minAdvance = Integer.MAX_VALUE;
                for (AdvanceEntity advanceEntity : gameConfigManager.getGameMemberConfig().getAdvance().values()) {
                    if (advanceEntity.getMaxLevel() >= level && minAdvance > advanceEntity.getId()) {
                        advance = advanceEntity.getId();
                        minAdvance = advanceEntity.getId();
                    }
                    if (advance == 1) {
                        break;
                    }
                }
                if (--advance != heroAdvance) {
                    Pair<Integer, Map<Integer, Integer>> result = getAdvanceSalesReturnMap(heroAdvance, advance);
                    if (result.getLeft() != ErrorCode.SUCCESS) {
                        return Pair.of(result.getLeft(), Collections.emptyList());
                    }
                    for (Map.Entry<Integer, Integer> entry : result.getRight().entrySet()) {
                        salesReturnMap.put(entry.getKey(), salesReturnMap.getOrDefault(entry.getKey(), 0) + entry.getValue());
                    }
                    hero.setAdvance(advance);
                }
            }
            List<List<Integer>> salesReturnList = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : salesReturnMap.entrySet()) {
                List<Integer> config = new ArrayList<>();
                config.add(entry.getKey());
                config.add(entry.getValue());
                salesReturnList.add(config);
            }
            hero.setLevel(level);
            return Pair.of(ErrorCode.SUCCESS, salesReturnList);
        }
    }

    /**
     * 设置英雄的等阶
     *
     * @param userId  用户id
     * @param hero    英雄实体
     * @param advance 设置的等阶
     */
    public Pair<Integer, List<List<Integer>>> setHeroAdvance(long userId, Hero hero, int advance) {
        if (hero == null) {
            return Pair.of(ErrorCode.HERO_NOT_EXIST, Collections.emptyList());
        }
        if (gameConfigManager == null) {
            return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
        }
        if (advance < 0) {//英雄进阶最低0级
            return Pair.of(ErrorCode.PARAMS_ERROR, Collections.emptyList());
        }
        int currentAdvance = hero.getAdvance();
        if (Objects.equals(currentAdvance, advance)) {//当前等阶和设置的等阶一样，不做任何操作直接返回成功
            return Pair.of(ErrorCode.SUCCESS, Collections.emptyList());
        }
        int currentLevel = hero.getLevel();
        int heroId = hero.getHeroId();

        if (advance > currentAdvance) {//设置的等阶大于当前等阶为升阶
            AdvanceEntity advanceEntity = gameConfigManager.getGameMemberConfig().getAdvanceEntity(currentAdvance + 1);
            if (advanceEntity == null) {
                log.error("hero advance up config error, userId: {}, heroId:{}, level:{}", userId, heroId, currentLevel);
                return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
            }
            if (advanceEntity.getNextID() == 0) {
                return Pair.of(ErrorCode.HERO_ADVANCE_MAX, Collections.emptyList());
            }
            int maxLevel = advanceEntity.getMaxLevel();
            if (currentLevel < maxLevel) {
                return Pair.of(ErrorCode.PARAMS_ERROR, Collections.emptyList());
            }
            Map<Integer, Integer> costItemConfig = new HashMap<>();
            int bakCurrentAdvance = currentAdvance;
            while (currentAdvance < advance) {
                advanceEntity = gameConfigManager.getGameMemberConfig().getAdvanceEntity(currentAdvance + 1);
                if (advanceEntity == null) {
                    log.error("hero advance up config error, userId: {}, heroId:{}, level:{}", userId, heroId, currentLevel);
                    return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyList());
                }
                maxLevel = advanceEntity.getMaxLevel();
                if (advanceEntity.getNextID() == 0 || currentLevel < maxLevel) {//不存在下一阶或者等阶达不到都不可以升阶
                    break;
                }
                for (List<Integer> cost : advanceEntity.getAdvanceUpCost()) {
                    costItemConfig.compute(cost.get(0), (k, v) -> v == null ? Math.abs(cost.get(1)) : Math.abs(cost.get(1)) + v);
                }
                currentAdvance++;
            }
            if (Objects.equals(currentAdvance, bakCurrentAdvance)) {//等阶没有变化
                return Pair.of(ErrorCode.SUCCESS, Collections.emptyList());
            }
            List<List<Integer>> costConfigList = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : costItemConfig.entrySet()) {
                List<Integer> config = new ArrayList<>();
                config.add(entry.getKey());
                config.add(-entry.getValue());
                costConfigList.add(config);
            }
            hero.setAdvance(currentAdvance);
            return Pair.of(ErrorCode.SUCCESS, costConfigList);
        } else {//设置的等阶小于当前等阶为降阶
            Pair<Integer, Map<Integer, Integer>> result = getAdvanceSalesReturnMap(currentAdvance, advance);
            if (result.getLeft() != ErrorCode.SUCCESS) {
                return Pair.of(result.getLeft(), Collections.emptyList());
            }
            Map<Integer, Integer> salesReturnMap = result.getRight();
            List<List<Integer>> salesReturnList = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : salesReturnMap.entrySet()) {
                List<Integer> config = new ArrayList<>();
                config.add(entry.getKey());
                config.add(entry.getValue());
                salesReturnList.add(config);
            }
            hero.setAdvance(advance);
            return Pair.of(ErrorCode.SUCCESS, salesReturnList);
        }
    }

    /**
     * 获取降阶返还的消耗
     *
     * @param currentAdvance 当前的等阶
     * @param advance        设置的等阶
     */
    private Pair<Integer, Map<Integer, Integer>> getAdvanceSalesReturnMap(int currentAdvance, int advance) {
        if (advance >= currentAdvance) {
            return Pair.of(ErrorCode.PARAMS_ERROR, Collections.emptyMap());
        }
        Map<Integer, Integer> currentAdvanceUpCostConfig = gameConfigManager.getHeroAdvanceUpTotalCost().get(currentAdvance - 1);
        if (currentAdvanceUpCostConfig == null) {
            return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyMap());
        }
        Map<Integer, Integer> setAdvanceCostConfig = Collections.emptyMap();
        if (advance != 0) {//不是零级有升阶成本
            setAdvanceCostConfig = gameConfigManager.getHeroAdvanceUpTotalCost().get(advance - 1);
            if (setAdvanceCostConfig == null) {
                return Pair.of(ErrorCode.CONFIG_NOT_EXIST, Collections.emptyMap());
            }
        }
        Map<Integer, Integer> salesReturnMap = new HashMap<>();//退还的消耗
        int type, cost;
        for (Map.Entry<Integer, Integer> entry : currentAdvanceUpCostConfig.entrySet()) {//升到当前等级使用的各种类型消耗
            type = entry.getKey();
            cost = entry.getValue() - setAdvanceCostConfig.getOrDefault(type, 0);
            if (cost <= 0) {
                continue;
            }
            salesReturnMap.put(type, salesReturnMap.getOrDefault(type, 0) + cost);
        }
        return Pair.of(ErrorCode.SUCCESS, salesReturnMap);
    }

    /**
     * 重置英雄
     *
     * @param hero      英雄实体
     * @param userId    用户id
     * @param isResetOk 是否必须重置动作才算成功
     */
    public Triple<Integer, List<List<Integer>>, List<Equip>> resetHero(Hero hero, long userId, boolean isResetOk) {
        if (hero == null) {
            return Triple.of(ErrorCode.HERO_NOT_EXIST, Collections.emptyList(), Collections.emptyList());
        }
        Pair<Integer, List<List<Integer>>> result = setHeroLevel(userId, hero, 1, false);//设置英雄的等级为一级
        if (result.getLeft() != ErrorCode.SUCCESS) {
            return Triple.of(result.getLeft(), Collections.emptyList(), Collections.emptyList());
        }
        List<List<Integer>> returnConsumption = new ArrayList<>();//返还的消耗
        List<List<Integer>> cost = result.getRight();
        if (!cost.isEmpty()) {//升级英雄消耗不为空就返还
            returnConsumption.addAll(cost);
        }
        result = setHeroAdvance(userId, hero, 0);//设置英雄的等阶为零阶
        if (result.getLeft() != ErrorCode.SUCCESS) {
            return Triple.of(result.getLeft(), Collections.emptyList(), Collections.emptyList());
        }

        cost = result.getRight();
        if (!cost.isEmpty()) {//升阶英雄消耗不为空就返还
            returnConsumption.addAll(cost);
        }

        return Triple.of(ErrorCode.SUCCESS, returnConsumption, resetHeroDressEquip(hero));
    }

    public List<Equip> resetHeroDressEquip(Hero hero) {
        List<Equip> result = new ArrayList<>();
        if(!hero.getEquips().isEmpty()) {
            for (Equip equip : hero.getEquips().values()) {
                equip.setHeroRowId(0L);
                result.add(equip);
            }
        }

        hero.getEquips().clear();
        return result;
    }


}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Builtin;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Vector2 {
    public Vector2(JsonObject _buf) { 
        x = _buf.get("x").getAsFloat();
        y = _buf.get("y").getAsFloat();
    }

    public static Vector2 deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Builtin.Vector2(_buf);
    }

    public final float x;
    public final float y;

    

    @Override
    public String toString() {
        return "{ "
        + "x:" + x + ","
        + "y:" + y + ","
        + "}";
    }
}


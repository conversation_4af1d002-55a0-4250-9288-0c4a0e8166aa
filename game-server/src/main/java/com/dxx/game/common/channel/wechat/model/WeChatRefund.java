package com.dxx.game.common.channel.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @authoer: lsc
 * @createDate: 2023/5/30
 * @description:
 */
@Data
public class WeChatRefund {
    /** 微信支付订单号 说明：微信支付交易订单号 */
    @JSONField(name = "transaction_id")
    private String transactionId;

    /** 商户订单号 说明：原支付交易对应的商户订单号 */
    @JSONField(name = "out_trade_no")
    private String outTradeNo;

    /** 商户退款单号 说明：商户系统内部的退款单号，商户系统内部唯一，只能是数字、大小写字母_-|*@ ，同一退款单号多次请求只退一笔。 */
    @JSONField(name = "out_refund_no")
    private String outRefundNo;


    @JSONField(name = "amount")
    private Amount amount;

    @Data
    public static class Amount {
        /** 退款金额 说明：退款标价金额，单位为分，可以做部分退款 */
        @JSONField(name = "refund")
        private int refund;
        @JSONField(name = "total")
        private int total;
        @JSONField(name = "currency")
        private String currency = "CNY";
    }
}

package com.dxx.game.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CollectionUtils {

	public static List<List<Integer>> copyM(List<List<Integer>> list) {
		List<List<Integer>> result = new ArrayList<>(list.size());
		for (List<Integer> l : list) {
			result.add(new ArrayList<>(l));
		}
		return result;
	}

	public static List<Integer> copyS(List<Integer> list) {
		List<Integer> result = new ArrayList<>(list.size());
		result.addAll(list);
		return result;
	}

	/**
	 * 深度拷贝list
	 * @param src
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> deepCopyList(List<T> src) {
		List<T> dest = null;
		try {
			ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
			ObjectOutputStream out = new ObjectOutputStream(byteOut);
			out.writeObject(src);
			ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
			ObjectInputStream in = new ObjectInputStream(byteIn);
			dest = (List<T>) in.readObject();
			out.close();
			in.close();
		} catch (Exception e) {
			log.error("deepCopyList e:", e);
		}

		return dest;
	}

	/**
	 * map 根据key 排序
	 * @param map 待排序的map
	 * @param isDesc 是否降序，true：降序，false：升序
	 * @return 排序好的map
	 */
	public static <K extends Comparable<? super K>, V> Map<K, V> sortByKey(Map<K, V> map, boolean isDesc) {
		Map<K, V> result = Maps.newLinkedHashMap();
		if (isDesc) {
			map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByKey().reversed())
					.forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
		} else {
			map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByKey())
					.forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
		}
		return result;
	}

	/**
	 * map 根据value 排序
	 * @param map 待排序的map
	 * @param isDesc 是否降序，true：降序，false：升序
	 * @return 排序好的map
	 */
	public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map, boolean isDesc) {
		Map<K, V> result = Maps.newLinkedHashMap();
		if (isDesc) {
			map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByValue().reversed())
					.forEach(e -> result.put(e.getKey(), e.getValue()));
		} else {
			map.entrySet().stream().sorted(Map.Entry.<K, V>comparingByValue())
					.forEachOrdered(e -> result.put(e.getKey(), e.getValue()));
		}
		return result;
	}

	public static <T> boolean isRepeat(List<T> list) {
		return list.size() != new HashSet<>(list).size();
	}


	public static int getMinKey(Object[] obj) {
		Arrays.sort(obj);
		return (int) obj[0];
	}

	public static int getMaxValue(Object[] obj) {
		Arrays.sort(obj);
		return (int)obj[obj.length - 1];
	}

	/**
	 * 分割list
	 * @param list
	 * @param groupSize
	 * @param <T>
	 * @return
	 */
	public static <T> List<List<T>> splitList(List<T> list, int groupSize){
		int length = list.size();
		// 计算可以分成多少组
		int num = ( length + groupSize - 1 ) / groupSize ;
		List<List<T>> newList = new ArrayList<>(num);
		for (int i = 0; i < num; i++) {
			// 开始位置
			int fromIndex = i * groupSize;
			// 结束位置
			int toIndex = Math.min((i + 1) * groupSize, length);
			newList.add(list.subList(fromIndex, toIndex)) ;
		}
		return  newList ;
	}

	public static <T> List<T> distinct(List<T> src) {
		return src.stream().distinct().collect(Collectors.toList());
	}

	public static <T> List<T> cast2List(Object obj, Class<T> clazz) {
		List<T> result = new ArrayList<>();
		if (obj instanceof List<?>) {
			for (Object o : (List<?>) obj) {
				result.add(clazz.cast(o));
			}
			return result;
		}
		return result;
	}

	/**
	 * 随机取几个元素
	 * @param list
	 * @param n
	 * @param <T>
	 * @return
	 */
	public static <T> List<T> createRandomList(List<T> list, int count) {
		if (list.size() <= count) {
			return list;
		} else {
			List<T> result = new ArrayList<>();
			for (int i = 0; i < count; i ++) {
				int randIdx = RandomUtil.nextInt(list.size());
				result.add(list.remove(randIdx));
			}
			return result;
		}
	}

	public static List<List<List<Object>>> splitMatrixList(List<List<Object>> list, int groupSize){
		List<List<List<Object>>> dividedKeys = new ArrayList<>();
		for (int i = 0; i < list.size(); i += groupSize) {
			int endIndex = Math.min(i + groupSize, list.size());
			List<List<Object>> batch = list.subList(i, endIndex);
			dividedKeys.add(batch);
		}
		return dividedKeys;
	}

	public static boolean isEmpty(List<Long> list) {
		return list == null || list.isEmpty();
	}

	public static <E> boolean isNullOrEmpty(Collection<E> collection) {
		return collection == null || collection.isEmpty();
	}

	public static <K, V> boolean isNullOrEmpty(Map<K, V> map) {
		return map == null || map.isEmpty();
	}

	/**
	 * 将 JSONArray 转换为 List<List<Integer>>
	 */
	public static List<List<Integer>> convertJsonArrayToList(JSONArray jsonArray) {
		List<List<Integer>> result = new ArrayList<>();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONArray innerArray = jsonArray.getJSONArray(i);
			List<Integer> innerList = new ArrayList<>();
			for (int j = 0; j < innerArray.size(); j++) {
				innerList.add(innerArray.getInteger(j));
			}
			result.add(innerList);
		}
		return result;
	}
}








//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Task_DailyTask {
    public Task_DailyTask(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        DailyType = _buf.get("DailyType").getAsInt();
        AccumulationType = _buf.get("AccumulationType").getAsInt();
        DailyNeed = _buf.get("DailyNeed").getAsInt();
        DailyNeedParam = _buf.get("DailyNeedParam").getAsInt();
        DailyDescribe = _buf.get("DailyDescribe").getAsString();
        DailyActiveReward = _buf.get("DailyActiveReward").getAsInt();
        Jump = _buf.get("Jump").getAsInt();
        UnlockNeed = _buf.get("UnlockNeed").getAsInt();
    }

    public static Task_DailyTask deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Task_DailyTask(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 1.日常-进行主线肉鸽<br/>2.日常-进行英雄升级<br/>3.日常-装备合成（无效，暂不处理）<br/>4.日常-领取挂机奖励<br/>5.日常-开宝箱（宝箱工厂）<br/>6.日常-进行装备升级<br/>7.日常-进行挑战之塔<br/>8.日常-登录游戏<br/>11.日常-挑战竞技场<br/>12.日常-挑战迷宫<br/>13.日常-进行黑市购买<br/>14.日常-进行英雄招募<br/>15.日常-参与空中夺宝
     */
    public final int DailyType;
    /**
     * 是否为累加条件<br/>0,非累加<br/>1,累加
     */
    public final int AccumulationType;
    /**
     * 日常要求完成次数
     */
    public final int DailyNeed;
    /**
     * 日常要求参数<br/>
     */
    public final int DailyNeedParam;
    /**
     * 日常描述
     */
    public final String DailyDescribe;
    /**
     * 奖励活跃度(日)
     */
    public final int DailyActiveReward;
    /**
     * 1、主页<br/>2、英雄页<br/>3、商店页<br/>4、活动页<br/>5、挑战之塔<br/>6、竞技场<br/>7、英雄页<br/>8、主城宝箱界面<br/>9、主城黑市<br/>10、主城工会<br/>11、迷宫<br/>12、英雄招募<br/>13、藏品界面<br/>14、矿场<br/>15、主线挂机<br/>16、钓鱼抽奖<br/>17、藏品抽奖<br/><br/><br/>
     */
    public final int Jump;
    /**
     * Function中ID<br/>0为默认解锁<br/>-1为不开放
     */
    public final int UnlockNeed;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "DailyType:" + DailyType + ","
        + "AccumulationType:" + AccumulationType + ","
        + "DailyNeed:" + DailyNeed + ","
        + "DailyNeedParam:" + DailyNeedParam + ","
        + "DailyDescribe:" + DailyDescribe + ","
        + "DailyActiveReward:" + DailyActiveReward + ","
        + "Jump:" + Jump + ","
        + "UnlockNeed:" + UnlockNeed + ","
        + "}";
    }
}


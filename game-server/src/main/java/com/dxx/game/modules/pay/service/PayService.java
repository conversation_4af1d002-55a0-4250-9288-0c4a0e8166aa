package com.dxx.game.modules.pay.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.PayProto.*;

import java.util.Map;


/**
 * 支付模块
 * <AUTHOR>
 * @date 2019-12-17 17:48
 */
public interface PayService {

	/**
	 * 内购
	 */
	Result<PayInAppPurchaseResponse> inAppPurchase(PayInAppPurchaseRequest params);

	/**
	 * 预下单
	 */
	Result<PayPreOrderResponse> preOrderAction(PayPreOrderRequest params);

	/**
	 * 内购测试
	 */
	Result<PayOnUnityResponse> payOnUnity(PayOnUnityRequest params);

	/**
	 * 校验是否超出限购
	 */
	boolean isPurchaseLimitExceeded(long userId, int purchaseId, String exInfo);

	/**
	 * 根据商品ID 查询玩家当时所购买的道具
	 */
	Map<String, Object> getRechargeOrderItems(long userId, int purchaseId);

	/**
	 * 保存补单的信息
	 */
	void iapSupplement(long userId, String uniqueId, long preOrderId, int extraType,String extraInfo);


	Result<PayInAppPurchaseResponse> transferIAPItems(long userId, boolean isSandBox, String orderId, int purchaseId,
															   int channelId, long preOrderId,
															   String extraInfo, String cpOrderId, String currency);
}

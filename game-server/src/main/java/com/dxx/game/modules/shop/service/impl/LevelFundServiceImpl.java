package com.dxx.game.modules.shop.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.iap.LevelFundEntity;
import com.dxx.game.config.entity.iap.LevelFundRewardEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.ShopProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.shop.service.LevelFundService;
import com.dxx.game.modules.shop.support.ShopSupport;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/6/7
 * @description:
 */
@Service
public class LevelFundServiceImpl implements LevelFundService {

    @Resource
    private ShopDao shopDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private UserDao userDao;
    @Resource
    private RewardService rewardService;
    @Resource
    private ShopSupport shopSupport;


    @DynamoDBTransactional
    @Override
    public Result<ShopProto.LevelFundGetInfoResponse> levelFundGetInfo(ShopProto.LevelFundGetInfoRequest params) {
        long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);

        ShopProto.LevelFundGetInfoResponse.Builder response = ShopProto.LevelFundGetInfoResponse.newBuilder();
        response.setLevelFund(shopSupport.buildLevelFund(shop));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopProto.LevelFundRewardResponse> levelFundReward(ShopProto.LevelFundRewardRequest params) {
        int levelFundRewardId = params.getLevelFundRewardId();
        IAPConfig iapConfig = gameConfigManager.getIAPConfig();
        LevelFundRewardEntity levelFundRewardEntity = iapConfig.getLevelFundRewardEntity(levelFundRewardId);
        int groupId = levelFundRewardEntity.getGroupId();
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        // 判断是否已购买
        Map<Integer, List<Integer>> levelFundRewardMap = iap.getLevelFundRewardMap();
        if (!levelFundRewardMap.containsKey(groupId)) {
            return Result.Error(ErrorCode.LEVELFUND_NOT_BUY);
        }
        // 判断是否领过
        List<Integer> idList = levelFundRewardMap.get(groupId);
        if (idList.contains(levelFundRewardId)) {
            return Result.Error(ErrorCode.LEVELFUND_REWARDED);
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        User user = userDao.getByUserId(userId);

        // 判断条件是否满足
        LevelFundEntity levelFundEntity = gameConfigManager.getGroupIdLevelFundMap().get(groupId);
        switch (levelFundEntity.getParamType()) {
            case 2: {
                int maxProcess = user.getLevel();

                int needProcess = Integer.parseInt(levelFundRewardEntity.getParam());
                if (maxProcess < needProcess) {
                    return Result.Error(ErrorCode.LEVELFUND_NOT_FINISH);
                }
                break;
            }
            case 1: {
                int maxProcess = userExtend.getChapterId();

                int needProcess = Integer.parseInt(levelFundRewardEntity.getParam());
                if (maxProcess < needProcess) {
                    return Result.Error(ErrorCode.LEVELFUND_NOT_FINISH);
                }
                break;
            }
            default:
                return Result.Error(ErrorCode.LEVELFUND_TYPE_WRONG);
        }
        // 记录已领取
        idList.add(levelFundRewardId);
        shopDao.updateIap(shop);
        // 发放奖励
        List<List<Integer>> rewardConfig = levelFundRewardEntity.getFundReward();
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);

        ShopProto.LevelFundRewardResponse.Builder builder = ShopProto.LevelFundRewardResponse.newBuilder();
        builder.setCommonData(commonData);
        builder.setLevelFund(shopSupport.buildLevelFund(shop));
        return Result.Success(builder.build());
    }
}

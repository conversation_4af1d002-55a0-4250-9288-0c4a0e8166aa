
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Item_battle {
    public Item_battle(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        className = _buf.get("className").getAsString();
        parameter = _buf.get("parameter").getAsInt();
        prefabId = _buf.get("prefabId").getAsInt();
    }

    public static Item_battle deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Item_battle(_buf);
    }

    /**
     * 必须有的ID
     */
    public final int id;
    /**
     * 类名
     */
    public final String className;
    /**
     * 数值<br/>比如：经验值
     */
    public final int parameter;
    /**
     * 战斗中掉落的道具id<br/>看GameArt_Battle表格
     */
    public final int prefabId;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "className:" + className + ","
        + "parameter:" + parameter + ","
        + "prefabId:" + prefabId + ","
        + "}";
    }
}


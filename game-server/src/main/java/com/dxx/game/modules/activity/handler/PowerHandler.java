package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.PowerProto;
import com.dxx.game.modules.activity.service.PowerService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class PowerHandler {
    @Autowired
    private PowerService powerService;

    @ApiMethod(command = MsgReqCommand.PowerOnOpenRequest, name = "战力活动-打开界面调用")
    public Result<PowerProto.PowerOnOpenResponse> onOpen(Message msg) {
        PowerProto.PowerOnOpenRequest params = (PowerProto.PowerOnOpenRequest)msg;
        return powerService.onOpen(params);
    }

    @ApiMethod(command = MsgReqCommand.PowerRewardRequest, name = "战力活动-领奖")
    public Result<PowerProto.PowerRewardResponse> reward(Message msg) {
        PowerProto.PowerRewardRequest params = (PowerProto.PowerRewardRequest)msg;
        return powerService.reward(params);
    }
}

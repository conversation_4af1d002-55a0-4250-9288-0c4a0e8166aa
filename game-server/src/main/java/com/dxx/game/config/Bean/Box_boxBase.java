
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Box_boxBase {
    public Box_boxBase(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        iconAtlasID = _buf.get("iconAtlasID").getAsInt();
        spriteName = _buf.get("spriteName").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("dropId").getAsJsonArray(); dropId = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  dropId.add(_v0); }   }
        score = _buf.get("score").getAsInt();
        name = _buf.get("name").getAsInt();
        uiObjName = _buf.get("uiObjName").getAsString();
    }

    public static Box_boxBase deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Box_boxBase(_buf);
    }

    /**
     * 宝箱id
     */
    public final int id;
    /**
     * 宝箱图片资源
     */
    public final int iconAtlasID;
    /**
     * 宝箱图片资源
     */
    public final String spriteName;
    /**
     * 掉落id
     */
    public final java.util.List<Integer> dropId;
    /**
     * 产出积分
     */
    public final int score;
    /**
     * 宝箱多语言
     */
    public final int name;
    /**
     * 宝箱动画资源
     */
    public final String uiObjName;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "iconAtlasID:" + iconAtlasID + ","
        + "spriteName:" + spriteName + ","
        + "dropId:" + dropId + ","
        + "score:" + score + ","
        + "name:" + name + ","
        + "uiObjName:" + uiObjName + ","
        + "}";
    }
}


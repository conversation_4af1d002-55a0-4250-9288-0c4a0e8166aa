package com.dxx.game.cron;

import com.dxx.game.GameServerApplication;
import com.dxx.game.common.server.consts.ServerType;
import com.dxx.game.common.server.context.ServerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.transaction.jta.JtaAutoConfiguration;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务处理
 *
 * @authoer: lsc
 * @createDate: 2023/4/6
 * @description:
 */
@SpringBootApplication(exclude = {JtaAutoConfiguration.class})
@ComponentScan(basePackages = {"com.dxx.game"}, excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = {GameServerApplication.class})
})
public class CronServerApplication implements ApplicationRunner, ApplicationListener<ContextClosedEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CronServerApplication.class);


    /**
     * Program arguments 添加 --command=test
     *
     * @param args
     */
    public static void main(String[] args) {
        try {
            ServerContext.setServerType(ServerType.CRON_SERVER);
            SpringApplication springApplication = new SpringApplication(CronServerApplication.class);
            // 关闭spring启动日志
            springApplication.setBannerMode(Banner.Mode.OFF);
            springApplication.setLogStartupInfo(false);
            springApplication.setAllowCircularReferences(true);
            springApplication.run(args);

        } catch (Exception e) {
            LOGGER.error("GuildGVGServerApplication start failed, e", e);
            System.exit(-1);
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        LOGGER.info("CronServerApplication start success.");
    }

    @Scheduled(cron = "*/1 * * * * ?")
    private void onSecond() {

    }


    @Override
    public void onApplicationEvent(ContextClosedEvent event) {

    }
}

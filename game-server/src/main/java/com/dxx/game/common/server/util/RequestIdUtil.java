package com.dxx.game.common.server.util;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.dto.CommonProto;
import com.google.protobuf.Message;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.dxx.game.common.utils.CryptUtil.md5Bytes;

/**
 * <AUTHOR>
 * @date 2022/4/2 10:20
 */
@Component
public class RequestIdUtil {

    @Resource
    private RedisService redisService;

    // 默认transId
    public static final long DEFAULT_TRANS_ID = 100000;
    // transId 过期时间 15天
    private static final int TRANS_ID_EXPIRE_SECONDS = 1296000;

    private static RequestIdUtil requestIdUtil;
    @PostConstruct
    private void init() {
        requestIdUtil = this;
    }

    /**
     * 请求唯一ID
     *
     * @return
     */
    public static String getRequestID(long transId, byte[] requestBytes) {
        try {
            return transId + "_" + md5Bytes(requestBytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static long increaseTransId(long userId, int addValue) {
        String redisKey = RequestIdUtil.getTransIdRedisKey(userId);
        long value = requestIdUtil.redisService.incrBy(redisKey, addValue);
        setTTL(redisKey);
        return value;
    }

    public static long getMaxTransId(long userId) {
        String redisKey = RequestIdUtil.getTransIdRedisKey(userId);
        return requestIdUtil.redisService.getLongValue(redisKey);
    }

    public static void updateTransId(long userId, long transId) {
        String redisKey = getTransIdRedisKey(userId);
        requestIdUtil.redisService.set(redisKey, transId);
        setTTL(redisKey);
    }

    private static void setTTL(String key) {
        requestIdUtil.redisService.expireKey(key, TRANS_ID_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }

    /**
     * transId redisKey
     *
     * @return
     */
    public static String getTransIdRedisKey(long userId) {
        return "user_trans_id:" + userId;
    }

    public static final byte[] emptyBytes = new byte[0];

    public static void fillTransId(CommonProto.CommonData.Builder builder) {
        if (RequestContext.getUserId() == null || RequestContext.getCommonParams() == null) {
            return;
        }

        var userContext = RequestContext.getUserContext(RequestContext.getUserId());
        long transId = userContext.getTransId();
        if (transId == 0) {
            String redisKey = getTransIdRedisKey(RequestContext.getUserId());
            transId = requestIdUtil.redisService.incrBy(redisKey, 1);
        }

        CommonProto.UpdateTransId.Builder updateTransIdBuilder = CommonProto.UpdateTransId.newBuilder();
        updateTransIdBuilder.setIsChange(true);
        updateTransIdBuilder.setTransId(transId);
        builder.setUpdateTransId(updateTransIdBuilder);
    }
}

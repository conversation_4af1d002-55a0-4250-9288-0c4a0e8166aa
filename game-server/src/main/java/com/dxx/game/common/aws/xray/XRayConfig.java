package com.dxx.game.common.aws.xray;

import com.amazonaws.xray.AWSXRay;
import com.amazonaws.xray.slf4j.SLF4JSegmentListener;
import com.amazonaws.xray.strategy.IgnoreErrorContextMissingStrategy;
import com.amazonaws.xray.strategy.LogErrorContextMissingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * @author: lsc
 * @createDate: 2024/12/16
 * @description:
 */
@Configuration
@Slf4j
public class XRayConfig {

    static {
        AWSXRay.getGlobalRecorder().addSegmentListener(new SLF4JSegmentListener(""));
        AWSXRay.getGlobalRecorder().setContextMissingStrategy(new IgnoreErrorContextMissingStrategy());
    }
}

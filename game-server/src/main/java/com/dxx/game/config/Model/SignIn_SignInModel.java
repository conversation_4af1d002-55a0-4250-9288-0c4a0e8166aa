
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class SignIn_SignInModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.SignIn_SignIn> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.SignIn_SignIn> _dataList;
    
    public SignIn_SignInModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.SignIn_SignIn>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.SignIn_SignIn>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.SignIn_SignIn _v;
            _v = com.dxx.game.config.Bean.SignIn_SignIn.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.SignIn_SignIn> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.SignIn_SignIn> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.SignIn_SignIn get(int key) { return _dataMap.get(key); }

}

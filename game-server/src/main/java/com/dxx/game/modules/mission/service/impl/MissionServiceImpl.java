package com.dxx.game.modules.mission.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.equip.EquipEntity;
import com.dxx.game.consts.BattleType;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.LogResourceDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.MissionProto.*;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.mission.service.MissionService;
import com.dxx.game.modules.mission.support.MissionSupport;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.user.support.VitalitySupport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class MissionServiceImpl implements MissionService {

    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private VitalitySupport vitalitySupport;
    @Resource
    private LogService logService;
    @Resource
    private UserService userService;
    @Resource
    private MissionSupport missionSupport;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private RewardService rewardService;


    @DynamoDBTransactional
    @Override
    public Result<MissionGetInfoResponse> getInfoAction(MissionGetInfoRequest params) {
        long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        // 主线章节数据
        MainMission mainMission = missionSupport.buildMainMission(userExtend);

        MissionGetInfoResponse.Builder response = MissionGetInfoResponse.newBuilder();
        response.setMainMission(mainMission);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<MissionStartResponse> startAction(MissionStartRequest params) {
        long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        MissionStartDto startDto = params.getStartDto();

        int costVitality = 0;
        if (startDto.getMissionType() == MissionType.MAIN) {
            // 主线章节
            // TODO 暂时写死5 之后需要读表
            costVitality = 5;
        } else {
            log.error("missionTypeError");
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        if (costVitality > 0) {
            boolean flag = vitalitySupport.costVitality(userId, costVitality);
            if (!flag) {
                return Result.Error(ErrorCode.VITALITY_IS_NOT_ENOUGH);
            }
        }

        String extraInfo = "missionType:" + startDto.getMissionType() + ",missionId:" + startDto.getMissionId();
        logService.sendBasic(userId, missionSupport.getMissionLogCommand(startDto.getMissionType()), userService.getTransId(userId), startDto.getMissionTypeValue(), extraInfo);

        MissionStartResponse.Builder response = MissionStartResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<MissionEndResponse> missionEndAction(MissionEndRequest params) {
        long userId = RequestContext.getUserId();
        MissionEndDto missionEndDto = params.getEndDto();

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        MissionEndResponse.Builder response = MissionEndResponse.newBuilder();
        List<List<Integer>> rewards = new ArrayList<>();
        if (missionEndDto.getMissionType() == MissionType.MAIN) {
            // 主线章节 验证是否扣除了体力
            if (!missionSupport.isCostVitality(userId, missionEndDto.getStartTransId(), missionEndDto.getMissionType())) {
                log.error("checkCostVitalityError, userId:{}, missionEndDto:{}", userId, missionEndDto);
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            if (missionEndDto.getMissionId() == 0) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }

            if (missionEndDto.getIsWin()) {
                // 通关
                if (userExtend.getChapterId() == missionEndDto.getMissionId()) {
                    int nextChapterId = userExtend.getChapterId() + 1;
                    if (nextChapterId > userExtend.getChapterId()) {
                        userExtend.setChapterId(nextChapterId);
                        userInfoRedisDao.updateChapterId(userId, nextChapterId);
                    }
                }
            }

            List<List<Integer>> endRewards = CommonHelper.convertRewardsToAttributeList(missionEndDto.getRewardsList());

            // TODO 需要验证客户端上报的奖励是否合法
            rewards.addAll(endRewards);

            userExtendDao.updateMainMission(userExtend);

            response.setMainMission(missionSupport.buildMainMission(userExtend));
        } else {
            log.error("missionType error");
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 发奖
        RewardResultSet rewardResultSet = null;
        if (!CollectionUtils.isNullOrEmpty(rewards)) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
        }
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(response.build());
    }

}

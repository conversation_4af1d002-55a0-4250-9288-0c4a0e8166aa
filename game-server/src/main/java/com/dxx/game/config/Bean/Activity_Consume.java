
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Activity_Consume {
    public Activity_Consume(JsonObject _buf) { 
        Id = _buf.get("Id").getAsInt();
        Note = _buf.get("Note").getAsString();
        GroupType = _buf.get("GroupType").getAsInt();
        Name = _buf.get("Name").getAsString();
        Num = _buf.get("Num").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
    }

    public static Activity_Consume deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Activity_Consume(_buf);
    }

    /**
     * 编号
     */
    public final int Id;
    /**
     * 备注
     */
    public final String Note;
    /**
     * 对应条目组
     */
    public final int GroupType;
    /**
     * 目标名称（多语言）
     */
    public final String Name;
    /**
     * 目标数量
     */
    public final int Num;
    /**
     * 目标奖励
     */
    public final java.util.List<String> Reward;

    

    @Override
    public String toString() {
        return "{ "
        + "Id:" + Id + ","
        + "Note:" + Note + ","
        + "GroupType:" + GroupType + ","
        + "Name:" + Name + ","
        + "Num:" + Num + ","
        + "Reward:" + Reward + ","
        + "}";
    }
}


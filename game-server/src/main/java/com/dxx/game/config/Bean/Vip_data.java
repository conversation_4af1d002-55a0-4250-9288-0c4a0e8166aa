
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Vip_data {
    public Vip_data(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        ParamType = _buf.get("ParamType").getAsInt();
        LangugaeID = _buf.get("LangugaeID").getAsString();
        OtherParameter = _buf.get("OtherParameter").getAsString();
    }

    public static Vip_data deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Vip_data(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 参数类型
     */
    public final int ParamType;
    /**
     * 多语言ID
     */
    public final String LangugaeID;
    /**
     * 参数
     */
    public final String OtherParameter;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "ParamType:" + ParamType + ","
        + "LangugaeID:" + LangugaeID + ","
        + "OtherParameter:" + OtherParameter + ","
        + "}";
    }
}


package com.dxx.game.modules.reward.result;


import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CommonProto.RewardDto;
import com.dxx.game.dto.CommonProto.UserLevel;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.hero.data.HeroRewardData;
import com.dxx.game.modules.reward.model.*;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 奖励执行结果集合
 * <AUTHOR>
 * @date 2019-12-13 17:23
 */
@Setter
@Getter
public class RewardResultSet {

    private int resultCode = ErrorCode.SUCCESS;

	/**
	 * 奖励结果集合
	 */
    private List<RewardResult<?>> results;

	/**
	 * 返回给客户端奖励列表
	 */
    private List<RewardDto> rewards;

	/**
	 * 用户货币
	 */
    private boolean hasCurrency;

	/**
	 * 用户等级经验
	 */
    private UserLevel userLevel;

	/**
	 * 是否构建过
	 */
	private boolean isBuild = false;

	/**
	 * 更新的道具信息
	 */
    private List<CommonProto.ItemDto> items;

	/**
	 * 更新的装备列表
	 */
    private List<CommonProto.EquipmentDto> equips;

    private List<Long> deleteEquipRowIds;
	/**
	 * 新增英雄数量
	 */
	private int addHeroCount = 0;

	/**
	 * 用户VIP等级经验
	 */
	private CommonProto.UserVipLevel userVipLevel;

	/**
	 * 用户通行证积分
	 */
	private int userBattlePassScore;

	/**
	 * 更新的英雄数据
	 */
	private List<CommonProto.HeroDto> heros;


	public RewardResultSet() {

	}

	public RewardResultSet(int resultCode) {
		this.resultCode = resultCode;
	}

	public static RewardResultSet valueOf(int resultCode) {
		return new RewardResultSet(resultCode);
	}

	/**
	 * 构建响应数据
	 */
	public void buildResponseData() {
		if (isBuild || this.isFailed() || this.results == null) {
			return;
		}

		this.isBuild = true;
		this.rewards = new ArrayList<RewardDto>();
		for (int i = 0, length = this.results.size(); i < length; i ++) {
			RewardResult<?> rewardResult = this.results.get(i);
			switch (rewardResult.getRewardType()) {
				case RESOURCE:
					this.buildUserResource(rewardResult);
					break;
				case ITEM:
					this.buildItems(rewardResult);
					break;
				case HERO:
					this.buildHeros(rewardResult);
					break;
				case EQUIP:
					this.buildEquips(rewardResult);
					break;
				case GUILD_EXP:
					this.buildGuildExpItem(rewardResult);
					break;
				default:
					break;
			}
		}
	}


	private void buildHeros(RewardResult<?> rewardResult) {
		if (this.heros == null) {
			this.heros = new ArrayList<>();
		}
		HeroRewardData heroRewardData = (HeroRewardData) rewardResult.getCurrent();

		if(!heroRewardData.getHeroList().isEmpty()) {
			for (Hero hero : heroRewardData.getHeroList()) {
				this.heros.add(CommonHelper.buildHeroDto(hero));

				RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
				rewardBuilder.setType(RewardType.HERO.getValue());
				rewardBuilder.setConfigId(hero.getHeroId());
				rewardBuilder.setCount(rewardResult.getActualCount());
				this.rewards.add(rewardBuilder.build());
				this.addHeroCount++;
			}
			return;
		}

		if(!heroRewardData.getRewardDtoList().isEmpty()) {
			for (CommonProto.RewardDto dto : heroRewardData.getRewardDtoList()) {
				RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
				rewardBuilder.setType(RewardType.HERO.getValue());
				rewardBuilder.setConfigId(dto.getConfigId());
				rewardBuilder.setCount(rewardResult.getActualCount());
				this.rewards.add(rewardBuilder.build());
			}
			if (this.items == null) {
				this.items = new ArrayList<>();
			}
			this.items.addAll(heroRewardData.getItems());
		}
	}

	/**
	 * 英雄
	 */
	private void buildHeros1(RewardResult<?> rewardResult) {
		if (this.heros == null) {
			this.heros = new ArrayList<>();
		}
		List<Hero> heroes = (List<Hero>) rewardResult.getCurrent();
		for (Hero hero : heroes) {
			this.heros.add(CommonHelper.buildHeroDto(hero));

			RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
			rewardBuilder.setType(RewardType.HERO.getValue());
			rewardBuilder.setConfigId(hero.getHeroId());
			rewardBuilder.setCount(rewardResult.getActualCount());
			this.rewards.add(rewardBuilder.build());
			this.addHeroCount++;


			//TODO
//			ApiReward apiReward = new ApiReward();
//			apiReward.setItemType(RewardType.HERO.getValue());
//			apiReward.setItemId(hero.getHeroId());
//			apiReward.setQuantity(rewardResult.getActualCount());
//			apiReward.setRowId(hero.getRowId());
//			this.apiRewards.add(apiReward);
		}
	}

	/**
	 * 构建用户资源
	 * @param rewardResult
	 */
	private void buildUserResource(RewardResult<?> rewardResult) {
		switch (rewardResult.getResourceType()) {
			case COINS:
			case DIAMONDS:
				this.buildResource(rewardResult);
				break;
			case VIP_EXP:
				this.buildUserVipLevel(rewardResult);
				break;
			case BATTLE_PASS_SCORE:
				this.buildUserBattlePassScore(rewardResult);
				break;
			default:
				break;
		}
	}

	/**
	 * @param rewardResult
	 */
	private void buildResource(RewardResult<?> rewardResult) {
		this.hasCurrency = true;
		addRewardDto(rewardResult);
	}

	/**
	 * VIP等级经验
	 *
	 * @param rewardResult
	 */
	private void buildUserVipLevel(RewardResult<?> rewardResult) {
		UserVipExpReward userVipExpReward = (UserVipExpReward) rewardResult.getCurrent();
		CommonProto.UserVipLevel.Builder builder = CommonProto.UserVipLevel.newBuilder();
		builder.setVipLevel(userVipExpReward.getCurrentVipLevel());
		builder.setVipExp(userVipExpReward.getCurrentVipExp());
		this.userVipLevel = builder.build();
		addRewardDto(rewardResult);
	}

	/**
	 * 通行证积分
	 * @param rewardResult
	 */
	private void buildUserBattlePassScore(RewardResult<?> rewardResult) {
		UserBattlePassScoreReward userBattlePassScoreReward=(UserBattlePassScoreReward) rewardResult.getCurrent();
		this.userBattlePassScore=userBattlePassScoreReward.getCurrentBattlePassScore();
		addRewardDto(rewardResult);
	}

	/**
	 * 等级经验
	 * @param rewardResult
	 */
	private void buildUserLevel(RewardResult<?> rewardResult) {
		UserExpReward userExpReward = (UserExpReward) rewardResult.getCurrent();
		UserLevel.Builder builder = UserLevel.newBuilder();
		builder.setLevel(userExpReward.getCurrentLevel());
		builder.setExp(userExpReward.getCurrentExp());
		this.userLevel = builder.build();
		addRewardDto(rewardResult);
	}

	/**
	 * 构建道具
	 * @param rewardResult
	 */
	private void buildItems(RewardResult<?> rewardResult) {
		if (this.items == null) {
			this.items = new ArrayList<>();
		}
		Item item = (Item) rewardResult.getCurrent();
		this.items.add(CommonHelper.buildItemDto(item));

		if (rewardResult.getActualCount() > 0) {
			RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
			rewardBuilder.setType(RewardType.RESOURCE.getValue());
			rewardBuilder.setConfigId(item.getItemId());
			rewardBuilder.setCount(rewardResult.getActualCount());
			this.rewards.add(rewardBuilder.build());
		}
	}

	private void buildGuildExpItem(RewardResult<?> rewardResult) {
		if (this.items == null) {
			this.items = new ArrayList<>();
		}

		if (rewardResult.getActualCount() > 0) {
			GuildExpReward guildExpReward = (GuildExpReward)rewardResult.getCurrent();
			RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
			rewardBuilder.setType(RewardType.GUILD_EXP.getValue());
			rewardBuilder.setConfigId(guildExpReward.getConfigId());
			rewardBuilder.setCount(guildExpReward.getCount());
			this.rewards.add(rewardBuilder.build());
		}
	}

	private void buildEquips(RewardResult<?> rewardResult) {
		if (this.equips == null) {
			this.equips = new ArrayList<>();
		}

		if (rewardResult.getActualCount() > 0) {
			List<Equip> equipList = (List<Equip>)rewardResult.getCurrent();

			for (int i = 0, length = equipList.size(); i < length; i ++) {
				this.equips.add(CommonHelper.buildEquipmentDto(equipList.get(i)));

				RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
				rewardBuilder.setType(RewardType.EQUIP.getValue());
				rewardBuilder.setConfigId(equipList.get(i).getEquipId());
				rewardBuilder.setCount(rewardResult.getActualCount());

				this.rewards.add(rewardBuilder.build());

//				this.addEquipCount++;
			}
		} else {
			if (deleteEquipRowIds == null) {
				this.deleteEquipRowIds = new ArrayList<>();
			}
			List<Equip> equipList = (List<Equip>)rewardResult.getCurrent();

			for (int i = 0, length = equipList.size(); i < length; i ++) {
				this.deleteEquipRowIds.add(equipList.get(i).getRowId());
			}

		}
	}

	private void addRewardDto(RewardResult<?> rewardResult) {
		if (rewardResult.getActualCount() > 0) {
			RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
			rewardBuilder.setType(rewardResult.getRewardType().getValue());
			rewardBuilder.setConfigId(rewardResult.getResourceType().getValue());
			rewardBuilder.setCount(rewardResult.getActualCount());
			this.rewards.add(rewardBuilder.build());
		}
	}

	private void addRewardDto2(RewardResult<?> rewardResult) {
		if (rewardResult.getActualCount() > 0) {
			RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
			rewardBuilder.setType(rewardResult.getRewardType().getValue());
			rewardBuilder.setConfigId(rewardResult.getConfigId());
			rewardBuilder.setCount(rewardResult.getActualCount());
			this.rewards.add(rewardBuilder.build());
		}
	}

    public void clearRewards() {
		this.rewards = null;
	}

	public void addRewardResult(RewardResult<?> result) {
		if (this.results == null) {
			this.results = new ArrayList<RewardResult<?>>();
		}
		this.results.add(result);
	}
	/**
	 * 操作结果是否ok
	 * @return boolean
	 */
	public boolean isSuccess() {
		return this.resultCode == ErrorCode.SUCCESS;
	}

	/**
	 * 操作结果是否不成立
	 * @return boolean
	 */
	public boolean isFailed() {
		return this.resultCode != ErrorCode.SUCCESS;
	}

}

package com.dxx.game.common.channel.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.alipay.model.AliPayConfig;
import com.dxx.game.common.channel.apple.config.AppleConfig;
import com.dxx.game.common.channel.bilibili.model.BiliConfig;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.douyin.model.DouYinConfig;
import com.dxx.game.common.channel.douyinminigame.model.DouYinMiniGameConfig;
import com.dxx.game.common.channel.google.config.GoogleConfig;
import com.dxx.game.common.channel.habby.model.HabbyConfig;
import com.dxx.game.common.channel.harmony.model.HarmonyConfig;
import com.dxx.game.common.channel.honor.model.HonorConfig;
import com.dxx.game.common.channel.huawei.model.HuaWeiConfig;
import com.dxx.game.common.channel.kuaishou.model.KuaiShouConfig;
import com.dxx.game.common.channel.official.config.OfficialConfig;
import com.dxx.game.common.channel.onestore.config.OneStoreConfig;
import com.dxx.game.common.channel.oppo.model.OppoConfig;
import com.dxx.game.common.channel.ssjj.model.SsjjConfig;
import com.dxx.game.common.channel.uc.model.UCConfig;
import com.dxx.game.common.channel.vivo.model.VivoConfig;
import com.dxx.game.common.channel.wechat.model.WeChatPayConfig;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameConfig;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameJsApiConfig;
import com.dxx.game.common.channel.xiaomi.model.XiaoMiConfig;
import com.dxx.game.common.channel.yyb.model.YybConfig;
import com.dxx.game.common.config.game.GameConfigLoader;
import com.dxx.game.common.log.LogUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9 8:48 下午
 */
@Slf4j
@Getter
@Component
public class ChannelConfig {

    @Autowired
    private GameConfigLoader gameConfigLoader;
    @Value("${spring.application.env}")
    private String applicationEnv;
    @Value("${game.country}")
    private String country;

    private GoogleConfig googleConfig;
    private AppleConfig appleConfig;
    private OneStoreConfig oneStoreConfig;

    // 微信配置
    private WeChatPayConfig weChatPayConfig;
    // 支付宝配置
    private AliPayConfig aliPayConfig;
    // 华为
    private HuaWeiConfig huaWeiConfig;
    // OPPO
    private OppoConfig oppoConfig;
    // 小米
    private XiaoMiConfig xiaoMiConfig;
    // vivo
    private VivoConfig vivoConfig;
    // 4399
    private SsjjConfig ssjjConfig;
    // uc
    private UCConfig ucConfig;
    // bilibili
    private BiliConfig biliConfig;
    // 应用宝
    private YybConfig yybConfig;
    // 快手
    private KuaiShouConfig kuaiShouConfig;
    // offical
    private OfficialConfig officialConfig;
    // 抖音
    private DouYinConfig douYinConfig;
    private WeChatMiniGameConfig weChatMiniGameConfig;
    private WeChatMiniGameJsApiConfig weChatMiniGameJsApiConfig;
    private DouYinMiniGameConfig douYinMiniGameConfig;

    // 支付排名单
    private List<Long> payWhiteListTest;
    private List<Long> payWhiteListPre;
    private List<Long> payWhiteListProd;

    // 游戏域名
    private List<String> gameUrls;
    // 游戏名称
    private String gameName;
    // 是否验证登录信息
    private boolean isVerifyLogin;

    private boolean orderTestPrice;

    private String gameNameCn;

    // 荣耀
    private HonorConfig honorConfig;

    // 鸿蒙
    private HarmonyConfig harmonyConfig;
    private HabbyConfig habbyConfig;

    @PostConstruct
    public void loadConfig() {
        try {
            String content = gameConfigLoader.loadConfig("Channel.json");
            this.setUp(content);
            String appEnv = System.getenv("APP_ENV");
            if (appEnv != null) {
                applicationEnv = appEnv;
            }
        } catch (Exception e) {
            log.error("load channel config failed, e:", e);
        }
    }

    private void setUp(String content) {
        JSONObject jsonObject = JSON.parseObject(content);
        if (jsonObject.containsKey("google")) {
            googleConfig = JSON.parseObject(jsonObject.getString("google"), GoogleConfig.class);
        }

        if (jsonObject.containsKey("apple")) {
            appleConfig = JSON.parseObject(jsonObject.getString("apple"), AppleConfig.class);
        }

        if (jsonObject.containsKey("one_store")) {
            oneStoreConfig = JSON.parseObject(jsonObject.getString("one_store"), OneStoreConfig.class);
        }

        if (jsonObject.containsKey("weChat")) {
            weChatPayConfig = JSON.parseObject(jsonObject.getString("weChat"), WeChatPayConfig.class);
        }
        if (jsonObject.containsKey("payWhiteList")) {
            JSONObject whiteList = jsonObject.getJSONObject("payWhiteList");
            payWhiteListTest = JSONObject.parseArray(whiteList.getString("test"), Long.class);
            payWhiteListPre = JSONObject.parseArray(whiteList.getString("pre"), Long.class);
            payWhiteListProd = JSONObject.parseArray(whiteList.getString("prod"), Long.class);
        }
        if (jsonObject.containsKey("ali_pay")) {
            aliPayConfig = JSON.parseObject(jsonObject.getString("ali_pay"), AliPayConfig.class);
        }

        if (jsonObject.containsKey("game_urls")) {
            gameUrls = JSONObject.parseArray(jsonObject.getString("game_urls"), String.class);
        }

        if (jsonObject.containsKey("huawei")) {
            huaWeiConfig = JSON.parseObject(jsonObject.getString("huawei"), HuaWeiConfig.class);
        }

        if (jsonObject.containsKey("oppo")) {
            oppoConfig = JSON.parseObject(jsonObject.getString("oppo"), OppoConfig.class);
        }

        if (jsonObject.containsKey("xiaomi")) {
            xiaoMiConfig = JSON.parseObject(jsonObject.getString("xiaomi"), XiaoMiConfig.class);
        }

        if (jsonObject.containsKey("vivo")) {
            vivoConfig = JSON.parseObject(jsonObject.getString("vivo"), VivoConfig.class);
        }

        if (jsonObject.containsKey("4399")) {
            ssjjConfig = JSON.parseObject(jsonObject.getString("4399"), SsjjConfig.class);
        }

        if (jsonObject.containsKey("uc")) {
            ucConfig = JSON.parseObject(jsonObject.getString("uc"), UCConfig.class);
        }

        if (jsonObject.containsKey("bilibili")) {
            biliConfig = JSON.parseObject(jsonObject.getString("bilibili"), BiliConfig.class);
        }

        if (jsonObject.containsKey("yyb")) {
            yybConfig = JSON.parseObject(jsonObject.getString("yyb"), YybConfig.class);
        }

        if (jsonObject.containsKey("kuaishou")) {
            kuaiShouConfig = JSON.parseObject(jsonObject.getString("kuaishou"), KuaiShouConfig.class);
        }

        if (jsonObject.containsKey("official")) {
            officialConfig = JSON.parseObject(jsonObject.getString("official"), OfficialConfig.class);
        }

        if (jsonObject.containsKey("douyin")) {
            douYinConfig = JSON.parseObject(jsonObject.getString("douyin"), DouYinConfig.class);
        }

        if (jsonObject.containsKey("game_name")) {
            gameName = jsonObject.getString("game_name");
        }

        if (jsonObject.containsKey("verify_login")) {
            isVerifyLogin = jsonObject.getBoolean("verify_login");
        }

        if (jsonObject.containsKey("order_test_price")) {
            orderTestPrice = jsonObject.getBoolean("order_test_price");
        }

        if (jsonObject.containsKey("game_name_cn")) {
            gameNameCn = jsonObject.getString("game_name_cn");
        }

        if (jsonObject.containsKey("weChatMiniGame")) {
            weChatMiniGameConfig = JSON.parseObject(jsonObject.getString("weChatMiniGame"), WeChatMiniGameConfig.class);
        }

        if (jsonObject.containsKey("weChatMiniGameJsApi")) {
            weChatMiniGameJsApiConfig = JSON.parseObject(jsonObject.getString("weChatMiniGameJsApi"), WeChatMiniGameJsApiConfig.class);
        }

        if (jsonObject.containsKey("honor")) {
            honorConfig = JSON.parseObject(jsonObject.getString("honor"), HonorConfig.class);
        }

        if (jsonObject.containsKey("harmony")) {
            harmonyConfig = JSON.parseObject(jsonObject.getString("harmony"), HarmonyConfig.class);
        }

        if (jsonObject.containsKey("habby")) {
            habbyConfig = JSON.parseObject(jsonObject.getString("habby"), HabbyConfig.class);
        }

        if (jsonObject.containsKey("douyinMiniGame")) {
            douYinMiniGameConfig = JSON.parseObject(jsonObject.getString("douyinMiniGame"), DouYinMiniGameConfig.class);
        }
    }

    /**
     * 是否是支付白名单用户
     * @param userId
     * @return
     */
    public boolean isPayWhiteList(long userId) {
        if (!orderTestPrice) {
            return false;
        }
        List<Long> list = null;
        if (this.applicationEnv.equals("pre")) {
            list = this.getPayWhiteListPre();
        } else if (this.applicationEnv.equals("prod")) {
            list = this.getPayWhiteListProd();
        } else if (this.applicationEnv.equals("test")) {
            // 测试服
            list = this.getPayWhiteListTest();
            return true;
        } else if (this.applicationEnv.equals("develop")) {
            return true;
        }
        if (list == null) {
            return false;
        }
        return list.contains(userId);
    }

    /**
     * 支付回调地址
     * @return
     */
    public String getNotifyUrl(ChannelID channelID) {
        if (channelID == null) {
            return "";
        }
        int idx = 0;
        if (this.applicationEnv.equals("pre")) {
            idx = 1;
        } else if (this.applicationEnv.equals("prod")) {
            idx = 2;
        }
        String url = this.getGameUrls().get(idx) + "/payCb/" + channelID.getName();
        return url;
    }

    public String getNotifyHost(ChannelID channelID) {
        if (channelID == null) {
            return "";
        }
        int idx = 0;
        if (this.applicationEnv.equals("pre")) {
            idx = 1;
        } else if (this.applicationEnv.equals("prod")) {
            idx = 2;
        }
        return this.getGameUrls().get(idx);
    }

    public String getNotifyHost(String env) {
        int idx = 0;
        if (env.equals("pre")) {
            idx = 1;
        } else if (env.equals("prod")) {
            idx = 2;
        }
        return this.getGameUrls().get(idx);
    }

    public boolean isProd() {
        return this.applicationEnv.equals("prod");
    }
    public boolean isPre() {
        return this.applicationEnv.equals("pre");
    }
    public boolean isTest() {
        return this.applicationEnv.equals("test") || this.applicationEnv.equals("develop");
    }

    public boolean isCn() {
        return this.country.equals("cn");
    }
    public String getHabbyIdUrl() {
        if (this.isCn()) {
            if (this.isPre() || this.isProd()) {
                return this.getHabbyConfig().getHabbyIdUrlCnProd();
            } else {
                return this.getHabbyConfig().getHabbyIdUrlCnTest();
            }
        } else {
            if (this.isPre() || this.isProd()) {
                return this.getHabbyConfig().getHabbyIdUrlEnProd();
            } else {
                return this.getHabbyConfig().getHabbyIdUrlEnTest();
            }
        }
    }

    public String getHabbyIdSecret() {
        if (this.isCn()) {
            if (this.isPre() || this.isProd()) {
                return this.getHabbyConfig().getHabbyIdSecretKeyEnProd();
            } else {
                return this.getHabbyConfig().getHabbyIdSecretKeyEnTest();
            }
        } else {
            if (this.isPre() || this.isProd()) {
                return this.getHabbyConfig().getHabbyIdSecretKeyEnProd();
            } else {
                return this.getHabbyConfig().getHabbyIdSecretKeyEnTest();
            }
        }
    }

    public String getHabbyGameId() {
        if (this.isCn()) {
            return this.getHabbyConfig().getHabbyIdGameCn();
        }
        return this.getHabbyConfig().getHabbyIdGameEn();
    }
}

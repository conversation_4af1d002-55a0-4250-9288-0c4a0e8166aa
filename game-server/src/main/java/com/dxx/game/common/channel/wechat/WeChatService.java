package com.dxx.game.common.channel.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.wechat.model.*;
import com.dxx.game.common.utils.DateUtils;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.Charsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 微信
 * <AUTHOR>
 * @date 2021/12/24 16:45
 */
@Slf4j
@DependsOn("okHttpClient")
@Component
public class WeChatService implements ChannelService {

    // 渠道ID
    private static final ChannelID CHANNEL_ID = ChannelID.WeChat;

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private AndroidCallBackService androidCallBackService;

    // 私钥
    private PrivateKey privateKey = null;
    // 配置
    WeChatPayConfig weChatPayConfig = null;
    // 平台证书列表
    private ConcurrentHashMap<String, X509Certificate> certificateMap = new ConcurrentHashMap<>();

    // 请求证书列表URL
    private HttpUrl certificatesUrl = null;
    // 下单URL
    private HttpUrl doOrderUrl = null;

    private HttpUrl refundUrl = null;

    @PostConstruct
    private void init() {
        try {
            weChatPayConfig = channelConfig.getWeChatPayConfig();
            if (weChatPayConfig == null || !weChatPayConfig.isOpen()) {
                return;
            }

            certificatesUrl = HttpUrl.parse(weChatPayConfig.getCertificatesUrl());
            doOrderUrl = HttpUrl.parse(weChatPayConfig.getDoOrderUrl());
            refundUrl = HttpUrl.parse(weChatPayConfig.getRefundUrl());

            initPrivateKey();

            refreshCertificates();
        } catch (Exception e) {
            log.error("init weChat failed", e);
            System.exit(0);
        }
    }



    /**
     * 下单接口  https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_2_1.shtml
     * @param description
     * @param attach
     * @param amount
     * @return
     */
    public WeChatUnifiedOrderResult unifiedOrder(long userId, String androidPackageName, String description, String attach, int amount) {
        String appId = weChatPayConfig.getAppIds().getOrDefault(androidPackageName, "");
        if (StringUtils.isEmpty(appId)) {
            appId = weChatPayConfig.getAppId();
        }

        boolean isWhiteUser = channelConfig.isPayWhiteList(userId);
        if (isWhiteUser) {
            // 测试账号下单固定1分钱
            amount = 1;
        }
        String type = channelConfig.getWeChatPayConfig().getOutTradeNoType().getOrDefault(androidPackageName, "");
        String notifyUrl = channelConfig.getNotifyUrl(CHANNEL_ID);
        String cpOrderId = channelConfig.getGameName() + "-" + type + PaymentUtils.createCpOrderId();
        if (cpOrderId.length() > 32) {
            cpOrderId = cpOrderId.substring(0, 32);
        }
        Map<String, Object> postBody = new HashMap<>();
        postBody.put("appid", appId);
        postBody.put("mchid", weChatPayConfig.getMchId());
        postBody.put("description", description);
        postBody.put("out_trade_no", cpOrderId);
        postBody.put("attach", attach);
        postBody.put("notify_url", notifyUrl);
        Map<String, Object> amountObj = new HashMap<>();
        amountObj.put("total", amount);
        postBody.put("amount", amountObj);

        try {
            String respData = this.doRequest("POST", this.doOrderUrl, postBody);

            if (StringUtils.isEmpty(respData)) {
                return null;
            }

            JSONObject jsonObject = JSONObject.parseObject(respData);
            String prepayId = jsonObject.getString("prepay_id");
            long timeStamp = DateUtils.getUnixTime();
            String nonceStr = UUID.randomUUID().toString().replace("-","");
            String signStr = Stream.of(appId, String.valueOf(timeStamp), nonceStr, prepayId)
                    .collect(Collectors.joining("\n", "", "\n"));

            String signature = this.buildSign(signStr.getBytes(StandardCharsets.UTF_8));

            WeChatUnifiedOrderResult result = new WeChatUnifiedOrderResult();
            result.setPrePayId(prepayId);
            result.setNonceStr(nonceStr);
            result.setTimestamp(timeStamp);
            result.setSign(signature);

            return result;
        } catch (Exception e) {
            log.error("doOrder error", e);
            return null;
        }
    }


    /**
     * 支付回调接口
     * @param request
     * @return
     */
    @Override
    public Object payCb(FullHttpRequest request) {
//        int code = androidPayCbService.doDeliverGoods(new PayCbVo());
//        return code;

        String body = request.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
        try {
            PayCbVo payCbVo = this.checkOrderState(request, body);
            if (payCbVo == null) {
                log.error("checkOrderState failed, body:{}", body);
                return this.error("ERROR", "checkOrderState failed");
            }

            // 超过限购次数发起退款
            if (androidCallBackService.isPurchaseLimitExceeded(payCbVo.getUserId(), payCbVo.getProductId())) {
                log.info("wechatRefund, userId:{}, cpOrderId:{}, orderId:{}, productId:{}, amount:{}", payCbVo.getUserId(), payCbVo.getCpOrderId(), payCbVo.getOrderId(), payCbVo.getProductId(), payCbVo.getAmount() / 100);
                this.refund(payCbVo.getUserId(), payCbVo.getCpOrderId(), payCbVo.getOrderId(), payCbVo.getAmount(), payCbVo.getExtra(), payCbVo.getProductId());
                return this.success();
            }

            int code = androidCallBackService.doDeliverGoods(payCbVo);
            if (code != AndroidPayCbErrorCode.SUCCESS) {
                log.error("doDeliverGoods failed, body:{}", body);
                return this.error("ERROR", "code = " + code);
            }

            return this.success();

        } catch (Exception e) {
            log.error("payCb failed", e);
            return this.error("EXCEPTION", "payCb failed");
        }
    }

    /**
     * 检测订单数据
     * @param request
     * @param body
     */
    private PayCbVo checkOrderState(FullHttpRequest request, String body) throws Exception {
        if (verifiedSign(request, body)) {
            WeChatPayNotifyVO payNotifyVO = JSONObject.parseObject(body, WeChatPayNotifyVO.class);
            if (!payNotifyVO.getEvent_type().equals("TRANSACTION.SUCCESS")) {
                log.error("event_type error:{}", body);
                return null;
            }

            // 通知资源数据
            WeChatPayNotifyVO.Resource resource = payNotifyVO.getResource();
            // 解密后的数据
            String notifyResourceStr = this.decryptResponseBody(resource.getAssociated_data(), resource.getNonce(), resource.getCiphertext());

            WeChatNotifyResourceVO notifyResourceVO = JSONObject.parseObject(notifyResourceStr, WeChatNotifyResourceVO.class);
            if (!notifyResourceVO.getTrade_state().equals("SUCCESS")) {
                log.error("trade_state error:{}", notifyResourceVO);
                return null;
            }


            String attach = notifyResourceVO.getAttach();
            PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(attach);

            PayCbVo payCbVo = new PayCbVo();
            payCbVo.setUserId(payBackParamsModel.getUserId());
            payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
            payCbVo.setOrderId(notifyResourceVO.getTransaction_id());
            payCbVo.setCpOrderId(notifyResourceVO.getOut_trade_no());
            payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
            payCbVo.setProductId(payBackParamsModel.getProductId());
            payCbVo.setChannelId(CHANNEL_ID.getId());
            payCbVo.setAmount(notifyResourceVO.getAmount().getTotal());
            payCbVo.setExtra(notifyResourceVO.getAmount().getPayer_total());

            return payCbVo;
        } else {
            return null;
        }
    }

    /**
     * 验证微信回调签名
     * @return
     */
    private boolean verifiedSign(FullHttpRequest request, String body) throws Exception {
        //微信返回的证书序列号
        String serialNo = request.headers().get("Wechatpay-Serial");
        //微信返回的随机字符串
        String nonceStr = request.headers().get("Wechatpay-Nonce");
        //微信返回的时间戳
        String timestamp = request.headers().get("Wechatpay-Timestamp");
        //微信返回的签名
        String weChatSign = request.headers().get("Wechatpay-Signature");

        if (serialNo == null) {
            return false;
        }

        //组装签名字符串
        String signStr = Stream.of(timestamp, nonceStr, body)
                .collect(Collectors.joining("\n", "", "\n"));

        if (certificateMap.isEmpty() || !certificateMap.containsKey(serialNo)) {
            this.refreshCertificates();
        }

        X509Certificate certificate = certificateMap.get(serialNo);
        if (certificate == null) {
            log.error("certificate not exist serialNo:{}", serialNo);
            return false;
        }

        //SHA256withRSA签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initVerify(certificate);
        signature.update(signStr.getBytes());
        //返回验签结果
        return signature.verify(Base64.getDecoder().decode(weChatSign));
    }

    // 执行请求
    private String doRequest(String method, HttpUrl httpUrl, Object params) throws Exception {
        String body = "";
        if (params != null) {
            body = JSONObject.toJSONString(params);
        }

        String token = this.buildToken(method, httpUrl, body);
        Map<String, String> headerParameter = new HashMap<>();
        headerParameter.put("Content-Type", "application/json;charset=UTF-8");
        headerParameter.put("Accept", "application/json");
        headerParameter.put("Authorization", token);

        if (method.equals("GET")) {
            return OkHttpClientUtil.get(httpUrl.toString(), headerParameter, null, String.class);
        } else {
            return OkHttpClientUtil.postJson(httpUrl.toString(), headerParameter, params, String.class);
        }
    }

    /**
     * 刷新平台证书列表
     */
    private void refreshCertificates() throws Exception {
        String respData = this.doRequest("GET", this.certificatesUrl, null);
        JSONObject jsonObject = JSON.parseObject(respData);
        List<WeChatCertificateVO> chatCertificateVOList = JSON.parseArray(jsonObject.getString("data"), WeChatCertificateVO.class);

        for (WeChatCertificateVO weChatCertificateVO : chatCertificateVOList) {
            String publicKey = this.decryptResponseBody(weChatCertificateVO.getEncrypt_certificate().getAssociated_data(),
                    weChatCertificateVO.getEncrypt_certificate().getNonce(), weChatCertificateVO.getEncrypt_certificate().getCiphertext());

            CertificateFactory certificateFactory = CertificateFactory.getInstance("X509");

            // 获取证书
            ByteArrayInputStream inputStream = new ByteArrayInputStream(publicKey.getBytes(StandardCharsets.UTF_8));
            X509Certificate x509Certificate = null;
            x509Certificate = (X509Certificate) certificateFactory.generateCertificate(inputStream);
            certificateMap.put(weChatCertificateVO.getSerial_no(), x509Certificate);
        }
    }
    // 初始化私钥
    private void initPrivateKey() {
        try {
            byte[] keyBytes = Base64.getMimeDecoder().decode(weChatPayConfig.getPrivateKey());
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // token
    private String buildToken(String method, HttpUrl httpUrl, String body) throws Exception {
        String nonceStr = UUID.randomUUID().toString();
        long timestamp = DateUtils.getUnixTime();
        String message = this.buildMessage(method, httpUrl, timestamp, nonceStr, body);

        String signature = this.buildSign(message.getBytes(StandardCharsets.UTF_8));
        String token = "WECHATPAY2-SHA256-RSA2048 mchid=\"" + weChatPayConfig.getMchId() + "\","
                + "nonce_str=\"" + nonceStr + "\","
                + "timestamp=\"" + timestamp + "\","
                + "serial_no=\"" + weChatPayConfig.getSerialNo() + "\","
                + "signature=\"" + signature + "\"";
        return token;
    }

    // sign
    private String buildSign(byte[] message) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message);
        return Base64.getEncoder().encodeToString(signature.sign());
    }

    private String buildMessage(String method, HttpUrl httpUrl, long timestamp, String nonceStr, String body) {
        String canonicalUrl = httpUrl.encodedPath();
        if (httpUrl.encodedQuery() != null) {
            canonicalUrl += "?" + httpUrl.encodedQuery();
        }

        return method + "\n" + canonicalUrl + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";
    }

    // 用微信V3密钥解密响应体.
    private String decryptResponseBody(String associatedData, String nonce, String ciphertext) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        SecretKeySpec key = new SecretKeySpec(weChatPayConfig.getApiV3Key().getBytes(StandardCharsets.UTF_8), "AES");
        GCMParameterSpec spec = new GCMParameterSpec(128, nonce.getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.DECRYPT_MODE, key, spec);
        cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));

        return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)));

    }

    /**
     * 微信登录
     * 文档地址 https://developers.weixin.qq.com/doc/oplatform/Mobile_App/WeChat_Login/Authorized_API_call_UnionID.html
     * @param code
     */
    public WeChatLoginData login(String code, String packageName) {
        String appId = channelConfig.getWeChatPayConfig().getAppIds().getOrDefault(packageName, "");
        String appSecret = channelConfig.getWeChatPayConfig().getAppSecrets().getOrDefault(packageName, "");
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
            return null;
        }

        String reqAccessTokenUrl = channelConfig.getWeChatPayConfig().getReqAccessTokenUrl();
        reqAccessTokenUrl = String.format(reqAccessTokenUrl, appId, appSecret, code);

        // 获取token
        String resp = OkHttpClientUtil.get(reqAccessTokenUrl, String.class);
        JSONObject respObj = JSONObject.parseObject(resp);
        // 返回错误
        if (respObj.containsKey("errorcode")) {
            log.error("weChat login get access_token failed, errorMsg:{}", resp);
            return null;
        }

        String accessToken = respObj.getString("access_token");
        String openId = respObj.getString("openid");
        long expireTime = DateUtils.getUnixTime() + respObj.getIntValue("expires_in");
        String refreshToken = respObj.getString("refresh_token");

        // 查询用户信息 获取unionId
        String reqUserInfoUrl = channelConfig.getWeChatPayConfig().getReqUserInfoUrl();
        reqUserInfoUrl = String.format(reqUserInfoUrl, accessToken, openId);


        String respUser = OkHttpClientUtil.get(reqUserInfoUrl, String.class);
        JSONObject respUserObj = JSONObject.parseObject(respUser);
        if (respUserObj.containsKey("errcode")) {
            log.error("weChat login get userinfo failed, errorMsg:{}", resp);
            return null;
        }

        String unionId = respUserObj.getString("unionid");

        WeChatLoginData.Token token = new WeChatLoginData.Token();
        token.setAccessToken(accessToken);
        token.setExpireTime(expireTime);
        token.setOpenId(openId);
        token.setRefreshToken(refreshToken);

        WeChatLoginData weChatLoginData = new WeChatLoginData();
        weChatLoginData.setToken(token);
        weChatLoginData.setUnionId(unionId);

        return weChatLoginData;
    }


    // 成功返回
    private Map<String, String> success() {
        Map<String, String> result = new HashMap<>();
        result.put("code", "SUCCESS");
        result.put("message", "");
        return result;
    }

    // 失败返回
    private Map<String, String> error(String code, String msg) {
        Map<String, String> result = new HashMap<>();
        result.put("code", code);
        result.put("message", msg);
        return result;
    }


    // https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_2_9.shtml
    // 退款
    private void refund(long userId, String outTradeNo, String tradeNo,
                        int amountTotal, int amountPayerTotal, String productId) {
        WeChatRefund weChatRefund = new WeChatRefund();
        weChatRefund.setOutRefundNo(outTradeNo);
        weChatRefund.setTransactionId(tradeNo);
        weChatRefund.setOutTradeNo(outTradeNo);
        WeChatRefund.Amount amountObj = new WeChatRefund.Amount();
        amountObj.setTotal(amountTotal);
        amountObj.setRefund(amountPayerTotal);
        weChatRefund.setAmount(amountObj);

        try {
            String respData = this.doRequest("POST", this.refundUrl, weChatRefund);
            JSONObject respObj = JSONObject.parseObject(respData);
            if (respObj.getString("status").equals("ABNORMAL")) {
                log.error("wechatRefund failed,userId:{}, cpOrderId:{}, orderId:{}, productId:{}, response:{}", userId, outTradeNo, tradeNo, productId, respData);
            } else {
                log.info("wechatRefund success, userId:{}, cpOrderId:{}, orderId:{}, productId:{}", userId, outTradeNo, tradeNo, productId);
            }
        } catch (Exception e) {
            log.error("wechatRefundFailed, error:", e);
        }
    }
}

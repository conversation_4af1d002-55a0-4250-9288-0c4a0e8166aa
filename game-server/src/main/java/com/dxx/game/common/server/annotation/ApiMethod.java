package com.dxx.game.common.server.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiMethod {
	
	/**
	 * 请求命令号
	 * @return
	 */
	short command() default 0;
	
	/**
	 * 方法名称
	 * @return
	 */
	String name() default "";

	/**
	 * 开发接口
	 * @return
	 */
	boolean developApi() default false;

	/**
	 * 跳过幂等
	 * @return
	 */
	boolean skipIdempotent() default true;


	/**
	 * 跳过Token验证
	 * @return
	 */
	boolean skipAuth() default false;

	/**
	 * 是否检查多端登录
	 *
	 * @return
	 */
	boolean skipLoginStateCheck() default false;

	/**
	 * 是否收集ip
	 */
	boolean collectIp() default false;

	/**
	 * 是否跳过redis锁
	 */
	boolean skipRedisLock() default false;
}

package com.dxx.game.modules.reward.service.impl;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.item.DropEntity;
import com.dxx.game.config.entity.item.DropLvEntity;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.reward.service.RewardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;


/**
 * 奖励掉落实现类
 * <AUTHOR>
 * @date 2019-12-17 14:08
 */
@Slf4j
@Service
public class DropServiceImpl implements DropService {
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;

    @Override
    public List<Reward> dropRewards(int dropId, int count) {
        return rewardService.parseRewards(this.dropRewardsConfig(dropId, count));
    }

    @Override
    public List<Integer> randEquipId(List<List<Integer>> dropConfig) {
        int totalWeight = this.calcTotalWeight(dropConfig);
        int randValue = RandomUtil.betweenValue(1, totalWeight);
        return this.randConfig(dropConfig, randValue);
    }

    private int calcTotalWeight(List<List<Integer>> dropConfig) {
        int result = 0;
        for (List<Integer> c : dropConfig) {
            result += c.get(2);
        }
        return result;
    }

    private List<Integer> randConfig(List<List<Integer>> dropConfig, int randValue) {
        List<Integer> result = new ArrayList<>();
        for (List<Integer> c : dropConfig) {
            int weight = c.get(2);
            if (randValue <= weight) {
                result.add(c.get(0));
                result.add(c.get(1));
                break;
            }
            randValue -= weight;
        }
        return result;
    }

    @Override
    public List<List<Integer>> dropRewardsConfig(int dropId) {
        return this.dropRewardsConfig(dropId, 1);
    }

    @Override
    public List<List<Integer>> dropRewardsConfig(int dropId, int count) {
        List<List<Integer>> dropConfigs = this.getDropConfigsByDropId(dropId);
        if (dropConfigs == null) {
            log.error("dropRewards failed, dropEntity is null, dropId: {}", dropId);
            return null;
        }
        return this.randItemByConfig(dropConfigs, count);
    }

    /**
     * 根据dropId 获取掉落组
     */
    private List<List<Integer>> getDropConfigsByDropId(int dropId) {
        DropEntity dropEntity = gameConfigManager.getItemConfig().getDropEntity(dropId);
        if (dropEntity == null) {
            log.error("dropRewards failed, dropEntity is null, dropId: {}", dropId);
            return null;
        }

        List<List<Integer>> dropConfigs = null;
        if (dropEntity.getBaseOnLv() == 0) {
            dropConfigs = dropEntity.getReward();
        } else {
            // 根据等级掉落
            dropConfigs = new ArrayList<>();
            List<List<Integer>> levelDropIds = gameConfigManager.getItemConfig().getDropEntity(dropId).getReward();
            int userLevel = RequestContext.getUser().getLevel();
            for (List<Integer> levelDrop : levelDropIds) {
                int id = levelDrop.get(0);
                DropLvEntity dropLvEntity = gameConfigManager.getItemConfig().getDropLvEntity(id);
                if (userLevel >= dropLvEntity.getLevel().get(0) && userLevel <= dropLvEntity.getLevel().get(1)) {
                    dropConfigs.addAll(dropLvEntity.getReward());
                }
            }
        }
        return dropConfigs;
    }

    @Override
    public List<List<Integer>> randItemByConfig(List<List<Integer>> dropConfigs, int dropCount) {
        int totalWeight = this.calcTotalWeight(dropConfigs);

        List<List<Integer>> result = new ArrayList<>();

        for (int i = 0; i < dropCount; i++) {
            int randValue = RandomUtil.betweenValue(1, totalWeight);
            List<Integer> dropItemConfig = new ArrayList<>();
            for (List<Integer> config : dropConfigs) {
                int weight = config.get(config.size() - 1);
                if (weight >= randValue) {
                    if (config.size() == 3) {
                        int itemId = config.get(0);
                        int count = config.get(1);
                        int itemType = gameConfigManager.getItemConfig().getItemEntity(itemId).getItemType();
                        dropItemConfig.add(itemType);
                        dropItemConfig.add(itemId);
                        dropItemConfig.add(count);
                    } else {
                        dropItemConfig.add(config.get(0));
                        dropItemConfig.add(config.get(1));
                        dropItemConfig.add(config.get(2));
                    }
                    if(dropItemConfig.get(2) > 0) {
                        result.add(dropItemConfig);
                    }
                    break;
                }
                randValue -= weight;
            }
        }
        return result;
    }

    @Override
    public int randIndex(List<List<Integer>> config) {
        return this.randIndexByWeight(config, null);
    }

    @Override
    public int randIndexByWeight(List<List<Integer>> dropConfigs, List<Integer> exclude) {
        int result = 0;
        int totalWeight = 0;
        for (List<Integer> config : dropConfigs) {
            if (exclude != null && exclude.contains(config.get(0))) {
                continue;
            }
            totalWeight += config.get(config.size() - 1);
        }

        int randValue = RandomUtil.betweenValue(1, totalWeight);
        for (int i = 0; i < dropConfigs.size(); i++) {
            if (exclude != null && exclude.contains(dropConfigs.get(i).get(0))) {
                continue;
            }
            int weight = dropConfigs.get(i).get(dropConfigs.get(i).size() - 1);
            if (weight >= randValue) {
                result = i;
                break;
            }
            randValue -= weight;
        }
        return result;
    }

    @Override
    public List<Integer> randomDropWishSeed(int dropId, Random random) {
        ItemConfig itemConfig = gameConfigManager.getItemConfig();
        DropEntity dropEntity = itemConfig.getDropEntity(dropId);
        List<List<Integer>> reward = dropEntity.getReward();
        List<Integer> prob = new ArrayList<>();
        for (List<Integer> dataList : reward) {
            int p = dataList.get(dataList.size() - 1);
            prob.add(p);
        }
        int index = RandomUtil.randomProbIndexWithSeed(random, prob);
        List<Integer> rewardList = reward.get(index);
        List<Integer> integers = rewardList.subList(0, rewardList.size() - 1);

        List<Integer> copyList = CollectionUtils.copyS(integers);
        rewardService.checkRewardConfig(copyList);
        return integers;
    }

    @Override
    public List<List<Integer>> randomDropsWithSeed(int dropId, Random random, int count) {
        ItemConfig itemConfig = gameConfigManager.getItemConfig();
        DropEntity dropEntity = itemConfig.getDropEntity(dropId);

        List<List<Integer>> reward = CollectionUtils.copyM(dropEntity.getReward());

        List<Integer> prob = new ArrayList<>();
        for (List<Integer> dataList : reward) {
            int p = dataList.get(dataList.size() - 1);
            prob.add(p);
        }
        List<Integer> indexes = RandomUtil.randomProbIndexCountWithSeed(random, prob, count);
        List<List<Integer>> dataList=new ArrayList<>();
        for(int index:indexes) {
            List<Integer> integers = reward.get(index);
            List<Integer> r = integers.subList(0, integers.size() - 1);

            List<Integer> copyList = CollectionUtils.copyS(r);
            rewardService.checkRewardConfig(copyList);

            dataList.add(r);
        }
        return dataList;
    }
}














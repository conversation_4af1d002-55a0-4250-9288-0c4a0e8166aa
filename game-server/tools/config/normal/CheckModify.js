var fs = require("fs");
var path = require("path");

var jsonConfigPath = path.join(__dirname, "../../../gameconf");
var configPath = path.join(__dirname, "./files/config");
var entityPath = path.join(__dirname, "./files/entity");

var args = process.argv.splice(2);
if (args.length > 0) {
	excelPath = args[0];
}

// 是否按照最新修改文件生成，暂时关闭
getModifyFiles();


function getModifyFiles() {

	var logPath = path.join(__dirname, "./data/filemtime.txt");

	var filemtimeArray = {};
	fs.readdir(jsonConfigPath, function(err, files){
	    (function iterator(i){
	        if(i == files.length) {
	          return ;
	        }
	        var fileStat = fs.statSync(jsonConfigPath + "/" + files[i]);
	        if (fileStat.isFile()) {
  				var fileInfo = files[i].split(".");
  				filemtimeArray[fileInfo[0]] = parseInt(fileStat['mtimeMs']);
  				
	        }
	        iterator(i+1);
	    })(0);
	    
	    var result = [];
	    var oldFiletimeArray = {};
	    var exists = fs.existsSync(logPath);
	    if (exists) {
	    	var content = fs.readFileSync(logPath,'utf-8');
	    	if (!content) {
	    		content = "{}";
	    	}
	    	oldFiletimeArray = JSON.parse(content);

	    	for (var key in filemtimeArray) {
	    		if (oldFiletimeArray.hasOwnProperty(key)) {
		    		if (oldFiletimeArray[key] != filemtimeArray[key]) {
		    			result.push(key);
		    		}
		    	} else {
		    		result.push(key);
		    	}
	    	}

	    } else {
	    	result = Object.keys(filemtimeArray);
	    }

	    var dataPath = path.join(__dirname, "./data");
	    if (!fs.existsSync(dataPath)) {
	    	fs.mkdirSync(dataPath);
	    }

	    var fd = fs.openSync(dataPath + "/modifyfile.txt",'w');
		fs.writeSync(fd, JSON.stringify(result), 0, "utf-8");
		fs.closeSync(fd);

		fd = fs.openSync(logPath,'w');
		fs.writeSync(fd, JSON.stringify(filemtimeArray), 0, "utf-8");
		fs.closeSync(fd);
	});
}
package com.dxx.game.modules.im.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.modules.guild.service.GuildService;
import com.dxx.game.modules.im.IMGroupIdGenerator;
import com.dxx.game.modules.im.dto.PublishMessageDto;
import com.dxx.game.modules.im.service.IMService;
import com.dxx.game.modules.message.service.MessageService;
import com.dxx.game.modules.server.service.ServerListService;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.dxx.game.dto.IMProto.MessageType;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/3/19
 * @description:
 */
@Slf4j
@Service
public class IMServiceImpl implements IMService {

    @Value("${GORILLA_IM_INTERNAL_HOST:http://127.0.0.1:8086}")
    private String wsInternalHost;
    @Resource
    private GuildService guildService;
    @Resource
    private ServerListService serverListService;
    @Resource
    private MessageService messageService;

    @Override
    public Map<Long, Long> getOnlineList(List<Long> userIds) {
        String url = wsInternalHost + "/im/online_list";
        JSONObject postData = new JSONObject();
        postData.put("userIds", userIds);

        JSONObject response = this.postRequest(url, postData);

        if (response == null) {
            log.error("getOnlineList error response is null error, userIds:{}", userIds);
            return null;
        }
        if (response.getIntValue("code") != 0) {
            log.error("getOnlineList error, userIds:{} response:{}", userIds, response);
            return null;
        }
        Map<Long, Long> result = new HashMap<>();
        JSONObject data = response.getJSONObject("data");
        for (String key : data.keySet()) {
            Long longKey = Long.valueOf(key);
            Long time = data.getLongValue(key);
            result.put(longKey, time);
        }
        // key: userId, value:时间戳(0=当前在线, >0 离线时间)
        return result;
    }

    @Override
    public Map<Long, Boolean> batchGetUserOnline(List<Long> userIds) {
        Map<Long, Long> onlineStatus = this.getOnlineList(userIds);
        if (onlineStatus == null) {
            return Collections.emptyMap();
        }
        Map<Long, Boolean> onlineMap = Maps.newHashMapWithExpectedSize(userIds.size());
        onlineStatus.forEach((userId, offlineTime) -> {
            if (offlineTime == 0) {
                onlineMap.put(userId, true);
            } else {
                onlineMap.put(userId, false);
            }
        });
        return onlineMap;
    }

    @Override
    public boolean joinGroup(long userId, String groupId) {
        return this.joinGroup(Collections.singletonList(userId), groupId);
    }

    @Override
    public boolean joinGroup(List<Long> userIds, String groupId) {
        JSONObject postData = new JSONObject();
        String url = wsInternalHost + "/im/joinGroup";
        postData.put("userIds", userIds);
        postData.put("groupId", groupId);

        JSONObject response = this.postRequest(url, postData);
        if (response == null || response.getIntValue("code") != 0) {
            log.error("joinGroup failed: userIds: {}, groupId: {}, response: {}", userIds, groupId, response);
            return false;
        }
        return true;
    }

    @Override
    public boolean leaveGroup(long userId, String groupId) {
        return this.leaveGroup(Collections.singletonList(userId), groupId);
    }

    @Override
    public boolean leaveGroup(List<Long> userIds, String groupId) {
        JSONObject postData = new JSONObject();
        String url = wsInternalHost + "/im/leaveGroup";
        postData.put("userIds", userIds);
        postData.put("groupId", groupId);

        JSONObject response = this.postRequest(url, postData);
        if (response == null || response.getIntValue("code") != 0) {
            log.error("leaveGroup failed: userIds: {}, groupId: {}, response: {}", userIds, groupId, response);
            return false;
        }
        return true;
    }

    @Override
    public void batchSendPersonalMsg(MessageType messageType, List<Long> userIdList, String content) {
        PublishMessageDto publishMessageDto = PublishMessageDto.valueOfByUser(messageType.getNumber(), content, userIdList);
        this.publishMessage(publishMessageDto);
    }

    @Override
    public void sendPersonalMsg(MessageType messageType, long userId, String content) {
        PublishMessageDto publishMessageDto = PublishMessageDto.valueOfByUser(messageType.getNumber(), content, userId);
        this.publishMessage(publishMessageDto);
    }

    @Override
    public void sendGroupMsg(MessageType messageType, String groupId, String content) {
        this.sendGroupMsg(messageType, groupId, content, false);
    }

    @Override
    public void sendGroupMsg(MessageType messageType, String groupId, String content, boolean saveGroupMessage) {
        PublishMessageDto publishMessageDto = PublishMessageDto.valueOfByGroup(messageType.getNumber(), groupId, content, saveGroupMessage);
        this.publishMessage(publishMessageDto);
    }

    @Override
    public void sendGuildGroupMsg(MessageType messageType, long guildId, String content) {
        this.sendGuildGroupMsg(messageType, guildId, content, false);
    }

    @Override
    public void sendGuildGroupMsg(MessageType messageType, long guildId, String content, boolean saveGroupMessage) {
        String groupId = IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guildId);
        this.sendGroupMsg(messageType, groupId, content, saveGroupMessage);
    }

    @Override
    public void sendServerGroupMsg(MessageType messageType, long serverId, String content) {
        this.sendServerGroupMsg(messageType, serverId, content, false);
    }

    @Override
    public void sendServerGroupMsg(MessageType messageType, long serverId, String content, boolean saveGroupMessage) {
        String groupId = IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, serverId);
        this.sendGroupMsg(messageType, groupId, content, saveGroupMessage);
    }

    @Override
    public void sendMsgToAll(MessageType messageType, String content) {
        PublishMessageDto publishMessageDto = PublishMessageDto.valueOfByAllUser(messageType.getNumber(), content);
        this.publishMessage(publishMessageDto);
    }

    @Override
    public String getUserServerGroupId(int serverId) {
        return IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, serverId);
    }

    @Override
    public String getUserGuildGroupId(long userId) {
        long guildId = guildService.getGuildId(userId);
        if (guildId > 0) {
            return IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guildId);
        }
        return "";
    }

    @Override
    public String getUserCrossServerGroupId(int serverId) {
        long crossServerId = IMGroupIdGenerator.getCrossServerId(serverId);
        return IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.CROSS_SERVER, crossServerId);
    }

    @Override
    public String getUserGlobalGroupId(int serverId) {
        long zoneId = serverListService.getZoneIdByServerId(serverId);
        return IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GLOBAL, zoneId);
    }

    @Override
    public long createMsgId(String groupId) {
        var msgId = 0L;
        var groupType = IMGroupIdGenerator.getGroupType(groupId);
        long id = IMGroupIdGenerator.parseIdFromGroupId(groupId);
        if (groupType == IMGroupIdGenerator.GroupType.GUILD) {
            msgId = messageService.generateGuildMsgId(id);
        } else {
            msgId = messageService.generateServerMsgId(id);
        }
        return msgId;
    }

    private void publishMessage(PublishMessageDto publishMessageDto) {
        String url = wsInternalHost + "/im/publish_message";
        JSONObject postData = new JSONObject();
        postData.put("publishMessageDto", publishMessageDto);

        JSONObject response = this.postRequest(url, postData);
        if (response == null || response.getIntValue("code") != 0) {
            log.error("publishMessage failed: publishMessageDto: {}, response: {}", publishMessageDto, response);
        }
    }

    private JSONObject postRequest(String url, JSONObject postData) {
        try {
            JSONObject response = OkHttpClientUtil.postJson(url, null, postData, JSONObject.class);
            if (response == null) {
                log.error("postRequest failed: response is null, url: {}, postData: {}", url, postData);
            }
            return response;
        } catch (Exception e) {
            log.error("postRequest exception: url: {}, postData: {}, errMsg: {}", url, postData, e.getMessage(), e);
            return null;
        }
    }
}

package com.dxx.game.modules.chat.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.redis.RedisReplicaService;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.consts.RewardType;
import com.dxx.game.consts.VipPermission;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.EquipDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dto.ChatProto.*;
import com.dxx.game.modules.chat.service.ChatService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.message.service.ChatMessageService;
import com.dxx.game.modules.user.service.UserService;
import com.google.protobuf.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RedisService redisService;
    @Resource
    private EquipDao equipDao;
    @Resource
    private UserService userService;
    @Resource
    private RedisReplicaService redisReplicaService;


    @DynamoDBTransactional
    @Override
    public Result<ChatShowItemResponse> chatShowItemAction(ChatShowItemRequest params) {
        int itemType = params.getItemType();
        long rowId = params.getRowId();
        long userId = RequestContext.getUserId();
        String groupId = params.getGroupId();
        if (this.isChatFrequencyTooFast(userId)) {
            return Result.Error(ErrorCode.IM_CHAT_FREQUENCY_TOO_FAST);
        }
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser == null || guildUser.getGuildId() == 0) {
            return Result.Error(ErrorCode.IM_CHAT_GUILD_NOT_JOIN);
        }
        Message itemProto = null;
        if (itemType == RewardType.EQUIP.getValue()) {
            Equip equip = equipDao.getByRowId(userId, rowId);
            if (equip != null) {
                itemProto = CommonHelper.buildEquipmentDto(equip);
            }
        }

        if (itemProto == null) {
            log.error("showItemNotFound, itemType:{}, rowId:{}", itemType, rowId);
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        User user = userService.getUser(userId);
        chatMessageService.publicChatShowItem(user, groupId, guildUser, itemType, itemProto);
        // 保存下次可聊天频率
        this.saveChatFrequency(userId);
        ChatShowItemResponse.Builder response = ChatShowItemResponse.newBuilder();
        return Result.Success(response.build());
    }

    @Override
    public boolean isChatFrequencyTooFast(long userId) {
        String redisKey = this.getChatFrequencyKey(userId);
        long frequency = redisReplicaService.getLongValue(redisKey);
        if (frequency >= DateUtils.getUnixTime()) {
            return true;
        }
        return false;
    }

    @Override
    public void saveChatFrequency(long userId) {
        String redisKey = this.getChatFrequencyKey(userId);
        int chatFrequency = gameConfigManager.getGuildConfig().getGuildConstEntity(114).getTypeInt();

        // vip权限检查
        double vpv = userService.vipPermissionValueCheck(userId, VipPermission.PERMISSION_16);
        if (vpv > 0){
            chatFrequency = 0;
        }

        redisService.set(redisKey, DateUtils.getUnixTime() + chatFrequency);
        redisService.expireKey(redisKey, chatFrequency, TimeUnit.SECONDS);
    }

    private String getChatFrequencyKey(long userId) {
        return RedisKeys.CHAT_FREQUENCY + userId;
    }
}

package com.dxx.game.modules.hero.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.Symbol;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.book.BondEntity;
import com.dxx.game.config.entity.book.BookEntity;
import com.dxx.game.config.entity.equip.EquipEntity;
import com.dxx.game.config.entity.gamemember.MemberEntity;
import com.dxx.game.config.entity.gamemember.StarEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.GameConstant;
import com.dxx.game.consts.TaskType;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.EquipDao;
import com.dxx.game.dao.dynamodb.repository.HeroDao;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.HeroProto;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.equip.support.EquipSupport;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.hero.support.HeroSupport;
import com.dxx.game.modules.pvp.support.BattleUnitCache;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.model.TaskProcess;
import com.dxx.game.modules.task.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:50
 */
@Slf4j
@Service
public class HeroServiceImpl implements HeroService {

    @Autowired
    private HeroDao heroDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RewardService rewardService;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private ItemDao itemDao;
    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private TaskService taskService;
    @Autowired
    private SevenDayTaskService sevenDayTaskService;
    @Autowired
    private EquipService equipService;
    @Autowired
    private EquipDao equipDao;
    @Autowired
    private BattleUnitCache formationCache;


    @Autowired
    private HeroSupport heroSupport;
    @Autowired
    private EquipSupport equipSupport;

    @DynamoDBTransactional
    @Override
    public void init(UserExtend userExtend) {
        long userId = userExtend.getUserId();

        String initHeroIdStr = gameConfigManager.getGameConfigConfig().getConfigEntity(801).getValue();
        String[] split = initHeroIdStr.split(Symbol.SHUXIAN);

        List<Integer> heroRecords = new ArrayList<>();

        Map<Integer, Integer> createHeroFormationMap = new HashMap<>();
        for (String heroIdStr : split) {
            String[] split1 = heroIdStr.split(",");
            int heroId = Integer.parseInt(split1[1]);
            int formation = Integer.parseInt(split1[0]);
            createHeroFormationMap.put(heroId, formation);

            heroRecords.add(heroId);
        }
        List<Long> formation = new ArrayList<>(5) {{
            for (int i = 0; i < 5; i++) {
                add(0L);
            }
        }};

        int totalStars = 0;
        int maxStar = 0;
        for (Map.Entry<Integer, Integer> entry : createHeroFormationMap.entrySet()) {
            int heroId = entry.getKey();
            int formationIndex = entry.getValue();
            int heroQuality = gameConfigManager.getGameMemberConfig().getMemberEntity(heroId).getInitialQuality();
            Hero hero = createHeroNow(userId, heroId, 1, 0, heroQuality, 0);
            totalStars += hero.getStar();
            if (hero.getStar() > maxStar) {
                maxStar = hero.getStar();
            }

            formation.set(formationIndex, hero.getRowId());
        }

        // 默认阵容章节
        Map<Integer, List<Long>> formations = new HashMap<>();
        formations.put(GameConstant.FORMATION_TYPE_CHAPTER, formation);
        userExtend.setFormations(formations);

        userExtend.setHeroRecords(heroRecords);

        UserExtend.HeroStatisticModel heroStatisticModel = new UserExtend.HeroStatisticModel();
        heroStatisticModel.setTotalHeroStars(totalStars);
        heroStatisticModel.setMaxHeroStar(maxStar);
        userExtend.setHeroStatisticModel(heroStatisticModel);
    }

    @Override
    public Hero createHero(long userId, int heroId, int level, int star, int quality, int rowId) {
        Hero hero = initHero(userId, heroId, level, star, quality, rowId);
        heroDao.insert(hero);

        return hero;
    }

    @Override
    public Hero createHeroNow(long userId, int heroId, int level, int star, int quality, int rowId) {
        Hero hero = initHero(userId, heroId, level, star, quality, rowId);
        heroDao.insertNow(hero);

        return hero;
    }

    @Override
    public Hero initHero(long userId, int heroId, int level, int star, int quality, int rowId) {
        Hero hero = new Hero();
        if (rowId <= 0) {
            hero.setRowId(commonService.generateId(userId));
        } else {
            hero.setRowId((long) rowId);
        }

        hero.setUserId(userId);
        hero.setHeroId(heroId);
        hero.setLevel(level);
        hero.setExp(0);
        hero.setStar(star);
        hero.setQuality(quality);
        hero.setAdvance(0);
        hero.setEquips(new HashMap<>());

        return hero;
    }

    @Override
    public List<Hero> getAllHeros(long userId) {
        return heroDao.getAllByUserId(userId);
    }

    @Override
    public List<Hero> getAllHeros(long userId, List<Long> rowIds) {
        return heroDao.getListByRowIds(userId, rowIds);
    }

    @Override
    public Hero getHero(long userId, long rowId) {
        return heroDao.getByRowId(userId, rowId);
    }

    @Override
    public Hero getHeroById(long userId, int heroId) {
        return heroDao.getByHeroId(userId, heroId);
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroUpgradeResponse> heroUpgradeAction(HeroProto.HeroUpgradeRequest params) {
        long userId = RequestContext.getUserId();
        int count = params.getCount();//升级次数
        if (count <= 0) {//最少升一级
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        Hero hero = heroDao.getByRowId(userId, params.getHeroRowId());
        if (hero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }
        int currentLevel = hero.getLevel();
        Pair<Integer, List<List<Integer>>> pair = heroSupport.setHeroLevel(userId, hero, currentLevel + count, false);
        if (pair.getLeft() != ErrorCode.SUCCESS) {
            return Result.Error(pair.getLeft());
        }
        List<List<Integer>> cost = pair.getRight();
        if (cost.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        RewardResultSet costRewardResultSet = rewardService.executeRewards(userId, cost);
        if (costRewardResultSet.isFailed()) {
            return Result.Error(costRewardResultSet.getResultCode());
        }
        List<CommonProto.HeroDto> heroDtos = new ArrayList<>();
        heroDao.updateNow(hero);
        heroDtos.add(CommonHelper.buildHeroDto(hero));

        //battleRedisDao.updateHero(userId, hero);
        battleRedisDao.removeData(userId);

        // 任务
        List<TaskProcess> taskProcesses = new ArrayList<>();
        taskProcesses.add(TaskProcess.valueOf(TaskType.DAILY, TaskType.DAILY_HERO_LEVEL_UP, 1));
        List<Long> formation = formationCache.getFormation(GameConstant.FORMATION_TYPE_CHAPTER, userExtendDao.getByUserId(userId));
        int totalLevel = this.totalFormationHeroLevel(userId, formation); // 此时从db获取 还是升级前的数据
        if (formation.contains((long) hero.getHeroId())) {
            totalLevel = totalLevel + hero.getLevel() - currentLevel;
        }
        taskProcesses.add(TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TOTAL_FORMATION_HERO_LEVEL, totalLevel));
        taskService.updateTask(userId, taskProcesses);

        HeroProto.HeroUpgradeResponse.Builder response = HeroProto.HeroUpgradeResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costRewardResultSet).toBuilder().addAllHeros(heroDtos));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroAdvanceResponse> advance(HeroProto.HeroAdvanceRequest params) {
        long userId = RequestContext.getUserId();
        long heroRowId = params.getHeroRowId();
        Hero hero = heroDao.getByRowId(userId, heroRowId);
        if (hero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }
        Pair<Integer, List<List<Integer>>> result = heroSupport.setHeroAdvance(userId, hero, hero.getAdvance() + 1);
        if (result.getLeft() != ErrorCode.SUCCESS) {
            return Result.Error(result.getLeft());
        }
        List<List<Integer>> cost = result.getRight();
        if (cost.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        RewardResultSet costRewardResultSet = rewardService.executeRewards(userId, cost);
        if (costRewardResultSet.isFailed()) {
            return Result.Error(costRewardResultSet.getResultCode());
        }
        List<CommonProto.HeroDto> heroDtos = new ArrayList<>();
        heroDao.update(hero);
        heroDtos.add(CommonHelper.buildHeroDto(hero));

        //battleRedisDao.updateHero(userId, hero);
        battleRedisDao.removeData(userId);

        HeroProto.HeroAdvanceResponse.Builder response = HeroProto.HeroAdvanceResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costRewardResultSet).toBuilder().addAllHeros(heroDtos));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroStarResponse> star(HeroProto.HeroStarRequest params) {
        long userId = RequestContext.getUserId();

        long heroRowId = params.getHeroRowId();

        Hero hero = heroDao.getByRowId(userId, heroRowId);
        if (hero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }

        int heroId = hero.getHeroId();
        int star = hero.getStar();

        StarEntity starEntity = gameConfigManager.getGameMemberConfig().getStarEntity(star + 1);
        if (starEntity == null) {
            log.error("hero star up config error, userId: {}, heroId:{}", userId, heroId);
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }
        if (starEntity.getNextID() == 0) {
            return Result.Error(ErrorCode.HERO_STAR_MAX);
        }

        MemberEntity memberEntity = gameConfigManager.getGameMemberConfig().getMemberEntity(heroId);

        List<Integer> costReward = new ArrayList<>();
        costReward.add(memberEntity.getHeroFragmentId());
        costReward.add(-starEntity.getStarUpCost());
        RewardResultSet costResultSet = rewardService.executeReward(userId, costReward);
        if (costResultSet.isFailed()) {
            return Result.Error(costResultSet.getResultCode());
        }

        hero.setStar(star + 1);

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        UserExtend.HeroStatisticModel heroStatisticModel = userExtend.getHeroStatisticModel();
        heroStatisticModel.setTotalHeroStars(heroStatisticModel.getTotalHeroStars() + 1);
        if (hero.getStar() > heroStatisticModel.getMaxHeroStar()) {
            heroStatisticModel.setMaxHeroStar(hero.getStar());
        }
        userExtendDao.update(userExtend);

        List<CommonProto.HeroDto> heroDtos = new ArrayList<>();
        heroDao.update(hero);
        heroDtos.add(CommonHelper.buildHeroDto(hero));

//        battleRedisDao.updateHero(userId, hero);
        battleRedisDao.removeData(userId);

        TaskProcess totalStarTaskProcess = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TOTAL_HERO_STAR, heroStatisticModel.getTotalHeroStars());
        TaskProcess maxStarTaskProcess = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_MAX_HERO_STAR, heroStatisticModel.getMaxHeroStar());
        taskService.updateTask(userId, totalStarTaskProcess, maxStarTaskProcess);

        // 新手7日活动任务
        SevenDayTaskProcess totalStarSevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_TOTAL_HERO_STAR, heroStatisticModel.getTotalHeroStars());
        SevenDayTaskProcess maxStarSevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_MAX_HERO_STAR, heroStatisticModel.getMaxHeroStar());
        sevenDayTaskService.updateTask(userId, totalStarSevenDayTaskProcess, maxStarSevenDayTaskProcess);


        HeroProto.HeroStarResponse.Builder response = HeroProto.HeroStarResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet).toBuilder().addAllHeros(heroDtos));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroResetResponse> reset(HeroProto.HeroResetRequest params) {
        long userId = RequestContext.getUserId();

        long heroRowId = params.getHeroRowId();

        UserExtend userExtend = userExtendDao.getByUserId(userId);

        Hero hero = heroDao.getByRowId(userId, heroRowId);
        if (hero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }

        Triple<Integer, List<List<Integer>>, List<Equip>> result = heroSupport.resetHero(hero, userId, true);
        if (result.getLeft() != ErrorCode.SUCCESS) {
            return Result.Error(result.getLeft());
        }
        List<List<Integer>> returnConsumption = result.getMiddle();

         List<RewardResultSet> rs = new ArrayList<>();
        if (!returnConsumption.isEmpty()) {
            returnConsumption.forEach(v -> {
                RewardResultSet rewardResultSet = rewardService.executeReward(userId, v);
                rs.add(rewardResultSet);
            });
        }

        List<CommonProto.EquipmentDto> equipmentDtos = new ArrayList<>();//卸下英雄装备
        for (Equip equip : result.getRight()) {
            equipDao.update(equip);
            equipmentDtos.add(CommonHelper.buildEquipmentDto(equip));
        }

        userExtendDao.update(userExtend);

        heroDao.update(hero);

        //battleRedisDao.updateHero(userId, hero);
        battleRedisDao.removeData(userId);

        HeroProto.HeroResetResponse.Builder response = HeroProto.HeroResetResponse.newBuilder();

        if(rs.isEmpty()) {
            response.setCommonData(CommonHelper.buildCommonData().toBuilder().addHeros(CommonHelper.buildHeroDto(hero)).addAllEquipment(equipmentDtos));
        }
        else {
            response.setCommonData(CommonHelper.buildCommonDataList(rs).toBuilder().addHeros(CommonHelper.buildHeroDto(hero)).addAllEquipment(equipmentDtos));
        }

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroBookScoreResponse> heroBookScore(HeroProto.HeroBookScoreRequest params) {
        long userId = RequestContext.getUserId();

        long heroRowId = params.getHeroRowId();
        Hero hero = heroDao.getByRowId(userId, heroRowId);
        if (hero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        Map<Integer, Integer> heroCounts = userExtend.getHeroScoreCounts();
        if (heroCounts == null) {
            heroCounts = new HashMap<>();
        }

        int count = heroCounts.getOrDefault(hero.getHeroId(), 0);
        if (count > hero.getStar()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Map<Integer, Integer> scores = commonService.getGameConfigValueSplitOneMap(1002, "\\|");
        int addScore = scores.get(hero.getQuality());
        if (addScore == 0) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        int itemId = commonService.getGameConfigIntValue(1001);
        List<Integer> rewardList = new ArrayList<>(List.of(itemId, addScore));
        RewardResultSet rewardResultSet = rewardService.executeReward(userId, rewardList);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        heroCounts.put(hero.getHeroId(), count + 1);
        userExtend.setHeroScoreCounts(heroCounts);

        userExtendDao.update(userExtend);
        battleRedisDao.removeData(userId);
        HeroProto.HeroBookScoreResponse.Builder response = HeroProto.HeroBookScoreResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroBookRewardResponse> heroBookReward(HeroProto.HeroBookRewardRequest params) {
        long userId = RequestContext.getUserId();

        int rewardId = params.getRewardId();
        BookEntity bookEntity = gameConfigManager.getBookConfig().getBookEntity(rewardId);
        if (bookEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        List<Integer> rewardRecords = userExtend.getHeroBookRewardRecords();
        if (rewardRecords == null) {
            rewardRecords = new ArrayList<>();
        }

        if (rewardRecords.contains(rewardId)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int itemId = commonService.getGameConfigIntValue(1001);
        Item item = itemDao.getByItemId(userId, itemId);

        int score = item.getCount();
        if (score < bookEntity.getStage()) {
            return Result.Error(ErrorCode.HERO_BOOK_SCORE_NOT_ENOUGH);
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, bookEntity.getRewards());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        rewardRecords.add(rewardId);
        userExtend.setHeroBookRewardRecords(rewardRecords);

        userExtendDao.update(userExtend);

        HeroProto.HeroBookRewardResponse.Builder response = HeroProto.HeroBookRewardResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        return Result.Success(response.build());
    }

    @Override
    public List<Hero> getHeroByFormations(long userId, Map<Integer, List<Long>> formations) {
        return getHeroByFormations(userId, formations, null);
    }

    @Override
    public List<Hero> getHeroByFormations(long userId, Map<Integer, List<Long>> formations, List<Hero> allHeroeDatas) {
        List<Hero> heroData = new ArrayList<>();
        if (formations == null) {
            return heroData;
        }

        Set<Long> allHero = new HashSet<>();
        formations.values().forEach(allHero::addAll);
        List<Long> ids = allHero.stream().filter(v -> v != 0L).collect(Collectors.toList());
        if (ids.isEmpty()) {
            return heroData;
        }

        if (allHeroeDatas == null) {
            allHeroeDatas = getAllHeros(userId, ids);
        }

        return allHeroeDatas.stream().filter(hero -> ids.contains(hero.getRowId())).collect(Collectors.toList());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroReplaceSkinResponse> heroReplaceSkin(HeroProto.HeroReplaceSkinRequest params) {
        long userId = RequestContext.getUserId();

        long heroRowId = params.getHeroRowId();
        int skinConfigId = params.getSkinConfigId();

        if (skinConfigId != 0 && gameConfigManager.getItemConfig().getItemEntity(skinConfigId) == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        List<Integer> skins = userExtend.getSkins();
        if (skinConfigId != 0 && (skins == null || !skins.contains(skinConfigId))) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Hero hero = heroDao.getByRowId(userId, heroRowId);
        if (hero == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        hero.setSkinConfigId(skinConfigId);
        heroDao.update(hero);

        battleRedisDao.removeData(userId);

        HeroProto.HeroReplaceSkinResponse.Builder response = HeroProto.HeroReplaceSkinResponse.newBuilder();
        return Result.Success(response.build());
    }


    /**
     * 计算上阵英雄的总等级
     */
    @Override
    public int totalFormationHeroLevel(long userId, List<Long> formation) {
        int totalLevel = 0;
        if (formation != null && !formation.isEmpty()) {
            // 去重
            List<Long> f = formation.stream().distinct().collect(Collectors.toList());
            List<Hero> formationHeroes = this.getAllHeros(userId, f);
            for (Hero hero : formationHeroes) {
                totalLevel += hero.getLevel();
            }
        }
        return totalLevel;
    }


    /**
     * 英雄羁绊升级
     */
    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroBondLevelUpResponse> heroBondLevelUp(HeroProto.HeroBondLevelUpRequest params) {
        long userId = RequestContext.getUserId();

        int bondId = params.getHeroBondConfigId();
        BondEntity bond = gameConfigManager.getBookConfig().getBondEntity(bondId);
        if (bond == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (bond.getCharacterBond().size() != params.getHeroRowIdsCount()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<Hero> heroList = new ArrayList<>();
        for (long heroRowId : params.getHeroRowIdsList()) {
            Hero hero = heroDao.getByRowId(userId, heroRowId);
            if (hero == null) {
                return Result.Error(ErrorCode.HERO_NOT_EXIST);
            }
            heroList.add(hero);
        }

        for (Hero value : heroList) {
            if (!bond.getCharacterBond().contains(value.getHeroId())) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
        }

        UserExtend userExtend = userExtendDao.getByUserId(userId);
        Map<Integer, Integer> heroBondCounts = userExtend.getHeroBondCounts();
        if (heroBondCounts == null) {
            heroBondCounts = new HashMap<>();
        }
        int nowCount = 0;
        for (List<String> value : bond.getBondAttributes()) {
            int star = Integer.parseInt(value.get(0));
            if (heroList.stream().allMatch(hero -> hero.getStar() >= star)) {
                nowCount++;
            }
        }

        int count = heroBondCounts.getOrDefault(bondId, 0);
        if (count >= nowCount) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }


        heroBondCounts.put(bondId, count + 1);
        userExtend.setHeroBondCounts(heroBondCounts);

        userExtendDao.update(userExtend);
        battleRedisDao.removeData(userId);
        HeroProto.HeroBondLevelUpResponse.Builder response = HeroProto.HeroBondLevelUpResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<HeroProto.HeroLosslessResponse> lossless(HeroProto.HeroLosslessRequest params) {
        long userId = RequestContext.getUserId();

        int type = params.getFType();//阵容类型
        int index = params.getFIndex();//阵容下标

        UserExtend userExtend = userExtendDao.getByUserId(userId);

        List<Long> formation = formationCache.getFormation(type, userExtend);
        if (CollectionUtils.isEmpty(formation)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        long rowId = formation.get(index) == null ? 0 : formation.get(index);//阵上英雄
        Hero hero = heroDao.getByRowId(userId, rowId);//阵上英雄
        if (hero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }

        long rRowId = params.getHeroRowId();//替换英雄
        if (formation.contains(rRowId)) {//之前就在阵上
            return Result.Error(ErrorCode.HERO_EXIST);
        }

        Hero rHero = heroDao.getByRowId(userId, rRowId);//替换英雄
        if (rHero == null) {
            return Result.Error(ErrorCode.HERO_NOT_EXIST);
        }

        int level = hero.getLevel();//阵上英雄的等级
        int advance = hero.getAdvance();//阵上英雄的等阶


        //重置阵上英雄
        Triple<Integer, List<List<Integer>>, List<Equip>> upResult = heroSupport.resetHero(hero, userId, false);
        if (upResult.getLeft() != ErrorCode.SUCCESS) {
            return Result.Error(upResult.getLeft());
        }

        //重置替换英雄
        Triple<Integer, List<List<Integer>>, List<Equip>> downResult = heroSupport.resetHero(rHero, userId, false);
        if (downResult.getLeft() != ErrorCode.SUCCESS) {
            return Result.Error(downResult.getLeft());
        }

        //设置替换英雄的等级和阵上英雄一样
        Pair<Integer, List<List<Integer>>> result2 = heroSupport.setHeroLevel(userId, rHero, level, true);
        if (result2.getLeft() != ErrorCode.SUCCESS) {
            return Result.Error(result2.getLeft());
        }

        //设置替换英雄的等阶和阵上英雄一样(防止英雄等级达到当前等阶最大值后，又升了一阶)
        Pair<Integer, List<List<Integer>>> result3 = heroSupport.setHeroAdvance(userId, rHero, advance);

        List<List<Integer>> rewards = new ArrayList<>();
        if (!upResult.getMiddle().isEmpty()) {//重置阵上英雄返回的消耗
            rewards.addAll(upResult.getMiddle());
        }
        if (!downResult.getMiddle().isEmpty()) {//重置替换英雄返回的消耗
            rewards.addAll(downResult.getMiddle());
        }
        if (!result2.getRight().isEmpty()) {//替换英雄升级和升阶所需的消耗
            rewards.addAll(result2.getRight());
        }
        if (!result3.getRight().isEmpty()) {//替换英雄额外升阶的消耗
            rewards.addAll(result3.getRight());
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {//进行资源操作
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
        }

        List<CommonProto.EquipmentDto> equipmentDtos = new ArrayList<>();
        for (Equip equip : upResult.getRight()) {//如果阵上的英雄有穿戴的装备就把装备给替换的英雄
            equipSupport.upEquipment(rHero.getRowId(), equip);
            equipDao.update(equip);

            EquipEntity equipCfgEntity = gameConfigManager.getEquipConfig().getEquipEntity(equip.getEquipId());
            rHero.getEquips().put(equipCfgEntity.getType(), equip);

            equipmentDtos.add(CommonHelper.buildEquipmentDto(equip));
        }

        for (Equip equip : downResult.getRight()) {//如果替换英雄有装备也已经被重置需要入库
            equipDao.update(equip);
            equipmentDtos.add(CommonHelper.buildEquipmentDto(equip));
        }


        // 替阵容
        formation.set(index, rHero.getRowId());

        userExtendDao.update(userExtend);

        heroDao.update(hero);
        heroDao.update(rHero);

        battleRedisDao.removeData(userId);

        HeroProto.HeroLosslessResponse.Builder response = HeroProto.HeroLosslessResponse.newBuilder();
        if (rewardResultSet == null) {
            response.setCommonData(CommonHelper.buildCommonData().toBuilder()
                    .addHeros(CommonHelper.buildHeroDto(hero))
                    .addHeros(CommonHelper.buildHeroDto(rHero))
                    .addAllEquipment(equipmentDtos).
                    putFormations(type, CommonProto.LongArray.newBuilder()
                            .addAllLongArray(formation).build()));
        } else {
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet).toBuilder()
                    .addHeros(CommonHelper.buildHeroDto(hero))
                    .addHeros(CommonHelper.buildHeroDto(rHero))
                    .addAllEquipment(equipmentDtos)
                    .putFormations(type, CommonProto.LongArray.newBuilder()
                            .addAllLongArray(formation).build()));
        }
        return Result.Success(response.build());
    }

}

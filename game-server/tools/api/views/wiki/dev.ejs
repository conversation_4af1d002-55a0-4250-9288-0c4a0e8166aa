<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title>game - wiki</title>
    <link href="/css/bootstrap.css" rel="stylesheet">
    <link href="/css/default.css" rel="stylesheet">
    <link href="/css/bootstrap-select.min.css" rel="stylesheet">
    <link href="/css/bootstrap-datetimepicker.min.css" rel="stylesheet">
    <script src="/js/jquery.min.js"></script>
    <script src="/js/bootstrap.min.js"></script>
    <script src="/js/layer.min.js"></script>
    <script src="/js/bootstrap-select.min.js"></script>
    <script src="/js/public.js"></script>
    <script src="/js/jsonview.js"></script>
    <script src="/js/bootstrap-datetimepicker.min.js"></script>
    <script src="/js/bootstrap-datetimepicker.zh-CN.js"></script>
</head>

<body>
    <div id="header">
        <div class="header">
            &nbsp;&nbsp;game - wiki文档
        </div>
    </div>
    <div id="main">
        <nav class="navbar navbar-default">
            <div class=" navbar-form navbar-left">
                <label for="name">选择服务器:</label>
                <select class="form-control serverList" >
                    <% for(var key in locals.serverJson) { %>
                        <option value="<%= key%>"><%= locals.serverJson[key]['name']%></option>
                    <% } %>
                </select>
            </div>
            <div class=" navbar-form navbar-left">
                <select class="form-control accountType" >
                    <option value="0">userId</option>
                </select>
            </div>
            <div class=" navbar-form navbar-left">
                <% if(locals.userId) { %>
                    <input type="text" name="account" class="form-control userId" placeholder="<%= locals.userId%>">
                <% } else {%>
                    <input type="text" name="account" class="form-control userId" placeholder="ID">
                <% } %>
            </div>
            <div class=" navbar-form navbar-left">
                <button type="submit" islogin="1" class="btn btn-default loginBtn" style="margin-left: 50px;margin-right: 50px;">登陆</button>
            </div>

            <div class="navbar-form navbar-left userInfoDiv">
                <% if(locals.userId) { %>
                    userId: <span class="blueFont"><%= locals.userId%></span>&nbsp;&nbsp;&nbsp;
                    <!-- dbIdx: <span class="blueFont"><%= locals.dbIdx%></span>&nbsp;&nbsp;&nbsp; -->
                    <!-- tableIdx: <span class="blueFont"><%= locals.tableIdx%></span>&nbsp;&nbsp;&nbsp; -->
                <% } %>
            </div>
            
        </nav>
        
        <div>
            <ul class="nav nav-tabs">
                <li class="tablist active" name="apilist" index="0"><a href="#">Api列表</a></li>
                <li class="tablist" name="change" index="1"><a href="#">数据修改</a></li>
                <li class="tablist" name="errorlist" index="2"><a href="#">错误码</a></li>
                <li class="tablist" name="tools" index="3"><a href="#">工具</a></li>
                <li class="tablist" name="statistics" index="4"><a href="#">统计</a></li>
            </ul>
            <div id="apilist">
                <table class="table table-bordered">
                    <caption></caption>
                    <thead>
                        <tr>
                            <th>ModuleId</th>
                            <th>MsgId</th>
                            <th>RequestProto</th>
                            <th>ResponseProto</th> 
                            <th>Test</th>
                            <th>Desc</th>
                        </tr>
                    </thead>
                    <tbody>

                        <% for(var moduleId in locals.protoJson) { %>
                        <% if (moduleId != 90) { %>
                            <% var span = false; %>
                            <% for(var msgId in locals.protoJson[moduleId]) { %>
                                <% if (msgId != "count") { %>
                                    <tr>
                                        <% if (!span) { %>
                                            <td rowspan="<%= locals.protoJson[moduleId].count%>">
                                                <%= moduleId%>
                                            </td>
                                            <% span = true; %>
                                        <% } %>
                                        <td><%= msgId%></td>
                                        <td><%= locals.protoJson[moduleId][msgId]['proto']%></td>
                                        <td><%= locals.protoJson[moduleId][msgId]['response']%></td>
                                        <td>
                                            <a href='./api?cmd=<%= msgId%>&data=<%= locals.protoJson[moduleId][msgId]['data']%>&name=<%= locals.protoJson[moduleId][msgId]['proto']%>-<%= locals.protoJson[moduleId][msgId]['desc']%>&userId=<%= locals.userId%>' target="_blank">
                                                doPost : <%= locals.protoJson[moduleId][msgId]['proto']%>
                                            </a>
                                        </td>
                                        <td><%= locals.protoJson[moduleId][msgId]['desc']%></td>
                                    </tr>
                                <% } %>
                            <% } %>
                        <% } %>
                            
                        <% } %>
                    </tbody>
                </table>
            </div>

            <!-- 修改用户数据 -->
            <br/>
            <div id="change" style="display: none;border:1px solid #ddd;border-radius:4px;">
                <select id="copyItemList" style="display: none;">
                    <% for(var itemId in locals.itemObj) { %>
                        <option value="<%= itemId%>"><%= locals.itemObj[itemId]['name']%> (<%= itemId%>)</option>
                    <% } %>
                </select>
                <br/>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">
                        <label for="exampleInputName2" class="margin20">
                            当前金币：
                            <span class="blueFont changeNum1">
                                <% if(locals.coins) { %>
                                    <%= locals.coins%>
                                <% } else {%>
                                    0
                                <% } %>
                            </span>
                        </label>
                        <input type="number" name="account"  class="form-control resType1" placeholder="输入金币">
                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="1">修改</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            当前钻石：
                            <span class="blueFont changeNum2">
                            <% if(locals.diamonds) { %>
                                <%= locals.diamonds%>
                            <% } else {%>
                                0
                            <% } %>
                            </span>
                        </label>
                        <input type="number" name="account"  class="form-control resType2" placeholder="输入钻石">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="2">修改</button>
                    </div>
                </nav>

                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            添加道具：
                        </label>

                        <select id="itemList" class="selectpicker itemList" data-live-search="true">
                            <% for(var itemId in locals.itemObj) { %>
                                <option value="<%= itemId%>"><%= locals.itemObj[itemId]['note']%> (<%= itemId%>)</option>
                            <% } %>
                        </select>

                        <input type="number" class="form-control resType3" placeholder="输入数量">
                        <input type="number" class="form-control equipLevel" placeholder="输入装备等级">
                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="3">添加</button>
                    </div>
                </nav>


                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            当前等级：
                            <span class="blueFont changeNum19">
                            <% if(locals.level) { %>
                                <%= locals.level%>
                            <% } else {%>
                                0
                            <% } %>
                            </span>
                        </label>
                        <input type="text" class="form-control resType4" placeholder="输入等级">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="4">修改</button>
                    </div>
                </nav>

                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            当前经验：
                            <span class="blueFont changeNum25">
                            <% if(locals.exp) { %>
                                <%= locals.exp%>
                            <% } else {%>
                                0
                            <% } %>
                            </span>
                        </label>
                        <input type="text" class="form-control resType6" placeholder="输入经验值">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="6">修改</button>
                    </div>
                </nav>

<!--                <nav class="navbar navbar-default navbarMargin">-->
<!--                    <div class=" navbar-form navbar-left">-->

<!--                        <label for="exampleInputName2" class="margin20">-->
<!--                            当前主线关卡ID：-->
<!--                            <span class="blueFont changeNum27">-->
<!--                            <% if(locals.missionId) { %>-->
<!--                                <%= locals.missionId%>-->
<!--                            <% } else {%>-->
<!--                                0-->
<!--                            <% } %>-->
<!--                            </span>-->
<!--                        </label>-->
<!--                        <input type="text" name="account" class="form-control resType23" placeholder="输入关卡ID">-->

<!--                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="23">修改</button>-->
<!--                    </div>-->
<!--                </nav>-->


                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            清空此号数据(删除账号)：
                        </label>
                        <input type="hidden" class="form-control resType5" value="1">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="5">确定</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            清空引导：
                        </label>
                        <input type="hidden" class="form-control resType11001" value="1">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="11001">确定</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            清空装备：
                        </label>
                        <input type="hidden" class="form-control resType10" value="1">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="10">确定</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            清空道具：
                        </label>
                        <input type="hidden" class="form-control resType11" value="1">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="11">确定</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            清空关卡宝箱领取记录:
                        </label>
                        <input type="text" name="account" class="form-control resType24" placeholder="输入关卡ID">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="24">确定</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            重置模块开启记录:
                        </label>
                        <input type="text" name="account" class="form-control resType25" placeholder="输入模块ID">
                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="25">确定</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            修改掩码：
                        </label>
                        <input type="text" name="account" class="form-control resType9" placeholder="输入掩码">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="9">修改</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            修改最后一次登录时间戳：
                        </label>
                        <input type="text" name="account" class="form-control resType20" placeholder="输入掩码">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="20">修改</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            登录天数:
                            <span class="blueFont loginDays">
                            0
                            </span>
                        </label>
                        <input type="text" name="account" class="form-control resType21" placeholder="输入数值">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="21">修改</button>
                    </div>
                </nav>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">

                        <label for="exampleInputName2" class="margin20">
                            总充值金额:
                            <span class="blueFont totalRechargeAmount">
                            0
                            </span>
                        </label>
                        <input type="text" name="account" class="form-control resType22" placeholder="输入数值">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="22">修改</button>
                    </div>
                </nav>

                <div class="panel-group" id="accordion" style="margin: 5px;">
                    <!-- 商店 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" 
                                   href="#collapseShop" style="font-size: 14px;font-weight: bold;">
                                    商店：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseShop" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改小宝箱免费时间戳：
                                        </label>
                                        <input type="number" min="1"  class="form-control resType12" placeholder="输入数值">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="12">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改大宝箱免费时间戳：
                                        </label>
                                        <input type="number" min="1"  class="form-control resType13" placeholder="输入数值">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="13">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改小宝箱每日免费次数：
                                        </label>
                                        <input type="number" min="1"  class="form-control resType14" placeholder="输入数值">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="14">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改数据重置时间戳：
                                        </label>
                                        <input type="number" min="1"  class="form-control resType15" placeholder="输入数值">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="15">确定</button>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>

                     <!-- 任务 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="#collapseThree" style="font-size: 14px;font-weight: bold;">
                                    任务：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseThree" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            清空 日常&周常 活跃度奖励领取记录
                                        </label>
                                        <input type="hidden" name="account" class="form-control resType13" value="1">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="101">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改日常活跃度：
                                        </label>
                                        <input class="form-control resType102" type="number"  min="1" placeholder="输入数量">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="102">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改日常任务重置时间戳：
                                        </label>
                                        <input class="form-control resType103" type="number" min="1" placeholder="输入时间戳">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="103">
                                            确定
                                        </button>
                                    </div>
                                </nav>

                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改周常活跃度：
                                        </label>
                                        <input type="number" min="1" class="form-control resType104" placeholder="输入数量">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="104">
                                            确定
                                        </button>
                                    </div>
                                </nav>

                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改周常任务重置时间戳：
                                        </label>
                                        <input type="number" min="1" class="form-control resType105" placeholder="输入时间戳">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="105">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            重置所有任务数据
                                        </label>
                                        <input type="hidden" class="form-control resType106" placeholder="输入数量" value="1">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="106">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                               <nav class="navbar navbar-default navbarMargin">
                                   <div class="navbar-form navbar-left">
                                       <label for="exampleInputName2" class="margin20">
                                           设置每日任务进度：
                                       </label>
                                       <input type="number" min="1" class="form-control resType107" placeholder="任务ID">
                                       <input type="number" min="1" class="form-control otherData107" placeholder="任务进度">
                                       <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="107">
                                           确定
                                       </button>
                                   </div>
                               </nav>
                               <nav class="navbar navbar-default navbarMargin">
                                   <div class="navbar-form navbar-left">
                                       <label for="exampleInputName2" class="margin20">
                                           设置成就进度：
                                       </label>
                                       <input type="number" min="1" class="form-control resType108" placeholder="成就类型">
                                       <input type="number" min="1" class="form-control otherData108" placeholder="成就进度">
                                       <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="108">
                                           确定
                                       </button>
                                   </div>
                               </nav>
                               </div>
                        </div>
                    </div>
                     <!-- 七日任务 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="#collapseSevenDayTask" style="font-size: 14px;font-weight: bold;">
                                    七日任务：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseSevenDayTask" class="panel-collapse collapse">
                            <div class="panel-body">

                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改七日任务活跃度：
                                        </label>
                                        <input class="form-control resType121" type="number"  min="1" placeholder="输入数量">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="121">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                   <div class="navbar-form navbar-left">
                                       <label for="exampleInputName2" class="margin20">
                                           快速完成七日任务：
                                       </label>
                                       <input type="number" min="1" class="form-control resType122" placeholder="七日任务类型">
                                       <input type="number" min="1" class="form-control otherData122" placeholder="成就进度">
                                       <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="122">
                                           确定
                                       </button>
                                   </div>
                               </nav>

                            </div>
                        </div>
                    </div>

                    <!-- 公会 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" 
                                   href="#collapseGuild" style="font-size: 14px;font-weight: bold;">
                                    公会：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseGuild" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-任务:
                                        </label>
                                        <textarea class="form-control content resType310001" rows="5" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310001">修改</button>
                                    </div>
                                </nav>
                                 <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-任务-已刷新次数(包含免费次数):
                                        </label>
                                        <textarea class="form-control content resType310010" rows="1" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310010">修改</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-每日商店:
                                        </label>
                                        <textarea class="form-control content resType310002" rows="5" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310002">修改</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-每周商店:
                                        </label>
                                        <textarea class="form-control content resType310003" rows="5" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310003">修改</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-已签到次数(建设):
                                        </label>
                                        <textarea class="form-control content resType310004" rows="1" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310004">修改</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-每日数据刷新时间戳:
                                        </label>
                                        <textarea class="form-control content resType310005" rows="1" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310005">修改</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            公会功能-每周数据刷新时间戳:
                                        </label>
                                        <textarea class="form-control content resType310006" rows="1" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310006">修改</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            公会boss(个人数据):<br>
                                            damage:个人总伤害, totalChallengeCnt:个人总挑战次数<br>
                                            challengeCnt:剩余挑战次数, buyCntByDiamonds:每日购买次数(钻石)<br>
                                            taskLog: 成就奖励领取记录, boxLog: 击杀boss宝箱领取记录<br>
                                            mailUniqueIdTask, mailUniqueIdBox: 邮件唯一ID, <br>
                                            guildBossTime: 公会boss下次刷新时间(记录用) <br>
                                            guildBossOpenId: 开启的公会boss配置ID(记录用)<br>
                                            recoveryTime: 挑战次数恢复时间<br>
                                            buyCntByCoins: 每日购买次数(金币)<br>
                                            dailyTime: 每日数据刷新时间<br>
                                            weeklyTime: 每周数据刷新时间
                                        </label>
                                        <textarea class="form-control content resType310007" rows="10" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310007">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            公会boss-增加总伤害值
                                        </label>
                                        <textarea class="form-control content resType310008" rows="1" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310008">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            公会boss-重置所有数据
                                        </label>
                                        <input type="hidden" class="form-control resType310009" value="1">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="310009">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            删除所有公会:
                                        </label>
                                        <input type="hidden" class="form-control resType300001" value="1">

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300001">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            随机创建公会(一次最多20个):
                                        </label>
                                        <input  class="form-control resType300002" placeholder="输入数量">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300002">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改公会等级:
                                        </label>
                                        <textarea class="form-control content resType300003" rows="1" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300003">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改公会经验:
                                        </label>
                                        <textarea class="form-control content resType300006" rows="1" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300006">确定</button>
                                    </div>
                                </nav>

                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改公会当日活跃度:
                                        </label>
                                        <textarea class="form-control content resType300004" rows="1" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300004">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改公会总活跃度:
                                        </label>
                                        <textarea class="form-control content resType300005" rows="1" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300005">确定</button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            清空公会聊天记录:
                                        </label>
                                        <input type="hidden" class="form-control resType300007" value="1">

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="300007">确定</button>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>

                    
                    <!-- 邮件 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="#collapseTwo" style="font-size: 14px;font-weight: bold;">
                                    测试邮件：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseTwo" class="panel-collapse collapse">
                            <div class="panel-body">

                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            邮件模板ID：
                                        </label>
                                        <input type="text" name="emailTempId" class="form-control emailTempId"
                                               placeholder="输入标题">
                                    </div>
                                </nav>

                                <nav class="navbar navbar-default navbarMargin mailParams">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            参数：
                                        </label>
                                        <input type="text" class="form-control mailParamName0" placeholder="参数名"/>
                                        <input type="text" class="form-control mailParamValue0" placeholder="参数值"/>
                                        <batton type="submit" onclick="addParamsInput(this,'addMailBtnNav')"
                                                class="btn btn-default" style="font-weight: bold;">+
                                        </batton>
                                    </div>
                                </nav>


                                <nav class="navbar navbar-default navbarMargin mailItems">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            奖励:
                                        </label>
                                        <select id="mailItemList" class="selectpicker mailItemList0"
                                                data-live-search="true">
                                            <% for(var itemId in locals.itemObj) { %>
                                                <option value="<%= itemId %>"><%= locals.itemObj[itemId]['note'] %>
                                                    (<%= itemId %>)
                                                </option>
                                            <% } %>
                                        </select>

                                        <input type="number" min="1" class="form-control mailItemListCount0"
                                               placeholder="输入数量">

                                        <button type="submit" onclick="addItemInput(this, 'mailItemList', 'addMailBtnNav')"
                                                class="btn btn-default" style="font-weight: bold;">+
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin addMailBtnNav">
                                    <div class=" navbar-form navbar-left">
                                        <button type="submit" class="btn btn-default marginLeft50 pubMail">确定</button>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- 章节 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="#collapseChapter" style="font-size: 14px;font-weight: bold;">
                                    章节：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseChapter" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            章节波数(章节,波数)：
                                        </label>
                                        <textarea class="form-control content resType901" rows="1" style="width: 600px;"></textarea>
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="901">确定
                                        </button>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- 英雄 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="#collapseHero" style="font-size: 14px;font-weight: bold;">
                                    英雄：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="collapseHero" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改英雄等级：
                                        </label>
                                        <input type="number" min="1" class="form-control resType1001" placeholder="英雄rowId">
                                        <input type="number" min="1" class="form-control otherData1001" placeholder="等级">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="1001">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改英雄等阶：
                                        </label>
                                        <input type="number" min="1" class="form-control resType1002" placeholder="英雄rowId">
                                        <input type="number" min="1" class="form-control otherData1002" placeholder="等阶">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="1002">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class="navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            修改英雄星级：
                                        </label>
                                        <input type="number" min="1" class="form-control resType1003" placeholder="英雄rowId">
                                        <input type="number" min="1" class="form-control otherData1003" placeholder="星级">
                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="1003">
                                            确定
                                        </button>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>

                    

                    <!-- 翻牌子 -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="#flip" style="font-size: 14px;font-weight: bold;">
                                    翻牌子：（点此展开）
                                </a>
                            </h4>
                        </div>
                        <div id="flip" class="panel-collapse collapse">
                            <div class="panel-body">

                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">

                                        <label for="exampleInputName2" class="margin20">
                                            翻牌子活动数据:<br>
                                            eventId:活动ID<br>
                                            first: 第一次进入活动, mailUniqueId: 邮件唯一ID, mail: 邮件是否发放<br>
                                            acc:已领取累计奖励, clue:线索数<br>
                                            mapId:地图ID, jumpMapId:补强库或补弱库地图ID<br>
                                            accSteps:累计消耗步数, map:地图<br>
                                        </label>
                                        <textarea class="form-control content resType7701" rows="15" style="width: 600px;"></textarea>

                                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="7701">修改</button>
                                    </div>
                                </nav>

                            </div>
                        </div>
                    </div>
                    <!-- 翻牌子 END -->
                    
                    </div>
                </div>


                
            </div>

            <div id="errorlist" style="display: none;border:1px solid #ddd;border-radius:4px;">
                <table class="table table-bordered">
                    <caption></caption>
                    <thead>
                        <tr>
                            <th>错误码</th>
                            <th>错误描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for(var key in locals.errorCode) { %>
                            <tr>
                                <td>
                                    <%= locals.errorCode[key]['code']%>
                                </td>
                                <td>
                                    <%= locals.errorCode[key]['msg']%>
                                </td>
                            </tr>
                        <% } %>

                    </tbody>
                </table> 
            </div>

            <div id="tools" style="display: none;border:1px solid #ddd;border-radius:4px;">
                <br/>
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">
                        <label for="exampleInputName2" class="margin20">
                            userId所在DB集群&表索引：
                        </label>
                        <input type="text" class="form-control getUserDetailInput" placeholder="输入userId">
                        <button type="submit" class="btn btn-default marginLeft50 getUserDetailBtn">确定</button>
                        &nbsp;
                        <label id="userIdDetailLabel">
                                 
                        </label>
                    </div>
                </nav>

                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">
                        <label for="exampleInputName2" class="margin20">
                            同步道具配置表:
                        </label>

                        <button type="submit" class="btn btn-default marginLeft50 syncItemConfigBtn">确定</button>

                        </label>
                    </div>
                </nav>

                <div class="panel-group" id="accordion" style="margin: 5px;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title" style="font-size: 14px;font-weight: bold;">
                                <a data-toggle="collapse" data-parent="#accordion" 
                                   href="#collapseMaskTools">
                                    掩码工具 : (点此展开)
                                </a>
                            </h4>
                        </div>
                        <div id="collapseMaskTools" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left">
                                        <label for="exampleInputName2" class="margin20">
                                            输入掩码:
                                        </label>
                                        <input type="text" name="mask" class="form-control serverMaskInput" placeholder="输入掩码"/>
                                        <button type="submit" class="btn btn-default marginLeft50 getServerMask">转换</button>

                                    </div>
                                </nav>
                                <nav class="navbar navbar-default navbarMargin">
                                    <div class=" navbar-form navbar-left" id="maskToolsDiv">
                                        
                                    </div>
                                    <button type="submit" class="btn btn-default makeServerMask" style="margin-left: 15px;">生成</button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">
                        <label for="exampleInputName2" class="margin20">
                            服务器当前时间: <span class="systemTime blueFont"></span>
                        </label>

                        <input type="text" class="form-control changeSystemTime" placeholder="输入时间戳"/>
                        <button type="submit" class="btn btn-default marginLeft50" onclick="changeSystemTime(1)">修改</button>
                        <button type="submit" class="btn btn-default marginLeft50" onclick="changeSystemTime(0)">还原服务器时间</button>
                        </label>
                    </div>
                </nav>

                <!-- <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">
                        <label for="exampleInputName2" class="margin20">
                            清除所有数据：
                        </label>

                        <input type="hidden" class="form-control resType31" value="1">

                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="31">确定</button>

                                 
                        </label>
                    </div>
                </nav>
                
                <nav class="navbar navbar-default navbarMargin">
                    <div class=" navbar-form navbar-left">
                        <label for="exampleInputName2" class="margin20">
                            设置此账号单独打印日志文件(目录 : /var/log/framework/gameserver/users/*/)：
                        </label>
                        <input type="hidden" class="form-control resType8" value="1">
                        <button type="submit" class="btn btn-default marginLeft50 changeBtn" resType="8">确定</button>
                                 
                        </label>
                    </div>
                </nav> -->

                <div class="panel-group" id="accordion" style="margin: 5px;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title" style="font-size: 14px;font-weight: bold;">
                                <a data-toggle="collapse" data-parent="#accordion" 
                                   href="#collapseLogResource">
                                    道具资源变化信息，最近100条 : (点此展开)
                                </a>
                            </h4>
                        </div>
                        <div id="collapseLogResource" class="panel-collapse collapse">
                            <div class="panel-body">
                                <nav class="navbar navbar-default navbarMargin">
                                        
                                    <button type="submit" class="btn btn-default queryLogResourceModify" style="margin-left: 15px;margin-top: 7px;">查询</button>
                                </nav>

                                <nav class="navbar navbar-default navbarMargin logResourceContent">

                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 统计 -->
            <div id="statistics" style="display: none;border:1px solid #ddd;border-radius:4px;">
                <nav class="navbar navbar-default">

                    <div class="navbar-form navbar-left">
                        <label for="name" style="padding-top:5px;">选择日期(15天以内):</label>
                        <div class="controls input-append date form_date form-control" data-date="" data-date-format="dd MM yyyy" data-link-field="statisticsDate" data-link-format="yyyy-mm-dd" style="float:right;">
                            <input size="16" type="text" value="" readonly style="border:none;">
                            <span class="add-on"><i class="icon-remove"></i></span>
                            <span class="add-on"><i class="icon-th"></i></span>
                        </div>
                        <input type="hidden" id="statisticsDate" value=""  class="form-control" /><br/>
                    </div>
                    
                    <div class=" navbar-form navbar-left">
                        <button type="submit" class="btn btn-default doStatisticsBtn" style="margin-left: 50px;margin-right: 50px;">查询</button>
                    </div>
                </nav>

                <nav class="navbar navbar-default navbarMargin statisticsDDBContent">

                </nav>

                <nav class="navbar navbar-default navbarMargin statisticsAPIContent">

                </nav>
            </div>

        </div>
    </div>
    <div id="footer">
        <span style="text-align:center;">
            接口文档
            
       </span>
    </div>
</body>

</html>
<script type="text/javascript">

    var selectServer = "<%= locals.selectServer %>";
    if (selectServer) {
        $('.serverList').val(selectServer);    
    }

    $('.form_date').datetimepicker({
        language: 'zh-CN',
        weekStart: 1,
        todayBtn:  1,
        autoclose: 1,
        todayHighlight: 1,
        startView: 2,
        minView: 2,
        forceParse: 0
    });

    setTab();

    $(document).ready(function () {
        var html = "";
        for (var i = 0; i <= 128; i ++) {
            html += '<div class="form-group" style="width:70px;">';
            html += '<label style="font-weight:normal;font-size:12px;">第'+i+'位</label>';
            html += '<input type="text" class="form-control maskValue'+i+'" style="width: 40px;height: 30px;" value="0"> ';
            html += '</div>';
        }
        $('#maskToolsDiv').html(html);

        var userId = localStorage.getItem("userId");
        var loginUserId = "<%= locals.userId %>";

        if (userId && !loginUserId) {
            var selectServerId = localStorage.getItem("selectServerId");
            if (selectServerId) {
                $('.serverList').val(selectServerId);    
            } else {
                selectServerId = 0;   
            }

            $('.userId').val(userId);
            $('.loginBtn').click();
        }
    });

    // 道具列表
    tabClick();

    var type = 0;

    // 登陆
    $('.loginBtn').click(function() {
        
        var userId = $('.userId').val();
        if (userId == "") {
            alert("输UserID");
            return;
        }
        
        localStorage.setItem('userId', userId);
        var type = $(".accountType").val();
        var index = parent.layer.load(1, {
            shade: [0.1,'#fff'] //0.1透明度的白色背景
        });
        $.ajax({
            type: "GET",
            url: "/login",
            data: {
                serverIdx:$('.serverList').val(),
                userId:userId,
            },
            success: function(result) {
                console.log(result);
                parent.layer.close(index);
                if (result == "-1") {
                    alert("解析字节流异常");
                    return;
                }
                var obj = JSON.parse(result);
                if (obj['code'] == 102) {
                    alert("用户ID不存在");
                    return;
                }
                if (obj['code'] != 0) {
                    alert("游戏服务器异常code:" + obj['code']);
                    return;
                }

                window.location.reload();
            },
            fail: function() {
                parent.layer.close(index);
            }
        });
    });

    $('.serverList').change(function() {
        var uri = "/saveServerIdx";
        var data = {
            serverIdx:$(this).val(),
        };
        localStorage.setItem("selectServerId", $(this).val());
        request(uri, data, function(result) {
        });
    });

    // 修改数据
    $('.changeBtn').click(function() {
        var resType = $(this).attr('resType');
        var equipLevel = 0;
        var otherData = $('.resType' + resType).val();
        // if (resType == 31) {
        //     // 询问框
        //     var re = confirm("要进行删除操作吗？");
        //     if (re == false) {
        //         return;
        //     }
        // } else 
        if (resType == 3) {
            equipLevel = $(".equipLevel").val();
            if (!equipLevel) {
                equipLevel = 0;
            }
            otherData = equipLevel;
        }
        
        var resNum = parseInt($('.resType' + resType).val());
        if (resNum === "" || resNum == "undefined") {
            alert("输入修改数值");
            return;
        }


        if($('.otherData' + resType).length>0){
            // otherData = parseInt($('.otherData' + resType).val());
            otherData = $('.otherData' + resType).val();
        }

        var itemId = $(".itemList").selectpicker('val');
        var uri = "/addResource";
        var data = {
            serverIdx:$('.serverList').val(),
            resType:resType,
            resNum:resNum,
            itemId:itemId,
            otherData:otherData
        };

        request(uri, data, function(result) {
            alert("修改成功");
            if(result["respData"] !== null && result["respData"] !== "") {
                console.log(result["respData"]);
            }
            $('.changeNum' + resType).html(resNum);
        });
    });

    // tools
    $('.getUserDetailBtn').click(function() {
        var userId = $('.getUserDetailInput').val();
        if (!userId) {
            alert('输入userId');
            return;
        }
        var params = {
            type:1,
            userId:userId
        };
        var uri = "/tools";
        var data = {
            params: params,
            serverIdx:$('.serverList').val(),
        };
        request(uri, data, function(result) {
            var respData = JSON.parse(result['respData']);
            var html = "";
            html += 'accountId: <span class="blueFont">' + respData['accountId'] + "</span>";
            html += ' , tableIdx: <span class="blueFont">' + respData['nDbTableIndex'] + "</span>";
            html += ' , dbIdx: <span class="blueFont">' + respData['dbId'] + "</span>";
            $('#userIdDetailLabel').html(html);
            console.log(respData);
        });
    });

    function addItemInput(e, prefix, btnClass) {

        var itemsCount = 0;
        if (prefix == 'mailItemList') {
            if (addMailItemsInputCount >= 5) {
                return;
            }
            addMailItemsInputCount++;    
            itemsCount = addMailItemsInputCount;
        } else {
            addItemsInputCount ++;    
            itemsCount = addItemsInputCount;
        }

        var className = prefix;

        var html = '<nav class="navbar navbar-default navbarMargin '+prefix+'InputNav">';


        if (prefix == 'addItemList') {
            html = '<nav class="navbar navbar-default navbarMargin '+prefix+'InputNav savedItemListInputNav">';
        }
        

        html += '<div class=" navbar-form navbar-left">';
        html += '<label for="exampleInputName2" class="margin20">奖励:</label>';
        html += '<select id="'+className+'" class="selectpicker '+className+'" data-live-search="true" >';
        html += $('#copyItemList').html();
        html += '</select>';
        html += '&nbsp;<input type="number" min="1" class="form-control '+prefix +'Count" placeholder="输入数量">';
        html += '&nbsp;<button type="submit" onclick="minusItemInput(this,\'' + prefix + '\')" class="btn btn-default" style="font-weight: bold;">-</button>';
        html += '</div>';
        html += '</nav>';

        $('.' + btnClass).before(html);
        // $(e).parent().parent().after(html);
        $("." + className).selectpicker('refresh');
    }
    


    function minusItemInput(e, prefix) {
        if (prefix == 'mailItemList') {
            addMailItemsInputCount--;    
        } else {
            addItemsInputCount --;
        }
        
        $(e).parent().parent().remove();
    }


    // 掩码转换
    $('.getServerMask').click(function() {
        var serverMaskInput = $('.serverMaskInput').val();
        if (serverMaskInput == "") {
            alert('输入mask值');
        }
        var params = {
            type:3,
            mask:serverMaskInput
        };

        var data = {
            params: params,
            serverIdx:$('.serverList').val(),
        };
        var uri = "/tools";
        request(uri, data, function(result) {
            var respData = JSON.parse(result.respData);
            var html = "";
            for (var key in respData) {
                
                html += '<div class="form-group" style="width:70px;">';
                html += '<label style="font-weight:normal;font-size:12px;">第'+key+'位</label>';
                if (respData[key]) {
                    html += '<input type="text" class="form-control maskValue'+key+'" style="width: 40px;height: 30px;color:red;" value="1"> ';
                } else {
                    html += '<input type="text" class="form-control maskValue'+key+'" style="width: 40px;height: 30px;" value="0"> ';
                }
                
                html += '</div>';
            }
            $('#maskToolsDiv').html(html);
        });
    });

    $('.makeServerMask').click(function() {

        var maskMap = {};
        for (var i = 0; i <= 128; i ++) {
            var value = parseInt($('.maskValue' + i).val());
            if (value != 0 && value != 1) {
                alert("第" + i + "位值错误!");
                return;
            }
            maskMap[i] = value;
        }

        var params = {
            type:4,
            maskMap:JSON.stringify(maskMap)
        };

        var data = {
            params: params,
            serverIdx:$('.serverList').val(),
        };
        var uri = "/tools";
        request(uri, data, function(result) {
            var respData = result.respData;
            $('.serverMaskInput').val(respData);
        });
    });


    $('.addItemsBatch').click(function() {
        addItemBatch("");
    });

    $('.addItemsBathSaveConfig').click(function() {
        var saveItemConfigName = $('.saveItemConfigName').val();
        if (!saveItemConfigName) {
            alert('请输入保存的标识名');
            return;
        }

        addItemBatch(saveItemConfigName);
    });

    var addMailItemsInputCount = 0;
    var addItemsInputCount = 0;
    $('.pubMail').click(function () {
        var emailTempId = $('.emailTempId').val();


        var mailParams = {};
        var mailParamsInputNav = $('.mailParamListInputNav');
        mailParamsInputNav.each(function (i, obj) {
            var name = $(obj).find('.mailParamListName').val();
            var value = $(obj).find('.mailParamListValue').val();
            if (name.length <= 0 || value.length <= 0) {
                alert("请删除无效的参数");
                return false
            }
            mailParams[name] = value;
        });
        var paramName0 = $('.mailParamName0').val();
        var paramValue0 = $('.mailParamValue0').val();
        if (paramName0.length > 0 && paramValue0.length > 0) {
            mailParams[paramName0] = paramValue0;
        }


        var items = {};
        var addItemsInputNav = $('.mailItemListInputNav');
        addItemsInputNav.each(function (i, obj) {
            var id = $(obj).find('.mailItemList').selectpicker('val');
            var count = $(obj).find('.mailItemListCount').val();
            if (count == "" || parseInt(count) <= 0) {
                alert("输入道具数量");
                return false;
            }
            items[id] = parseInt(count);
        });


        var itemId = $(".mailItemList0").selectpicker('val');
        var itemCount = $('.mailItemListCount0').val();
        itemCount = parseInt(itemCount);
        if (!itemCount) {
            itemCount = 0;
        }
        if (itemCount == "") {
            itemCount = 0;
        }

        if (itemCount > 0) {
            items[itemId] = itemCount;
        }

        var params = {
            type: 2,
            emailTempId: emailTempId,
            params: JSON.stringify(mailParams),
            items: JSON.stringify(items),
        };

        var data = {
            params: params,
            serverIdx: $('.serverList').val(),
        };
        var uri = "/tools";
        request(uri, data, function (result) {
            if (result.code == 0) {
                alert("发送成功");
                return;
            }
            alert("发送失败，稍后重试");
            // var respData = JSON.parse(result['respData']);
        });
    });

    var loginExtra = "<%= locals.extra%>";
    if (loginExtra != "{}" && loginExtra) {
        loginExtra = JSON.parse(formateObject(replaceAll(loginExtra)));   
        console.log(loginExtra); 
        if (loginExtra.hasOwnProperty("nowServerTime")) {
            $(".systemTime").html(loginExtra['nowServerTime']);
        }

        if (loginExtra.hasOwnProperty("loginDays")) {
            $(".loginDays").html(loginExtra['loginDays']);
        }
        if (loginExtra.hasOwnProperty("totalRechargeAmount")) {
            $(".totalRechargeAmount").html(loginExtra['totalRechargeAmount']);
        }


        // 公会
        if (loginExtra.hasOwnProperty("guildUser")) {
            var guildUser = loginExtra["guildUser"];
            if (guildUser.hasOwnProperty("tasks")) {
                $(".resType310001").html(JSON.stringify(guildUser["tasks"]));
            }
            if (guildUser.hasOwnProperty("dailyShop")) {
                $(".resType310002").html(JSON.stringify(guildUser["dailyShop"]));
            }
            if (guildUser.hasOwnProperty("weeklyShop")) {
                $(".resType310003").html(JSON.stringify(guildUser["weeklyShop"]));
            }
            if (guildUser.hasOwnProperty("signInCnt")) {
                $(".resType310004").html(guildUser["signInCnt"]);
            }
            if (guildUser.hasOwnProperty("dailyTM")) {
                $(".resType310005").html(guildUser["dailyTM"]);
            }
            if (guildUser.hasOwnProperty("weeklyTM")) {
                $(".resType310006").html(guildUser["weeklyTM"]);
            }
            if (guildUser.hasOwnProperty("weeklyTM")) {
                $(".resType310006").html(guildUser["weeklyTM"]);
            }
            if (guildUser.hasOwnProperty("guildBossModel")) {
                $(".resType310007").html(JSON.stringify(guildUser["guildBossModel"]));
            }
            if (guildUser.hasOwnProperty("taskRefreshCount")) {
                $(".resType310010").html(guildUser["taskRefreshCount"]);
            }
            
        }

        if (loginExtra.hasOwnProperty("guild")) {
            var guild = loginExtra['guild'];
            if (guild.hasOwnProperty("guildLevel")) {
                $(".resType300003").html(guild["guildLevel"]);
            }
            if (guild.hasOwnProperty("guildExp")) {
                $(".resType300006").html(guild["guildExp"]);
            }
            if (guild.hasOwnProperty("guildDayActive")) {
                $(".resType300004").html(guild["guildDayActive"]);
            }
            if (guild.hasOwnProperty("guildActive")) {
                $(".resType300005").html(guild["guildActive"]);
            }
        }
            
    }

    function fillLoginExtra() {
        $('.savedAddItemsConfig').selectpicker('destroy');
        var html = "";
        html += '<option value="0">请选择</option>';
        for (var key in loginExtra) {
            html += '<option value="'+key+'">'+key+'</option>';
        }
        $('.savedAddItemsConfig').html(html);
        $(".savedAddItemsConfig").selectpicker('refresh');
        $('.savedAddItemsConfigNav').show();
    }

    function addItemBatch(saveItemConfigName) {
        var items = {};
        var addItemsInputNav = $('.addItemListInputNav');
        addItemsInputNav.each(function (i, obj) {
            var id = $(obj).find('.addItemList').selectpicker('val');
            var count = $(obj).find('.addItemListCount').val();
            if (count == "" || parseInt(count) <= 0) {
                alert("输入道具数量");
                return false;
            }
            items[id] = parseInt(count);
        });

        var itemId = $(".addItemList0").selectpicker('val');
        var itemCount = $('.addItemListCount0').val();
        itemCount = parseInt(itemCount);
        if (!itemCount) {
            itemCount = 0;
        }
        if (itemCount == "") {
            itemCount = 0;
        }

        if (itemCount > 0) {
            items[itemId] = itemCount;
        }
        var params = {
            type: 5,
            items:JSON.stringify(items),
            saveItemConfigName:saveItemConfigName
        };

        var data = {
            params: params,
            serverIdx:$('.serverList').val(),
        };
        var uri = "/tools";
        request(uri, data, function(result) {
            if (result.code == 0) {
                alert("添加成功");
                if (saveItemConfigName != "") {
                    if (loginExtra == "{}") {
                        loginExtra = {};
                    }
                    loginExtra[saveItemConfigName] = items;
                    fillLoginExtra();
                }
                return;
            }
            alert("添加失败，稍后重试");
        });
    }

    function addParamsInput(e, btnClass) {
        var paramsCount = 0;

        var html = '<nav class="navbar navbar-default navbarMargin mailParamListInputNav">';
        html += '<div class="navbar-form navbar-left">';
        html += '<label for="exampleInputName2" class="margin20">参数：</label>';
        html += '<input type="text" class="form-control mailParamListName" placeholder="参数名"/>';
        html += '<input type="text" class="form-control mailParamListValue" placeholder="参数值"/>';
        html += '<batton type="submit" onclick="minusParamsInput(this)" class="btn btn-default" style="font-weight: bold;">-</batton>'
        html += '</div>';
        html += '</nav>';
        $('.mailItems').before(html);
        $('.mailParamList').selectpicker('refresh');
    }

    function saveAddItemsChange() {
        var selectKey = $('.savedAddItemsConfig').selectpicker('val');
        $('.savedItemListInputNav').remove();
        $('.addItemList0').selectpicker('val', 1);
        $('.addItemListCount0').val("");
        $('.saveItemConfigName').val("");
        if (selectKey == 0) {
            return;
        }

        var items = loginExtra[selectKey];

        var html = '';
        var isFirst = true;
        for (var key in items) {
            if (isFirst) {
                isFirst = false;
                $('.addItemList0').selectpicker('val', key);
                $('.addItemListCount0').val(items[key]);
                continue;
            }
            html += '<nav class="navbar navbar-default navbarMargin addItemListInputNav savedItemListInputNav">';
            html += '<div class=" navbar-form navbar-left">';
            html += '<label for="exampleInputName2" class="margin20">奖励:</label>';
            html += '<select id="addItemList" class="selectpicker addItemList itemConfig_'+key+'" data-live-search="true" itemConfig='+key+' >';
            html += $('#copyItemList').html();
            html += '</select>';
            html += '&nbsp;<input type="number" min="1" class="form-control addItemListCount" placeholder="输入数量" value="'+items[key]+'">';
            html += '&nbsp;<button type="submit" onclick="minusItemInput(this,\'addItemList\')" class="btn btn-default" style="font-weight: bold;">-</button>';
            html += '</div>';
            html += '</nav>';
        }

        $('.addItemsConfigBtnNav').before(html);
        $(".addItemList").selectpicker('refresh');

        $('.savedItemListInputNav').each(function (i, obj) {
            // console.log(i);
            // console.log($(obj).find('.addItemList').attr("class")); 
            var clazz = $(obj).find('.addItemList').attr("class").split(" ");
            var itemId = 0;
            for (var i = 0; i < clazz.length; i ++) {
                if (clazz[i].indexOf('itemConfig_') >= 0) {
                    itemId = clazz[i].split('_')[1];
                    break;
                } 
            }
            $(obj).find('.addItemList').selectpicker('val', itemId);
            
        });

        $('.saveItemConfigName').val(selectKey);
    }

    // 最近资源变化信息
    $('.queryLogResourceModify').click(function() {
        // logResourceContent
        var params = {
            type:5,
        };

        var data = {
            params: params,
            serverIdx:$('.serverList').val(),
        };
        var uri = "/tools";
        request(uri, data, function(result) {
            var respData = result.respData;

            var result = JSON.parse(respData);
            
            var html = '';
            html += '<table class="table" >';
            html += '  <thead>';
            html += '    <tr>';
            html += '      <th style="width:10%;">transId</th>';
            html += '      <th style="width:20%;">logTime</th>';
            html += '      <th style="width:15%;">command</th>';
            html += '      <th style="width:40%;">items</th>';
            html += '      <th style="width:5%;">customType</th>';
            html += '      <th style="width:20%;">extra</th>';
            html += '    </tr>';
            html += '  </thead>';
            html += '  <tbody>';

            for (var i = 0; i < result.length; i ++) {
                var data = result[i];
                html += '    <tr>';
                html += '    <td style="vertical-align:middle;">' + data['transId'] + '</td>';
                html += '    <td style="vertical-align:middle;">' + data['logTime'] + '</td>';
                html += '    <td style="vertical-align:middle;">' + data['command'] + '</td>';
                html += '    <td>';
                var items = data['items'];
                for (var key in items) {
                    var item = items[key];
                    var itemNum = item['num'];
                    var itemId = item['itemId'];
                    var itemName = item['name'];
                    var total = item['total'];

                    html += getIden(itemNum) + " 道具ID: " + itemId + " , " + itemName + " * " + getColorValue(itemNum) + ", 剩余 * " + getColorValue(total) + "<br>";


                }
                html += '    </td>';
                html += '    <td style="vertical-align:middle;word-break:break-all;">' + data['customType'] + '</td>';
                html += '    <td style="vertical-align:middle;word-break:break-all;">' + data['extra'] + '</td>';
                html += '    </tr>';
            }

            html += '  </tbody>';
            html += '</table>';

            $('.logResourceContent').html(html);
            
        });
    });

    function getIden(num) {
        if (num < 0) {
            return "<span style='color:red;font-size:18px;'> - </span>";
        } else {
            return "<span style='color:green;font-size:18px;'> + </span>";
        }
    }
    function getColorValue(num) {
        if (num < 0) {
            return "<span style='color:red;font-size:14px;'> " + num + " </span>";
        } else {
            return "<span style='color:green;font-size:14px;'> " + num + " </span>";
        }
    }

    $(".syncItemConfigBtn").click(function() {
        var index = parent.layer.load(1, {
            shade: [0.1,'#fff'] //0.1透明度的白色背景
        });
        $.ajax({
            type: "GET",
            url: "/syncItemConfig",
            data: {
                ids:[878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,953]
            },
            success: function(result) {
                alert("同步成功!");
                parent.layer.close(index);
                window.location.reload();
            },
            fail: function() {
                alert("同步失败!");
                parent.layer.close(index);
            }
        });
    });

    function changeSystemTime(type) {
        if (type == 1) {
            var value = $('.changeSystemTime').val();
            if (!checkIsNumber(value) || value.length != 10) {
                alert("输入正确的时间戳");
                return;
            }
        } else {
            value = 0;
        }

        var params = {
            type:0,
            timestamp: value
        };
        var data = {
            params: params,
            serverIdx:$('.serverList').val(),
        };
        var uri = "/tools";
        request(uri, data, function(result) {
            if (result.code != 0) {
                alert("修改失败");
                return;
            }
            var time = result.respData;
            if (time === "false") {
                alert("修改失败, 修改时间只支持k8s环境下");
                return;
            }
            $('.systemTime').html(time);

            alert("修改成功");
            // window.location.reload();
        });
    }

    $('.mazeRoomUnFinish').click(function () {
        var xPoint = $('.xPoint').val();
        var yPoint = $('.yPoint').val();
        var resType = $(this).attr('resType');
        var uri = "/mazeRoomUnFinish";
        var data = {
            serverIdx: $(".serverList").val(),
            resType: resType,
            xPoint: xPoint,
            yPoint: yPoint
        };
        request(uri, data, function (result) {
            alert("修改成功");
        });
    });


    var cmdDesc = <%- JSON.stringify(locals.protoJson) %>;
    var cmdDescMap = {};
    for (var key in cmdDesc) {
        for (var cmd in cmdDesc[key]) {
            cmdDescMap[cmd] = cmdDesc[key][cmd]['desc'];
        }
    }

    $(".doStatisticsBtn").click(function() {
        var date = $("#statisticsDate").val();
        if (date == "") {
            alert("选择日期");
            return;
        }

        
          var uri = "/statistics/query";
          var data = {
              date: date,
              serverIdx:$('.serverList').val(),
          };

          $.ajax({
                type: "GET",
                url: uri,
                data: data,
                success: function(result) {
                    var data = JSON.parse(result);

                    var ddbData = {};
                    var apiData = {};
                    for (var key in data) {
                        if (key.startsWith("ddb-")) {


                            var keyArr = key.split("-");

                            var tableNameParts = keyArr.slice(1, -1);
                            var tableName = tableNameParts.join('-');
                            var type = keyArr[keyArr.length - 1];
                            
                            if (!ddbData.hasOwnProperty(tableName)) {
                                ddbData[tableName] = {};
                            }
                            ddbData[tableName][type] = data[key];
                        } else {
                            var cmd = "";
                            if (key.indexOf("-") >= 0) {
                                var keyArr = key.split("-");
                                cmd = keyArr[0];
                                if (!apiData.hasOwnProperty(cmd)) {
                                    apiData[cmd] = {};
                                }

                                var tableNameParts = keyArr.slice(1, -1);
                                var tableName = tableNameParts.join('-');
                                var type = keyArr[keyArr.length - 1];

                                
                                var value = data[key];
                                if (!apiData[cmd].hasOwnProperty(tableName)) {
                                    apiData[cmd][tableName] = {};
                                }
                                apiData[cmd][tableName][type] = data[key];
                                
                            } else {
                                cmd = key;
                                if (!apiData.hasOwnProperty(cmd)) {
                                    apiData[cmd] = {};
                                }
                                apiData[cmd]["api_counts"] = data[key];
                            }
                            
                        }
                    }

                    

                    // ddb 统计
                    var html = '<label for="name" style="padding-top:5px;">ddb使用量</label>';
                    html += '<table class="table" >';
                    html += '  <thead>';
                    html += '    <tr>';
                    html += '      <th style="width:10%;">表名</th>';
                    html += '      <th style="width:20%;">rcu</th>';
                    html += '      <th style="width:15%;">wcu</th>';
                    html += '    </tr>';
                    html += '  </thead>';
                    html += '  <tbody>';

                    for (var key in ddbData) {
                        html += '    <tr>';
                        html += '    <td style="vertical-align:middle;">' + key + '</td>';
                        html += '    <td style="vertical-align:middle;">' + ddbData[key]['rcu'] + '</td>';
                        html += '    <td style="vertical-align:middle;">' + ddbData[key]['wcu'] + '</td>';
                        html += '    <tr>';
                    }

                    html += '  </tbody>';
                    html += '</table>';
                    $('.statisticsDDBContent').html(html);


                    // ddb 统计
                    var html = '<label for="name" style="padding-top:5px;">api请求</label>';
                    html += '<table class="table" >';
                    html += '  <thead>';
                    html += '    <tr>';
                    html += '      <th style="width:10%;">command</th>';
                    html += '      <th style="width:10%;">commandDesc</th>';
                    html += '      <th style="width:20%;">counts</th>';
                    html += '      <th style="width:15%;">tableName</th>';
                    html += '      <th style="width:15%;">rcu</th>';
                    html += '      <th style="width:15%;">wcu</th>';
                    html += '    </tr>';
                    html += '  </thead>';
                    html += '  <tbody>';

                    for (var key in apiData) {
                        var tableNamesCount = Object.keys(apiData[key]).length - 1; // 减去 'api_counts' 那一项
                        var isFirstTableName = true;
                        for (var tableName in apiData[key]) {
                            if (tableName === "api_counts") {
                                continue;
                            }
                            if (isFirstTableName) {
                                // 在第一个tableName的时候写入key和api_counts，并设置rowspan
                                html += '    <tr>';
                                html += '      <td rowspan="' + tableNamesCount + '" style="vertical-align:middle;" >' + key + '</td>';
                                html += '      <td rowspan="' + tableNamesCount + '" style="vertical-align:middle;" >' + cmdDescMap[key] + '</td>';
                                
                                html += '      <td rowspan="' + tableNamesCount + '" style="vertical-align:middle;" >' + apiData[key]['api_counts'] + '</td>';
                                isFirstTableName = false;
                            } else {
                                // 后续的tableName开始新的行
                                html += '    <tr>';
                            }
                            html += '      <td style="vertical-align:middle;" >' + tableName + '</td>';
                            html += '      <td style="vertical-align:middle;" >' + apiData[key][tableName]['rcu'] + '</td>';
                            html += '      <td style="vertical-align:middle;" >' + apiData[key][tableName]['wcu'] + '</td>';
                            html += '    </tr>';
                        }
                    }

                    html += '  </tbody>';
                    html += '</table>';
                    $('.statisticsAPIContent').html(html);

                    console.log(apiData);
                    console.log(JSON.stringify(apiData));
                },
                fail: function() {
                    parent.layer.close(index);
                }
            });

          
    });

</script>













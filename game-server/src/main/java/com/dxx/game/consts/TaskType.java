package com.dxx.game.consts;

/**
 * <AUTHOR>
 * @date 2021/4/10 20:49
 */
public interface TaskType {

    /**
     * 每日任务
     */
    int DAILY = 1;
    /**
     * 每周任务
     */
    int WEEKLY = 2;
    /**
     * 成就
     */
    int ACHIEVE = 3;

    /* ********************************日常**************************************** */
    int DAILY_CHAPTER = 1; // 进行主线肉鸽次数
    int DAILY_HERO_LEVEL_UP = 2; // 进行英雄升级次数
    int DAILY_EQUIP_COMPOSE = 3; // 装备合成次数
    int DAILY_HANGUP_RECEIVE = 4; // 领取挂机奖励次数
    int DAILY_HANGUP_OPEN_BOX = 5;// 开宝箱次数（宝箱工厂）
    int DAILY_EQUIP_LEVEL_UP = 6; // 进行(系统)装备升级次数
    int DAILY_TOWER_CHALLENGE = 7;// 进行挑战之塔次数
    int DAILY_LOGIN = 8;// 登录游戏次数
    int DAILY_ARENA_CHALLENGE = 11;// 进行竞技场挑战次数
    int DAILY_MAZE_CHALLENGE = 12;// 挑战迷宫次数
    int DAILY_INTEGRAL_SHOP_BUY = 13;// 进行黑市购买次数
    int DAILY_GACHA_HERO = 14;// 进行英雄招募次数
    int DAILY_HOLD_ORE = 15; // 占领矿场次数


    /* ********************************成就**************************************** */
    int ACHIEVE_PROGRESS_CHAPTER = 1; // 章节进度
    int ACHIEVE_PROGRESS_CHAPTER_WAVE = 2; // 章节+波数进度
    int ACHIEVE_LEVEL_HERO = 3; // 英雄等级
    int ACHIEVE_TOTAL_HANGUP_COINS = 6; // 挂机累积金币
    int ACHIEVE_LEVEL_TOWER = 8;// 挑战之塔层数（参数来自Tower）
    int ACHIEVE_LEVEL_CROSS_ARENA = 9; // 竞技场到达某个场（参数来自crossArena）
    int ACHIEVE_TIMES_INTEGRAL_SHOP_BUY = 10; // 黑市购买次数
    int ACHIEVE_TOTAL_LOGIN_DAYS = 11; // 累计登录XX天
    int ACHIEVE_COUNT_DIFF_HERO = 12; // 拥有N个不同的英雄
    int ACHIEVE_MAX_POWER = 13; // 队伍最高战力达到XX
    int ACHIEVE_COUNT_MAZE_PASS = 14; // 迷宫冒险通关XX次
    int ACHIEVE_TOTAL_FORMATION_HERO_LEVEL = 15; // 上阵英雄总等级达到XX级
    int ACHIEVE_TOTAL_HERO_STAR = 16; // 所有英雄总星级达到XX级
    int ACHIEVE_TOTAL_EQUIP_LEVEL = 17; // (系统)装备总等级达到XX级
    int ACHIEVE_COUNT_GACHA_HERO = 18; // 累计招募英雄XX次
    int ACHIEVE_COUNT_OPEN_BOX = 19; // 开启XX个宝箱
    int ACHIEVE_COUNT_DIFF_RELIC = 20; // 拥有N个不同的藏品
    int ACHIEVE_TOTAL_RELIC_STAR = 21; // 藏品总星级达到XX星
    int ACHIEVE_COUNT_ORE_FISH = 22; // 矿场累计攻打成功XX次
    int ACHIEVE_MAX_HERO_STAR = 23; // 英雄最高星级
    int ACHIEVE_COUNT_TOTAL_FISH = 24; // 累计钓鱼XX次
    int ACHIEVE_COUNT_TOTAL_RELIC = 25; // 累计藏品抽奖XX次


    /* ********************************活动**************************************** */
    int EVENT_PROGRESS_LOGIN = 1; // 登录天数
    int EVENT_PROGRESS_INTEGRAL_BUY = 2; // 黑市购买
}

package com.dxx.game.modules.mission.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.MissionProto.*;

public interface MissionService {

    /**
     * 获取数据
     */
    Result<MissionGetInfoResponse> getInfoAction(MissionGetInfoRequest params);

    /**
     * 开始挑战
     */
    Result<MissionStartResponse> startAction(MissionStartRequest params);

    /**
     *
     */
    Result<MissionEndResponse> missionEndAction(MissionEndRequest params);
}

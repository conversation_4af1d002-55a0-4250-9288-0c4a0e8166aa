#!/bin/sh


WORKSPACE="$(cd "$(dirname "$0")" && pwd)"


sh $WORKSPACE/../config/luban/Server/gen.sh

rsync -av --remove-source-files $WORKSPACE/../config/luban/Server/output_code/* $WORKSPACE/../../src/main/java/com/dxx/game/config/

rsync -av --remove-source-files $WORKSPACE/../config/luban/Server/output_json/* $WORKSPACE/../../gameconf/

# echo $dir_name

# if [ ! -d $dir_name"/../config/normal/data" ]; then
# 	mkdir -p $dir_name"/../config/normal/data"
# fi

# node $dir_name/../config/normal/GetConfigGameName.js


# if [ $? -ne 0 ];then
#     echo "fail"
#     exit
# fi

# read -p "Are you sure (Y/[N]):" para

# case $para in
# 	[yY])
# 		node $dir_name/../config/normal/CheckModify.js
# 		node $dir_name/../config/normal/CreateConfig.js
# 		;;
# 	(nN)
# 		exit
# 		;;
# 	(*)
# 		exit
# 		;;
# esac

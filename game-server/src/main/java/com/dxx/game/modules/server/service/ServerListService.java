package com.dxx.game.modules.server.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.repository.ServerDao;
import com.dxx.game.dto.ServerListProto;

public interface ServerListService {
    int generateServerId(String mark);

    int getMarkId(String mark);

    int getZoneIdByServerId(int serverId);

    long getOpenServerTime(int serverId);

    void onUserCreate(int serverId);

    Result<ServerListProto.UserGetLastLoginResponse> userGetLastLoginRequest(ServerListProto.UserGetLastLoginRequest params);

    void checkStatus(int serverId);

    Result<ServerListProto.FindServerListResponse> findServerListRequest(ServerListProto.FindServerListRequest params);

    int getServerWarZoneId(int serverId);

    Long getServerWarZoneOpenTime(int warType, int serverId);

    Long getServerWarZoneOpenTimeByWarZoneId(int warType, int warId);


}

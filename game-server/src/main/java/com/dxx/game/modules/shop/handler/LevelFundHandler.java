package com.dxx.game.modules.shop.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.ShopProto;
import com.dxx.game.modules.shop.service.LevelFundService;
import com.google.protobuf.Message;
import jakarta.annotation.Resource;

/**
 * @author: lsc
 * @createDate: 2025/6/7
 * @description:
 */
@ApiHandler
public class LevelFundHandler {

    @Resource
    private LevelFundService levelFundService;

    @ApiMethod(command = MsgReqCommand.LevelFundGetInfoRequest, name = "基金-获取数据")
    public Result<ShopProto.LevelFundGetInfoResponse> levelFundGetInfo(Message msg) {
        ShopProto.LevelFundGetInfoRequest params = (ShopProto.LevelFundGetInfoRequest) msg;
        return levelFundService.levelFundGetInfo(params);
    }

    @ApiMethod(command = MsgReqCommand.LevelFundRewardRequest, name = "基金-领取奖励")
    public Result<ShopProto.LevelFundRewardResponse> levelFundReward(Message msg) {
        ShopProto.LevelFundRewardRequest params = (ShopProto.LevelFundRewardRequest) msg;
        return levelFundService.levelFundReward(params);
    }
}

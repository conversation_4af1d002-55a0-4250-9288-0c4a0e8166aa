
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Achievements_Achievements {
    public Achievements_Achievements(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        AchievementsType = _buf.get("AchievementsType").getAsInt();
        AchievementsLevel = _buf.get("AchievementsLevel").getAsInt();
        AccumulationType = _buf.get("AccumulationType").getAsInt();
        ProgressType = _buf.get("ProgressType").getAsInt();
        AchievementsNeed = _buf.get("AchievementsNeed").getAsInt();
        AchievementsDescribe = _buf.get("AchievementsDescribe").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  Reward.add(_v0); }   }
        Jump = _buf.get("Jump").getAsInt();
        UnlockNeed = _buf.get("UnlockNeed").getAsInt();
    }

    public static Achievements_Achievements deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Achievements_Achievements(_buf);
    }

    /**
     * ID<br/>新加后续成就不要打乱id,在后面成组添加即可
     */
    public final int ID;
    /**
     * 成就类型<br/>
     */
    public final int AchievementsType;
    /**
     * 成就等级<br/>(同类型成就,先完成上一级成就才显示出下一级成绩.成就进度会记录到下一级.)
     */
    public final int AchievementsLevel;
    /**
     * 是否为累加条件<br/>0,非累加<br/>1,累加
     */
    public final int AccumulationType;
    /**
     * 0:显示0/1<br/>1:直接显示配置数
     */
    public final int ProgressType;
    /**
     * 成就要求
     */
    public final int AchievementsNeed;
    /**
     * 成就描述
     */
    public final String AchievementsDescribe;
    /**
     * 成就奖励<br/>道具类型,道具id,道具数量|…
     */
    public final java.util.List<Integer> Reward;
    /**
     * 跳转
     */
    public final int Jump;
    /**
     * Function中ID<br/>0为默认解锁<br/>-1为不开放
     */
    public final int UnlockNeed;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "AchievementsType:" + AchievementsType + ","
        + "AchievementsLevel:" + AchievementsLevel + ","
        + "AccumulationType:" + AccumulationType + ","
        + "ProgressType:" + ProgressType + ","
        + "AchievementsNeed:" + AchievementsNeed + ","
        + "AchievementsDescribe:" + AchievementsDescribe + ","
        + "Reward:" + Reward + ","
        + "Jump:" + Jump + ","
        + "UnlockNeed:" + UnlockNeed + ","
        + "}";
    }
}


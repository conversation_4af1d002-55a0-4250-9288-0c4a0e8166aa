package com.dxx.game.consts;

/**
 * 游戏相关常亮
 * <AUTHOR>
 * @date 2019-12-24 11:07
 */
public interface GameConstant {


    /** 阵容类型 章节 */
    int FORMATION_TYPE_CHAPTER = 1;
    int FORMATION_TYPE_ARENA_A = 2;
    int FORMATION_TYPE_ARENA_O = 3;
    int FORMATION_TYPE_TOWER = 4;
    int FORMATION_TYPE_CONQUER_A = 5;
    int FORMATION_TYPE_CONQUER_O = 6;
    int FORMATION_TYPE_GUILD_RACE = 7;
    int FORMATION_TYPE_GUILD_BOSS = 10;

    /** ABTest */
    int AB_A = 1;
    int AB_B = 2;

    /** 抽英雄 */
    int GACHA_ID_HERO = 1;
    /** 抽装备 */
    int GACHA_ID_EQUIP = 2;
    /** 抽遗物 */
    int GACHA_ID_RELIC = 3;
}

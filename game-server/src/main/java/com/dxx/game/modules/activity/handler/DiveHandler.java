package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.DiveProto;
import com.dxx.game.modules.activity.service.DiveService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 潜水
 */
@ApiHandler
public class DiveHandler {
    @Autowired
    private DiveService diveService;

    @ApiMethod(command = MsgReqCommand.DiveOnOpenRequest, name = "潜水活动-打开界面调用")
    public Result<DiveProto.DiveOnOpenResponse> getInfo(Message msg) {
        DiveProto.DiveOnOpenRequest params = (DiveProto.DiveOnOpenRequest) msg;
        return diveService.onOpen(params);
    }

    @ApiMethod(command = MsgReqCommand.DiveBuyItemRequest, name = "潜水活动-购买潜水道具")
    public Result<DiveProto.DiveBuyItemResponse> buyItem(Message msg) {
        DiveProto.DiveBuyItemRequest params = (DiveProto.DiveBuyItemRequest) msg;
        return diveService.buyItem(params);
    }

    @ApiMethod(command = MsgReqCommand.DiveAccRewardRequest, name = "潜水活动-领取累计奖励")
    public Result<DiveProto.DiveAccRewardResponse> accReward(Message msg) {
        DiveProto.DiveAccRewardRequest params = (DiveProto.DiveAccRewardRequest) msg;
        return diveService.accReward(params);
    }

    @ApiMethod(command = MsgReqCommand.DiveShineRequest, name = "潜水活动-使用电光水母照亮")
    public Result<DiveProto.DiveShineResponse> shine(Message msg) {
        DiveProto.DiveShineRequest params = (DiveProto.DiveShineRequest) msg;
        return diveService.shine(params);
    }

    @ApiMethod(command = MsgReqCommand.DiveUsePropRequest, name = "潜水活动-使用手电筒")
    public Result<DiveProto.DiveUsePropResponse> shineItem(Message msg) {
        DiveProto.DiveUsePropRequest params = (DiveProto.DiveUsePropRequest) msg;
        return diveService.shineItem(params);
    }

    @ApiMethod(command = MsgReqCommand.DiveAllAccRewardRequest, name = "潜水活动-一键领取累计奖励")
    public Result<DiveProto.DiveAllAccRewardResponse> allAcc(Message msg) {
        DiveProto.DiveAllAccRewardRequest params = (DiveProto.DiveAllAccRewardRequest) msg;
        return diveService.allAcc(params);
    }

}

package com.dxx.game.modules.develop.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.*;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.*;
import com.dxx.game.dao.dynamodb.model.activity.Flip;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildMessage;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.*;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildMessageDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.dynamodb.repository.guild.opensearch.GuildOpenSearchDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.guild.GuildBossRedisDao;
import com.dxx.game.dto.CommonProto.CommonParams;
import com.dxx.game.dto.DevelopProto.*;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.develop.service.DevelopService;
import com.dxx.game.modules.guild.service.GuildService;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.task.service.TaskService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.primitives.Ints;
import io.github.faketime.FakeTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DevelopServiceImpl implements DevelopService {

    @Value("${spring.application.env}")
    private String applicationEnv;

    @Resource
    private RewardService rewardService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserService userService;
    @Resource
    private CommonService commonService;
    @Resource
    private RedisService redisService;
    @Resource
    private UserDao userDao;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private EquipDao equipDao;
    @Resource
    private ItemDao itemDao;
    @Resource
    private ShopDao shopDao;
    @Resource
    private TaskDao taskDao;
    @Autowired
    private PayService payService;
    //	@Autowired
//	private MailService mailService;
    @Resource
    private GuildOpenSearchDao guildOpenSearchDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GuildService guildService;
    @Resource
    private LogResourceDao logResourceDao;
    @Resource
    private GuildBossRedisDao guildBossRedisDao;
    @Resource
    private GuildBossService guildBossService;
    @Resource
    private GuildMessageDao guildMessageDao;

    @Autowired
    private MailService mailService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private SevenDayTaskService sevenDayTaskService;
    @Autowired
    private HeroService heroService;
    @Autowired
    private HeroDao heroDao;
    @Autowired
    private AccountDao accountDao;
    @Resource
    private ActivityBaseDao.EventsDao eventsDao;
    @Resource
    private ActivityBaseDao.FlipDao flipDao;
    @DynamoDBTransactional
    @Override
    public Result<DevelopLoginResponse> login(DevelopLoginRequest params) {
        if (!this.checkValid()) {
            return Result.Error(ErrorCode.FORMAT_ERROR);
        }

        long userId = params.getUserId();
        int type = params.getType();


        User user = userDao.getByUserId(userId);

        if (user == null) {
            return Result.Error(ErrorCode.USER_LOGIN_FAILED);
        }

        DevelopLoginResponse.Builder result = DevelopLoginResponse.newBuilder();
        String accessToken = userService.createAccessToken(user);

        CommonParams.Builder cp = CommonParams.newBuilder();
        if (user.getAccountId() != null) {
            cp.setAccountId(user.getAccountId());
            cp.setDeviceId(user.getAccountId());
        }
        if(user.getDeviceId() != null) {
            cp.setDeviceId(user.getDeviceId());
        }

        cp.setAccessToken(accessToken);
        cp.setVersion(0);
        cp.setServerId(user.getServerId());

        result.setUserId(user.getUserId());

//        Long dbId = (0x00000000000000FFL & (user.getUserId() >> 56));
//        Long nDbTableIndex = ((((0x00FFFF0000000000L) & user.getUserId()) << 8) >> 48);

        GuildUser guildUser = guildUserDao.getByUserId(userId);
        Guild guild = null;
        if (guildUser != null && guildUser.getGuildId() > 0) {
            guild = guildDao.getByGuildId(guildUser.getGuildId());
        }
        Shop shop = shopDao.getByUserId(userId);
        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("nowServerTime", DateUtils.stampToDate(DateUtils.getUnixTime()));
//		extraInfo.put("totalRechargeAmount", shop.getTotalRechargeAmount());
        extraInfo.put("loginDays", user.getLoginDays());
        extraInfo.put("guildUser", guildUser);
        extraInfo.put("guild", guild);

        UserExtend userExtend = userExtendDao.getByUserId(userId);

        // 服务器保存的批量添加道具的配置
//		Map<Object, Object> addItemsBatchInfo = redisService.hGetAll("DEV_ADD_ITEMS_BATCH_INFO");
        result.setCommonParams(cp);
        result.setDbIdx(0);
        result.setTableIdx(0);
        result.setCoins(user.getCoins());
        result.setDiamonds(user.getDiamonds());
        result.setLoginType(type);
        result.setLevel(user.getLevel());
        result.setExp(user.getExp());
        result.setMissionId(userExtend.getChapterId());
        result.setExtra(JSONObject.toJSONString(extraInfo, SerializerFeature.WriteNonStringKeyAsString));
        result.setMissionId(userExtend.getChapterId());

        return Result.Success(result.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<DevelopChangeResourceResponse> change(DevelopChangeResourceRequest params) {

        if (!this.checkValid()) {
            return Result.Error(ErrorCode.FORMAT_ERROR);
        }
        if (RequestContext.getUserId() == null) {
            return Result.Error(ErrorCode.USER_LOGIN_FAILED);
        }
        DevelopChangeResourceResponse.Builder result = DevelopChangeResourceResponse.newBuilder();
        int resType = params.getResType();
        int resNum = params.getResNum();
        if(resNum < 0) {
            return Result.Error(ErrorCode.COUNT_GT_ZERO);
        }

        int itemId = params.getItemId();
        long userId = RequestContext.getUserId();
        String otherData = params.getOtherData();
        User user = userDao.getByUserId(userId);
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        if (resType == 1) {
            // 修改金币
            user.setCoins((long) resNum);
            userDao.update(user);
        } else if (resType == 2) {
            // 修改钻石
            user.setDiamonds((long) resNum);
            userDao.update(user);
        } else if (resType == 3) {
            // 添加道具
            List<List<Integer>> rewardsConfig = new ArrayList<>();

            List<Integer> config = new ArrayList<>();
            config.add(gameConfigManager.getItemConfig().getItemEntity(itemId).getItemType());
            config.add(itemId);
            config.add(resNum);
            int equipLevel = Integer.parseInt(otherData);
            if (equipLevel > 0) {
                config.add(equipLevel);
            }
            rewardsConfig.add(config);

            RewardResultSet resultSet = rewardService.executeRewards(RequestContext.getUserId(), rewardsConfig);
            result.setCommonData(CommonHelper.buildCommonData(resultSet));
        } else if (resType == 4) {
            // 修改当前等级
            user.setLevel((short) resNum);
            userDao.update(user);
        } else if (resType == 5) {
            // 删除账号
            Account account = accountDao.getItem(user.getAccountKey());
            if (!StringUtils.isEmpty(account.getDeviceId())) {
                account.setDeviceId("999-" + account.getDeviceId());
            }
            if (!StringUtils.isEmpty(account.getAccountId())) {
                account.setAccountId("999-" + account.getAccountId());
            }
            accountDao.update(account);
        } else if (resType == 6) {
            user.setExp(resNum);
            userDao.update(user);
        } else if (resType == 7) {
            // 删除所有数据
//			List<User> users = userDao.getAll();
//			userDao.batchDeleteNow(users);
//			user.setDeviceId(user.getDeviceId() + "." + System.currentTimeMillis());
//			userDao.update(user);
        } else if (resType == 8) {
        } else if (resType == 9) {
            // 修改掩码
            user.setSystemMask(otherData);
            userDao.update(user);
        } else if (resType == 10) {
            // 删除装备
            List<Equip> equipList = equipDao.getAllByUserId(userId);
            for (Equip equip : equipList) {
                if (equip.getHeroRowId() != null && equip.getHeroRowId() > 0) {
                    equipDao.delete(equip);
                }
            }
        } else if (resType == 11) {
            // 删除道具
            List<Item> itemList = itemDao.getAllByUserId(userId);
            for (Item item : itemList) {
                itemDao.delete(item);
            }
        } else if (resType == 12) {
            Shop shop = shopDao.getByUserId(userId);
//			shop.setSmallBoxAdTimestamp((long) resNum);
            shopDao.update(shop);
        } else if (resType == 13) {
            Shop shop = shopDao.getByUserId(userId);
//			shop.setLargeBoxAdTimestamp((long) resNum);
            shopDao.update(shop);
        } else if (resType == 14) {
//			Shop shop = shopDao.getByUserId(userId);
//			shop.setNormalBoxCount(resNum);
//			shopDao.update(shop);
        } else if (resType == 15) {
            Shop shop = shopDao.getByUserId(userId);
//			shop.setRefreshTimestamp((long)resNum);
            shopDao.update(shop);
        } else if (resType == 20) {
            user.setLoginTimestamp((long) resNum);
            userDao.update(user);
        } else if (resType == 21) {
            user.setLoginDays(resNum);
            userDao.update(user);
            sevenDayTaskService.resetCheckDayTM(userId, resNum);
        } else if (resType == 22) {
            Shop shop = shopDao.getByUserId(userId);
//			shop.setTotalRechargeAmount(Double.parseDouble(otherData));
            shopDao.update(shop);
        } else if (resType == 23) {
            userExtend.setChapterId(resNum);
            userExtendDao.update(userExtend);
            //任务
//            TaskProcess achieveChapter = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_MISSION_CHAPTER, resNum);
//            taskService.updateTask(userId,achieveChapter);
            // 新手7日活动任务
//            SevenDayTaskProcess sevenDayMission = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_MISSION_CHAPTER, resNum);
//            sevenDayTaskService.updateTask(userId,sevenDayMission);

        } else if (resType == 24) {

        } else if (resType == 25) {
            int modelId = resNum;
            List<Integer> openModelId = userExtend.getOpenModelId();
            List<Integer> temp = openModelId.stream().filter(value -> value != modelId).collect(Collectors.toList());
            temp.add(modelId);
            userExtend.setOpenModelId(temp);
            userExtendDao.update(userExtend);
        } else if (resType == 101) {
            // 清空 日常&周常 活跃度领取记录
            Task task = taskDao.getByUserId(userId);
            task.setWeeklyReward(0L);
            task.setDailyReward(0L);
            taskDao.update(task);

        } else if (resType == 102) {
            // 修改日常活跃度：
            Task task = taskDao.getByUserId(userId);
            task.setDailyActive((short) resNum);
            taskDao.update(task);
        } else if (resType == 103) {
            // 日常时间戳
            Task task = taskDao.getByUserId(userId);
            task.setDailyTime((long) resNum);
            taskDao.update(task);

        } else if (resType == 104) {
            // 修改周常活跃度：
            Task task = taskDao.getByUserId(userId);
            task.setWeeklyActive((short) resNum);
            taskDao.update(task);

        } else if (resType == 105) {
            // 周常任务时间戳
            Task task = taskDao.getByUserId(userId);
            task.setWeeklyTime((long) resNum);
            taskDao.update(task);
        } else if (resType == 106) {
            // 重置所有任务数据
            Task task = taskDao.getByUserId(userId);
            taskDao.deleteNow(task);
        } else if (resType == 107) {
            // 快速完成每日任务
            taskService.quickFinishDayTask(userId, resNum, Ints.tryParse(otherData));
        } else if (resType == 108) {
            // 快速完成成就
            taskService.quickFinishAchieveTask(userId, resNum, Ints.tryParse(otherData));
        } else if (resType == 121) {
            // 修改七日任务活跃度
            sevenDayTaskService.updateActive(userId, resNum);
        } else if (resType == 122) {
            // 快速完成七日任务
            sevenDayTaskService.quickFinishSevenDayTask(userId, resNum, Ints.tryParse(otherData));

        } else if (resType == 300001) {
            List<Guild> guilds = guildOpenSearchDao.queryAllGuilds();
            for (Guild guild : guilds) {
                List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guild.getGuildId());
                for (GuildUser guildUser : guildUsers) {
                    guildUserDao.delete(guildUser);
                }
                guildDao.delete(guild);
            }
            guildOpenSearchDao.deleteIndex();
            guildOpenSearchDao.createIndex();
        } else if (resType == 300002) {
            int createNum = Math.min(resNum, 20);
            guildService.devCreateRandomGuild(createNum, user.getServerId());

        } else if (resType == 300003) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                int maxLevel = gameConfigManager.getGuildConfig().getGuildLevel().size();
                int changeLevel = Integer.parseInt(otherData);
                if (guild != null && changeLevel <= maxLevel) {
                    guild.setGuildLevel(changeLevel);
                    guildDao.updateGuildLevel(guild);

                    int maxMembersCount = gameConfigManager.getGuildConfig().getGuildLevelEntity(changeLevel).getMaxMemberCount();
                    guild.setGuildMaxMembersCount(maxMembersCount);
                    guildDao.updateGuildMaxMembersCount(guild);
                }
            }
        } else if (resType == 300004) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                int changeActive = Integer.parseInt(otherData);
                if (guild != null) {
                    guild.setGuildDayActive(changeActive);
                    guild.setGuildDayActiveTime(DateUtils.getUnixTime());
                    guildDao.updateGuildDayActive(guild);
                }
            }
        } else if (resType == 300005) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null) {
                int changeActive = Integer.parseInt(otherData);
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild != null) {
                    guild.setGuildActive(changeActive);
                    guildDao.updateGuildActive(guild);
                }
            }
        } else if (resType == 300006) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null) {
                int changeExp = Integer.parseInt(otherData);
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild != null) {
                    guild.setGuildExp(changeExp);
                    guildDao.updateGuildExp(guild);
                }
            }
        } else if (resType == 300007) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                List<GuildMessage> guildMessageList = guildMessageDao.getMessageListByGuildId(guildUser.getGuildId());
                for (GuildMessage guildMessage : guildMessageList) {
                    if (guildMessage.getType() == 1 || guildMessage.getType() == 2) {
                        guildMessageDao.delete(guildMessage);
                    }
                }
            }

        } else if (resType == 310001) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                Map<Integer, GuildUser.GuildTaskModel> values = JSONObject.parseObject(otherData, new TypeReference<Map<Integer, GuildUser.GuildTaskModel>>() {
                });
                guildUser.setTasks(values);
                guildUserDao.updateGuildTask(guildUser);
            }
        } else if (resType == 310002) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                Map<Integer, GuildUser.GuildShopModel> values = JSONObject.parseObject(otherData, new TypeReference<Map<Integer, GuildUser.GuildShopModel>>() {
                });
                guildUser.setDailyShop(values);
                guildUserDao.updateDailyShop(guildUser);
            }
        } else if (resType == 310003) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                Map<Integer, GuildUser.GuildShopModel> values = JSONObject.parseObject(otherData, new TypeReference<Map<Integer, GuildUser.GuildShopModel>>() {
                });
                guildUser.setWeeklyShop(values);
                guildUserDao.updateWeeklyShop(guildUser);
            }
        } else if (resType == 310004) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                guildUser.setSignInCnt(Integer.parseInt(otherData));
                guildUserDao.updateSignIn(guildUser);
            }
        } else if (resType == 310005) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                guildUser.setDailyTM(Long.parseLong(otherData));
                guildUserDao.updateDailyTM(guildUser);
                if (guildUser.getGuildBossModel() != null) {
                    guildUser.getGuildBossModel().setDailyTime(Long.parseLong(otherData));
                    guildUserDao.updateGuildBossModel(guildUser);
                }
            }
        } else if (resType == 310006) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                guildUser.setWeeklyTM(Long.parseLong(otherData));
                guildUserDao.updateWeeklyTM(guildUser);
                if (guildUser.getGuildBossModel() != null) {
                    guildUser.getGuildBossModel().setWeeklyTime(Long.parseLong(otherData));
                    guildUserDao.updateGuildBossModel(guildUser);
                }
            }
        } else if (resType == 310007) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser != null && guildUser.getGuildId() > 0) {
                if (StringUtils.isEmpty(otherData)) {
                    guildUser.setGuildBossModel(null);
                    guildUserDao.update(guildUser);
                } else {
                    GuildUser.GuildBossModel model = JSONObject.parseObject(otherData, GuildUser.GuildBossModel.class);
                    guildUser.setGuildBossModel(model);
                    guildUserDao.updateGuildBossModel(guildUser);
                }
            }
        } else if (resType == 310008) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser.getGuildId() > 0) {
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild != null) {
                    guildBossRedisDao.addDamage(guild, Long.parseLong(otherData));
                }
            }
        } else if (resType == 310009) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
//            if (guildUser.getGuildId() > 0) {
//                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
//                guildBossRedisDao.removeDamage(guild);
//                guild.setGuildBossTime(0L);
//                guildBossService.checkBossData(guild);
//                List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guild.getGuildId());
//                for (GuildUser gu : guildUsers) {
//                    guildBossService.checkUserData(guild, gu);
//                }
//            }

            guildUser.getGuildBossModel().setGuildBossRewardTime(0);
            guildUserDao.update(guildUser);
        } else if (resType == 310010) {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            guildUser.setTaskRefreshCount(Integer.parseInt(otherData));
            guildUserDao.updateGuildTaskRefreshCount(guildUser);
        } else if (resType == 1001) {
            int num = Integer.parseInt(otherData);

            Hero hero = heroService.getHero(userId, resNum);
            if(hero != null) {
                hero.setLevel(num);
                heroDao.update(hero);
            }
        } else if (resType == 1002) {
            int num = Integer.parseInt(otherData);

            Hero hero = heroService.getHero(userId, resNum);
            if(hero != null) {
                hero.setAdvance(num);
                heroDao.update(hero);
            }
        } else if (resType == 1003) {
            int num = Integer.parseInt(otherData);

            Hero hero = heroService.getHero(userId, resNum);
            if(hero != null) {
                hero.setStar(num);
                heroDao.update(hero);
            }
        } else if (resType == 11001) {
            user.setGuideMask(0L);
            userDao.updateGuideMask(user);
        } else if (resType == 7701) {
            // 翻牌子
            Flip flip = flipDao.getByUserId(userId);

            if (StringUtils.isEmpty(otherData)) {
                flip.setFlip(null);
            } else {
                Flip data = JSONObject.parseObject(otherData, Flip.class);
                flip = data;
            }
            flipDao.update(flip);
        }

        return Result.Success(result.build());
    }

    /**
     * 是否是开发环境
     *
     * @return
     */
    private boolean checkValid() {
        return !gameConfigManager.isProd() && !gameConfigManager.isPre();
    }

    @DynamoDBTransactional
    @Override
    public Result<DevelopToolsResponse> tools(DevelopToolsRequest params) {
        if (!this.checkValid()) {
            return Result.Error(ErrorCode.FORMAT_ERROR);
        }
        if (RequestContext.getUserId() == null) {
            return Result.Error(ErrorCode.USER_LOGIN_FAILED);
        }
        long userId = RequestContext.getUserId();
        String content = params.getParams();
        JSONObject jsonObj = (JSONObject) JSONObject.parse(content);

        int type = jsonObj.getIntValue("type");

        DevelopToolsResponse.Builder response = DevelopToolsResponse.newBuilder();
        try {
            if (type == 0) {
                long timestamp = jsonObj.getLongValue("timestamp");
                if (timestamp > 0) {
                    timestamp = timestamp * 1000;
                    FakeTime.restoreReal();
                    long offset = timestamp - System.currentTimeMillis();
                    FakeTime.offsetRealBy(offset);
                } else {
                    FakeTime.restoreReal();
                }
                response.setRespData(DateUtils.stampToDate(DateUtils.getUnixTime()));
            } else if (type == 1) {
                // 解析userId
                long parseUserId = Long.parseLong(jsonObj.getString("userId"));
                Long dbId = (0x00000000000000FFL & (parseUserId >> 56));
                Long nDbTableIndex = ((((0x00FFFF0000000000L) & parseUserId) << 8) >> 48);
                byte[] bytes = BitConverter.getBytes(parseUserId);
                bytes[5] = 0;
                bytes[6] = 0;
                bytes[7] = 0;
                long accountId = BitConverter.toLong(bytes, 0);
                Map<String, Object> respData = new HashMap<String, Object>();
                respData.put("dbId", dbId);
                respData.put("nDbTableIndex", nDbTableIndex);
                respData.put("accountId", accountId);
                response.setRespData(JSONObject.toJSONString(respData));
            } else if (type == 2) {
                // 新增邮件
                String emailTempId = jsonObj.getString("emailTempId");
                // 读取参数
                String paramsStr = jsonObj.getString("params");
                Map<String, String> mailParams = JSONObject.parseObject(paramsStr, new TypeReference<Map<String, String>>() {
                });


                String items = jsonObj.getString("items");
                Map<Integer, Integer> itemsMap = JSONObject.parseObject(items, new TypeReference<Map<Integer, Integer>>() {
                });
                List<List<Integer>> config = new ArrayList<List<Integer>>(itemsMap.size());
                for (Map.Entry<Integer, Integer> entry : itemsMap.entrySet()) {
                    int itemType = gameConfigManager.getItemConfig().getItem().get(entry.getKey()).getItemType();
//				int itemType = 1;
                    List<Integer> c = new ArrayList<>(3);
                    c.add(itemType);
                    c.add(entry.getKey());
                    c.add(entry.getValue());
                    config.add(c);
                }

                String uniqueId = UUID.randomUUID().toString();
                mailService.createMail(userId, emailTempId, uniqueId, mailParams, config);
            } else if (type == 3) {
                // 解析 ulong 类型的掩码
                String maskValue = jsonObj.getString("mask");
                BigInteger mask = new BigInteger(maskValue);

                Map<String, Boolean> resMap = new HashMap<>();
                for (int i = 0; i <= 128; i ++) {
                    resMap.put(i + "", MaskUtil.isTrue(mask, i));
                }
                response.setRespData(JSONObject.toJSONString(resMap));

            } else if (type == 4) {
                // 生成掩码的值
                String mask = jsonObj.getString("maskMap");
                Map<Integer, Integer> maskMap = JSONObject.parseObject(mask, new TypeReference<Map<Integer, Integer>>(){});

                BigInteger makeMask = BigInteger.valueOf(0L);
                for (Map.Entry<Integer, Integer> entry : maskMap.entrySet()) {
                    if (entry.getValue() == 1) {
                        makeMask = MaskUtil.setMask(makeMask, entry.getKey());
                    }
                }

                response.setRespData(makeMask.toString());
            } else if (type == 5) {
                List<Map<String, Object>> result = new ArrayList<>();
                List<LogResource> logResourceList = logResourceDao.queryModify(userId);
                for (LogResource logResource : logResourceList) {
                    Map<String, Object> values = new HashMap<>();
                    values.put("transId", logResource.getTransId());
                    values.put("logTime", DateUtils.stampToDate(logResource.getLogTime()));
                    values.put("command", logResource.getCommand());
                    values.put("customType", logResource.getCustomType());
                    values.put("extra", logResource.getExtra());

//				logResource.getItems()
                    List<Map<String, Object>> items = new ArrayList<>();
                    List<List<Long>> logItems = logResource.getItems();
                    for (List<Long> logItem : logItems) {
                        String itemName = gameConfigManager.getItemConfig().getItemEntity(logItem.get(0).intValue()).getNote();
                        Map<String, Object> item = new HashMap<>();
                        item.put("name", itemName);
                        item.put("itemId", logItem.get(0));
                        item.put("num", logItem.get(1));
                        item.put("total", logItem.get(2));

                        items.add(item);
                    }
                    values.put("items", items);

                    result.add(values);
                }

                response.setRespData(JSONObject.toJSONString(result));
            } else if (type == 6) {
                response.setRespData(String.valueOf(userService.getMaxTransId(userId)));
            }
            return Result.Success(response.build());
        } catch (Exception e) {
            return Result.Error(ErrorCode.SERVER_SYSTEM_ERROR);
        }
    }
}















package com.dxx.game.modules.activity.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.eventflipcard.FlipBaseEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.ActivityType;
import com.dxx.game.consts.MailSourceType;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.Item;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.activity.Flip;
import com.dxx.game.dao.dynamodb.repository.ItemDao;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dto.FlipProto;
import com.dxx.game.modules.activity.base.ActivityLifeCycle;
import com.dxx.game.modules.activity.base.ActivityManager;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.dxx.game.modules.activity.consts.FlipGridType;
import com.dxx.game.modules.activity.service.FlipService;
import com.dxx.game.modules.activity.support.FlipSupport;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FlipServiceImpl implements FlipService, ActivityLifeCycle<Flip, FlipProto.FlipDto> {
    @Resource
    private FlipSupport flipSupport;
    @Resource
    private RewardService rewardService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private DropService dropService;
    @Resource
    private ItemDao itemDao;
    @Resource
    private MailService mailService;
    @Resource
    private UserService userService;
    @Resource
    private ActivityBaseDao.FlipDao flipDao;
    @Resource
    private RedisLock redisLock;

    @Override
    public ActivityMetaData<Flip, FlipProto.FlipDto> buildMetaData() {
        return ActivityMetaData.<Flip, FlipProto.FlipDto>valueOf(ActivityType.FLIP).withDao(flipDao);
    }

    @Override
    public void initializeModel(long userId, Flip model) {

        Flip.FlipModel flipModel = new Flip.FlipModel();

        flipModel.setAcc(Lists.newArrayList());
        flipModel.setClue(0);

        model.setFlip(flipModel);
        // 创建地图
        flipSupport.createMap(null, model, true, Collections.emptyMap());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipOnOpenResponse> onOpen(FlipProto.FlipOnOpenRequest params) {
        long userId = RequestContext.getUserId();
        User user = RequestContext.getUser();


        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.ACTIVITY_NOT_OPEN);
        }
        FlipBaseEntity baseEntity = flipSupport.getBaseEntity(flip.getActivityId());
        if(baseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        List<List<Integer>> rewards = Lists.newArrayList();

        if (flip.getIsFirst()) {
            flip.setIsFirst(false);
            rewards.add(Lists.newArrayList(Lists.newArrayList(baseEntity.getStepItem(), baseEntity.getStepDefault())));
            updateModel(flip);
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
        }

        FlipProto.FlipOnOpenResponse.Builder response = FlipProto.FlipOnOpenResponse.newBuilder();

        if(rewardResultSet != null) {
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        }

        response.setFlipDto(buildDto(flip));

        return Result.Success(response.build());
    }


    @Override
    public FlipProto.FlipDto buildDto(Flip model) {
        int activityId = model.getActivityId();

        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(activityId);

        FlipProto.FlipDto.Builder result = FlipProto.FlipDto.newBuilder();
        result.setActivityId(activityId);
        result.setClueNum(model.getFlip().getClue());

        // 累计奖励
        result.addAllAccRewards(flipSupport.buildAccRewardList(model, flipBaseEntity));

        // 配置
        result.setConfig(flipSupport.buildConfigDto(flipBaseEntity));

        // 地图
        result.addAllGrids(flipSupport.buildGridList(model));

        return result.build();
    }

    @Override
    public void onActivityEnd(Flip model) {
        this.exchangeMail(model.getUserId(), model);
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipAccRewardResponse> accReward(FlipProto.FlipAccRewardRequest params) {
        long userId = RequestContext.getUserId();

        Integer need = Integer.valueOf(params.getNeed());
        if (need <= 0) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        if (model.getAcc().contains(need)) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }

        if (need > model.getClue()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int eventId = flip.getActivityId();
        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(eventId);
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        int dropId = 0;
        for (List<Integer> list : flipBaseEntity.getBigReward()) {
            int configNeed = list.get(0);
            if (configNeed == need) {
                dropId = list.get(1);
                break;
            }
        }

        if (dropId <= 0) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        List<List<Integer>> rewards = dropService.dropRewardsConfig(dropId);
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        model.getAcc().add(need);
        updateModel(flip);

        FlipProto.FlipAccRewardResponse.Builder response = FlipProto.FlipAccRewardResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setClueNum(model.getClue());

        List<FlipProto.AccClueRewardDto> accRewardDtos = flipSupport.buildAccRewardList(flip, flipBaseEntity);
        response.addAllAccRewards(accRewardDtos);

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipBuyStepResponse> buyStep(FlipProto.FlipBuyStepRequest params) {
        long userId = RequestContext.getUserId();

        Integer buyNum = params.getBuyNum();
        if (buyNum <= 0 || buyNum >= 500) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(flip.getActivityId());
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        List<List<Integer>> rewards = Lists.newArrayList();
        rewards.add(Lists.newArrayList(flipBaseEntity.getStepItem(), buyNum));
        rewards.add(Lists.newArrayList(RewardResourceType.DIAMONDS.getValue(), -buyNum * flipBaseEntity.getStepPrice()));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        FlipProto.FlipBuyStepResponse.Builder resp = FlipProto.FlipBuyStepResponse.newBuilder();
        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipShowGridResponse> showGrid(FlipProto.FlipShowGridRequest params) {
        long userId = RequestContext.getUserId();

        boolean isItem = params.getIsItem();
        int index = params.getIndex();
        if (!flipSupport.isValidIndex(index)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(flip.getActivityId());
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        FlipProto.FlipShowGridResponse.Builder resp = FlipProto.FlipShowGridResponse.newBuilder();
        FlipProto.FlipGridDto.Builder gridBuilder = FlipProto.FlipGridDto.newBuilder();

        List<List<Integer>> rewards = Lists.newArrayList();

        // 全开道具或步数
        int costItemId = isItem ? flipBaseEntity.getAllShowItem() : flipBaseEntity.getStepItem();
        int costCount = 1;
        Item costItem = itemDao.getByItemId(userId, costItemId);
        if (costItem.getCount() < costCount) {
            return Result.Error(ErrorCode.ITEM_IS_NOT_ENOUGH);
        }
        rewards.add(Lists.newArrayList(costItemId, -costCount));

        if (!isItem) {
            model.setAccSteps(model.getAccSteps() + costCount);
            // 目标格子
            Flip.Grid targetGrid = model.getMap().get(index);
            if (!targetGrid.isShow()) {
                return Result.Error(ErrorCode.FLIP_GRID_UN_SHOW);
            }

            FlipGridType targetGridType = FlipGridType.getFlipGridByType(targetGrid.getType());

            // 普通格子
            if (targetGridType.isNormalGrid()) {
                // 收集一个石头
                rewards.add(Lists.newArrayList(targetGridType.getItemId(flipBaseEntity), 1));
                targetGrid.setStatus(Flip.Grid.STATUS_ACTIVE);

                // 周围8个方向的格子都打开
                List<Integer> aroundIndexs = flipSupport.getAroundGrids(index);
                for (int aroundIndex : aroundIndexs) {
                    Flip.Grid grid = model.getMap().get(aroundIndex);
                    if (showGird(grid)) {
                        resp.addShowGrids(flipSupport.buildGrid(grid, gridBuilder));
                    }
                }
            } else if (targetGridType.isAllShowGrid()) {
                //全开格子
                activeGird(targetGrid);

                List<Flip.Grid> showGirds = allShowGirds(flip);
                for (Flip.Grid grid : showGirds) {
                    resp.addShowGrids(flipSupport.buildGrid(grid, gridBuilder));
                }
            } else if (targetGridType.isPartShowGrid()) {
                //部分开格子
                activeGird(targetGrid);

                List<Flip.Grid> selectList = partShowGirds(flip);
                for (Flip.Grid grid : selectList) {
                    resp.addShowGrids(flipSupport.buildGrid(grid, gridBuilder));
                }
            } else {
                return Result.Error(ErrorCode.FLIP_SHOW_GRID_TYPE_ERROR);
            }
        }

        // 全开道具
        if (isItem) {
            List<Flip.Grid> showGirds = allShowGirds(flip);
            for (Flip.Grid grid : showGirds) {
                resp.addShowGrids(flipSupport.buildGrid(grid, gridBuilder));
            }
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        Flip.Grid impasseGrid = checkImpasse(flip);
        if (null != impasseGrid) {
            resp.addShowGrids(flipSupport.buildGrid(impasseGrid, gridBuilder));
        }

        updateModel(flip);

        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        resp.addAllMapGrids(flipSupport.buildGridList(flip));
        resp.setAccSteps(model.getAccSteps());

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipRewardGridResponse> rewardGrid(FlipProto.FlipRewardGridRequest params) {
        long userId = RequestContext.getUserId();

        int index = params.getIndex();
        if (!flipSupport.isValidIndex(index)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(flip.getActivityId());
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        // 目标格子
        Flip.Grid targetGrid = model.getMap().get(index);
        if (!targetGrid.isShow()) {
            return Result.Error(ErrorCode.FLIP_GRID_UN_SHOW);
        }

        FlipGridType targetGridType = FlipGridType.getFlipGridByType(targetGrid.getType());
        if (!targetGridType.isStepGrid() && !targetGridType.isSpecialGrid() && !targetGridType.isCollectGrid()) {
            return Result.Error(ErrorCode.FLIP_REWARD_GRID_TYPE_ERROR);
        }

        List<List<Integer>> rewards = Lists.newArrayList();

        if (!targetGridType.isStepGrid()) {
            int costItemId = flipBaseEntity.getStepItem();
            int costCount = 1;
            Item costItem = itemDao.getByItemId(userId, costItemId);
            if (costItem.getCount() < costCount) {
                return Result.Error(ErrorCode.ITEM_IS_NOT_ENOUGH);
            }
            rewards.add(Lists.newArrayList(costItemId, -costCount));
            model.setAccSteps(model.getAccSteps() + costCount);
        }

        FlipProto.FlipRewardGridResponse.Builder resp = FlipProto.FlipRewardGridResponse.newBuilder();

        if (targetGridType.isStepGrid() || targetGridType.isSpecialGrid()) {
            // 周围8个方向的格子都打开
            List<Integer> aroundIndexs = flipSupport.getAroundGrids(index);
            for (int aroundIndex : aroundIndexs) {
                Flip.Grid grid = model.getMap().get(aroundIndex);
                if (showGird(grid)) {
                    resp.addShowGrids(flipSupport.buildGrid(grid, null));
                }
            }
        }

        // 收集格子
        if (targetGridType.isCollectGrid()) {
            FlipGridType collectType = targetGridType.getCollectType();
            for (Flip.Grid grid : model.getMap()) {
                FlipGridType gridType = grid.getFlipGridType();
                if (grid.isShow() && Objects.equals(collectType, gridType)) {
                    activeGird(grid);
                    // 收集一个石头
                    rewards.add(Lists.newArrayList(gridType.getItemId(flipBaseEntity), 1));

                    resp.addActiveGrids(flipSupport.buildGrid(grid, null));
                }
            }
        }

        activeGird(targetGrid);


        if (targetGrid.getRewards() != null) {
            rewards.add(targetGrid.getRewards());
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
        }

        // 检查死局
        Flip.Grid impasseShowGrid = checkImpasse(flip);
        if (null != impasseShowGrid) {
            resp.setImpasseShowGrid(flipSupport.buildGrid(impasseShowGrid, null));
        }

        updateModel(flip);

        resp.setCommonData(rewardResultSet == null ? CommonHelper.buildCommonData() : CommonHelper.buildCommonData(rewardResultSet));

        resp.addAllMapGrids(flipSupport.buildGridList(flip));
        resp.setAccSteps(model.getAccSteps());

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipClueGridResponse> clueGrid(FlipProto.FlipClueGridRequest params) {
        long userId = RequestContext.getUserId();

        int index = params.getIndex();
        if (!flipSupport.isValidIndex(index)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(flip.getActivityId());
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        // 目标格子
        Flip.Grid targetGrid = model.getMap().get(index);
        if (!targetGrid.isShow()) {
            return Result.Error(ErrorCode.FLIP_GRID_UN_SHOW);
        }

        FlipGridType targetGridType = FlipGridType.getFlipGridByType(targetGrid.getType());
        if (!targetGridType.isClueGrid()) {
            return Result.Error(ErrorCode.FLIP_CLUE_GRID_TYPE_ERROR);
        }

        List<List<Integer>> rewards = Lists.newArrayList();

        FlipProto.FlipClueGridResponse.Builder resp = FlipProto.FlipClueGridResponse.newBuilder();

        rewards.add(Lists.newArrayList(flipBaseEntity.getClueItem(), 1));
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        // 旧地图都翻开
        allShowGirds(flip);
        resp.addAllOldMapGrids(flipSupport.buildGridList(flip));

        // 开新地图
        flipSupport.createMap(index, flip, false, getAccStones(userId, flipBaseEntity));
        updateModel(flip);

        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        resp.setClueNum(model.getClue());
        resp.addAllAccRewards(flipSupport.buildAccRewardList(flip, flipBaseEntity));
        resp.addAllMapGrids(flipSupport.buildGridList(flip));
        resp.setHasSpecialRewards(hasSprcialRewards(flip));
        resp.setAccSteps(model.getAccSteps());

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipBombGridResponse> bombGrid(FlipProto.FlipBombGridRequest params) {
        long userId = RequestContext.getUserId();

        int index = params.getIndex();
        if (!flipSupport.isValidIndex(index)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(flip.getActivityId());
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        // 目标格子
        Flip.Grid targetGrid = model.getMap().get(index);
        if (!targetGrid.isShow()) {
            return Result.Error(ErrorCode.FLIP_GRID_UN_SHOW);
        }

        FlipGridType targetGridType = FlipGridType.getFlipGridByType(targetGrid.getType());
        if (!targetGridType.isBombGrid()) {
            return Result.Error(ErrorCode.FLIP_BOMB_GRID_TYPE_ERROR);
        }

        List<List<Integer>> rewards = Lists.newArrayList();

        int costItemId = flipBaseEntity.getStepItem();
        int costCount = 1;
        Item costItem = itemDao.getByItemId(userId, costItemId);
        if (costItem.getCount() < costCount) {
            return Result.Error(ErrorCode.ITEM_IS_NOT_ENOUGH);
        }
        rewards.add(Lists.newArrayList(costItemId, -costCount));
        model.setAccSteps(model.getAccSteps() + costCount);

        FlipProto.FlipBombGridResponse.Builder resp = FlipProto.FlipBombGridResponse.newBuilder();
        FlipProto.FlipGridDto.Builder gridBuilder = FlipProto.FlipGridDto.newBuilder();
        FlipProto.FlipBombGridDto.Builder bombBuilder = FlipProto.FlipBombGridDto.newBuilder();
        FlipProto.FlipBombRewardGridDto.Builder rewardBuilder = FlipProto.FlipBombRewardGridDto.newBuilder();
        FlipProto.FlipBombAllShowGridDto.Builder allShowBuilder = FlipProto.FlipBombAllShowGridDto.newBuilder();
        FlipProto.FlipBombStoneGridDto.Builder stoneBuilder = FlipProto.FlipBombStoneGridDto.newBuilder();
        FlipProto.FlipBombCollectGridDto.Builder collectBuilder = FlipProto.FlipBombCollectGridDto.newBuilder();
        FlipProto.FlipCollectEffectDto.Builder collectEffectBuilder = FlipProto.FlipCollectEffectDto.newBuilder();

        int chainMax = 16;  // 连锁上限
        int fireCount = 1;

        List<Flip.Grid> fireGrids = Lists.newArrayList(targetGrid); // 炸弹格子
        List<Flip.Grid> blowGrids = Lists.newArrayList();           // 被炸格子
        Map<Flip.Grid, List<Flip.Grid>> stoneGrids = Maps.newHashMap(); // 被炸的石头格

        // 1.处理连锁爆炸
        while (fireCount <= chainMax && !fireGrids.isEmpty()) {
            Flip.Grid fireGrid = fireGrids.remove(0);
            activeGird(fireGrid);
            List<Flip.Grid> stones = Lists.newArrayList();
            stoneGrids.put(fireGrid, stones);
            List<Integer> showlist = flipSupport.getBombEffectGrids(fireGrid.getIdx(), fireGrid.getFlipGridType());
            Map<Integer, Flip.Grid> extraShowGrids = Maps.newHashMap();

            bombBuilder.clear();
            bombBuilder.setBombSource(flipSupport.buildGrid(fireGrid, gridBuilder));
            for (int idx : showlist) {
                Flip.Grid grid = model.getMap().get(idx);

                if (blowGrids.contains(grid))
                    continue;

                if (showGird(grid)) {
                    bombBuilder.addShowGrids(flipSupport.buildGrid(grid, gridBuilder));
                }

                List<Integer> extraShowIndexList = flipSupport.getAroundGrids(idx);
                for (int extraShowIndex : extraShowIndexList) {
                    if (extraShowGrids.containsKey(extraShowIndex))
                        continue;
                    if (showlist.contains(extraShowIndex))
                        continue;
                    Flip.Grid extraShowGrid = model.getMap().get(extraShowIndex);
                    extraShowGrids.put(extraShowIndex, extraShowGrid);
                    if (showGird(extraShowGrid)) {
                        bombBuilder.addShowGrids(flipSupport.buildGrid(extraShowGrid, gridBuilder));
                    }
                }

                FlipGridType gridType = grid.getFlipGridType();

                // 炸弹
                if (gridType.isBombGrid()) {
                    if (activeGird(grid)) {
                        fireGrids.add(grid);
                    }
                } else if (gridType.isNormalGrid() && grid.isShow()) {
                    stones.add(grid);
                }

                if (grid.isShow()) {
                    blowGrids.add(grid);
                }
            }

            resp.addBombGrids(bombBuilder.build());
            fireCount++;
        }

        // 2.处理奖励格(6.特殊奖励格子 7.步数格子)
        for (Iterator<Flip.Grid> iterator = blowGrids.iterator(); iterator.hasNext(); ) {
            Flip.Grid grid = iterator.next();
            FlipGridType gridType = grid.getFlipGridType();
            if (!gridType.isStepGrid() && !gridType.isSpecialGrid())
                continue;

            activeGird(grid);
            rewardBuilder.clear();
            rewardBuilder.setRewardSource(flipSupport.buildGrid(grid, gridBuilder));
            rewardBuilder.setRewardDto(CommonHelper.buildRewardDto(grid.getRewards()));
            rewards.add(grid.getRewards());

            resp.addRewardGrids(rewardBuilder.build());

            iterator.remove();
        }

        // 3.处理全开格(11.全开格子 12.部分开格子)
        for (Iterator<Flip.Grid> iterator = blowGrids.iterator(); iterator.hasNext(); ) {
            Flip.Grid grid = iterator.next();
            FlipGridType gridType = grid.getFlipGridType();
            if (!gridType.isAllShowGrid() && !gridType.isPartShowGrid())
                continue;

            activeGird(grid);
            allShowBuilder.clear();
            allShowBuilder.setShowSource(flipSupport.buildGrid(grid, gridBuilder));

            List<Flip.Grid> allShowGrids = gridType.isAllShowGrid() ? allShowGirds(flip) : partShowGirds(flip);
            for (Flip.Grid showGrid : allShowGrids) {
                allShowBuilder.addShowGrids(flipSupport.buildGrid(showGrid, gridBuilder));
            }

            resp.addAllShowGrids(allShowBuilder.build());

            iterator.remove();
        }

        // 4.石头格(1.红 2.蓝 3.绿 4.紫)
        for (Iterator<Flip.Grid> iterator = blowGrids.iterator(); iterator.hasNext(); ) {
            Flip.Grid grid = iterator.next();
            FlipGridType gridType = grid.getFlipGridType();
            if (!gridType.isNormalGrid())
                continue;

            activeGird(grid);
            stoneBuilder.clear();

            for (Map.Entry<Flip.Grid, List<Flip.Grid>> entry : stoneGrids.entrySet()) {
                Flip.Grid source = entry.getKey();
                if (entry.getValue().contains(grid)) {
                    stoneBuilder.setSource(flipSupport.buildGrid(source, gridBuilder));
                    break;
                }
            }

            List<Integer> stoneReward = Lists.newArrayList(gridType.getItemId(flipBaseEntity), 1);

            stoneBuilder.setSelf(flipSupport.buildGrid(grid, gridBuilder));
            stoneBuilder.setRewardDto(CommonHelper.buildRewardDto(stoneReward));
            rewards.add(stoneReward);

            resp.addStoneGrids(stoneBuilder.build());

            iterator.remove();
        }

        // 5.收集格(13.收集红 14.收集蓝 15.收集绿 16.收集紫)
        for (Iterator<Flip.Grid> iterator = blowGrids.iterator(); iterator.hasNext(); ) {
            Flip.Grid grid = iterator.next();
            FlipGridType gridType = grid.getFlipGridType();
            if (!gridType.isCollectGrid())
                continue;

            activeGird(grid);
            collectBuilder.clear();

            collectBuilder.setCollectSource(flipSupport.buildGrid(grid, gridBuilder));
            FlipGridType collectType = gridType.getCollectType();
            // 全图收集
            for (Flip.Grid collectGrid : model.getMap()) {
                if (collectType.getType() != collectGrid.getType())
                    continue;
                if (!collectGrid.isShow())
                    continue;

                activeGird(collectGrid);

                List<Integer> stoneReward = Lists.newArrayList(collectType.getItemId(flipBaseEntity), 1);

                collectEffectBuilder.clear();
                collectEffectBuilder.setStoneTarger(flipSupport.buildGrid(collectGrid, gridBuilder));
                collectEffectBuilder.setRewardDto(CommonHelper.buildRewardDto(stoneReward));
                rewards.add(stoneReward);

                collectBuilder.addEffects(collectEffectBuilder.build());
            }

            resp.addCollectGrids(collectBuilder.build());

            iterator.remove();
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        // 检查死局
        Flip.Grid impasseShowGrid = checkImpasse(flip);
        if (null != impasseShowGrid) {
            resp.setImpasseShowGrid(flipSupport.buildGrid(impasseShowGrid, null));
        }

        updateModel(flip);

        resp.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        resp.addAllMapGrids(flipSupport.buildGridList(flip));
        resp.setAccSteps(model.getAccSteps());

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipMapFindSpecialResponse> hasSpecila(FlipProto.FlipMapFindSpecialRequest params) {
        long userId = RequestContext.getUserId();

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        FlipProto.FlipMapFindSpecialResponse.Builder resp = FlipProto.FlipMapFindSpecialResponse.newBuilder();
        resp.setCommonData(CommonHelper.buildCommonData());
        resp.setHasSpecialRewards(hasSprcialRewards(flip));

        return Result.Success(resp.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FlipProto.FlipAllAccRewardResponse> allAccReward(FlipProto.FlipAllAccRewardRequest params) {
        long userId = RequestContext.getUserId();

        Flip flip = fetchModel(userId);
        if (flip == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return Result.Error(ErrorCode.QUERY_USER_DATA_ERROR);
        }

        int eventId = flip.getActivityId();
        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(eventId);
        if (null == flipBaseEntity) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        FlipProto.FlipAllAccRewardResponse.Builder response = FlipProto.FlipAllAccRewardResponse.newBuilder();

        List<List<Integer>> rewards = Lists.newArrayList();

        for (List<Integer> list : flipBaseEntity.getBigReward()) {
            int configNeed = list.get(0);
            if (model.getClue() < configNeed)
                continue;

            if (model.getAcc().contains(configNeed))
                continue;

            model.getAcc().add(configNeed);
            response.addNeeds(configNeed);

            int dropId = list.get(1);

            rewards.addAll(dropService.dropRewardsConfig(dropId));
        }

        RewardResultSet rewardResultSet = null;
        if (!rewards.isEmpty()) {
            rewardResultSet = rewardService.executeRewards(userId, rewards);
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
        }

        if (response.getNeedsCount() > 0) {
            updateModel(flip);
        }

        response.setCommonData(rewardResultSet == null ? CommonHelper.buildCommonData() : CommonHelper.buildCommonData(rewardResultSet));
        response.setClueNum(model.getClue());

        List<FlipProto.AccClueRewardDto> accRewardDtos = flipSupport.buildAccRewardList(flip, flipBaseEntity);
        response.addAllAccRewards(accRewardDtos);

        return Result.Success(response.build());
    }

    @Override
    public void onAddClue(long userId, int itemId, int count, boolean saveLog) {
        Flip flip = fetchModel(userId);
        if (flip == null) {
            return;
        }

        Flip.FlipModel model = flip.getFlip();
        if (model == null) {
            return;
        }

        // 更新排行榜分数
        ActivityManager.getInstance().incRank(userId, flip.getActivityId(), count);

        // 累计线索数
        model.setClue(model.getClue() + count);
        updateModel(flip);

        //  todo 记录日志
    }

    private boolean hasSprcialRewards(Flip model) {
        boolean result = false;

        for (Flip.Grid grid : model.getFlip().getMap()) {
            if (grid.getFlipGridType().isSpecialGrid() && !grid.isActive()) {
                result = true;
                break;
            }
        }

        return result;
    }

    private Map<FlipGridType, Integer> getAccStones(long userId, FlipBaseEntity baseEntity) {
        Map<FlipGridType, Integer> result = Maps.newHashMap();
        List<Integer> itemIds = flipSupport.getStones(baseEntity);
        List<Item> items = itemDao.queryByItemIds(userId, itemIds);
        for (Item item : items) {
            int itemId = item.getItemId();
            int cnt = item.getCount();
            if (FlipGridType.eNormalGrid1.getItemId(baseEntity) == itemId) {
                result.put(FlipGridType.eNormalGrid1, cnt);
                continue;
            }
            if (FlipGridType.eNormalGrid2.getItemId(baseEntity) == itemId) {
                result.put(FlipGridType.eNormalGrid2, cnt);
                continue;
            }
            if (FlipGridType.eNormalGrid3.getItemId(baseEntity) == itemId) {
                result.put(FlipGridType.eNormalGrid3, cnt);
                continue;
            }
            if (FlipGridType.eNormalGrid4.getItemId(baseEntity) == itemId) {
                result.put(FlipGridType.eNormalGrid4, cnt);
                continue;
            }
        }

        return result;
    }

    private boolean showGird(Flip.Grid grid) {
        if (grid.isHidden()) {
            grid.setStatus(Flip.Grid.STATUS_SHOW);
            return true;
        }

        return false;
    }

    private boolean activeGird(Flip.Grid grid) {
        if (grid.isShow()) {
            grid.setStatus(Flip.Grid.STATUS_ACTIVE);
            return true;
        }

        return false;
    }

    private List<Flip.Grid> allShowGirds(Flip model) {
        List<Flip.Grid> result = Lists.newArrayList();
        for (Flip.Grid grid : model.getFlip().getMap()) {
            if (showGird(grid)) {
                result.add(grid);
            }
        }

        return result;
    }

    private List<Flip.Grid> partShowGirds(Flip model) {
        List<Flip.Grid> result = Lists.newArrayList();
        List<Flip.Grid> pool = Lists.newArrayList();
        for (Flip.Grid grid : model.getFlip().getMap()) {
            if (grid.isHidden()) {
                pool.add(grid);
            }
        }

        List<Flip.Grid> selectList = CollectionUtils.createRandomList(pool, 5);
        result.addAll(selectList);

        selectList.forEach(g -> showGird(g));

        return result;
    }

    private Flip.Grid checkImpasse(Flip model) {
        // 地图翻开的普通格周围8方向没有可以翻开的格即为死局
        List<Flip.Grid> stones = Lists.newArrayList();
        for (Flip.Grid grid : model.getFlip().getMap()) {
            if (!grid.isShow())
                continue;
            if (!grid.getFlipGridType().isNormalGrid())
                continue;
            stones.add(grid);
        }

        boolean impasse = true;
        if (!stones.isEmpty()) {
            out:
            for (Flip.Grid grid : stones) {
                List<Integer> idxList = flipSupport.getAroundGrids(grid.getIdx());
                for (int idx : idxList) {
                    Flip.Grid aroundGrid = model.getFlip().getMap().get(idx);
                    if (aroundGrid.isHidden()) {
                        impasse = false;
                        break out;
                    }
                }
            }
        }

        // 死局随机开一个格子
        Flip.Grid impasseGrid = null;
        if (impasse) {
            impasseGrid = findImpassGrid(model);
        }

        return impasseGrid;
    }

    private Flip.Grid findImpassGrid(Flip model) {
        List<Flip.Grid> hiddenPool = model.getFlip().getMap().stream().filter(g -> g.isHidden()).collect(Collectors.toList());
        List<Flip.Grid> randPool = null;

        // 1.普通石头格
        randPool = hiddenPool.stream()
                .filter(g -> g.getFlipGridType().isNormalGrid())
                .collect(Collectors.toList());
        if (!randPool.isEmpty()) {
            Flip.Grid grid = RandomUtil.randomElement(randPool);
            showGird(grid);
            return grid;
        }

        // 2.全开格子
        randPool = hiddenPool.stream()
                .filter(g -> g.getFlipGridType().isAllShowGrid() || g.getFlipGridType().isPartShowGrid())
                .collect(Collectors.toList());
        if (!randPool.isEmpty()) {
            Flip.Grid grid = RandomUtil.randomElement(randPool);
            showGird(grid);
            return grid;
        }

        // 3.炸弹格子
        randPool = hiddenPool.stream()
                .filter(g -> g.getFlipGridType().isBombGrid())
                .collect(Collectors.toList());
        if (!randPool.isEmpty()) {
            Flip.Grid grid = RandomUtil.randomElement(randPool);
            showGird(grid);
            return grid;
        }

        // 4.步数格 特殊奖励格
        randPool = hiddenPool.stream()
                .filter(g -> g.getFlipGridType().isStepGrid() || g.getFlipGridType().isSpecialGrid())
                .collect(Collectors.toList());
        if (!randPool.isEmpty()) {
            Flip.Grid grid = RandomUtil.randomElement(randPool);
            showGird(grid);
            return grid;
        }

        // 5.收集格
        randPool = hiddenPool.stream()
                .filter(g -> g.getFlipGridType().isCollectGrid())
                .collect(Collectors.toList());
        if (!randPool.isEmpty()) {
            Flip.Grid grid = RandomUtil.randomElement(randPool);
            showGird(grid);
            return grid;
        }

        // 6.线索格
        for (Flip.Grid grid : hiddenPool) {
            FlipGridType gridType = grid.getFlipGridType();
            if (gridType.isClueGrid()) {
                showGird(grid);
                return grid;
            }
        }

        return null;
    }


    private void exchangeMail(long userId, Flip model) {
        if (model.getIsMail()) {
            return;
        }
        FlipBaseEntity flipBaseEntity = flipSupport.getBaseEntity(model.getActivityId());
        if(flipBaseEntity == null) {
            return;
        }

        // 清理的道具ID
        List<List<Integer>> mailItems = flipBaseEntity.getMailItems();
        List<Integer> clearItemIds = mailItems.stream().map(l -> l.get(0)).collect(Collectors.toList());
        Map<Integer, Integer> exchangCoins = mailItems.stream().collect(Collectors.toMap(l -> l.get(0), l -> l.get(1)));

        int coins = 0;

        // 删除道具
        List<Item> clearItems = itemDao.queryByItemIds(userId, clearItemIds);
        for (Item clearItem : clearItems) {
            if (clearItem.getCount() > 0) {
                // 换成金币
                Integer ratio = exchangCoins.get(clearItem.getItemId());
                if (ratio != null) {
                    int exchangeNum = clearItem.getCount() * ratio;
                    coins += Math.max(0, exchangeNum);
                }

            }
            itemDao.delete(clearItem);  // 清空
            log.info("FlipDeleteItem activityId={} itemId={} count={}", model.getActivityId(), clearItem.getItemId(), clearItem.getCount());
        }

        // 邮件奖励
        List<List<Integer>> rewards = new ArrayList<>();

        // 累计奖励
        int clue = model.getFlip().getClue();
        if (clue > 0) {
            for (List<Integer> list : flipBaseEntity.getBigReward()) {
                int configNeed = list.get(0);
                if (clue >= configNeed && !model.getFlip().getAcc().contains(configNeed)) {
                    int dropId = list.get(1);
                    rewards.addAll(dropService.dropRewardsConfig(dropId));
                    model.getFlip().getAcc().add(configNeed);
                }
            }
        }

        if (coins > 0) {
            rewards.add(Lists.newArrayList(RewardResourceType.COINS.getValue(), coins));
        }

        if (!rewards.isEmpty()) {
            String mailUniqueId = userId + "_flip_" + model.getActivityId() + "_" + model.getMailUniqueId();
            if (redisLock.lockWithOutRetry(mailUniqueId, "1", 3000)) {
                String tmpId = gameConfigManager.getFlipMail(flipBaseEntity);
                boolean flag = mailService.createMail(userId, tmpId, mailUniqueId, rewards, MailSourceType.FLIP, model.getActivityId());
                log.info("sendFlipMail userId:{}, rewards:{}", userId, rewards);
                if (!flag) {
                    log.error("sendFlipMail error, userId:{}, rewards:{}", userId, rewards);
                    return;
                }
            }
        }

        model.setIsMail(true);
        flipDao.update(model);
    }
}

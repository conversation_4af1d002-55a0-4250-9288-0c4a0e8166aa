package com.dxx.game.common.error;

import com.dxx.game.consts.ErrorCode;
import lombok.Getter;

/**
 * @author: lsc
 * @createDate: 2025/5/22
 * @description:
 */
@Getter
public class GameError extends RuntimeException {
    private int code;

    public GameError(int code) {
        this.code = code;
    }

    //系统错误，业务不处理
    public GameError(String message) {
        super(message);
        this.code = ErrorCode.SERVER_SYSTEM_ERROR;
    }

    public GameError(String message, int code) {
        super(message);
        this.code = code;
    }
}

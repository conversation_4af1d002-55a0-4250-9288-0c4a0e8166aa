package com.dxx.game.modules.activity.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.eventpower.PowerBaseEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.ActivityType;
import com.dxx.game.consts.MailSourceType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.activity.Power;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.activity.ActivityBaseDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dto.PowerProto;
import com.dxx.game.modules.activity.base.ActivityLifeCycle;
import com.dxx.game.modules.activity.base.ActivityManager;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.dxx.game.modules.activity.service.PowerService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.pvp.support.PVPHelper;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.user.support.PowerSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PowerServiceImpl implements PowerService, ActivityLifeCycle<Power, PowerProto.PowerDto> {
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private MailService mailService;
    @Resource
    private RewardService rewardService;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private PVPHelper pvpHelper;
    @Resource
    private ActivityBaseDao.PowerDao powerDao;
    @Resource
    private RedisLock redisLock;
    @Autowired
    private PowerSupport powerSupport;

    @Override
    public ActivityMetaData<Power, PowerProto.PowerDto> buildMetaData() {
        return ActivityMetaData.<Power, PowerProto.PowerDto>valueOf(ActivityType.POWER).withDao(powerDao);
    }

    @Override
    public void initializeModel(long userId, Power model) {
        Power.PowerModel powerModel = new Power.PowerModel();
        powerModel.setRewardIds(new ArrayList<>());
        model.setPower(powerModel);
    }

    @DynamoDBTransactional
    @Override
    public Result<PowerProto.PowerOnOpenResponse> onOpen(PowerProto.PowerOnOpenRequest params) {
        long userId = RequestContext.getUserId();

        Power power = fetchModel(userId);
        if (power == null) {
            return Result.Error(ErrorCode.ACTIVITY_NOT_OPEN);
        }
        PowerProto.PowerOnOpenResponse.Builder response = PowerProto.PowerOnOpenResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData());
        response.setPowerDto(buildDto(power));
        return Result.Success(response.build());
    }

    @Override
    public void syncPower(User user, long maxPower) {
        var entity = ActivityManager.getInstance().getEntityByType(ActivityType.POWER);
        if (entity == null) {
            return;
        }

        long value = ActivityManager.getInstance().getRankScoreByActivityId(user.getUserId(), entity.getFirst().getId());
        if (maxPower > value) {
            ActivityManager.getInstance().commitRank(user.getUserId(), entity.getFirst().getId(), maxPower);
        }
    }

    @Override
    public PowerProto.PowerDto buildDto(Power model) {
        PowerProto.PowerDto.Builder dto = PowerProto.PowerDto.newBuilder();
        dto.setActivityId(model.getActivityId());
        dto.addAllRewardId(model.getPower().getRewardIds());
        return dto.build();
    }

    @Override
    public void onActivityEnd(Power model) {
        this.exchangeMail(model.getUserId(), model);
    }


    @DynamoDBTransactional
    @Override
    public Result<PowerProto.PowerRewardResponse> reward(PowerProto.PowerRewardRequest params) {
        long userId = RequestContext.getUserId();

        int configId = params.getConfigId();
        PowerBaseEntity rewardEntity = getPowerBaseEntity(configId);
        if(rewardEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        Power power = powerDao.getByUserId(userId);
        if (power == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Power model = fetchModel(userId);
        if(model.getPower().getRewardIds().contains(configId)) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }

        long powerValue = ActivityManager.getInstance().getRankScoreByActivityId(userId, model.getActivityId());
        if (powerValue < rewardEntity.getNeed()) {
            return Result.Error(ErrorCode.ACTIVITY_COND_NOT_ENOUGH);
        }

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardEntity.getReward());
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        model.getPower().getRewardIds().add(configId);
        updateModel(model);

        PowerProto.PowerRewardResponse.Builder response = PowerProto.PowerRewardResponse.newBuilder();
        return Result.Success(response.build());
    }

    private void exchangeMail(long userId, Power model) {
        if (model.getIsMail()) {
            return;
        }
        long power = ActivityManager.getInstance().getRankScoreByActivityId(userId, model.getActivityId());
        List<List<Integer>> rewards = new ArrayList<>();
        gameConfigManager.getEventPowerConfig().getPowerBase().values().forEach(v -> {
            if(!model.getPower().getRewardIds().contains(v.getID()) && power >= v.getNeed()) {
                model.getPower().getRewardIds().add(v.getID());
                rewards.addAll(v.getReward());
            }
        });

        if (!rewards.isEmpty()) {
            String mailUniqueId = userId + "_power_" + model.getActivityId() + "_" + model.getMailUniqueId();
            if (redisLock.lockWithOutRetry(mailUniqueId, "1", 3000)) {
                String tmpId = gameConfigManager.getMailTempId(4006, 4005);
                boolean flag = mailService.createMail(userId, tmpId, mailUniqueId, rewards, MailSourceType.POWER, model.getActivityId());
                log.info("sendPowerMail userId:{}, rewards:{}", userId, rewards);
                if (!flag) {
                    log.error("sendPowerMail error, userId:{}, rewards:{}", userId, rewards);
                    return;
                }
            }
        }
        model.setIsMail(true);
        powerDao.update(model);
    }

    public PowerBaseEntity getPowerBaseEntity(int id) {
        return gameConfigManager.getEventPowerConfig().getPowerBaseEntity(id);
    }
}

package com.dxx.game.modules.shop.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.ShopProto;

/**
 * @author: lsc
 * @createDate: 2025/6/7
 * @description:
 */
public interface BattlePassService {

    /**
     * 获取通行证信息
     */
    Result<ShopProto.BattlePassGetInfoResponse> battlePassGetInfo(ShopProto.BattlePassGetInfoRequest params);

    /**
     * 领取通行证奖励
     */
    Result<ShopProto.BattlePassRewardResponse> battlePassReward(ShopProto.BattlePassRewardRequest params);

    /**
     * 兑换通行证积分
     */
    Result<ShopProto.BattlePassChangeScoreResponse> battlePassChangeScore(ShopProto.BattlePassChangeScoreRequest params);

    /**
     * 领取通行证最终奖励
     */
    Result<ShopProto.BattlePassFinalRewardResponse> battlePassFinalReward(ShopProto.BattlePassFinalRewardRequest params);
}

package com.dxx.game.common.aws.dynamodb.annotation;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.utils.PackageScanner;
import lombok.SneakyThrows;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.type.AnnotationMetadata;

import java.lang.reflect.Method;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2020/10/27 10:27
 */
public class DynamoDBTransactionScannerRegistrar implements ImportBeanDefinitionRegistrar {


    @SneakyThrows
    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry beanDefinitionRegistry) {
        AnnotationAttributes annoAttrs = AnnotationAttributes.fromMap(importingClassMetadata.getAnnotationAttributes(DynamoDBTransactionScan.class.getName()));

        String[] packages = annoAttrs.getStringArray("basePackages");
        Class<?>[] methodReturnTypeClasses = annoAttrs.getClassArray("methodReturnTypeClasses");

        for (int i = 0; i < packages.length; i ++) {
            Collection<Class<?>> classes = PackageScanner.scanPackages(packages[i]);
            Class<?> returnType = methodReturnTypeClasses[i];
            for(Class<?> clazz : classes){
                for (Method method : clazz.getMethods()) {
                    if (method.getReturnType().equals(returnType)) {
                        DynamoDBTransactional annotation = AnnotationUtils.getAnnotation(method, DynamoDBTransactional.class);
                        if (annotation == null) {
                            throw new Exception("the method [" +clazz.getName() + "." + method.getName() + "] not used DynamoDBTransactional annotation");
                        }
                    }
                }
            }
        }

    }
}

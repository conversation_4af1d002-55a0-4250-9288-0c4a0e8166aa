package com.dxx.game.modules.pay.support;

import com.dxx.game.common.channel.alipay.AliPayService;
import com.dxx.game.common.channel.bilibili.BiliService;
import com.dxx.game.common.channel.bilibili.model.BiliUnifiedOrderResult;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.channel.douyin.DouYinService;
import com.dxx.game.common.channel.douyin.model.DouYinUnifiedOrderResult;
import com.dxx.game.common.channel.honor.HonorService;
import com.dxx.game.common.channel.kuaishou.KuaiShouService;
import com.dxx.game.common.channel.kuaishou.model.KuaiShouUnifiedOrderResult;
import com.dxx.game.common.channel.ssjj.SsjjService;
import com.dxx.game.common.channel.uc.UCService;
import com.dxx.game.common.channel.uc.model.UcUnifiedOrderResult;
import com.dxx.game.common.channel.vivo.VivoService;
import com.dxx.game.common.channel.vivo.model.VivoUnifiedOrderResult;
import com.dxx.game.common.channel.wechat.WeChatService;
import com.dxx.game.common.channel.wechat.model.WeChatUnifiedOrderResult;
import com.dxx.game.common.channel.wechatminigame.WeChatMiniGameService;
import com.dxx.game.common.channel.wechatminigame.model.WeChatMiniGameUnifiedOrderResult;
import com.dxx.game.common.channel.yyb.YybService;
import com.dxx.game.common.channel.yyb.model.YybOrderResult;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.iap.*;
import com.dxx.game.consts.IapType;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dto.PayProto;
import com.dxx.game.modules.activity.service.ActivityService;
import com.dxx.game.modules.user.service.UserService;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/6/11
 * @description:
 */
@Slf4j
@Component
public class PaySupport {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private ActivityService activityService;
    @Resource
    private ShopDao shopDao;
    @Resource
    private WeChatService weChatService;
    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private AliPayService aliPayService;
    @Resource
    private UserService userService;
    @Resource
    private UCService ucService;
    @Resource
    private BiliService biliService;
    @Resource
    private KuaiShouService kuaiShouService;
    @Resource
    private VivoService vivoService;
    @Resource
    private SsjjService ssjjService;
    @Resource
    private YybService yybService;
    @Resource
    private DouYinService douYinService;
    @Resource
    private WeChatMiniGameService weChatMiniGameService;
    @Resource
    private HonorService honorService;

    // 获取支付道具
    public Pair<Boolean, List<List<Integer>>> getPurchaseRewards(long userId, int purchaseId, String extraInfo) {
        PurchaseEntity purchaseEntity = this.getByPurchaseId(purchaseId);
        if (purchaseEntity == null) {
            log.error("PurchaseEntity not found for purchaseId: {}", purchaseId);
            return null;
        }

        List<List<Integer>> rewardsConfig = new ArrayList<>();
        int addVipExp = purchaseEntity.getVipExp();
        if (addVipExp > 0) {
            // 添加 VIP 经验奖励
            rewardsConfig.add(Lists.newArrayList(RewardResourceType.VIP_EXP.getValue(), addVipExp));
        }

        Shop shop = shopDao.getByUserId(userId);
        long now = DateUtils.getUnixTime();

        boolean success = switch (purchaseEntity.getProductType()) {
            case IapType.DIAMONDS -> handleDiamondsRewards(purchaseId, rewardsConfig);
            case IapType.GIFT_PACK -> handleGiftPackRewards(purchaseId, shop, now, rewardsConfig);
            case IapType.MONTH_CARD -> handleMonthCardRewards(purchaseId, shop, now, rewardsConfig);
            case IapType.BATTLE_PASS -> handleBattlePassRewards(purchaseId, shop, rewardsConfig);
            case IapType.LEVEL_FUND -> handleLevelFundRewards(purchaseId, shop, rewardsConfig);
            case IapType.PUSH_GIFT -> handlePushGiftRewards(purchaseId, shop, rewardsConfig);
            case IapType.ACTIVITY_GIFT -> handleActivityGiftRewards(extraInfo, purchaseId, rewardsConfig);
            default -> {
                log.error("Unhandled product type for purchaseId: {}", purchaseId);
                yield false;
            }
        };

        return Pair.of(success, rewardsConfig);
    }

    private boolean handleDiamondsRewards(int purchaseId, List<List<Integer>> rewardsConfig) {
        DiamondsEntity diamondsEntity = gameConfigManager.getIAPConfig().getDiamondsEntity(purchaseId);
        if (diamondsEntity == null) {
            log.error("DiamondsEntity is null for purchaseId: {}", purchaseId);
            return false;
        }
        rewardsConfig.addAll(CollectionUtils.deepCopyList(diamondsEntity.getProducts()));
        return true;
    }

    private boolean handleGiftPackRewards(int purchaseId, Shop shop, long now, List<List<Integer>> rewardsConfig) {
        GiftPacksEntity giftPacksEntity = gameConfigManager.getIAPConfig().getGiftPacksEntity(purchaseId);
        if (giftPacksEntity == null) {
            log.error("GiftPacksEntity is null for purchaseId: {}", purchaseId);
            return false;
        }
        rewardsConfig.addAll(CollectionUtils.deepCopyList(giftPacksEntity.getProducts()));

        Shop.IAPModel iapModel = shop.getIap();
        Map<Integer, Shop.IAPPacksModel> packsMap = iapModel.getPacksMap();
        Shop.IAPPacksModel iapPacksModel = packsMap.computeIfAbsent(purchaseId, id -> {
            Shop.IAPPacksModel newModel = new Shop.IAPPacksModel();
            newModel.setId(id);
            newModel.setBuyCount(0);
            newModel.setLastBuyTime(now);
            return newModel;
        });

        iapPacksModel.setBuyCount(iapPacksModel.getBuyCount() + 1);
        iapPacksModel.setLastBuyTime(now);
        return true;
    }

    private boolean handleMonthCardRewards(int purchaseId, Shop shop, long now, List<List<Integer>> rewardsConfig) {
        MonthCardEntity monthCardEntity = gameConfigManager.getIAPConfig().getMonthCardEntity(purchaseId);
        if (monthCardEntity == null) {
            log.error("MonthCardEntity is null for purchaseId: {}", purchaseId);
            return false;
        }
        rewardsConfig.addAll(CollectionUtils.deepCopyList(monthCardEntity.getProducts()));

        Shop.IAPModel iap = shop.getIap();
        Shop.IAPMonthCardModel iapMonthCardModel = iap.getMonthCardMap().computeIfAbsent(purchaseId, id -> {
            Shop.IAPMonthCardModel newModel = new Shop.IAPMonthCardModel();
            newModel.setId(id);
            return newModel;
        });

        int lastCount = iapMonthCardModel.getLastCount();
        lastCount = monthCardEntity.getDuration() == 0 ? Integer.MAX_VALUE - 1 : lastCount + monthCardEntity.getDuration();
        iapMonthCardModel.setLastCount(lastCount);
        iapMonthCardModel.setLastRewardTime(now - DateUtils.SECONDS_PRE_DAY);

        long durationTime = (long) (monthCardEntity.getDuration() <= 0 ? 7300 : monthCardEntity.getDuration() - 1) * DateUtils.SECONDS_PRE_DAY;
        long expireTime = iapMonthCardModel.getExpireTime() == 0
                ? DateUtils.getSystemResetTime() + durationTime
                : iapMonthCardModel.getExpireTime() + durationTime;
        iapMonthCardModel.setExpireTime(expireTime);
        return true;
    }

    private boolean handleBattlePassRewards(int purchaseId, Shop shop, List<List<Integer>> rewardsConfig) {
        Shop.IAPModel iap = shop.getIap();
        Shop.IAPBattlePassModel battlePassModel = iap.getBattlePassModel();
        if (battlePassModel.getId() == 0 || battlePassModel.getBuy() == 1) {
            log.error("Invalid BattlePassModel for purchaseId: {}", purchaseId);
            return false;
        }

        BattlePassEntity battlePassEntity = gameConfigManager.getIAPConfig().getBattlePassEntity(battlePassModel.getId());
        if (battlePassEntity == null) {
            log.error("BattlePassEntity is null for seasonId: {}", battlePassModel.getId());
            return false;
        }
        battlePassModel.setBuy(1);
        rewardsConfig.addAll(CollectionUtils.deepCopyList(battlePassEntity.getProducts()));
        return true;
    }

    private boolean handleLevelFundRewards(int purchaseId, Shop shop, List<List<Integer>> rewardsConfig) {
        LevelFundEntity levelFundEntity = gameConfigManager.getIAPConfig().getLevelFundEntity(purchaseId);
        if (levelFundEntity == null) {
            log.error("LevelFundEntity is null for purchaseId: {}", purchaseId);
            return false;
        }

        Shop.IAPModel iapModel = shop.getIap();
        if (iapModel.getLevelFundRewardMap().containsKey(levelFundEntity.getGroupId())) {
            log.error("LevelFund groupId already exists: {}", levelFundEntity.getGroupId());
            return false;
        }
        iapModel.getLevelFundRewardMap().put(levelFundEntity.getGroupId(), new ArrayList<>());

        rewardsConfig.addAll(CollectionUtils.deepCopyList(levelFundEntity.getProducts()));
        return true;
    }

    private boolean handlePushGiftRewards(int purchaseId, Shop shop, List<List<Integer>> rewardsConfig) {
        PushPacksEntity pushPacksEntity = gameConfigManager.getIAPConfig().getPushPacksEntity(purchaseId);
        if (pushPacksEntity == null) {
            log.error("PushPacksEntity is null for purchaseId: {}", purchaseId);
            return false;
        }

        Shop.IAPModel iap = shop.getIap();
        if (pushPacksEntity.getPackType() == 2) {
            iap.getOpenServerGiftTime().put(purchaseId, 0L);
        } else if (pushPacksEntity.getPackType() == 4) {
            iap.getChapterGiftTime().put(purchaseId, 0L);
        }
        rewardsConfig.addAll(CollectionUtils.deepCopyList(pushPacksEntity.getProducts()));
        return true;
    }

    private boolean handleActivityGiftRewards(String extraInfo, int purchaseId, List<List<Integer>> rewardsConfig) {
        int eventId = Integer.parseInt(extraInfo.split(",")[0]);
        List<List<Integer>> reward = activityService.getShopIapReward(eventId, purchaseId);
        if (reward == null) {
            log.error("Activity reward is null for eventId: {}, purchaseId: {}", eventId, purchaseId);
            return false;
        }
        rewardsConfig.addAll(CollectionUtils.deepCopyList(reward));
        return true;
    }

    public boolean unifiedOrder(long userId, PayProto.PayPreOrderRequest request, PayProto.PayPreOrderResponse.Builder response) {
        int channelId = request.getChannelId();
        if (channelId < 100) {
            return true;
        }
        String extraInfo = request.getExtraInfo();
        long preOrderId = request.getPreOrderId();
        int purchaseId = request.getPurchaseId();
        User user = userService.getUser(userId);
        IAPEntity iapEntity = this.getIAPEntityByPurchaseId(purchaseId);
        int amountFen = (int) (iapEntity.getCNprice() * 100);
        int amountYuan = (int)iapEntity.getCNprice();
        int amountJiao = (int) (iapEntity.getCNprice() * 10);
        String cpOrderId = PaymentUtils.createCpOrderId();
        ChannelID channel = ChannelID.valueOf(channelId);
        response.setCpOrderId(cpOrderId);
        response.setNotifyUrl(channelConfig.getNotifyUrl(channel));
        response.setAmount(amountFen);
        if (channelId != ChannelID.Ssjj.getId() && channelId != ChannelID.Honor.getId() && channelConfig.isPayWhiteList(userId)) {
            // 测试服固定1分 (4399 除外)
            response.setAmount(1);
        }
        if (channelId == ChannelID.Ssjj.getId() && channelConfig.isPayWhiteList(userId)) {
            response.setAmount(100);
        }
        String attach = PaymentUtils.createPassBackParams(userId, purchaseId, extraInfo, preOrderId);
        response.setPassBackParams(attach);

        if (channelId == ChannelID.WeChat.getId()) {

            // 透传参数
            WeChatUnifiedOrderResult weChatUnifiedOrderResult
                    = weChatService.unifiedOrder(userId, request.getAndroidPackageName(), iapEntity.getIapName(),
                    attach, amountFen);
            if (weChatUnifiedOrderResult == null) {
                return false;
            }
            PayProto.WeChatOrderDto.Builder weChatOrderBuilder = PayProto.WeChatOrderDto.newBuilder();
            weChatOrderBuilder.setPrePayId(weChatUnifiedOrderResult.getPrePayId());
            weChatOrderBuilder.setNonceStr(weChatUnifiedOrderResult.getNonceStr());
            weChatOrderBuilder.setTimestamp(weChatUnifiedOrderResult.getTimestamp());
            weChatOrderBuilder.setSign(weChatUnifiedOrderResult.getSign());

            response.setWeChatOrderDto(weChatOrderBuilder.build());
        } else if (channelId == ChannelID.AliPay.getId()) {
            // 支付宝 - 金额单位(元)

            String orderData = aliPayService.unifiedOrder(userId, request.getAndroidPackageName(), iapEntity.getIapName(), attach, amountYuan);
            if (StringUtils.isEmpty(orderData)) {
                return false;
            }
            response.setAliOrderData(orderData);
        } else if (channelId == ChannelID.UC.getId()) {
            // UC - 金额单位(元)
            UcUnifiedOrderResult ucUnifiedOrderResult = ucService.unifiedOrder(user.getAccountId(), userId, attach, amountYuan);

            PayProto.UcOrderDto.Builder ucOrderBuilder = PayProto.UcOrderDto.newBuilder();
            ucOrderBuilder.setCallbackInfo(ucUnifiedOrderResult.getCallbackInfo());
            ucOrderBuilder.setAmount(ucUnifiedOrderResult.getAmount());
            ucOrderBuilder.setNotifyUrl(ucUnifiedOrderResult.getNotifyUrl());
            ucOrderBuilder.setCpOrderId(ucUnifiedOrderResult.getCpOrderId());
            ucOrderBuilder.setAccountId(ucUnifiedOrderResult.getAccountId());
            ucOrderBuilder.setSignType(ucUnifiedOrderResult.getSignType());
            ucOrderBuilder.setSign(ucUnifiedOrderResult.getSign());
            response.setUcOrderDto(ucOrderBuilder.build());
        } else if (channelId == ChannelID.Bili.getId()) {
            // bilibili - 金额(分)
            BiliUnifiedOrderResult biliUnifiedOrderResult = biliService.unifiedOrder(userId, attach, amountFen);

            PayProto.BiliOrderDto.Builder biliBuilder = PayProto.BiliOrderDto.newBuilder();
            biliBuilder.setNotifyUrl(biliUnifiedOrderResult.getNotifyUrl());
            biliBuilder.setOutTradeNo(biliUnifiedOrderResult.getOutTradeNo());
            biliBuilder.setTotalFee(biliUnifiedOrderResult.getAmount());
            biliBuilder.setExtensionInfo(biliUnifiedOrderResult.getExtensionInfo());
            biliBuilder.setGameMoney(biliUnifiedOrderResult.getGameMoney());
            biliBuilder.setSubject(iapEntity.getIapName());
            biliBuilder.setBody(iapEntity.getIapName());
            biliBuilder.setUid(user.getAccountId());
            biliBuilder.setRole(String.valueOf(user.getUserId()));
            biliBuilder.setOrderSign(biliUnifiedOrderResult.getOrderSign());

            response.setBiliOrderDto(biliBuilder.build());

        } else if (channelId == ChannelID.KuaiSHou.getId()) {
            // 快手 金额 分
            KuaiShouUnifiedOrderResult kuaiShouUnifiedOrderResult = kuaiShouService.unifiedOrder(userId, user.getAccountId(), String.valueOf(user.getLevel()),
                    String.valueOf(userId), request.getKuaiShouChannelID(),
                    String.valueOf(purchaseId), iapEntity.getIapName(), attach, amountFen);

            PayProto.KuaiShouOrderDto.Builder kuaiShouOrderBuilder = PayProto.KuaiShouOrderDto.newBuilder();
            kuaiShouOrderBuilder.setChannelId(kuaiShouUnifiedOrderResult.getChannelId());
            kuaiShouOrderBuilder.setUserIp(kuaiShouUnifiedOrderResult.getUserIp());
            kuaiShouOrderBuilder.setAppId(kuaiShouUnifiedOrderResult.getAppId());
            kuaiShouOrderBuilder.setProductId(kuaiShouUnifiedOrderResult.getProductId());
            kuaiShouOrderBuilder.setProductName(kuaiShouUnifiedOrderResult.getProductName());
            kuaiShouOrderBuilder.setProductDesc(kuaiShouUnifiedOrderResult.getProductDesc());
            kuaiShouOrderBuilder.setProductNum(kuaiShouUnifiedOrderResult.getProductNum());
            kuaiShouOrderBuilder.setPrice(kuaiShouUnifiedOrderResult.getPrice());
            kuaiShouOrderBuilder.setServerId(kuaiShouUnifiedOrderResult.getServerId());
            kuaiShouOrderBuilder.setServerName(kuaiShouUnifiedOrderResult.getServerName());
            kuaiShouOrderBuilder.setRoleId(kuaiShouUnifiedOrderResult.getRoleId());
            kuaiShouOrderBuilder.setRoleName(kuaiShouUnifiedOrderResult.getRoleName());
            kuaiShouOrderBuilder.setRoleLevel(kuaiShouUnifiedOrderResult.getRoleLevel());
            kuaiShouOrderBuilder.setOrderId(kuaiShouUnifiedOrderResult.getOrderId());
            kuaiShouOrderBuilder.setPayNotifyUrl(kuaiShouUnifiedOrderResult.getPayNotifyUrl());
            kuaiShouOrderBuilder.setExtension(kuaiShouUnifiedOrderResult.getExtension());
            kuaiShouOrderBuilder.setCoinName(kuaiShouUnifiedOrderResult.getCoinName());
            kuaiShouOrderBuilder.setSign(kuaiShouUnifiedOrderResult.getSign());

            response.setKusiShouOrderDto(kuaiShouOrderBuilder.build());
        } else if (channelId == ChannelID.Vivo.getId()) {
            // vivo 金额 分
            VivoUnifiedOrderResult vivoUnifiedOrderResult = vivoService.unifiedOrder(userId, user.getAccountId(), String.valueOf(user.getLevel()),
                    iapEntity.getIapName(), attach, amountFen);
            PayProto.VivoOrderDto.Builder vivoOrderDto = PayProto.VivoOrderDto.newBuilder();
            vivoOrderDto.setAppId(vivoUnifiedOrderResult.getAppId());
            vivoOrderDto.setCpOrderNumber(vivoUnifiedOrderResult.getCpOrderNumber());
            vivoOrderDto.setProductName(vivoUnifiedOrderResult.getProductName());
            vivoOrderDto.setProductDesc(vivoUnifiedOrderResult.getProductDesc());
            vivoOrderDto.setOrderAmount(vivoUnifiedOrderResult.getOrderAmount());
            vivoOrderDto.setVivoSignature(vivoUnifiedOrderResult.getVivoSignature());
            vivoOrderDto.setExtuid(vivoUnifiedOrderResult.getExtuid());
            vivoOrderDto.setNotifyUrl(vivoUnifiedOrderResult.getNotifyUrl());
            vivoOrderDto.setExpireTime(vivoUnifiedOrderResult.getExpireTime());
            vivoOrderDto.setLevel(vivoUnifiedOrderResult.getLevel());
            vivoOrderDto.setVip(vivoUnifiedOrderResult.getVip());
            vivoOrderDto.setBalance(vivoUnifiedOrderResult.getBalance());
            vivoOrderDto.setParty(vivoUnifiedOrderResult.getParty());
            vivoOrderDto.setRoleId(vivoUnifiedOrderResult.getRoleId());
            vivoOrderDto.setRoleName(vivoUnifiedOrderResult.getRoleName());
            vivoOrderDto.setServerName(vivoUnifiedOrderResult.getServerName());
            vivoOrderDto.setExtInfo(vivoUnifiedOrderResult.getExtInfo());
            response.setVivoOrderDto(vivoOrderDto.build());
        } else if (channelId == ChannelID.Ssjj.getId()) {
            ssjjService.unifiedOrder(user.getAccountId(), attach, cpOrderId);
        } else if (channelId == ChannelID.YYB.getId()) {
            // 毛
            YybOrderResult yybOrderResult = yybService.unifiedOrder(userId, request.getYybType(), request.getYybOpenId(), request.getYybOpenkey(), request.getYybPf(),
                    request.getYybPfKey(), String.valueOf(purchaseId), amountJiao, iapEntity.getIapName(), iapEntity.getIapName(),
                    attach);
            if (yybOrderResult == null) {
                return false;
            }

            PayProto.YybOrderDto.Builder yybOrderDto = PayProto.YybOrderDto.newBuilder();
            yybOrderDto.setZoneId(yybOrderResult.getZoneId());
            yybOrderDto.setGoodsTokenUrl(yybOrderResult.getGoodsTokenUrl());
            response.setYybOrderDto(yybOrderDto);
        } else if (channelId == ChannelID.DouYin.getId()) {
            // 分
            DouYinUnifiedOrderResult douYinUnifiedOrderResult
                    = douYinService.unifiedOrder(userId, user.getDouYinSdkOpenId(), cpOrderId, String.valueOf(iapEntity.getIapName()), iapEntity.getIapName(), iapEntity.getIapName(),
                    amountFen, RequestContext.getClientIp(), request.getDouyinRiskControlInfo(), attach);
            if (douYinUnifiedOrderResult.getCode() != 0) {
                return false;
            }
            String sdkParam = douYinUnifiedOrderResult.getSdkParam();
            PayProto.DouYinOrderDto.Builder douYinOrderDto = PayProto.DouYinOrderDto.newBuilder();
            douYinOrderDto.setSdkParam(sdkParam);
            douYinOrderDto.setCode(douYinUnifiedOrderResult.getCode());
            douYinOrderDto.setMessage(douYinUnifiedOrderResult.getMessage());
            response.setDouYinOrderDto(douYinOrderDto.build());
        } else if (channelId == ChannelID.WeChatMiniGame.getId()) {
            // 微信小游戏 - 金额(分)
            WeChatMiniGameUnifiedOrderResult weChatMiniGameUnifiedOrderResult
                    = weChatMiniGameService.unifiedOrder(userId, attach, amountFen, user.getWeChatMiniGameSessionKey(), iapEntity.getCNMiniIapId());
            PayProto.WeChatMiniGameOrderDto.Builder weChatMiniGameOrderDto = PayProto.WeChatMiniGameOrderDto.newBuilder();
            weChatMiniGameOrderDto.setSignData(weChatMiniGameUnifiedOrderResult.getSignData());
            weChatMiniGameOrderDto.setPaySig(weChatMiniGameUnifiedOrderResult.getPaySig());
            weChatMiniGameOrderDto.setSignature(weChatMiniGameUnifiedOrderResult.getSignature());
            response.setWeChatMiniGameOrderDto(weChatMiniGameOrderDto.build());

        } else if (channelId == ChannelID.Honor.getId()) {
            honorService.unifiedOrder(userId, attach, cpOrderId);
        }
        return true;
    }


    public PurchaseEntity getByPurchaseId(int purchaseId) {
        return gameConfigManager.getIAPConfig().getPurchaseEntity(purchaseId);
    }

    public IAPEntity getIAPEntityByPurchaseId(int purchaseId) {
        PurchaseEntity purchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(purchaseId);
        if (purchaseEntity == null) {
            return null;
        }
        return gameConfigManager.getIAPConfig().getIAPEntity(purchaseEntity.getIAPID());
    }
}

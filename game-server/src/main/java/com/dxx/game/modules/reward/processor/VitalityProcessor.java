package com.dxx.game.modules.reward.processor;

import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.user.support.VitalitySupport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: lsc
 * @createDate: 2025/6/11
 * @description:
 */
@Slf4j
@Component
public class VitalityProcessor implements RewardProcessor {

    @Resource
    private UserService userService;
    @Resource
    private VitalitySupport vitalitySupport;
    @Resource
    private UserDao userDao;

    @Override
    public RewardType getType() {
        return RewardType.RESOURCE;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.VITALITY;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        if (reward.getCount() < 0) {
            vitalitySupport.checkVitality(userId);
            User user = userService.getUser(userId);
            if (user.getVitality() < Math.abs(reward.getCount())) {
                resultCode = ErrorCode.VITALITY_IS_NOT_ENOUGH;
            }
        }

        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        User user = userService.getUser(userId);
        vitalitySupport.checkVitality(userId);

        int addCount = rewardAction.getReward().getCount();
        int current = user.getVitality() + addCount;
        if (current < 0) {
            log.error("vitality error : userId : {}, vitality: {}, addCount : {}", userId, user.getVitality(), addCount);
            return null;
        }
        int maxLifeValue = vitalitySupport.getMaxVitality(userId);
        if (current >= maxLifeValue) {
            user.setVitalityTimeStamp(DateUtils.getUnixTime());
        }

        user.setVitality(current);
        userDao.updateVitality(user);

        RewardResult<Integer> result = new RewardResult<>(this.getType(), this.getRewardResourceType());
        result.setActualCount(addCount);
        result.setCurrent(current);

        vitalitySupport.buildFillCommon(user);
        return result;
    }
}

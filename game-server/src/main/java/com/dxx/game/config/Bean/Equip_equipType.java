
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Equip_equipType {
    public Equip_equipType(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        atlasID = _buf.get("atlasID").getAsInt();
        iconName = _buf.get("iconName").getAsString();
    }

    public static Equip_equipType deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Equip_equipType(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 图集ID
     */
    public final int atlasID;
    /**
     * 切片名称
     */
    public final String iconName;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "atlasID:" + atlasID + ","
        + "iconName:" + iconName + ","
        + "}";
    }
}


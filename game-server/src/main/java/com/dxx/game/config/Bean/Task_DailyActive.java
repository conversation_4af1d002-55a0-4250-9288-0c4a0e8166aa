
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Task_DailyActive {
    public Task_DailyActive(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Requirements = _buf.get("Requirements").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
    }

    public static Task_DailyActive deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Task_DailyActive(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 活跃度要求
     */
    public final int Requirements;
    /**
     * 奖励
     */
    public final java.util.List<String> Reward;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Requirements:" + Requirements + ","
        + "Reward:" + Reward + ","
        + "}";
    }
}


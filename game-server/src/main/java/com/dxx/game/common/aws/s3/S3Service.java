package com.dxx.game.common.aws.s3;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import jakarta.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/29 18:19
 */
@Slf4j
@Service
public class S3Service {

    @Resource
    private S3Client s3Client;

    /**
     * 加载S3 存储桶上的配置文件
     * @param bucketName
     * @param fileName
     * @return
     */
    public String loadContent(String bucketName, String fileName) {
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest
                    .builder()
                    .bucket(bucketName)
                    .key(fileName)
                    .build();

            ResponseBytes<GetObjectResponse> objectBytes = s3Client.getObjectAsBytes(getObjectRequest);
            return objectBytes.asString(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("loadContent, e:",e);
            return "";
        }
    }

    /**
     * 上传文件到S3
     * @param bucketName
     * @param file
     */
    public void putObject(String bucketName, File file) {

        Map<String, String> metadata = new HashMap<>();
        metadata.put("x-amz-meta-myVal", "test");

        PutObjectRequest putOb = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(file.getName())
                .metadata(metadata)
                .build();
        log.info("upload config [{}] to s3", file.getName());
        s3Client.putObject(putOb, RequestBody.fromBytes(getObjectFile(file)));
    }

    private static byte[] getObjectFile(File file) {

        FileInputStream fileInputStream = null;
        byte[] bytesArray = null;

        try {
            bytesArray = new byte[(int) file.length()];
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(bytesArray);

        } catch (IOException e) {
            log.error("getObjectFile, e:",e);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("getObjectFile close, e:",e);
                }
            }
        }
        return bytesArray;
    }

}

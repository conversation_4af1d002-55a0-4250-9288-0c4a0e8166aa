package com.dxx.game.common.server.protocol;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CommonProto.CommonParams;
import com.google.protobuf.Descriptors.Descriptor;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2019年12月5日
 */
@Slf4j
@Component
public class MessageProto {
    /////////////////////////////////// proto 编解码处理器集合  /////////////////////////////
    /**
     * cmd 对应的消息object
     **/
    private static final ConcurrentMap<Short, Class<? extends Message>> messages = new ConcurrentHashMap<Short, Class<? extends Message>>();
    /**
     * 消息object对应的 cmd
     **/
    private static final ConcurrentMap<Class<? extends Message>, Short> commands = new ConcurrentHashMap<Class<? extends Message>, Short>();
    /**
     * cmd 对应的parseFrom 反序列化方法
     **/
    private static final ConcurrentMap<Short, Parser<? extends Message>> deserializeMethods = new ConcurrentHashMap<>();
    /**
     * cmd 对应的newBuidler 方法
     **/
    private static final ConcurrentMap<Short, Supplier<Message.Builder>> newBuilderMethods = new ConcurrentHashMap<>();
    /**
     * cmd 对应的CommonData对象名称
     **/
    private static final ConcurrentMap<Short, String> responseMsgCommonDataIndexMap = new ConcurrentHashMap<>();

    /**
     * 注册proto
     *
     * @param cmd
     * @param msgClass
     */
    @SuppressWarnings("rawtypes")
    public <M extends Message> void registerMessage(short cmd, Class<? extends Message> msgClass, Supplier<Parser<M>> parser, Supplier<Message.Builder> builder) {
        Class<? extends Message> clazz = messages.put(cmd, msgClass);
        if (clazz != null) {
            log.error("gameProto 消息[cmd:{}, clazz:{}]被覆盖", cmd, clazz);
            throw new RuntimeException("gameProto 消息[cmd:{}, clazz:{}]被覆盖");
        }
        commands.put(msgClass, cmd);
        try {
            deserializeMethods.put(cmd, parser.get());

            newBuilderMethods.put(cmd, builder);
            // 对象中CommonData 所在的index
            Message.Builder newBuilder = builder.get();
            Descriptor descriptor = newBuilder.getDescriptorForType();
            List<FieldDescriptor> fieldDescriptors = descriptor.getFields();
            for (FieldDescriptor fieldDescriptor : fieldDescriptors) {
                if (fieldDescriptor.getType().name().equals("MESSAGE") && fieldDescriptor.getMessageType().getName().equals("CommonData")) {
                    responseMsgCommonDataIndexMap.put(cmd, fieldDescriptor.getName());
                    break;
                }
            }

        } catch (Exception e) {
            log.error("setMessage Error : {}", e.getMessage());
        }
    }

    /**
     * 获取command 对应的 class
     *
     * @param cmd
     * @return
     */
    public Class<? extends Message> getMessageClass(short cmd) {
        return messages.get(cmd);
    }

    /**
     * 获取 class 对应的 command id
     *
     * @param msgClass
     * @return
     */
    @SuppressWarnings("rawtypes")
    private short getMessageCommandID(Class msgClass) {
        if (commands.containsKey(msgClass)) {
            return commands.get(msgClass);
        } else {
            return -1;
        }
    }

    /**
     * 字节数组反序列化成Message对象
     *
     * @param bytes
     * @param command
     * @return
     */
    public Message deserializeBytesToMessage(byte[] bytes, short command) {
        if (command < 0) {
            log.error("command: 命令号错误{}", command);
            return null;
        }

        Class<? extends Message> cls = this.getMessageClass(command);
        var m = deserializeMethods.get(command);
        if (cls == null || m == null) {
            log.error("deserializeBytesToMessage: 查找UnserializeMethod failed , cmd={}", command);
            return null;
        }
        Message msg;
        try {
            msg = m.parseFrom(bytes);
            return msg;
        } catch (Exception e) {
            log.error("deserializeBytesToMessage e:", e);
            log.error("deserializeBytesToMessage : m.invoke error ： {}", e.getMessage());
            return null;
        }
    }

    /**
     * byte 数据封装成message对象
     *
     * @param data
     * @param command
     * @return
     */

    public Message buildRequestParams(byte[] data, short command) {
        if (data == null) {
            return null;
        }
        Message msg = this.deserializeBytesToMessage(data, command);
        if (msg == null) {
            return null;
        }

        Descriptor descriptor = msg.getDescriptorForType();
        FieldDescriptor field = descriptor.findFieldByName("commonParams");
        if (field != null) {
            RequestContext.setCommonParams((CommonParams) msg.getField(field));
        }
        RequestContext.setLocalClientParams(msg);
        return msg;
    }

    /**
     * json字符串封装成message对象
     *
     * @param jsonStr
     * @param command
     * @return
     */
    public Message buildRequestParams(String jsonStr, short command) {
        try {
            var method = newBuilderMethods.get(command);
            Message.Builder msg = method.get();
            JsonFormat.parser().ignoringUnknownFields().merge(jsonStr, msg);
            Descriptor descriptor = msg.getDescriptorForType();
            FieldDescriptor field = descriptor.findFieldByName("commonParams");
            if (field != null) {
                RequestContext.setCommonParams((CommonParams) msg.getField(field));
            }
            Message params = msg.build();
            RequestContext.setLocalClientParams(params);
            return params;
        } catch (Exception e) {
            log.error("buildRequestParams e:", e);
            return null;
        }
    }

    /**
     * buildResponse
     *
     * @param result
     * @param response
     */
    public void buildResponse(Result<Message> result, ByteBuf response) {

        if (result == null) {
            log.error("response result is null");
            return;
        }

        var message = result.getContent();
        var command = result.getCmd();
        if (command < 0) {
            log.error("response to byteBuff failed, command is empty, class = {}", message.getClass());
            return;
        }
        byte[] bytes = message.toByteArray();
        response.writeShortLE(command);
        response.writeIntLE(bytes.length);
        response.writeBytes(bytes);
    }

    /**
     * buildResponse
     *
     * @param response
     */
    public void buildResponse(short command, byte[] bytes, ByteBuf response) {
        response.writeShortLE(command);
        response.writeIntLE(bytes.length);
        response.writeBytes(bytes);
    }

    /**
     * 返回值是否包含commonData对象
     *
     * @param cmd
     * @return
     */
    public boolean isExistCommonData(short cmd) {
        return responseMsgCommonDataIndexMap.containsKey((short) (cmd + 1));
    }

    public <T extends Message> void processResult(Result<T> result, Consumer<CommonProto.CommonData.Builder> consumer) {
        var message = result.getContent();

        if (message == null || result.getCode() != ErrorCode.SUCCESS) {
            var messageBuilder = newBuilderMethods.get(result.getCmd()).get();
            // code 字段赋值
            Descriptor descriptor = messageBuilder.getDescriptorForType();
            FieldDescriptor field = descriptor.findFieldByName("code");
            if (field != null) {
                messageBuilder.setField(field, result.getCode());
            }
            message = (T) messageBuilder.build();
        }

        // 通过反射返回最新的transId
        if (responseMsgCommonDataIndexMap.containsKey(result.getCmd())) {
            // 查找commonData对象
            Descriptor descriptor = message.toBuilder().getDescriptorForType();
            FieldDescriptor commonDataFieldDescriptor = descriptor.findFieldByName(responseMsgCommonDataIndexMap.get(result.getCmd()));
            CommonProto.CommonData commonData = (CommonProto.CommonData) message.getAllFields().get(commonDataFieldDescriptor);

            if (commonData == null) {
                commonData = CommonProto.CommonData.newBuilder().build();
            }
            CommonProto.CommonData.Builder commonDataBuilder = commonData.toBuilder();
            consumer.accept(commonDataBuilder);
            var msgBuilder = message.toBuilder().setField(commonDataFieldDescriptor, commonDataBuilder.build());
            message = (T) msgBuilder.build();
        }
        result.setContent(message);
    }
}













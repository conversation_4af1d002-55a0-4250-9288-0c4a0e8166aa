package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.dao.dynamodb.model.FrozeDevice;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.FrozeDeviceDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.USER_SET_ACCOUNT_STATE)
public class GMUserSetAccountState extends AbstractGMProcessor {

    @Resource
    private UserDao userDao;
    @Resource
    private FrozeDeviceDao frozeDeviceDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private List<Long> userIds;
        private int type;           // type == 1 封设备，
        private int days;           // days > 0 按天数封 (=0永久封)
        private short state;        // 0解封, 1封号
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        short state = request.getState();
        int days = request.getDays();
        int type = request.getType();
        List<Long> userIdList = request.getUserIds();
        for (Long userId : userIdList) {
            User user = userDao.getByUserId(userId);
            if (user != null) {
                user.setAccountStatus(state);
                if (state == 1) {
                    long tm = 0;
                    if (days > 0) {
                        tm = DateUtils.getUnixTime() + days * 86400L;;
                    }
                    if (type == 1) {
                        if (!StringUtils.isEmpty(user.getDeviceId())) {
                            FrozeDevice frozeDevice = new FrozeDevice();
                            frozeDevice.setDeviceId(user.getDeviceId());
                            frozeDevice.setFromUserId(user.getUserId());
                            frozeDevice.setTm(tm);
                            frozeDeviceDao.insert(frozeDevice);
                        }
                    }
                    user.setFrozeTime(tm);
                } else {
                    if (type == 1) {
                        // 解封设备ID
                        FrozeDevice frozeDevice = frozeDeviceDao.getItem(user.getDeviceId());
                        if (frozeDevice != null) {
                            frozeDeviceDao.delete(frozeDevice);
                        }
                    }
                    user.setFrozeTime(0L);
                }

                userDao.updateAccountStatus(user);
            }
        }
        return this.success();
    }
}

package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.QUERY_USER_SIMPLE)
public class GMQueryUserSimpleInfo extends AbstractGMProcessor {

    @Resource
    private UserDao userDao;
    @Resource
    private UserExtendDao userExtendDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private List<Long> userIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    private static class GMResponse {
        private Map<String, Map<String, Object>> users;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        List<Long> userIds = request.getUserIds();
        List<User> userList = userDao.getUserList(userIds);
        Map<Long, UserExtend> userExtendMap = userExtendDao.getByUserIds(userIds);
        Map<String, Map<String, Object>> userMap = new HashMap<>();
        for (User user : userList) {
            Map<String, Object> userData = new HashMap<>();
            userData.put("channelId", user.getChannelId());
            userData.put("serverId", 0);
            userData.put("registerTime", user.getCreateTimestamp());
            userData.put("packageId", "");         // 官网分包id
            userData.put("serverOpenTime", 0);                      // 开服时间
            userData.put("chapterId", userExtendMap.get(user.getUserId()).getChapterId());
            userMap.put(user.getUserId().toString(), userData);
        }
        return GMResponse.builder().users(userMap).build();

    }
}

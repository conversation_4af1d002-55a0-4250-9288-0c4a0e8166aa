package com.dxx.game.modules.user.support;

import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.GameConstant;
import com.dxx.game.consts.TaskType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;
import com.dxx.game.modules.activity.service.PowerService;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.guild.service.GuildService;
import com.dxx.game.modules.pvp.support.BattleUnitCache;
import com.dxx.game.modules.rank.support.RankHelper;
import com.dxx.game.modules.task.model.TaskProcess;
import com.dxx.game.modules.task.service.TaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: lsc
 * @createDate: 2025/6/6
 * @description:
 */
@Slf4j
@Component
public class PowerSupport {

    @Resource
    private BattleUnitCache formationCache;
    @Resource
    private UserDao userDao;
    @Resource
    private CommonService commonService;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private GuildService guildService;
    @Resource
    private PowerService powerService;
    @Resource
    private SevenDayTaskService sevenDayTaskService;
    @Resource
    private TaskService taskService;

    public long getPower(long userId) {
        return getPower(formationCache.getBattleUnit(userId, GameConstant.FORMATION_TYPE_CHAPTER));
    }

    private long getPower(CommonProto.BattleUnitDto unit) {
        long power = getPowerByUnit(unit);
        User user = userDao.getByUserId(unit.getUserId());
        String key = RankHelper.getPowerRankKey(user.getServerId());
        RankHelper.commit(key, user.getUserId(), power);
        return power;
    }

    public long getPowerByUnit(CommonProto.BattleUnitDto unit) {
        /*
        BattleProto.RpcPowerReq.Builder req = BattleProto.RpcPowerReq.newBuilder();
        req.setUnit(unit);
        BattleProto.RpcPowerResp resp = battleService.power(req.build());
        if(resp.getCode() != ErrorCode.SUCCESS) {
            log.error("getPower code: {}", resp.getCode());
            return 0L;
        }
        return resp.getResult();
        */
        return 0L;
    }

    public long syncPower(long userId, String reason){
        User user = userDao.getByUserId(userId);
        UserExtend userExtend = userExtendDao.getByUserId(user.getUserId());

        long power = this.getPower(user.getUserId());
        long maxPower = userInfoRedisDao.updatePower(userId, power);

        log.info("[USER_POWER]USER_ID:{},POWER:{},REASON:{}",user.getUserId(),power,reason);
        guildService.updateGuildPower(user.getUserId(), power);

        // 排名活动
        powerService.syncPower(user, maxPower);

        // 成就
        taskService.updateTask(userExtend.getUserId(), TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_MAX_POWER, maxPower));

        // 新手7日活动任务
        SevenDayTaskProcess sevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_MAX_POWER, maxPower);
        sevenDayTaskService.updateTask(userExtend.getUserId(), sevenDayTaskProcess);

        return maxPower;
    }

}

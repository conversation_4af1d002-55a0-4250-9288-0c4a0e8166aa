// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tower.proto

package com.dxx.game.dto;

public final class TowerProto {
  private TowerProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TowerChallengeRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerChallengeRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>uint32 configId = 2;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * <pre>
   *CMD PackageId=13001 爬塔-挑战
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerChallengeRequest}
   */
  public static final class TowerChallengeRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerChallengeRequest)
      TowerChallengeRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerChallengeRequest.newBuilder() to construct.
    private TowerChallengeRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerChallengeRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerChallengeRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerChallengeRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              configId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerChallengeRequest.class, com.dxx.game.dto.TowerProto.TowerChallengeRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int CONFIGID_FIELD_NUMBER = 2;
    private int configId_;
    /**
     * <code>uint32 configId = 2;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (configId_ != 0) {
        output.writeUInt32(2, configId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerChallengeRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerChallengeRequest other = (com.dxx.game.dto.TowerProto.TowerChallengeRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getConfigId()
          != other.getConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerChallengeRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13001 爬塔-挑战
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerChallengeRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerChallengeRequest)
        com.dxx.game.dto.TowerProto.TowerChallengeRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerChallengeRequest.class, com.dxx.game.dto.TowerProto.TowerChallengeRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerChallengeRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        configId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerChallengeRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerChallengeRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerChallengeRequest build() {
        com.dxx.game.dto.TowerProto.TowerChallengeRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerChallengeRequest buildPartial() {
        com.dxx.game.dto.TowerProto.TowerChallengeRequest result = new com.dxx.game.dto.TowerProto.TowerChallengeRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.configId_ = configId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerChallengeRequest) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerChallengeRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerChallengeRequest other) {
        if (other == com.dxx.game.dto.TowerProto.TowerChallengeRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerChallengeRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerChallengeRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int configId_ ;
      /**
       * <code>uint32 configId = 2;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>uint32 configId = 2;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 configId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerChallengeRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerChallengeRequest)
    private static final com.dxx.game.dto.TowerProto.TowerChallengeRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerChallengeRequest();
    }

    public static com.dxx.game.dto.TowerProto.TowerChallengeRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerChallengeRequest>
        PARSER = new com.google.protobuf.AbstractParser<TowerChallengeRequest>() {
      @java.lang.Override
      public TowerChallengeRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerChallengeRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerChallengeRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerChallengeRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerChallengeRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerChallengeResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerChallengeResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>uint32 configId = 3;</code>
     * @return The configId.
     */
    int getConfigId();

    /**
     * <pre>
     * 0:失败 1:成功
     * </pre>
     *
     * <code>uint32 result = 4;</code>
     * @return The result.
     */
    int getResult();

    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 5;</code>
     * @return The seed.
     */
    int getSeed();

    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getStartUnitsList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index);
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    int getStartUnitsCount();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> 
        getEndUnitsList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index);
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    int getEndUnitsCount();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList();
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */
    int getChapterGiftTimeCount();
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */
    boolean containsChapterGiftTime(
        int key);
    /**
     * Use {@link #getChapterGiftTimeMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Long>
    getChapterGiftTime();
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Long>
    getChapterGiftTimeMap();
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */

    long getChapterGiftTimeOrDefault(
        int key,
        long defaultValue);
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */

    long getChapterGiftTimeOrThrow(
        int key);
  }
  /**
   * <pre>
   *CMD PackageId=13002
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerChallengeResponse}
   */
  public static final class TowerChallengeResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerChallengeResponse)
      TowerChallengeResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerChallengeResponse.newBuilder() to construct.
    private TowerChallengeResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerChallengeResponse() {
      startUnits_ = java.util.Collections.emptyList();
      endUnits_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerChallengeResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerChallengeResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              configId_ = input.readUInt32();
              break;
            }
            case 32: {

              result_ = input.readUInt32();
              break;
            }
            case 40: {

              seed_ = input.readInt32();
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              startUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>();
                mutable_bitField0_ |= 0x00000002;
              }
              endUnits_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.CombatUnitDto.parser(), extensionRegistry));
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                chapterGiftTime_ = com.google.protobuf.MapField.newMapField(
                    ChapterGiftTimeDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
              chapterGiftTime__ = input.readMessage(
                  ChapterGiftTimeDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              chapterGiftTime_.getMutableMap().put(
                  chapterGiftTime__.getKey(), chapterGiftTime__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 8:
          return internalGetChapterGiftTime();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerChallengeResponse.class, com.dxx.game.dto.TowerProto.TowerChallengeResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int CONFIGID_FIELD_NUMBER = 3;
    private int configId_;
    /**
     * <code>uint32 configId = 3;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    public static final int RESULT_FIELD_NUMBER = 4;
    private int result_;
    /**
     * <pre>
     * 0:失败 1:成功
     * </pre>
     *
     * <code>uint32 result = 4;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    public static final int SEED_FIELD_NUMBER = 5;
    private int seed_;
    /**
     * <pre>
     * 随机种子
     * </pre>
     *
     * <code>int32 seed = 5;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    public static final int STARTUNITS_FIELD_NUMBER = 6;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_;
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getStartUnitsOrBuilderList() {
      return startUnits_;
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    @java.lang.Override
    public int getStartUnitsCount() {
      return startUnits_.size();
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
      return startUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗开始单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
        int index) {
      return startUnits_.get(index);
    }

    public static final int ENDUNITS_FIELD_NUMBER = 7;
    private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_;
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
        getEndUnitsOrBuilderList() {
      return endUnits_;
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    @java.lang.Override
    public int getEndUnitsCount() {
      return endUnits_.size();
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
      return endUnits_.get(index);
    }
    /**
     * <pre>
     * 战斗结束单位信息
     * </pre>
     *
     * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
        int index) {
      return endUnits_.get(index);
    }

    public static final int CHAPTERGIFTTIME_FIELD_NUMBER = 8;
    private static final class ChapterGiftTimeDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Long>newDefaultInstance(
                  com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeResponse_ChapterGiftTimeEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.UINT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Long> chapterGiftTime_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
    internalGetChapterGiftTime() {
      if (chapterGiftTime_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ChapterGiftTimeDefaultEntryHolder.defaultEntry);
      }
      return chapterGiftTime_;
    }

    public int getChapterGiftTimeCount() {
      return internalGetChapterGiftTime().getMap().size();
    }
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */

    @java.lang.Override
    public boolean containsChapterGiftTime(
        int key) {
      
      return internalGetChapterGiftTime().getMap().containsKey(key);
    }
    /**
     * Use {@link #getChapterGiftTimeMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Long> getChapterGiftTime() {
      return getChapterGiftTimeMap();
    }
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Long> getChapterGiftTimeMap() {
      return internalGetChapterGiftTime().getMap();
    }
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */
    @java.lang.Override

    public long getChapterGiftTimeOrDefault(
        int key,
        long defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetChapterGiftTime().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 章节礼包购买时限
     * </pre>
     *
     * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
     */
    @java.lang.Override

    public long getChapterGiftTimeOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetChapterGiftTime().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (configId_ != 0) {
        output.writeUInt32(3, configId_);
      }
      if (result_ != 0) {
        output.writeUInt32(4, result_);
      }
      if (seed_ != 0) {
        output.writeInt32(5, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        output.writeMessage(6, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        output.writeMessage(7, endUnits_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetChapterGiftTime(),
          ChapterGiftTimeDefaultEntryHolder.defaultEntry,
          8);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, configId_);
      }
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, result_);
      }
      if (seed_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, seed_);
      }
      for (int i = 0; i < startUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, startUnits_.get(i));
      }
      for (int i = 0; i < endUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, endUnits_.get(i));
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> entry
           : internalGetChapterGiftTime().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
        chapterGiftTime__ = ChapterGiftTimeDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(8, chapterGiftTime__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerChallengeResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerChallengeResponse other = (com.dxx.game.dto.TowerProto.TowerChallengeResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getConfigId()
          != other.getConfigId()) return false;
      if (getResult()
          != other.getResult()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (!getStartUnitsList()
          .equals(other.getStartUnitsList())) return false;
      if (!getEndUnitsList()
          .equals(other.getEndUnitsList())) return false;
      if (!internalGetChapterGiftTime().equals(
          other.internalGetChapterGiftTime())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + getSeed();
      if (getStartUnitsCount() > 0) {
        hash = (37 * hash) + STARTUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getStartUnitsList().hashCode();
      }
      if (getEndUnitsCount() > 0) {
        hash = (37 * hash) + ENDUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getEndUnitsList().hashCode();
      }
      if (!internalGetChapterGiftTime().getMap().isEmpty()) {
        hash = (37 * hash) + CHAPTERGIFTTIME_FIELD_NUMBER;
        hash = (53 * hash) + internalGetChapterGiftTime().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerChallengeResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13002
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerChallengeResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerChallengeResponse)
        com.dxx.game.dto.TowerProto.TowerChallengeResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 8:
            return internalGetChapterGiftTime();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 8:
            return internalGetMutableChapterGiftTime();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerChallengeResponse.class, com.dxx.game.dto.TowerProto.TowerChallengeResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerChallengeResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getStartUnitsFieldBuilder();
          getEndUnitsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        configId_ = 0;

        result_ = 0;

        seed_ = 0;

        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          startUnitsBuilder_.clear();
        }
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          endUnitsBuilder_.clear();
        }
        internalGetMutableChapterGiftTime().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerChallengeResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerChallengeResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerChallengeResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerChallengeResponse build() {
        com.dxx.game.dto.TowerProto.TowerChallengeResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerChallengeResponse buildPartial() {
        com.dxx.game.dto.TowerProto.TowerChallengeResponse result = new com.dxx.game.dto.TowerProto.TowerChallengeResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.configId_ = configId_;
        result.result_ = result_;
        result.seed_ = seed_;
        if (startUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            startUnits_ = java.util.Collections.unmodifiableList(startUnits_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.startUnits_ = startUnits_;
        } else {
          result.startUnits_ = startUnitsBuilder_.build();
        }
        if (endUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            endUnits_ = java.util.Collections.unmodifiableList(endUnits_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.endUnits_ = endUnits_;
        } else {
          result.endUnits_ = endUnitsBuilder_.build();
        }
        result.chapterGiftTime_ = internalGetChapterGiftTime();
        result.chapterGiftTime_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerChallengeResponse) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerChallengeResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerChallengeResponse other) {
        if (other == com.dxx.game.dto.TowerProto.TowerChallengeResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getSeed() != 0) {
          setSeed(other.getSeed());
        }
        if (startUnitsBuilder_ == null) {
          if (!other.startUnits_.isEmpty()) {
            if (startUnits_.isEmpty()) {
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureStartUnitsIsMutable();
              startUnits_.addAll(other.startUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.startUnits_.isEmpty()) {
            if (startUnitsBuilder_.isEmpty()) {
              startUnitsBuilder_.dispose();
              startUnitsBuilder_ = null;
              startUnits_ = other.startUnits_;
              bitField0_ = (bitField0_ & ~0x00000001);
              startUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getStartUnitsFieldBuilder() : null;
            } else {
              startUnitsBuilder_.addAllMessages(other.startUnits_);
            }
          }
        }
        if (endUnitsBuilder_ == null) {
          if (!other.endUnits_.isEmpty()) {
            if (endUnits_.isEmpty()) {
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureEndUnitsIsMutable();
              endUnits_.addAll(other.endUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.endUnits_.isEmpty()) {
            if (endUnitsBuilder_.isEmpty()) {
              endUnitsBuilder_.dispose();
              endUnitsBuilder_ = null;
              endUnits_ = other.endUnits_;
              bitField0_ = (bitField0_ & ~0x00000002);
              endUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEndUnitsFieldBuilder() : null;
            } else {
              endUnitsBuilder_.addAllMessages(other.endUnits_);
            }
          }
        }
        internalGetMutableChapterGiftTime().mergeFrom(
            other.internalGetChapterGiftTime());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerChallengeResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerChallengeResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int configId_ ;
      /**
       * <code>uint32 configId = 3;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>uint32 configId = 3;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 configId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }

      private int result_ ;
      /**
       * <pre>
       * 0:失败 1:成功
       * </pre>
       *
       * <code>uint32 result = 4;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       * 0:失败 1:成功
       * </pre>
       *
       * <code>uint32 result = 4;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0:失败 1:成功
       * </pre>
       *
       * <code>uint32 result = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int seed_ ;
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 5;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 5;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机种子
       * </pre>
       *
       * <code>int32 seed = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        
        seed_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> startUnits_ =
        java.util.Collections.emptyList();
      private void ensureStartUnitsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          startUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(startUnits_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> startUnitsBuilder_;

      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getStartUnitsList() {
        if (startUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(startUnits_);
        } else {
          return startUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public int getStartUnitsCount() {
        if (startUnitsBuilder_ == null) {
          return startUnits_.size();
        } else {
          return startUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);
        } else {
          return startUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.set(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder setStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder addStartUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (startUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStartUnitsIsMutable();
          startUnits_.add(index, value);
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder addStartUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder addStartUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          startUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder addAllStartUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, startUnits_);
          onChanged();
        } else {
          startUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder clearStartUnits() {
        if (startUnitsBuilder_ == null) {
          startUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          startUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public Builder removeStartUnits(int index) {
        if (startUnitsBuilder_ == null) {
          ensureStartUnitsIsMutable();
          startUnits_.remove(index);
          onChanged();
        } else {
          startUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getStartUnitsOrBuilder(
          int index) {
        if (startUnitsBuilder_ == null) {
          return startUnits_.get(index);  } else {
          return startUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getStartUnitsOrBuilderList() {
        if (startUnitsBuilder_ != null) {
          return startUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(startUnits_);
        }
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder() {
        return getStartUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addStartUnitsBuilder(
          int index) {
        return getStartUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗开始单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto startUnits = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getStartUnitsBuilderList() {
        return getStartUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getStartUnitsFieldBuilder() {
        if (startUnitsBuilder_ == null) {
          startUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  startUnits_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          startUnits_ = null;
        }
        return startUnitsBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> endUnits_ =
        java.util.Collections.emptyList();
      private void ensureEndUnitsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          endUnits_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CombatUnitDto>(endUnits_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> endUnitsBuilder_;

      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto> getEndUnitsList() {
        if (endUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(endUnits_);
        } else {
          return endUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public int getEndUnitsCount() {
        if (endUnitsBuilder_ == null) {
          return endUnits_.size();
        } else {
          return endUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto getEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);
        } else {
          return endUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.set(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder setEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder addEndUnits(com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto value) {
        if (endUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEndUnitsIsMutable();
          endUnits_.add(index, value);
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder addEndUnits(
          com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder addEndUnits(
          int index, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder builderForValue) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          endUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder addAllEndUnits(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CombatUnitDto> values) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, endUnits_);
          onChanged();
        } else {
          endUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder clearEndUnits() {
        if (endUnitsBuilder_ == null) {
          endUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          endUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public Builder removeEndUnits(int index) {
        if (endUnitsBuilder_ == null) {
          ensureEndUnitsIsMutable();
          endUnits_.remove(index);
          onChanged();
        } else {
          endUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder getEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder getEndUnitsOrBuilder(
          int index) {
        if (endUnitsBuilder_ == null) {
          return endUnits_.get(index);  } else {
          return endUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
           getEndUnitsOrBuilderList() {
        if (endUnitsBuilder_ != null) {
          return endUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(endUnits_);
        }
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder() {
        return getEndUnitsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.CombatUnitDto.Builder addEndUnitsBuilder(
          int index) {
        return getEndUnitsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CombatUnitDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 战斗结束单位信息
       * </pre>
       *
       * <code>repeated .Proto.Common.CombatUnitDto endUnits = 7;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CombatUnitDto.Builder> 
           getEndUnitsBuilderList() {
        return getEndUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder> 
          getEndUnitsFieldBuilder() {
        if (endUnitsBuilder_ == null) {
          endUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CombatUnitDto, com.dxx.game.dto.CommonProto.CombatUnitDto.Builder, com.dxx.game.dto.CommonProto.CombatUnitDtoOrBuilder>(
                  endUnits_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          endUnits_ = null;
        }
        return endUnitsBuilder_;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Long> chapterGiftTime_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetChapterGiftTime() {
        if (chapterGiftTime_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ChapterGiftTimeDefaultEntryHolder.defaultEntry);
        }
        return chapterGiftTime_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetMutableChapterGiftTime() {
        onChanged();;
        if (chapterGiftTime_ == null) {
          chapterGiftTime_ = com.google.protobuf.MapField.newMapField(
              ChapterGiftTimeDefaultEntryHolder.defaultEntry);
        }
        if (!chapterGiftTime_.isMutable()) {
          chapterGiftTime_ = chapterGiftTime_.copy();
        }
        return chapterGiftTime_;
      }

      public int getChapterGiftTimeCount() {
        return internalGetChapterGiftTime().getMap().size();
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */

      @java.lang.Override
      public boolean containsChapterGiftTime(
          int key) {
        
        return internalGetChapterGiftTime().getMap().containsKey(key);
      }
      /**
       * Use {@link #getChapterGiftTimeMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long> getChapterGiftTime() {
        return getChapterGiftTimeMap();
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Long> getChapterGiftTimeMap() {
        return internalGetChapterGiftTime().getMap();
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */
      @java.lang.Override

      public long getChapterGiftTimeOrDefault(
          int key,
          long defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetChapterGiftTime().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */
      @java.lang.Override

      public long getChapterGiftTimeOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetChapterGiftTime().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearChapterGiftTime() {
        internalGetMutableChapterGiftTime().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */

      public Builder removeChapterGiftTime(
          int key) {
        
        internalGetMutableChapterGiftTime().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long>
      getMutableChapterGiftTime() {
        return internalGetMutableChapterGiftTime().getMutableMap();
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */
      public Builder putChapterGiftTime(
          int key,
          long value) {
        
        
        internalGetMutableChapterGiftTime().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 章节礼包购买时限
       * </pre>
       *
       * <code>map&lt;uint32, uint64&gt; chapterGiftTime = 8;</code>
       */

      public Builder putAllChapterGiftTime(
          java.util.Map<java.lang.Integer, java.lang.Long> values) {
        internalGetMutableChapterGiftTime().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerChallengeResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerChallengeResponse)
    private static final com.dxx.game.dto.TowerProto.TowerChallengeResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerChallengeResponse();
    }

    public static com.dxx.game.dto.TowerProto.TowerChallengeResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerChallengeResponse>
        PARSER = new com.google.protobuf.AbstractParser<TowerChallengeResponse>() {
      @java.lang.Override
      public TowerChallengeResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerChallengeResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerChallengeResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerChallengeResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerChallengeResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>uint32 configId = 2;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * <pre>
   *CMD PackageId=13003 爬塔-领奖
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerRewardRequest}
   */
  public static final class TowerRewardRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerRewardRequest)
      TowerRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerRewardRequest.newBuilder() to construct.
    private TowerRewardRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerRewardRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerRewardRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerRewardRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              configId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerRewardRequest.class, com.dxx.game.dto.TowerProto.TowerRewardRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int CONFIGID_FIELD_NUMBER = 2;
    private int configId_;
    /**
     * <code>uint32 configId = 2;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (configId_ != 0) {
        output.writeUInt32(2, configId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerRewardRequest other = (com.dxx.game.dto.TowerProto.TowerRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getConfigId()
          != other.getConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13003 爬塔-领奖
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerRewardRequest)
        com.dxx.game.dto.TowerProto.TowerRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerRewardRequest.class, com.dxx.game.dto.TowerProto.TowerRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        configId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRewardRequest build() {
        com.dxx.game.dto.TowerProto.TowerRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRewardRequest buildPartial() {
        com.dxx.game.dto.TowerProto.TowerRewardRequest result = new com.dxx.game.dto.TowerProto.TowerRewardRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.configId_ = configId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerRewardRequest) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerRewardRequest other) {
        if (other == com.dxx.game.dto.TowerProto.TowerRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerRewardRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerRewardRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int configId_ ;
      /**
       * <code>uint32 configId = 2;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>uint32 configId = 2;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 configId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerRewardRequest)
    private static final com.dxx.game.dto.TowerProto.TowerRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerRewardRequest();
    }

    public static com.dxx.game.dto.TowerProto.TowerRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<TowerRewardRequest>() {
      @java.lang.Override
      public TowerRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerRewardRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>uint32 configId = 3;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * <pre>
   *CMD PackageId=13004
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerRewardResponse}
   */
  public static final class TowerRewardResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerRewardResponse)
      TowerRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerRewardResponse.newBuilder() to construct.
    private TowerRewardResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerRewardResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerRewardResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerRewardResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              configId_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerRewardResponse.class, com.dxx.game.dto.TowerProto.TowerRewardResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int CONFIGID_FIELD_NUMBER = 3;
    private int configId_;
    /**
     * <code>uint32 configId = 3;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (configId_ != 0) {
        output.writeUInt32(3, configId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerRewardResponse other = (com.dxx.game.dto.TowerProto.TowerRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getConfigId()
          != other.getConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13004
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerRewardResponse)
        com.dxx.game.dto.TowerProto.TowerRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerRewardResponse.class, com.dxx.game.dto.TowerProto.TowerRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        configId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRewardResponse build() {
        com.dxx.game.dto.TowerProto.TowerRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRewardResponse buildPartial() {
        com.dxx.game.dto.TowerProto.TowerRewardResponse result = new com.dxx.game.dto.TowerProto.TowerRewardResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.configId_ = configId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerRewardResponse) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerRewardResponse other) {
        if (other == com.dxx.game.dto.TowerProto.TowerRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerRewardResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerRewardResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int configId_ ;
      /**
       * <code>uint32 configId = 3;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>uint32 configId = 3;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 configId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerRewardResponse)
    private static final com.dxx.game.dto.TowerProto.TowerRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerRewardResponse();
    }

    public static com.dxx.game.dto.TowerProto.TowerRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<TowerRewardResponse>() {
      @java.lang.Override
      public TowerRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerRewardResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerRankRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerRankRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    int getPage();
  }
  /**
   * <pre>
   *CMD PackageId=13005 爬塔-分服排行榜
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerRankRequest}
   */
  public static final class TowerRankRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerRankRequest)
      TowerRankRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerRankRequest.newBuilder() to construct.
    private TowerRankRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerRankRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerRankRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerRankRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              page_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerRankRequest.class, com.dxx.game.dto.TowerProto.TowerRankRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      if (page_ != 0) {
        output.writeInt32(2, page_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerRankRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerRankRequest other = (com.dxx.game.dto.TowerProto.TowerRankRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getPage()
          != other.getPage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerRankRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13005 爬塔-分服排行榜
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerRankRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerRankRequest)
        com.dxx.game.dto.TowerProto.TowerRankRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerRankRequest.class, com.dxx.game.dto.TowerProto.TowerRankRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerRankRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        page_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerRankRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankRequest build() {
        com.dxx.game.dto.TowerProto.TowerRankRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankRequest buildPartial() {
        com.dxx.game.dto.TowerProto.TowerRankRequest result = new com.dxx.game.dto.TowerProto.TowerRankRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        result.page_ = page_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerRankRequest) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerRankRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerRankRequest other) {
        if (other == com.dxx.game.dto.TowerProto.TowerRankRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerRankRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerRankRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int page_ ;
      /**
       * <code>int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerRankRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerRankRequest)
    private static final com.dxx.game.dto.TowerProto.TowerRankRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerRankRequest();
    }

    public static com.dxx.game.dto.TowerProto.TowerRankRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerRankRequest>
        PARSER = new com.google.protobuf.AbstractParser<TowerRankRequest>() {
      @java.lang.Override
      public TowerRankRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerRankRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerRankRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerRankRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerRankRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerRankResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerRankResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.TowerRankDto> 
        getRankList();
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    com.dxx.game.dto.CommonProto.TowerRankDto getRank(int index);
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    int getRankCount();
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder> 
        getRankOrBuilderList();
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder getRankOrBuilder(
        int index);
  }
  /**
   * <pre>
   *CMD PackageId=13006
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerRankResponse}
   */
  public static final class TowerRankResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerRankResponse)
      TowerRankResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerRankResponse.newBuilder() to construct.
    private TowerRankResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerRankResponse() {
      rank_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerRankResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerRankResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rank_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TowerRankDto>();
                mutable_bitField0_ |= 0x00000001;
              }
              rank_.add(
                  input.readMessage(com.dxx.game.dto.CommonProto.TowerRankDto.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rank_ = java.util.Collections.unmodifiableList(rank_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerRankResponse.class, com.dxx.game.dto.TowerProto.TowerRankResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int RANK_FIELD_NUMBER = 3;
    private java.util.List<com.dxx.game.dto.CommonProto.TowerRankDto> rank_;
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.TowerRankDto> getRankList() {
      return rank_;
    }
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder> 
        getRankOrBuilderList() {
      return rank_;
    }
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    @java.lang.Override
    public int getRankCount() {
      return rank_.size();
    }
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TowerRankDto getRank(int index) {
      return rank_.get(index);
    }
    /**
     * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder getRankOrBuilder(
        int index) {
      return rank_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      for (int i = 0; i < rank_.size(); i++) {
        output.writeMessage(3, rank_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      for (int i = 0; i < rank_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, rank_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerRankResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerRankResponse other = (com.dxx.game.dto.TowerProto.TowerRankResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getRankList()
          .equals(other.getRankList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (getRankCount() > 0) {
        hash = (37 * hash) + RANK_FIELD_NUMBER;
        hash = (53 * hash) + getRankList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerRankResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13006
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerRankResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerRankResponse)
        com.dxx.game.dto.TowerProto.TowerRankResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerRankResponse.class, com.dxx.game.dto.TowerProto.TowerRankResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerRankResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRankFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        if (rankBuilder_ == null) {
          rank_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rankBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerRankResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankResponse build() {
        com.dxx.game.dto.TowerProto.TowerRankResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankResponse buildPartial() {
        com.dxx.game.dto.TowerProto.TowerRankResponse result = new com.dxx.game.dto.TowerProto.TowerRankResponse(this);
        int from_bitField0_ = bitField0_;
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        if (rankBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rank_ = java.util.Collections.unmodifiableList(rank_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rank_ = rank_;
        } else {
          result.rank_ = rankBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerRankResponse) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerRankResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerRankResponse other) {
        if (other == com.dxx.game.dto.TowerProto.TowerRankResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (rankBuilder_ == null) {
          if (!other.rank_.isEmpty()) {
            if (rank_.isEmpty()) {
              rank_ = other.rank_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankIsMutable();
              rank_.addAll(other.rank_);
            }
            onChanged();
          }
        } else {
          if (!other.rank_.isEmpty()) {
            if (rankBuilder_.isEmpty()) {
              rankBuilder_.dispose();
              rankBuilder_ = null;
              rank_ = other.rank_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankFieldBuilder() : null;
            } else {
              rankBuilder_.addAllMessages(other.rank_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerRankResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerRankResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.TowerRankDto> rank_ =
        java.util.Collections.emptyList();
      private void ensureRankIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rank_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TowerRankDto>(rank_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TowerRankDto, com.dxx.game.dto.CommonProto.TowerRankDto.Builder, com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder> rankBuilder_;

      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TowerRankDto> getRankList() {
        if (rankBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rank_);
        } else {
          return rankBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public int getRankCount() {
        if (rankBuilder_ == null) {
          return rank_.size();
        } else {
          return rankBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TowerRankDto getRank(int index) {
        if (rankBuilder_ == null) {
          return rank_.get(index);
        } else {
          return rankBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder setRank(
          int index, com.dxx.game.dto.CommonProto.TowerRankDto value) {
        if (rankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankIsMutable();
          rank_.set(index, value);
          onChanged();
        } else {
          rankBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder setRank(
          int index, com.dxx.game.dto.CommonProto.TowerRankDto.Builder builderForValue) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder addRank(com.dxx.game.dto.CommonProto.TowerRankDto value) {
        if (rankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankIsMutable();
          rank_.add(value);
          onChanged();
        } else {
          rankBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder addRank(
          int index, com.dxx.game.dto.CommonProto.TowerRankDto value) {
        if (rankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankIsMutable();
          rank_.add(index, value);
          onChanged();
        } else {
          rankBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder addRank(
          com.dxx.game.dto.CommonProto.TowerRankDto.Builder builderForValue) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.add(builderForValue.build());
          onChanged();
        } else {
          rankBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder addRank(
          int index, com.dxx.game.dto.CommonProto.TowerRankDto.Builder builderForValue) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder addAllRank(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.TowerRankDto> values) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rank_);
          onChanged();
        } else {
          rankBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder clearRank() {
        if (rankBuilder_ == null) {
          rank_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public Builder removeRank(int index) {
        if (rankBuilder_ == null) {
          ensureRankIsMutable();
          rank_.remove(index);
          onChanged();
        } else {
          rankBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TowerRankDto.Builder getRankBuilder(
          int index) {
        return getRankFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder getRankOrBuilder(
          int index) {
        if (rankBuilder_ == null) {
          return rank_.get(index);  } else {
          return rankBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder> 
           getRankOrBuilderList() {
        if (rankBuilder_ != null) {
          return rankBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rank_);
        }
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TowerRankDto.Builder addRankBuilder() {
        return getRankFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.TowerRankDto.getDefaultInstance());
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.TowerRankDto.Builder addRankBuilder(
          int index) {
        return getRankFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.TowerRankDto.getDefaultInstance());
      }
      /**
       * <code>repeated .Proto.Common.TowerRankDto rank = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TowerRankDto.Builder> 
           getRankBuilderList() {
        return getRankFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.dxx.game.dto.CommonProto.TowerRankDto, com.dxx.game.dto.CommonProto.TowerRankDto.Builder, com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder> 
          getRankFieldBuilder() {
        if (rankBuilder_ == null) {
          rankBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.dxx.game.dto.CommonProto.TowerRankDto, com.dxx.game.dto.CommonProto.TowerRankDto.Builder, com.dxx.game.dto.CommonProto.TowerRankDtoOrBuilder>(
                  rank_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rank_ = null;
        }
        return rankBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerRankResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerRankResponse)
    private static final com.dxx.game.dto.TowerProto.TowerRankResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerRankResponse();
    }

    public static com.dxx.game.dto.TowerProto.TowerRankResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerRankResponse>
        PARSER = new com.google.protobuf.AbstractParser<TowerRankResponse>() {
      @java.lang.Override
      public TowerRankResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerRankResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerRankResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerRankResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerRankResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerRankIndexRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerRankIndexRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   *CMD PackageId=13007 爬塔-获取排名
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerRankIndexRequest}
   */
  public static final class TowerRankIndexRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerRankIndexRequest)
      TowerRankIndexRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerRankIndexRequest.newBuilder() to construct.
    private TowerRankIndexRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerRankIndexRequest() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerRankIndexRequest();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerRankIndexRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.dxx.game.dto.CommonProto.CommonParams.Builder subBuilder = null;
              if (commonParams_ != null) {
                subBuilder = commonParams_.toBuilder();
              }
              commonParams_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonParams.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonParams_);
                commonParams_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerRankIndexRequest.class, com.dxx.game.dto.TowerProto.TowerRankIndexRequest.Builder.class);
    }

    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return commonParams_ != null;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return getCommonParams();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (commonParams_ != null) {
        output.writeMessage(1, getCommonParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (commonParams_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerRankIndexRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerRankIndexRequest other = (com.dxx.game.dto.TowerProto.TowerRankIndexRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerRankIndexRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13007 爬塔-获取排名
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerRankIndexRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerRankIndexRequest)
        com.dxx.game.dto.TowerProto.TowerRankIndexRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerRankIndexRequest.class, com.dxx.game.dto.TowerProto.TowerRankIndexRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerRankIndexRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankIndexRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerRankIndexRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankIndexRequest build() {
        com.dxx.game.dto.TowerProto.TowerRankIndexRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankIndexRequest buildPartial() {
        com.dxx.game.dto.TowerProto.TowerRankIndexRequest result = new com.dxx.game.dto.TowerProto.TowerRankIndexRequest(this);
        if (commonParamsBuilder_ == null) {
          result.commonParams_ = commonParams_;
        } else {
          result.commonParams_ = commonParamsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerRankIndexRequest) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerRankIndexRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerRankIndexRequest other) {
        if (other == com.dxx.game.dto.TowerProto.TowerRankIndexRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerRankIndexRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerRankIndexRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return commonParamsBuilder_ != null || commonParams_ != null;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
          onChanged();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (commonParams_ != null) {
            commonParams_ =
              com.dxx.game.dto.CommonProto.CommonParams.newBuilder(commonParams_).mergeFrom(value).buildPartial();
          } else {
            commonParams_ = value;
          }
          onChanged();
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        if (commonParamsBuilder_ == null) {
          commonParams_ = null;
          onChanged();
        } else {
          commonParams_ = null;
          commonParamsBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerRankIndexRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerRankIndexRequest)
    private static final com.dxx.game.dto.TowerProto.TowerRankIndexRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerRankIndexRequest();
    }

    public static com.dxx.game.dto.TowerProto.TowerRankIndexRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerRankIndexRequest>
        PARSER = new com.google.protobuf.AbstractParser<TowerRankIndexRequest>() {
      @java.lang.Override
      public TowerRankIndexRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerRankIndexRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerRankIndexRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerRankIndexRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerRankIndexRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TowerRankIndexResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Tower.TowerRankIndexResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>uint32 index = 3;</code>
     * @return The index.
     */
    int getIndex();
  }
  /**
   * <pre>
   *CMD PackageId=13008
   * </pre>
   *
   * Protobuf type {@code Proto.Tower.TowerRankIndexResponse}
   */
  public static final class TowerRankIndexResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Proto.Tower.TowerRankIndexResponse)
      TowerRankIndexResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TowerRankIndexResponse.newBuilder() to construct.
    private TowerRankIndexResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TowerRankIndexResponse() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TowerRankIndexResponse();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TowerRankIndexResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              com.dxx.game.dto.CommonProto.CommonData.Builder subBuilder = null;
              if (commonData_ != null) {
                subBuilder = commonData_.toBuilder();
              }
              commonData_ = input.readMessage(com.dxx.game.dto.CommonProto.CommonData.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(commonData_);
                commonData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              index_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TowerProto.TowerRankIndexResponse.class, com.dxx.game.dto.TowerProto.TowerRankIndexResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return commonData_ != null;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return getCommonData();
    }

    public static final int INDEX_FIELD_NUMBER = 3;
    private int index_;
    /**
     * <code>uint32 index = 3;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (commonData_ != null) {
        output.writeMessage(2, getCommonData());
      }
      if (index_ != 0) {
        output.writeUInt32(3, index_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (commonData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, index_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TowerProto.TowerRankIndexResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TowerProto.TowerRankIndexResponse other = (com.dxx.game.dto.TowerProto.TowerRankIndexResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getIndex()
          != other.getIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TowerProto.TowerRankIndexResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *CMD PackageId=13008
     * </pre>
     *
     * Protobuf type {@code Proto.Tower.TowerRankIndexResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Tower.TowerRankIndexResponse)
        com.dxx.game.dto.TowerProto.TowerRankIndexResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TowerProto.TowerRankIndexResponse.class, com.dxx.game.dto.TowerProto.TowerRankIndexResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TowerProto.TowerRankIndexResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        if (commonDataBuilder_ == null) {
          commonData_ = null;
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }
        index_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TowerProto.internal_static_Proto_Tower_TowerRankIndexResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankIndexResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TowerProto.TowerRankIndexResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankIndexResponse build() {
        com.dxx.game.dto.TowerProto.TowerRankIndexResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TowerProto.TowerRankIndexResponse buildPartial() {
        com.dxx.game.dto.TowerProto.TowerRankIndexResponse result = new com.dxx.game.dto.TowerProto.TowerRankIndexResponse(this);
        result.code_ = code_;
        if (commonDataBuilder_ == null) {
          result.commonData_ = commonData_;
        } else {
          result.commonData_ = commonDataBuilder_.build();
        }
        result.index_ = index_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TowerProto.TowerRankIndexResponse) {
          return mergeFrom((com.dxx.game.dto.TowerProto.TowerRankIndexResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TowerProto.TowerRankIndexResponse other) {
        if (other == com.dxx.game.dto.TowerProto.TowerRankIndexResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.dxx.game.dto.TowerProto.TowerRankIndexResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.dxx.game.dto.TowerProto.TowerRankIndexResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return commonDataBuilder_ != null || commonData_ != null;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
          onChanged();
        } else {
          commonDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
          onChanged();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (commonData_ != null) {
            commonData_ =
              com.dxx.game.dto.CommonProto.CommonData.newBuilder(commonData_).mergeFrom(value).buildPartial();
          } else {
            commonData_ = value;
          }
          onChanged();
        } else {
          commonDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        if (commonDataBuilder_ == null) {
          commonData_ = null;
          onChanged();
        } else {
          commonData_ = null;
          commonDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private int index_ ;
      /**
       * <code>uint32 index = 3;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <code>uint32 index = 3;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 index = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Proto.Tower.TowerRankIndexResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Tower.TowerRankIndexResponse)
    private static final com.dxx.game.dto.TowerProto.TowerRankIndexResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TowerProto.TowerRankIndexResponse();
    }

    public static com.dxx.game.dto.TowerProto.TowerRankIndexResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TowerRankIndexResponse>
        PARSER = new com.google.protobuf.AbstractParser<TowerRankIndexResponse>() {
      @java.lang.Override
      public TowerRankIndexResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TowerRankIndexResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TowerRankIndexResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TowerRankIndexResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TowerProto.TowerRankIndexResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerChallengeRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerChallengeRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerChallengeResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerChallengeResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerChallengeResponse_ChapterGiftTimeEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerChallengeResponse_ChapterGiftTimeEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerRewardResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerRankRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerRankRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerRankResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerRankResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerRankIndexRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerRankIndexRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Tower_TowerRankIndexResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Proto_Tower_TowerRankIndexResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013tower.proto\022\013Proto.Tower\032\014common.proto" +
      "\"[\n\025TowerChallengeRequest\0220\n\014commonParam" +
      "s\030\001 \001(\0132\032.Proto.Common.CommonParams\022\020\n\010c" +
      "onfigId\030\002 \001(\r\"\357\002\n\026TowerChallengeResponse" +
      "\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Pro" +
      "to.Common.CommonData\022\020\n\010configId\030\003 \001(\r\022\016" +
      "\n\006result\030\004 \001(\r\022\014\n\004seed\030\005 \001(\005\022/\n\nstartUni" +
      "ts\030\006 \003(\0132\033.Proto.Common.CombatUnitDto\022-\n" +
      "\010endUnits\030\007 \003(\0132\033.Proto.Common.CombatUni" +
      "tDto\022Q\n\017chapterGiftTime\030\010 \003(\01328.Proto.To" +
      "wer.TowerChallengeResponse.ChapterGiftTi" +
      "meEntry\0326\n\024ChapterGiftTimeEntry\022\013\n\003key\030\001" +
      " \001(\r\022\r\n\005value\030\002 \001(\004:\0028\001\"X\n\022TowerRewardRe" +
      "quest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Comm" +
      "on.CommonParams\022\020\n\010configId\030\002 \001(\r\"c\n\023Tow" +
      "erRewardResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommon" +
      "Data\030\002 \001(\0132\030.Proto.Common.CommonData\022\020\n\010" +
      "configId\030\003 \001(\r\"R\n\020TowerRankRequest\0220\n\014co" +
      "mmonParams\030\001 \001(\0132\032.Proto.Common.CommonPa" +
      "rams\022\014\n\004page\030\002 \001(\005\"y\n\021TowerRankResponse\022" +
      "\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Prot" +
      "o.Common.CommonData\022(\n\004rank\030\003 \003(\0132\032.Prot" +
      "o.Common.TowerRankDto\"I\n\025TowerRankIndexR" +
      "equest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Com" +
      "mon.CommonParams\"c\n\026TowerRankIndexRespon" +
      "se\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.P" +
      "roto.Common.CommonData\022\r\n\005index\030\003 \001(\rB\036\n" +
      "\020com.dxx.game.dtoB\nTowerProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Tower_TowerChallengeRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Tower_TowerChallengeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerChallengeRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ConfigId", });
    internal_static_Proto_Tower_TowerChallengeResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Tower_TowerChallengeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerChallengeResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "ConfigId", "Result", "Seed", "StartUnits", "EndUnits", "ChapterGiftTime", });
    internal_static_Proto_Tower_TowerChallengeResponse_ChapterGiftTimeEntry_descriptor =
      internal_static_Proto_Tower_TowerChallengeResponse_descriptor.getNestedTypes().get(0);
    internal_static_Proto_Tower_TowerChallengeResponse_ChapterGiftTimeEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerChallengeResponse_ChapterGiftTimeEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Tower_TowerRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Tower_TowerRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ConfigId", });
    internal_static_Proto_Tower_TowerRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Tower_TowerRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "ConfigId", });
    internal_static_Proto_Tower_TowerRankRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Tower_TowerRankRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerRankRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Page", });
    internal_static_Proto_Tower_TowerRankResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Tower_TowerRankResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerRankResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Rank", });
    internal_static_Proto_Tower_TowerRankIndexRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Tower_TowerRankIndexRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerRankIndexRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Tower_TowerRankIndexResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Tower_TowerRankIndexResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Proto_Tower_TowerRankIndexResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Index", });
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

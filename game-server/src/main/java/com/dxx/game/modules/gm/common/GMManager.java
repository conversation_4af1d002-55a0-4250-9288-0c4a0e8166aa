package com.dxx.game.modules.gm.common;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
public class GMManager {
    private final Map<Integer, AbstractGMProcessor> gmFunctionMap = new HashMap<>();

    // 使用 volatile 确保 instance 的可见性和防止指令重排
    private static volatile GMManager instance;

    // 私有化构造函数，防止外部实例化
    private GMManager() {}

    /**
     * 获取 GMManager 单例实例
     *
     * @return GMManager 实例
     */
    public static GMManager getInstance() {
        if (instance == null) { // 第一次检查
            synchronized (GMManager.class) {
                if (instance == null) { // 第二次检查
                    instance = new GMManager();
                }
            }
        }
        return instance;
    }

    /**
     * 注册 GM 命令
     *
     * @param cmd  GM 命令 ID
     * @param bean 对应的 GM 命令处理类
     * @param <E>  继承自 BaseGMFunction 的泛型
     */
    public <E extends AbstractGMProcessor> void registerBean(int cmd, E bean) {
        if (cmd <= 0) {
            return;
        }
        this.gmFunctionMap.put(cmd, bean);
    }

    /**
     * 获取对应的 GM 命令处理类
     *
     * @param cmd GM 命令 ID
     * @return 对应的 GM 命令处理类
     */
    public AbstractGMProcessor getBaseGMFunction(int cmd) {
        return this.gmFunctionMap.get(cmd);
    }
}


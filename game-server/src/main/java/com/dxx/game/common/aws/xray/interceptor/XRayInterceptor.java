package com.dxx.game.common.aws.xray.interceptor;

import com.amazonaws.xray.spring.aop.BaseAbstractXRayInterceptor;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * https://github.com/aws/aws-xray-sdk-java/issues/45
 */
@Aspect
@Component
@Conditional(XRayEnabledCondition.class)
public class XRayInterceptor extends BaseAbstractXRayInterceptor {

    @Override
    @Pointcut("@within(com.amazonaws.xray.spring.aop.XRayEnabled)")
    public void xrayEnabledClasses() {
        // just a pointcut
    }
}

package com.dxx.game.common.channel.wechatminigame.model;

import lombok.Data;

/**
 * @author: lsc
 * @createDate: 2023/8/21
 * @description:
 */
@Data
public class WeChatMiniGameJsApiConfig {
    // 是否启用
    private boolean open;
    // appId
    private String appId;
    // AppSecret
    private String appSecret;
    // 商户ID
    private String mchId;
    // 证书序列号 -- 生成方式 ( openssl x509 -in apiclient_cert.pem -noout -serial )
    private String serialNo;
    // 私钥字符串
    private String privateKey;
    // api v3 密钥
    private String apiV3Key;
    // 下单地址
    private String doOrderUrl;
    // 获取 access_token 地址
    private String reqAccessTokenUrl;
    // 获取 请求登录Code url
    private String reqCodeUrl;
    // 支付静态页面 url
    private String[] payStaticUrl;
    private String uploadUrl;
    private String qrCodeUrl;
}

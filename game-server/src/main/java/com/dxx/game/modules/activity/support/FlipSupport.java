package com.dxx.game.modules.activity.support;

import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.eventflipcard.FlipBaseEntity;
import com.dxx.game.config.entity.eventflipcard.FlipDynamicLayerEntity;
import com.dxx.game.config.entity.eventflipcard.FlipMapEntity;
import com.dxx.game.dao.dynamodb.model.activity.Flip;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.FlipProto;
import com.dxx.game.modules.activity.consts.FlipGridType;
import com.dxx.game.modules.common.service.RandomService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.reward.service.DropService;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FlipSupport {
    public static final int WIDTH = 8;
    public static final int HEIGHT = 8;
    public static final int MAP_SIZE = WIDTH * HEIGHT;

    public static final int[] LEFT = {-1, 0};
    public static final int[] RIGHT = {1, 0};
    public static final int[] UP = {0, -1};
    public static final int[] DOWN = {0, 1};
    public static final int[] LEFT_UP = {-1, -1};
    public static final int[] RIGHT_UP = {1, -1};
    public static final int[] LEFT_DOWN = {-1, 1};
    public static final int[] RIGHT_DOWN = {1, 1};
    public static final int[][] DIRECTIONS = {LEFT, RIGHT, UP, DOWN, LEFT_UP, RIGHT_UP, LEFT_DOWN, RIGHT_DOWN};

    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private RandomService randomService;
    @Autowired
    private DropService dropService;

    /**
     * 随机一个坐标
     * @return
     */
    public int randomIndex() {
        int index = RandomUtil.nextInt(MAP_SIZE);
        return index;
    }

    private List<FlipMapEntity> getFlipMapsByType(int type) {
        List<FlipMapEntity> list = gameConfigManager.getEventFlipCardConfig().getFlipMap().values().stream().filter(m -> m.getType() == type).collect(Collectors.toList());
        return list;
    }

    /**
     * 下个地图
     * @param isFirst
     * @param model
     * @param baseEntity
     * @return
     */
    public FlipMapEntity nextMap(boolean isFirst, Flip model, FlipBaseEntity baseEntity) {
        if (isFirst) {
            int rndMapId = RandomUtil.randomElement(baseEntity.getInitialSeed());
            FlipMapEntity mapEntity = gameConfigManager.getEventFlipCardConfig().getFlipMapEntity(rndMapId);
            model.getFlip().setMapId(mapEntity.getID());
            return mapEntity;
        }

        FlipMapEntity jumpMapEntity = checkJumpMap(model);
        if (null != jumpMapEntity) {
            model.getFlip().setJumpMapId(jumpMapEntity.getID());
            return jumpMapEntity;
        }

        Collection<FlipMapEntity> entities = getFlipMapsByType(1);
        FlipMapEntity lastMap = Iterables.getLast(entities);

        int nextMapId = model.getFlip().getMapId() + 1;
        FlipMapEntity nextMap = gameConfigManager.getEventFlipCardConfig().getFlipMap().get(nextMapId);
        if (nextMap == null || nextMap.getType() != 1) {
            int rndMapId = RandomUtil.randomElement(baseEntity.getInitialSeed());
            nextMap = gameConfigManager.getEventFlipCardConfig().getFlipMapEntity(rndMapId);;
            nextMapId = nextMap.getID();
            model.getFlip().setMapId(nextMapId);
        }

        // 最后一张图重置到第一张地图
        if (model.getFlip().getMapId() == lastMap.getID()) {
            nextMapId  = Iterables.getFirst(entities, null).getID();
        }

        nextMap = gameConfigManager.getEventFlipCardConfig().getFlipMapEntity(nextMapId);
        model.getFlip().setMapId(nextMap.getID());
        model.getFlip().setJumpMapId(0);
        return nextMap;
    }

    private FlipMapEntity checkJumpMap(Flip model) {
        // 查找符合条件的
        int mapType = 1;

        int accSteps = model.getFlip().getAccSteps();
        int clue = model.getFlip().getClue();
        for (FlipDynamicLayerEntity dynamicLayerEntity : gameConfigManager.getEventFlipCardConfig().getFlipDynamicLayer().values()) {
            int min = dynamicLayerEntity.getComsumRange().get(0);
            int max = dynamicLayerEntity.getComsumRange().get(1);
            if (accSteps < min || accSteps > max)
                continue;

            if (clue < dynamicLayerEntity.getMinClue()) {
                mapType = 2;    // 补强库（线索太少了走这个）
                break;
            }

            if (clue > dynamicLayerEntity.getMaxClue()) {
                mapType = 3;    // 补弱库（线索太多了走这个）
                break;
            }
        }

        if (mapType == 2 || mapType == 3) {
            final int jumpMapType = mapType;
            List<FlipMapEntity> pool = getFlipMapsByType(mapType);
            FlipMapEntity mapEntity = RandomUtil.randomElement(pool);
            return mapEntity;
        }

        return null;
    }

    /**
     * 所有石头道具
     * @param flipBaseEntity
     * @return
     */
    public List<Integer> getStones(FlipBaseEntity flipBaseEntity) {
        List<Integer> result = Lists.newArrayList(flipBaseEntity.getFlipStones());
        return result;
    }

    /**
     * 累计奖励信息
     * @param model
     * @param flipBaseEntity
     * @return
     */
    public List<FlipProto.AccClueRewardDto> buildAccRewardList(Flip model, FlipBaseEntity flipBaseEntity) {
        List<FlipProto.AccClueRewardDto> result = Lists.newArrayListWithCapacity(flipBaseEntity.getBigReward().size());
        // 累计奖励
        FlipProto.AccClueRewardDto.Builder accRewardBuilder = FlipProto.AccClueRewardDto.newBuilder();
        for (List<Integer> list : flipBaseEntity.getBigReward()) {
            accRewardBuilder.clear();
            int need = list.get(0);
            int dropId = list.get(1);
            List<List<Integer>> reward = dropService.dropRewardsConfig(dropId);

            List<CommonProto.RewardDto> rewardDtos = CommonHelper.buildRewardDtoList(reward);
            int state = model.getFlip().getAcc().contains(need) ? 1 : (model.getFlip().getClue() >= need ? 2 : 0);

            accRewardBuilder.setNeed(need);
            accRewardBuilder.addAllRewardDto(rewardDtos);
            accRewardBuilder.setState(state);

            result.add(accRewardBuilder.build());
        }

        return result;
    }

    /**
     * 配置数据
     * @param baseEntity
     * @return
     */
    public FlipProto.FlipConfigDto buildConfigDto(FlipBaseEntity baseEntity) {
        FlipProto.FlipConfigDto.Builder result = FlipProto.FlipConfigDto.newBuilder();
        result.setStepItemId(baseEntity.getStepItem());
        result.setClueItemId(baseEntity.getClueItem());
        result.setStepPrice(baseEntity.getStepPrice());
        result.addAllStones(getStones(baseEntity));
        result.setAllShowItemId(baseEntity.getAllShowItem());
        return result.build();
    }

    public List<FlipProto.FlipGridDto> buildGridList(Flip model) {
        List<FlipProto.FlipGridDto> result = Lists.newArrayList();
        FlipProto.FlipGridDto.Builder gridBuilder = FlipProto.FlipGridDto.newBuilder();
        for (Flip.Grid grid : model.getFlip().getMap()) {
            FlipProto.FlipGridDto gridDto = buildGrid(grid, gridBuilder);
            result.add(gridDto);
        }

        return result;
    }

    public FlipProto.FlipGridDto buildGrid(Flip.Grid grid, FlipProto.FlipGridDto.Builder gridBuilder) {
        if (gridBuilder != null)
            gridBuilder.clear();
        else
            gridBuilder = FlipProto.FlipGridDto.newBuilder();

        gridBuilder.setIndex(grid.getIdx());
        gridBuilder.setStatus(grid.getStatus());

        // 展示的格子显示类型
        if (!grid.isHidden()) {
            gridBuilder.setType(grid.getType());

            // 奖励
            List<Integer> rewards = grid.getRewards();
            if (rewards != null && !rewards.isEmpty()) {
                gridBuilder.setRewardDto(CommonHelper.buildRewardDto(rewards));
            }
        }

        return gridBuilder.build();
    }

    /**
     * 创建地图
     */
    public void createMap(Integer lastClueIndex, Flip model, boolean isFirst, Map<FlipGridType, Integer> accStones) {
        // 线索数
        int clue = model.getFlip().getClue();
        // 基础配置
        FlipBaseEntity baseEntity = getBaseEntity(model.getActivityId());
        // 地图配置
        FlipMapEntity mapEntity = nextMap(isFirst, model, baseEntity);
        // 格子类型配置数量
        Map<FlipGridType, Integer> gridsNumMap = initGridsNumMap(model, mapEntity, accStones, baseEntity);
        // 初始化格子列表
        List<Flip.Grid> map = initGridsList();
        // 开始格子
        int origin = initStartGrid(map, isFirst, lastClueIndex, gridsNumMap);
        // 线索格子
        initClueGrid(map, origin, mapEntity);
        // 填充剩余格子
        fillGrid(map, gridsNumMap, baseEntity, mapEntity);
        // 保存地图
        model.getFlip().setMap(map);
    }

    // 填充剩余格子
    private void fillGrid(List<Flip.Grid> map, Map<FlipGridType, Integer> gridsNumMap, FlipBaseEntity baseEntity, FlipMapEntity mapEntity) {
        List<Flip.Grid> pool = map.stream().filter(grid -> grid.getType() == 0).collect(Collectors.toList());
        List<List<Integer>> specialLib = getItemLib(mapEntity);
        for (Map.Entry<FlipGridType, Integer> entry : gridsNumMap.entrySet()) {
            FlipGridType gridType = entry.getKey();
            int num = entry.getValue();
            for (int i = 0; i < num; i++) {
                int idx = RandomUtil.nextInt(pool.size());
                Flip.Grid grid = pool.get(idx);
                grid.setType(gridType.getType());

                // 步数格子
                if (gridType.isStepGrid()) {
                    grid.setRewards(Lists.newArrayList(baseEntity.getStepItem(), baseEntity.getStepGrid()));
                } else if (gridType.isSpecialGrid()) {
                    // 随机奖励
                    grid.setRewards(randomSpecialReward(mapEntity, specialLib));
                }

                pool.remove(idx);
            }
        }

        if (!pool.isEmpty()) {
            int normal1Count = gridsNumMap.getOrDefault(FlipGridType.eNormalGrid1, 0);
            int normal2Count = gridsNumMap.getOrDefault(FlipGridType.eNormalGrid2, 0);
            int normal3Count = gridsNumMap.getOrDefault(FlipGridType.eNormalGrid3, 0);
            int normal4Count = gridsNumMap.getOrDefault(FlipGridType.eNormalGrid4, 0);
            int minCount = Lists.newArrayList(normal1Count, normal2Count, normal3Count, normal4Count).stream().min(Integer::compare).get();
            FlipGridType numMinType = FlipGridType.eNormalGrid1;
            if (minCount == normal2Count)
                numMinType = FlipGridType.eNormalGrid2;
            else if (minCount == normal3Count)
                numMinType = FlipGridType.eNormalGrid3;
            else if (minCount == normal4Count)
                numMinType = FlipGridType.eNormalGrid4;

            for (Flip.Grid grid : pool) {
                grid.setType(numMinType.getType());
            }
        }
    }

    private List<Integer> randomSpecialReward(FlipMapEntity mapEntity, List<List<Integer>> lib) {
        List<Integer> result = Lists.newArrayList();

        if (lib.isEmpty()) {
            lib.addAll(getItemLib(mapEntity));
        }

        List<Integer> select = randomService.randConfig(lib);
        result.add(select.get(0));
        result.add(select.get(1));

        lib.remove(select);

        return result;
    }

    private List<List<Integer>> getItemLib(FlipMapEntity mapEntity) {
        List<List<Integer>> result = Lists.newArrayList();

        for (List<Integer> lib : mapEntity.getItemLib()) {
            int item = lib.get(0);
            int count = lib.get(1);
            int weight = lib.get(2);
            for (int i = 0; i < weight; i++) {
                List<Integer> list = Lists.newArrayList(item, count, 1);
                result.add(list);
            }
        }

        return result;
    }

    /**
     * 初始化线索格子
     */
    private void initClueGrid(List<Flip.Grid> map, int origin, FlipMapEntity mapEntity) {
        // 随机位置
        int idx = randomIndexByDistance(origin, mapEntity.getClueDistance(), map);
        Flip.Grid grid = map.get(idx);
        grid.setType(FlipGridType.eClue.getType());
    }

    /**
     * 根据距离随机位置
     * @param origin
     * @param distances
     * @param map
     * @return
     */
    private int randomIndexByDistance(int origin, List<Integer> distances, List<Flip.Grid> map) {
        Point originPoint = indexToPoint(origin);
        List<Point> pool = Lists.newArrayList();

        int tryMax = 100;
        int tryCount = 1;

        while (tryCount <= tryMax && pool.isEmpty()) {
            for (int x = 0; x < WIDTH; x++) {
                for (int y = 0; y < HEIGHT; y++) {
                    Point p = new Point(x, y);
                    int idx = pointToIndex(p);
                    Flip.Grid grid = map.get(idx);
                    if (grid.getType() > 0)
                        continue;
                    if (p.equals(originPoint))
                        continue;

                    int distance = distance(originPoint, p);
                    if (distance >= distances.get(0) && distance <= distances.get(1))
                        pool.add(p);
                }
            }
            if (pool.isEmpty()) {
                distances.set(0, Math.max(0, distances.get(0) - 1));
            }
            tryCount++;
        }

        if (pool.isEmpty())
            log.error("Flip randomIndexByDistance pool is empty origin:{} distances:{}", origin, distances);

        Point select = RandomUtil.randomElement(pool);
        return pointToIndex(select);
    }

    private int distance(Point p1, Point p2) {
//        return Math.abs(p1.x - p2.x) + Math.abs(p1.y - p2.y);
        return Math.max(Math.abs(p1.x - p2.x), Math.abs(p1.y - p2.y));
    }

    /**
     * 初始化格子列表
     * @return
     */
    private List<Flip.Grid> initGridsList() {
        List<Flip.Grid> result = Lists.newArrayList();
        for (int i = 0; i < MAP_SIZE; i++) {
            Flip.Grid grid = new Flip.Grid();
            grid.setIdx(i);
            grid.setStatus(Flip.Grid.STATUS_HIDDEN);
            result.add(grid);
        }

        return result;
    }

    /**
     * 初始化起始点
     * @param map
     * @param isFirst
     * @param lastClueIndex
     * @param gridsNumMap
     * @return
     */
    private int initStartGrid(List<Flip.Grid> map, boolean isFirst, Integer lastClueIndex, Map<FlipGridType, Integer> gridsNumMap) {
        List<Integer> idxs = Lists.newArrayList();
        // 第一次默认两个固定点
        if (isFirst) {
            idxs.add(pointToIndex(3, 4));
            idxs.add(pointToIndex(4, 4));
            // 上次的线索点
        } else if (Objects.nonNull(lastClueIndex) && lastClueIndex >= 0) {
            idxs.add(lastClueIndex);
        } else {
            idxs.add(randomIndex());
        }

        List<FlipGridType> pool = Lists.newArrayList();
        if (gridsNumMap.get(FlipGridType.eNormalGrid1) > 0) pool.add(FlipGridType.eNormalGrid1);
        if (gridsNumMap.get(FlipGridType.eNormalGrid2) > 0) pool.add(FlipGridType.eNormalGrid2);
        if (gridsNumMap.get(FlipGridType.eNormalGrid3) > 0) pool.add(FlipGridType.eNormalGrid3);
        if (gridsNumMap.get(FlipGridType.eNormalGrid4) > 0) pool.add(FlipGridType.eNormalGrid4);

        for (Integer idx : idxs) {
            Flip.Grid grid = map.get(idx);
            FlipGridType type = RandomUtil.randomElement(pool);
            grid.setType(type.getType());
            grid.setStatus(Flip.Grid.STATUS_SHOW);

            // 消耗一个数量
            costGridNum(gridsNumMap, type, 1);
        }

        return idxs.get(0);
    }

    private int pointToIndex(Point p) {
        return pointToIndex(p.x, p.y);
    }

    private int pointToIndex(int x, int y) {
        return x + y * WIDTH;
    }

    private Point indexToPoint(int index) {
        int x = index % WIDTH;
        int y = index / WIDTH;
        return new Point(x, y);
    }

    /**
     * 消耗配置数量
     * @param gridsNumMap
     * @param type
     * @param num
     */
    private void costGridNum(Map<FlipGridType, Integer> gridsNumMap, FlipGridType type, int num) {
        int cfgNum = gridsNumMap.get(type);
        cfgNum -= num;
        if (cfgNum > 0) {
            gridsNumMap.put(type, cfgNum);
        } else {
            gridsNumMap.remove(type);
        }
    }

    /**
     * 初始化格子数量Map
     */
    private Map<FlipGridType, Integer> initGridsNumMap(Flip model, FlipMapEntity mapEntity, Map<FlipGridType, Integer> accStones, FlipBaseEntity baseEntity) {
        Map<FlipGridType, Integer> result = Maps.newHashMap();

        // 普通格子
        int normal1Count = accStones.getOrDefault(FlipGridType.eNormalGrid1, 1);
        int normal2Count = accStones.getOrDefault(FlipGridType.eNormalGrid2, 1);
        int normal3Count = accStones.getOrDefault(FlipGridType.eNormalGrid3, 1);
        int normal4Count = accStones.getOrDefault(FlipGridType.eNormalGrid4, 1);
        int normalTotalCount = accStones.getOrDefault(FlipGridType.eNormalGrid1, 0)
                + accStones.getOrDefault(FlipGridType.eNormalGrid2, 0)
                + accStones.getOrDefault(FlipGridType.eNormalGrid3, 0)
                + accStones.getOrDefault(FlipGridType.eNormalGrid4, 0);

        // step1 用1/数量
        double normal1Quotient = 1d / normal1Count;
        double normal2Quotient = 1d / normal2Count;
        double normal3Quotient = 1d / normal3Count;
        double normal4Quotient = 1d / normal4Count;

        double minQuotient = Lists.newArrayList(normal1Quotient, normal2Quotient, normal3Quotient, normal4Quotient)
                .stream()
                .min(Double::compare)
                .get();

        double normal1Ratio = Math.min(baseEntity.getDynamicLimit(), normal1Quotient / minQuotient);
        double normal2Ratio = Math.min(baseEntity.getDynamicLimit(), normal2Quotient / minQuotient);
        double normal3Ratio = Math.min(baseEntity.getDynamicLimit(), normal3Quotient / minQuotient);
        double normal4Ratio = Math.min(baseEntity.getDynamicLimit(), normal4Quotient / minQuotient);

        // step2 取整
        int normal1Round = (int) (normal1Ratio * 10000);
        int normal2Round = (int) (normal2Ratio * 10000);
        int normal3Round = (int) (normal3Ratio * 10000);
        int normal4Round = (int) (normal4Ratio * 10000);
        int totalWeight = normal1Round + normal2Round + normal3Round + normal4Round;

        // 权重计算数量
        normal1Ratio = (double) normal1Round / totalWeight;
        normal2Ratio = (double) normal2Round / totalWeight;
        normal3Ratio = (double) normal3Round / totalWeight;
        normal4Ratio = (double) normal4Round / totalWeight;

        // 普通格配置总数
        int normalConfigNum = mapEntity.getNormalGrid1();

        int count1 = (int) (normal1Ratio * normalConfigNum);
        int count2 = (int) (normal2Ratio * normalConfigNum);
        int count3 = (int) (normal3Ratio * normalConfigNum);
        int count4 = (int) (normal4Ratio * normalConfigNum);

        int totalCount = count1 + count2 + count3 + count4;
        if (totalCount < normalConfigNum) {
            int fillCount = normalConfigNum - totalCount;
            int minCount = Lists.newArrayList(count1, count2, count3, count4).stream().min(Integer::compare).get();
            if (count1 == minCount)
                count1 += fillCount;
            else if (count2 == minCount)
                count2 += fillCount;
            else if (count3 == minCount)
                count3 += fillCount;
            else
                count4 += fillCount;
        }

        // 全收格子
        int accSteps = model.getFlip().getAccSteps();
        int dynamicCollectType = 0;
        for (FlipDynamicLayerEntity dynamicLayerEntity : gameConfigManager.getEventFlipCardConfig().getFlipDynamicLayer().values()) {
            int min = dynamicLayerEntity.getComsumRange().get(0);
            int max = dynamicLayerEntity.getComsumRange().get(1);
            if (accSteps < min || accSteps > max)
                continue;
            if (normalTotalCount <= dynamicLayerEntity.getMinGrid()) {
                dynamicCollectType = 1;
                break;
            }

            if (normalTotalCount >= dynamicLayerEntity.getMaxGrid1() && normalTotalCount < dynamicLayerEntity.getMaxGrid2()) {
                dynamicCollectType = 2;
                break;
            }

            if (normalTotalCount >= dynamicLayerEntity.getMaxGrid2()) {
                dynamicCollectType = 3;
                break;
            }
        }

        int collectGridCount = mapEntity.getCollectGrid();
        if (dynamicCollectType > 0) {
            switch (dynamicCollectType) {
                case 1:
                    // 减少一个全收集
                    if (mapEntity.getCollectGrid() > 0) {
                        collectGridCount--;
                        int minCount = Lists.newArrayList(count1, count2, count3, count4).stream().min(Integer::compare).get();
                        if (count1 == minCount)
                            count1++;
                        else if (count2 == minCount)
                            count2++;
                        else if (count3 == minCount)
                            count3++;
                        else
                            count4++;
                    }
                    break;
                case 2:
                    // 增加一个全收集
                    collectGridCount++;
                    int maxCount = Lists.newArrayList(count1, count2, count3, count4).stream().max(Integer::compare).get();
                    if (count1 == maxCount)
                        count1--;
                    else if (count2 == maxCount)
                        count2--;
                    else if (count3 == maxCount)
                        count3--;
                    else
                        count4--;
                    break;
                case 3:
                    // 全清全收集
                    collectGridCount = 0;
                    int minCount = Lists.newArrayList(count1, count2, count3, count4).stream().min(Integer::compare).get();
                    if (count1 == minCount)
                        count1 += mapEntity.getCollectGrid();
                    else if (count2 == minCount)
                        count2 += mapEntity.getCollectGrid();
                    else if (count3 == minCount)
                        count3 += mapEntity.getCollectGrid();
                    else
                        count4 += mapEntity.getCollectGrid();
                    break;
                default:
                    break;
            }
        }

        if (collectGridCount > 0) {
            List<FlipGridType> collectGridTypePool = Lists.newArrayList(FlipGridType.eCollectGrid1, FlipGridType.eCollectGrid2, FlipGridType.eCollectGrid3, FlipGridType.eCollectGrid4);
            List<FlipGridType> selectCollectTypes = Lists.newArrayList();
            if (collectGridCount < collectGridTypePool.size()) {
                for (int i = 0; i < collectGridCount; i++) {
                    selectCollectTypes.add(collectGridTypePool.remove(RandomUtil.nextInt(collectGridTypePool.size())));
                }
            } else if (collectGridCount == collectGridTypePool.size()) {
                selectCollectTypes = collectGridTypePool;
            } else {
                selectCollectTypes.addAll(collectGridTypePool);
                for (int i = 0; i < collectGridCount - collectGridTypePool.size(); i++) {
                    selectCollectTypes.add(collectGridTypePool.get(RandomUtil.nextInt(collectGridTypePool.size())));
                }
            }

            for (FlipGridType flipGridType : selectCollectTypes) {
                int count = result.getOrDefault(flipGridType, 0);
                result.put(flipGridType, count + 1);
            }
        }

        result.put(FlipGridType.eNormalGrid1, count1);
        result.put(FlipGridType.eNormalGrid2, count2);
        result.put(FlipGridType.eNormalGrid3, count3);
        result.put(FlipGridType.eNormalGrid4, count4);

        initGridsNum(result, FlipGridType.eSpecial, mapEntity);
        initGridsNum(result, FlipGridType.eStep, mapEntity);
        initGridsNum(result, FlipGridType.eBomb, mapEntity);
        initGridsNum(result, FlipGridType.eBombH, mapEntity);
        initGridsNum(result, FlipGridType.eBombV, mapEntity);
        initGridsNum(result, FlipGridType.eShow, mapEntity);
        initGridsNum(result, FlipGridType.ePartShow, mapEntity);

        return result;
    }

    /**
     * 初始化格子数量
     * @param numMap
     * @param type
     * @param mapEntity
     */
    private void initGridsNum(Map<FlipGridType, Integer> numMap, FlipGridType type, FlipMapEntity mapEntity) {
        int cfgNum = type.getCfgNum(mapEntity);
        if (cfgNum > 0)
            numMap.put(type, cfgNum);
    }

    public boolean isValidIndex(int index) {
        return index >= 0 && index < MAP_SIZE;
    }

    public boolean isValidPoint(int x, int y) {
        return x >= 0 && x < WIDTH && y >= 0 && y < HEIGHT;
    }

    /**
     * 获取周围8个方向的格子下标
     * @param index
     * @return
     */
    public List<Integer> getAroundGrids(int index) {
        if (!isValidIndex(index))
            return Collections.emptyList();

        Point p = indexToPoint(index);

        List<Integer> result = Lists.newArrayList();
        for (int[] dire : DIRECTIONS) {
            Point move = new Point(p.x + dire[0], p.y + dire[1]);
            int idx = pointToIndex(move);
            if (isValidPoint(move.x, move.y))
                result.add(idx);
        }

        return result;
    }

    public List<Integer> getHGrids(int index) {
        if (!isValidIndex(index))
            return Collections.emptyList();

        List<Integer> result = Lists.newArrayList();

        Point p = indexToPoint(index);

        for (int x = 0; x < WIDTH; x++) {
            Point targetPoint = new Point(x, p.y);
            if (!targetPoint.equals(p)) {
                int targetIndex = pointToIndex(targetPoint);
                result.add(targetIndex);
            }
        }

        return result;
    }

    public List<Integer> getVGrids(int index) {
        if (!isValidIndex(index))
            return Collections.emptyList();

        List<Integer> result = Lists.newArrayList();

        Point p = indexToPoint(index);

        for (int y = 0; y < HEIGHT; y++) {
            Point targetPoint = new Point(p.x, y);
            if (!targetPoint.equals(p)) {
                int targetIndex = pointToIndex(targetPoint);
                result.add(targetIndex);
            }
        }

        return result;
    }

    public List<Integer> getBombEffectGrids(int index, FlipGridType bombType) {
        if (!isValidIndex(index))
            return Collections.emptyList();

        List<Integer> result = Lists.newArrayList();

        switch (bombType) {
            case eBomb:
                result.addAll(getAroundGrids(index));
                break;
            case eBombH:
                result.addAll(getHGrids(index));
                break;
            case eBombV:
                result.addAll(getVGrids(index));
                break;
            default:
                break;
        }

        return result;
    }

    public FlipBaseEntity getBaseEntity(int eventId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(eventId);
        if(eventEntity == null) {
            log.error("cant find event config, eventId={}", eventId);
            return null;
        }

        int subType = eventEntity.getSubType1();

        return gameConfigManager.getEventFlipCardConfig().getFlipBaseEntity(subType);
    }
}

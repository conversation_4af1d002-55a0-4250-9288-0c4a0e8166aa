package com.dxx.game.common.redis;

import com.dxx.game.common.redis.factory.LettuceConnectionConfiguration;
import io.lettuce.core.ReadFrom;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * redis 从库-读节点
 * @author: lsc
 * @createDate: 2025/3/18
 * @description:
 */
@Slf4j
@Configuration
public class RedisReplicaConfig {

    @Bean(name = "replicaRedisProperties")
    public RedisProperties replicaRedisProperties() {
        return new RedisProperties();
    }

    @Bean(name = "replicaRedisConnectionFactory")
    public RedisConnectionFactory replicaRedisConnectionFactory(
            @Qualifier("replicaRedisProperties") RedisProperties redisProperties) {

        return new LettuceConnectionConfiguration(redisProperties).redisConnectionFactory(ReadFrom.REPLICA_PREFERRED);
    }

    @Bean(name = "replicaRedisTemplate")
    public RedisTemplate<String, String> replicaRedisTemplate(
            @Qualifier("replicaRedisConnectionFactory") RedisConnectionFactory connectionFactory) {

        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setValueSerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setHashValueSerializer(stringSerializer);

        template.afterPropertiesSet();
        return template;
    }

    @Bean(name = "stringReplicaRedisTemplate")
    public StringRedisTemplate stringReplicaRedisTemplate(
            @Qualifier("replicaRedisConnectionFactory") RedisConnectionFactory connectionFactory) {

        return new StringRedisTemplate(connectionFactory);
    }
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityDive_DiveBase {
    public ActivityDive_DiveBase(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        DiveItem = _buf.get("DiveItem").getAsInt();
        DivePrice = _buf.get("DivePrice").getAsInt();
        DivePrice1 = _buf.get("DivePrice1").getAsInt();
        DivePrice2 = _buf.get("DivePrice2").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("BuyLimit").getAsJsonArray(); BuyLimit = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BuyLimit.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("BuyLimit1").getAsJsonArray(); BuyLimit1 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BuyLimit1.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("BuyLimit2").getAsJsonArray(); BuyLimit2 = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BuyLimit2.add(_v0); }   }
        DefaultDive = _buf.get("DefaultDive").getAsInt();
        DivePropA = _buf.get("DivePropA").getAsInt();
        DivePropB = _buf.get("DivePropB").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("ExchangeItem").getAsJsonArray(); ExchangeItem = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  ExchangeItem.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("RewardProb").getAsJsonArray(); RewardProb = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  RewardProb.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("GenerateGap").getAsJsonArray(); GenerateGap = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  GenerateGap.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("BigReward").getAsJsonArray(); BigReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BigReward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("MailItems").getAsJsonArray(); MailItems = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailItems.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("MailItemsId").getAsJsonArray(); MailItemsId = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailItemsId.add(_v0); }   }
    }

    public static ActivityDive_DiveBase deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityDive_DiveBase(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 潜水道具
     */
    public final int DiveItem;
    /**
     * 水母道具售价，钻石
     */
    public final int DivePrice;
    /**
     * 炸弹道具售价，钻石
     */
    public final int DivePrice1;
    /**
     * 手电筒道具售价，钻石
     */
    public final int DivePrice2;
    /**
     * 根据钻石余量设定单次购买材料上限<br/>最小数,最大数,上限|...
     */
    public final java.util.List<String> BuyLimit;
    /**
     * 根据钻石余量设定单次购买材料上限<br/>最小数,最大数,上限|...
     */
    public final java.util.List<String> BuyLimit1;
    /**
     * 根据钻石余量设定单次购买材料上限<br/>最小数,最大数,上限|...
     */
    public final java.util.List<String> BuyLimit2;
    /**
     * 初始给道具数量
     */
    public final int DefaultDive;
    /**
     * 清除列道具（手电筒）
     */
    public final int DivePropA;
    /**
     * 清除一片道具（炸弹）
     */
    public final int DivePropB;
    /**
     * 兑换物ID
     */
    public final java.util.List<Integer> ExchangeItem;
    /**
     * 气泡格子会出现奖励（DiveSpecial）的概率百分比<br/>1：标准<br/>2：补强库（奖励太少了走这个）<br/>3：补弱库（奖励太多了走这个）
     */
    public final java.util.List<Integer> RewardProb;
    /**
     * 生成的块直接的行数差范围<br/>（随到两次0之后，第三次固定不会随到0）
     */
    public final java.util.List<String> GenerateGap;
    /**
     * 累计深度奖励：<br/>累计深度，掉落表ID|
     */
    public final java.util.List<String> BigReward;
    /**
     * 活动结束时，以下这些道具兑换为金币<br/>道具ID，金币数量|
     */
    public final java.util.List<String> MailItems;
    /**
     * 道具转为金币邮件模板ID<br/>测试服|正式服
     */
    public final java.util.List<String> MailItemsId;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "DiveItem:" + DiveItem + ","
        + "DivePrice:" + DivePrice + ","
        + "DivePrice1:" + DivePrice1 + ","
        + "DivePrice2:" + DivePrice2 + ","
        + "BuyLimit:" + BuyLimit + ","
        + "BuyLimit1:" + BuyLimit1 + ","
        + "BuyLimit2:" + BuyLimit2 + ","
        + "DefaultDive:" + DefaultDive + ","
        + "DivePropA:" + DivePropA + ","
        + "DivePropB:" + DivePropB + ","
        + "ExchangeItem:" + ExchangeItem + ","
        + "RewardProb:" + RewardProb + ","
        + "GenerateGap:" + GenerateGap + ","
        + "BigReward:" + BigReward + ","
        + "MailItems:" + MailItems + ","
        + "MailItemsId:" + MailItemsId + ","
        + "}";
    }
}


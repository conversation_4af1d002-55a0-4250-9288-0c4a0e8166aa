[{"Id": 1001, "IsOpen": 1, "NoteID": "17001", "Type": 1, "srvType": 1, "SubType1": 1001, "SubType2": 0, "Param": [], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 101, "ShopGroupType": 101, "RankGroupType": 101, "ConsumeGroupType": 101, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": []}, {"Id": 2001, "IsOpen": 1, "NoteID": "17002", "Type": 2, "srvType": 1, "SubType1": 3001, "SubType2": 0, "Param": [], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 201, "ShopGroupType": 201, "RankGroupType": 201, "ConsumeGroupType": 201, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": []}, {"Id": 1002, "IsOpen": 1, "NoteID": "17001", "Type": 1, "srvType": 1, "SubType1": 1001, "SubType2": 0, "Param": [], "OpenType": 1, "OpenTime": 30, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 101, "ShopGroupType": 101, "RankGroupType": 101, "ConsumeGroupType": 101, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": []}, {"Id": 3001, "IsOpen": 1, "NoteID": "17001", "Type": 3, "srvType": 1, "SubType1": 4001, "SubType2": 0, "Param": [], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 301, "ShopGroupType": 301, "RankGroupType": 301, "ConsumeGroupType": 301, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": []}, {"Id": 4001, "IsOpen": 1, "NoteID": "17001", "Type": 4, "srvType": 1, "SubType1": 0, "SubType2": 0, "Param": ["4"], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 0, "ShopGroupType": 301, "RankGroupType": 301, "ConsumeGroupType": 401, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": ["t-20240918084307", "t-20240918084234"]}, {"Id": 5001, "IsOpen": 1, "NoteID": "17001", "Type": 5, "srvType": 1, "SubType1": 0, "SubType2": 0, "Param": ["4"], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 0, "ShopGroupType": 301, "RankGroupType": 301, "ConsumeGroupType": 501, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": ["t-20240918084307", "t-20240918084234"]}, {"Id": 6001, "IsOpen": 1, "NoteID": "17001", "Type": 6, "srvType": 1, "SubType1": 0, "SubType2": 0, "Param": ["4"], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 0, "ShopGroupType": 301, "RankGroupType": 301, "ConsumeGroupType": 601, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": ["t-20240918084307", "t-20240918084234"]}, {"Id": 7001, "IsOpen": 1, "NoteID": "17001", "Type": 7, "srvType": 1, "SubType1": 0, "SubType2": 0, "Param": [], "OpenType": 1, "OpenTime": 0, "DurationTime": 999, "EndTime": 0, "TaskGroupType": 0, "ShopGroupType": 0, "RankGroupType": 301, "ConsumeGroupType": 0, "MailTempId": ["t-20240918084307", "t-20240918084234"], "MailRoundTempId": ["t-20240918084307", "t-20240918084234"]}]
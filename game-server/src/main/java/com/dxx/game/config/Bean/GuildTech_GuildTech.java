
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class GuildTech_GuildTech {
    public GuildTech_GuildTech(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        level = _buf.get("level").getAsInt();
        nextID = _buf.get("nextID").getAsInt();
        advance = _buf.get("advance").getAsInt();
        nameID = _buf.get("nameID").getAsString();
        atlasID = _buf.get("atlasID").getAsInt();
        iconName = _buf.get("iconName").getAsString();
        Type = _buf.get("Type").getAsInt();
        Attributes = _buf.get("Attributes").getAsString();
        allAttributes = _buf.get("allAttributes").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("levelupCost").getAsJsonArray(); levelupCost = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  levelupCost.add(_v0); }   }
    }

    public static GuildTech_GuildTech deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.GuildTech_GuildTech(_buf);
    }

    /**
     * Id
     */
    public final int ID;
    /**
     * 等级
     */
    public final int level;
    /**
     * 下一等级ID <br/>满级为0
     */
    public final int nextID;
    /**
     * 等阶（显示用）
     */
    public final int advance;
    /**
     * 名称
     */
    public final String nameID;
    /**
     * 图集ID
     */
    public final int atlasID;
    /**
     * 图标
     */
    public final String iconName;
    /**
     * 属性类型区分大小段（1小阶段2大阶段）
     */
    public final int Type;
    /**
     * 基础属性
     */
    public final String Attributes;
    /**
     * 当前公会科技累计属性
     */
    public final String allAttributes;
    /**
     * 解锁消耗
     */
    public final java.util.List<String> levelupCost;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "level:" + level + ","
        + "nextID:" + nextID + ","
        + "advance:" + advance + ","
        + "nameID:" + nameID + ","
        + "atlasID:" + atlasID + ","
        + "iconName:" + iconName + ","
        + "Type:" + Type + ","
        + "Attributes:" + Attributes + ","
        + "allAttributes:" + allAttributes + ","
        + "levelupCost:" + levelupCost + ","
        + "}";
    }
}



//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Task_WeeklyActive {
    public Task_WeeklyActive(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Requirements = _buf.get("Requirements").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("FixReward").getAsJsonArray(); FixReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  FixReward.add(_v0); }   }
    }

    public static Task_WeeklyActive deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Task_WeeklyActive(_buf);
    }

    /**
     * id
     */
    public final int ID;
    /**
     * 活跃度要求
     */
    public final int Requirements;
    /**
     * 固定奖励
     */
    public final java.util.List<String> FixReward;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Requirements:" + Requirements + ","
        + "FixReward:" + FixReward + ","
        + "}";
    }
}


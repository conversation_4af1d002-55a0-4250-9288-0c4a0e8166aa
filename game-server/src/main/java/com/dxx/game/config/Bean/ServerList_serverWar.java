
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ServerList_serverWar {
    public ServerList_serverWar(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        desc = _buf.get("desc").getAsString();
        war1 = _buf.get("war1").getAsInt();
    }

    public static ServerList_serverWar deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ServerList_serverWar(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 描述
     */
    public final String desc;
    /**
     * war1战区范围（最大服务器＞）
     */
    public final int war1;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "desc:" + desc + ","
        + "war1:" + war1 + ","
        + "}";
    }
}


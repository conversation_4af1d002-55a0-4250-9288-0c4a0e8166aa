package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.dao.dynamodb.repository.RechargeOrderDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.QUERY_RECHARGE_ORDER)
public class GMQueryUserRechargeOrder extends AbstractGMProcessor {

    @Resource
    private RechargeOrderDao rechargeOrderDao;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long userId;
        private int pageIndex;
        private int pageSize;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        long userId = request.getUserId();
        int pageIndex = request.getPageIndex();
        int pageSize = request.getPageSize();
        Map<String, Object> queryData = rechargeOrderDao.getLimit(userId, pageIndex, pageSize);
        return this.success(queryData);
    }
}

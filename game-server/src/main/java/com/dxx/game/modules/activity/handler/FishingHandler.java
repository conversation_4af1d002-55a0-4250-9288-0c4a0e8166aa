package com.dxx.game.modules.activity.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.FishingProto;
import com.dxx.game.modules.activity.service.FishingService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class FishingHandler {
    @Autowired
    private FishingService fishingService;

    @ApiMethod(command = MsgReqCommand.FishingOnOpenRequest, name = "钓鱼活动-打开界面调用")
    public Result<FishingProto.FishingOnOpenResponse> onOpen(Message msg) {
        FishingProto.FishingOnOpenRequest params = (FishingProto.FishingOnOpenRequest)msg;
        return fishingService.onOpen(params);
    }

    @ApiMethod(command = MsgReqCommand.FishingCastRodRequest, name = "钓鱼活动-抛竿")
    public Result<FishingProto.FishingCastRodResponse> castRod(Message msg) {
        FishingProto.FishingCastRodRequest params = (FishingProto.FishingCastRodRequest)msg;
        return fishingService.castRodAction(params);
    }

    @ApiMethod(command = MsgReqCommand.FishingReelInRequest, name = "钓鱼活动-收竿")
    public Result<FishingProto.FishingReelInResponse> reelIn(Message msg) {
        FishingProto.FishingReelInRequest params = (FishingProto.FishingReelInRequest)msg;
        return fishingService.reelIn(params);
    }

    @ApiMethod(command = MsgReqCommand.FishingBuyBaitRequest, name = "钓鱼活动-购买鱼饵")
    public Result<FishingProto.FishingBuyBaitResponse> buyBait(Message msg) {
        FishingProto.FishingBuyBaitRequest params = (FishingProto.FishingBuyBaitRequest)msg;
        return fishingService.buyBait(params);
    }

    @ApiMethod(command = MsgReqCommand.FishingRebornRequest, name = "钓鱼活动-复活")
    public Result<FishingProto.FishingRebornResponse> reborn(Message msg) {
        FishingProto.FishingRebornRequest params = (FishingProto.FishingRebornRequest)msg;
        return fishingService.reborn(params);
    }
}

package com.dxx.game.modules.reward.processor;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.modules.guild.service.GuildService;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.GuildActiveReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.Expression;

import jakarta.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@Slf4j
@Component
public class GuildActiveProcessor implements RewardProcessor {

    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RedisLock redisLock;
    @Resource
    private LogService logService;
    @Resource
    private UserService userService;
    @Resource
    private GuildService guildService;

    @Override
    public RewardType getType() {
        return RewardType.GUILD_ACTIVE;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.NONE;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        int addCount = reward.getCount();
        if (addCount <= 0) {
            resultCode = ErrorCode.PARAMS_ERROR;
            log.error("add guild exp count error, value : {}", addCount);
        } else {
            GuildUser guildUser = guildUserDao.getByUserId(userId);
            if (guildUser == null || guildUser.getGuildId() == 0) {
                resultCode = ErrorCode.GUILD_JOIN_FIRST;
                log.error("user not join guild, userId:{}", userId);
            } else {
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild == null) {
                    resultCode = ErrorCode.GUILD_NOT_EXIST;
                    log.error("guild not exist, guildId:{}", guildUser.getGuildId());
                }
            }
        }
        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }

        GuildActiveReward reward = (GuildActiveReward)rewardAction.getReward();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        Guild guild = guildDao.getByGuildId(guildUser.getGuildId());

        if (DateUtils.getUnixTime() >= guild.getGuildDayActiveTime()) {
            // 跨天
            guild.setGuildDayActive(reward.getCount());
            guild.setGuildDayActiveTime(DateUtils.getSystemResetTime());

            Expression updateCondition = Expression.builder().expression("#guildDayActiveTime <> :value")
                    .putExpressionName("#guildDayActiveTime", "guildDayActiveTime")
                    .putExpressionValue(":value", DynamoDBConvertUtil.buildAttributeValue(DateUtils.getSystemResetTime())).build();
            guild.addUpdateCondition(updateCondition);

        } else {
            Expression updateGuildExpression = Expression.builder()
                    .expression("#guildActive = #guildActive + :guildActive, #guildDayActive = #guildDayActive + :guildDayActive")
                    .putExpressionName("#guildActive", "guildActive")
                    .putExpressionName("#guildDayActive", "guildDayActive")
                    .putExpressionValue(":guildActive", DynamoDBConvertUtil.buildAttributeValue(reward.getCount()))
                    .putExpressionValue(":guildDayActive", DynamoDBConvertUtil.buildAttributeValue(reward.getCount()))
                    .build();

            // 更新公会活跃度
            guild.addUpdateExpression(updateGuildExpression);
            guild.setGuildDayActive(guild.getGuildDayActive() + reward.getCount());

        }

        guild.setGuildActive(guild.getGuildActive() + reward.getCount());
        guildDao.updateGuildActive(guild);
        guildDao.updateGuildDayActive(guild);

        reward.setGuildActive(guild.getGuildActive());

        // 更新用户活跃度
        guildService.detectAcrossDaysData(guild, guildUser);
        guildUser.setActive(guildUser.getActive() + reward.getCount());
        guildUser.setDailyActive(guildUser.getDailyActive() + reward.getCount());
        guildUser.setWeeklyActive(guildUser.getWeeklyActive() + reward.getCount());

        guildUserDao.updateAllActive(guildUser);

        JSONObject logObj = new JSONObject();
        logObj.put("guildActive", guild.getGuildActive());
        logObj.put("userActive", guildUser.getActive());
        logObj.put("dailyActive", guild.getGuildDayActive());

        logService.sendBasic(userId, RequestContext.getCommand(), userService.getTransId(userId), logObj.toJSONString());

        RewardResult<GuildActiveReward> result = new RewardResult<>(this.getType());
        result.setActualCount(reward.getCount());
        result.setCurrent(reward);
        return result;
    }
}

[{"ID": 1, "ComsumRange": [0, 12], "MinClue": 0, "MaxClue": 99999, "MinGrid": 0, "MaxGrid1": 99999, "MaxGrid2": 99999}, {"ID": 2, "ComsumRange": [12, 24], "MinClue": 0, "MaxClue": 99999, "MinGrid": 0, "MaxGrid1": 99999, "MaxGrid2": 99999}, {"ID": 3, "ComsumRange": [24, 36], "MinClue": 0, "MaxClue": 99999, "MinGrid": 0, "MaxGrid1": 99999, "MaxGrid2": 99999}, {"ID": 4, "ComsumRange": [36, 60], "MinClue": 0, "MaxClue": 99999, "MinGrid": 0, "MaxGrid1": 99999, "MaxGrid2": 99999}, {"ID": 5, "ComsumRange": [60, 120], "MinClue": 0, "MaxClue": 99999, "MinGrid": 216, "MaxGrid1": 264, "MaxGrid2": 300}, {"ID": 6, "ComsumRange": [120, 180], "MinClue": 0, "MaxClue": 99999, "MinGrid": 324, "MaxGrid1": 396, "MaxGrid2": 450}, {"ID": 7, "ComsumRange": [180, 240], "MinClue": 0, "MaxClue": 99999, "MinGrid": 432, "MaxGrid1": 528, "MaxGrid2": 600}, {"ID": 8, "ComsumRange": [240, 300], "MinClue": 20, "MaxClue": 30, "MinGrid": 540, "MaxGrid1": 660, "MaxGrid2": 750}, {"ID": 9, "ComsumRange": [300, 360], "MinClue": 25, "MaxClue": 40, "MinGrid": 648, "MaxGrid1": 792, "MaxGrid2": 900}, {"ID": 10, "ComsumRange": [360, 480], "MinClue": 30, "MaxClue": 50, "MinGrid": 864, "MaxGrid1": 1056, "MaxGrid2": 1200}, {"ID": 11, "ComsumRange": [480, 600], "MinClue": 40, "MaxClue": 60, "MinGrid": 1080, "MaxGrid1": 1320, "MaxGrid2": 1500}, {"ID": 12, "ComsumRange": [600, 720], "MinClue": 50, "MaxClue": 70, "MinGrid": 1296, "MaxGrid1": 1584, "MaxGrid2": 1800}, {"ID": 13, "ComsumRange": [720, 840], "MinClue": 60, "MaxClue": 80, "MinGrid": 1512, "MaxGrid1": 1848, "MaxGrid2": 2100}, {"ID": 14, "ComsumRange": [840, 960], "MinClue": 70, "MaxClue": 90, "MinGrid": 1728, "MaxGrid1": 2112, "MaxGrid2": 2400}, {"ID": 15, "ComsumRange": [960, 1080], "MinClue": 80, "MaxClue": 100, "MinGrid": 1944, "MaxGrid1": 2376, "MaxGrid2": 2700}, {"ID": 16, "ComsumRange": [1080, 1200], "MinClue": 90, "MaxClue": 125, "MinGrid": 2160, "MaxGrid1": 2640, "MaxGrid2": 3000}, {"ID": 17, "ComsumRange": [1200, 1500], "MinClue": 100, "MaxClue": 150, "MinGrid": 2700, "MaxGrid1": 3300, "MaxGrid2": 3750}, {"ID": 18, "ComsumRange": [1500, 1800], "MinClue": 125, "MaxClue": 175, "MinGrid": 3240, "MaxGrid1": 3960, "MaxGrid2": 4500}, {"ID": 19, "ComsumRange": [1800, 2100], "MinClue": 150, "MaxClue": 200, "MinGrid": 3780, "MaxGrid1": 4620, "MaxGrid2": 5250}, {"ID": 20, "ComsumRange": [2100, 2400], "MinClue": 175, "MaxClue": 225, "MinGrid": 4320, "MaxGrid1": 5280, "MaxGrid2": 6000}, {"ID": 21, "ComsumRange": [2400, 2700], "MinClue": 200, "MaxClue": 250, "MinGrid": 4860, "MaxGrid1": 5940, "MaxGrid2": 6750}, {"ID": 22, "ComsumRange": [2700, 3000], "MinClue": 225, "MaxClue": 275, "MinGrid": 5400, "MaxGrid1": 6600, "MaxGrid2": 7500}, {"ID": 23, "ComsumRange": [3000, 3300], "MinClue": 250, "MaxClue": 300, "MinGrid": 5940, "MaxGrid1": 7260, "MaxGrid2": 8250}, {"ID": 24, "ComsumRange": [3300, 3600], "MinClue": 275, "MaxClue": 325, "MinGrid": 6480, "MaxGrid1": 7920, "MaxGrid2": 9000}, {"ID": 25, "ComsumRange": [3600, 3900], "MinClue": 300, "MaxClue": 350, "MinGrid": 7020, "MaxGrid1": 8580, "MaxGrid2": 9750}, {"ID": 26, "ComsumRange": [3900, 4200], "MinClue": 325, "MaxClue": 375, "MinGrid": 7560, "MaxGrid1": 9240, "MaxGrid2": 10500}, {"ID": 27, "ComsumRange": [4200, 4500], "MinClue": 350, "MaxClue": 400, "MinGrid": 8100, "MaxGrid1": 9900, "MaxGrid2": 11250}, {"ID": 28, "ComsumRange": [4500, 4800], "MinClue": 375, "MaxClue": 425, "MinGrid": 8640, "MaxGrid1": 10560, "MaxGrid2": 12000}, {"ID": 29, "ComsumRange": [4800, 5100], "MinClue": 400, "MaxClue": 450, "MinGrid": 9180, "MaxGrid1": 11220, "MaxGrid2": 12750}, {"ID": 30, "ComsumRange": [5100, 5400], "MinClue": 425, "MaxClue": 475, "MinGrid": 9720, "MaxGrid1": 11880, "MaxGrid2": 13500}, {"ID": 31, "ComsumRange": [5400, 5700], "MinClue": 450, "MaxClue": 500, "MinGrid": 10260, "MaxGrid1": 12540, "MaxGrid2": 14250}, {"ID": 32, "ComsumRange": [5700, 6000], "MinClue": 475, "MaxClue": 525, "MinGrid": 10800, "MaxGrid1": 13200, "MaxGrid2": 15000}, {"ID": 33, "ComsumRange": [6000, 6300], "MinClue": 500, "MaxClue": 550, "MinGrid": 11340, "MaxGrid1": 13860, "MaxGrid2": 15750}, {"ID": 34, "ComsumRange": [6300, 6600], "MinClue": 525, "MaxClue": 575, "MinGrid": 11880, "MaxGrid1": 14520, "MaxGrid2": 16500}, {"ID": 35, "ComsumRange": [6600, 6900], "MinClue": 550, "MaxClue": 600, "MinGrid": 12420, "MaxGrid1": 15180, "MaxGrid2": 17250}, {"ID": 36, "ComsumRange": [6900, 7200], "MinClue": 575, "MaxClue": 625, "MinGrid": 12960, "MaxGrid1": 15840, "MaxGrid2": 18000}, {"ID": 37, "ComsumRange": [7200, 7500], "MinClue": 600, "MaxClue": 650, "MinGrid": 13500, "MaxGrid1": 16500, "MaxGrid2": 18750}, {"ID": 38, "ComsumRange": [7500, 7800], "MinClue": 625, "MaxClue": 675, "MinGrid": 14040, "MaxGrid1": 17160, "MaxGrid2": 19500}, {"ID": 39, "ComsumRange": [7800, 8100], "MinClue": 650, "MaxClue": 700, "MinGrid": 14580, "MaxGrid1": 17820, "MaxGrid2": 20250}, {"ID": 40, "ComsumRange": [8100, 8400], "MinClue": 675, "MaxClue": 725, "MinGrid": 15120, "MaxGrid1": 18480, "MaxGrid2": 21000}, {"ID": 41, "ComsumRange": [8400, 8700], "MinClue": 700, "MaxClue": 750, "MinGrid": 15660, "MaxGrid1": 19140, "MaxGrid2": 21750}, {"ID": 42, "ComsumRange": [8700, 9000], "MinClue": 725, "MaxClue": 850, "MinGrid": 16200, "MaxGrid1": 19800, "MaxGrid2": 22500}, {"ID": 43, "ComsumRange": [9000, 9600], "MinClue": 750, "MaxClue": 850, "MinGrid": 17280, "MaxGrid1": 21120, "MaxGrid2": 24000}, {"ID": 44, "ComsumRange": [9600, 10200], "MinClue": 800, "MaxClue": 850, "MinGrid": 18360, "MaxGrid1": 22440, "MaxGrid2": 25500}, {"ID": 45, "ComsumRange": [10200, 10800], "MinClue": 850, "MaxClue": 950, "MinGrid": 19440, "MaxGrid1": 23760, "MaxGrid2": 27000}, {"ID": 46, "ComsumRange": [10800, 11400], "MinClue": 900, "MaxClue": 850, "MinGrid": 20520, "MaxGrid1": 25080, "MaxGrid2": 28500}, {"ID": 47, "ComsumRange": [11400, 12000], "MinClue": 950, "MaxClue": 1050, "MinGrid": 21600, "MaxGrid1": 26400, "MaxGrid2": 30000}, {"ID": 48, "ComsumRange": [12000, 12600], "MinClue": 1000, "MaxClue": 1100, "MinGrid": 22680, "MaxGrid1": 27720, "MaxGrid2": 31500}, {"ID": 49, "ComsumRange": [12600, 13200], "MinClue": 1050, "MaxClue": 1150, "MinGrid": 23760, "MaxGrid1": 29040, "MaxGrid2": 33000}, {"ID": 50, "ComsumRange": [13200, 13800], "MinClue": 1100, "MaxClue": 1200, "MinGrid": 24840, "MaxGrid1": 30360, "MaxGrid2": 34500}, {"ID": 51, "ComsumRange": [13800, 14400], "MinClue": 1150, "MaxClue": 1300, "MinGrid": 25920, "MaxGrid1": 31680, "MaxGrid2": 36000}, {"ID": 52, "ComsumRange": [14400, 15600], "MinClue": 1200, "MaxClue": 1400, "MinGrid": 28080, "MaxGrid1": 34320, "MaxGrid2": 39000}, {"ID": 53, "ComsumRange": [15600, 16800], "MinClue": 1300, "MaxClue": 1500, "MinGrid": 30240, "MaxGrid1": 36960, "MaxGrid2": 42000}, {"ID": 54, "ComsumRange": [16800, 18000], "MinClue": 1400, "MaxClue": 1600, "MinGrid": 32400, "MaxGrid1": 39600, "MaxGrid2": 45000}, {"ID": 55, "ComsumRange": [18000, 19200], "MinClue": 1500, "MaxClue": 1700, "MinGrid": 34560, "MaxGrid1": 42240, "MaxGrid2": 48000}, {"ID": 56, "ComsumRange": [19200, 20400], "MinClue": 1600, "MaxClue": 1800, "MinGrid": 36720, "MaxGrid1": 44880, "MaxGrid2": 51000}, {"ID": 57, "ComsumRange": [20400, 21600], "MinClue": 1700, "MaxClue": 1900, "MinGrid": 38880, "MaxGrid1": 47520, "MaxGrid2": 54000}, {"ID": 58, "ComsumRange": [21600, 22800], "MinClue": 1800, "MaxClue": 2000, "MinGrid": 41040, "MaxGrid1": 50160, "MaxGrid2": 57000}, {"ID": 59, "ComsumRange": [22800, 24000], "MinClue": 1900, "MaxClue": 2100, "MinGrid": 43200, "MaxGrid1": 52800, "MaxGrid2": 60000}, {"ID": 60, "ComsumRange": [24000, 25200], "MinClue": 2000, "MaxClue": 2200, "MinGrid": 45360, "MaxGrid1": 55440, "MaxGrid2": 63000}, {"ID": 61, "ComsumRange": [25200, 26400], "MinClue": 2100, "MaxClue": 2300, "MinGrid": 47520, "MaxGrid1": 58080, "MaxGrid2": 66000}, {"ID": 62, "ComsumRange": [26400, 27600], "MinClue": 2200, "MaxClue": 2400, "MinGrid": 49680, "MaxGrid1": 60720, "MaxGrid2": 69000}, {"ID": 63, "ComsumRange": [27600, 28800], "MinClue": 2300, "MaxClue": 2500, "MinGrid": 51840, "MaxGrid1": 63360, "MaxGrid2": 72000}, {"ID": 64, "ComsumRange": [28800, 30000], "MinClue": 2400, "MaxClue": 2600, "MinGrid": 54000, "MaxGrid1": 66000, "MaxGrid2": 75000}, {"ID": 65, "ComsumRange": [30000, 31200], "MinClue": 2500, "MaxClue": 2700, "MinGrid": 56160, "MaxGrid1": 68640, "MaxGrid2": 78000}, {"ID": 66, "ComsumRange": [31200, 32400], "MinClue": 2600, "MaxClue": 2800, "MinGrid": 58320, "MaxGrid1": 71280, "MaxGrid2": 81000}, {"ID": 67, "ComsumRange": [32400, 33600], "MinClue": 2700, "MaxClue": 2900, "MinGrid": 60480, "MaxGrid1": 73920, "MaxGrid2": 84000}, {"ID": 68, "ComsumRange": [33600, 34800], "MinClue": 2800, "MaxClue": 3000, "MinGrid": 62640, "MaxGrid1": 76560, "MaxGrid2": 87000}, {"ID": 69, "ComsumRange": [34800, 999999], "MinClue": 2900, "MaxClue": 999999, "MinGrid": 64800, "MaxGrid1": 79200, "MaxGrid2": 9999999}]
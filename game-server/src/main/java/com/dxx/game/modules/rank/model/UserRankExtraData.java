package com.dxx.game.modules.rank.model;

import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.UserProto;
import com.dxx.game.modules.user.model.UserInfoModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/1 16:39
 */
public class UserRankExtraData {
    @Getter
    @Setter
    private String nickName;

    @Getter
    @Setter
    private int avatar;

    @Getter
    @Setter
    private int avatarFrame;

    @Getter
    @Setter
    private int level;

    @Getter
    @Setter
    private long power;

    public static UserRankExtraData build(UserProto.PlayerInfoDto user) {
        UserRankExtraData data = new UserRankExtraData();
        data.setNickName(Optional.of(user.getNickName()).orElse(""));
        data.setAvatar(Optional.of(user.getAvatar()).orElse(0));
        data.setAvatarFrame(Optional.of(user.getAvatarFrame()).orElse(0));
        data.setPower(user.getPower());
        return data;
    }

    public static UserRankExtraData build(UserInfoModel user) {
        UserRankExtraData data = new UserRankExtraData();
        data.setNickName(Optional.of(user.getNickName()).orElse(""));
        data.setAvatar(Optional.of(user.getAvatar()).orElse(0));
        data.setAvatarFrame(Optional.of(user.getAvatarFrame()).orElse(0));
        data.setLevel(user.getLevel());
        data.setPower(user.getPower());
        return data;
    }
}

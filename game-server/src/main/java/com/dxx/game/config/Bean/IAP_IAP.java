
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_IAP {
    public IAP_IAP(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        IapId = _buf.get("IapId").getAsString();
        CNIapId = _buf.get("CNIapId").getAsString();
        CNMiniIapId = _buf.get("CNMiniIapId").getAsString();
        price = _buf.get("price").getAsFloat();
        CNprice = _buf.get("CNprice").getAsFloat();
        type = _buf.get("type").getAsInt();
        IapName = _buf.get("IapName").getAsString();
        title = _buf.get("title").getAsString();
        desc = _buf.get("desc").getAsString();
    }

    public static IAP_IAP deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_IAP(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 产品回调ID
     */
    public final String IapId;
    /**
     * 国内app产品回调ID
     */
    public final String CNIapId;
    /**
     * 国内小程序回调ID
     */
    public final String CNMiniIapId;
    /**
     * 价格<br/>真实货币单位为美元
     */
    public final float price;
    /**
     * 国内价格CNY(元)
     */
    public final float CNprice;
    /**
     * 0.消耗类型<br/>1.终身限购1次类型<br/>2.订阅类型
     */
    public final int type;
    /**
     * 商品名
     */
    public final String IapName;
    /**
     * 商品介绍多语言
     */
    public final String title;
    /**
     * 商品详情描述多语言
     */
    public final String desc;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "IapId:" + IapId + ","
        + "CNIapId:" + CNIapId + ","
        + "CNMiniIapId:" + CNMiniIapId + ","
        + "price:" + price + ","
        + "CNprice:" + CNprice + ","
        + "type:" + type + ","
        + "IapName:" + IapName + ","
        + "title:" + title + ","
        + "desc:" + desc + ","
        + "}";
    }
}


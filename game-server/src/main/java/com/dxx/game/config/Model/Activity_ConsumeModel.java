
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Activity_ConsumeModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Consume> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.Activity_Consume> _dataList;
    
    public Activity_ConsumeModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Consume>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Activity_Consume>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Activity_Consume _v;
            _v = com.dxx.game.config.Bean.Activity_Consume.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.Id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Activity_Consume> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.Activity_Consume> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Activity_Consume get(int key) { return _dataMap.get(key); }

}

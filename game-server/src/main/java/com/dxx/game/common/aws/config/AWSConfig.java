package com.dxx.game.common.aws.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2021/4/16 16:11
 */
@Configuration
public class AWSConfig {

    @Value("${aws.profile.name}")
    protected String PROFILE_NAME;
    @Value("${aws.credentials.accessKey}")
    protected String ACCESS_KEY;
    @Value("${aws.credentials.secretKey}")
    protected String SECRET_KEY;
    @Value("${aws.region.static}")
    protected String REGION;
    @Value("${aws.dynamodb.endpoint.url}")
    protected String DYNAMODB_END_POINT;
    @Value("${spring.application.env}")
    protected String ENV;
    @Value("${aws.opensearch.domain.endpoint}")
    protected String openSearchEndPoint;
    @Value("${aws.opensearch.user}")
    protected String openSearchUser;
    @Value("${aws.opensearch.password}")
    protected String openSearchPassword;
    @Value("${aws.opensearch.password_secret_name}")
    protected String openSearchSecretName;

    @Value("${aws.kinesis.stream.name.cheat.log}")
    private String kinesisStreamCheat;
    @Value("${aws.kinesis.stream.name.resource.log}")
    private String kinesisStreamResource;

    public static String KINESIS_STREAM_CHEAT;
    public static String KINESIS_STREAM_RESOURCE;
    public static String APP_ENV;
    public static String OPEN_SEARCH_DOMAIN_ENDPOINT;
    public static String OPEN_SEARCH_USER;
    public static String OPEN_SEARCH_PASSWORD;
    public static String OPEN_SEARCH_PASSWORD_SECRET_NAME;

    // profile
    protected ProfileCredentialsProvider profileCredentialsProvider;
    // access_key & secret
    protected AwsCredentialsProvider awsCredentialsProvider;

    @PostConstruct
    public void init() {
        KINESIS_STREAM_CHEAT = kinesisStreamCheat;
        KINESIS_STREAM_RESOURCE = kinesisStreamResource;
        OPEN_SEARCH_DOMAIN_ENDPOINT = openSearchEndPoint;
        OPEN_SEARCH_USER = openSearchUser;
        OPEN_SEARCH_PASSWORD = openSearchPassword;
        OPEN_SEARCH_PASSWORD_SECRET_NAME = openSearchSecretName;

        if (!StringUtils.isEmpty(PROFILE_NAME)) {
            profileCredentialsProvider = ProfileCredentialsProvider.create(PROFILE_NAME);
        }
        if (!StringUtils.isEmpty(ACCESS_KEY) && !StringUtils.isEmpty(SECRET_KEY)) {
            AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(ACCESS_KEY, SECRET_KEY);
            awsCredentialsProvider = StaticCredentialsProvider.create(awsBasicCredentials);
        }

        String appEnv = System.getenv("APP_ENV");
        if (appEnv != null) {
            ENV = appEnv;
        }
        APP_ENV = ENV;
    }

}

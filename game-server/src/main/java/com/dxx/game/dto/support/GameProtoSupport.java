package com.dxx.game.dto.support;

import jakarta.annotation.PostConstruct;
import com.dxx.game.consts.MsgReqCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dxx.game.common.server.protocol.MessageProto;

@Component
public class GameProtoSupport {
	
	@Autowired
	private MessageProto messageProto;
	
	@PostConstruct
	private void initialize() {
		messageProto.registerMessage(MsgReqCommand.ActivityGetListRequest, com.dxx.game.dto.ActivityProto.ActivityGetListRequest.class,com.dxx.game.dto.ActivityProto.ActivityGetListRequest::parser,com.dxx.game.dto.ActivityProto.ActivityGetListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetListResponse, com.dxx.game.dto.ActivityProto.ActivityGetListResponse.class,com.dxx.game.dto.ActivityProto.ActivityGetListResponse::parser,com.dxx.game.dto.ActivityProto.ActivityGetListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetTaskRequest, com.dxx.game.dto.ActivityProto.ActivityGetTaskRequest.class,com.dxx.game.dto.ActivityProto.ActivityGetTaskRequest::parser,com.dxx.game.dto.ActivityProto.ActivityGetTaskRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetTaskResponse, com.dxx.game.dto.ActivityProto.ActivityGetTaskResponse.class,com.dxx.game.dto.ActivityProto.ActivityGetTaskResponse::parser,com.dxx.game.dto.ActivityProto.ActivityGetTaskResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityTaskRewardRequest, com.dxx.game.dto.ActivityProto.ActivityTaskRewardRequest.class,com.dxx.game.dto.ActivityProto.ActivityTaskRewardRequest::parser,com.dxx.game.dto.ActivityProto.ActivityTaskRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityTaskRewardResponse, com.dxx.game.dto.ActivityProto.ActivityTaskRewardResponse.class,com.dxx.game.dto.ActivityProto.ActivityTaskRewardResponse::parser,com.dxx.game.dto.ActivityProto.ActivityTaskRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetShopRequest, com.dxx.game.dto.ActivityProto.ActivityGetShopRequest.class,com.dxx.game.dto.ActivityProto.ActivityGetShopRequest::parser,com.dxx.game.dto.ActivityProto.ActivityGetShopRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetShopResponse, com.dxx.game.dto.ActivityProto.ActivityGetShopResponse.class,com.dxx.game.dto.ActivityProto.ActivityGetShopResponse::parser,com.dxx.game.dto.ActivityProto.ActivityGetShopResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityShopExchangeRequest, com.dxx.game.dto.ActivityProto.ActivityShopExchangeRequest.class,com.dxx.game.dto.ActivityProto.ActivityShopExchangeRequest::parser,com.dxx.game.dto.ActivityProto.ActivityShopExchangeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityShopExchangeResponse, com.dxx.game.dto.ActivityProto.ActivityShopExchangeResponse.class,com.dxx.game.dto.ActivityProto.ActivityShopExchangeResponse::parser,com.dxx.game.dto.ActivityProto.ActivityShopExchangeResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetRankRequest, com.dxx.game.dto.ActivityProto.ActivityGetRankRequest.class,com.dxx.game.dto.ActivityProto.ActivityGetRankRequest::parser,com.dxx.game.dto.ActivityProto.ActivityGetRankRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ActivityGetRankResponse, com.dxx.game.dto.ActivityProto.ActivityGetRankResponse.class,com.dxx.game.dto.ActivityProto.ActivityGetRankResponse::parser,com.dxx.game.dto.ActivityProto.ActivityGetRankResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ChatShowItemRequest, com.dxx.game.dto.ChatProto.ChatShowItemRequest.class,com.dxx.game.dto.ChatProto.ChatShowItemRequest::parser,com.dxx.game.dto.ChatProto.ChatShowItemRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ChatShowItemResponse, com.dxx.game.dto.ChatProto.ChatShowItemResponse.class,com.dxx.game.dto.ChatProto.ChatShowItemResponse::parser,com.dxx.game.dto.ChatProto.ChatShowItemResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ErrorMsg, com.dxx.game.dto.CommonProto.ErrorMsg.class,com.dxx.game.dto.CommonProto.ErrorMsg::parser,com.dxx.game.dto.CommonProto.ErrorMsg::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DevelopLoginRequest, com.dxx.game.dto.DevelopProto.DevelopLoginRequest.class,com.dxx.game.dto.DevelopProto.DevelopLoginRequest::parser,com.dxx.game.dto.DevelopProto.DevelopLoginRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DevelopLoginResponse, com.dxx.game.dto.DevelopProto.DevelopLoginResponse.class,com.dxx.game.dto.DevelopProto.DevelopLoginResponse::parser,com.dxx.game.dto.DevelopProto.DevelopLoginResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DevelopChangeResourceRequest, com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.class,com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest::parser,com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DevelopChangeResourceResponse, com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.class,com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse::parser,com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DevelopToolsRequest, com.dxx.game.dto.DevelopProto.DevelopToolsRequest.class,com.dxx.game.dto.DevelopProto.DevelopToolsRequest::parser,com.dxx.game.dto.DevelopProto.DevelopToolsRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DevelopToolsResponse, com.dxx.game.dto.DevelopProto.DevelopToolsResponse.class,com.dxx.game.dto.DevelopProto.DevelopToolsResponse::parser,com.dxx.game.dto.DevelopProto.DevelopToolsResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveOnOpenRequest, com.dxx.game.dto.DiveProto.DiveOnOpenRequest.class,com.dxx.game.dto.DiveProto.DiveOnOpenRequest::parser,com.dxx.game.dto.DiveProto.DiveOnOpenRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveOnOpenResponse, com.dxx.game.dto.DiveProto.DiveOnOpenResponse.class,com.dxx.game.dto.DiveProto.DiveOnOpenResponse::parser,com.dxx.game.dto.DiveProto.DiveOnOpenResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveBuyItemRequest, com.dxx.game.dto.DiveProto.DiveBuyItemRequest.class,com.dxx.game.dto.DiveProto.DiveBuyItemRequest::parser,com.dxx.game.dto.DiveProto.DiveBuyItemRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveBuyItemResponse, com.dxx.game.dto.DiveProto.DiveBuyItemResponse.class,com.dxx.game.dto.DiveProto.DiveBuyItemResponse::parser,com.dxx.game.dto.DiveProto.DiveBuyItemResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveAccRewardRequest, com.dxx.game.dto.DiveProto.DiveAccRewardRequest.class,com.dxx.game.dto.DiveProto.DiveAccRewardRequest::parser,com.dxx.game.dto.DiveProto.DiveAccRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveAccRewardResponse, com.dxx.game.dto.DiveProto.DiveAccRewardResponse.class,com.dxx.game.dto.DiveProto.DiveAccRewardResponse::parser,com.dxx.game.dto.DiveProto.DiveAccRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveShineRequest, com.dxx.game.dto.DiveProto.DiveShineRequest.class,com.dxx.game.dto.DiveProto.DiveShineRequest::parser,com.dxx.game.dto.DiveProto.DiveShineRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveShineResponse, com.dxx.game.dto.DiveProto.DiveShineResponse.class,com.dxx.game.dto.DiveProto.DiveShineResponse::parser,com.dxx.game.dto.DiveProto.DiveShineResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveUsePropRequest, com.dxx.game.dto.DiveProto.DiveUsePropRequest.class,com.dxx.game.dto.DiveProto.DiveUsePropRequest::parser,com.dxx.game.dto.DiveProto.DiveUsePropRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveUsePropResponse, com.dxx.game.dto.DiveProto.DiveUsePropResponse.class,com.dxx.game.dto.DiveProto.DiveUsePropResponse::parser,com.dxx.game.dto.DiveProto.DiveUsePropResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveAllAccRewardRequest, com.dxx.game.dto.DiveProto.DiveAllAccRewardRequest.class,com.dxx.game.dto.DiveProto.DiveAllAccRewardRequest::parser,com.dxx.game.dto.DiveProto.DiveAllAccRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.DiveAllAccRewardResponse, com.dxx.game.dto.DiveProto.DiveAllAccRewardResponse.class,com.dxx.game.dto.DiveProto.DiveAllAccRewardResponse::parser,com.dxx.game.dto.DiveProto.DiveAllAccRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipStrengthRequest, com.dxx.game.dto.EquipProto.EquipStrengthRequest.class,com.dxx.game.dto.EquipProto.EquipStrengthRequest::parser,com.dxx.game.dto.EquipProto.EquipStrengthRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipStrengthResponse, com.dxx.game.dto.EquipProto.EquipStrengthResponse.class,com.dxx.game.dto.EquipProto.EquipStrengthResponse::parser,com.dxx.game.dto.EquipProto.EquipStrengthResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipComposeRequest, com.dxx.game.dto.EquipProto.EquipComposeRequest.class,com.dxx.game.dto.EquipProto.EquipComposeRequest::parser,com.dxx.game.dto.EquipProto.EquipComposeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipComposeResponse, com.dxx.game.dto.EquipProto.EquipComposeResponse.class,com.dxx.game.dto.EquipProto.EquipComposeResponse::parser,com.dxx.game.dto.EquipProto.EquipComposeResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipUpgradeRequest, com.dxx.game.dto.EquipProto.EquipUpgradeRequest.class,com.dxx.game.dto.EquipProto.EquipUpgradeRequest::parser,com.dxx.game.dto.EquipProto.EquipUpgradeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipUpgradeResponse, com.dxx.game.dto.EquipProto.EquipUpgradeResponse.class,com.dxx.game.dto.EquipProto.EquipUpgradeResponse::parser,com.dxx.game.dto.EquipProto.EquipUpgradeResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipDressRequest, com.dxx.game.dto.EquipProto.EquipDressRequest.class,com.dxx.game.dto.EquipProto.EquipDressRequest::parser,com.dxx.game.dto.EquipProto.EquipDressRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipDressResponse, com.dxx.game.dto.EquipProto.EquipDressResponse.class,com.dxx.game.dto.EquipProto.EquipDressResponse::parser,com.dxx.game.dto.EquipProto.EquipDressResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipLevelResetRequest, com.dxx.game.dto.EquipProto.EquipLevelResetRequest.class,com.dxx.game.dto.EquipProto.EquipLevelResetRequest::parser,com.dxx.game.dto.EquipProto.EquipLevelResetRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipLevelResetResponse, com.dxx.game.dto.EquipProto.EquipLevelResetResponse.class,com.dxx.game.dto.EquipProto.EquipLevelResetResponse::parser,com.dxx.game.dto.EquipProto.EquipLevelResetResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipQualityDownRequest, com.dxx.game.dto.EquipProto.EquipQualityDownRequest.class,com.dxx.game.dto.EquipProto.EquipQualityDownRequest::parser,com.dxx.game.dto.EquipProto.EquipQualityDownRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipQualityDownResponse, com.dxx.game.dto.EquipProto.EquipQualityDownResponse.class,com.dxx.game.dto.EquipProto.EquipQualityDownResponse::parser,com.dxx.game.dto.EquipProto.EquipQualityDownResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipOffRequest, com.dxx.game.dto.EquipProto.EquipOffRequest.class,com.dxx.game.dto.EquipProto.EquipOffRequest::parser,com.dxx.game.dto.EquipProto.EquipOffRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipOffResponse, com.dxx.game.dto.EquipProto.EquipOffResponse.class,com.dxx.game.dto.EquipProto.EquipOffResponse::parser,com.dxx.game.dto.EquipProto.EquipOffResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipReplaceRequest, com.dxx.game.dto.EquipProto.EquipReplaceRequest.class,com.dxx.game.dto.EquipProto.EquipReplaceRequest::parser,com.dxx.game.dto.EquipProto.EquipReplaceRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.EquipReplaceResponse, com.dxx.game.dto.EquipProto.EquipReplaceResponse.class,com.dxx.game.dto.EquipProto.EquipReplaceResponse::parser,com.dxx.game.dto.EquipProto.EquipReplaceResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingOnOpenRequest, com.dxx.game.dto.FishingProto.FishingOnOpenRequest.class,com.dxx.game.dto.FishingProto.FishingOnOpenRequest::parser,com.dxx.game.dto.FishingProto.FishingOnOpenRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingOnOpenResponse, com.dxx.game.dto.FishingProto.FishingOnOpenResponse.class,com.dxx.game.dto.FishingProto.FishingOnOpenResponse::parser,com.dxx.game.dto.FishingProto.FishingOnOpenResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingCastRodRequest, com.dxx.game.dto.FishingProto.FishingCastRodRequest.class,com.dxx.game.dto.FishingProto.FishingCastRodRequest::parser,com.dxx.game.dto.FishingProto.FishingCastRodRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingCastRodResponse, com.dxx.game.dto.FishingProto.FishingCastRodResponse.class,com.dxx.game.dto.FishingProto.FishingCastRodResponse::parser,com.dxx.game.dto.FishingProto.FishingCastRodResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingReelInRequest, com.dxx.game.dto.FishingProto.FishingReelInRequest.class,com.dxx.game.dto.FishingProto.FishingReelInRequest::parser,com.dxx.game.dto.FishingProto.FishingReelInRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingReelInResponse, com.dxx.game.dto.FishingProto.FishingReelInResponse.class,com.dxx.game.dto.FishingProto.FishingReelInResponse::parser,com.dxx.game.dto.FishingProto.FishingReelInResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingBuyBaitRequest, com.dxx.game.dto.FishingProto.FishingBuyBaitRequest.class,com.dxx.game.dto.FishingProto.FishingBuyBaitRequest::parser,com.dxx.game.dto.FishingProto.FishingBuyBaitRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingBuyBaitResponse, com.dxx.game.dto.FishingProto.FishingBuyBaitResponse.class,com.dxx.game.dto.FishingProto.FishingBuyBaitResponse::parser,com.dxx.game.dto.FishingProto.FishingBuyBaitResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingRebornRequest, com.dxx.game.dto.FishingProto.FishingRebornRequest.class,com.dxx.game.dto.FishingProto.FishingRebornRequest::parser,com.dxx.game.dto.FishingProto.FishingRebornRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FishingRebornResponse, com.dxx.game.dto.FishingProto.FishingRebornResponse.class,com.dxx.game.dto.FishingProto.FishingRebornResponse::parser,com.dxx.game.dto.FishingProto.FishingRebornResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipOnOpenRequest, com.dxx.game.dto.FlipProto.FlipOnOpenRequest.class,com.dxx.game.dto.FlipProto.FlipOnOpenRequest::parser,com.dxx.game.dto.FlipProto.FlipOnOpenRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipOnOpenResponse, com.dxx.game.dto.FlipProto.FlipOnOpenResponse.class,com.dxx.game.dto.FlipProto.FlipOnOpenResponse::parser,com.dxx.game.dto.FlipProto.FlipOnOpenResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipAccRewardRequest, com.dxx.game.dto.FlipProto.FlipAccRewardRequest.class,com.dxx.game.dto.FlipProto.FlipAccRewardRequest::parser,com.dxx.game.dto.FlipProto.FlipAccRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipAccRewardResponse, com.dxx.game.dto.FlipProto.FlipAccRewardResponse.class,com.dxx.game.dto.FlipProto.FlipAccRewardResponse::parser,com.dxx.game.dto.FlipProto.FlipAccRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipBuyStepRequest, com.dxx.game.dto.FlipProto.FlipBuyStepRequest.class,com.dxx.game.dto.FlipProto.FlipBuyStepRequest::parser,com.dxx.game.dto.FlipProto.FlipBuyStepRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipBuyStepResponse, com.dxx.game.dto.FlipProto.FlipBuyStepResponse.class,com.dxx.game.dto.FlipProto.FlipBuyStepResponse::parser,com.dxx.game.dto.FlipProto.FlipBuyStepResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipShowGridRequest, com.dxx.game.dto.FlipProto.FlipShowGridRequest.class,com.dxx.game.dto.FlipProto.FlipShowGridRequest::parser,com.dxx.game.dto.FlipProto.FlipShowGridRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipShowGridResponse, com.dxx.game.dto.FlipProto.FlipShowGridResponse.class,com.dxx.game.dto.FlipProto.FlipShowGridResponse::parser,com.dxx.game.dto.FlipProto.FlipShowGridResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipRewardGridRequest, com.dxx.game.dto.FlipProto.FlipRewardGridRequest.class,com.dxx.game.dto.FlipProto.FlipRewardGridRequest::parser,com.dxx.game.dto.FlipProto.FlipRewardGridRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipRewardGridResponse, com.dxx.game.dto.FlipProto.FlipRewardGridResponse.class,com.dxx.game.dto.FlipProto.FlipRewardGridResponse::parser,com.dxx.game.dto.FlipProto.FlipRewardGridResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipClueGridRequest, com.dxx.game.dto.FlipProto.FlipClueGridRequest.class,com.dxx.game.dto.FlipProto.FlipClueGridRequest::parser,com.dxx.game.dto.FlipProto.FlipClueGridRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipClueGridResponse, com.dxx.game.dto.FlipProto.FlipClueGridResponse.class,com.dxx.game.dto.FlipProto.FlipClueGridResponse::parser,com.dxx.game.dto.FlipProto.FlipClueGridResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipBombGridRequest, com.dxx.game.dto.FlipProto.FlipBombGridRequest.class,com.dxx.game.dto.FlipProto.FlipBombGridRequest::parser,com.dxx.game.dto.FlipProto.FlipBombGridRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipBombGridResponse, com.dxx.game.dto.FlipProto.FlipBombGridResponse.class,com.dxx.game.dto.FlipProto.FlipBombGridResponse::parser,com.dxx.game.dto.FlipProto.FlipBombGridResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipMapFindSpecialRequest, com.dxx.game.dto.FlipProto.FlipMapFindSpecialRequest.class,com.dxx.game.dto.FlipProto.FlipMapFindSpecialRequest::parser,com.dxx.game.dto.FlipProto.FlipMapFindSpecialRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipMapFindSpecialResponse, com.dxx.game.dto.FlipProto.FlipMapFindSpecialResponse.class,com.dxx.game.dto.FlipProto.FlipMapFindSpecialResponse::parser,com.dxx.game.dto.FlipProto.FlipMapFindSpecialResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipAllAccRewardRequest, com.dxx.game.dto.FlipProto.FlipAllAccRewardRequest.class,com.dxx.game.dto.FlipProto.FlipAllAccRewardRequest::parser,com.dxx.game.dto.FlipProto.FlipAllAccRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FlipAllAccRewardResponse, com.dxx.game.dto.FlipProto.FlipAllAccRewardResponse.class,com.dxx.game.dto.FlipProto.FlipAllAccRewardResponse::parser,com.dxx.game.dto.FlipProto.FlipAllAccRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetInfoRequest, com.dxx.game.dto.GuildProto.GuildGetInfoRequest.class,com.dxx.game.dto.GuildProto.GuildGetInfoRequest::parser,com.dxx.game.dto.GuildProto.GuildGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetInfoResponse, com.dxx.game.dto.GuildProto.GuildGetInfoResponse.class,com.dxx.game.dto.GuildProto.GuildGetInfoResponse::parser,com.dxx.game.dto.GuildProto.GuildGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildCreateRequest, com.dxx.game.dto.GuildProto.GuildCreateRequest.class,com.dxx.game.dto.GuildProto.GuildCreateRequest::parser,com.dxx.game.dto.GuildProto.GuildCreateRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildCreateResponse, com.dxx.game.dto.GuildProto.GuildCreateResponse.class,com.dxx.game.dto.GuildProto.GuildCreateResponse::parser,com.dxx.game.dto.GuildProto.GuildCreateResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildSearchRequest, com.dxx.game.dto.GuildProto.GuildSearchRequest.class,com.dxx.game.dto.GuildProto.GuildSearchRequest::parser,com.dxx.game.dto.GuildProto.GuildSearchRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildSearchResponse, com.dxx.game.dto.GuildProto.GuildSearchResponse.class,com.dxx.game.dto.GuildProto.GuildSearchResponse::parser,com.dxx.game.dto.GuildProto.GuildSearchResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetDetailRequest, com.dxx.game.dto.GuildProto.GuildGetDetailRequest.class,com.dxx.game.dto.GuildProto.GuildGetDetailRequest::parser,com.dxx.game.dto.GuildProto.GuildGetDetailRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetDetailResponse, com.dxx.game.dto.GuildProto.GuildGetDetailResponse.class,com.dxx.game.dto.GuildProto.GuildGetDetailResponse::parser,com.dxx.game.dto.GuildProto.GuildGetDetailResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetMemberListRequest, com.dxx.game.dto.GuildProto.GuildGetMemberListRequest.class,com.dxx.game.dto.GuildProto.GuildGetMemberListRequest::parser,com.dxx.game.dto.GuildProto.GuildGetMemberListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetMemberListResponse, com.dxx.game.dto.GuildProto.GuildGetMemberListResponse.class,com.dxx.game.dto.GuildProto.GuildGetMemberListResponse::parser,com.dxx.game.dto.GuildProto.GuildGetMemberListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildModifyRequest, com.dxx.game.dto.GuildProto.GuildModifyRequest.class,com.dxx.game.dto.GuildProto.GuildModifyRequest::parser,com.dxx.game.dto.GuildProto.GuildModifyRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildModifyResponse, com.dxx.game.dto.GuildProto.GuildModifyResponse.class,com.dxx.game.dto.GuildProto.GuildModifyResponse::parser,com.dxx.game.dto.GuildProto.GuildModifyResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDismissRequest, com.dxx.game.dto.GuildProto.GuildDismissRequest.class,com.dxx.game.dto.GuildProto.GuildDismissRequest::parser,com.dxx.game.dto.GuildProto.GuildDismissRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDismissResponse, com.dxx.game.dto.GuildProto.GuildDismissResponse.class,com.dxx.game.dto.GuildProto.GuildDismissResponse::parser,com.dxx.game.dto.GuildProto.GuildDismissResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildApplyJoinRequest, com.dxx.game.dto.GuildProto.GuildApplyJoinRequest.class,com.dxx.game.dto.GuildProto.GuildApplyJoinRequest::parser,com.dxx.game.dto.GuildProto.GuildApplyJoinRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildApplyJoinResponse, com.dxx.game.dto.GuildProto.GuildApplyJoinResponse.class,com.dxx.game.dto.GuildProto.GuildApplyJoinResponse::parser,com.dxx.game.dto.GuildProto.GuildApplyJoinResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildCancelApplyRequest, com.dxx.game.dto.GuildProto.GuildCancelApplyRequest.class,com.dxx.game.dto.GuildProto.GuildCancelApplyRequest::parser,com.dxx.game.dto.GuildProto.GuildCancelApplyRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildCancelApplyResponse, com.dxx.game.dto.GuildProto.GuildCancelApplyResponse.class,com.dxx.game.dto.GuildProto.GuildCancelApplyResponse::parser,com.dxx.game.dto.GuildProto.GuildCancelApplyResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildAutoJoinRequest, com.dxx.game.dto.GuildProto.GuildAutoJoinRequest.class,com.dxx.game.dto.GuildProto.GuildAutoJoinRequest::parser,com.dxx.game.dto.GuildProto.GuildAutoJoinRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildAutoJoinResponse, com.dxx.game.dto.GuildProto.GuildAutoJoinResponse.class,com.dxx.game.dto.GuildProto.GuildAutoJoinResponse::parser,com.dxx.game.dto.GuildProto.GuildAutoJoinResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetApplyListRequest, com.dxx.game.dto.GuildProto.GuildGetApplyListRequest.class,com.dxx.game.dto.GuildProto.GuildGetApplyListRequest::parser,com.dxx.game.dto.GuildProto.GuildGetApplyListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetApplyListResponse, com.dxx.game.dto.GuildProto.GuildGetApplyListResponse.class,com.dxx.game.dto.GuildProto.GuildGetApplyListResponse::parser,com.dxx.game.dto.GuildProto.GuildGetApplyListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildAgreeJoinRequest, com.dxx.game.dto.GuildProto.GuildAgreeJoinRequest.class,com.dxx.game.dto.GuildProto.GuildAgreeJoinRequest::parser,com.dxx.game.dto.GuildProto.GuildAgreeJoinRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildAgreeJoinResponse, com.dxx.game.dto.GuildProto.GuildAgreeJoinResponse.class,com.dxx.game.dto.GuildProto.GuildAgreeJoinResponse::parser,com.dxx.game.dto.GuildProto.GuildAgreeJoinResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildRefuseJoinRequest, com.dxx.game.dto.GuildProto.GuildRefuseJoinRequest.class,com.dxx.game.dto.GuildProto.GuildRefuseJoinRequest::parser,com.dxx.game.dto.GuildProto.GuildRefuseJoinRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildRefuseJoinResponse, com.dxx.game.dto.GuildProto.GuildRefuseJoinResponse.class,com.dxx.game.dto.GuildProto.GuildRefuseJoinResponse::parser,com.dxx.game.dto.GuildProto.GuildRefuseJoinResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildKickOutRequest, com.dxx.game.dto.GuildProto.GuildKickOutRequest.class,com.dxx.game.dto.GuildProto.GuildKickOutRequest::parser,com.dxx.game.dto.GuildProto.GuildKickOutRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildKickOutResponse, com.dxx.game.dto.GuildProto.GuildKickOutResponse.class,com.dxx.game.dto.GuildProto.GuildKickOutResponse::parser,com.dxx.game.dto.GuildProto.GuildKickOutResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildLeaveRequest, com.dxx.game.dto.GuildProto.GuildLeaveRequest.class,com.dxx.game.dto.GuildProto.GuildLeaveRequest::parser,com.dxx.game.dto.GuildProto.GuildLeaveRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildLeaveResponse, com.dxx.game.dto.GuildProto.GuildLeaveResponse.class,com.dxx.game.dto.GuildProto.GuildLeaveResponse::parser,com.dxx.game.dto.GuildProto.GuildLeaveResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildUpPositionRequest, com.dxx.game.dto.GuildProto.GuildUpPositionRequest.class,com.dxx.game.dto.GuildProto.GuildUpPositionRequest::parser,com.dxx.game.dto.GuildProto.GuildUpPositionRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildUpPositionResponse, com.dxx.game.dto.GuildProto.GuildUpPositionResponse.class,com.dxx.game.dto.GuildProto.GuildUpPositionResponse::parser,com.dxx.game.dto.GuildProto.GuildUpPositionResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTransferPresidentRequest, com.dxx.game.dto.GuildProto.GuildTransferPresidentRequest.class,com.dxx.game.dto.GuildProto.GuildTransferPresidentRequest::parser,com.dxx.game.dto.GuildProto.GuildTransferPresidentRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTransferPresidentResponse, com.dxx.game.dto.GuildProto.GuildTransferPresidentResponse.class,com.dxx.game.dto.GuildProto.GuildTransferPresidentResponse::parser,com.dxx.game.dto.GuildProto.GuildTransferPresidentResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetFeaturesInfoRequest, com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoRequest.class,com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoRequest::parser,com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetFeaturesInfoResponse, com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoResponse.class,com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoResponse::parser,com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildLevelUpRequest, com.dxx.game.dto.GuildProto.GuildLevelUpRequest.class,com.dxx.game.dto.GuildProto.GuildLevelUpRequest::parser,com.dxx.game.dto.GuildProto.GuildLevelUpRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildLevelUpResponse, com.dxx.game.dto.GuildProto.GuildLevelUpResponse.class,com.dxx.game.dto.GuildProto.GuildLevelUpResponse::parser,com.dxx.game.dto.GuildProto.GuildLevelUpResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildSignInRequest, com.dxx.game.dto.GuildProto.GuildSignInRequest.class,com.dxx.game.dto.GuildProto.GuildSignInRequest::parser,com.dxx.game.dto.GuildProto.GuildSignInRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildSignInResponse, com.dxx.game.dto.GuildProto.GuildSignInResponse.class,com.dxx.game.dto.GuildProto.GuildSignInResponse::parser,com.dxx.game.dto.GuildProto.GuildSignInResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildShopBuyRequest, com.dxx.game.dto.GuildProto.GuildShopBuyRequest.class,com.dxx.game.dto.GuildProto.GuildShopBuyRequest::parser,com.dxx.game.dto.GuildProto.GuildShopBuyRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildShopBuyResponse, com.dxx.game.dto.GuildProto.GuildShopBuyResponse.class,com.dxx.game.dto.GuildProto.GuildShopBuyResponse::parser,com.dxx.game.dto.GuildProto.GuildShopBuyResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildShopRefreshRequest, com.dxx.game.dto.GuildProto.GuildShopRefreshRequest.class,com.dxx.game.dto.GuildProto.GuildShopRefreshRequest::parser,com.dxx.game.dto.GuildProto.GuildShopRefreshRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildShopRefreshResponse, com.dxx.game.dto.GuildProto.GuildShopRefreshResponse.class,com.dxx.game.dto.GuildProto.GuildShopRefreshResponse::parser,com.dxx.game.dto.GuildProto.GuildShopRefreshResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTaskRewardRequest, com.dxx.game.dto.GuildProto.GuildTaskRewardRequest.class,com.dxx.game.dto.GuildProto.GuildTaskRewardRequest::parser,com.dxx.game.dto.GuildProto.GuildTaskRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTaskRewardResponse, com.dxx.game.dto.GuildProto.GuildTaskRewardResponse.class,com.dxx.game.dto.GuildProto.GuildTaskRewardResponse::parser,com.dxx.game.dto.GuildProto.GuildTaskRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTaskRefreshRequest, com.dxx.game.dto.GuildProto.GuildTaskRefreshRequest.class,com.dxx.game.dto.GuildProto.GuildTaskRefreshRequest::parser,com.dxx.game.dto.GuildProto.GuildTaskRefreshRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTaskRefreshResponse, com.dxx.game.dto.GuildProto.GuildTaskRefreshResponse.class,com.dxx.game.dto.GuildProto.GuildTaskRefreshResponse::parser,com.dxx.game.dto.GuildProto.GuildTaskRefreshResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetMessageRecordsRequest, com.dxx.game.dto.GuildProto.GuildGetMessageRecordsRequest.class,com.dxx.game.dto.GuildProto.GuildGetMessageRecordsRequest::parser,com.dxx.game.dto.GuildProto.GuildGetMessageRecordsRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildGetMessageRecordsResponse, com.dxx.game.dto.GuildProto.GuildGetMessageRecordsResponse.class,com.dxx.game.dto.GuildProto.GuildGetMessageRecordsResponse::parser,com.dxx.game.dto.GuildProto.GuildGetMessageRecordsResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationReqItemRequest, com.dxx.game.dto.GuildProto.GuildDonationReqItemRequest.class,com.dxx.game.dto.GuildProto.GuildDonationReqItemRequest::parser,com.dxx.game.dto.GuildProto.GuildDonationReqItemRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationReqItemResponse, com.dxx.game.dto.GuildProto.GuildDonationReqItemResponse.class,com.dxx.game.dto.GuildProto.GuildDonationReqItemResponse::parser,com.dxx.game.dto.GuildProto.GuildDonationReqItemResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationSendItemRequest, com.dxx.game.dto.GuildProto.GuildDonationSendItemRequest.class,com.dxx.game.dto.GuildProto.GuildDonationSendItemRequest::parser,com.dxx.game.dto.GuildProto.GuildDonationSendItemRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationSendItemResponse, com.dxx.game.dto.GuildProto.GuildDonationSendItemResponse.class,com.dxx.game.dto.GuildProto.GuildDonationSendItemResponse::parser,com.dxx.game.dto.GuildProto.GuildDonationSendItemResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationReceiveRequest, com.dxx.game.dto.GuildProto.GuildDonationReceiveRequest.class,com.dxx.game.dto.GuildProto.GuildDonationReceiveRequest::parser,com.dxx.game.dto.GuildProto.GuildDonationReceiveRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationReceiveResponse, com.dxx.game.dto.GuildProto.GuildDonationReceiveResponse.class,com.dxx.game.dto.GuildProto.GuildDonationReceiveResponse::parser,com.dxx.game.dto.GuildProto.GuildDonationReceiveResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationGetRecordsRequest, com.dxx.game.dto.GuildProto.GuildDonationGetRecordsRequest.class,com.dxx.game.dto.GuildProto.GuildDonationGetRecordsRequest::parser,com.dxx.game.dto.GuildProto.GuildDonationGetRecordsRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationGetRecordsResponse, com.dxx.game.dto.GuildProto.GuildDonationGetRecordsResponse.class,com.dxx.game.dto.GuildProto.GuildDonationGetRecordsResponse::parser,com.dxx.game.dto.GuildProto.GuildDonationGetRecordsResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationGetOperationRecordsRequest, com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsRequest.class,com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsRequest::parser,com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildDonationGetOperationRecordsResponse, com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsResponse.class,com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsResponse::parser,com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildBossBattleRequest, com.dxx.game.dto.GuildProto.GuildBossBattleRequest.class,com.dxx.game.dto.GuildProto.GuildBossBattleRequest::parser,com.dxx.game.dto.GuildProto.GuildBossBattleRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildBossBattleResponse, com.dxx.game.dto.GuildProto.GuildBossBattleResponse.class,com.dxx.game.dto.GuildProto.GuildBossBattleResponse::parser,com.dxx.game.dto.GuildProto.GuildBossBattleResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildBossBattleGRankRequest, com.dxx.game.dto.GuildProto.GuildBossBattleGRankRequest.class,com.dxx.game.dto.GuildProto.GuildBossBattleGRankRequest::parser,com.dxx.game.dto.GuildProto.GuildBossBattleGRankRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildBossBattleGRankResponse, com.dxx.game.dto.GuildProto.GuildBossBattleGRankResponse.class,com.dxx.game.dto.GuildProto.GuildBossBattleGRankResponse::parser,com.dxx.game.dto.GuildProto.GuildBossBattleGRankResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTechUpgradeRequest, com.dxx.game.dto.GuildProto.GuildTechUpgradeRequest.class,com.dxx.game.dto.GuildProto.GuildTechUpgradeRequest::parser,com.dxx.game.dto.GuildProto.GuildTechUpgradeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.GuildTechUpgradeResponse, com.dxx.game.dto.GuildProto.GuildTechUpgradeResponse.class,com.dxx.game.dto.GuildProto.GuildTechUpgradeResponse::parser,com.dxx.game.dto.GuildProto.GuildTechUpgradeResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroUpgradeRequest, com.dxx.game.dto.HeroProto.HeroUpgradeRequest.class,com.dxx.game.dto.HeroProto.HeroUpgradeRequest::parser,com.dxx.game.dto.HeroProto.HeroUpgradeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroUpgradeResponse, com.dxx.game.dto.HeroProto.HeroUpgradeResponse.class,com.dxx.game.dto.HeroProto.HeroUpgradeResponse::parser,com.dxx.game.dto.HeroProto.HeroUpgradeResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroAdvanceRequest, com.dxx.game.dto.HeroProto.HeroAdvanceRequest.class,com.dxx.game.dto.HeroProto.HeroAdvanceRequest::parser,com.dxx.game.dto.HeroProto.HeroAdvanceRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroAdvanceResponse, com.dxx.game.dto.HeroProto.HeroAdvanceResponse.class,com.dxx.game.dto.HeroProto.HeroAdvanceResponse::parser,com.dxx.game.dto.HeroProto.HeroAdvanceResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroStarRequest, com.dxx.game.dto.HeroProto.HeroStarRequest.class,com.dxx.game.dto.HeroProto.HeroStarRequest::parser,com.dxx.game.dto.HeroProto.HeroStarRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroStarResponse, com.dxx.game.dto.HeroProto.HeroStarResponse.class,com.dxx.game.dto.HeroProto.HeroStarResponse::parser,com.dxx.game.dto.HeroProto.HeroStarResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroResetRequest, com.dxx.game.dto.HeroProto.HeroResetRequest.class,com.dxx.game.dto.HeroProto.HeroResetRequest::parser,com.dxx.game.dto.HeroProto.HeroResetRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroResetResponse, com.dxx.game.dto.HeroProto.HeroResetResponse.class,com.dxx.game.dto.HeroProto.HeroResetResponse::parser,com.dxx.game.dto.HeroProto.HeroResetResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroBookScoreRequest, com.dxx.game.dto.HeroProto.HeroBookScoreRequest.class,com.dxx.game.dto.HeroProto.HeroBookScoreRequest::parser,com.dxx.game.dto.HeroProto.HeroBookScoreRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroBookScoreResponse, com.dxx.game.dto.HeroProto.HeroBookScoreResponse.class,com.dxx.game.dto.HeroProto.HeroBookScoreResponse::parser,com.dxx.game.dto.HeroProto.HeroBookScoreResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroBookRewardRequest, com.dxx.game.dto.HeroProto.HeroBookRewardRequest.class,com.dxx.game.dto.HeroProto.HeroBookRewardRequest::parser,com.dxx.game.dto.HeroProto.HeroBookRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroBookRewardResponse, com.dxx.game.dto.HeroProto.HeroBookRewardResponse.class,com.dxx.game.dto.HeroProto.HeroBookRewardResponse::parser,com.dxx.game.dto.HeroProto.HeroBookRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroReplaceSkinRequest, com.dxx.game.dto.HeroProto.HeroReplaceSkinRequest.class,com.dxx.game.dto.HeroProto.HeroReplaceSkinRequest::parser,com.dxx.game.dto.HeroProto.HeroReplaceSkinRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroReplaceSkinResponse, com.dxx.game.dto.HeroProto.HeroReplaceSkinResponse.class,com.dxx.game.dto.HeroProto.HeroReplaceSkinResponse::parser,com.dxx.game.dto.HeroProto.HeroReplaceSkinResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroBondLevelUpRequest, com.dxx.game.dto.HeroProto.HeroBondLevelUpRequest.class,com.dxx.game.dto.HeroProto.HeroBondLevelUpRequest::parser,com.dxx.game.dto.HeroProto.HeroBondLevelUpRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroBondLevelUpResponse, com.dxx.game.dto.HeroProto.HeroBondLevelUpResponse.class,com.dxx.game.dto.HeroProto.HeroBondLevelUpResponse::parser,com.dxx.game.dto.HeroProto.HeroBondLevelUpResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroLosslessRequest, com.dxx.game.dto.HeroProto.HeroLosslessRequest.class,com.dxx.game.dto.HeroProto.HeroLosslessRequest::parser,com.dxx.game.dto.HeroProto.HeroLosslessRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.HeroLosslessResponse, com.dxx.game.dto.HeroProto.HeroLosslessResponse.class,com.dxx.game.dto.HeroProto.HeroLosslessResponse::parser,com.dxx.game.dto.HeroProto.HeroLosslessResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMLoginRequest, com.dxx.game.dto.IMProto.IMLoginRequest.class,com.dxx.game.dto.IMProto.IMLoginRequest::parser,com.dxx.game.dto.IMProto.IMLoginRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMLoginResponse, com.dxx.game.dto.IMProto.IMLoginResponse.class,com.dxx.game.dto.IMProto.IMLoginResponse::parser,com.dxx.game.dto.IMProto.IMLoginResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMJoinGroupRequest, com.dxx.game.dto.IMProto.IMJoinGroupRequest.class,com.dxx.game.dto.IMProto.IMJoinGroupRequest::parser,com.dxx.game.dto.IMProto.IMJoinGroupRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMJoinGroupResponse, com.dxx.game.dto.IMProto.IMJoinGroupResponse.class,com.dxx.game.dto.IMProto.IMJoinGroupResponse::parser,com.dxx.game.dto.IMProto.IMJoinGroupResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMQuitGroupRequest, com.dxx.game.dto.IMProto.IMQuitGroupRequest.class,com.dxx.game.dto.IMProto.IMQuitGroupRequest::parser,com.dxx.game.dto.IMProto.IMQuitGroupRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMQuitGroupResponse, com.dxx.game.dto.IMProto.IMQuitGroupResponse.class,com.dxx.game.dto.IMProto.IMQuitGroupResponse::parser,com.dxx.game.dto.IMProto.IMQuitGroupResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMHeartBeatRequest, com.dxx.game.dto.IMProto.IMHeartBeatRequest.class,com.dxx.game.dto.IMProto.IMHeartBeatRequest::parser,com.dxx.game.dto.IMProto.IMHeartBeatRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMHeartBeatResponse, com.dxx.game.dto.IMProto.IMHeartBeatResponse.class,com.dxx.game.dto.IMProto.IMHeartBeatResponse::parser,com.dxx.game.dto.IMProto.IMHeartBeatResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMGroupChatRequest, com.dxx.game.dto.IMProto.IMGroupChatRequest.class,com.dxx.game.dto.IMProto.IMGroupChatRequest::parser,com.dxx.game.dto.IMProto.IMGroupChatRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMGroupChatResponse, com.dxx.game.dto.IMProto.IMGroupChatResponse.class,com.dxx.game.dto.IMProto.IMGroupChatResponse::parser,com.dxx.game.dto.IMProto.IMGroupChatResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPrivateChatRequest, com.dxx.game.dto.IMProto.IMPrivateChatRequest.class,com.dxx.game.dto.IMProto.IMPrivateChatRequest::parser,com.dxx.game.dto.IMProto.IMPrivateChatRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPrivateChatResponse, com.dxx.game.dto.IMProto.IMPrivateChatResponse.class,com.dxx.game.dto.IMProto.IMPrivateChatResponse::parser,com.dxx.game.dto.IMProto.IMPrivateChatResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMGroupMessageRecordRequest, com.dxx.game.dto.IMProto.IMGroupMessageRecordRequest.class,com.dxx.game.dto.IMProto.IMGroupMessageRecordRequest::parser,com.dxx.game.dto.IMProto.IMGroupMessageRecordRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMGroupMessageRecordResponse, com.dxx.game.dto.IMProto.IMGroupMessageRecordResponse.class,com.dxx.game.dto.IMProto.IMGroupMessageRecordResponse::parser,com.dxx.game.dto.IMProto.IMGroupMessageRecordResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPrivateListRequest, com.dxx.game.dto.IMProto.IMPrivateListRequest.class,com.dxx.game.dto.IMProto.IMPrivateListRequest::parser,com.dxx.game.dto.IMProto.IMPrivateListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPrivateListResponse, com.dxx.game.dto.IMProto.IMPrivateListResponse.class,com.dxx.game.dto.IMProto.IMPrivateListResponse::parser,com.dxx.game.dto.IMProto.IMPrivateListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPrivateChatRecordRequest, com.dxx.game.dto.IMProto.IMPrivateChatRecordRequest.class,com.dxx.game.dto.IMProto.IMPrivateChatRecordRequest::parser,com.dxx.game.dto.IMProto.IMPrivateChatRecordRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPrivateChatRecordResponse, com.dxx.game.dto.IMProto.IMPrivateChatRecordResponse.class,com.dxx.game.dto.IMProto.IMPrivateChatRecordResponse::parser,com.dxx.game.dto.IMProto.IMPrivateChatRecordResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMGetBlackListRequest, com.dxx.game.dto.IMProto.IMGetBlackListRequest.class,com.dxx.game.dto.IMProto.IMGetBlackListRequest::parser,com.dxx.game.dto.IMProto.IMGetBlackListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMGetBlackListResponse, com.dxx.game.dto.IMProto.IMGetBlackListResponse.class,com.dxx.game.dto.IMProto.IMGetBlackListResponse::parser,com.dxx.game.dto.IMProto.IMGetBlackListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMAddToBlackListRequest, com.dxx.game.dto.IMProto.IMAddToBlackListRequest.class,com.dxx.game.dto.IMProto.IMAddToBlackListRequest::parser,com.dxx.game.dto.IMProto.IMAddToBlackListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMAddToBlackListResponse, com.dxx.game.dto.IMProto.IMAddToBlackListResponse.class,com.dxx.game.dto.IMProto.IMAddToBlackListResponse::parser,com.dxx.game.dto.IMProto.IMAddToBlackListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMRemoveFromBlackListRequest, com.dxx.game.dto.IMProto.IMRemoveFromBlackListRequest.class,com.dxx.game.dto.IMProto.IMRemoveFromBlackListRequest::parser,com.dxx.game.dto.IMProto.IMRemoveFromBlackListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMRemoveFromBlackListResponse, com.dxx.game.dto.IMProto.IMRemoveFromBlackListResponse.class,com.dxx.game.dto.IMProto.IMRemoveFromBlackListResponse::parser,com.dxx.game.dto.IMProto.IMRemoveFromBlackListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMChatTextTranslateRequest, com.dxx.game.dto.IMProto.IMChatTextTranslateRequest.class,com.dxx.game.dto.IMProto.IMChatTextTranslateRequest::parser,com.dxx.game.dto.IMProto.IMChatTextTranslateRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMChatTextTranslateResponse, com.dxx.game.dto.IMProto.IMChatTextTranslateResponse.class,com.dxx.game.dto.IMProto.IMChatTextTranslateResponse::parser,com.dxx.game.dto.IMProto.IMChatTextTranslateResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMLoginRepeatMessage, com.dxx.game.dto.IMProto.IMLoginRepeatMessage.class,com.dxx.game.dto.IMProto.IMLoginRepeatMessage::parser,com.dxx.game.dto.IMProto.IMLoginRepeatMessage::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMReconnectMessage, com.dxx.game.dto.IMProto.IMReconnectMessage.class,com.dxx.game.dto.IMProto.IMReconnectMessage::parser,com.dxx.game.dto.IMProto.IMReconnectMessage::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMPushMessage, com.dxx.game.dto.IMProto.IMPushMessage.class,com.dxx.game.dto.IMProto.IMPushMessage::parser,com.dxx.game.dto.IMProto.IMPushMessage::newBuilder);
		messageProto.registerMessage(MsgReqCommand.IMErrorMessage, com.dxx.game.dto.IMProto.IMErrorMessage.class,com.dxx.game.dto.IMProto.IMErrorMessage::parser,com.dxx.game.dto.IMProto.IMErrorMessage::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ItemUseRequest, com.dxx.game.dto.ItemProto.ItemUseRequest.class,com.dxx.game.dto.ItemProto.ItemUseRequest::parser,com.dxx.game.dto.ItemProto.ItemUseRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ItemUseResponse, com.dxx.game.dto.ItemProto.ItemUseResponse.class,com.dxx.game.dto.ItemProto.ItemUseResponse::parser,com.dxx.game.dto.ItemProto.ItemUseResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionGetInfoRequest, com.dxx.game.dto.MissionProto.MissionGetInfoRequest.class,com.dxx.game.dto.MissionProto.MissionGetInfoRequest::parser,com.dxx.game.dto.MissionProto.MissionGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionGetInfoResponse, com.dxx.game.dto.MissionProto.MissionGetInfoResponse.class,com.dxx.game.dto.MissionProto.MissionGetInfoResponse::parser,com.dxx.game.dto.MissionProto.MissionGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionStartRequest, com.dxx.game.dto.MissionProto.MissionStartRequest.class,com.dxx.game.dto.MissionProto.MissionStartRequest::parser,com.dxx.game.dto.MissionProto.MissionStartRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionStartResponse, com.dxx.game.dto.MissionProto.MissionStartResponse.class,com.dxx.game.dto.MissionProto.MissionStartResponse::parser,com.dxx.game.dto.MissionProto.MissionStartResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionEndRequest, com.dxx.game.dto.MissionProto.MissionEndRequest.class,com.dxx.game.dto.MissionProto.MissionEndRequest::parser,com.dxx.game.dto.MissionProto.MissionEndRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionEndResponse, com.dxx.game.dto.MissionProto.MissionEndResponse.class,com.dxx.game.dto.MissionProto.MissionEndResponse::parser,com.dxx.game.dto.MissionProto.MissionEndResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionGetHangUpItemsRequest, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.class,com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest::parser,com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionGetHangUpItemsResponse, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.class,com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse::parser,com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionReceiveHangUpItemsRequest, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.class,com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest::parser,com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionReceiveHangUpItemsResponse, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.class,com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse::parser,com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionQuickHangUpRequest, com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.class,com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest::parser,com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MissionQuickHangUpResponse, com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.class,com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse::parser,com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PayInAppPurchaseRequest, com.dxx.game.dto.PayProto.PayInAppPurchaseRequest.class,com.dxx.game.dto.PayProto.PayInAppPurchaseRequest::parser,com.dxx.game.dto.PayProto.PayInAppPurchaseRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PayInAppPurchaseResponse, com.dxx.game.dto.PayProto.PayInAppPurchaseResponse.class,com.dxx.game.dto.PayProto.PayInAppPurchaseResponse::parser,com.dxx.game.dto.PayProto.PayInAppPurchaseResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PayPreOrderRequest, com.dxx.game.dto.PayProto.PayPreOrderRequest.class,com.dxx.game.dto.PayProto.PayPreOrderRequest::parser,com.dxx.game.dto.PayProto.PayPreOrderRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PayPreOrderResponse, com.dxx.game.dto.PayProto.PayPreOrderResponse.class,com.dxx.game.dto.PayProto.PayPreOrderResponse::parser,com.dxx.game.dto.PayProto.PayPreOrderResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PayOnUnityRequest, com.dxx.game.dto.PayProto.PayOnUnityRequest.class,com.dxx.game.dto.PayProto.PayOnUnityRequest::parser,com.dxx.game.dto.PayProto.PayOnUnityRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PayOnUnityResponse, com.dxx.game.dto.PayProto.PayOnUnityResponse.class,com.dxx.game.dto.PayProto.PayOnUnityResponse::parser,com.dxx.game.dto.PayProto.PayOnUnityResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PowerOnOpenRequest, com.dxx.game.dto.PowerProto.PowerOnOpenRequest.class,com.dxx.game.dto.PowerProto.PowerOnOpenRequest::parser,com.dxx.game.dto.PowerProto.PowerOnOpenRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PowerOnOpenResponse, com.dxx.game.dto.PowerProto.PowerOnOpenResponse.class,com.dxx.game.dto.PowerProto.PowerOnOpenResponse::parser,com.dxx.game.dto.PowerProto.PowerOnOpenResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PowerRewardRequest, com.dxx.game.dto.PowerProto.PowerRewardRequest.class,com.dxx.game.dto.PowerProto.PowerRewardRequest::parser,com.dxx.game.dto.PowerProto.PowerRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.PowerRewardResponse, com.dxx.game.dto.PowerProto.PowerRewardResponse.class,com.dxx.game.dto.PowerProto.PowerRewardResponse::parser,com.dxx.game.dto.PowerProto.PowerRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetLastLoginRequest, com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.class,com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest::parser,com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetLastLoginResponse, com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.class,com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse::parser,com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FindServerListRequest, com.dxx.game.dto.ServerListProto.FindServerListRequest.class,com.dxx.game.dto.ServerListProto.FindServerListRequest::parser,com.dxx.game.dto.ServerListProto.FindServerListRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FindServerListResponse, com.dxx.game.dto.ServerListProto.FindServerListResponse.class,com.dxx.game.dto.ServerListProto.FindServerListResponse::parser,com.dxx.game.dto.ServerListProto.FindServerListResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SevenDayTaskGetInfoRequest, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest.class,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest::parser,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SevenDayTaskGetInfoResponse, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse.class,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse::parser,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SevenDayTaskRewardRequest, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest.class,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest::parser,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SevenDayTaskRewardResponse, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse.class,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse::parser,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SevenDayTaskActiveRewardRequest, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest.class,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest::parser,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SevenDayTaskActiveRewardResponse, com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse.class,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse::parser,com.dxx.game.dto.SevenDayTaskProto.SevenDayTaskActiveRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopGetInfoRequest, com.dxx.game.dto.ShopProto.ShopGetInfoRequest.class,com.dxx.game.dto.ShopProto.ShopGetInfoRequest::parser,com.dxx.game.dto.ShopProto.ShopGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopGetInfoResponse, com.dxx.game.dto.ShopProto.ShopGetInfoResponse.class,com.dxx.game.dto.ShopProto.ShopGetInfoResponse::parser,com.dxx.game.dto.ShopProto.ShopGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopDoGachaRequest, com.dxx.game.dto.ShopProto.ShopDoGachaRequest.class,com.dxx.game.dto.ShopProto.ShopDoGachaRequest::parser,com.dxx.game.dto.ShopProto.ShopDoGachaRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopDoGachaResponse, com.dxx.game.dto.ShopProto.ShopDoGachaResponse.class,com.dxx.game.dto.ShopProto.ShopDoGachaResponse::parser,com.dxx.game.dto.ShopProto.ShopDoGachaResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopIntegralGetInfoRequest, com.dxx.game.dto.ShopProto.ShopIntegralGetInfoRequest.class,com.dxx.game.dto.ShopProto.ShopIntegralGetInfoRequest::parser,com.dxx.game.dto.ShopProto.ShopIntegralGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopIntegralGetInfoResponse, com.dxx.game.dto.ShopProto.ShopIntegralGetInfoResponse.class,com.dxx.game.dto.ShopProto.ShopIntegralGetInfoResponse::parser,com.dxx.game.dto.ShopProto.ShopIntegralGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopIntegralRefreshRequest, com.dxx.game.dto.ShopProto.ShopIntegralRefreshRequest.class,com.dxx.game.dto.ShopProto.ShopIntegralRefreshRequest::parser,com.dxx.game.dto.ShopProto.ShopIntegralRefreshRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopIntegralRefreshResponse, com.dxx.game.dto.ShopProto.ShopIntegralRefreshResponse.class,com.dxx.game.dto.ShopProto.ShopIntegralRefreshResponse::parser,com.dxx.game.dto.ShopProto.ShopIntegralRefreshResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopIntegralBuyItemRequest, com.dxx.game.dto.ShopProto.ShopIntegralBuyItemRequest.class,com.dxx.game.dto.ShopProto.ShopIntegralBuyItemRequest::parser,com.dxx.game.dto.ShopProto.ShopIntegralBuyItemRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopIntegralBuyItemResponse, com.dxx.game.dto.ShopProto.ShopIntegralBuyItemResponse.class,com.dxx.game.dto.ShopProto.ShopIntegralBuyItemResponse::parser,com.dxx.game.dto.ShopProto.ShopIntegralBuyItemResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopGacheWishRequest, com.dxx.game.dto.ShopProto.ShopGacheWishRequest.class,com.dxx.game.dto.ShopProto.ShopGacheWishRequest::parser,com.dxx.game.dto.ShopProto.ShopGacheWishRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopGacheWishResponse, com.dxx.game.dto.ShopProto.ShopGacheWishResponse.class,com.dxx.game.dto.ShopProto.ShopGacheWishResponse::parser,com.dxx.game.dto.ShopProto.ShopGacheWishResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopFreeIAPItemRequest, com.dxx.game.dto.ShopProto.ShopFreeIAPItemRequest.class,com.dxx.game.dto.ShopProto.ShopFreeIAPItemRequest::parser,com.dxx.game.dto.ShopProto.ShopFreeIAPItemRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.ShopFreeIAPItemResponse, com.dxx.game.dto.ShopProto.ShopFreeIAPItemResponse.class,com.dxx.game.dto.ShopProto.ShopFreeIAPItemResponse::parser,com.dxx.game.dto.ShopProto.ShopFreeIAPItemResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassGetInfoRequest, com.dxx.game.dto.ShopProto.BattlePassGetInfoRequest.class,com.dxx.game.dto.ShopProto.BattlePassGetInfoRequest::parser,com.dxx.game.dto.ShopProto.BattlePassGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassGetInfoResponse, com.dxx.game.dto.ShopProto.BattlePassGetInfoResponse.class,com.dxx.game.dto.ShopProto.BattlePassGetInfoResponse::parser,com.dxx.game.dto.ShopProto.BattlePassGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassRewardRequest, com.dxx.game.dto.ShopProto.BattlePassRewardRequest.class,com.dxx.game.dto.ShopProto.BattlePassRewardRequest::parser,com.dxx.game.dto.ShopProto.BattlePassRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassRewardResponse, com.dxx.game.dto.ShopProto.BattlePassRewardResponse.class,com.dxx.game.dto.ShopProto.BattlePassRewardResponse::parser,com.dxx.game.dto.ShopProto.BattlePassRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassChangeScoreRequest, com.dxx.game.dto.ShopProto.BattlePassChangeScoreRequest.class,com.dxx.game.dto.ShopProto.BattlePassChangeScoreRequest::parser,com.dxx.game.dto.ShopProto.BattlePassChangeScoreRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassChangeScoreResponse, com.dxx.game.dto.ShopProto.BattlePassChangeScoreResponse.class,com.dxx.game.dto.ShopProto.BattlePassChangeScoreResponse::parser,com.dxx.game.dto.ShopProto.BattlePassChangeScoreResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassFinalRewardRequest, com.dxx.game.dto.ShopProto.BattlePassFinalRewardRequest.class,com.dxx.game.dto.ShopProto.BattlePassFinalRewardRequest::parser,com.dxx.game.dto.ShopProto.BattlePassFinalRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.BattlePassFinalRewardResponse, com.dxx.game.dto.ShopProto.BattlePassFinalRewardResponse.class,com.dxx.game.dto.ShopProto.BattlePassFinalRewardResponse::parser,com.dxx.game.dto.ShopProto.BattlePassFinalRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MonthCardGetRewardRequest, com.dxx.game.dto.ShopProto.MonthCardGetRewardRequest.class,com.dxx.game.dto.ShopProto.MonthCardGetRewardRequest::parser,com.dxx.game.dto.ShopProto.MonthCardGetRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.MonthCardGetRewardResponse, com.dxx.game.dto.ShopProto.MonthCardGetRewardResponse.class,com.dxx.game.dto.ShopProto.MonthCardGetRewardResponse::parser,com.dxx.game.dto.ShopProto.MonthCardGetRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.VIPLevelRewardRequest, com.dxx.game.dto.ShopProto.VIPLevelRewardRequest.class,com.dxx.game.dto.ShopProto.VIPLevelRewardRequest::parser,com.dxx.game.dto.ShopProto.VIPLevelRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.VIPLevelRewardResponse, com.dxx.game.dto.ShopProto.VIPLevelRewardResponse.class,com.dxx.game.dto.ShopProto.VIPLevelRewardResponse::parser,com.dxx.game.dto.ShopProto.VIPLevelRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.LevelFundGetInfoRequest, com.dxx.game.dto.ShopProto.LevelFundGetInfoRequest.class,com.dxx.game.dto.ShopProto.LevelFundGetInfoRequest::parser,com.dxx.game.dto.ShopProto.LevelFundGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.LevelFundGetInfoResponse, com.dxx.game.dto.ShopProto.LevelFundGetInfoResponse.class,com.dxx.game.dto.ShopProto.LevelFundGetInfoResponse::parser,com.dxx.game.dto.ShopProto.LevelFundGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.LevelFundRewardRequest, com.dxx.game.dto.ShopProto.LevelFundRewardRequest.class,com.dxx.game.dto.ShopProto.LevelFundRewardRequest::parser,com.dxx.game.dto.ShopProto.LevelFundRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.LevelFundRewardResponse, com.dxx.game.dto.ShopProto.LevelFundRewardResponse.class,com.dxx.game.dto.ShopProto.LevelFundRewardResponse::parser,com.dxx.game.dto.ShopProto.LevelFundRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FirstRechargeRewardRequest, com.dxx.game.dto.ShopProto.FirstRechargeRewardRequest.class,com.dxx.game.dto.ShopProto.FirstRechargeRewardRequest::parser,com.dxx.game.dto.ShopProto.FirstRechargeRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.FirstRechargeRewardResponse, com.dxx.game.dto.ShopProto.FirstRechargeRewardResponse.class,com.dxx.game.dto.ShopProto.FirstRechargeRewardResponse::parser,com.dxx.game.dto.ShopProto.FirstRechargeRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SignInGetInfoRequest, com.dxx.game.dto.SignInProto.SignInGetInfoRequest.class,com.dxx.game.dto.SignInProto.SignInGetInfoRequest::parser,com.dxx.game.dto.SignInProto.SignInGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SignInGetInfoResponse, com.dxx.game.dto.SignInProto.SignInGetInfoResponse.class,com.dxx.game.dto.SignInProto.SignInGetInfoResponse::parser,com.dxx.game.dto.SignInProto.SignInGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SignInDoSignRequest, com.dxx.game.dto.SignInProto.SignInDoSignRequest.class,com.dxx.game.dto.SignInProto.SignInDoSignRequest::parser,com.dxx.game.dto.SignInProto.SignInDoSignRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.SignInDoSignResponse, com.dxx.game.dto.SignInProto.SignInDoSignResponse.class,com.dxx.game.dto.SignInProto.SignInDoSignResponse::parser,com.dxx.game.dto.SignInProto.SignInDoSignResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskGetInfoRequest, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.class,com.dxx.game.dto.TaskProto.TaskGetInfoRequest::parser,com.dxx.game.dto.TaskProto.TaskGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskGetInfoResponse, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.class,com.dxx.game.dto.TaskProto.TaskGetInfoResponse::parser,com.dxx.game.dto.TaskProto.TaskGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskRewardDailyRequest, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.class,com.dxx.game.dto.TaskProto.TaskRewardDailyRequest::parser,com.dxx.game.dto.TaskProto.TaskRewardDailyRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskRewardDailyResponse, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.class,com.dxx.game.dto.TaskProto.TaskRewardDailyResponse::parser,com.dxx.game.dto.TaskProto.TaskRewardDailyResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskRewardAchieveRequest, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.class,com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest::parser,com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskRewardAchieveResponse, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.class,com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse::parser,com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskActiveRewardRequest, com.dxx.game.dto.TaskProto.TaskActiveRewardRequest.class,com.dxx.game.dto.TaskProto.TaskActiveRewardRequest::parser,com.dxx.game.dto.TaskProto.TaskActiveRewardRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskActiveRewardResponse, com.dxx.game.dto.TaskProto.TaskActiveRewardResponse.class,com.dxx.game.dto.TaskProto.TaskActiveRewardResponse::parser,com.dxx.game.dto.TaskProto.TaskActiveRewardResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskActiveRewardAllRequest, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.class,com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest::parser,com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.TaskActiveRewardAllResponse, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.class,com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse::parser,com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserLoginRequest, com.dxx.game.dto.UserProto.UserLoginRequest.class,com.dxx.game.dto.UserProto.UserLoginRequest::parser,com.dxx.game.dto.UserProto.UserLoginRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserLoginResponse, com.dxx.game.dto.UserProto.UserLoginResponse.class,com.dxx.game.dto.UserProto.UserLoginResponse::parser,com.dxx.game.dto.UserProto.UserLoginResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetInfoRequest, com.dxx.game.dto.UserProto.UserGetInfoRequest.class,com.dxx.game.dto.UserProto.UserGetInfoRequest::parser,com.dxx.game.dto.UserProto.UserGetInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetInfoResponse, com.dxx.game.dto.UserProto.UserGetInfoResponse.class,com.dxx.game.dto.UserProto.UserGetInfoResponse::parser,com.dxx.game.dto.UserProto.UserGetInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserHeartbeatRequest, com.dxx.game.dto.UserProto.UserHeartbeatRequest.class,com.dxx.game.dto.UserProto.UserHeartbeatRequest::parser,com.dxx.game.dto.UserProto.UserHeartbeatRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserHeartbeatResponse, com.dxx.game.dto.UserProto.UserHeartbeatResponse.class,com.dxx.game.dto.UserProto.UserHeartbeatResponse::parser,com.dxx.game.dto.UserProto.UserHeartbeatResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserUpdateSystemMaskRequest, com.dxx.game.dto.UserProto.UserUpdateSystemMaskRequest.class,com.dxx.game.dto.UserProto.UserUpdateSystemMaskRequest::parser,com.dxx.game.dto.UserProto.UserUpdateSystemMaskRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserUpdateSystemMaskResponse, com.dxx.game.dto.UserProto.UserUpdateSystemMaskResponse.class,com.dxx.game.dto.UserProto.UserUpdateSystemMaskResponse::parser,com.dxx.game.dto.UserProto.UserUpdateSystemMaskResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserUpdateGuideMaskRequest, com.dxx.game.dto.UserProto.UserUpdateGuideMaskRequest.class,com.dxx.game.dto.UserProto.UserUpdateGuideMaskRequest::parser,com.dxx.game.dto.UserProto.UserUpdateGuideMaskRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserUpdateGuideMaskResponse, com.dxx.game.dto.UserProto.UserUpdateGuideMaskResponse.class,com.dxx.game.dto.UserProto.UserUpdateGuideMaskResponse::parser,com.dxx.game.dto.UserProto.UserUpdateGuideMaskResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserCancelAccountRequest, com.dxx.game.dto.UserProto.UserCancelAccountRequest.class,com.dxx.game.dto.UserProto.UserCancelAccountRequest::parser,com.dxx.game.dto.UserProto.UserCancelAccountRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserCancelAccountResponse, com.dxx.game.dto.UserProto.UserCancelAccountResponse.class,com.dxx.game.dto.UserProto.UserCancelAccountResponse::parser,com.dxx.game.dto.UserProto.UserCancelAccountResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserUpdateInfoRequest, com.dxx.game.dto.UserProto.UserUpdateInfoRequest.class,com.dxx.game.dto.UserProto.UserUpdateInfoRequest::parser,com.dxx.game.dto.UserProto.UserUpdateInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserUpdateInfoResponse, com.dxx.game.dto.UserProto.UserUpdateInfoResponse.class,com.dxx.game.dto.UserProto.UserUpdateInfoResponse::parser,com.dxx.game.dto.UserProto.UserUpdateInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetOtherPlayerInfoRequest, com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoRequest.class,com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoRequest::parser,com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetOtherPlayerInfoResponse, com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoResponse.class,com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoResponse::parser,com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetBattleReportRequest, com.dxx.game.dto.UserProto.UserGetBattleReportRequest.class,com.dxx.game.dto.UserProto.UserGetBattleReportRequest::parser,com.dxx.game.dto.UserProto.UserGetBattleReportRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetBattleReportResponse, com.dxx.game.dto.UserProto.UserGetBattleReportResponse.class,com.dxx.game.dto.UserProto.UserGetBattleReportResponse::parser,com.dxx.game.dto.UserProto.UserGetBattleReportResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserOpenModelRequest, com.dxx.game.dto.UserProto.UserOpenModelRequest.class,com.dxx.game.dto.UserProto.UserOpenModelRequest::parser,com.dxx.game.dto.UserProto.UserOpenModelRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserOpenModelResponse, com.dxx.game.dto.UserProto.UserOpenModelResponse.class,com.dxx.game.dto.UserProto.UserOpenModelResponse::parser,com.dxx.game.dto.UserProto.UserOpenModelResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserSetFormationByTypeRequest, com.dxx.game.dto.UserProto.UserSetFormationByTypeRequest.class,com.dxx.game.dto.UserProto.UserSetFormationByTypeRequest::parser,com.dxx.game.dto.UserProto.UserSetFormationByTypeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserSetFormationByTypeResponse, com.dxx.game.dto.UserProto.UserSetFormationByTypeResponse.class,com.dxx.game.dto.UserProto.UserSetFormationByTypeResponse::parser,com.dxx.game.dto.UserProto.UserSetFormationByTypeResponse::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetFormationByTypeRequest, com.dxx.game.dto.UserProto.UserGetFormationByTypeRequest.class,com.dxx.game.dto.UserProto.UserGetFormationByTypeRequest::parser,com.dxx.game.dto.UserProto.UserGetFormationByTypeRequest::newBuilder);
		messageProto.registerMessage(MsgReqCommand.UserGetFormationByTypeResponse, com.dxx.game.dto.UserProto.UserGetFormationByTypeResponse.class,com.dxx.game.dto.UserProto.UserGetFormationByTypeResponse::parser,com.dxx.game.dto.UserProto.UserGetFormationByTypeResponse::newBuilder);
		
	}
}

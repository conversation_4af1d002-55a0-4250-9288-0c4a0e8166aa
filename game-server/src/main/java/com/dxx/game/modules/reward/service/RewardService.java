package com.dxx.game.modules.reward.service;

import java.util.Collection;
import java.util.List;

import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.action.RewardActionSet;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.reward.result.RewardResultSet;

/**
 * 奖励接口
 *
 * <AUTHOR>
 * @date 2019-12-13 16:39
 */
public interface RewardService {

    /**
     * 奖励预判
     *
     * @param userId 角色id
     * @param reward 奖励
     * @return RewardActionSet
     */
    RewardAction tryReward(long userId, Reward reward);

    /**
     * 奖励预判
     *
     * @param userId 角色id
     * @param reward 奖励
     * @return RewardActionSet
     */
    RewardActionSet tryRewards(long userId, List<Integer> reward);

    /**
     * 奖励预判
     *
     * @param userId  角色id
     * @param rewards 奖励集合
     * @return RewardActionSet
     */
    RewardActionSet tryRewards(long userId, final Collection<Reward> rewards);

    /**
     * 执行奖励操作
     *
     * @param userId
     * @param rewardConfig 奖励配置表
     * @return
     */
    RewardResultSet executeReward(long userId, List<Integer> rewardConfig);

    /***
     * 执行奖励操作
     * @param userId
     * @param reward
     * @return
     */
    RewardResultSet executeReward(long userId, Reward reward);

    /**
     * 执行奖励操作
     *
     * @param userId       角色id
     * @param rewardAction rewardAction
     * @return ValueResultSet
     */
    RewardResultSet executeReward(long userId, RewardAction rewardAction);

    /**
     * 执行奖励操作
     *
     * @param userId
     * @param rewardConfig 奖励配置表
     * @return
     */
    RewardResultSet executeRewards(long userId, List<List<Integer>> rewardConfig);

    /**
     * 执行奖励操作
     *
     * @param userId
     * @param rewards
     * @return
     */
    RewardResultSet executeRewards(long userId, final Collection<Reward> rewards);

    /**
     * 执行奖励操作
     *
     * @param userId          角色id
     * @param rewardActionSet RewardAction集合
     * @return ValueResultSet
     */
    RewardResultSet executeRewards(long userId, RewardActionSet rewardActionSet);

    RewardResultSet executeCosts(long userId, List<List<Integer>> costConfig);

    RewardResultSet executeCost(long userId, List<Integer> costConfig);

    List<Integer> getCostList(List<Integer> costConfig);

    /**
     * 配置表整理成reward奖励数据
     *
     * @param rewardsConfig
     * @return
     */
    List<Reward> parseRewards(List<List<Integer>> rewardsConfig);

    List<Reward> parseRewards(List<List<Integer>> rewardsConfig, boolean combine);

    /**
     * 配置表数据整理成奖励数据
     *
     * @param rewardConfig
     * @return
     */
    List<Reward> parseReward(List<Integer> rewardConfig);


    /**
     * 合并奖励
     *
     * @param data
     * @return
     */
    List<List<Integer>> combineRewards(List<List<Integer>> data);

    /**
     * 合并奖励
     *
     * @param rewards
     */
    public void combineRewards(final Collection<Reward> rewards);

    List<List<Integer>> stringToRewardList(String str);

    List<Integer> checkRewardConfig(List<Integer> reward);
}






























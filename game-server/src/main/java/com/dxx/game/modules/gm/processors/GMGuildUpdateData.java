package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.common.GMCommonReqMsg;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import com.dxx.game.modules.guild.support.GuildSupport;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.GUILD_CHANGE_DATA)
public class GMGuildUpdateData extends AbstractGMProcessor {

    @Resource
    private GuildDao guildDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private GuildSupport guildSupport;

    @Data
    private static class GMRequest extends GMCommonReqMsg {
        private long guildId;
        private Map<String, String> data;
    }

    @Override
    protected Object execute(JSONObject params) {
        GMRequest request = params.toJavaObject(GMRequest.class);
        long guildId = request.getGuildId();
        Guild guild = guildDao.getByGuildId(guildId);
        if (guild == null) {
            return this.error(GMErrorCode.ERROR_CODE_GUILD_NOT_EXIST, "guild id not exist, id:" + guildId);
        }
        Map<String, String> dataObj = request.getData();
        boolean isModified = false;
        for (Map.Entry<String, String> entry : dataObj.entrySet()) {
            String key = entry.getKey();
            switch (key) {
                case "guildName":
                    // 判断名称是否修改
                    String guildName = entry.getValue();
                    if (!StringUtils.isEmpty(guildName)) {
                        //没有修改
                        if (guild.getGuildName() != null && !guild.getGuildName().equals(guildName)) {
                            // 避免昵称重复
                            String nameId = CryptUtil.md5(guildName);
                            if (!redisLock.lockWithOutRetry(nameId, nameId, 30000)) {
                                return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
                            }

                            // 判断昵称是否重复
                            Guild findGuild = guildDao.getByName(guildName);
                            if (findGuild != null) {
                                return Result.Error(ErrorCode.GUILD_NAME_REPEAT);
                            }

                            // 检测公会昵称
                            int checkCode = guildSupport.checkGuildName(guildName);
                            if (checkCode != ErrorCode.SUCCESS) {
                                return Result.Error(checkCode);
                            }

                            //更新
                            guild.setGuildName(guildName);
                        }
                        isModified = true;
                    }
                    break;
                default:
                    return this.error(GMErrorCode.ERROR_CODE_PARAMS_ERROR, "params error");
            }
        }

        if(!isModified) {
            return this.error(ErrorCode.SERVER_SYSTEM_ERROR, "something wrong :not modify any thing");
        }

        //更新
        guildDao.updateGuildInfo(guild);

        return this.success();
    }
}

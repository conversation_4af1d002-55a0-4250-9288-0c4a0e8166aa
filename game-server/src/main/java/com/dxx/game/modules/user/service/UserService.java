
package com.dxx.game.modules.user.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.model.UserToken;
import com.dxx.game.consts.CardPermission;
import com.dxx.game.consts.VipPermission;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.CommonProto.CommonParams;
import com.dxx.game.dto.UserProto.*;

import java.util.List;
import java.util.Map;

/**
 * 用户相关模块接口
 * <AUTHOR>
 * @date 2019-12-09 20:41
 */
public interface UserService {

	/**
	 * 用户登录
	 */
	Result<UserLoginResponse> loginAction(UserLoginRequest params, String ip);

	/**

	 /**
	 * 获取用户数据
	 * @param params
	 * @return
	 */
	Result<UserGetInfoResponse> getInfoAction(UserGetInfoRequest params);
	
	/**
	 * 客户端心跳
	 * @param params
	 * @return
	 */
	Result<UserHeartbeatResponse> heartbeatAction(UserHeartbeatRequest params);
	
	
	/**
	 * 更新系统掩码
	 * @param params
	 * @return
	 */
	Result<UserUpdateSystemMaskResponse> updateSystemMaskAction(UserUpdateSystemMaskRequest params);

	/**
	 * 更新新手引导步骤
	 * @param params
	 * @return
	 */
	Result<UserUpdateGuideMaskResponse> updateGudeMaskAction(UserUpdateGuideMaskRequest params);

	/**
	 * 注销账号
	 * @param params
	 * @return
	 */
	Result<UserCancelAccountResponse> cancelAccountAction(UserCancelAccountRequest params);

	/**
	 * 更新头像昵称
	 * @param params
	 * @return
	 */
	Result<UserUpdateInfoResponse> updateInfoAction(UserUpdateInfoRequest params);

	//--------------------------------------------------------------------------------------------

	Result<UserGetOtherPlayerInfoResponse> getOtherInfo(UserGetOtherPlayerInfoRequest params);


	Result<UserGetBattleReportResponse> getBattleReport(UserGetBattleReportRequest params);

	/**
	 * 用户开启模块
	 * @param params
	 * @return
	 */
	Result<UserOpenModelResponse> userOpenModel(UserOpenModelRequest params);

	Result<UserSetFormationByTypeResponse> userSetFormationByType(UserSetFormationByTypeRequest params);

	Result<UserGetFormationByTypeResponse> userGetFormationByType(UserGetFormationByTypeRequest params);

	/**
	 * 获取user数据
	 * @param userId
	 * @return
	 */
	User getUser(long userId);

	User DevCreateRobot(int serverId);

	/**
	 * 生成accessToken
	 * @return
	 */
	String createAccessToken(User user);

	/**
	 * 解析token
	 * @param commonParams
	 * @return
	 */
	int parseAccessToken(CommonParams commonParams);

	UserToken parseAccessToken(String accessToken);
	/**
	 * 登录状态检查，防双开窗口
	 */
	int checkLoginState(boolean checkDoubleClient);

	/**
	 * 获取章节id
	 */
	int getChapterId(long userId);

	long getTransId(long userId);

	long getMaxTransId(long userId);

    List<PlayerInfoDto> getPlayerInfos(List<Long> userIds);


	// 检查vip权限(用于通过vip获取权限效果值)
	double vipPermissionValueCheck(long userId, VipPermission vipPermission);

	int vipPermissionValueIntCheck(long userId, VipPermission vipPermission);

	Double cardPermissionValueCheck(long userId, CardPermission permission);

	// 触发vip权限(用于vip变化时，处理权限效果)
	void vipPermissionTrigger(long userId);


	int getPrivateChatChapterLimit(Integer value);

	long getOpenServerTime(User user);

	long getSystemOpenServerTime(User user);

	long getWarOpenServerTime(User user);

	long getWarSystemOpenServerTime(User user);

	// 获取战力
	long getPower(long userId);

	Map<Long, Long> getPower(List<Long> userIds);

}

























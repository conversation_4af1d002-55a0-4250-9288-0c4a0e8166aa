
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class SevenDay_SevenDayActiveReward {
    public SevenDay_SevenDayActiveReward(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        NeedActive = _buf.get("NeedActive").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Reward").getAsJsonArray(); Reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Reward.add(_v0); }   }
        IfEquip = _buf.get("IfEquip").getAsInt();
    }

    public static SevenDay_SevenDayActiveReward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.SevenDay_SevenDayActiveReward(_buf);
    }

    /**
     * ID<br/>
     */
    public final int ID;
    /**
     * 需要的活跃度
     */
    public final int NeedActive;
    /**
     * 活跃度奖励<br/>道具id,道具数量|…
     */
    public final java.util.List<String> Reward;
    /**
     * 是否<br/>装备奖励
     */
    public final int IfEquip;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "NeedActive:" + NeedActive + ","
        + "Reward:" + Reward + ","
        + "IfEquip:" + IfEquip + ","
        + "}";
    }
}


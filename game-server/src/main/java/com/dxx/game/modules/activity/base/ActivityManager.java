package com.dxx.game.modules.activity.base;

import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.Triple;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.event.EventEntity;
import com.dxx.game.config.entity.event.RankEntity;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.activity.Activity;
import com.dxx.game.dao.dynamodb.model.activity.ActivityBase;
import com.dxx.game.dto.ActivityProto;
import com.dxx.game.modules.activity.base.data.ActivityMetaData;
import com.dxx.game.modules.mail.MailService;
import com.dxx.game.modules.rank.support.RankHelper;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.user.service.UserService;
import com.google.protobuf.Message;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: lsc
 * @createDate: 2025/6/3
 * @description:
 */
@Slf4j
@Component
public class ActivityManager {

    @Resource
    private ActivityRegistry activityRegistry;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private MailService mailService;
    @Resource
    private UserService userService;
    @Resource
    private ServerListService serverListService;

    public static final int RANK_REWARD_MAX_DAY = 30;       // 排行榜领奖最长天数

    @Getter
    private static ActivityManager instance;

    @PostConstruct
    private void init() {
        instance = this;
    }

    /**
     * 将各活动dto设置到response中
     */
    @SuppressWarnings("unchecked")
    public void populateActivityListResponse(long userId, ActivityProto.ActivityGetListResponse.Builder response) {

        for (ActivityMetaData metaData : activityRegistry.getActivityMetaDataList()) {
            var model = metaData.getActivityLifeCycle().fetchModel(userId, true);
            if (model == null) {
                continue;
            }

            Message dto = metaData.getActivityLifeCycle().buildDto(model);

            metaData.addDtoToResponse(response, dto);
        }
    }

    @SuppressWarnings("unchecked")
    public <M extends ActivityBase, D extends Message> M processActivity(long userId, Activity activity, M model, ActivityMetaData<M, D> metaData) {
        ActivityLifeCycle<M, D> lifeCycle = metaData.getActivityLifeCycle();

        Pair<Long, Long> time = null;
        if (model != null) {
            time = this.calculateTime(model.getActivityId());

            if (!DateUtils.isBetween(DateUtils.getUnixTime(), time.getLeft(), time.getRight())) {
                lifeCycle.onActivityEnd(model);
                if (!model.getIsRankMail()) {
                    boolean flag = ActivityManager.getInstance().sendRankMailReward(model.getUserId(), model.getActivityId(), model.getRankMailUniqueId());
                    if (flag) {
                        model.setIsRankMail(true);
                        metaData.updateModel(activity, model);
                    }
                }
            }
            if (this.canRemove(time.getRight())) {
                // 删除活动数据
                metaData.deleteModel(activity, model);
            }
        }

        // 检测是否有新开启的活动
        var eventData = getEntityByType(metaData.getActivityType());
        if (eventData == null) {
            return null;
        }

        var eventEntity = eventData.getFirst();
        var startTime = eventData.getSecond();
        var endTime = eventData.getThird();
        int activityId = eventEntity.getId();
        if (model == null || model.getActivityId() != activityId) {
            model = metaData.createNewModel();
            model.setUserId(userId);
            model.setActivityId(activityId);
            model.setStartTime(startTime);
            model.setEndTime(endTime);
            model.setRefreshTime(DateUtils.getSystemResetTime());
            model.setIsFirst(false);
            model.setIsMail(false);
            model.setMailUniqueId(mailService.getUniqueId());
            model.setIsRankMail(false);
            model.setRankMailUniqueId(mailService.getUniqueId());
            lifeCycle.initializeModel(userId, model);
            metaData.updateModel(activity, model);

        }

        return model;
    }

    public Triple<EventEntity, Long, Long> getEntityByType(int activityType) {
        long nowTimestamp = DateUtils.getUnixTime();
        for (EventEntity entity : gameConfigManager.getEventConfig().getEvent().values()) {
            if (entity.getIsOpen() == 0 || entity.getType() != activityType) {
                continue;
            }
            // TODO 判断客户端版本
            Pair<Long, Long> time = this.calculateTime(entity);
            if (nowTimestamp >= time.getLeft() && nowTimestamp <= time.getRight()) {
                return Triple.of(entity, time.getLeft(), time.getRight());
            }
        }
        return null;
    }

    public List<Triple<EventEntity, Long, Long>> getOpenConfig() {
        long nowTimestamp = DateUtils.getUnixTime();
        List<Triple<EventEntity, Long, Long>> result = new ArrayList<>();
        for (EventEntity entity : gameConfigManager.getEventConfig().getEvent().values()) {
            if (entity.getIsOpen() == 0) {
                continue;
            }

            // TODO 判断客户端版本
            Pair<Long, Long> time = this.calculateTime(entity);
            if (nowTimestamp >= time.getLeft() && nowTimestamp <= time.getRight()) {
                result.add(Triple.of(entity, time.getLeft(), time.getRight()));
            }
        }
        return result;
    }

    public Pair<Long, Long> calculateTime(EventEntity eventEntity) {
        long openTime = eventEntity.getOpenTime();
        int duration = eventEntity.getDurationTime();

        long startTime = 0;
        long endTime = 0;
        if (eventEntity.getOpenType() == 1) {       // 根据服务器开服时间开启
            User user = userService.getUser(RequestContext.getUserId());
            if (this.isWarZone(eventEntity)) {
                startTime = userService.getWarSystemOpenServerTime(user) + (openTime * DateUtils.SECONDS_PRE_DAY);
            } else {
                startTime = userService.getSystemOpenServerTime(user) + (openTime * DateUtils.SECONDS_PRE_DAY);
            }

            endTime = DateUtils.getSystemResetTimeByTime(startTime + ((long) duration * DateUtils.SECONDS_PRE_DAY)) - DateUtils.SECONDS_PRE_DAY;
        } else {                                    // 配置表绝对时间
            startTime = eventEntity.getOpenTime();
            endTime = eventEntity.getEndTime();
        }

        return Pair.of(startTime, endTime);
    }

    public Pair<Long, Long> calculateTime(int activityId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity == null) {
            return Pair.of(0L, 0L);
        }
        return this.calculateTime(eventEntity);
    }

    public boolean isEnd(int activityId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity == null || eventEntity.getIsOpen() == 0) {
            return false;
        }
        var time = this.calculateTime(eventEntity);
        return DateUtils.getUnixTime() >= time.getRight();
    }

    public boolean isOpen(int activityId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity == null || eventEntity.getIsOpen() == 0) {
            return false;
        }
        var time = this.calculateTime(eventEntity);
        return DateUtils.isBetween(DateUtils.getUnixTime(), time.getLeft(), time.getRight());
    }

    // 活动是否过期了15天
    public boolean canRemove(int activityId) {
        var activityTime = ActivityManager.getInstance().calculateTime(activityId);
        return this.canRemove(activityTime.getRight());
    }

    // 活动是否过期了15天
    public boolean canRemove(long endTime) {
        return DateUtils.getUnixTime() >= endTime + DateUtils.SECONDS_15_DAY;
    }

    public boolean isWarZone(EventEntity entity) {
        return entity.getSrvType() == 2;
    }

    /**
     * 覆盖排行榜的score
     */
    public void commitRank(long userId, int activityId, long score) {
        this.setRank(userId, activityId, score, false);
    }

    /**
     * 排行榜自增
     */
    public void incRank(long userId, int activityId, long score) {
        this.setRank(userId, activityId, score, true);
    }

    private void setRank(long userId, int activityId, long score, boolean isInc) {
        if (!this.isOpen(activityId)) {
            return;
        }

        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity.getRankGroupType() == 0) {
            // 没有排行榜
            return;
        }
        int expire = RANK_REWARD_MAX_DAY + eventEntity.getDurationTime();
        String rankKey = this.getRankKey(userId, activityId);
        if (isInc) {
            RankHelper.inc(rankKey, userId, score, expire, TimeUnit.DAYS);
        } else {
            RankHelper.commit(rankKey, userId, score, expire, TimeUnit.DAYS);
        }
    }

    /**
     * 根据活动id获取用户分数
     */
    public long getRankScoreByActivityId(long userId, int activityId) {
        return RankHelper.getScore(this.getRankKey(userId, activityId), userId);
    }


    /**
     * 获取排行榜的key
     */
    public String getRankKey(long userId, int activityId) {
        User user = userService.getUser(userId);
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (ActivityManager.getInstance().isWarZone(eventEntity)) {
            return getWarZoneRankKey(serverListService.getServerWarZoneId(user.getServerId()), activityId);
        }
        return getServerRankKey(user.getServerId(), activityId);
    }

    private String getServerRankKey(int serverId, int eventId) {
        return "r:" + "et:l:" + eventId + ":" + serverId;
    }

    // 战区-跨服排行
    private String getWarZoneRankKey(int warId, int eventId) {
        return "r:" + "et:w:" + eventId + ":" + warId;
    }

    /**
     * 发送排行榜奖励
     */
    public boolean sendRankMailReward(long userId, int activityId, long mailUniqueId) {
        EventEntity eventEntity = gameConfigManager.getEventConfig().getEventEntity(activityId);
        if (eventEntity == null) {
            return true;
        }

        var time = this.calculateTime(activityId);
        long now = DateUtils.getUnixTime();
        long finishTime = time.getRight() + (RANK_REWARD_MAX_DAY * DateUtils.SECONDS_PRE_DAY);
        if (now > finishTime) {
            return true;
        }

        int groupType = eventEntity.getRankGroupType();
        if (groupType == 0) {
            return true;
        }
        String rankKey = this.getRankKey(userId, activityId);
        int rank = RankHelper.getRank(rankKey, userId);
        if (rank <= 0) {
            return true;
        }

        List<RankEntity> rewardEntities = gameConfigManager.getEventConfig().getRank().values().stream()
                .filter(v -> v.getGroupType() == groupType).collect(Collectors.toList());
        if (CollectionUtils.isNullOrEmpty(rewardEntities)) {
            return true;
        }
        List<List<Integer>> rewards = new ArrayList<>();
        for (RankEntity v : rewardEntities) {
            List<Integer> r = v.getRank();
            if (rank >= r.get(0) && rank <= r.get(1)) {
                rewards = v.getReward();
                break;
            }
        }
        if (rewards.isEmpty()) {
            return true;
        }

        Map<String, String> params = new HashMap<>();
        params.put("rank", String.valueOf(rank));
        String id = gameConfigManager.getEventMailTempId(eventEntity);
        String uniqueId = userId + "_activity_rank_" + rank + "_" + eventEntity.getId() + "_" + mailUniqueId;

        boolean flag = mailService.createMail(userId, id, uniqueId, params, rewards, "event", eventEntity.getId());
        log.info("sendRankMail userId:{}, activityId:{}, rank:{}, rewards:{}", userId, eventEntity.getId(), rank, rewards);
        if (!flag) {
            log.error("sendRankMail error, userId:{}, activityId:{}, rank:{}, rewards:{}", userId, eventEntity.getId(), rank, rewards);
        }
        return flag;
    }

}

package com.dxx.game.modules.mission.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dto.MissionProto.*;
import com.dxx.game.modules.mission.service.MissionService;
import com.google.protobuf.Message;
import org.springframework.beans.factory.annotation.Autowired;

@ApiHandler
public class MissionHandler {
	
	@Autowired
	private MissionService missionService;

	@ApiMethod(command = MsgReqCommand.MissionGetInfoRequest, name = "关卡-获取数据")
	public Result<MissionGetInfoResponse> getInfo(Message msg) {
		MissionGetInfoRequest params = (MissionGetInfoRequest)msg;
		return missionService.getInfoAction(params);
	}

	@ApiMethod(command = MsgReqCommand.MissionStartRequest, name = "关卡-开始挑战")
	public Result<MissionStartResponse> missionStart(Message msg) {
		MissionStartRequest params = (MissionStartRequest)msg;
		return missionService.startAction(params);
	}

	@ApiMethod(command = MsgReqCommand.MissionEndRequest, name = "关卡-战斗结算")
	public Result<MissionEndResponse> missionEnd(Message msg) {
		MissionEndRequest params = (MissionEndRequest)msg;
		return missionService.missionEndAction(params);
	}
}
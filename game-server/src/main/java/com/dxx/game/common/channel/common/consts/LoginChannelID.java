package com.dxx.game.common.channel.common.consts;

/**
 * @authoer: lsc
 * @createDate: 2022/6/28
 * @description:
 */
public enum LoginChannelID {

    UNITY(0, "unity"),
    APPLE(1, "apple"),
    GOOGLE(2, "google"),
    HUAWEI_OVERSEAS(3, "huaweiOverseas"),

    OFFICIAL(100, "official"),      // 国内官网
    OFFICIAL_TAPTAP(101, "official_taptap"),
    OFFICIAL_TOUTIAO(102, "official_toutiao"),
    OFFICIAL_KUAISHOU(103, "official_kuaishou"),
    OFFICIAL_FENSITONG(104, "official_fensitong"),
    OFFICIAL_YOULIANGHUI(105, "official_youlianghui"),
    OFFICIAL_HAOYOUKUAIBAO(106, "official_haoyouku<PERSON>bao"),
    OFFICIAL_CHUANSHANJIA(107, "official_chuan<PERSON><PERSON>a"),
    OFFICIAL_SHANXIAN(108, "official_shanxian"),
    OFFICIAL_MOMOYU(109, "official_momoyu"),
    OFFICIAL_BIYADI(110, "official_biyadi"),
    OFFICIAL_HARMONY(111, "official_harmony"),

    HUAWEI(201,"huawei"),
    YYB(202,"yyb"),
    XiaoMi(203,"xiaomi"),
    Oppo(204,"oppo"),
    Vivo(205,"vivo"),
    Ssjj(206,"ssjj"),
    UC(207,"uc"),
    Bili(208,"bili"),
    DOUYIN(209,"douyin"),
    HONOR(210, "honor"),
    HARMONY(211, "harmony"),

    WECHAT_MINI_GAME(301, "weChatMiniGame"),

    DOUYIN_MINI_GAME(401, "douyinMiniGame");



    private int id;
    private String name;

    LoginChannelID(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public static LoginChannelID valueOf(int id) {
        for (LoginChannelID index : LoginChannelID.values()) {
            if (index.getId() == id) {
                return index;
            }
        }
        return null;
    }
}

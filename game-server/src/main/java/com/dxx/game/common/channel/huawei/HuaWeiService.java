package com.dxx.game.common.channel.huawei;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.internal.util.codec.Base64;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.huawei.model.HuaWeiConfig;
import com.dxx.game.common.channel.huawei.model.HuaWeiPurchaseData;
import com.dxx.game.common.redis.RedisService;
import com.google.common.collect.Maps;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Security;
import java.security.spec.X509EncodedKeySpec;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 华为
 * <AUTHOR>
 * @date 2022/4/21 14:25
 */
@Slf4j
@DependsOn("okHttpClient")
@Service
public class HuaWeiService implements ChannelService {

    private static final String LOCK_KEY = "huawei_token_lock";
    private static final String LOCK_VALUE = "1";
    private static final long EXPIRE_MILLIS_SECONDS = 60000;

    private static final String REDIS_TOKEN_KEY = "huawei_token";

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private RedisService redisService;


//    @PostConstruct
//    private void init() {
//        String test = "{\"autoRenewing\":false,\"orderId\":\"202002260944239974b108b8f1.*********\",\"packageName\":\"com.habby.cnarchero.huawei\",\"applicationId\":*********,\"kind\":0,\"productId\":\"a_com.habby.archero_d4\",\"productName\":\"2500钻石\",\"purchaseTime\":1582681472000,\"purchaseTimeMillis\":1582681472000,\"purchaseState\":0,\"developerPayload\":\"1656a1e1-6a0b-4f11-8947-054d59846fe0\",\"purchaseToken\":\"000001707f2ac04cedbdcc5f5135104b2e4883996c5f778f65c2a7d844a9a159010cbe2028641082.1.*********\",\"consumptionState\":0,\"acknowledged\":0,\"currency\":\"CNY\",\"price\":1,\"country\":\"CN\",\"payOrderId\":\"A8b9f083bf2e5b025832ebca03bba675\",\"payType\":\"4\"}";
//        JSONObject jsonObject = JSON.parseObject(test);


//        this.verifyToken(jsonObject);

//        System.out.println(this.reqAccessToken());
//    }

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        try {
            HuaWeiConfig huaWeiConfig = channelConfig.getHuaWeiConfig();
            if (!huaWeiConfig.isVerifyLogin()) {
                return true;
            }

            JSONObject jsonObject = JSONObject.parseObject(verification);
            String openId = jsonObject.getString("openId");
            if (!accountId.equals(openId)) {
                return false;
            }
            String accessToken = jsonObject.getString("accessToken");

            Map<String, String> params = new HashMap<>();
            params.put("method", "external.hms.gs.getTokenInfo");
            params.put("accessToken", accessToken);
//            params.put("open_id", "OPENID");
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-type", "application/x-www-form-urlencoded");

            String resp = OkHttpClientUtil.postForm(huaWeiConfig.getVerifyLoginUrl(), headers, params, String.class);
            if (StringUtils.isEmpty(resp)) {
                log.error("huawei verfiyLogin resp is empty, verification:{}", verification);
                return true;
            }
            JSONObject respObj = JSONObject.parseObject(resp);
            int rtnCode = respObj.getIntValue("rtnCode");
            if (rtnCode != 0) {
                log.error("huawei verfiyLogin failed resp:{}", resp);
                return false;
            }

            String receiveOpenId = respObj.getString("openId");
            if (!receiveOpenId.equals(accountId)) {
                log.error("huawei verifyLogin failed, resp:{}", resp);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("huawei verifyLogin error:", e);
            return false;
        }
    }

    /**
     * 验证购买token
     */
    public HuaWeiPurchaseData verifyToken(JSONObject purchaseData) {
        HuaWeiConfig huaWeiConfig = channelConfig.getHuaWeiConfig();
        long applicationId = purchaseData.getLongValue("applicationId");
        if (applicationId != Long.parseLong(huaWeiConfig.getAppId())) {
            log.error("payCb app_id error receive:{}, real:{}", applicationId, huaWeiConfig.getAppId());
            return null;
        }

        String productId = purchaseData.getString("productId");
        String purchaseToken = purchaseData.getString("purchaseToken");

        String accessToken = this.getAccessToken();

        Map<String, String> headers = this.buildAuthorization(accessToken);

        Map<String, String> bodyMap = Maps.newHashMap();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String url = huaWeiConfig.getCheckUrl();
        String response = OkHttpClientUtil.postJson(url, headers, bodyMap, String.class);

        JSONObject respObj = JSON.parseObject(response);
        int responseCode = Integer.parseInt(respObj.getString("responseCode"));
        if (responseCode != 0) {
            log.error("payCb check failed response:{}", response);
            return null;
        }

//        boolean signRet = this.checkSign(respObj.getString("purchaseTokenData"), respObj.getString("dataSignature"), channelConfig.getHuaWeiConfig().getPayPublicKey(), respObj.getString("signatureAlgorithm"));
//        if (!signRet) {
//            log.error("payCb check sign failed response:{}", response);
//            return null;
//        }

        JSONObject purchaseTokenData = respObj.getJSONObject("purchaseTokenData");
        int purchaseState = purchaseTokenData.getIntValue("purchaseState");
        if (purchaseState != 0) {
            log.error("payCb purchaseState error value:{}", purchaseState);
            return null;
        }

        int price = purchaseTokenData.getIntValue("price");

        HuaWeiPurchaseData data = new HuaWeiPurchaseData();
        data.setOrderId(purchaseTokenData.getString("orderId"));
        data.setProductId(purchaseTokenData.getString("productId"));
        data.setSandBox(purchaseTokenData.containsKey("purchaseType"));
        data.setPrice(price);
        return data;
    }


    public static boolean checkSign(String content, String sign, String publicKey, String signatureAlgorithm) {
        if (sign == null) {
            return false;
        }
        if (publicKey == null) {
            return false;
        }

        // 当signatureAlgorithm为空时使用默认签名算法
        if (StringUtils.isEmpty(signatureAlgorithm)) {
            signatureAlgorithm = "SHA256WithRSA";
        }
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            // 生成"RSA"的KeyFactory对象
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] decodedKey = Base64.decodeBase64String(publicKey);
            // 生成公钥
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(decodedKey));
            java.security.Signature signature = null;
            // 根据SHA256WithRSA算法获取签名对象实例
            signature = java.security.Signature.getInstance(signatureAlgorithm);
            // 初始化验证签名的公钥
            signature.initVerify(pubKey);
            // 把原始报文更新到签名对象中
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            // 将sign解码
            byte[] bsign = Base64.decodeBase64String(sign);
            // 进行验签
            return signature.verify(bsign);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 确认购买
     */
    public void confirmPurchase(JSONObject purchaseData) {
        HuaWeiConfig huaWeiConfig = channelConfig.getHuaWeiConfig();
        String productId = purchaseData.getString("productId");
        String purchaseToken = purchaseData.getString("purchaseToken");
        String accessToken = this.getAccessToken();

        Map<String, String> headers = this.buildAuthorization(accessToken);
        Map<String, String> bodyMap = Maps.newHashMap();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String url = huaWeiConfig.getConfirmUrl();

        String response = OkHttpClientUtil.postJson(url, headers, bodyMap, String.class);
        if (StringUtils.isEmpty(response)) {
            log.error("payCb confirm failed response:{}", response);
            return;
        }
        JSONObject respObj = JSON.parseObject(response);
        int responseCode = Integer.parseInt(respObj.getString("responseCode"));
        if (responseCode != 0) {
            log.error("payCb confirm failed response:{}", response);
        }
    }

    private Map<String, String> buildAuthorization(String accessToken) {
        String oriString = MessageFormat.format("APPAT:{0}", accessToken);
        String authorization =
                MessageFormat.format("Basic {0}", Base64.encodeBase64String(oriString.getBytes(StandardCharsets.UTF_8)));
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        return headers;
    }

    /**
     * 从华为api获取access_token
     */
    private String reqAccessToken() {
        try {
            HuaWeiConfig huaWeiConfig = channelConfig.getHuaWeiConfig();
            String grantType = "client_credentials";

            Map<String, String> params = new HashMap<>();
            params.put("grant_type", grantType);
            params.put("client_secret", URLEncoder.encode(huaWeiConfig.getAppSecret(), "UTF-8"));
            params.put("client_id", huaWeiConfig.getAppId());

            String response = OkHttpClientUtil.postForm(huaWeiConfig.getTokenUrl(), params, String.class);
            JSONObject respObj = JSONObject.parseObject(response);
            String token = respObj.getString("access_token");
            int expiresIn = respObj.getIntValue("expires_in");

            // redis 保存token
            redisService.set(REDIS_TOKEN_KEY, token);
            // 过期时间 - 60秒
            redisService.expireKey(REDIS_TOKEN_KEY, expiresIn - 60, TimeUnit.SECONDS);

            return token;
        } catch (Exception e) {
            log.error("payCb getAccessToken failed, e:", e);
            return null;
        }
    }

    /**
     * 查询token
     * @returnf
     */
    public String getAccessToken() {
        String accessToken = redisService.get(REDIS_TOKEN_KEY);
        if (StringUtils.isEmpty(accessToken)) {
            int loopCnt = 0;
            do {
                accessToken = this.reqAccessToken();
                loopCnt++;
            } while (loopCnt != 3 && StringUtils.isEmpty(accessToken));
        }
        return accessToken;
    }


    @Override
    public Object payCb(FullHttpRequest request) {
        return null;
    }
}

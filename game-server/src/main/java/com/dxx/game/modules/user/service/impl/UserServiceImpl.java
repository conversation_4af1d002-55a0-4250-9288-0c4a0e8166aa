package com.dxx.game.modules.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBWriteType;
import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.channel.common.consts.LoginChannelID;
import com.dxx.game.common.redis.RedisLock;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.server.model.UserToken;
import com.dxx.game.common.server.util.RequestIdUtil;
import com.dxx.game.common.utils.*;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.avatar.AvatarEntity;
import com.dxx.game.config.entity.cloudconsts.DataEntity;
import com.dxx.game.config.entity.gameconfig.ConfigEntity;
import com.dxx.game.config.entity.iap.MonthCardEntity;
import com.dxx.game.config.entity.playeravatar.PlayerNameEntity;
import com.dxx.game.config.entity.vip.VipEntity;
import com.dxx.game.consts.*;
import com.dxx.game.dao.dynamodb.model.*;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.*;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CommonProto.CommonParams;
import com.dxx.game.dto.UserProto;
import com.dxx.game.dto.UserProto.*;
import com.dxx.game.modules.activity.model.EventTaskProcess;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;
import com.dxx.game.modules.activity.service.ActivityService;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.service.SensitiveWordsService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.function.support.FunctionHelp;
import com.dxx.game.modules.hero.service.HeroService;
import com.dxx.game.modules.im.service.IMService;
import com.dxx.game.modules.item.service.ItemService;
import com.dxx.game.modules.log.service.LogService;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.pvp.support.BattleUnitCache;
import com.dxx.game.modules.pvp.support.PVPHelper;
import com.dxx.game.modules.reward.model.ResourceReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.shop.service.ShopService;
import com.dxx.game.modules.task.model.TaskProcess;
import com.dxx.game.modules.task.service.TaskService;
import com.dxx.game.modules.user.service.AccountService;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.user.support.AdSupport;
import com.dxx.game.modules.user.support.PowerSupport;
import com.dxx.game.modules.user.support.VitalitySupport;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private CommonService commonService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RewardService rewardService;
    @Resource
    private UserDao userDao;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private ItemService itemService;
    @Resource
    private EquipService equipService;
    @Resource
    private ShopDao shopDao;
    @Resource
    private LogResourceDao logResourceDao;
    @Resource
    private FrozeDeviceDao frozeDeviceDao;
    @Resource
    private AccountService accountService;
    @Resource
    private SensitiveWordsService sensitiveWordsService;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private RedisLock redisLock;
    @Resource
    private EquipDao equipDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private GuildUserDao guildUserDao;

    @Resource
    private HeroService heroService;

    @Resource
    private ReportDao reportDao;
    @Resource
    private PVPHelper pvpHelper;

    @Resource
    private TaskService taskService;
    @Resource
    private ActivityService activityService;
    @Resource
    private SevenDayTaskService sevenDayTaskService;
    @Resource
    private PayService payService;
    @Resource
    private BattleUnitCacheRedisDao battleRedisDao;
    @Resource
    private HeroDao heroDao;
    @Resource
    private VitalitySupport vitalitySupport;
    @Resource
    private AccountDao accountDao;
    @Resource
    private ServerListService serverListService;

    @Resource
    private FunctionHelp functionHelp;
    @Resource
    private ServerDao serverDao;
    @Resource
    private RedisService redisService;
    @Resource
    private BattleUnitCache formationCache;
    @Resource
    private IMService imService;
    @Resource
    private PowerSupport powerSupport;
    @Resource
    private ShopService shopService;
    @Resource
    private LogService logService;
    @Resource
    private AdSupport adSupport;

    @DynamoDBTransactional
    @Override
    public Result<UserLoginResponse> loginAction(UserLoginRequest params, String ip) {
        if ((params.getCommonParams().getDeviceId().isEmpty() && params.getCommonParams().getAccountId().isEmpty())
                || params.getCommonParams().getAccountId().length() >= 256 || params.getCommonParams().getDeviceId().length() >= 128) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isEmpty(params.getCommonParams().getLanguageMark())) {
            return Result.Error(ErrorCode.LOGIN_LANGUAGE_IS_NULL);
        }

        int channelId = params.getChannelId();
        var account = accountService.accountLogin(params);
        var newAccount = false;
        if (account.getLastLoginUserId() == 0) {
            newAccount = true;
        }
        var user = this.queryUser(account, params.getCommonParams().getServerId());
        boolean isNewUser = false;
        if (user == null) {
            int serverId = params.getCommonParams().getServerId();
            if (serverId == 0) {
                serverId = serverListService.generateServerId(params.getCommonParams().getLanguageMark());
                log.info("generate serverId mark:{} serverId:{}", params.getCommonParams().getLanguageMark(), serverId);
            } else {
                serverListService.onUserCreate(serverId);
            }

            user = this.createUser(serverId, params, ip);
            user.setAccountKey(account.getPK());
            isNewUser = true;
            serverListService.checkStatus(user.getServerId());
        }

        // 设备号是否被封
        if (this.isFrozeDeviceId(params.getCommonParams().getDeviceId())) {
            log.error("user_has_been_banned from deviceId userId:{}, deviceId:{}", user.getUserId(), RequestContext.getCommonParams().getDeviceId());
            return Result.Error(ErrorCode.USER_BE_SEALED);
        }

        if (!Objects.equals(user.getUserId(), account.getLastLoginUserId())) {
            account.setLastLoginUserId(user.getUserId());
            account.getServerUserIdMap().put(user.getServerId(), user.getUserId());
        }

        account.setDeviceId(params.getCommonParams().getDeviceId().trim());
        account.setLastLoginTime(DateUtils.getUnixTime());
        accountDao.updateLastLogin(account);

        if(!StringUtils.isEmpty(account.getAccountId())){
            user.setAccountId(account.getAccountId().trim());
            user.setAccountId2(account.getAccountId2());
        }
        user.setLastLoginDeviceId(account.getDeviceId());

        List<TaskProcess> taskProcesses = new ArrayList<>();

        UserLoginResponse.Builder response = UserLoginResponse.newBuilder();
        long userId = user.getUserId();
        RequestContext.setUserId(userId);
        RequestContext.setUser(user);
        UserExtend userExtend = userExtendDao.getByUserId(userId);

        if (!isNewUser) {
            // 非新号
            if (!DateUtils.isSameDay(user.getLoginTimestamp(), DateUtils.getUnixTime())) {
                user.setLoginDays(user.getLoginDays() + 1);
                user.setVitalityBuyCount(0);
                taskProcesses.add(TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TOTAL_LOGIN_DAYS, 1));
            }
        } else {
            ConfigEntity configEntity901 = gameConfigManager.getGameConfigConfig().getConfigEntity(901);
            String value = configEntity901.getValue();
            List<List<Integer>> rewardConfig = rewardService.stringToRewardList(value);
            for (List<Integer> reward : rewardConfig) {
                rewardService.checkRewardConfig(reward);
                int type = reward.get(0);
                int itemId = reward.get(1);
                int count = reward.get(2);
                if (type == 0) {
                    if (itemId == RewardResourceType.COINS.getValue()) {
                        user.setCoins((long) count);
                    } else if (itemId == RewardResourceType.DIAMONDS.getValue()) {
                        user.setDiamonds((long) count);
                    }
                }
                else if (type == 3) {
                    Item item = itemService.createItem(userId, itemId, count);
                    response.addItems(CommonHelper.buildItemDto(item));
                }

            }

            userExtendDao.update(userExtend);

            // 任务
            taskProcesses.add(TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TOTAL_LOGIN_DAYS, 1));
            taskProcesses.add(TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_COUNT_DIFF_HERO, userExtend.getHeroRecords().size()));
            List<Long> formation = formationCache.getFormation(GameConstant.FORMATION_TYPE_CHAPTER, userExtend);
            int totalLevel = heroService.totalFormationHeroLevel(userId, formation);
            taskProcesses.add(TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TOTAL_FORMATION_HERO_LEVEL, totalLevel));

            // 新手7日活动任务
            SevenDayTaskProcess sevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_COUNT_DIFF_HERO, userExtend.getHeroRecords().size());
            sevenDayTaskService.updateTask(userId, sevenDayTaskProcess);
        }

        RequestContext.setUserId(userId);
        RequestContext.setUser(user);

        // 判断是否被封号
        if (this.isFrozeUser(user)) {
            return Result.Error(ErrorCode.USER_BE_SEALED);
        }
        if(channelId == LoginChannelID.WECHAT_MINI_GAME.getId()){//微信登录
            if (StringUtils.isEmpty(params.getVerification())) {//需要把code2Session获取的verification传回来
                log.error("verification is empty, params:{}", params);
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            String jsonVerification = CryptUtil.decode(params.getVerification());
            JSONObject jsonObject = JSONObject.parseObject(jsonVerification);
            if (!jsonObject.containsKey("sessionKey")) {
                log.error("sessionKey is empty, params:{}", params);
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
            user.setWeChatMiniGameSessionKey(jsonObject.getString("sessionKey"));
        }

        // 判断是否是白名单
//        response.setIsWhite(isWhite(userId));

        // 更新最近登录时间戳
        user.setLoginTimestamp(DateUtils.getUnixTime());
        user.setLastLoginDeviceId(params.getCommonParams().getDeviceId());
        user.setClientNetVersion(params.getCommonParams().getVersion());

        int abTest = commonService.getGameConfigIntValue(10001);
        if(abTest == 0) {
            if(user.getAbTest() == null || user.getAbTest() == 0) {
                String country = user.getCountry();
                if(country == null || country.isEmpty() || country.isBlank()) {
                    country = "default";
                }
                String key = RedisKeys.AB + country;
                long count = redisService.incrBy(key);
                if ((count & 1) == 0) {
                    user.setAbTest(GameConstant.AB_A);
                } else {
                    user.setAbTest(GameConstant.AB_B);
                }
            }
        } else if(abTest == GameConstant.AB_A) {
            user.setAbTest(GameConstant.AB_A);
        } else if(abTest == GameConstant.AB_B) {
            user.setAbTest(GameConstant.AB_B);
        }

        List<Equip> equips = equipService.getAllEquip(userId);
        List<Hero> heros = heroService.getAllHeros(userId);

        userInfoRedisDao.updateInfo(user, userExtend.getChapterId());
        resetBattleData(userId, heros, userExtend);
        user.setOs(RequestContext.getOs());
        // 检查体力信息
        vitalitySupport.checkVitality(userId);

        userDao.updateUserDataWhenLogin(user);

        String accessToken = createAccessToken(user);
        if (accessToken.isEmpty()) {
            DynamoDBTransactionAspectSupport.setRollBack();
            log.error("createAccessToken failed, userId:{}", userId);
            return Result.Error(ErrorCode.SERVER_SYSTEM_ERROR);
        }

        // 任务
        taskProcesses.add(TaskProcess.valueOf(TaskType.DAILY, TaskType.DAILY_LOGIN, 1));
        taskService.updateTask(userId, taskProcesses);

        // 活动任务
        activityService.updateTask(userId, EventTaskProcess.valueOf(TaskType.EVENT_PROGRESS_LOGIN, 1));

        response.setUserId(userId);
        response.setUserInfoDto(CommonHelper.buildUserInfoDto(user));
        response.setUserCurrency(CommonHelper.buildUserCurrencyMsg(user));
        response.setAccessToken(accessToken);
        response.setTimestamp(DateUtils.getUnixTime());
        response.setSystemMask(user.getSystemMask());
        response.setUserLevel(CommonHelper.buildUserLevelMsg(user));
        response.setGuideMask(user.getGuideMask());
        response.setRegisterTimestamp(user.getCreateTimestamp());
        response.addAllItems(itemService.getAllItems(userId));
        response.setTransId(this.getTransId(userId));
        response.setUserVipLevel(CommonHelper.buildUserVipLevelMsg(user));
        response.setTimeZone(DateUtils.strZone);
        response.addAllAdInfo(adSupport.getAdInfoDtos(userId));

        Integer serverId = user.getServerId();
        response.setServerId(serverId);
        this.setIMGroupId(user, response);
        response.addAllOpenModelIdList(userExtend.getOpenModelId());

        if (user.getOpenServerTime() != null) {
            response.setOpenServerTime(getOpenServerTime(user));
            response.setOpenServerResetTime(getSystemOpenServerTime(user));
        }

        return Result.Success(response.build());
    }

    private void setIMGroupId(User user, UserLoginResponse.Builder response) {
        int serverId = user.getServerId();
        long userId = user.getUserId();
        String serverIMGroupId = imService.getUserServerGroupId(serverId);
        String guildIMGroupId = imService.getUserGuildGroupId(userId);
        String crossServerIMGroupId = imService.getUserCrossServerGroupId(serverId);
        String globalIMGroupId = imService.getUserGlobalGroupId(serverId);

        response.setServerIMGroupId(serverIMGroupId);
        response.setGuildServerIMGroupId(guildIMGroupId);
        response.setCrossServerIMGroupId(crossServerIMGroupId);
        response.setGlobalIMGroupId(globalIMGroupId);
    }



    @Override
    public long getOpenServerTime(User user) {
        return user.getOpenServerTime();
    }

    @Override
    public long getSystemOpenServerTime(User user) {
        return DateUtils.getSystemResetTimeByTime(user.getOpenServerTime()
                - DateUtils.SECONDS_PRE_DAY);
    }

    @Override
    public long getWarOpenServerTime(User user) {
        return serverListService.getServerWarZoneOpenTime(WarType.DEFAULT_WAR_TYPE, user.getServerId());
    }


    @Override
    public long getWarSystemOpenServerTime(User user) {
        return DateUtils.getSystemResetTimeByTime(getWarOpenServerTime(user) - DateUtils.SECONDS_PRE_DAY);
    }

    @Override
    public long getPower(long userId) {
        long power = userInfoRedisDao.getPower(userId);
        if (power == 0) {
            // 请求战斗服计算power
            int clientVersion = RequestContext.getCommonParams().getVersion();
            power = powerSupport.getPower(userId);
            userInfoRedisDao.updatePower(userId, power);
        }
        return power;
    }

    @Override
    public Map<Long, Long> getPower(List<Long> userIds) {
        int clientVersion = RequestContext.getCommonParams().getVersion();
        Map<Long, Long> powerMap = userInfoRedisDao.getPower(userIds);
        for (Long userId : userIds) {
            long power = powerMap.get(userId);
            if (power == 0) {
                power = powerSupport.getPower(userId);
                userInfoRedisDao.updatePower(userId, power);
            }
            powerMap.put(userId, power);
        }
        return powerMap;
    }


    @DynamoDBTransactional
    @Override
    public Result<UserGetInfoResponse> getInfoAction(UserGetInfoRequest params) {
        UserGetInfoResponse.Builder response = UserGetInfoResponse.newBuilder();
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);
        if (user == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        response.setUserCurrency(CommonHelper.buildUserCurrencyMsg(user));
        response.setUserLevel(CommonHelper.buildUserLevelMsg(user));
        response.setTransId(this.getTransId(user.getUserId()));
        response.addAllEquipments(equipService.getAllEquips(userId));
        response.addAllItems(itemService.getAllItems(userId));
        return Result.Success(response.build());
    }


    @DynamoDBTransactional(DynamoDBWriteType.TRANSACTION)
    @Override
    public Result<UserHeartbeatResponse> heartbeatAction(UserHeartbeatRequest params) {
        UserHeartbeatResponse.Builder response = UserHeartbeatResponse.newBuilder();
        userInfoRedisDao.updateActiveTM(RequestContext.getUserId());

        long time = this.getWarOpenServerTime(this.getUser(RequestContext.getUserId()));
        System.out.println(time);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserUpdateSystemMaskResponse> updateSystemMaskAction(UserUpdateSystemMaskRequest params) {
        int position = params.getPosition();
        int value = params.getValue();
        if (position <= 1) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if (value < 0 || value > 1) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        UserUpdateSystemMaskResponse.Builder response = UserUpdateSystemMaskResponse.newBuilder();
        BigInteger mask = new BigInteger(user.getSystemMask());

        if (value == 1 && !MaskUtil.isTrue(mask, position)) {
            mask = MaskUtil.setMask(mask, position);
        } else if (value == 0 && MaskUtil.isTrue(mask, position)) {
            mask = MaskUtil.resetMask(mask, position);
        }
        user.setSystemMask(mask.toString());
        userDao.updateSystemMask(user);

        response.setSystemMask(mask.toString());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserUpdateGuideMaskResponse> updateGudeMaskAction(UserUpdateGuideMaskRequest params) {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        user.setGuideMask(params.getGuideMask());
        userDao.updateGuideMask(user);
        UserUpdateGuideMaskResponse.Builder response = UserUpdateGuideMaskResponse.newBuilder();
        response.setGuideMask(user.getGuideMask());
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserProto.UserCancelAccountResponse> cancelAccountAction(UserProto.UserCancelAccountRequest params) {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        if (!StringUtils.isEmpty(user.getAccountId())) {
            user.setAccountId(user.getAccountId() + "_" + DateUtils.getUnixTime());
        }
        if (!StringUtils.isEmpty(user.getDeviceId())) {
            user.setDeviceId(user.getDeviceId() + "_" + DateUtils.getUnixTime());
        }

        userDao.updateAccount(user);

        UserProto.UserCancelAccountResponse.Builder response = UserProto.UserCancelAccountResponse.newBuilder();
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<UserUpdateInfoResponse> updateInfoAction(UserUpdateInfoRequest params) {
        long userId = RequestContext.getUserId();
        User user = this.getUser(userId);

        String nameId = null;
        try {
            String nickName = params.getNickName().trim();
            int avatar = params.getAvatar();
            int avatarFrame = params.getAvatarFrame();
            boolean isUpdate = false;
            RewardResultSet costResultSet = null;
            if (!StringUtils.isEmpty(nickName) && (user.getNickName() == null || !user.getNickName().equals(nickName))) {
                // 更新昵称
                int costDiamonds = 0;
                int nameLength = nickName.getBytes(StandardCharsets.UTF_8).length;
                int minLen = 3;
                int maxLen = 18;
                if (nameLength < minLen || nameLength > maxLen) {
                    return Result.Error(ErrorCode.USER_NICKNAME_LEN_ERROR);
                }

                nameId = CryptUtil.md5(nickName).toLowerCase();
                // 避免昵称重复
                if (!redisLock.lockWithOutRetry(nameId, String.valueOf(userId), 30000)) {
                    return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
                }
                //TODO 动态传入
                User searchUser = userDao.getByNickName(nickName, 1);
                if (searchUser != null) {
                    return Result.Error(ErrorCode.USER_NICKNAME_REPEATED);
                }

                boolean isLegal = sensitiveWordsService.checkIsLegal(nickName);
                if (!isLegal) {
                    return Result.Error(ErrorCode.USER_NICKNAME_NOT_LEGAL);
                }

                if (costDiamonds > 0) {
                    ResourceReward cost = ResourceReward.valueOf(RewardResourceType.DIAMONDS, -costDiamonds);
                    costResultSet = rewardService.executeReward(userId, cost);
                    if (costResultSet.isFailed()) {
                        return Result.Error(costResultSet.getResultCode());
                    }
                }

                user.setNickName(nickName);
                isUpdate = true;
                userInfoRedisDao.updateNickName(userId, nickName);
            }

            // 更新头像
            if (avatar > 0 && (user.getAvatar() == null || user.getAvatar() != avatar)) {
                // 判断是否拥有头像
//				AvatarEntity avatarEntity = gameConfigManager.getItemConfig().getAvatarEntity(avatar);
//				if (avatarEntity == null || avatarEntity.getType() != 1) {
//					return Result.Error(ErrorCode.PARAMS_ERROR);
//				}
//
//				int defaultAvatarId = gameConfigManager.getConstantConfig().getConstantEntity("avatar_default").getTypeInt();
//				if (avatar != defaultAvatarId) {
//					// 是否拥有头像
//					Item item = itemDao.getByItemId(userId, avatar);
//					if (item == null) {
//						return Result.Error(ErrorCode.PARAMS_ERROR);
//					}
//				}
                user.setAvatar(avatar);
                userInfoRedisDao.updateAvatar(userId, avatar);
                isUpdate = true;
            }

            if (avatarFrame > 0 && (user.getAvatarFrame() == null || user.getAvatarFrame() != avatarFrame)) {
//				AvatarEntity avatarEntity = gameConfigManager.getItemConfig().getAvatarEntity(avatarFrame);
//				if (avatarEntity == null || avatarEntity.getType() != 2) {
//					return Result.Error(ErrorCode.PARAMS_ERROR);
//				}
//				int defaultFrameId = gameConfigManager.getConstantConfig().getConstantEntity("avatarframe_default").getTypeInt();
//				if (avatarFrame != defaultFrameId) {
//					// 是否拥有头像框
//					Item item = itemDao.getByItemId(userId, avatarFrame);
//					if (item == null) {
//						return Result.Error(ErrorCode.PARAMS_ERROR);
//					}
//				}
                user.setAvatarFrame(avatarFrame);
                userInfoRedisDao.updateAvatarFrame(userId, avatarFrame);
                isUpdate = true;
            }

            if (isUpdate) {
                userDao.updateUserInfo(user);
            }

            UserProto.UserUpdateInfoResponse.Builder response = UserProto.UserUpdateInfoResponse.newBuilder();
            response.setCommonData(CommonHelper.buildCommonData(costResultSet));
            response.setUserInfoDto(CommonHelper.buildUserInfoDto(user));
            return Result.Success(response.build());
        } finally {
            if (!StringUtils.isEmpty(nameId)) {
                redisLock.unlock(nameId, String.valueOf(userId));
            }
        }
    }

    @DynamoDBTransactional
    @Override
    public Result<UserGetOtherPlayerInfoResponse> getOtherInfo(UserGetOtherPlayerInfoRequest params) {
        List<Long> userIds = params.getOtherUserIdsList();

        List<PlayerInfoDto> playerInfoDtoList = this.getPlayerInfos(userIds);
        if (playerInfoDtoList.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        UserGetOtherPlayerInfoResponse.Builder response = UserGetOtherPlayerInfoResponse.newBuilder();
        response.addAllPlayerInfos(playerInfoDtoList);
        return Result.Success(response.build());
    }

    private void resetBattleData(long userId, List<Hero> heroes, UserExtend userExtend) {
        formationCache.creatCache(userId, heroes, userExtend);
    }

    private User queryUser(Account account, int serverId) {
        long userId = account.getLastLoginUserId() == null ? 0 : account.getLastLoginUserId();
        if (serverId != 0) {
            userId = account.getServerUserIdMap().getOrDefault(serverId, 0L);
        }
        if (userId > 0) {
            return userDao.getByUserId(userId);
        }
        return null;
    }

    @Override
    public User getUser(long userId) {
        if (userId <= 0) {
            return userDao.getByUserId(userId);
        }
        if (RequestContext.getUser() != null && RequestContext.getUser().getUserId().equals(userId)) {
            return RequestContext.getUser();
        }

        User user = userDao.getByUserId(userId);
        if (user == null) {
            log.error("user data is null, userId = {}", userId);
            return null;
        }

        if (RequestContext.getUserId() != null && RequestContext.getUserId() == userId) {
            RequestContext.setUser(user);
        }
        return user;
    }


    @Override
    public long getTransId(long userId) {
        var userContext = RequestContext.getUserContext(userId);
        if (userContext.getTransId() == 0) {
            userContext.setTransId(this.getNextTransId(userId));
        }
        return userContext.getTransId();
    }

    @Override
    public long getMaxTransId(long userId) {
        User user = userDao.getByUserId(userId);
        long maxTransId = RequestIdUtil.getMaxTransId(userId);
        if (user.getMaxTransId() != null && user.getMaxTransId() > maxTransId) {
            maxTransId = user.getMaxTransId() + 100;
            RequestIdUtil.updateTransId(userId, maxTransId);
        }
        return maxTransId;
    }

    private long getNextTransId(long userId) {
        var transId = RequestIdUtil.increaseTransId(userId, 1);
        if (transId < RequestIdUtil.DEFAULT_TRANS_ID) {
            //缓存没拿到,直接+100,因为每100次存一次数据库
            User user = userDao.getByUserId(userId);
            if (user.getMaxTransId() == null) {
                user.setMaxTransId(RequestIdUtil.DEFAULT_TRANS_ID);
            } else {
                user.setMaxTransId(user.getMaxTransId() + 100);
            }
            transId = RequestIdUtil.increaseTransId(userId, (int) (user.getMaxTransId() - transId));
            user.setMaxTransId(transId);
            userDao.updateMaxTransId(user);
        } else {
            User user = userDao.getByUserId(userId);
            if (transId - user.getMaxTransId() >= 100) {
                user.setMaxTransId(transId);
                userDao.updateMaxTransId(user);
            }
        }
        return transId;
    }

    @Override
    public String createAccessToken(User user) {
        UserToken userToken = new UserToken();
        userToken.setServerId(user.getServerId());
        userToken.setUserId(user.getUserId());
        userToken.setLoginTM(user.getLoginTimestamp());
        userToken.setLoginDeviceId(user.getLastLoginDeviceId());
        userToken.setAccountId(user.getAccountId());
        userToken.setAccountKey(user.getAccountKey());
//        userToken.setServerIMGroupId(IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.SERVER, user.getServerId()));
        userToken.setRegisterTime(user.getCreateTimestamp());
        userToken.setChannelId(user.getChannelId());
        userToken.setPackageId("");
        userToken.setServerOpenTime(user.getOpenServerTime());
        return CryptUtil.encode(JSONObject.toJSONString(userToken));
    }

    @Override
    public int parseAccessToken(CommonParams commonParams) {
        try {
            String accessToken = commonParams.getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                log.error("accessToken is empty, commonParams:{}", commonParams);
                return ErrorCode.USER_ACCESS_TOKEN_ERROR;
            }
            String decodeStr = CryptUtil.decode(accessToken);
            if (decodeStr.isEmpty()) {
                log.error("decode accessToken is empty, commonParams:{}", commonParams);
                return ErrorCode.USER_ACCESS_TOKEN_ERROR;
            }

            UserToken userToken = JSONObject.parseObject(decodeStr, UserToken.class);
            RequestContext.setUserId(userToken.getUserId());
            RequestContext.setDeviceId(userToken.getLoginDeviceId());
            RequestContext.setAccountId(userToken.getAccountId());
            userInfoRedisDao.updateActiveTM(RequestContext.getUserId());
            return ErrorCode.SUCCESS;
        } catch (Exception e) {
            log.error("parseAccessToken, e:",e);
            log.error("accessToken decode error, commonParams:{}, errMsg:{}", commonParams, e.getMessage());
            return ErrorCode.USER_ACCESS_TOKEN_ERROR;
        }
    }

    @Override
    public int checkLoginState(boolean checkDoubleClient) {
        if (RequestContext.getUserId() == null) {
            return ErrorCode.USER_ACCESS_TOKEN_ERROR;
        }
        User user = this.getUser(RequestContext.getUserId());
        if (user == null) {
            log.error("getUser is null, userId:{}", RequestContext.getUserId());
            return ErrorCode.USER_LOGIN_FAILED;
        }

        // 是否被封号
        if (this.isFrozeUser(user)) {
            return ErrorCode.USER_BE_SEALED;
        }

        // 设备是否被封
        if (this.isFrozeDeviceId(RequestContext.getCommonParams().getDeviceId())) {
            log.error("user_has_been_banned from deviceId userId:{}, deviceId:{}", user.getUserId(), RequestContext.getCommonParams().getDeviceId());
            return ErrorCode.USER_BE_SEALED;
        }

        // 重复登录
        if (checkDoubleClient) {
            if (!StringUtils.isEmpty(RequestContext.getDeviceId()) && !user.getLastLoginDeviceId().equals(RequestContext.getDeviceId())) {
                log.error("login repeated, userId:{}, lastLoginDeviceId:{}, deviceId:{}", RequestContext.getUserId(), user.getLastLoginDeviceId(), RequestContext.getDeviceId());
                return ErrorCode.USER_LONGIN_REPEAT;
            }
        }

        return ErrorCode.SUCCESS;
    }


    @Override
    public int getChapterId(long userId) {
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        return userExtend.getChapterId();
    }

    private String randomName() {
        List<PlayerNameEntity> entities = new ArrayList<>(gameConfigManager.getPlayerAvatarConfig().getPlayerName().values());
        int randomIndex = RandomUtil.nextInt(entities.size() - 1);

        return entities.get(randomIndex).getName();
    }

    private int randomAvatar() {
        List<AvatarEntity> as = gameConfigManager.getAvatarConfig().getAvatar().values().stream().filter(v -> v.getType() == 1).collect(Collectors.toList());
        List<AvatarEntity> entities = new ArrayList<>(as);
        int randomIndex = RandomUtil.nextInt(entities.size() - 1);

        return entities.get(randomIndex).getId();
    }


    @Override
    public User DevCreateRobot(int serverId) {
        String deviceId = "ROBOT_" + UUID.randomUUID();

        UserProto.UserLoginRequest.Builder p = UserProto.UserLoginRequest.newBuilder();
        p.setChannelId(0);
        p.setAccountId2(deviceId);

        CommonParams.Builder b = CommonParams.newBuilder();
        b.setDeviceId(deviceId);
        b.setServerId(serverId);
        b.setLanguageMark("EDITOR");
        b.setAccountId(deviceId);

        p.setCommonParams(b);

        Account account = new Account();
        account.setPK(deviceId);
        account.setDeviceId(deviceId);
        account.setAccountId(deviceId);
        account.setAccountId2(deviceId);
        account.setCreateTime(DateUtils.getUnixTime());
        account.setLastLoginUserId(0L);


        if (serverId == 0) {
            serverId = serverListService.generateServerId(b.getLanguageMark());
        }

        User user = this.createUser(serverId, p.build(), "0.0.0.0");
        user.setAccountKey(account.getPK());


        powerSupport.syncPower(user.getUserId(), "Login");

        UserExtend userExtend = userExtendDao.getByUserId(user.getUserId());
        userExtend.setChapterId(19);
        userExtendDao.update(userExtend);

        account.setServerUserIdMap(Maps.newHashMap());
        account.getServerUserIdMap().put(user.getServerId(), user.getUserId());

        accountDao.insert(account);
        return user;
    }


    /**
     * 创建新用户
     *
     * @param params
     * @param ip
     * @return
     */
    public User createUser(int serverId, UserLoginRequest params, String ip) {
        long userId = commonService.generateUserId();
        // 初始金币
        long initCoins = 100;
        // 初始钻石
        long initDiamonds = 100;
        // 初始等级
        int initLevel = 1;

        User user = new User();
        if (!StringUtils.isEmpty(params.getCommonParams().getDeviceId())) {
            user.setDeviceId(params.getCommonParams().getDeviceId());
        }
        if (!StringUtils.isEmpty(params.getCommonParams().getAccountId())) {
            user.setAccountId(params.getCommonParams().getAccountId());
        }
        if (!StringUtils.isEmpty(params.getCommonParams().getLanguageMark())) {
            user.setLanguageMark(params.getCommonParams().getLanguageMark());
        }

        user.setAccountId2(params.getAccountId2());
        user.setChannelId((short) params.getChannelId());
        user.setUserId(userId);
        user.setCoins(initCoins);
        user.setDiamonds(initDiamonds);
        user.setLevel((short) initLevel);
        user.setExp(0);
        user.setAccountStatus((short) 0);
        user.setLoginTimestamp(DateUtils.getUnixTime());
        user.setUserIp(ip);
        user.setCreateTimestamp(DateUtils.getUnixTime());
        user.setLastLoginDeviceId(user.getDeviceId());
        user.setCountry(RequestContext.getCountryCode());
        user.setLoginDays(1);

        user.setMaxTransId(RequestIdUtil.DEFAULT_TRANS_ID);
        user.setSystemMask("0");
        user.setGuideMask(0L);

        user.setVipExp(0);
        user.setVipLevel((short) 0);

        user.setServerId(serverId);
        user.setOpenServerTime(serverListService.getOpenServerTime(user.getServerId()));
        user.setOs(RequestContext.getOs());

        // 体力
        int initVitality = Integer.parseInt(gameConfigManager.getGameConfigConfig().getConfigEntity(2006).getValue());
        user.setVitality(initVitality);
        user.setVitalityTimeStamp(DateUtils.getUnixTime());
        user.setVitalityBuyCount(0);

        userDao.insert(user);

        // 初始化userExtend表
        UserExtend userExtend = UserExtend.init(userId, 1);
        this.initNewUser(userExtend);
        userExtendDao.insert(userExtend);

        // 初始化商店数据
        Shop shop = Shop.init(userId);
        shopService.initWealCard(shop);
        shopDao.insert(shop);


        return user;
    }


    private boolean isWhite(long userId) {
        DataEntity data = gameConfigManager.getCloudConstsConfig().getDataEntity(CloudConstant.WHITE);
        if(data == null) {
            return false;
        }

        return data.getVaule().contains(String.valueOf(userId));
    }


    /**
     * 是否被封号
     *
     * @param user
     * @return
     */
    private boolean isFrozeUser(User user) {
        // 判断是否被封号
        if (user.getAccountStatus() == 1) {
            if (user.getFrozeTime() == 0 || user.getFrozeTime() > DateUtils.getUnixTime()) {
                ResponseContext.setFrozeTime(user.getFrozeTime());
                return true;
            } else {
                // 自动解封
                user.setAccountStatus((short) 0);
                user.setFrozeTime(0L);
            }
        }
        return false;
    }

    @Override
    public List<PlayerInfoDto> getPlayerInfos(List<Long> userIds) {
        Map<Long, User> users = userDao.getByUserIds(userIds);
        Map<Long, UserExtend> userExtendMap = userExtendDao.getByUserIds(userIds);

        List<PlayerInfoDto> playerInfoDtos = new ArrayList<>();
        for (Long userId : userIds) {
            User user = users.get(userId);
            if (user == null) {
                continue;
            }

            UserExtend userExtend = userExtendMap.get(user.getUserId());

            PlayerInfoDto.Builder builder = PlayerInfoDto.newBuilder();

            builder.setNickName(user.getNickName() == null ? "" : user.getNickName());
            builder.setUserId(user.getUserId());
            builder.setAvatar(user.getAvatar() == null ? 0 : user.getAvatar());
            builder.setAvatarFrame(user.getAvatarFrame() == null ? 0 : user.getAvatarFrame());
            builder.setLastLoginTimestamp(user.getLoginTimestamp());

            builder.setChapterId(userExtend.getChapterId());

//            builder.setHero(CommonHelper.buildHeroDto(heroService.getHero(userId, userExtend.getActorModel().getRowId())));

//            List<CommonProto.HeroDto> heroDtos = new ArrayList<>();
//            List<Long> formation = formationCache.getFormation(GameConstant.FORMATION_TYPE_CHAPTER, userExtend);
//            List<Long> heroRowIds = formation.stream().filter(Objects::nonNull).filter(v -> v != 0L).collect(Collectors.toList());
//            Map<Long, Hero> heroMap = heroService.getAllHeros(userId, heroRowIds).stream().collect(Collectors.toMap(Hero::getRowId, hero -> hero));
//            formation.forEach(v -> {
//                if (v != null && v != 0L) {
//                    heroDtos.add(CommonHelper.buildHeroDto(heroMap.get(v)));
//                }
//            });
//            builder.addAllFormationHero(heroDtos);
//            builder.addAllFormationRowIds(formation);

//            List<Long> equipDress = new ArrayList<>();
//            heroMap.forEach((k,v) ->{
//                if (v.getEquips() != null){
//                    for (Equip equip : v.getEquips().values()){
//                        equipDress.add(equip.getRowId());
//                    }
//                }
//            });
//
//            if (!equipDress.isEmpty()) {
//                equipDao.getListByRowIds(userId, equipDress).forEach(v -> {
//                    builder.addEquipments(CommonHelper.buildEquipmentDto(v));
//                });
//            }
//
//            builder.setPower(user.getPower().intValue());
            GuildUser guildUser = guildUserDao.getByUserId(userId);

            if (guildUser != null) {
                builder.setGuildId(String.valueOf(guildUser.getGuildId()));
                Guild guild = guildDao.getByGuildId(guildUser.getGuildId());
                if (guild != null) {
                    builder.setGuildName(guild.getGuildName() == null ? "" : guild.getGuildName());
                    builder.setGuildIcon(guild.getGuildIcon() == null ? 0 : guild.getGuildIcon());
                    builder.setGuildIconBg(guild.getGuildIconBg() == null ? 0 : guild.getGuildIconBg());
                }
            }

            playerInfoDtos.add(builder.build());
        }
        return playerInfoDtos;
    }

    @Override
    public int getPrivateChatChapterLimit(Integer value) {
        if (value == null || value == 0) {
            var data = gameConfigManager.getFunctionConfig().getFunctionEntity(FunctionTypeEnum.FUNCTION_CHAT.getFunctionKey()).getUnlockArgs();
            return Integer.parseInt(data);
        }
        return value;
    }

    @DynamoDBTransactional
    @Override
    public Result<UserGetBattleReportResponse> getBattleReport(UserGetBattleReportRequest params) {
        long reportId = params.getReportId();

        Report report = reportDao.getByRowId(reportId);
        if (report == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        UserGetBattleReportResponse.Builder response = UserGetBattleReportResponse.newBuilder();
        response.setRecord(pvpHelper.buildPVPRecord(report));

        return Result.Success(response.build());
    }

    @Override
    @DynamoDBTransactional
    public Result<UserOpenModelResponse> userOpenModel(UserOpenModelRequest params) {
        Long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        List<Integer> modelId = params.getModelIdsList();
        List<Integer> openModelId = userExtend.getOpenModelId();

        for (Integer v : modelId) {
            if (!openModelId.contains(v)) {
                if (!functionHelp.openFunction(userId, v, userExtend)){
                    return Result.Error(ErrorCode.PARAMS_ERROR);
                }else{
                    openModelId.add(v);
                }
            }
        }

        userExtendDao.update(userExtend);
        UserOpenModelResponse.Builder builder = UserOpenModelResponse.newBuilder();
        return Result.Success(builder.build());
    }

    @Override
    @DynamoDBTransactional
    public Result<UserSetFormationByTypeResponse> userSetFormationByType(UserSetFormationByTypeRequest params) {
        long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        int formationType = params.getFormationType();
        List<Long> formation = params.getFormation().getLongArrayList();

        if(formation.isEmpty()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if(formation.size() != 5) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        if(formation.stream().allMatch(v -> v == 0)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        List<Long> ids = heroDao.getAllByUserId(userId).stream().map(Hero::getRowId).collect(Collectors.toList());
        for(Long id : formation) {
            if(id != 0 && !ids.contains(id)) {
                return Result.Error(ErrorCode.PARAMS_ERROR);
            }
        }

        userExtend.getFormations().put(formationType, formation);
        userExtendDao.updateNow(userExtend);

//        battleRedisDao.updateFormationHero(userId, pvpHelper.getFormationMap(userExtend.getFormations(), heroService.getHeroByFormations(userId, userExtend.getFormations())));
        battleRedisDao.removeData(userId);


        if (formationType == GameConstant.FORMATION_TYPE_CHAPTER) {
            powerSupport.syncPower(RequestContext.getUserId(),"SetFormation");

            // 任务
            int totalLevel = heroService.totalFormationHeroLevel(userId, formation);
            TaskProcess taskProcess = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TOTAL_FORMATION_HERO_LEVEL, totalLevel);
            taskService.updateTask(userId, taskProcess);

            // 新手7日活动任务
            SevenDayTaskProcess sevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_TOTAL_FORMATION_HERO_LEVEL, totalLevel);
            sevenDayTaskService.updateTask(userId, sevenDayTaskProcess);
        }

        UserSetFormationByTypeResponse.Builder builder = UserSetFormationByTypeResponse.newBuilder();
        builder.setCommonData(CommonHelper.buildCommonData());
        return Result.Success(builder.build());
    }

    @Override
    @DynamoDBTransactional
    public Result<UserGetFormationByTypeResponse> userGetFormationByType(UserGetFormationByTypeRequest params) {
        Long userId = RequestContext.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        int formationType = params.getFormationType();
        Map<Integer, List<Long>> formations = userExtend.getFormations();
        List<Long> formation = new ArrayList<>();
        if (formations.containsKey(formationType)) {
            formation = formations.get(formationType);
        }
        CommonProto.LongArray.Builder longBuilder = CommonProto.LongArray.newBuilder();
        longBuilder.addAllLongArray(formation);
        UserGetFormationByTypeResponse.Builder builder = UserGetFormationByTypeResponse.newBuilder();
        builder.setFormationType(formationType);
        builder.setFormation(longBuilder.build());
        return Result.Success(builder.build());
    }

    @Override
    public double vipPermissionValueCheck(long userId, VipPermission vipPermission) {
        return Double.parseDouble(gteVipPermissionValue(userId, vipPermission));
    }

    public int vipPermissionValueIntCheck(long userId, VipPermission vipPermission) {
        return Integer.parseInt(gteVipPermissionValue(userId, vipPermission));
    }

    public String gteVipPermissionValue(long userId, VipPermission vipPermission) {
        User user = this.getUser(userId);
        int vip = user.getVipLevel();

        if (vip > 0) {
            VipEntity vipEntity = gameConfigManager.getVipConfig().getVipEntity(vip);
            if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_3) {
                return vipEntity.getPower3().isEmpty() ? "0" : vipEntity.getPower3();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_8) {
                return vipEntity.getPower8().isEmpty() ? "0" : vipEntity.getPower8();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_10) {
                return vipEntity.getPower10().isEmpty() ? "0" : vipEntity.getPower10();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_11) {
                return vipEntity.getPower11().isEmpty() ? "0" : vipEntity.getPower11();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_12) {
                return vipEntity.getPower12().isEmpty() ? "0" : vipEntity.getPower12();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_13) {
                return vipEntity.getPower13().isEmpty() ? "0" : vipEntity.getPower13();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_14) {
                return vipEntity.getPower14().isEmpty() ? "0" : vipEntity.getPower14();
            }  else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_15) {
                return vipEntity.getPower15().isEmpty() ? "0" : vipEntity.getPower15();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_16) {
                return vipEntity.getPower16().isEmpty() ? "0" : vipEntity.getPower16();
            } else if (Objects.requireNonNull(vipPermission) == VipPermission.PERMISSION_18) {
                return vipEntity.getPower18().isEmpty() ? "0" : vipEntity.getPower18();
            }
        }
        return "0";
    }

    @Override
    public Double cardPermissionValueCheck(long userId, CardPermission permission) {
        long now = DateUtils.getUnixTime();

        Shop shop = shopDao.getByUserId(userId);
        if(shop == null) {
            return null;
        }
        Shop.IAPModel iapModel = shop.getIap();
        if(iapModel == null) {
            return null;
        }

        if(permission == CardPermission.PERMISSION_1) {
            double d = 0;

            for(MonthCardEntity entity : gameConfigManager.getIAPConfig().getMonthCard().values()) {
                Shop.IAPMonthCardModel model = iapModel.getMonthCardMap().get(entity.getId());
                if(model == null || now > model.getExpireTime()) {
                    continue;
                }

                d = d + Double.parseDouble(entity.getPower1().isEmpty() ? "0" : entity.getPower1());
            }

            return d;
        }

        if(permission == CardPermission.PERMISSION_2) {
            double d = 0;

            for(MonthCardEntity entity : gameConfigManager.getIAPConfig().getMonthCard().values()) {
                Shop.IAPMonthCardModel model = iapModel.getMonthCardMap().get(entity.getId());
                if(model == null || now > model.getExpireTime()) {
                    continue;
                }

                d = d + Double.parseDouble(entity.getPower2().isEmpty() ? "0" : entity.getPower2());
            }

            return d;
        }

        return null;
    }


    @Override
    public void vipPermissionTrigger(long userId) {
        User user = this.getUser(userId);
        int vip = user.getVipLevel();
        if (vip > 0) {
            UserExtend userExtend = userExtendDao.getByUserId(userId);
            VipEntity vipEntity = gameConfigManager.getVipConfig().getVipEntity(vip);

            int needSave = 0;

            // 黑市刷新次数
            int power15 = Integer.parseInt(vipEntity.getPower15().isEmpty() ? "0" : vipEntity.getPower15());
            if (power15 > 0) {
                Shop shop = shopDao.getByUserId(userId);
                Shop.IntegralShopModel shopModel = shop.getIntegralShops().get(ShopIdEnum.SHOP_BLACK.getId());
                List<Integer> refreshCost = gameConfigManager.getIntegralShopConfig().getDataEntity(ShopIdEnum.SHOP_BLACK.getId()).getRefreshCost();
                int defaultNum = refreshCost == null ? 0 : refreshCost.size();
                shopModel.setMaxNum(defaultNum + power15);

                needSave++;
            }

            if (needSave > 0) {
                userExtendDao.update(userExtend);
            }
        }
    }

    @Override
    public UserToken parseAccessToken(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            log.error("accessToken is empty");
            return null;
        }

        String decodeStr = CryptUtil.decode(accessToken);
        if (decodeStr.isEmpty()) {
            log.error("accessToken decode error");
            return null;
        }

        UserToken userToken = JSONObject.parseObject(decodeStr, UserToken.class);
        if (userToken == null) {
            log.error("parse accessToken to UserToken error");
            return null;
        }
        return userToken;
    }

    private static Map<String, Object> buildRespData(UserToken userToken) {
        long userId = userToken.getUserId();
        long loginTM = userToken.getLoginTM();
        String loginDeviceId = userToken.getLoginDeviceId();

        Map<String, Object> gameUser = com.google.api.client.util.Maps.newHashMap();
        gameUser.put("serverId", String.valueOf(userToken.getServerId()));
        gameUser.put("serverIMGroupId", String.valueOf(userToken.getServerIMGroupId()));
        gameUser.put("userId", String.valueOf(userId));
        gameUser.put("loginTM", loginTM);
        gameUser.put("accountKey", userToken.getAccountKey());
        gameUser.put("loginDeviceId", loginDeviceId);

        Map<String, Object> data = com.google.api.client.util.Maps.newHashMap();
        data.put("gameUser", gameUser);
        return data;
    }

    private boolean isFrozeDeviceId(String deviceId) {
        if (!StringUtils.isEmpty(deviceId)) {
            FrozeDevice frozeDevice = frozeDeviceDao.getItem(deviceId);
            if (frozeDevice != null && frozeDevice.getTm() != null) {
                if (frozeDevice.getTm() == 0 || frozeDevice.getTm() > DateUtils.getUnixTime()) {
                    ResponseContext.setFrozeTime(frozeDevice.getTm());
                    return true;
                } else {
                    // 自动解封
                    frozeDeviceDao.delete(frozeDevice);
                }
            }
        }
        return false;
    }

    private void initNewUser(UserExtend userExtend) {
        //初始化英雄&主角
        heroService.init(userExtend);
    }
}














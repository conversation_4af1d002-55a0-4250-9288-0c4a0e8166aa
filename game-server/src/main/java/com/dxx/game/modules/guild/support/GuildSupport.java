package com.dxx.game.modules.guild.support;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.Bean.Guild_guildLevel;
import com.dxx.game.config.Bean.Guild_guildStyle;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildDao;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.dynamodb.repository.guild.opensearch.GuildOpenSearchDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dto.GuildProto.GuildDetailInfoDto;
import com.dxx.game.dto.GuildProto.GuildInfoDto;
import com.dxx.game.dto.GuildProto.GuildMemberInfoDto;
import com.dxx.game.modules.common.service.SensitiveWordsService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.guild.consts.GuildPosition;
import com.dxx.game.modules.guild.consts.GuildState;
import com.dxx.game.modules.im.IMGroupIdGenerator;
import com.dxx.game.modules.im.service.IMService;
import com.dxx.game.modules.user.model.UserInfoModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/23
 * @description:
 */
@Slf4j
@Component
public class GuildSupport {

    @Resource
    private RedisService redisService;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private GuildDao guildDao;
    @Resource
    private UserDao userDao;
    @Resource
    private GuildOpenSearchDao guildOpenSearchDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private SensitiveWordsService sensitiveWordsService;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private IMService imService;

    private static final String REDIS_GUILD_GENERATE_KEY = "guild_generate_key";

    @PostConstruct
    private void init() {
        long id = redisService.getLongValue(REDIS_GUILD_GENERATE_KEY);
        if (id == 0) {
            if(gameConfigManager.isTest()) {
                redisService.set(REDIS_GUILD_GENERATE_KEY, 90000L);
            }
            else {
                redisService.set(REDIS_GUILD_GENERATE_KEY, 10000L);
            }
        }
    }

    public long getGenerateId() {
        return redisService.incrBy(REDIS_GUILD_GENERATE_KEY);
    }

    // 公会状态
    public int guildStatus(Guild guild) {
        if (guild == null) {
            return ErrorCode.GUILD_NOT_EXIST;
        } else if (guild.getGuildIsDissolved() == GuildState.DISMISS) {
            return ErrorCode.GUILD_HAS_BEEN_DISSOLVED;
        } else {
            return ErrorCode.SUCCESS;
        }
    }

    // 公会成员状态
    public int guildUserStatus(GuildUser guildUser) {
        if (guildUser == null || guildUser.getGuildId() == 0) {
            return ErrorCode.GUILD_JOIN_FIRST;
        }
        return ErrorCode.SUCCESS;
    }

    /**
     * 推荐给玩家的都是满足 没满员&符合限制条件 作为前提.
     * 优先从活跃公会中随机选出20个人员不满的公会，按照人数多少排列，多的在前
     * 获取推荐列表
     *
     * @return
     */
    public List<Guild> getRecommendList(boolean isOnlyJoinable, int condition, List<Long> excludeGuildIds, int serverId) {
        if (excludeGuildIds == null) {
            excludeGuildIds = new ArrayList<>();
        }

        int limit = gameConfigManager.getTables().getGuild_guildConstModel().get(110).TypeInt;
        List<Guild> result = new ArrayList<>(limit);
        result.addAll(guildOpenSearchDao.getRecommendList(limit, excludeGuildIds, true, isOnlyJoinable, condition, serverId));
        List<Long> guildIds = this.getGuildIds(result);
        excludeGuildIds.addAll(guildIds);

        int lastLimit = limit - result.size();
        if (lastLimit > 0) {
            result.addAll(guildOpenSearchDao.getRecommendList(lastLimit, excludeGuildIds, false, isOnlyJoinable, condition, serverId));
        }

        return result;
    }

    public List<Long> getGuildIds(List<Guild> guilds) {
        List<Long> result = new ArrayList<>(guilds.size());
        for (Guild guild : guilds) {
            result.add(guild.getGuildId());
        }
        return result;
    }

    /**
     * 构建公会基本信息
     *
     * @param guild
     * @return
     */
    public GuildDetailInfoDto buildGuildDetailInfoDto(Guild guild) {
        GuildDetailInfoDto.Builder result = GuildDetailInfoDto.newBuilder();
        result.setGuildInfoDto(this.buildGuildInfoDto(guild));
        result.addAllGuildMemberInfoDtos(this.getGuildMemberInfoDtoList(guild.getGuildId()));
        return result.build();
    }

    public GuildDetailInfoDto buildGuildDetailInfoDto(Guild guild, List<GuildUser> guildUsers) {
        GuildDetailInfoDto.Builder result = GuildDetailInfoDto.newBuilder();
        result.setGuildInfoDto(this.buildGuildInfoDto(guild));
        result.addAllGuildMemberInfoDtos(this.getGuildMemberInfoDtoList(guild.getGuildId()));
        if (guildUsers != null) {
            result.addAllGuildMemberInfoDtos(this.getGuildMemberInfoDtoList(guildUsers));
        }
        return result.build();
    }

    /**
     * 构建公会基本信息
     *
     * @param guilds
     * @return
     */
    public List<GuildInfoDto> buildGuildInfoDtoList(List<Guild> guilds, GuildUser guildUser) {
        List<GuildInfoDto> result = new ArrayList<>(guilds.size());
        List<GuildInfoDto> guildInfoDtos = this.buildGuildInfoDto(guilds);
        for (GuildInfoDto guildInfoDto : guildInfoDtos) {
            if (guildUser != null && guildUser.getApplyGuildIds() != null && guildUser.getApplyGuildIds().contains(guildInfoDto.getGuildId())) {
                guildInfoDto = guildInfoDto.toBuilder().setIsApply(true).build();
            }
            result.add(guildInfoDto);
        }

        return result;
    }

    // 构造公会信息
    public GuildInfoDto buildGuildInfoDto(Guild guild) {
        return this.buildGuildInfoDto(Collections.singletonList(guild)).get(0);
    }

    public List<GuildInfoDto> buildGuildInfoDto(List<Guild> guilds) {
        List<Long> guildPresidentUserIds = new ArrayList<>(guilds.size());
        for (Guild guild : guilds) {
            guildPresidentUserIds.add(guild.getGuildPresidentUserId());
        }

        guildPresidentUserIds = guildPresidentUserIds.stream().distinct().collect(Collectors.toList());

        List<GuildInfoDto> result = new ArrayList<>(guilds.size());
        Map<Long, UserInfoModel> userInfoModelMap = userDao.queryUserInfo(guildPresidentUserIds, false);
        for (Guild guild : guilds) {
            GuildInfoDto.Builder builder = GuildInfoDto.newBuilder();
            builder.setGuildId(guild.getGuildId());
            builder.setGuildName(guild.getGuildName());
            builder.setGuildIcon(guild.getGuildIcon());
            builder.setGuildIntro(guild.getGuildIntro());
            builder.setGuildIconBg(guild.getGuildIconBg());
            builder.setMembers(guild.getGuildMembersCount());
            builder.setMaxMembers(guild.getGuildMaxMembersCount());
            builder.setActive(guild.getGuildActive());
            builder.setLevel(guild.getGuildLevel());
            builder.setApplyType(guild.getGuildApplyType());
            builder.setApplyCondition(guild.getGuildApplyCondition());
            builder.setLanguage(guild.getGuildLanguage());
            builder.setGuildNotice(guild.getGuildNotice());
            builder.setExp(guild.getGuildExp());

            String imGroupId = IMGroupIdGenerator.generateGroupId(IMGroupIdGenerator.GroupType.GUILD, guild.getGuildId());
            builder.setImGroupId(imGroupId);

            UserInfoModel userInfoModel = userInfoModelMap.get(guild.getGuildPresidentUserId());
            if (userInfoModel != null) {
                builder.setGuildPresidentUserId(guild.getGuildPresidentUserId());
                builder.setGuildPresidentNickName(userInfoModel.getNickName());
            }
            builder.setTotalPower(guildDao.getTotalPower(guild.getGuildId()));
            result.add(builder.build());
        }

        return result;
    }

    /**
     * 获取公会成员列表
     *
     * @param guildUsers
     * @return
     */
    public List<GuildMemberInfoDto> getGuildMemberInfoDtoList(List<GuildUser> guildUsers) {
        List<GuildMemberInfoDto> result = new ArrayList<>();
        long nowTime = DateUtils.getUnixTime();
        if (guildUsers != null && !guildUsers.isEmpty()) {
            List<Long> userIds = new ArrayList<>();
            for (GuildUser guildUser : guildUsers) {
                userIds.add(guildUser.getUserId());
            }

            Map<Long, UserInfoModel> userInfoModelMap = userDao.queryUserInfo(userIds, true);
            Map<Long, UserExtend> userExtendMap = userExtendDao.getByUserIds(userIds);
            Map<Long, Boolean> onlineMap = imService.batchGetUserOnline(userIds);
            for (GuildUser guildUser : guildUsers) {
                long uId = guildUser.getUserId();
                GuildMemberInfoDto.Builder builder = GuildMemberInfoDto.newBuilder();

                UserInfoModel userInfoModel = userInfoModelMap.get(uId);
                UserExtend userExtend = userExtendMap.get(uId);

                if (userInfoModel != null) {
                    builder.setUserId(uId);
                    builder.setNickName(userInfoModel.getNickName());
                    builder.setAvatar(userInfoModel.getAvatar());
                    builder.setAvatarFrame(userInfoModel.getAvatarFrame());
                    builder.setLevel(userInfoModel.getLevel());
                    builder.setActiveTime(userInfoModel.getActiveTM());
                    if (guildUser.getPosition() != null) {
                        builder.setPosition(guildUser.getPosition());
                    }

                    builder.setChapterId(userInfoModel.getChapterId());

                    // 加入时间
                    if (guildUser.getJoinTime() != null) {
                        builder.setJoinTime(guildUser.getJoinTime());
                    }

                    // 申请时间
                    if (guildUser.getApplyTime() != null) {
                        builder.setApplyTime(guildUser.getApplyTime());
                    }

                    // 每日活跃度
                    if (guildUser.getDailyActive() != null && guildUser.getDailyTM() != null && guildUser.getDailyTM() > nowTime) {
                        builder.setDailyActive(guildUser.getDailyActive());
                    }

                    // 每周活跃度
                    if (guildUser.getWeeklyActive() != null && guildUser.getWeeklyTM() != null && guildUser.getWeeklyTM() > nowTime) {
                        builder.setWeekActive(guildUser.getWeeklyActive());
                    }

                    builder.setBattlePower(userInfoModel.getPower());

                    Boolean isOnline = onlineMap.get(uId);
                    if (isOnline != null) {
                        builder.setIsOnline(isOnline);
                    }

                }
                result.add(builder.build());
            }
        }
        return result;
    }
    public List<GuildMemberInfoDto> getGuildMemberInfoDtoList(long guildId) {
        List<GuildUser> guildUsers = guildUserDao.getAllByGuildId(guildId);
        return this.getGuildMemberInfoDtoList(guildUsers);
    }

    /**
     * 检查权限
     *
     * @param position
     * @param power
     * @return
     */
    public boolean checkPowerIsAvailable(int position, int power) {
        List<Integer> powerList = gameConfigManager.getTables().getGuild_guildPowerModel().get(position).Power;
        if (powerList.contains(power)) {
            return true;
        }
        return false;
    }

    /**
     * 判断语言id是否合法
     *
     * @param languageId
     * @return
     */
    public boolean checkLanguageConfig(int languageId) {
        if (gameConfigManager.getTables().getGuild_guildLanguageModel().getDataMap().containsKey(languageId)) {
            return true;
        }
        return false;
    }

    /**
     * 检测图标
     *
     * @param iconId
     * @param iconBg
     * @return
     */
    public boolean checkGuildIcon(int iconId, int iconBg) {
        if (iconId > 0) {
            Guild_guildStyle guildStyleEntity = gameConfigManager.getTables().getGuild_guildStyleModel().get(iconId);
            if (guildStyleEntity == null) {
                return false;
            }
        }
        if (iconBg > 0) {
            Guild_guildStyle guildStyleEntity = gameConfigManager.getTables().getGuild_guildStyleModel().get(iconBg);
            if (guildStyleEntity == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 公会昵称是否合法
     *
     * @param guildName
     * @return
     */
    public int checkGuildName(String guildName) {
        int nameLength = guildName.getBytes(StandardCharsets.UTF_8).length;
        int minNameLength = gameConfigManager.getTables().getGuild_guildConstModel().get(105).TypeInt;
        int maxNameLength = gameConfigManager.getTables().getGuild_guildConstModel().get(106).TypeInt;

        if (nameLength < minNameLength || nameLength > maxNameLength) {
            return ErrorCode.GUILD_NAME_LENGTH_ERROR;
        }

        // 判断昵称是否合法
        boolean isLegal = sensitiveWordsService.checkIsLegal(guildName);
        if (!isLegal) {
            return ErrorCode.GUILD_NAME_ILLEGAL;
        }
        return ErrorCode.SUCCESS;
    }

    /**
     * 检测公会宣言
     *
     * @param guildIntro
     * @return
     */
    public int checkGuildIntro(String guildIntro) {
        int introLength = guildIntro.getBytes(StandardCharsets.UTF_8).length;
        int maxIntroLength = gameConfigManager.getTables().getGuild_guildConstModel().get(107).TypeInt;
        if (introLength > maxIntroLength) {
            return ErrorCode.GUILD_INFO_LENGTH_ERROR;
        }

        // 检测敏感词
        if (!StringUtils.isEmpty(guildIntro)) {
            boolean isLegal = sensitiveWordsService.checkIsLegal(guildIntro);
            if (!isLegal) {
                return ErrorCode.GUILD_INFO_ILLEGAL;
            }
        }

        return ErrorCode.SUCCESS;
    }

    public int checkGuildNotice(String guildNotice) {
        int introLength = guildNotice.getBytes(StandardCharsets.UTF_8).length;
        int maxIntroLength = gameConfigManager.getTables().getGuild_guildConstModel().get(107).TypeInt;
        if (introLength > maxIntroLength) {
            return ErrorCode.GUILD_NOTICE_LENGTH_ERROR;
        }

        // 检测敏感词
        if (!StringUtils.isEmpty(guildNotice)) {
            boolean isLegal = sensitiveWordsService.checkIsLegal(guildNotice);
            if (!isLegal) {
                return ErrorCode.GUILD_NOTICE_ILLEGAL;
            }
        }

        return ErrorCode.SUCCESS;
    }

    // 获取自己的入会条件值
    public int getConditionValue(long userId) {
        // 用章节id处理
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        return userExtend.getChapterId();
    }

    // 获取公会各个成员最大数量
    public int getGuildMaxMemberCount(int guildLevel, int position) {
        Guild_guildLevel guildLevelEntity = gameConfigManager.getTables().getGuild_guildLevelModel().get(guildLevel);
        if (position == GuildPosition.MEMBER) {
            return guildLevelEntity.MaxMemberCount;
        } else if (position == GuildPosition.VICE_PRESIDENT) {
            return guildLevelEntity.MaxPositionCount.get(0);
        } else if (position == GuildPosition.MANAGER) {
            return guildLevelEntity.MaxPositionCount.get(1);
        }
        return 0;
    }

}


package com.dxx.game.modules.reward.model;

/**
 * @Description : 用户VIP经验奖励
 * <AUTHOR> wzy
 * @Date : 2022/6/17 15:06
 **/
public class UserVipExpReward {

    private int currentVipLevel = 0;

    private int currentVipExp = 0;

    public static UserVipExpReward valueOf(int currentVipLevel, int currentVipExp) {
        UserVipExpReward userVipExpReward = new UserVipExpReward();
        userVipExpReward.setCurrentVipLevel(currentVipLevel);
        userVipExpReward.setCurrentVipExp(currentVipExp);
        return userVipExpReward;
    }


    public int getCurrentVipLevel() {
        return currentVipLevel;
    }

    public void setCurrentVipLevel(int currentVipLevel) {
        this.currentVipLevel = currentVipLevel;
    }

    public int getCurrentVipExp() {
        return currentVipExp;
    }

    public void setCurrentVipExp(int currentVipExp) {
        this.currentVipExp = currentVipExp;
    }
}

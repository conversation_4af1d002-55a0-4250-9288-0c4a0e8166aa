
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class IntegralShop_goodsModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.IntegralShop_goods> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.IntegralShop_goods> _dataList;
    
    public IntegralShop_goodsModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.IntegralShop_goods>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.IntegralShop_goods>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.IntegralShop_goods _v;
            _v = com.dxx.game.config.Bean.IntegralShop_goods.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.IntegralShop_goods> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.IntegralShop_goods> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.IntegralShop_goods get(int key) { return _dataMap.get(key); }

}

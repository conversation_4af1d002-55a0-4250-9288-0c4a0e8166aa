
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class Item_dropLv {
    public Item_dropLv(JsonObject _buf) { 
        drop_id = _buf.get("drop_id").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("level").getAsJsonArray(); level = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  level.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("reward").getAsJsonArray(); reward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  reward.add(_v0); }   }
    }

    public static Item_dropLv deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.Item_dropLv(_buf);
    }

    /**
     * 掉落组ID
     */
    public final int drop_id;
    /**
     * 等级区间
     */
    public final java.util.List<Integer> level;
    /**
     * 奖励内容<br/>物品id1,数量1,权重1|...
     */
    public final java.util.List<String> reward;

    

    @Override
    public String toString() {
        return "{ "
        + "drop_id:" + drop_id + ","
        + "level:" + level + ","
        + "reward:" + reward + ","
        + "}";
    }
}


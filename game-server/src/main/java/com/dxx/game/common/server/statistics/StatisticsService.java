package com.dxx.game.common.server.statistics;

import com.dxx.game.common.aws.dynamodb.capacity.ConsumedCapacityInfo;
import com.dxx.game.common.aws.dynamodb.capacity.ConsumedCapacityTools;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.RedisKeys;
import io.netty.handler.codec.http.FullHttpRequest;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: lsc
 * @createDate: 2024/11/11
 * @description:
 */
@Component
public class StatisticsService {

    @Resource
    public RedisTemplate<String, String> redisTemplate;
    @Resource
    private RedisService redisService;
    @Resource
    private GameConfigManager gameConfigManager;

    public void doRecord() {
        try {
            if (!gameConfigManager.isProd()) {
                StatisticsContext.StatisticsContextData statisticsContextData = StatisticsContext.getStatisticsContextData();
                if (statisticsContextData != null) {

                    String redisKey = RedisKeys.STATISTICS + DateUtils.getTimeHour0InDayOfBeijing();
                    int cmd = statisticsContextData.getCmd();
                    if (cmd == 0) {
                        return;
                    }

                    redisTemplate.executePipelined(new SessionCallback<Void>() {

                        @Override
                        public <K, V> Void execute(RedisOperations<K, V> operations) throws DataAccessException {
                            RedisOperations<String, String> stringOperations =
                                    (RedisOperations<String, String>) operations;
                            // 请求次数统计
                            stringOperations.opsForHash().increment(redisKey, String.valueOf(cmd), 1);

                            for (ConsumedCapacityInfo consumedCapacityInfo : statisticsContextData.getDdbConsumedCapacity()) {
                                for (ConsumedCapacityInfo.TableConsumedCapacityInfo info : consumedCapacityInfo.getInfos()) {
                                    String hashKey = info.tableName;
                                    stringOperations.opsForHash().increment(redisKey, "ddb-" + hashKey + "-rcu", info.cost.rcu);
                                    stringOperations.opsForHash().increment(redisKey, "ddb-" + hashKey + "-wcu", info.cost.wcu);

                                    stringOperations.opsForHash().increment(redisKey, cmd + "-" + hashKey + "-rcu", info.cost.rcu);
                                    stringOperations.opsForHash().increment(redisKey, cmd + "-" + hashKey + "-wcu", info.cost.wcu);
                                }
                            }

                            stringOperations.expire(redisKey, DateUtils.SECONDS_15_DAY, TimeUnit.SECONDS);

                            return null;
                        }
                    });
                }
            }
        } finally {
            StatisticsContext.clear();
        }
    }

    public Map<Object, Object> queryData(FullHttpRequest fullRequest) {
        String host = fullRequest.headers().get("Host");
        if (gameConfigManager.isDevelop() && gameConfigManager.isTest() && !host.startsWith("internal-")) {
            return null;
        }
        Map<String, String> params = HttpRequester.getParams(fullRequest);
        String date = params.get("date") + " 00:00:00";
        long timestamp = DateUtils.parseToTimestamp(date);
        return this.queryData(timestamp);
    }

    private Map<Object, Object> queryData(long timestamp) {
        String redisKey = RedisKeys.STATISTICS + timestamp;
        return redisService.hGetAll(redisKey);
    }

    public void clear() {
        StatisticsContext.clear();
    }
}

package com.dxx.game.common.channel.oppo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.channel.AndroidCallBackService;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.common.consts.AndroidPayCbErrorCode;
import com.dxx.game.common.channel.common.consts.ChannelID;
import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.channel.common.model.PayCbVo;
import com.dxx.game.common.channel.common.util.PaymentUtils;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.server.handler.HttpRequester;
import com.dxx.game.common.utils.DateUtils;
import com.google.api.client.util.Base64;
import com.google.common.collect.Maps;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * oppo
 * <AUTHOR>
 * @date 2022/4/21 16:22
 */
@Slf4j
@Service
public class OppoService implements ChannelService {

    // 渠道ID
    private static final ChannelID CHANNEL_ID = ChannelID.Oppo;

    @Autowired
    private ChannelConfig channelConfig;
    @Autowired
    private AndroidCallBackService androidCallBackService;

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        try {
            if (!channelConfig.getOppoConfig().isVerifyLogin()) {
                return true;
            }
            JSONObject jsonObject = JSONObject.parseObject(verification);
            String ssoid = jsonObject.getString("ssoid");
            String token = jsonObject.getString("token");
            String url = channelConfig.getOppoConfig().getVerifyLoginUrl();
            String encodeToken = URLEncoder.encode(token,"UTF-8");
            String appKey = channelConfig.getOppoConfig().getAppKey();
            String appSecret = channelConfig.getOppoConfig().getAppSecret();
            url = url + "?fileId=" + ssoid + "&token=" + encodeToken;

            long ts = DateUtils.getUnixTime();
            String nonce = UUID.randomUUID().toString();
            StringBuilder sBuilder = new StringBuilder();
            sBuilder.append("oauthConsumerKey").append("=").append(appKey).append("&");
            sBuilder.append("oauthToken").append("=").append(encodeToken).append("&");
            sBuilder.append("oauthSignatureMethod").append("=").append("HMAC-SHA1").append("&");
            sBuilder.append("oauthTimestamp").append("=").append(ts).append("&");
            sBuilder.append("oauthNonce").append("=").append(nonce).append("&");
            sBuilder.append("oauthVersion").append("=").append("1.0").append("&");

            String baseStr = sBuilder.toString();
            String sign = this.makeLoginSign(baseStr, appSecret);

            Map<String, String> headers = new HashMap<>();
            headers.put("param", baseStr);
            headers.put("oauthSignature", sign);

            String resp = OkHttpClientUtil.get(url, headers, null, String.class);
            JSONObject respObj = JSONObject.parseObject(resp);
            int resultCode = Integer.parseInt(respObj.getString("resultCode"));
            if (resultCode != 200) {
                log.error("oppo verifyLogin failed, resp:{}", resp);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("oppo verifyLogin failed e:", e);
            return false;
        }
    }

    /**
     * 支付回调接口
     */
    @Override
    public String payCb(FullHttpRequest request) {

        PayCbVo payCbVo = this.checkOrderState(request);
        if (payCbVo == null) {
            return "failure";
        }

        int code = androidCallBackService.doDeliverGoods(payCbVo);
        if (code != AndroidPayCbErrorCode.SUCCESS) {
            log.error("payCb doDeliverGoods failed, body:{}", payCbVo);
            return "result=FAIL&resultMsg=发货失败, code=" + code;
        }

        this.notifyResult(payCbVo.getOrderId(), payCbVo.getCpOrderId(), payCbVo.getUserId());
        return "result=OK&resultMsg=成功";
    }

    /**
     * 校验订单状态
     * @param request
     * @return
     */
    private PayCbVo checkOrderState(FullHttpRequest request) {
        Map<String, String> params = HttpRequester.getPostParams(request);

        String notifyId = params.get("notifyId");   // 订单号
        String partnerOrder = params.get("partnerOrder");   // CP订单号
        String productName = params.get("productName");
        String productDesc = params.get("productDesc");
        int price = Integer.parseInt(params.get("price"));  // 商品价格单位为分
        int count = Integer.parseInt(params.get("count"));
        String attach = params.get("attach");               // 透传参数
        String sign = params.get("sign");

        String baseString = this.getBaseString(notifyId, partnerOrder, productName, productDesc, price, count, attach);

        boolean check = false;
        try {
            check = doCheck(baseString, sign, channelConfig.getOppoConfig().getPublicKey());
        } catch (Exception e) {
            log.error("payCb check sign failed, baseString:{}, sign:{}, e:", baseString, sign, e);
            return null;
        }

        if (!check) {
            log.error("payCb check sign failed, baseString:{}, sign:{}, e:", baseString, sign);
            return null;
        }

        PayBackParamsModel payBackParamsModel = PaymentUtils.formatPassBackParams(attach);

        PayCbVo payCbVo = new PayCbVo();
        payCbVo.setUserId(payBackParamsModel.getUserId());
        payCbVo.setExtraInfo(payBackParamsModel.getExtraInfo());
        payCbVo.setOrderId(notifyId);
        payCbVo.setCpOrderId(partnerOrder);
        payCbVo.setPreOrderId(payBackParamsModel.getPreOrderId());
        payCbVo.setProductId(payBackParamsModel.getProductId());
        payCbVo.setChannelId(CHANNEL_ID.getId());
        payCbVo.setAmount(price);
        return payCbVo;

    }

    private String getBaseString(String notifyId, String partnerOrder, String productName, String productDesc,
                                 int price, int count,  String attach) {
        StringBuilder sb = new StringBuilder();
        sb.append("notifyId=").append(notifyId);
        sb.append("&partnerOrder=").append(partnerOrder);
        sb.append("&productName=").append(productName);
        sb.append("&productDesc=").append(productDesc);
        sb.append("&price=").append(price);
        sb.append("&count=").append(count);
        sb.append("&attach=").append(attach);
        return sb.toString();
    }

    private boolean doCheck(String content, String sign, String publicKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = Base64.decodeBase64(publicKey);
        PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

        java.security.Signature signature = java.security.Signature.getInstance("SHA1WithRSA");

        signature.initVerify(pubKey);
        signature.update(content.getBytes("UTF-8"));
        return signature.verify(Base64.decodeBase64(sign));
    }

    private void notifyResult(String orderId, String cpOrderId, long userId) {

        TreeMap<String, String> map = new TreeMap<>();
        map.put("cpOrderId", cpOrderId);
        map.put("msg","ok");
        map.put("orderId", orderId);
        map.put("sendPropsTime", DateUtils.getDateTime());
        map.put("sendPropsRole", String.valueOf(userId));

        JSONObject client = new JSONObject();
        client.put("pkg", channelConfig.getOppoConfig().getPackageName());

        String data = getEncryptData(channelConfig.getOppoConfig().getAppSecret(), map);
        Long t = System.currentTimeMillis();

        TreeMap<String, Object> request = new TreeMap();
        request.put("t", String.valueOf(t));
        request.put("client", client);
        request.put("data", data);
        request.put("sign", this.getSign(channelConfig.getOppoConfig().getCpPrivateKey(), request));

        int maxRetry = 5;
        while (maxRetry >= 0) {
            maxRetry --;
            String resp = OkHttpClientUtil.postJson(channelConfig.getOppoConfig().getNotifyUrl(), null, request, String.class);
            JSONObject jsonObject = JSONObject.parseObject(resp);
            String code = jsonObject.getString("code");
//            code = "50000";
            if (code.equals("50000")) {
                // 重试一次
                log.error("oppo notify result == 50000, retry:{}", maxRetry);
                continue;
            }

            if (!code.equals("20000")) {
                log.error("oppo notify result failed : {}", resp);
            }
            break;
        }




    }

    private String getEncryptData(String appSecret, TreeMap<String, String> map) {
        String key = appSecret.substring(0, 16);
        String content = JSON.toJSONString(map);
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NOPadding");
            int blockSize = cipher.getBlockSize();
            byte[] dataBytes = content.getBytes(StandardCharsets.UTF_8);
            int plaintextLen = dataBytes.length;
            if (plaintextLen % blockSize != 0) {
                plaintextLen = plaintextLen + (blockSize - plaintextLen %
                        blockSize);
            }
            byte[] plaintext = new byte[plaintextLen];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            SecretKeySpec keySpec = new
                    SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new
                    IvParameterSpec(key.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encryptBytes = cipher.doFinal(plaintext);
            return java.util.Base64.getEncoder().encodeToString(encryptBytes);
        } catch (Exception e) {
            log.error("请求体加密失败, appSecret={}, map={}", appSecret,
                    JSON.toJSONString(map), e);
            return StringUtils.EMPTY;
        }
    }

    private String getSign(String privateKey, TreeMap<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        Iterator<Map.Entry<String, Object>> parts = map.entrySet().iterator();
        while (parts.hasNext()) {
            Map.Entry<String, Object> part = parts.next();
            if (part.getValue() != null) {

                sb.append(part.getKey()).append("=").append(part.getValue()).append("&");
            }
        }
        return sign(privateKey, sb.toString());
    }

    private String sign(String privateKey, String content) {
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new
                    PKCS8EncodedKeySpec(java.util.Base64.getDecoder().decode(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey priKey =
                    keyFactory.generatePrivate(pkcs8EncodedKeySpec);
            Signature signature = Signature.getInstance("SHA1WithRSA");
            signature.initSign(priKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            byte[] signed = signature.sign();
            return new String(java.util.Base64.getEncoder().encode(signed),
                    StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("生成签名失败, content={} privateKey={}", content,
                    privateKey, e);
            return StringUtils.EMPTY;
        }
    }

    private String makeLoginSign(String baseStr, String appSecret) throws Exception {
        byte[] byteHMAC = null;
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec spec = null;
        String oauthSignatureKey = appSecret + "&";
        spec = new SecretKeySpec(oauthSignatureKey.getBytes(),"HmacSHA1");
        mac.init(spec);
        byteHMAC = mac.doFinal(baseStr.getBytes());
        return URLEncoder.encode(new String(java.util.Base64.getEncoder().encode(byteHMAC),
                StandardCharsets.UTF_8), "UTF-8");
    }

    /**
     * 登录数据归因
     * @param accountId
     * @return
     */
    public String getLoginInfo(String accountId) {
        JSONObject client = new JSONObject();
        client.put("pkg", channelConfig.getOppoConfig().getPackageName());
        TreeMap<String, String> dataMap = new TreeMap<>();
        dataMap.put("oaid", "");
        dataMap.put("ssoid", accountId);
        dataMap.put("imei", "");
        String data = this.getEncryptData(channelConfig.getOppoConfig().getAppSecret(), dataMap);
        long t = System.currentTimeMillis();

        TreeMap<String, Object> request = new TreeMap();
        request.put("t", String.valueOf(t));
        request.put("client", client);
        request.put("data", data);
        request.put("sign", this.getSign(channelConfig.getOppoConfig().getCpPrivateKey(), request));

        String response = OkHttpClientUtil.postJson(channelConfig.getOppoConfig().getLoginInfoUrl(), null, request, String.class);
        JSONObject respObj = JSONObject.parseObject(response);
        if (!respObj.getString("code").equals("20000")) {
            JSONObject newData = new JSONObject();
            newData.put("channel", "0");
            newData.put("adId", "0");
            respObj.put("data", newData);
            return respObj.toJSONString();
        }
        return response;
    }

    public String getOrderInfo(String orderId) {
        JSONObject client = new JSONObject();
        client.put("pkg", channelConfig.getOppoConfig().getPackageName());
        TreeMap<String, String> dataMap = new TreeMap<>();
        dataMap.put("orderId", orderId);

        String data = this.getEncryptData(channelConfig.getOppoConfig().getAppSecret(), dataMap);

        long t = System.currentTimeMillis();
        TreeMap<String, Object> request = new TreeMap();
        request.put("t", String.valueOf(t));
        request.put("client", client);
        request.put("data", data);
        request.put("sign", this.getSign(channelConfig.getOppoConfig().getCpPrivateKey(), request));

        return OkHttpClientUtil.postJson(channelConfig.getOppoConfig().getOrderInfoUrl(), null, request, String.class);
    }

}

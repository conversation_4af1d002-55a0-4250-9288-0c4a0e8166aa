package com.dxx.game.modules.reward.processor;

import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.vip.VipEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.reward.action.RewardAction;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.model.UserVipExpReward;
import com.dxx.game.modules.reward.result.RewardResult;
import com.dxx.game.modules.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentSkipListMap;


/**
 * @Description : TODO
 * <AUTHOR> wzy
 * @Date : 2022/6/17 14:06
 **/
@Component
public class UserVipExpProcessor implements RewardProcessor {

    private static final Logger logger = LoggerFactory.getLogger(UserVipExpProcessor.class);
    @Autowired
    private UserService userService;
    @Autowired
    private GameConfigManager gameConfigManager;
    @Autowired
    private UserDao userDao;

    @Override
    public RewardType getType() {
        return RewardType.RESOURCE;
    }

    @Override
    public RewardResourceType getRewardResourceType() {
        return RewardResourceType.VIP_EXP;
    }

    @Override
    public RewardAction tryReward(long userId, Reward reward) {
        int resultCode = ErrorCode.SUCCESS;
        User user = userService.getUser(userId);
        if (user == null) {
            resultCode = ErrorCode.QUERY_USER_DATA_ERROR;
        } else {
            if (reward.getCount() <= 0) {
                logger.error("add user vip exp count error ,value : {}", reward.getCount());
                resultCode = ErrorCode.PARAMS_ERROR;
            }
        }
        return simpleRewardAction(reward, resultCode);
    }

    @Override
    public RewardResult<?> executeReward(long userId, RewardAction rewardAction) {
        if (rewardAction.isFailed()) {
            return null;
        }
        User user = userService.getUser(userId);
        VipConfig vipConfig = gameConfigManager.getVipConfig();
        ConcurrentSkipListMap<Integer, VipEntity> vip = vipConfig.getVip();
        int beforeVipExp = user.getVipExp();
        int addExp = rewardAction.getReward().getCount();
        int toExp = beforeVipExp + addExp;
        int maxLevel = vip.size();
        int toLevel = user.getVipLevel();
        while (true) {
            if (toLevel >= maxLevel) {
                break;
            }
            if (!vip.containsKey(toLevel + 1)) {
                break;
            }
            VipEntity vipEntity = vip.get(toLevel + 1);
            if (vipEntity == null) {
                break;
            }
            int needExp = vipEntity.getExp();
            if (needExp > toExp) {
                break;
            }
            toLevel++;
        }
        user.setVipLevel((short) toLevel);
        user.setVipExp(toExp);
        userDao.update(user);
        UserVipExpReward userVipExpReward = UserVipExpReward.valueOf(toLevel, toExp);
        RewardResult<UserVipExpReward> result = new RewardResult<>(this.getType(), this.getRewardResourceType());
        result.setActualCount(addExp);
        result.setCurrent(userVipExpReward);

        // vip权限触发
        userService.vipPermissionTrigger(userId);

        return result;
    }
}

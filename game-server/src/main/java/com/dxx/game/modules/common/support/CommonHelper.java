package com.dxx.game.modules.common.support;


import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.*;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.CommonProto.*;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.rank.model.UserRankExtraData;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.shop.support.ShopSupport;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.user.support.VitalitySupport;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * common 协议帮助类
 *
 * <AUTHOR>
 * @date 2019-12-14 17:52
 */
@Component
public class CommonHelper {

    private static final Logger log = LoggerFactory.getLogger(CommonHelper.class);
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserService userService;
    @Resource
    private PayService payService;
    @Resource
    private ShopSupport shopSupport;
    @Resource
    private VitalitySupport userVitalitySupport;

    private static CommonHelper commonHelper;

    @PostConstruct
    private void init() {
        commonHelper = this;
    }

    /**
     * 构建UserCurrency Message
     *
     * @param user
     * @return
     */
    public static UserCurrency buildUserCurrencyMsg(User user) {
        UserCurrency.Builder userCurrency = UserCurrency.newBuilder();
        userCurrency.setCoins(user.getCoins());
        userCurrency.setDiamonds(user.getDiamonds());
        return userCurrency.build();
    }

    /**
     * 构建UserLevel message
     *
     * @param user
     * @return
     */
    public static UserLevel buildUserLevelMsg(User user) {
        UserLevel.Builder userLevel = UserLevel.newBuilder();
        userLevel.setLevel(user.getLevel());
        userLevel.setExp(user.getExp());
        return userLevel.build();
    }

    public static List<RewardDto> buildRewardDtoList(List<List<Integer>> config) {
        if (config == null) {
            return new ArrayList<>();
        }
        List<RewardDto> result = new ArrayList<>(config.size());
        List<List<Integer>> copyConfig = CollectionUtils.copyM(config);
        for (int i = 0; i < copyConfig.size(); i++) {
            if (copyConfig.get(i).size() < 3) {
                int itemId = copyConfig.get(i).get(0);
                int itemCount = copyConfig.get(i).get(1);
                int itemType = 0;
                if (itemId > 0) {
                    if (commonHelper.gameConfigManager.getItemConfig().getItemEntity(itemId) == null) {
                        return new ArrayList<>();
                    }
                    itemType = commonHelper.gameConfigManager.getItemConfig().getItemEntity(itemId).getItemType();
                }

                copyConfig.get(i).clear();
                copyConfig.get(i).add(itemType);
                copyConfig.get(i).add(itemId);
                copyConfig.get(i).add(itemCount);
            }
            result.add(CommonHelper.buildRewardDto(copyConfig.get(i)));
        }
        return result;
    }


    public static RewardDto buildRewardDto(List<Integer> config) {
        if (config.size() < 3) {
            int type = commonHelper.gameConfigManager.getItemConfig().getItemEntity(config.get(0)).getItemType();
            return buildRewardDto(type, config.get(0), config.get(1));
        } else {
            return buildRewardDto(config.get(0), config.get(1), config.get(2));
        }
    }

    public static RewardDto buildRewardDto(int type, int configId, int num) {
        RewardDto.Builder rewardBuilder = RewardDto.newBuilder();
        rewardBuilder.setType(type);
        rewardBuilder.setConfigId(configId);
        rewardBuilder.setCount(num);
        return rewardBuilder.build();
    }

    /**
     * 构造通用奖励信息返回
     */
    public static CommonData buildCommonData(RewardResultSet... resultSets) {
        CommonData.Builder builder = CommonData.newBuilder();
        for (RewardResultSet resultSet : resultSets) {
            fillCommonData(resultSet, builder, false);
        }

        fillCommonData((RewardResultSet) ResponseContext.getLevelUpRewardResultSet(), builder, true);
        return builder.build();
    }

    public static CommonData buildCommonDataList(List<RewardResultSet> resultSets) {
        RewardResultSet[] resultSetsArray = new RewardResultSet[resultSets.size()];
        resultSets.toArray(resultSetsArray);
        return buildCommonData(resultSetsArray);
    }

    private static void fillCommonData(RewardResultSet resultSet, CommonData.Builder builder, boolean isLevelUpReward) {
        if (resultSet == null) {
            return;
        }

        if (resultSet.isHasCurrency() && !builder.hasUpdateUserCurrency()) {
            UpdateUserCurrency.Builder updateUserCurrencyBuilder = UpdateUserCurrency.newBuilder();
            updateUserCurrencyBuilder.setIsChange(true);
            updateUserCurrencyBuilder.setUserCurrency(buildUserCurrencyMsg(RequestContext.getUser()));
            builder.setUpdateUserCurrency(updateUserCurrencyBuilder);
        }

        if (resultSet.getHeros() != null) {
            builder.addAllHeros(resultSet.getHeros());
        }

        if (resultSet.getEquips() != null) {
            builder.addAllEquipment(resultSet.getEquips());
        }

        if (resultSet.getItems() != null) {
            builder.addAllItems(resultSet.getItems());
        }

        if (resultSet.getUserLevel() != null) {
            UpdateUserLevel.Builder updateUserLevelBuilder = UpdateUserLevel.newBuilder();
            updateUserLevelBuilder.setIsChange(true);
            updateUserLevelBuilder.setUserLevel(resultSet.getUserLevel());
            builder.setUpdateUserLevel(updateUserLevelBuilder);
        }


        if (resultSet.getRewards() != null) {
            if (isLevelUpReward) {
                UpdateUserLevel.Builder updateUserLevelBuilder = builder.getUpdateUserLevelBuilder();
                updateUserLevelBuilder.addAllLevelUpReward(resultSet.getRewards());
                builder.setUpdateUserLevel(updateUserLevelBuilder);
            } else {
                builder.addAllReward(resultSet.getRewards());
            }
        }

        if (resultSet.getUserVipLevel() != null) {
            UpdateUserVipLevel.Builder updateUserVipLevelBuilder = UpdateUserVipLevel.newBuilder();
            updateUserVipLevelBuilder.setIsChange(true);
            UserVipLevel.Builder userVipLevelBuilder = resultSet.getUserVipLevel().toBuilder();
            List<Integer> vipIdList = commonHelper.shopSupport.vipGetRewardIdList();
            userVipLevelBuilder.addAllRewardId(vipIdList);
            updateUserVipLevelBuilder.setUserVipLevel(userVipLevelBuilder);
            builder.setUpdateUserVipLevel(updateUserVipLevelBuilder);
        }


        if (resultSet.getDeleteEquipRowIds() != null) {
            builder.addAllDeleteEquipRowIds(resultSet.getDeleteEquipRowIds());
        }

        if (resultSet.getUserBattlePassScore() != 0){
            builder.setBattlePassScore(resultSet.getUserBattlePassScore());
        }

    }


    /**
     * 构造通用信息返回 返回红点
     *
     * @return
     */
    public static CommonData buildCommonData() {
        CommonData.Builder builder = CommonData.newBuilder();
        return builder.build();
    }


    public static ItemDto buildItemDto(Item item) {
        ItemDto.Builder builder = ItemDto.newBuilder();
        builder.setRowId(item.getRowId());
        builder.setItemId(item.getItemId());
        builder.setCount(item.getCount());
        return builder.build();
    }

    /**
     * 用户信息
     *
     * @param user
     * @return
     */
    public static UserInfoDto buildUserInfoDto(User user) {
        UserInfoDto.Builder result = UserInfoDto.newBuilder();
        if (!StringUtils.isEmpty(user.getNickName())) {
            result.setNickName(user.getNickName());
        }
        if (user.getAvatar() != null) {
            result.setAvatar(user.getAvatar());
        }
        if (user.getAvatarFrame() != null) {
            result.setAvatarFrame(user.getAvatarFrame());
        }
        result.setPower(commonHelper.userService.getPower(user.getUserId()));
        result.setLevel(user.getLevel());
        return result.build();
    }

    /**
     * 构造 Equipment Message
     *
     * @param equip
     * @return
     */
    public static EquipmentDto buildEquipmentDto(Equip equip) {
        EquipmentDto.Builder builder = EquipmentDto.newBuilder();
        builder.setRowId(equip.getRowId());
        builder.setEquipId(equip.getEquipId());
        builder.setLevel(equip.getLevel());
//        builder.setQuality(equip.getQuality());
        builder.setExp(equip.getExp());
        builder.setHeroRowId(equip.getHeroRowId());
        return builder.build();
    }

    /**
     * 保存通用请求数据
     *
     * @param user
     */
    public static void buildCommonParams(User user) {
        CommonProto.CommonParams.Builder commonParams = CommonProto.CommonParams.newBuilder();
        commonParams.setVersion(user.getClientNetVersion());
        if (!StringUtils.isEmpty(user.getDeviceId())) {
            commonParams.setDeviceId(user.getDeviceId());
            RequestContext.setDeviceId(user.getDeviceId());
        }
        if (!StringUtils.isEmpty(user.getAccountId())) {
            commonParams.setAccountId(user.getAccountId());
            RequestContext.setAccountId(user.getAccountId());
        }
        RequestContext.setCommonParams(commonParams.buildPartial());
        RequestContext.setUserId(user.getUserId());

    }

    public static TowerRankDto buildTowerRankDto(long userId, int tower, UserRankExtraData data) {
        TowerRankDto.Builder builder = TowerRankDto.newBuilder();

        builder.setUserId(userId);
        builder.setNickName(data.getNickName() == null ? "" : data.getNickName());
        builder.setAvatar(data.getAvatar());
        builder.setAvatarFrame(data.getAvatarFrame());
        builder.setPower(data.getPower());
        builder.setTower(tower);

        return builder.build();
    }

    public static RankDto buildRankDto(long userId, long score, UserRankExtraData data) {
        RankDto.Builder builder = RankDto.newBuilder();

        builder.setUserId(userId);
        builder.setNickName(data.getNickName() == null ? "" : data.getNickName());
        builder.setAvatar(data.getAvatar());
        builder.setAvatarFrame(data.getAvatarFrame());
        builder.setPower(data.getPower());
        builder.setScore(score);

        return builder.build();
    }

    public static CommonProto.HeroDto buildHeroDto(Hero hero) {
        CommonProto.HeroDto.Builder builder = CommonProto.HeroDto.newBuilder();
        builder.setRowId(hero.getRowId());
        builder.setHeroId(hero.getHeroId());
        builder.setLevel(hero.getLevel());
        builder.setExp(hero.getExp());
        builder.setStar(hero.getStar());
        builder.setQuality(hero.getQuality());
        builder.setAdvance(hero.getAdvance());

        if(hero.getSkinConfigId() != null) {
            builder.setSkinConfigId(hero.getSkinConfigId());
        }

        return builder.build();
    }

    /**
     * 构建UserVipLevel message
     *
     * @param user
     * @return
     */
    public static UserVipLevel buildUserVipLevelMsg(User user) {
        UserVipLevel.Builder userVipLevel = UserVipLevel.newBuilder();
        userVipLevel.setVipLevel(user.getVipLevel());
        userVipLevel.setVipExp(user.getVipExp());
        List<Integer> vipLevelList = commonHelper.shopSupport.vipGetRewardIdList();
        userVipLevel.addAllRewardId(vipLevelList);
        return userVipLevel.build();
    }


    public static String convertDto2Json(Message msg) {
        try {
            return JsonFormat.printer().print(msg);
        } catch (Exception e) {
            log.error("convertDto2Json, e:", e);
        }

        return "";
    }

    public static CommonProto.BattleUnitDto convertJson2Uni(String data) {
        CommonProto.BattleUnitDto.Builder dto = CommonProto.BattleUnitDto.newBuilder();

        if(data == null || data.isEmpty()) {
            return dto.build();
        }

        try {
            JsonFormat.parser().merge(data, dto);
        } catch (Exception e) {
            log.error("convertJson2Dto, e:", e);
        }

        return dto.build();
    }

    public static UserVitality buildUserVitality(User user) {
        UserVitality.Builder builder = UserVitality.newBuilder();
        builder.setValue(user.getVitality());
        builder.setMaxValue(commonHelper.userVitalitySupport.getMaxVitality(user.getUserId()));
        builder.setTs(user.getVitalityTimeStamp());
        builder.setBuyCount(user.getVitalityBuyCount());
        return builder.build();
    }

    public static UpdateUserVitality buildUpdateUserVitality(User user) {
        UpdateUserVitality.Builder builder = UpdateUserVitality.newBuilder();
        builder.setIsChange(true);
        builder.setVitality(buildUserVitality(user));
        return builder.build();
    }

    public static List<List<Integer>> convertRewardsToAttributeList(List<RewardDto> rewards) {
        return rewards.stream()
                .map(reward -> List.of(reward.getType(), reward.getConfigId(), reward.getCount()))
                .collect(Collectors.toList());
    }
}



















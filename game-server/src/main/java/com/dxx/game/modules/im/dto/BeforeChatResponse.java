package com.dxx.game.modules.im.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @author: lsc
 * @createDate: 2025/4/11
 * @description:
 */
@lombok.Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BeforeChatResponse extends CallbackResponse {
    private int code;
    private String message;
    private Data data;

    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {
        private MsgContent msgContent;
    }
}

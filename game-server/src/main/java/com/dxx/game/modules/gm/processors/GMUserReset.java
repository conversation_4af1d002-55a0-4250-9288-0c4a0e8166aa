package com.dxx.game.modules.gm.processors;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.modules.gm.annotation.GMCommand;
import com.dxx.game.modules.gm.common.AbstractGMProcessor;
import com.dxx.game.modules.gm.consts.GMCommandType;
import com.dxx.game.modules.gm.consts.GMErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @author: lsc
 * @createDate: 2025/4/17
 * @description:
 */
@Slf4j
@Component
@GMCommand(GMCommandType.USER_RESET)
public class GMUserReset extends AbstractGMProcessor {

    @Resource
    private UserDao userDao;


    @Override
    protected Object execute(JSONObject params) {
        long userId = params.getLongValue("userId");
        User user = userDao.getByUserId(userId);
        if (user == null) {
            return this.error(GMErrorCode.ERROR_CODE_USER_NOT_EXIST,"user not exist");
        }
        user.setDeviceId(user.getDeviceId() + "_" + System.currentTimeMillis());
        user.setAccountId(user.getAccountId() + "_" + System.currentTimeMillis());
        userDao.updateAccount(user);

        return this.success();
    }
}

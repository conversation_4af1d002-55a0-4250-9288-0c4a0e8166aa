
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class ActivityDive_DiveDynamicModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityDive_DiveDynamic> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.ActivityDive_DiveDynamic> _dataList;
    
    public ActivityDive_DiveDynamicModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityDive_DiveDynamic>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.ActivityDive_DiveDynamic>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.ActivityDive_DiveDynamic _v;
            _v = com.dxx.game.config.Bean.ActivityDive_DiveDynamic.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.ID, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.ActivityDive_DiveDynamic> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.ActivityDive_DiveDynamic> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.ActivityDive_DiveDynamic get(int key) { return _dataMap.get(key); }

}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_PushPacks {
    public IAP_PushPacks(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        PackType = _buf.get("PackType").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("Products").getAsJsonArray(); Products = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  Products.add(_v0); }   }
        SuperProduct = _buf.get("SuperProduct").getAsInt();
        priority = _buf.get("priority").getAsInt();
        Parameters = _buf.get("Parameters").getAsString();
        NameID = _buf.get("NameID").getAsString();
        DescID = _buf.get("DescID").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("ValueDescID").getAsJsonArray(); ValueDescID = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  ValueDescID.add(_v0); }   }
        IconAtlasID = _buf.get("IconAtlasID").getAsInt();
        IconName = _buf.get("IconName").getAsString();
        IsEffect = _buf.get("IsEffect").getAsInt();
    }

    public static IAP_PushPacks deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_PushPacks(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 触发方式<br/><br/>1、首充礼包(1次)<br/>2、开服礼包(1次)<br/>3、限时礼包(可重复购买)<br/>4、章节礼包(只显示一个，可填写多个)<br/>5、推送礼包(只显示一个，可填写多个)
     */
    public final int PackType;
    /**
     * 当即奖励<br/>itemid,count,showCount|itemid,count,,showCount
     */
    public final java.util.List<String> Products;
    /**
     * 指向Products的index，从0开始（只用来展示，具体奖励还是配在Products中）
     */
    public final int SuperProduct;
    /**
     * 优先级<br/>1最优先
     */
    public final int priority;
    /**
     * 参数<br/>
     */
    public final String Parameters;
    /**
     * 名称
     */
    public final String NameID;
    /**
     * 描述
     */
    public final String DescID;
    /**
     * 价值描述
     */
    public final java.util.List<String> ValueDescID;
    /**
     * icon图集
     */
    public final int IconAtlasID;
    /**
     * icon名称
     */
    public final String IconName;
    /**
     * 是否显示特效
     */
    public final int IsEffect;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "PackType:" + PackType + ","
        + "Products:" + Products + ","
        + "SuperProduct:" + SuperProduct + ","
        + "priority:" + priority + ","
        + "Parameters:" + Parameters + ","
        + "NameID:" + NameID + ","
        + "DescID:" + DescID + ","
        + "ValueDescID:" + ValueDescID + ","
        + "IconAtlasID:" + IconAtlasID + ","
        + "IconName:" + IconName + ","
        + "IsEffect:" + IsEffect + ","
        + "}";
    }
}


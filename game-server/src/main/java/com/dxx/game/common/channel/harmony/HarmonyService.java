package com.dxx.game.common.channel.harmony;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.internal.util.codec.Base64;
import com.dxx.game.common.channel.ChannelService;
import com.dxx.game.common.channel.common.config.ChannelConfig;
import com.dxx.game.common.channel.harmony.model.HarmonyConfig;
import com.dxx.game.common.channel.harmony.model.HarmonyPurchaseData;
import com.dxx.game.common.httpclient.OkHttpClientUtil;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.google.common.collect.Maps;
import io.netty.handler.codec.http.FullHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Security;
import java.security.spec.X509EncodedKeySpec;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: lsc
 * @createDate: 2024/1/15
 * @description:
 */
@Slf4j
@DependsOn("okHttpClient")
@Service
public class HarmonyService implements ChannelService {

    private static final String REDIS_TOKEN_KEY = "harmony_token";

    @Resource
    private RedisService redisService;
    @Resource
    private ChannelConfig channelConfig;
    @Resource
    private UserDao userDao;

//    @PostConstruct
//    private void init() {
//        String ve = "{\"authorizationCode\":\"DwEEAArgbYTzbSz3lgYmcH9Ytlfy+lc9zVhRmecq/2H3WYhTFUVgZiEecHcrWARhlb8KI+Q7ALJhGat7qsL1hqRtr7oM/WNlEzdA6Z4slpExoiXcHKdgcqaNXle0Cp3rTyj64mLRuUorxcAVzdlmbyLanists+wizgChz+1p3r78Hob9b1am2OEtWKj6jwFyIZvWtPYpYjpRLAEwSrwGlnNtMhDEaAclPukX0evX8PJGz+iCDwdyH9J1q4bRQ+IAqQ0+RJPrfb9j\",\"gamePlayerId\":\"A96EAE058D3C8DCD739F4386070EC846163FA98B499E2119D2F88DEB80531B69\"}";
//        boolean aaa = this.verifyLogin("A96EAE058D3C8DCD739F4386070EC846163FA98B499E2119D2F88DEB80531B69", ve);
//        System.out.println(aaa);
//    }

    public Map<String, Object> account(JSONObject params) {
//        String teamPlayerId = params.getString("teamPlayerId");
//        if (StringUtils.isEmpty(teamPlayerId)) {
//            return null;
//        }
//        Map<String, Object> result = new HashMap<>();
//        result.put("code", 0);
//        result.put("userId", 0);
//        User user = userDao.getByAccountId(teamPlayerId);
//        if (user != null) {
//            result.put("userId", user.getUserId());
//        }
//        return result;
        return null;
    }

    @Override
    public boolean verifyLogin(String accountId, String verification) {
        try {
            HarmonyConfig harmonyConfig = channelConfig.getHarmonyConfig();
            if (!harmonyConfig.isVerifyLogin()) {
                return true;
            }

            JSONObject jsonObject = JSONObject.parseObject(verification);
            String gamePlayerId = jsonObject.getString("gamePlayerId");
            if (!accountId.equals(gamePlayerId)) {
                return false;
            }
            String authorizationCode = jsonObject.getString("authorizationCode");

            String accessToken = this.getUserAccessToken(authorizationCode);
            if (StringUtils.isEmpty(accessToken)) {
                log.error("getUserAccessToken is null, accountId:{}, verification:{}", gamePlayerId, verification);
                return false;
            }

            Map<String, String> params = new HashMap<>();
            params.put("method", "external.hms.gs.getPlayerInfo");
            params.put("accessToken", accessToken);
//            params.put("open_id", "OPENID");
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-type", "application/x-www-form-urlencoded");

            String resp = OkHttpClientUtil.postForm(harmonyConfig.getVerifyLoginUrl(), headers, params, String.class);
            if (StringUtils.isEmpty(resp)) {
                log.error("harmony verfiyLogin resp is empty, verification:{}", verification);
                return true;
            }
            JSONObject respObj = JSONObject.parseObject(resp);
            int rtnCode = respObj.getIntValue("rtnCode");
            if (rtnCode != 0) {
                log.error("harmony verfiyLogin failed resp:{}", resp);
                return false;
            }
            JSONObject acctInfo = respObj.getJSONObject("acctInfo");
            String receiveGamePlayerId = "";
            if (acctInfo.containsKey("gamePlayerAcct")) {
                JSONObject gamePlayerAcct = acctInfo.getJSONObject("gamePlayerAcct");
                receiveGamePlayerId = gamePlayerAcct.getString("id");
            } else if (acctInfo.containsKey("teamPlayerAcct")) {
                JSONObject teamPlayerAcct = acctInfo.getJSONObject("teamPlayerAcct");
                receiveGamePlayerId = teamPlayerAcct.getString("id");
            }

            if (!receiveGamePlayerId.equals(accountId)) {
                log.error("harmony verifyLogin failed, accountId:{}, verification:{},  resp:{}", accountId, verification, resp);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("harmony verifyLogin error:", e);
            return false;
        }
    }

    /**
     * 验证购买token
     */
    public HarmonyPurchaseData verifyToken(JSONObject purchaseData) {
        HarmonyConfig harmonyConfig = channelConfig.getHarmonyConfig();
        String applicationId = purchaseData.getString("applicationId");
        if (!applicationId.equals(harmonyConfig.getAppId())) {
            log.error("payCb app_id error receive:{}, real:{}", applicationId, harmonyConfig.getAppId());
            return null;
        }

        String productId = purchaseData.getString("productId");
        String purchaseToken = purchaseData.getString("purchaseToken");

        String accessToken = this.getAccessToken();

        Map<String, String> headers = this.buildAuthorization(accessToken);

        Map<String, String> bodyMap = Maps.newHashMap();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String url = harmonyConfig.getCheckUrl();
        String response = OkHttpClientUtil.postJson(url, headers, bodyMap, String.class);

        JSONObject respObj = JSON.parseObject(response);
        int responseCode = Integer.parseInt(respObj.getString("responseCode"));
        if (responseCode != 0) {
            log.error("payCb check failed response:{}", response);
            return null;
        }

        boolean signRet = this.checkSign(respObj.getString("purchaseTokenData"), respObj.getString("dataSignature"), channelConfig.getHarmonyConfig().getPayPublicKey(), respObj.getString("signatureAlgorithm"));
        if (!signRet) {
            log.error("payCb check sign failed response:{}", response);
            return null;
        }

        JSONObject purchaseTokenData = respObj.getJSONObject("purchaseTokenData");
        int purchaseState = purchaseTokenData.getIntValue("purchaseState");
        if (purchaseState != 0) {
            log.error("payCb purchaseState error value:{}", purchaseState);
            return null;
        }

        int price = purchaseTokenData.getIntValue("price");

        HarmonyPurchaseData data = new HarmonyPurchaseData();
        data.setOrderId(purchaseTokenData.getString("orderId"));
        data.setProductId(purchaseTokenData.getString("productId"));
        data.setSandBox(purchaseTokenData.containsKey("purchaseType"));
        data.setPrice(price);
        return data;
    }

    /**
     * 确认购买
     */
    public void confirmPurchase(JSONObject purchaseData) {
        HarmonyConfig harmonyConfig = channelConfig.getHarmonyConfig();
        String productId = purchaseData.getString("productId");
        String purchaseToken = purchaseData.getString("purchaseToken");
        String accessToken = this.getAccessToken();

        Map<String, String> headers = this.buildAuthorization(accessToken);
        Map<String, String> bodyMap = Maps.newHashMap();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String url = harmonyConfig.getConfirmUrl();

        String response = OkHttpClientUtil.postJson(url, headers, bodyMap, String.class);
        if (StringUtils.isEmpty(response)) {
            log.error("payCb confirm failed response:{}", response);
            return;
        }
        JSONObject respObj = JSON.parseObject(response);
        int responseCode = Integer.parseInt(respObj.getString("responseCode"));
        if (responseCode != 0) {
            log.error("payCb confirm failed response:{}", response);
        }
    }

    private Map<String, String> buildAuthorization(String accessToken) {
        String oriString = MessageFormat.format("APPAT:{0}", accessToken);
        String authorization =
                MessageFormat.format("Basic {0}", Base64.encodeBase64String(oriString.getBytes(StandardCharsets.UTF_8)));
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        return headers;
    }

    @Override
    public Object payCb(FullHttpRequest request) {
        return null;
    }


    /**
     * 从华为api获取access_token
     */
    private String reqAccessToken() {
        try {
            HarmonyConfig harmonyConfig = channelConfig.getHarmonyConfig();
            String grantType = "client_credentials";

            Map<String, String> params = new HashMap<>();
            params.put("grant_type", grantType);
            params.put("client_secret", URLEncoder.encode(harmonyConfig.getAppSecret(), "UTF-8"));
            params.put("client_id", harmonyConfig.getClientId());

            String response = OkHttpClientUtil.postForm(harmonyConfig.getTokenUrl(), params, String.class);
            JSONObject respObj = JSONObject.parseObject(response);
            String token = respObj.getString("access_token");
            int expiresIn = respObj.getIntValue("expires_in");

            // redis 保存token
            redisService.set(REDIS_TOKEN_KEY, token);
            // 过期时间 - 60秒
            redisService.expireKey(REDIS_TOKEN_KEY, expiresIn - 60, TimeUnit.SECONDS);

            return token;
        } catch (Exception e) {
            log.error("payCb getAccessToken failed, e:", e);
            return null;
        }
    }

    /**
     * 查询token
     * @returnf
     */
    public String getAccessToken() {
        if (!channelConfig.getHarmonyConfig().isReqToken()) {
            return null;
        }
        String accessToken = redisService.get(REDIS_TOKEN_KEY);
        if (StringUtils.isEmpty(accessToken)) {
            int loopCnt = 0;
            do {
                accessToken = this.reqAccessToken();
                loopCnt++;
            } while (loopCnt != 3 && StringUtils.isEmpty(accessToken));
        }
        return accessToken;
    }

    private String getUserAccessToken(String authorizationCode) {
        try {
            HarmonyConfig harmonyConfig = channelConfig.getHarmonyConfig();

            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "authorization_code");
            params.put("client_secret", URLEncoder.encode(harmonyConfig.getAppSecret(), "UTF-8"));
            params.put("client_id", harmonyConfig.getClientId());
            params.put("code", authorizationCode);

            String response = OkHttpClientUtil.postForm(harmonyConfig.getTokenUrl(), params, String.class);
            if (StringUtils.isEmpty(response)) {
                log.error("getUserAccessToken failed, authorizationCode:{}, httpCode:{}, httpBody:{}",
                        authorizationCode, ResponseContext.getOkHttpStatusCode(), ResponseContext.getOkHttpBody());
                return null;
            }
            JSONObject respObj = JSONObject.parseObject(response);

            return respObj.getString("access_token");
        } catch (Exception e) {
            log.error("getUserAccessToken failed, e:", e);
            return null;
        }
    }

    public static boolean checkSign(String content, String sign, String publicKey, String signatureAlgorithm) {
        if (sign == null) {
            return false;
        }
        if (publicKey == null) {
            return false;
        }

        // 当signatureAlgorithm为空时使用默认签名算法
        if (StringUtils.isEmpty(signatureAlgorithm)) {
            signatureAlgorithm = "SHA256WithRSA";
        }
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            // 生成"RSA"的KeyFactory对象
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] decodedKey = Base64.decodeBase64String(publicKey);
            // 生成公钥
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(decodedKey));
            java.security.Signature signature = null;
            // 根据SHA256WithRSA算法获取签名对象实例
            signature = java.security.Signature.getInstance(signatureAlgorithm);
            // 初始化验证签名的公钥
            signature.initVerify(pubKey);
            // 把原始报文更新到签名对象中
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            // 将sign解码
            byte[] bsign = Base64.decodeBase64String(sign);
            // 进行验签
            return signature.verify(bsign);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}

package com.dxx.game.modules.guild.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.context.ResponseContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.guild.GuildTaskEntity;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.modules.common.service.RandomService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.guild.consts.GuildTaskType;
import com.dxx.game.modules.guild.model.GuildTaskProcess;
import com.dxx.game.modules.guild.service.GuildTaskService;
import com.dxx.game.modules.guild.support.GuildFeaturesSupport;
import com.dxx.game.modules.guild.support.GuildSupport;
import com.dxx.game.modules.reward.model.ResourceReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.RewardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.dxx.game.dto.GuildProto.*;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
@Slf4j
@Service
public class GuildTaskServiceImpl implements GuildTaskService {

    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private RandomService randomService;
    @Resource
    private GuildUserDao guildUserDao;
    @Resource
    private RewardService rewardService;
    @Resource
    private GuildFeaturesSupport guildFeaturesSupport;
    @Resource
    private GuildSupport guildSupport;

    @DynamoDBTransactional
    @Override
    public Result<GuildTaskRewardResponse> taskRewardAction(GuildTaskRewardRequest params) {
        long userId = RequestContext.getUserId();
        int taskId = params.getTaskId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        if (!guildUser.getTasks().containsKey(taskId)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }
        GuildUser.GuildTaskModel taskModel = guildUser.getTasks().get(taskId);
        if (taskModel.getState() == 1) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }

        GuildTaskEntity guildTaskEntity = gameConfigManager.getGuildConfig().getGuildTaskEntity(taskId);

        List<List<Integer>> rewards = new ArrayList<>();
        rewards.addAll(guildTaskEntity.getReward());
        rewards.addAll(guildTaskEntity.getOtherReward());
        // 发奖励
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        taskModel.setState(1);

        GuildTaskRewardResponse.Builder response = GuildTaskRewardResponse.newBuilder();
        int nextTaskId = guildTaskEntity.getChild();
        // 判断是否有下一级任务
        if (nextTaskId > 0) {
            taskModel.setState(0);
            guildUser.getTasks().remove(taskId);
            guildUser.getTasks().put(nextTaskId, taskModel);
            response.setDeleteTaskDtoId(taskId);

            taskId = nextTaskId;
        }

        guildUserDao.updateGuildTask(guildUser);

        List<GuildTaskDto> taskDtos = this.getGuildTaskListAfterOperation(guildUser);
        if (taskDtos != null) {
            response.addAllTasks(taskDtos);
        }

        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setUpdateTaskDto(this.buildTaskDto(taskModel, taskId));
        response.setGuildUpdateInfo(guildFeaturesSupport.buildGuildUpdateInfo(guildUser.getGuildId()));
        response.setUserDailyActive(guildUser.getDailyActive());
        response.setUserWeeklyActive(guildUser.getWeeklyActive());

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<GuildTaskRefreshResponse> refreshTaskAction(GuildTaskRefreshRequest params) {
        long userId = RequestContext.getUserId();
        int taskId = params.getTaskId();
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        int guildUserStatus = guildSupport.guildUserStatus(guildUser);
        if (guildUserStatus != ErrorCode.SUCCESS) {
            return Result.Error(guildUserStatus);
        }
        if (!guildUser.getTasks().containsKey(taskId)) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int freeCnt = this.getRefreshTaskFreeCount(guildUser);
        RewardResultSet costResultSet = null;
        if (freeCnt == 0) {
            // 消耗钻石
            int costDiamonds = this.getRefreshTaskCost(guildUser);
            costResultSet = rewardService.executeReward(userId, ResourceReward.valueOf(RewardResourceType.DIAMONDS, -costDiamonds));
            if (costResultSet.isFailed()) {
                return Result.Error(costResultSet.getResultCode());
            }
        }
        guildUser.setTaskRefreshCount(guildUser.getTaskRefreshCount() + 1);
        guildUserDao.updateGuildTaskRefreshCount(guildUser);

        List<Integer> taskIds = this.generateRandomTasks(guildUser, 1, false);
        if (taskIds == null || taskIds.isEmpty()) {
            log.error("refreshNewTaskError, taskIds is empty, userId:{}, taskIds:{}", userId, taskIds);
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 删除老任务
        guildUser.getTasks().remove(taskId);
        guildUserDao.updateGuildTask(guildUser);

        int newTaskId = taskIds.get(0);

        GuildTaskRefreshResponse.Builder response = GuildTaskRefreshResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costResultSet));
        response.setGuildTask(this.buildTaskDto(guildUser.getTasks().get(newTaskId), newTaskId));
        response.setTaskRefreshCount(this.getRefreshTaskFreeCount(guildUser));
        response.setTaskRefreshCost(this.getRefreshTaskCost(guildUser));
        return Result.Success(response.build());
    }

    @Override
    public void refreshTask(Guild guild, GuildUser guildUser) {
        if (guildUser.getDailyTM() != null && guildUser.getDailyTM() >= DateUtils.getUnixTime()) {
            return;
        }
        int taskCount = gameConfigManager.getGuildConfig().getGuildLevelEntity(guild.getGuildLevel()).getTaskCount();
        this.generateRandomTasks(guildUser, 1, true);
    }

    @Override
    public void updateTask(long userId, GuildTaskProcess... processes) {
        GuildUser guildUser = guildUserDao.getByUserId(userId);
        if (guildUser == null) {
            return;
        }

        // 将map的key 转成任务类型
        Map<Integer, List<Integer>> taskIdMap = new HashMap<>();

        for (Map.Entry<Integer, GuildUser.GuildTaskModel> entry : guildUser.getTasks().entrySet()) {
            GuildTaskEntity taskEntity = gameConfigManager.getGuildConfig().getGuildTaskEntity(entry.getKey());
            if (!taskIdMap.containsKey(taskEntity.getType())) {
                taskIdMap.put(taskEntity.getType(), new ArrayList<>());
            }
            taskIdMap.get(taskEntity.getType()).add(entry.getKey());
        }

        List<Integer> updateTaskIds = new ArrayList<>();
        for (GuildTaskProcess process : processes) {
            int type = process.getTaskType();
            int count = process.getCount();
            if (taskIdMap.containsKey(type)) {
                for (int i = 0; i < taskIdMap.get(type).size(); i ++) {
                    int taskId = taskIdMap.get(type).get(i);
                    GuildUser.GuildTaskModel taskModel = guildUser.getTasks().get(taskId);
                    GuildTaskEntity taskEntity = gameConfigManager.getGuildConfig().getGuildTaskEntity(taskId);
                    int accumulationType = taskEntity.getAccumulationType();
                    int totalCount = taskModel.getProgress() + count;

                    if (accumulationType == 0) {    // 非累加
                        if (count <= taskModel.getProgress()) {
                            continue;
                        }
                        totalCount = count;
                    }

                    taskModel.setProgress(totalCount);
                    guildUserDao.updateGuildTask(guildUser);

                    // 完成任务
                    if (totalCount >= taskEntity.getNeed() && taskModel.getState() == 0) {
                        ResponseContext.addFillCommon("guildTaskRedPoint", builder -> builder.setGuildTaskRedPoint(true));
                    }

                    updateTaskIds.add(taskId);
                }
            }
        }
        if (!updateTaskIds.isEmpty()
                && (RequestContext.getCommand() == MsgReqCommand.GuildTaskRewardRequest
                || RequestContext.getCommand() == MsgReqCommand.GuildSignInRequest
                || RequestContext.getCommand() == MsgReqCommand.GuildShopBuyRequest
                || RequestContext.getCommand() == MsgReqCommand.GuildBossStartRequest)) {
            ResponseContext.setGuildTaskIds(updateTaskIds);
        }
    }

    @Override
    public GuildTaskDto buildTaskDto(GuildUser.GuildTaskModel taskModel, int taskId) {
        GuildTaskEntity taskEntity = gameConfigManager.getGuildConfig().getGuildTaskEntity(taskId);
        GuildTaskDto.Builder result = GuildTaskDto.newBuilder();
        result.setTaskId(taskId);
        result.setProgress(Math.min(taskModel.getProgress(), taskEntity.getNeed()));
        result.setNeed(taskEntity.getNeed());
        result.addAllRewards(CommonHelper.buildRewardDtoList(taskEntity.getReward()));
        result.setIsFinish(taskModel.getProgress() >= taskEntity.getNeed());
        result.setIsReceive(taskModel.getState() == 1);
        result.setLanguageId(taskEntity.getLanguageId());
        return result.build();
    }

    @Override
    public List<GuildTaskDto> buildTaskDtoList(GuildUser guildUser) {
        List<GuildTaskDto> taskDtos = new ArrayList<>();

        guildUser.getTasks().entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    taskDtos.add(this.buildTaskDto(entry.getValue(), entry.getKey()));
                });
        return taskDtos;
    }


    @Override
    public List<GuildTaskDto> getGuildTaskListAfterOperation(GuildUser guildUser) {
        List<GuildTaskDto> result = null;
        List<Integer> taskIds = ResponseContext.getGuildTaskIds();
        if (taskIds != null) {
            result = new ArrayList<>();
            for (Integer tId : taskIds) {
                result.add(this.buildTaskDto(guildUser.getTasks().get(tId), tId));
            }
        }
        return result;
    }

    @Override
    public void checkNewTask(Guild guild, GuildUser guildUser) {
        int maxTaskCount = gameConfigManager.getGuildConfig().getGuildLevelEntity(guild.getGuildLevel()).getTaskCount();
        int nowTaskCount = guildUser.getTasks().size();
        int taskCount = maxTaskCount - nowTaskCount;
        this.generateRandomTasks(guildUser, taskCount, false);
    }

    /**
     * 随机任务
     */
    private List<Integer> generateRandomTasks(GuildUser guildUser, int taskCount, boolean isInit) {
        if (taskCount == 0) {
            return null;
        }
        List<Integer> taskIds = new ArrayList<>();
        Map<Integer, GuildUser.GuildTaskModel> guildTaskModelMap = guildUser.getTasks();
        if (isInit) {
            // 初始化任务数据
            guildTaskModelMap = new HashMap<>();
            guildUser.setTaskRefreshCount(0);
            guildUserDao.updateGuildTaskRefreshCount(guildUser);
        }

        long userId = guildUser.getUserId();
        UserExtend userExtend = userExtendDao.getByUserId(userId);
        int chapterId = userExtend.getChapterId();
        List<List<Integer>> randomConfig = new ArrayList<>();
        for (Map.Entry<Integer, GuildTaskEntity> entry : gameConfigManager.getGuildConfig().getGuildTask().entrySet()) {
            if (entry.getValue().getIsInit() == 1
                    && chapterId >= entry.getValue().getCondition().get(0)
                    && entry.getValue().getCondition().get(1) >= chapterId
                    && !guildTaskModelMap.containsKey(entry.getValue().getID())) {
                if (!isInit && entry.getValue().getType() == GuildTaskType.LOGIN) {
                    // 刷新任务排除登录任务
                    continue;
                }
                List<Integer> config = new ArrayList<>();
                config.add(entry.getValue().getID());
                config.add(entry.getValue().getWeight());
                randomConfig.add(config);
            }
        }
        for (int i = 0; i < taskCount; i ++) {
            int idx = randomService.randConfigIdxFromNestedList(randomConfig);
            List<Integer> config = randomConfig.remove(idx);
            int taskId = config.get(0);

            GuildTaskEntity guildTaskEntity = gameConfigManager.getGuildConfig().getGuildTaskEntity(taskId);
            GuildUser.GuildTaskModel taskModel = new GuildUser.GuildTaskModel();
            if (guildTaskEntity.getType() == GuildTaskType.LOGIN) {
                taskModel.setProgress(1);
            } else {
                taskModel.setProgress(0);
            }
            taskModel.setState(0);
            guildTaskModelMap.put(taskId, taskModel);
            taskIds.add(taskId);
        }
        guildUser.setTasks(guildTaskModelMap);
        guildUserDao.updateGuildTask(guildUser);
        return taskIds;
    }

    @Override
    public int getRefreshTaskFreeCount(GuildUser guildUser) {
        int count = guildUser.getTaskRefreshCount();
        int maxCount = gameConfigManager.getGuildConfig().getGuildConstEntity(130).getTypeInt();
        return Math.max(0, maxCount - count);
    }

    @Override
    public int getRefreshTaskCost(GuildUser guildUser) {
        int count = guildUser.getTaskRefreshCount();
        int maxCount = gameConfigManager.getGuildConfig().getGuildConstEntity(130).getTypeInt();
        if (maxCount > count) {
            return 0;
        }
        int key = count - maxCount;
        List<Integer> cost = gameConfigManager.getGuildConfig().getGuildConstEntity(131).getTypeIntArray();
        if (key >= cost.size()) {
            key = cost.size() - 1;
        }
        return cost.get(key);
    }
}

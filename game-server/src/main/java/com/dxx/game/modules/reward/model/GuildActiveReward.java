package com.dxx.game.modules.reward.model;

import com.dxx.game.consts.RewardResourceType;
import com.dxx.game.consts.RewardType;

/**
 * @authoer: lsc
 * @createDate: 2023/4/11
 * @description:
 */
public class GuildActiveReward implements Reward {

    /**
     * 奖励类型   {@link RewardType}
     */
    private RewardType type = RewardType.GUILD_ACTIVE;

    /**
     * 奖励子类型   {@link RewardResourceType}
     */
    private RewardResourceType resourceType = RewardResourceType.NONE;

    /**
     * 数量
     */
    private int count = 0;

    /**
     * 配置表ID
     */
    private int configId = 0;

    // 公会总活跃度
    private int guildActive = 0;
    // 用户总活跃度
    private int userActive = 0;

    public GuildActiveReward() {

    }

    public GuildActiveReward(int configId, int count) {
        this.configId = configId;
        this.count = count;
    }

    public static GuildActiveReward valueOf(int configId, int count) {
        return new GuildActiveReward(configId, count);
    }


    @Override
    public RewardType getType() {
        return this.type;
    }

    @Override
    public RewardResourceType getResourceType() {
        return this.resourceType;
    }

    @Override
    public int getCount() {
        return this.count;
    }

    @Override
    public Reward increase(int incrCount) {
        this.count += incrCount;
        return this;
    }

    @Override
    public int getConfigId() {
        return this.configId;
    }

    @Override
    public Reward union(Reward reward) {
        if (match(reward)) {
            this.count += reward.getCount();
        }
        return this;
    }

    @Override
    public boolean match(Reward reward) {
        if (!(reward instanceof ItemReward)) {
            return false;
        }
        return this.configId == reward.getConfigId();
    }

    public void setGuildActive(int active) {
        this.guildActive = active;
    }
    public int getGuildActive() {
        return this.guildActive;
    }

    public void setUserActive(int active) {
        this.userActive = active;
    }
    public int getUserActive() {
        return this.userActive;
    }
}

package com.dxx.game.modules.pvp.support;

import com.dxx.game.consts.GameConstant;
import com.dxx.game.dao.dynamodb.model.Equip;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.model.usermodule.UserExtend;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.equip.service.EquipService;
import com.dxx.game.modules.hero.service.HeroService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BattleUnitCache {

    @Autowired
    private BattleUnitCacheRedisDao battleRedisDao;
    @Autowired
    private EquipService equipService;
    @Autowired
    private HeroService heroService;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private CommonHelper commonHelper;

    public CommonProto.BattleUnitDto getBattleUnit(long userId, int type) {
        CommonProto.BattleUnitCacheDto cache = battleRedisDao.getData(userId);
        if(cache == null) {
            cache = creatCache(userId);
        }

        return buildBattleUnit(type, cache);
    }

    public CommonProto.BattleUnitDto buildBattleUnit(int type, CommonProto.BattleUnitCacheDto data) {
        CommonProto.BattleUnitDto.Builder builder = CommonProto.BattleUnitDto.newBuilder();

        // 过滤阵容
        if(!data.getFormationsMap().containsKey(type)) {
            type = GameConstant.FORMATION_TYPE_CHAPTER;
        }

        var formations = data.getFormationsMap().get(type).getLongArrayList();
        List<CommonProto.HeroDto> heroDtos = new ArrayList<>();
        formations.forEach(v ->{
            if (v == null || v != 0){
                data.getHeroesList().stream().filter(hero -> hero.getRowId() == v).findFirst().ifPresent(heroDtos::add);
            }else{
                heroDtos.add(CommonProto.HeroDto.getDefaultInstance());
            }
        });

        var formationBuilder = CommonProto.FormationDto.newBuilder();
        formationBuilder.addAllHeroes(heroDtos);

        data.getEquipsList().forEach(equip -> {
            if (formations.contains(equip.getHeroRowId())){
                builder.addEquips(equip);
            }
        });

        builder.setUserId(data.getUserId());
        builder.setGuildTechLv(data.getGuildTechLv());
        builder.setVipLevel(data.getVipLevel());
        builder.addAllSkinConfigIds(data.getSkinConfigIdsList());
        builder.addAllHeroBondCounts(data.getHeroBondCountsList());
        builder.addAllHeroBookScoreCounts(data.getHeroBookScoreCountsList());
        builder.putFormations(type, formationBuilder.build());

        return builder.build();
    }

    public CommonProto.BattleUnitCacheDto creatCache(long userId) {
        return creatCache(userId, null, null);
    }

    public CommonProto.BattleUnitCacheDto creatCache(long userId, List<Hero> heroes, UserExtend userExtend) {
        if(heroes == null) {
            heroes = heroService.getAllHeros(userId);
        }
        if(userExtend == null) {
            userExtend = userExtendDao.getByUserId(userId);
        }

        User user = userDao.getByUserId(userId);
        int vip = user.getVipLevel() == null ? 0: user.getVipLevel();
        List<CommonProto.EquipmentDto> equipData = new ArrayList<>();
        List<CommonProto.HeroDto> heroData = new ArrayList<>();
        List<Hero> hs = heroService.getHeroByFormations(userId, userExtend.getFormations(), heroes);

        for (Hero hero : hs) {
            if (hero == null) {
                continue;
            }
            heroData.add(CommonHelper.buildHeroDto(hero));
            for (Equip e : hero.getEquips().values()) {
                equipData.add(CommonHelper.buildEquipmentDto(e));
            }
        }

        Map<Integer, List<Long>> heroIds = userExtend.getFormations();
        Map<Integer, CommonProto.LongArray> formations = getFormationMap(heroIds);

        int guildTechLv = userExtend.getGuildTechLv();
        List<Integer> skinConfigIds = userExtend.getSkins();

        List<CommonProto.DIntInt> heroScoreCounts = new ArrayList<>();
        if (userExtend.getHeroScoreCounts() != null) {
            userExtend.getHeroScoreCounts().forEach((k, v) -> {
                CommonProto.DIntInt.Builder data = CommonProto.DIntInt.newBuilder();
                data.setKey(k);
                data.setVal(v);
                heroScoreCounts.add(data.build());
            });
        }

        List<CommonProto.DIntInt> heroBondCounts = new ArrayList<>();
        if (userExtend.getHeroBondCounts() != null) {
            userExtend.getHeroBondCounts().forEach((k, v) -> {
                CommonProto.DIntInt.Builder data = CommonProto.DIntInt.newBuilder();
                data.setKey(k);
                data.setVal(v);
                heroBondCounts.add(data.build());
            });
        }

        return battleRedisDao.resetData(userId, formations, heroData, equipData, guildTechLv, vip, skinConfigIds, heroScoreCounts, heroBondCounts);
    }


    public List<Long> getFormation(int type, UserExtend userExtend) {
        List<Long> formation = null;

        if(type == GameConstant.FORMATION_TYPE_CHAPTER) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_CHAPTER);
        } else if (type == GameConstant.FORMATION_TYPE_ARENA_A) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_ARENA_A);
        } else if (type == GameConstant.FORMATION_TYPE_ARENA_O) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_ARENA_O);
        } else if (type == GameConstant.FORMATION_TYPE_TOWER) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_TOWER);
        } else if (type == GameConstant.FORMATION_TYPE_CONQUER_A) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_CONQUER_A);
        } else if (type == GameConstant.FORMATION_TYPE_CONQUER_O) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_CONQUER_O);
        } else if (type == GameConstant.FORMATION_TYPE_GUILD_RACE) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_GUILD_RACE);
        }

        if(formation == null) {
            formation = userExtend.getFormations().get(GameConstant.FORMATION_TYPE_CHAPTER);
        }

        return formation;
    }

    public Map<Integer, CommonProto.LongArray> getFormationMap(Map<Integer, List<Long>> formationHeroIds) {
        Map<Integer, CommonProto.LongArray> formationDtos = new HashMap<>();

        formationHeroIds.forEach((k, v) -> {
            CommonProto.LongArray.Builder builder = CommonProto.LongArray.newBuilder();
            builder.addAllLongArray(v);

            formationDtos.put(k, builder.build());
        });

        return formationDtos;
    }

}

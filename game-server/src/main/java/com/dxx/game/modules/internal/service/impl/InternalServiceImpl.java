package com.dxx.game.modules.internal.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.server.model.UserToken;
import com.dxx.game.common.utils.CryptUtil;
import com.dxx.game.common.utils.JSONRespHelper;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.consts.ErrorCode;
import com.dxx.game.modules.internal.service.InternalService;
import io.netty.handler.codec.http.FullHttpRequest;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * @author: lsc
 * @createDate: 2025/3/27
 * @description:
 */
@Service
public class InternalServiceImpl implements InternalService {

    @Resource
    private GameConfigManager gameConfigManager;

    @Override
    public Object verifyAccessToken(JSONObject reqBody, FullHttpRequest fullHttpRequest) {
        if (!this.isInternal(fullHttpRequest)) {
            return null;
        }
        String accessToken = reqBody.getString("accessToken");
        String decodeStr = CryptUtil.decode(accessToken);
        if (decodeStr.isEmpty()) {
            return JSONRespHelper.error(ErrorCode.PARAMS_ERROR, "accessToken decode error");
        }
        UserToken userToken = JSONObject.parseObject(decodeStr, UserToken.class);
        if (userToken == null) {
            return JSONRespHelper.error(ErrorCode.PARAMS_ERROR, "parse accessToken to UserToken error");
        }

        return JSONRespHelper.success(userToken);
    }

    private boolean isInternal(FullHttpRequest fullHttpRequest) {
        String host = fullHttpRequest.headers().get("Host");
        if ((!gameConfigManager.isDevelop() && !gameConfigManager.isTest() && !host.startsWith("internal-"))) {
            return false;
        }
        return true;
    }
}

package com.dxx.game.modules.im.service;



import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dto.IMProto.MessageType;

import java.util.List;
import java.util.Map;

/**
 * @author: lsc
 * @createDate: 2025/3/19
 * @description:
 */
public interface IMService {

    /**
     * 获取在线状态
     * key: userId, value:时间戳(0=当前在线, >0 离线时间)
     * @param userIds
     * @return
     */
    Map<Long, Long> getOnlineList(List<Long> userIds);

    Map<Long, Boolean> batchGetUserOnline(List<Long> userIds);

    /** 加入分组 */
    boolean joinGroup(long userId, String groupId);
    boolean joinGroup(List<Long> userIds, String groupId);

    /** 离开分组 */
    boolean leaveGroup(long userId, String groupId);

    boolean leaveGroup(List<Long> userIds, String groupId);

    /** 给指定用户推送消息 */
    void batchSendPersonalMsg(MessageType messageType, List<Long> userIdList, String content);

    /** 给指定用户推送消息 */
    void sendPersonalMsg(MessageType messageType, long userId, String content);

    /** 给组推送消息 */
    void sendGroupMsg(MessageType messageType, String groupId, String content);
    void sendGroupMsg(MessageType messageType, String groupId, String content, boolean saveGroupMessage);

    /** 给公会组推送消息 */
    void sendGuildGroupMsg(MessageType messageType, long guildId, String content);
    void sendGuildGroupMsg(MessageType messageType, long guildId, String content, boolean saveGroupMessage);

    /** 给服务器组推送消息 */
    void sendServerGroupMsg(MessageType messageType, long serverId, String content);
    void sendServerGroupMsg(MessageType messageType, long serverId, String content, boolean saveGroupMessage);

    /** 给所有用户推送消息 */
    void sendMsgToAll(MessageType messageType, String content);

    /** 群组ID */
    String getUserServerGroupId(int serverId);
    String getUserGuildGroupId(long userId);
    String getUserCrossServerGroupId(int serverId);
    String getUserGlobalGroupId(int serverId);

    long createMsgId(String groupId);
}

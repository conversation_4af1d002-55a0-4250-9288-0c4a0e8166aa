
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class ActivityFlipCard_FlipBase {
    public ActivityFlipCard_FlipBase(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        StepItem = _buf.get("StepItem").getAsInt();
        ClueItem = _buf.get("ClueItem").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("InitialSeed").getAsJsonArray(); InitialSeed = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  InitialSeed.add(_v0); }   }
        AllShowItem = _buf.get("AllShowItem").getAsInt();
        StepDefault = _buf.get("StepDefault").getAsInt();
        StepPrice = _buf.get("StepPrice").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("BuyLimit").getAsJsonArray(); BuyLimit = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BuyLimit.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("FlipStones").getAsJsonArray(); FlipStones = new java.util.ArrayList<Integer>(_json0_.size()); for(JsonElement _e0 : _json0_) { int _v0;  _v0 = _e0.getAsInt();  FlipStones.add(_v0); }   }
        DynamicLimit = _buf.get("DynamicLimit").getAsFloat();
        StepGrid = _buf.get("StepGrid").getAsInt();
        { com.google.gson.JsonArray _json0_ = _buf.get("BigReward").getAsJsonArray(); BigReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  BigReward.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("MailItems").getAsJsonArray(); MailItems = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailItems.add(_v0); }   }
        { com.google.gson.JsonArray _json0_ = _buf.get("MailItemsId").getAsJsonArray(); MailItemsId = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  MailItemsId.add(_v0); }   }
    }

    public static ActivityFlipCard_FlipBase deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.ActivityFlipCard_FlipBase(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 步数道具
     */
    public final int StepItem;
    /**
     * 线索道具
     */
    public final int ClueItem;
    /**
     * 初始种子随机库
     */
    public final java.util.List<Integer> InitialSeed;
    /**
     * 主动使用的全开道具
     */
    public final int AllShowItem;
    /**
     * 初始给几个步数
     */
    public final int StepDefault;
    /**
     * 步数价格、钻石
     */
    public final int StepPrice;
    /**
     * 根据钻石余量设定单次购买步数上限<br/>最小数,最大数,上限|...
     */
    public final java.util.List<String> BuyLimit;
    /**
     * 四种石头兑换物ID
     */
    public final java.util.List<Integer> FlipStones;
    /**
     * 普通格子数动态调整的倍数上限
     */
    public final float DynamicLimit;
    /**
     * 增加步数的特殊格子，点击后增加几步
     */
    public final int StepGrid;
    /**
     * 累计线索奖励：<br/>累计线索数量，掉落表ID|
     */
    public final java.util.List<String> BigReward;
    /**
     * 活动结束时，以下这些道具兑换为金币<br/>道具ID，金币数量|
     */
    public final java.util.List<String> MailItems;
    /**
     * 道具转为金币邮件模板ID<br/>测试服|正式服
     */
    public final java.util.List<String> MailItemsId;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "StepItem:" + StepItem + ","
        + "ClueItem:" + ClueItem + ","
        + "InitialSeed:" + InitialSeed + ","
        + "AllShowItem:" + AllShowItem + ","
        + "StepDefault:" + StepDefault + ","
        + "StepPrice:" + StepPrice + ","
        + "BuyLimit:" + BuyLimit + ","
        + "FlipStones:" + FlipStones + ","
        + "DynamicLimit:" + DynamicLimit + ","
        + "StepGrid:" + StepGrid + ","
        + "BigReward:" + BigReward + ","
        + "MailItems:" + MailItems + ","
        + "MailItemsId:" + MailItemsId + ","
        + "}";
    }
}


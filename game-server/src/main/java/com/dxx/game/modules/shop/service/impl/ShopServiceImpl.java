package com.dxx.game.modules.shop.service.impl;

import com.dxx.game.common.aws.dynamodb.transaction.annotation.DynamoDBTransactional;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.RandomUtil;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.config.entity.gacha.GachaPoolEntity;
import com.dxx.game.config.entity.iap.*;
import com.dxx.game.config.entity.integralshop.GoodsEntity;
import com.dxx.game.config.entity.item.DropEntity;
import com.dxx.game.config.entity.vip.VipEntity;
import com.dxx.game.consts.*;
import com.dxx.game.dao.dynamodb.model.Shop;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.ShopDao;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.dto.ShopProto.*;
import com.dxx.game.modules.activity.model.EventTaskProcess;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;
import com.dxx.game.modules.activity.service.ActivityService;
import com.dxx.game.modules.activity.service.SevenDayTaskService;
import com.dxx.game.modules.common.service.CommonService;
import com.dxx.game.modules.common.support.CommonHelper;
import com.dxx.game.modules.mission.service.MissionService;
import com.dxx.game.modules.pay.service.PayService;
import com.dxx.game.modules.pay.support.PaySupport;
import com.dxx.game.modules.reward.model.ItemReward;
import com.dxx.game.modules.reward.model.Reward;
import com.dxx.game.modules.reward.model.ResourceReward;
import com.dxx.game.modules.reward.result.RewardResultSet;
import com.dxx.game.modules.reward.service.DropService;
import com.dxx.game.modules.reward.service.RewardService;
import com.dxx.game.modules.shop.service.ShopService;
import com.dxx.game.modules.shop.support.ShopSupport;
import com.dxx.game.modules.task.model.TaskProcess;
import com.dxx.game.modules.task.service.TaskService;
import com.dxx.game.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShopServiceImpl implements ShopService {

    @Resource
    private ShopDao shopDao;
    @Resource
    private GameConfigManager gameConfigManager;
    @Resource
    private RewardService rewardService;
    @Resource
    private DropService dropService;
    @Resource
    private CommonService commonService;
    @Resource
    private MissionService missionService;
    @Resource
    private PayService payService;
    @Resource
    private UserService userService;
    @Resource
    private UserDao userDao;
    @Resource
    private TaskService taskService;
    @Resource
    private SevenDayTaskService sevenDayTaskService;
    @Resource
    private ActivityService activityService;
    @Resource
    private UserInfoRedisDao userInfoRedisDao;
    @Resource
    private ShopSupport shopSupport;
    @Resource
    private UserExtendDao userExtendDao;
    @Resource
    private PaySupport paySupport;


    @DynamoDBTransactional
    @Override
    public Result<ShopGetInfoResponse> getInfoAction(ShopGetInfoRequest params) {
        long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        if (shop == null) {
            shop = Shop.init(userId);
            shopDao.insert(shop);
        } else {
            // 是否跨天
            if (shop.getResetTimestamp() != DateUtils.getSystemResetTime()) {
                shop.setGachaResetTimestamp(DateUtils.getSystemResetTime());
                shop.getGachaDayCountMap().clear();
                shop.getGachaDayFreeCountMap().clear();
                shopDao.update(shop);
            }
        }

        // 检测积分商店数据
        shopSupport.checkDataWhenShopGetInfo(userId);

        ShopGetInfoResponse.Builder response = ShopGetInfoResponse.newBuilder();

        // 积分商店数据
        shop.getIntegralShops().forEach((shopId, model) -> {
            response.addIntegralShops(shopSupport.buildIntegralShopDto(model));
        });

        // 抽卡
        long nowTimestamp = DateUtils.getUnixTime();
        Map<Integer, GachaPoolEntity> gachaPoolEntityMap = gameConfigManager.getGachaConfig().getGachaPool();
        for (Map.Entry<Integer, GachaPoolEntity> entry : gachaPoolEntityMap.entrySet()) {
            if (entry.getValue().getOpenType() == 1
                    && (nowTimestamp < entry.getValue().getOpenPara1() || nowTimestamp > entry.getValue().getOpenPara2())) {
                // 不在开启时间内
                continue;
            }

            long openTimestamp = entry.getValue().getOpenPara1();
            long endTimestamp = entry.getValue().getOpenPara2();

            ShopGachaDataDto.Builder builder = ShopGachaDataDto.newBuilder();
            builder.setId(entry.getValue().getId());
            builder.setOpenType(entry.getValue().getOpenType());
            builder.setOpenTimestamp(openTimestamp);
            builder.setEndTimestamp(endTimestamp);
            int gachaCount = shop.getGachaDayCountMap().getOrDefault(entry.getValue().getId(), 0);
            int gachaFreeCount = shop.getGachaDayFreeCountMap().getOrDefault(entry.getValue().getId(), 0);
            builder.setGachaCount(gachaCount);
            builder.setGachaFreeCount(gachaFreeCount);
            builder.setRefreshTimestamp(DateUtils.getSystemResetTime());
            response.addShopGachaDataDtos(builder.build());
        }

        response.putAllRechargeIds(shop.getIap().getRechargeIds());
        response.setIapInfo(shopSupport.buildUserIAPInfo());
        response.setRefreshTime(DateUtils.getSystemResetTime());

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopFreeIAPItemResponse> free(ShopFreeIAPItemRequest params) {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        int purchaseId = params.getPurchaseId();
        PurchaseEntity purchaseEntity = gameConfigManager.getIAPConfig().getPurchaseEntity(purchaseId);
        if (purchaseEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }
        IAPEntity iapEntity = gameConfigManager.getIAPConfig().getIAPEntity(purchaseEntity.getIAPID());
        if (iapEntity == null) {
            return Result.Error(ErrorCode.CONFIG_NOT_EXIST);
        }

        if (iapEntity.getPrice() != 0F) {
            return Result.Error(ErrorCode.MSG_INTERNAL_ARG_FAILED);
        }

        // 验证超出限购次数
        if (payService.isPurchaseLimitExceeded(userId, purchaseId, params.getExtraInfo())) {
            return Result.Error(ErrorCode.IAP_PURCHASE_COUNT_FULL);
        }

        ShopFreeIAPItemResponse.Builder response = ShopFreeIAPItemResponse.newBuilder();

        var rewardResult = paySupport.getPurchaseRewards(userId, purchaseId, params.getExtraInfo());
        if (!rewardResult.getLeft()) {
            log.error("rewards is null, userId:{}, purchaseId:{}", userId, purchaseId);
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        // 发奖
        if (!rewardResult.getRight().isEmpty()) {
            RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardResult.getRight());
            if (rewardResultSet.isFailed()) {
                return Result.Error(rewardResultSet.getResultCode());
            }
            response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        }


        Integer buyCount = shop.getIap().getRechargeIds().getOrDefault(purchaseId, 0);
        shop.getIap().getRechargeIds().put(purchaseId, buyCount + 1);


        shopDao.updateIap(shop);
        response.putAllRechargeIds(shop.getIap().getRechargeIds());
        response.setIapInfo(shopSupport.buildUserIAPInfo());
        return Result.Success(response.build());
    }


    @DynamoDBTransactional
    @Override
    public Result<ShopDoGachaResponse> doGachaAction(ShopDoGachaRequest params) {
        long userId = RequestContext.getUserId();
        int gachaId = params.getGachaId();
        int costType = params.getCostType();
        int gachaType = params.getGachaType();

        GachaPoolEntity gachaPoolEntity = gameConfigManager.getGachaConfig().getGachaPoolEntity(gachaId);
        if (gachaPoolEntity == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Shop shop = shopDao.getByUserId(userId);
        if (shop == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int costItemId = 0;
        int costItemCount = 0;
        int dropCount = 0;
        Reward costReward = null;
        if (costType == 0) {
            costItemId = gachaPoolEntity.getCostItemid();
            if (gachaType == 0) {
                costItemCount = gachaPoolEntity.getPrice1();
            } else if (gachaType == 1) {
                costItemCount = gachaPoolEntity.getPrice10();
            }

            costReward = ResourceReward.valueOf(RewardResourceType.DIAMONDS, -costItemCount);
        } else if (costType == 1) {
            costItemId = gachaPoolEntity.getTokenId();
            if (gachaType == 0) {
                costItemCount = 1;
            } else if (gachaType == 1) {
                costItemCount = 10;
            }
            costReward = ItemReward.valueOf(costItemId, -costItemCount);
        }

        if (gachaType == 0) {
            dropCount = 1;
        } else if (gachaType == 1) {
            dropCount = 10;
        }

        if (costReward == null) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        int drawCount = shop.getGachaCountMap().getOrDefault(gachaId, 0);
        int drawDayCount = shop.getGachaDayCountMap().getOrDefault(gachaId, 0);
        int drawDayFreeCount = shop.getGachaDayFreeCountMap().getOrDefault(gachaId, 0);

        if(gachaType == 1 || drawDayFreeCount > 0) {
            int totalCount = drawDayCount + dropCount - drawDayFreeCount;
            if(totalCount > gachaPoolEntity.getTimeLimit()) {
                return Result.Error(ErrorCode.GACHA_COUNT_MAX);
            }
        }

        RewardResultSet costRewardResultSet = null;
        // 每日免费
        if(gachaType == 1 || drawDayFreeCount >= 1) {
            // 扣除道具
            costRewardResultSet = rewardService.executeReward(userId, costReward);
            if (costRewardResultSet.isFailed()) {
                return Result.Error(costRewardResultSet.getResultCode());
            }
        }
        else {
            drawDayFreeCount = drawDayFreeCount + 1;
        }

        // 固定抽卡次数dropId
        Map<Integer, List<Integer>> beginnerRewardMap = new HashMap<>();
        for (List<Integer> beginner : gachaPoolEntity.getBeginnerReward()) {
            beginnerRewardMap.put(beginner.get(0), beginner);
        }

        List<Integer> wishData = new ArrayList<>();
        if (shop.getWishData() != null) {
            wishData = shop.getWishData().getOrDefault(gachaId, new ArrayList<>());
        }


        List<Reward> rewards = new ArrayList<>();
        for (int i = 0; i < dropCount; i++) {
            drawCount++;
            drawDayCount++;
            int idx = dropService.randIndex(gachaPoolEntity.getReward());
            int dropId = gachaPoolEntity.getReward().get(idx).get(0);
            int count = gachaPoolEntity.getReward().get(idx).get(1);

            if (beginnerRewardMap.containsKey(drawCount)) {
                dropId = beginnerRewardMap.get(drawCount).get(1);
                count = beginnerRewardMap.get(drawCount).get(2);
            }

            DropEntity dropEntity = gameConfigManager.getItemConfig().getDropEntity(dropId);
            List<List<Integer>> dropRewardConfig = CollectionUtils.copyM(dropEntity.getReward());
            for (List<Integer> dropConfig : dropRewardConfig) {
                if (wishData.contains(dropConfig.get(0))) {
                    dropConfig.set(2, (dropConfig.get(2) * (1 + gachaPoolEntity.getExtraChance() / 100)));
                }
            }
            List<List<Integer>> rewardsConfig = dropService.randItemByConfig(dropRewardConfig, count);
            rewards.addAll(rewardService.parseRewards(rewardsConfig));
        }

        // 更新次数
        shop.getGachaCountMap().put(gachaId, drawCount);
        shop.getGachaDayCountMap().put(gachaId, drawDayCount);
        shop.getGachaDayFreeCountMap().put(gachaId, drawDayFreeCount);

        boolean isHasPurple = false;

        int updateCount = shop.getGachaPurpleCountMap().getOrDefault(gachaId, 0) + dropCount;
        shop.getGachaPurpleCountMap().put(gachaId, updateCount);


        if(gachaId == GameConstant.GACHA_ID_HERO) {
            activityService.updateConsume(userId, ActivityType.CONSUME_HERO, dropCount);

            for (Reward reward : rewards) {
                int configId = reward.getConfigId();
                int quality = gameConfigManager.getItemConfig().getItemEntity(configId).getQuality();

                if (quality >= 9) {
                    isHasPurple = true;
                    shop.getGachaPurpleCountMap().put(gachaId, 0);
                    break;
                }
            }
        }

        if(gachaId == GameConstant.GACHA_ID_EQUIP) {
            activityService.updateConsume(userId, ActivityType.CONSUME_EQUIP, dropCount);

            for (Reward reward : rewards) {
                int configId = reward.getConfigId();
                int subType = gameConfigManager.getItemConfig().getItemEntity(configId).getSubType();

                // 1:项链
                if (subType == 1) {
                    isHasPurple = true;
                    shop.getGachaPurpleCountMap().put(gachaId, 0);
                    break;
                }
            }
        }

        if(gachaId == GameConstant.GACHA_ID_RELIC) {
            activityService.updateConsume(userId, ActivityType.CONSUME_RELIC, dropCount);

            for (Reward reward : rewards) {
                int configId = reward.getConfigId();
                int subType = gameConfigManager.getItemConfig().getItemEntity(configId).getSubType();

                // 2:完整藏品
                if (subType == 2) {
                    isHasPurple = true;
                    shop.getGachaPurpleCountMap().put(gachaId, 0);
                    break;
                }
            }
        }

        // 判断是否有紫色
        if (!isHasPurple) {
            int purpleCount = commonService.getGameConfigIntValue(800);
            int totalDrawCount = shop.getGachaPurpleCountMap().get(gachaId);
            if (totalDrawCount >= purpleCount) {
                // 随机一个紫色
                Reward purpleReward = rewardService.parseReward(gachaPoolEntity.getRareReward()).get(0);
                rewards.set(RandomUtil.betweenValue(0, rewards.size() - 1), purpleReward);
                shop.getGachaPurpleCountMap().put(gachaId, 0);
            }
        }


        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewards);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        shopDao.updateGachaData(shop);

        // 更新任务
        TaskProcess dailyTask = TaskProcess.valueOf(TaskType.DAILY, TaskType.DAILY_GACHA_HERO, gachaType == 0 ? 1 : 10);
        TaskProcess achieveTask = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_COUNT_GACHA_HERO, gachaType == 0 ? 1 : 10);
        taskService.updateTask(userId, dailyTask, achieveTask);

        // 新手7日活动任务
        SevenDayTaskProcess sevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_COUNT_GACHA_HERO, gachaType == 0 ? 1 : 10);
        sevenDayTaskService.updateTask(userId, sevenDayTaskProcess);

        ShopDoGachaResponse.Builder response = ShopDoGachaResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(costRewardResultSet, rewardResultSet));
        response.setGachaCount(shop.getGachaDayCountMap().get(gachaId));
        response.setGachaFreeCount(shop.getGachaDayFreeCountMap().get(gachaId));

        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopIntegralGetInfoResponse> integralGetInfoAction(ShopIntegralGetInfoRequest params) {
        long userId = RequestContext.getUserId();

        Shop shop = shopDao.getByUserId(userId);

        shopSupport.checkIntegralShop(shop);

        ShopIntegralGetInfoResponse.Builder response = ShopIntegralGetInfoResponse.newBuilder();

        shop.getIntegralShops().forEach((shopId, model) -> {
            response.addIntegralShops(shopSupport.buildIntegralShopDto(model));
        });
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopIntegralRefreshResponse> refreshIntegralAction(ShopIntegralRefreshRequest params) {
        long userId = RequestContext.getUserId();
        int shopId = params.getShopConfigId();

        Shop shop = shopDao.getByUserId(userId);
        Shop.IntegralShopModel integralShopModel = shop.getIntegralShops().get(shopId);
        if (integralShopModel == null) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_CANT_INIT);
        }
        int refreshNum = integralShopModel.getRefreshNum();
        int maxNum = integralShopModel.getMaxNum();
        if (refreshNum >= maxNum) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_REFRESH_NUM_MAX);
        }

        List<Integer> refreshCost = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getRefreshCost();
        int cost;
        if (refreshCost.size() <= refreshNum){
            cost = refreshCost.get(refreshCost.size() - 1);
        }else{
            cost = refreshCost.get(refreshNum);
        }
        List<List<Integer>> rewardConfig = new ArrayList<>();
        rewardConfig.add(new ArrayList<>(List.of(2, -cost)));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        int chapterId = userInfoRedisDao.getChapterId(shop.getUserId());

        List<Integer> list = shopSupport.getIntegralShopGoods(shopId, chapterId).stream().map(GoodsEntity::getID).collect(Collectors.toList());
        integralShopModel.setGoodsList(list);
        integralShopModel.setBuyList(new ArrayList<>());
        integralShopModel.setRefreshNum(refreshNum + 1);
        integralShopModel.setRefreshTime(DateUtils.getUnixTime());

        shopDao.updateIntegralShop(shop);

        ShopIntegralRefreshResponse.Builder response = ShopIntegralRefreshResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setIntegralShop(shopSupport.buildIntegralShopDto(integralShopModel));
        return Result.Success(response.build());
    }


    @DynamoDBTransactional
    @Override
    public Result<ShopIntegralBuyItemResponse> integralBuyItemAction(ShopIntegralBuyItemRequest params) {
        long userId = RequestContext.getUserId();
        int shopId = params.getShopConfigId();
        int goodId = params.getGoodsConfigId();
        Shop shop = shopDao.getByUserId(userId);
        shopSupport.checkIntegralShop(shop);
        Shop.IntegralShopModel integralShopModel = shop.getIntegralShops().get(shopId);
        if (integralShopModel == null) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_CANT_INIT);
        }
        int costItemId = gameConfigManager.getIntegralShopConfig().getDataEntity(shopId).getCurrencyID();

        GoodsEntity entity = gameConfigManager.getShopGoodMap().get(shopId).get(goodId);
        int price = gameConfigManager.getShopGoodMap().get(shopId).get(goodId).getPrice();
        int discount = gameConfigManager.getShopGoodMap().get(shopId).get(goodId).getDiscount();

        int rewardItemId = entity.getItems().get(0);
        int rewardCount = entity.getItems().get(1);

        if (integralShopModel.getBuyList().contains(goodId)) {
            return Result.Error(ErrorCode.INTEGRAL_SHOP_GOODS_BUY_COUNT);
        }

        int finalPrice = price * discount / 100;

        List<List<Integer>> rewardConfig = new ArrayList<>();
        rewardConfig.add(new ArrayList<>(List.of(rewardItemId, rewardCount)));
        rewardConfig.add(new ArrayList<>(List.of(costItemId, -finalPrice)));

        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        integralShopModel.getBuyList().add(goodId);
        shopDao.updateIntegralShop(shop);

        if (entity.getTypeId() == 2) {
            //任务、成就
            TaskProcess taskProcess = TaskProcess.valueOf(TaskType.DAILY, TaskType.DAILY_INTEGRAL_SHOP_BUY, 1);
            TaskProcess achieveProcess = TaskProcess.valueOf(TaskType.ACHIEVE, TaskType.ACHIEVE_TIMES_INTEGRAL_SHOP_BUY, 1);
            taskService.updateTask(userId, taskProcess, achieveProcess);

            // 新手7日活动任务
            SevenDayTaskProcess sevenDayTaskProcess = SevenDayTaskProcess.valueOf(TaskType.ACHIEVE_TIMES_INTEGRAL_SHOP_BUY, 1);
            sevenDayTaskService.updateTask(userId, sevenDayTaskProcess);

            activityService.updateTask(userId, EventTaskProcess.valueOf(TaskType.EVENT_PROGRESS_INTEGRAL_BUY, 1));
        }

        ShopIntegralBuyItemResponse.Builder response = ShopIntegralBuyItemResponse.newBuilder();
        response.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        response.setShopConfigId(shopId);
        response.setGoodsConfigId(goodId);
        return Result.Success(response.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<ShopGacheWishResponse> gachaWishAction(ShopGacheWishRequest params) {
        return null;
    }


    @DynamoDBTransactional
    @Override
    public Result<MonthCardGetRewardResponse> monthCardGetReward(MonthCardGetRewardRequest params) {
        int monthCardId = params.getMonthCardId();
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        IAPConfig iapConfig = gameConfigManager.getIAPConfig();
        MonthCardEntity monthCardEntity = iapConfig.getMonthCard().get(monthCardId);
        Map<Integer, Shop.IAPMonthCardModel> monthCardMap = iap.getMonthCardMap();
        if (!monthCardMap.containsKey(monthCardId)) {
            return Result.Error(ErrorCode.IAP_MONTHCARD_NOT_BUY);
        }
        Shop.IAPMonthCardModel iapMonthCardModel = monthCardMap.get(monthCardId);
        if (iapMonthCardModel.getLastCount() <= 0) {
            return Result.Error(ErrorCode.IAP_MONTHCARD_NOT_BUY);
        }
        long now = DateUtils.getUnixTime();
        if (DateUtils.isSameDay(now, iapMonthCardModel.getRealLastRewardTime())) {
            return Result.Error(ErrorCode.IAP_MONTHCARD_REWARD_TODAY);
        }

        shopSupport.checkMonthCard();

        // 先记录领取次数
        int lastCount = iapMonthCardModel.getLastCount();
        iapMonthCardModel.setLastCount(lastCount - 1);
        iapMonthCardModel.setLastRewardTime(now);
        iapMonthCardModel.setRealLastRewardTime(now);
        shopDao.updateIap(shop);

        int afterLastCount = iapMonthCardModel.getLastCount();
        int alarmClock = monthCardEntity.getAlarmClock();
        if (afterLastCount <= alarmClock) {
            // TODO 发送邮件通知玩家续费?
            String postID = gameConfigManager.getMonthMailTempId(monthCardEntity);

//            mailService.createMail(userId, postID, new HashMap<>(), new ArrayList<>());
        }


        // 后发放奖励
        List<List<Integer>> rewardConfig = monthCardEntity.getProductsPerDay();
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        MonthCardGetRewardResponse.Builder builder = MonthCardGetRewardResponse.newBuilder();
        builder.setCommonData(commonData);
        builder.setIapInfo(shopSupport.buildUserIAPInfo());
        return Result.Success(builder.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<VIPLevelRewardResponse> vipLevelReward(VIPLevelRewardRequest params) {
        Long userId = RequestContext.getUserId();
        User user = userService.getUser(userId);

        int level = params.getLevel();

        if (level > user.getLevel()) {
            return Result.Error(ErrorCode.PARAMS_ERROR);
        }

        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();

        if (iap.getRewardVipLevelSet().contains(level)) {
            return Result.Error(ErrorCode.REWARD_RECEIVED);
        }


        VipConfig vipConfig = gameConfigManager.getVipConfig();

        List<List<Integer>> rewardAll = new ArrayList<>();

        VipEntity vipEntity = vipConfig.getVipEntity(level);

        List<List<Integer>> costList = new ArrayList<>();
        List<List<Integer>> costConfig = vipEntity.getPrice();
        for (List<Integer> tmp : costConfig) {
            List<Integer> cost = new ArrayList<>();
            cost.add(tmp.get(0));
            cost.add(-tmp.get(1));
            costList.add(cost);
        }

        List<List<Integer>> unlockReward = vipEntity.getUnlockReward();
        rewardAll.addAll(costList);
        rewardAll.addAll(unlockReward);

        // 更新领取记录
        iap.getRewardVipLevelSet().add(level);
        shopDao.updateIap(shop);

        // 发放奖励
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardAll);
        if (rewardResultSet.isFailed()) {
            return Result.Error(rewardResultSet.getResultCode());
        }

        CommonProto.CommonData commonData = CommonHelper.buildCommonData(rewardResultSet);
        CommonProto.CommonData.Builder commonDataBuilder = commonData.toBuilder();
        CommonProto.UpdateUserVipLevel.Builder updateVipLevelBuilder = CommonProto.UpdateUserVipLevel.newBuilder();
        CommonProto.UserVipLevel userVipLevel = CommonHelper.buildUserVipLevelMsg(user);
        updateVipLevelBuilder.setIsChange(true);
        updateVipLevelBuilder.setUserVipLevel(userVipLevel);
        commonDataBuilder.setUpdateUserVipLevel(updateVipLevelBuilder);
        VIPLevelRewardResponse.Builder builder = VIPLevelRewardResponse.newBuilder();
        builder.setCommonData(commonDataBuilder);
        return Result.Success(builder.build());
    }

    @DynamoDBTransactional
    @Override
    public Result<FirstRechargeRewardResponse> firstRechargeReward(FirstRechargeRewardRequest params) {
        Long userId = RequestContext.getUserId();
        Shop shop = shopDao.getByUserId(userId);
        Shop.IAPModel iap = shop.getIap();
        if (iap.getTotalRecharge() <= 0) {
            return Result.Error(ErrorCode.FIRSTREWARD_NOT_RECHARGE);
        }
        if (iap.getFirstRechargeReward() == 1) {
            return Result.Error(ErrorCode.FIRSTREWARD_REWARDED);
        }
        iap.setFirstRechargeReward(1);
        shopDao.updateIap(shop);
        PushPacksEntity pushPacksEntity = gameConfigManager.getIAPConfig().getPushPacksEntity(601);
        List<List<Integer>> rewardConfig = pushPacksEntity.getProducts();
        RewardResultSet rewardResultSet = rewardService.executeRewards(userId, rewardConfig);
        FirstRechargeRewardResponse.Builder builder = FirstRechargeRewardResponse.newBuilder();
        builder.setCommonData(CommonHelper.buildCommonData(rewardResultSet));
        builder.setTotalRecharge(iap.getTotalRecharge());
        builder.setFirstRechargeReward(iap.getFirstRechargeReward() == 1);
        return Result.Success(builder.build());
    }


    @Override
    public void initWealCard(Shop shop) {
        IAPConfig iapConfig = gameConfigManager.getIAPConfig();
        int configId = 303;

        MonthCardEntity monthCardEntity = iapConfig.getMonthCardEntity(configId);
        Shop.IAPModel iap = shop.getIap();
        Map<Integer, Shop.IAPMonthCardModel> monthCardMap = iap.getMonthCardMap();
        if (monthCardMap.containsKey(configId)) {
            return;
        }

        Shop.IAPMonthCardModel iapMonthCardModel = new Shop.IAPMonthCardModel();
        iapMonthCardModel.setId(configId);

        int lastCount = iapMonthCardModel.getLastCount();
        if (monthCardEntity.getDuration() == 0) {
            lastCount = Integer.MAX_VALUE - 1;
        }
        iapMonthCardModel.setLastCount(lastCount + monthCardEntity.getDuration());

        monthCardMap.put(configId, iapMonthCardModel);
        shopDao.updateIap(shop);
    }
}














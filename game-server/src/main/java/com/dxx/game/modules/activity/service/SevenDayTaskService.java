package com.dxx.game.modules.activity.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dto.SevenDayTaskProto.*;
import com.dxx.game.modules.activity.model.SevenDayTaskProcess;

/**
 * <AUTHOR>
 * @date 2022/5/17 5:33 下午
 */
public interface SevenDayTaskService {

    /**
     * 获取数据
     *
     * @param params
     * @return
     */
    Result<SevenDayTaskGetInfoResponse> getInfoAction(SevenDayTaskGetInfoRequest params);

    /**
     * 领取任务奖励
     *
     * @param params
     * @return
     */
    Result<SevenDayTaskRewardResponse> taskRewardAction(SevenDayTaskRewardRequest params);

    /**
     * 领取活跃度奖励
     *
     * @param params
     * @return
     */
    Result<SevenDayTaskActiveRewardResponse> activeRewardAction(SevenDayTaskActiveRewardRequest params);

    /**
     * 获取数据
     *
     * @param userId
     * @return
     */
    SevenDayDto getInfo(long userId);

    /**
     * 更新任务进度
     *
     * @param userId
     * @param processes
     */
    void updateTask(long userId, SevenDayTaskProcess... processes);

    /**
     * 更新数据
     *
     * @param userId
     */
    void resetCheckDayTM(long userId, long days);

    /**
     * 更新数据
     *
     * @param userId
     * @param active
     */
    void updateActive(long userId, int active);

    /**
     * 更新数据
     *
     * @param userId
     * @param taskType
     * @param process
     */
    void quickFinishSevenDayTask(long userId, int taskType, int process);
}

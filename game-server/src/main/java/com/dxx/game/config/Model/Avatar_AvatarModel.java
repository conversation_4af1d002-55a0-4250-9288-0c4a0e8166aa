
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Model;

import com.google.gson.JsonElement;


public final class Avatar_AvatarModel {
    private final java.util.HashMap<Integer, com.dxx.game.config.Bean.Avatar_Avatar> _dataMap;
    private final java.util.List<com.dxx.game.config.Bean.Avatar_Avatar> _dataList;
    
    public Avatar_AvatarModel(JsonElement _buf) {
        _dataMap = new java.util.HashMap<Integer, com.dxx.game.config.Bean.Avatar_Avatar>();
        _dataList = new java.util.ArrayList<com.dxx.game.config.Bean.Avatar_Avatar>();
        
        for (com.google.gson.JsonElement _e_ : _buf.getAsJsonArray()) {
            com.dxx.game.config.Bean.Avatar_Avatar _v;
            _v = com.dxx.game.config.Bean.Avatar_Avatar.deserialize(_e_.getAsJsonObject());
            _dataList.add(_v);
            _dataMap.put(_v.id, _v);
        }
    }

    public java.util.HashMap<Integer, com.dxx.game.config.Bean.Avatar_Avatar> getDataMap() { return _dataMap; }
    public java.util.List<com.dxx.game.config.Bean.Avatar_Avatar> getDataList() { return _dataList; }

    public com.dxx.game.config.Bean.Avatar_Avatar get(int key) { return _dataMap.get(key); }

}

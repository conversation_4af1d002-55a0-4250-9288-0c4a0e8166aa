package com.dxx.game.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.MessageDigest;

/**
 * 加密解密工具
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class CryptUtil {

	private static String KEY_STR = "2O@0)x!#g*o(r)i@l#l^a1d&ple88m7n";

	private static final String AESTYPE = "AES/ECB/PKCS5Padding";

	private static SecretKeySpec keySpec = null;

	private static final String CLIENT_KEY_STR = "32b7b986dcf6c2fb";
	private static SecretKeySpec clientKeySpec = null;
	private static final String HMAC_SHA256 = "HmacSHA256";

	@Value("${spring.application.name}")
	private String appName;

	@Value("${game.country}")
	private String country;

	@PostConstruct
	private void init() {
		String replaceName = appName + country;
		CryptUtil.KEY_STR = KEY_STR.substring(0, KEY_STR.length() - replaceName.length()) + replaceName;
		CryptUtil.keySpec = new SecretKeySpec(CryptUtil.KEY_STR.getBytes(), "AES");
		CryptUtil.clientKeySpec = new SecretKeySpec(CryptUtil.CLIENT_KEY_STR.getBytes(), "AES");
	}

	/** 加密 **/
	public static String encode(String text, SecretKeySpec keySpec) {
		byte[] encrypt = null;
		try {
			Key key = keySpec;
			Cipher cipher = Cipher.getInstance(AESTYPE);
			cipher.init(Cipher.ENCRYPT_MODE, key);
			encrypt = cipher.doFinal(text.getBytes());
		} catch (Exception e) {
			log.error("encode failed:{}", e.getMessage());
			return "";
		}
		return new String(Base64.encodeBase64(encrypt));
	}

	/** 解密 **/
	public static String decode(String text, SecretKeySpec keySpec) {
		byte[] decrypt = null;
		try {
			Key key = keySpec;
			Cipher cipher = Cipher.getInstance(AESTYPE);
			cipher.init(Cipher.DECRYPT_MODE, key);
			decrypt = cipher.doFinal(Base64.decodeBase64(text));
		} catch (Exception e) {
			log.error("decode failed:{}", e.getMessage());
			return "";
		}
		return new String(decrypt).trim();
	}

	/**加密*/
	public static String encode(String text) {
		return encode(text, CryptUtil.keySpec);
	}

	/**解密*/
	public static String decode(String text) {
		return decode(text, CryptUtil.keySpec);
	}

	public static String encodeBattleData(String text) {
		return encode(text, CryptUtil.clientKeySpec);
	}
	public static String decodeBattleData(String text) {
		return decode(text, CryptUtil.clientKeySpec);
	}


	/**
	 * MD5 摘要计算(byte[]).
	 *
	 * @param src
	 *            byte[]
	 * @throws Exception
	 * @return byte[] 16 bit digest
	 */
	public static byte[] md5(byte[] src) throws Exception {
		MessageDigest alg = MessageDigest.getInstance("MD5");
		return alg.digest(src);
	}

	/**
	 * MD5 摘要计算(String).
	 *
	 * @param src
	 *            String
	 * @throws Exception
	 * @return String
	 */
	public static String md5(String src) {
		try {
			return byte2hex(md5(src.getBytes(StandardCharsets.UTF_8)));
		} catch (Exception e) {
			log.error("md5 e:", e);
			return "";
		}
	}

	/**
	 * 二行制转字符串
	 *
	 * @param b
	 * @return
	 */
	public static String byte2hex(byte[] b) {
		StringBuilder sb = new StringBuilder();
		String stmp = "";
		for (int n = 0; b != null && n < b.length; n++) {
			stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
			if (stmp.length() == 1)
				sb.append("0").append(stmp);
			else
				sb.append(stmp);
		}
		return sb.toString().toUpperCase();
	}

	/** 通过Hmac_Sha算法加密 */
	public static byte[] hmac_sha(String type, String key, String stringSignTemp) {
		try {
			Mac sha256_HMAC = Mac.getInstance(type);
			SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(), type);
			sha256_HMAC.init(secret_key);
			byte[] bytes = sha256_HMAC.doFinal(stringSignTemp.getBytes());
			return bytes;
		} catch (Exception e) {
			log.error("通过Hmac_Sha算法加密发生错误，type={}，e.getMessage()={}", type, e.getMessage());
		}
		return null;
	}

	private static Key generateKey(String key) throws Exception {
		try {
			SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
			return keySpec;
		} catch (Exception e) {
			log.error("generateKey e:", e);
			throw e;
		}
	}

	public static String getHmacSha256(String data, byte[] keyBytes) {
		try {
			SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, HMAC_SHA256);
			Mac mac = Mac.getInstance(HMAC_SHA256);
			mac.init(secretKeySpec);
			byte[] encryptByte = mac.doFinal(data.getBytes());

			StringBuilder hs = new StringBuilder();
			String stmp;
			for (byte b : encryptByte) {
				stmp = Integer.toHexString(b & 0XFF);
				if (stmp.length() == 1) {
					hs.append('0');
				}
				hs.append(stmp);
			}
			return hs.toString().toLowerCase();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public static String md5Bytes(byte[] src) {
		try {
			return byte2hex(md5(src));
		} catch (Exception e) {
			log.error("md5Bytes failed", e);
			return "";
		}
	}

}

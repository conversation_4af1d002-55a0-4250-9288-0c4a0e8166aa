package com.dxx.game.common.channel.common.util;

import com.dxx.game.common.channel.common.model.PayBackParamsModel;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.common.utils.RandomUtil;

import java.util.Arrays;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/4/20 18:43
 */
public class PaymentUtils {

    /**
     * 创建cp订单号
     * @return
     */
    public static String createCpOrderId() {

        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        int hashCode = uuid.hashCode();
        if (hashCode < 0) {
            hashCode = -hashCode;
        }
        return DateUtils.getUnixTime() + String.format("%010d", hashCode);
    }

    /**
     * 解析透传参数
     * 自定义透传参数 根据|分割 userId|productId|extraInfo|preId
     * @param value
     * @return
     */
    public static PayBackParamsModel formatPassBackParams(String value) {
        // 透传参数
        String[] attchArray = value.split("\\|");
        PayBackParamsModel payBackParamsModel = new PayBackParamsModel();
        payBackParamsModel.setUserId(Long.parseLong(attchArray[0]));
        payBackParamsModel.setPreOrderId(Long.parseLong(attchArray[3]));
        payBackParamsModel.setProductId(attchArray[1]);
        payBackParamsModel.setExtraInfo(attchArray[2]);

        if (attchArray.length == 5) {
            payBackParamsModel.setOther(attchArray[4]);
        }

        return payBackParamsModel;
    }

    /**
     * 构造透传参数
     * userId|productId|extraInfo|preId - 透传参数
     * @param userId
     * @param productId
     * @param extraInfo
     * @param preOrderId
     * @return
     */
    public static String createPassBackParams(long userId, String productId, String extraInfo, long preOrderId) {
        return userId + "|" + productId + "|" + extraInfo + "|" + preOrderId;
    }

    public static String createPassBackParams(long userId, int purchaseId, String extraInfo, long preOrderId) {
        return userId + "|" + purchaseId + "|" + extraInfo + "|" + createOrderId();
    }

    /**
     * 获取得到排序好的查询字符串
     * @param params 请求参数
     * @return
     */
    public static  String getSortQueryString(Map<String, String> params) {

        Object[] keys = params.keySet().toArray();
        Arrays.sort(keys);
        StringBuilder sBuilder = new StringBuilder();
        for(Object key : keys){
            sBuilder.append(key).append("=").append(params.get(String.valueOf(key))).append("&");
        }

        String text = sBuilder.toString();
        if(text.endsWith("&")) {
            text = text.substring(0,text.length()-1);
        }
        return text;
    }

    public static String createOrderId() {

        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        int hashCode = uuid.hashCode();
        if (hashCode < 0) {
            hashCode = -hashCode;
        }
        return System.currentTimeMillis() + String.format("%010d", hashCode);
    }
}

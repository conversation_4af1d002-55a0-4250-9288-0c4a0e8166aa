package com.dxx.game.modules.activity.service;

import com.dxx.game.common.server.model.Result;
import com.dxx.game.dao.dynamodb.model.activity.Fishing;
import com.dxx.game.dto.FishingProto;

public interface FishingService {

    Result<FishingProto.FishingCastRodResponse> castRodAction(FishingProto.FishingCastRodRequest params);

    Result<FishingProto.FishingReelInResponse> reelIn(FishingProto.FishingReelInRequest params);

    Result<FishingProto.FishingBuyBaitResponse> buyBait(FishingProto.FishingBuyBaitRequest params);

    Result<FishingProto.FishingRebornResponse> reborn(FishingProto.FishingRebornRequest params);

    Result<FishingProto.FishingOnOpenResponse> onOpen(FishingProto.FishingOnOpenRequest params);
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


/**
 * Bean测试
 */
public final class BeanDemo {
    public BeanDemo(JsonObject _buf) { 
        theId = _buf.get("theId").getAsInt();
        theOtherValue = _buf.get("theOtherValue").getAsString();
    }

    public static BeanDemo deserialize(JsonObject _buf) {
            return new com.dxx.game.config.BeanDemo(_buf);
    }

    /**
     * ID
     */
    public final int theId;
    /**
     * item值
     */
    public final String theOtherValue;

    

    @Override
    public String toString() {
        return "{ "
        + "theId:" + theId + ","
        + "theOtherValue:" + theOtherValue + ","
        + "}";
    }
}


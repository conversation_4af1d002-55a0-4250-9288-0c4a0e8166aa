[{"ID": 1, "RewardLevel": 1, "RequiredLevel": 0, "HangGold": 1, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20001}, {"ID": 2, "RewardLevel": 2, "RequiredLevel": 1, "HangGold": 10, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20001}, {"ID": 3, "RewardLevel": 3, "RequiredLevel": 2, "HangGold": 15, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20001}, {"ID": 4, "RewardLevel": 4, "RequiredLevel": 3, "HangGold": 20, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20001}, {"ID": 5, "RewardLevel": 5, "RequiredLevel": 4, "HangGold": 25, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20001}, {"ID": 6, "RewardLevel": 6, "RequiredLevel": 5, "HangGold": 30, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20002}, {"ID": 7, "RewardLevel": 7, "RequiredLevel": 6, "HangGold": 35, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20002}, {"ID": 8, "RewardLevel": 8, "RequiredLevel": 7, "HangGold": 40, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20002}, {"ID": 9, "RewardLevel": 9, "RequiredLevel": 8, "HangGold": 45, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20002}, {"ID": 10, "RewardLevel": 10, "RequiredLevel": 9, "HangGold": 50, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20002}, {"ID": 11, "RewardLevel": 11, "RequiredLevel": 10, "HangGold": 55, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20003}, {"ID": 12, "RewardLevel": 12, "RequiredLevel": 11, "HangGold": 60, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20003}, {"ID": 13, "RewardLevel": 13, "RequiredLevel": 12, "HangGold": 65, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20003}, {"ID": 14, "RewardLevel": 14, "RequiredLevel": 13, "HangGold": 70, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20003}, {"ID": 15, "RewardLevel": 15, "RequiredLevel": 14, "HangGold": 75, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20003}, {"ID": 16, "RewardLevel": 16, "RequiredLevel": 15, "HangGold": 80, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20004}, {"ID": 17, "RewardLevel": 17, "RequiredLevel": 16, "HangGold": 85, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20004}, {"ID": 18, "RewardLevel": 18, "RequiredLevel": 17, "HangGold": 90, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20004}, {"ID": 19, "RewardLevel": 19, "RequiredLevel": 18, "HangGold": 95, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20004}, {"ID": 20, "RewardLevel": 20, "RequiredLevel": 19, "HangGold": 100, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20004}, {"ID": 21, "RewardLevel": 21, "RequiredLevel": 20, "HangGold": 105, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20005}, {"ID": 22, "RewardLevel": 22, "RequiredLevel": 21, "HangGold": 110, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20005}, {"ID": 23, "RewardLevel": 23, "RequiredLevel": 22, "HangGold": 115, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20005}, {"ID": 24, "RewardLevel": 24, "RequiredLevel": 23, "HangGold": 120, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20005}, {"ID": 25, "RewardLevel": 25, "RequiredLevel": 24, "HangGold": 125, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20005}, {"ID": 26, "RewardLevel": 26, "RequiredLevel": 25, "HangGold": 130, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20006}, {"ID": 27, "RewardLevel": 27, "RequiredLevel": 26, "HangGold": 135, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20006}, {"ID": 28, "RewardLevel": 28, "RequiredLevel": 27, "HangGold": 140, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20006}, {"ID": 29, "RewardLevel": 29, "RequiredLevel": 28, "HangGold": 145, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20006}, {"ID": 30, "RewardLevel": 30, "RequiredLevel": 29, "HangGold": 150, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20006}, {"ID": 31, "RewardLevel": 31, "RequiredLevel": 30, "HangGold": 155, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20007}, {"ID": 32, "RewardLevel": 32, "RequiredLevel": 31, "HangGold": 160, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20007}, {"ID": 33, "RewardLevel": 33, "RequiredLevel": 32, "HangGold": 165, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20007}, {"ID": 34, "RewardLevel": 34, "RequiredLevel": 33, "HangGold": 170, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20007}, {"ID": 35, "RewardLevel": 35, "RequiredLevel": 34, "HangGold": 175, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20007}, {"ID": 36, "RewardLevel": 36, "RequiredLevel": 35, "HangGold": 180, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20008}, {"ID": 37, "RewardLevel": 37, "RequiredLevel": 36, "HangGold": 185, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20008}, {"ID": 38, "RewardLevel": 38, "RequiredLevel": 37, "HangGold": 190, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20008}, {"ID": 39, "RewardLevel": 39, "RequiredLevel": 38, "HangGold": 195, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20008}, {"ID": 40, "RewardLevel": 40, "RequiredLevel": 39, "HangGold": 200, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20008}, {"ID": 41, "RewardLevel": 41, "RequiredLevel": 40, "HangGold": 205, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20009}, {"ID": 42, "RewardLevel": 42, "RequiredLevel": 41, "HangGold": 210, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20009}, {"ID": 43, "RewardLevel": 43, "RequiredLevel": 42, "HangGold": 215, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20009}, {"ID": 44, "RewardLevel": 44, "RequiredLevel": 43, "HangGold": 220, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20009}, {"ID": 45, "RewardLevel": 45, "RequiredLevel": 44, "HangGold": 225, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20009}, {"ID": 46, "RewardLevel": 46, "RequiredLevel": 45, "HangGold": 230, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20010}, {"ID": 47, "RewardLevel": 47, "RequiredLevel": 46, "HangGold": 235, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20010}, {"ID": 48, "RewardLevel": 48, "RequiredLevel": 47, "HangGold": 240, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20010}, {"ID": 49, "RewardLevel": 49, "RequiredLevel": 48, "HangGold": 245, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20010}, {"ID": 50, "RewardLevel": 50, "RequiredLevel": 49, "HangGold": 250, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20010}, {"ID": 51, "RewardLevel": 51, "RequiredLevel": 50, "HangGold": 255, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20011}, {"ID": 52, "RewardLevel": 52, "RequiredLevel": 51, "HangGold": 260, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20011}, {"ID": 53, "RewardLevel": 53, "RequiredLevel": 52, "HangGold": 265, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20011}, {"ID": 54, "RewardLevel": 54, "RequiredLevel": 53, "HangGold": 270, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20011}, {"ID": 55, "RewardLevel": 55, "RequiredLevel": 54, "HangGold": 275, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20011}, {"ID": 56, "RewardLevel": 56, "RequiredLevel": 55, "HangGold": 280, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20012}, {"ID": 57, "RewardLevel": 57, "RequiredLevel": 56, "HangGold": 285, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20012}, {"ID": 58, "RewardLevel": 58, "RequiredLevel": 57, "HangGold": 290, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20012}, {"ID": 59, "RewardLevel": 59, "RequiredLevel": 58, "HangGold": 295, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20012}, {"ID": 60, "RewardLevel": 60, "RequiredLevel": 59, "HangGold": 300, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20012}, {"ID": 61, "RewardLevel": 61, "RequiredLevel": 60, "HangGold": 305, "HangDust": 0, "HangHeroExp": 0, "HangDiamond": 0, "HangRewards": 20013}]
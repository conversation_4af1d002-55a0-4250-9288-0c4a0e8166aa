package com.dxx.game.modules.develop.handler;

import com.dxx.game.common.server.model.Result;
import org.springframework.beans.factory.annotation.Autowired;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.dto.CommonProto.CommonParams;
import com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest;
import com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse;
import com.dxx.game.dto.DevelopProto.DevelopLoginRequest;
import com.dxx.game.dto.DevelopProto.DevelopLoginResponse;
import com.dxx.game.dto.DevelopProto.DevelopToolsRequest;
import com.dxx.game.dto.DevelopProto.DevelopToolsResponse;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.modules.develop.service.DevelopService;
import com.google.protobuf.Message;

@ApiHandler
public class DevelopHandler {

	@Autowired
	private DevelopService developService;

	@ApiMethod(command = MsgReqCommand.DevelopLoginRequest, name = "测试-用户登录", skipAuth = true, skipLoginStateCheck = true, developApi = true)
	public Result<DevelopLoginResponse> login(Message msg) {
		DevelopLoginRequest params = (DevelopLoginRequest)msg;
		return developService.login(params);
	}

	@ApiMethod(command = MsgReqCommand.DevelopChangeResourceRequest, name = "测试-修改资源", developApi = true)
	public Result<DevelopChangeResourceResponse> changeResource(Message msg) {
		DevelopChangeResourceRequest params = (DevelopChangeResourceRequest)msg;
		return developService.change(params);
	}

	@ApiMethod(command = MsgReqCommand.DevelopToolsRequest, name = "测试-工具",  developApi = true)
	public Result<DevelopToolsResponse> tools(Message msg) {
		DevelopToolsRequest params = (DevelopToolsRequest)msg;
		return developService.tools(params);
	}
}



















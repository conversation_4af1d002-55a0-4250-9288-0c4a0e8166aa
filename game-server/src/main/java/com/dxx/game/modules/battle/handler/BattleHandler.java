package com.dxx.game.modules.battle.handler;

import com.dxx.game.common.server.annotation.ApiHandler;
import com.dxx.game.common.server.annotation.ApiMethod;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.server.model.Result;
import com.dxx.game.consts.MsgReqCommand;
import com.dxx.game.dao.dynamodb.repository.UserDao;
import com.dxx.game.dao.dynamodb.repository.usermodule.UserExtendDao;
import com.dxx.game.dao.redis.BattleUnitCacheRedisDao;
import com.dxx.game.dto.BattleProto;
import com.dxx.game.modules.battle.BattleService;
import com.dxx.game.modules.pvp.support.PVPHelper;
import com.dxx.game.modules.user.service.UserService;
import com.dxx.game.modules.user.support.PowerSupport;
import com.google.protobuf.Message;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ApiHandler
public class BattleHandler {
    @Resource
    private PowerSupport powerSupport;

}

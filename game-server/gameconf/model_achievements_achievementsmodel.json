[{"ID": 11001, "AchievementsType": 11, "AchievementsLevel": 1, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 2, "AchievementsDescribe": "4311", "Reward": [1, 5000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11002, "AchievementsType": 11, "AchievementsLevel": 2, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 3, "AchievementsDescribe": "4311", "Reward": [1, 12000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11003, "AchievementsType": 11, "AchievementsLevel": 3, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 4, "AchievementsDescribe": "4311", "Reward": [1, 20000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11004, "AchievementsType": 11, "AchievementsLevel": 4, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4311", "Reward": [1, 30000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11005, "AchievementsType": 11, "AchievementsLevel": 5, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 6, "AchievementsDescribe": "4311", "Reward": [1, 40000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11006, "AchievementsType": 11, "AchievementsLevel": 6, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 7, "AchievementsDescribe": "4311", "Reward": [1, 50000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11007, "AchievementsType": 11, "AchievementsLevel": 7, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 8, "AchievementsDescribe": "4311", "Reward": [1, 60000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11008, "AchievementsType": 11, "AchievementsLevel": 8, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 9, "AchievementsDescribe": "4311", "Reward": [1, 70000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11009, "AchievementsType": 11, "AchievementsLevel": 9, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4311", "Reward": [1, 80000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11010, "AchievementsType": 11, "AchievementsLevel": 10, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 11, "AchievementsDescribe": "4311", "Reward": [1, 90000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11011, "AchievementsType": 11, "AchievementsLevel": 11, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 12, "AchievementsDescribe": "4311", "Reward": [1, 100000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11012, "AchievementsType": 11, "AchievementsLevel": 12, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 13, "AchievementsDescribe": "4311", "Reward": [1, 150000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11013, "AchievementsType": 11, "AchievementsLevel": 13, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 14, "AchievementsDescribe": "4311", "Reward": [1, 200000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11014, "AchievementsType": 11, "AchievementsLevel": 14, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4311", "Reward": [1, 300000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11015, "AchievementsType": 11, "AchievementsLevel": 15, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 60, "AchievementsDescribe": "4311", "Reward": [1, 400000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11016, "AchievementsType": 11, "AchievementsLevel": 16, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 90, "AchievementsDescribe": "4311", "Reward": [1, 600000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11017, "AchievementsType": 11, "AchievementsLevel": 17, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 120, "AchievementsDescribe": "4311", "Reward": [1, 800000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11018, "AchievementsType": 11, "AchievementsLevel": 18, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 150, "AchievementsDescribe": "4311", "Reward": [1, 1000000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11019, "AchievementsType": 11, "AchievementsLevel": 19, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 180, "AchievementsDescribe": "4311", "Reward": [1, 1200000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11020, "AchievementsType": 11, "AchievementsLevel": 20, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 210, "AchievementsDescribe": "4311", "Reward": [1, 1400000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11021, "AchievementsType": 11, "AchievementsLevel": 21, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 240, "AchievementsDescribe": "4311", "Reward": [1, 1600000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11022, "AchievementsType": 11, "AchievementsLevel": 22, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 270, "AchievementsDescribe": "4311", "Reward": [1, 1800000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11023, "AchievementsType": 11, "AchievementsLevel": 23, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4311", "Reward": [1, 2000000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11024, "AchievementsType": 11, "AchievementsLevel": 24, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 330, "AchievementsDescribe": "4311", "Reward": [1, 2200000], "Jump": 1, "UnlockNeed": 3}, {"ID": 11025, "AchievementsType": 11, "AchievementsLevel": 25, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 360, "AchievementsDescribe": "4311", "Reward": [1, 2500000], "Jump": 1, "UnlockNeed": 3}, {"ID": 12001, "AchievementsType": 12, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4312", "Reward": [2, 20], "Jump": 12, "UnlockNeed": 2}, {"ID": 12002, "AchievementsType": 12, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4312", "Reward": [2, 20], "Jump": 12, "UnlockNeed": 2}, {"ID": 12003, "AchievementsType": 12, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 15, "AchievementsDescribe": "4312", "Reward": [2, 20], "Jump": 12, "UnlockNeed": 2}, {"ID": 12004, "AchievementsType": 12, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4312", "Reward": [2, 20], "Jump": 12, "UnlockNeed": 2}, {"ID": 12005, "AchievementsType": 12, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 25, "AchievementsDescribe": "4312", "Reward": [2, 20], "Jump": 12, "UnlockNeed": 2}, {"ID": 12006, "AchievementsType": 12, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4312", "Reward": [2, 20], "Jump": 12, "UnlockNeed": 2}, {"ID": 1001, "AchievementsType": 1, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1002, "AchievementsType": 1, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1003, "AchievementsType": 1, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 15, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1004, "AchievementsType": 1, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1005, "AchievementsType": 1, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 25, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1006, "AchievementsType": 1, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1007, "AchievementsType": 1, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 35, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1008, "AchievementsType": 1, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 40, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1009, "AchievementsType": 1, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 45, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 1010, "AchievementsType": 1, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4301", "Reward": [2, 50], "Jump": 1, "UnlockNeed": 3}, {"ID": 8001, "AchievementsType": 8, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 2, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8002, "AchievementsType": 8, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 4, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8003, "AchievementsType": 8, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 6, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8004, "AchievementsType": 8, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 8, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8005, "AchievementsType": 8, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 10, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8006, "AchievementsType": 8, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 15, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8007, "AchievementsType": 8, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 20, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8008, "AchievementsType": 8, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 25, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8009, "AchievementsType": 8, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 30, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8010, "AchievementsType": 8, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 35, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8011, "AchievementsType": 8, "AchievementsLevel": 11, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 40, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8012, "AchievementsType": 8, "AchievementsLevel": 12, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 45, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8013, "AchievementsType": 8, "AchievementsLevel": 13, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 50, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8014, "AchievementsType": 8, "AchievementsLevel": 14, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 55, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8015, "AchievementsType": 8, "AchievementsLevel": 15, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 60, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8016, "AchievementsType": 8, "AchievementsLevel": 16, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 65, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8017, "AchievementsType": 8, "AchievementsLevel": 17, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 70, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8018, "AchievementsType": 8, "AchievementsLevel": 18, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 80, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8019, "AchievementsType": 8, "AchievementsLevel": 19, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 90, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 8020, "AchievementsType": 8, "AchievementsLevel": 20, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 100, "AchievementsDescribe": "4308", "Reward": [1006, 5], "Jump": 5, "UnlockNeed": 15}, {"ID": 9001, "AchievementsType": 9, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 2, "AchievementsDescribe": "4309", "Reward": [2, 100], "Jump": 6, "UnlockNeed": 16}, {"ID": 9002, "AchievementsType": 9, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 3, "AchievementsDescribe": "4309", "Reward": [2, 150], "Jump": 6, "UnlockNeed": 16}, {"ID": 9003, "AchievementsType": 9, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 4, "AchievementsDescribe": "4309", "Reward": [2, 200], "Jump": 6, "UnlockNeed": 16}, {"ID": 9004, "AchievementsType": 9, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 5, "AchievementsDescribe": "4309", "Reward": [2, 250], "Jump": 6, "UnlockNeed": 16}, {"ID": 9005, "AchievementsType": 9, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 0, "AchievementsNeed": 6, "AchievementsDescribe": "4309", "Reward": [2, 300], "Jump": 6, "UnlockNeed": 16}, {"ID": 13001, "AchievementsType": 13, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 10000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13002, "AchievementsType": 13, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 20000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13003, "AchievementsType": 13, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 40000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13004, "AchievementsType": 13, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 80000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13005, "AchievementsType": 13, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 150000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13006, "AchievementsType": 13, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 300000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13007, "AchievementsType": 13, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 600000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13008, "AchievementsType": 13, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1200000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13009, "AchievementsType": 13, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 2500000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 13010, "AchievementsType": 13, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 5000000, "AchievementsDescribe": "4313", "Reward": [2, 100], "Jump": 2, "UnlockNeed": 5}, {"ID": 14001, "AchievementsType": 14, "AchievementsLevel": 1, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14002, "AchievementsType": 14, "AchievementsLevel": 2, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 3, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14003, "AchievementsType": 14, "AchievementsLevel": 3, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14004, "AchievementsType": 14, "AchievementsLevel": 4, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14005, "AchievementsType": 14, "AchievementsLevel": 5, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14006, "AchievementsType": 14, "AchievementsLevel": 6, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14007, "AchievementsType": 14, "AchievementsLevel": 7, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14008, "AchievementsType": 14, "AchievementsLevel": 8, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14009, "AchievementsType": 14, "AchievementsLevel": 9, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 14010, "AchievementsType": 14, "AchievementsLevel": 10, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4314", "Reward": [1016, 5], "Jump": 11, "UnlockNeed": 17}, {"ID": 15001, "AchievementsType": 15, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4315", "Reward": [2010, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15002, "AchievementsType": 15, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4315", "Reward": [2010, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15003, "AchievementsType": 15, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4315", "Reward": [2010, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15004, "AchievementsType": 15, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4315", "Reward": [2010, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15005, "AchievementsType": 15, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 400, "AchievementsDescribe": "4315", "Reward": [2010, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15006, "AchievementsType": 15, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 500, "AchievementsDescribe": "4315", "Reward": [2011, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15007, "AchievementsType": 15, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 600, "AchievementsDescribe": "4315", "Reward": [2011, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15008, "AchievementsType": 15, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 700, "AchievementsDescribe": "4315", "Reward": [2011, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15009, "AchievementsType": 15, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 800, "AchievementsDescribe": "4315", "Reward": [2011, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15010, "AchievementsType": 15, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1000, "AchievementsDescribe": "4315", "Reward": [2011, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15011, "AchievementsType": 15, "AchievementsLevel": 11, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1200, "AchievementsDescribe": "4315", "Reward": [2044, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15012, "AchievementsType": 15, "AchievementsLevel": 12, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1400, "AchievementsDescribe": "4315", "Reward": [2044, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15013, "AchievementsType": 15, "AchievementsLevel": 13, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1600, "AchievementsDescribe": "4315", "Reward": [2044, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15014, "AchievementsType": 15, "AchievementsLevel": 14, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1800, "AchievementsDescribe": "4315", "Reward": [2044, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 15015, "AchievementsType": 15, "AchievementsLevel": 15, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 2000, "AchievementsDescribe": "4315", "Reward": [2044, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16001, "AchievementsType": 16, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16002, "AchievementsType": 16, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16003, "AchievementsType": 16, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16004, "AchievementsType": 16, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 40, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16005, "AchievementsType": 16, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16006, "AchievementsType": 16, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16007, "AchievementsType": 16, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 150, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16008, "AchievementsType": 16, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16009, "AchievementsType": 16, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 250, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 16010, "AchievementsType": 16, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4316", "Reward": [1004, 1], "Jump": 2, "UnlockNeed": 5}, {"ID": 17001, "AchievementsType": 17, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4317", "Reward": [400, 50], "Jump": 2, "UnlockNeed": 73}, {"ID": 17002, "AchievementsType": 17, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 400, "AchievementsDescribe": "4317", "Reward": [400, 50], "Jump": 2, "UnlockNeed": 73}, {"ID": 17003, "AchievementsType": 17, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 800, "AchievementsDescribe": "4317", "Reward": [400, 50], "Jump": 2, "UnlockNeed": 73}, {"ID": 17004, "AchievementsType": 17, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1200, "AchievementsDescribe": "4317", "Reward": [400, 50], "Jump": 2, "UnlockNeed": 73}, {"ID": 17005, "AchievementsType": 17, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1600, "AchievementsDescribe": "4317", "Reward": [400, 50], "Jump": 2, "UnlockNeed": 73}, {"ID": 17006, "AchievementsType": 17, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 2000, "AchievementsDescribe": "4317", "Reward": [400, 100], "Jump": 2, "UnlockNeed": 73}, {"ID": 17007, "AchievementsType": 17, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 2400, "AchievementsDescribe": "4317", "Reward": [400, 100], "Jump": 2, "UnlockNeed": 73}, {"ID": 17008, "AchievementsType": 17, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 2800, "AchievementsDescribe": "4317", "Reward": [400, 100], "Jump": 2, "UnlockNeed": 73}, {"ID": 17009, "AchievementsType": 17, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 3200, "AchievementsDescribe": "4317", "Reward": [400, 100], "Jump": 2, "UnlockNeed": 73}, {"ID": 17010, "AchievementsType": 17, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 4000, "AchievementsDescribe": "4317", "Reward": [400, 200], "Jump": 2, "UnlockNeed": 73}, {"ID": 17011, "AchievementsType": 17, "AchievementsLevel": 11, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 4800, "AchievementsDescribe": "4317", "Reward": [400, 200], "Jump": 2, "UnlockNeed": 73}, {"ID": 17012, "AchievementsType": 17, "AchievementsLevel": 12, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 5600, "AchievementsDescribe": "4317", "Reward": [400, 200], "Jump": 2, "UnlockNeed": 73}, {"ID": 17013, "AchievementsType": 17, "AchievementsLevel": 13, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 6400, "AchievementsDescribe": "4317", "Reward": [400, 200], "Jump": 2, "UnlockNeed": 73}, {"ID": 17014, "AchievementsType": 17, "AchievementsLevel": 14, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 7200, "AchievementsDescribe": "4317", "Reward": [400, 200], "Jump": 2, "UnlockNeed": 73}, {"ID": 17015, "AchievementsType": 17, "AchievementsLevel": 15, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 8000, "AchievementsDescribe": "4317", "Reward": [400, 500], "Jump": 2, "UnlockNeed": 73}, {"ID": 18001, "AchievementsType": 18, "AchievementsLevel": 1, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18002, "AchievementsType": 18, "AchievementsLevel": 2, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18003, "AchievementsType": 18, "AchievementsLevel": 3, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 150, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18004, "AchievementsType": 18, "AchievementsLevel": 4, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18005, "AchievementsType": 18, "AchievementsLevel": 5, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18006, "AchievementsType": 18, "AchievementsLevel": 6, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 500, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18007, "AchievementsType": 18, "AchievementsLevel": 7, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1000, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18008, "AchievementsType": 18, "AchievementsLevel": 8, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1500, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18009, "AchievementsType": 18, "AchievementsLevel": 9, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 2000, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18010, "AchievementsType": 18, "AchievementsLevel": 10, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 2500, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18011, "AchievementsType": 18, "AchievementsLevel": 11, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 3000, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18012, "AchievementsType": 18, "AchievementsLevel": 12, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 3500, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18013, "AchievementsType": 18, "AchievementsLevel": 13, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 4000, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18014, "AchievementsType": 18, "AchievementsLevel": 14, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 4500, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 18015, "AchievementsType": 18, "AchievementsLevel": 15, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 5000, "AchievementsDescribe": "4318", "Reward": [1007, 1], "Jump": 12, "UnlockNeed": 2}, {"ID": 19001, "AchievementsType": 19, "AchievementsLevel": 1, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19002, "AchievementsType": 19, "AchievementsLevel": 2, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19003, "AchievementsType": 19, "AchievementsLevel": 3, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 60, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19004, "AchievementsType": 19, "AchievementsLevel": 4, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19005, "AchievementsType": 19, "AchievementsLevel": 5, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19006, "AchievementsType": 19, "AchievementsLevel": 6, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 500, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19007, "AchievementsType": 19, "AchievementsLevel": 7, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1000, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19008, "AchievementsType": 19, "AchievementsLevel": 8, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1500, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19009, "AchievementsType": 19, "AchievementsLevel": 9, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 2000, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19010, "AchievementsType": 19, "AchievementsLevel": 10, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 2500, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19011, "AchievementsType": 19, "AchievementsLevel": 11, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 3000, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19012, "AchievementsType": 19, "AchievementsLevel": 12, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 3500, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19013, "AchievementsType": 19, "AchievementsLevel": 13, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 4000, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19014, "AchievementsType": 19, "AchievementsLevel": 14, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 4500, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 19015, "AchievementsType": 19, "AchievementsLevel": 15, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 5000, "AchievementsDescribe": "4319", "Reward": [1024, 1], "Jump": 8, "UnlockNeed": 51}, {"ID": 10001, "AchievementsType": 10, "AchievementsLevel": 1, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10002, "AchievementsType": 10, "AchievementsLevel": 2, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10003, "AchievementsType": 10, "AchievementsLevel": 3, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10004, "AchievementsType": 10, "AchievementsLevel": 4, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10005, "AchievementsType": 10, "AchievementsLevel": 5, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10006, "AchievementsType": 10, "AchievementsLevel": 6, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10007, "AchievementsType": 10, "AchievementsLevel": 7, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 150, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10008, "AchievementsType": 10, "AchievementsLevel": 8, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10009, "AchievementsType": 10, "AchievementsLevel": 9, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 250, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10010, "AchievementsType": 10, "AchievementsLevel": 10, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10011, "AchievementsType": 10, "AchievementsLevel": 11, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 350, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10012, "AchievementsType": 10, "AchievementsLevel": 12, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 400, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10013, "AchievementsType": 10, "AchievementsLevel": 13, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 450, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10014, "AchievementsType": 10, "AchievementsLevel": 14, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 500, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 10015, "AchievementsType": 10, "AchievementsLevel": 15, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 600, "AchievementsDescribe": "4310", "Reward": [2, 20], "Jump": 9, "UnlockNeed": 53}, {"ID": 20001, "AchievementsType": 20, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 1, "AchievementsDescribe": "4320", "Reward": [405, 5], "Jump": 13, "UnlockNeed": 17}, {"ID": 20002, "AchievementsType": 20, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 3, "AchievementsDescribe": "4320", "Reward": [405, 5], "Jump": 13, "UnlockNeed": 17}, {"ID": 20003, "AchievementsType": 20, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4320", "Reward": [405, 5], "Jump": 13, "UnlockNeed": 17}, {"ID": 20004, "AchievementsType": 20, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4320", "Reward": [405, 10], "Jump": 13, "UnlockNeed": 17}, {"ID": 20005, "AchievementsType": 20, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 15, "AchievementsDescribe": "4320", "Reward": [405, 10], "Jump": 13, "UnlockNeed": 17}, {"ID": 20006, "AchievementsType": 20, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4320", "Reward": [405, 10], "Jump": 13, "UnlockNeed": 17}, {"ID": 20007, "AchievementsType": 20, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 25, "AchievementsDescribe": "4320", "Reward": [405, 15], "Jump": 13, "UnlockNeed": 17}, {"ID": 20008, "AchievementsType": 20, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 30, "AchievementsDescribe": "4320", "Reward": [405, 15], "Jump": 13, "UnlockNeed": 17}, {"ID": 20009, "AchievementsType": 20, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 35, "AchievementsDescribe": "4320", "Reward": [405, 15], "Jump": 13, "UnlockNeed": 17}, {"ID": 20010, "AchievementsType": 20, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 40, "AchievementsDescribe": "4320", "Reward": [405, 20], "Jump": 13, "UnlockNeed": 17}, {"ID": 21001, "AchievementsType": 21, "AchievementsLevel": 1, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4321", "Reward": [2, 50], "Jump": 13, "UnlockNeed": 17}, {"ID": 21002, "AchievementsType": 21, "AchievementsLevel": 2, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4321", "Reward": [2, 50], "Jump": 13, "UnlockNeed": 17}, {"ID": 21003, "AchievementsType": 21, "AchievementsLevel": 3, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4321", "Reward": [2, 50], "Jump": 13, "UnlockNeed": 17}, {"ID": 21004, "AchievementsType": 21, "AchievementsLevel": 4, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4321", "Reward": [2, 50], "Jump": 13, "UnlockNeed": 17}, {"ID": 21005, "AchievementsType": 21, "AchievementsLevel": 5, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 150, "AchievementsDescribe": "4321", "Reward": [2, 100], "Jump": 13, "UnlockNeed": 17}, {"ID": 21006, "AchievementsType": 21, "AchievementsLevel": 6, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4321", "Reward": [2, 100], "Jump": 13, "UnlockNeed": 17}, {"ID": 21007, "AchievementsType": 21, "AchievementsLevel": 7, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 250, "AchievementsDescribe": "4321", "Reward": [2, 100], "Jump": 13, "UnlockNeed": 17}, {"ID": 21008, "AchievementsType": 21, "AchievementsLevel": 8, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4321", "Reward": [2, 100], "Jump": 13, "UnlockNeed": 17}, {"ID": 21009, "AchievementsType": 21, "AchievementsLevel": 9, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 350, "AchievementsDescribe": "4321", "Reward": [2, 100], "Jump": 13, "UnlockNeed": 17}, {"ID": 21010, "AchievementsType": 21, "AchievementsLevel": 10, "AccumulationType": 0, "ProgressType": 1, "AchievementsNeed": 400, "AchievementsDescribe": "4321", "Reward": [2, 100], "Jump": 13, "UnlockNeed": 17}, {"ID": 22001, "AchievementsType": 22, "AchievementsLevel": 1, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 1, "AchievementsDescribe": "4322", "Reward": [405, 10], "Jump": 14, "UnlockNeed": 70}, {"ID": 22002, "AchievementsType": 22, "AchievementsLevel": 2, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 5, "AchievementsDescribe": "4322", "Reward": [405, 10], "Jump": 14, "UnlockNeed": 70}, {"ID": 22003, "AchievementsType": 22, "AchievementsLevel": 3, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 10, "AchievementsDescribe": "4322", "Reward": [405, 10], "Jump": 14, "UnlockNeed": 70}, {"ID": 22004, "AchievementsType": 22, "AchievementsLevel": 4, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 20, "AchievementsDescribe": "4322", "Reward": [405, 15], "Jump": 14, "UnlockNeed": 70}, {"ID": 22005, "AchievementsType": 22, "AchievementsLevel": 5, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 50, "AchievementsDescribe": "4322", "Reward": [405, 15], "Jump": 14, "UnlockNeed": 70}, {"ID": 22006, "AchievementsType": 22, "AchievementsLevel": 6, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 100, "AchievementsDescribe": "4322", "Reward": [405, 15], "Jump": 14, "UnlockNeed": 70}, {"ID": 22007, "AchievementsType": 22, "AchievementsLevel": 7, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 150, "AchievementsDescribe": "4322", "Reward": [405, 20], "Jump": 14, "UnlockNeed": 70}, {"ID": 22008, "AchievementsType": 22, "AchievementsLevel": 8, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 200, "AchievementsDescribe": "4322", "Reward": [405, 20], "Jump": 14, "UnlockNeed": 70}, {"ID": 22009, "AchievementsType": 22, "AchievementsLevel": 9, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 250, "AchievementsDescribe": "4322", "Reward": [405, 20], "Jump": 14, "UnlockNeed": 70}, {"ID": 22010, "AchievementsType": 22, "AchievementsLevel": 10, "AccumulationType": 1, "ProgressType": 1, "AchievementsNeed": 300, "AchievementsDescribe": "4322", "Reward": [405, 30], "Jump": 14, "UnlockNeed": 70}]
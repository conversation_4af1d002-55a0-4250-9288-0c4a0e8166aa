package com.dxx.game.common.redis.annotation;

import com.dxx.game.common.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/11/3 10:14
 */
@Aspect
@Slf4j
@Component
public class ScheduledLockAspect {

    private static final int PROTECT_TIME = 200;

    @Resource
    private RedisLock redisLock;

    // 使用 ThreadLocal 存储当前线程获取的锁信息
//    private ThreadLocal<String> lockKeyThreadLocal = new ThreadLocal<>();
//    private ThreadLocal<String> lockValueThreadLocal = new ThreadLocal<>();


    @Pointcut(value = "@annotation(com.dxx.game.common.redis.annotation.ScheduledLock)")
    public void pointCut() {}

    @Around(value = "pointCut()")
    public void lockAroundAction(ProceedingJoinPoint joinPoint) throws Exception {
        Method method = joinPoint.getTarget().getClass().getDeclaredMethod(joinPoint.getSignature().getName());
        ScheduledLock scheduledLock = method.getAnnotation(ScheduledLock.class);
        String lockKey = scheduledLock.lockKey();
        String lockValue = scheduledLock.lockValue();
        long expireMillisSeconds = scheduledLock.expireSeconds() * 1000;

        boolean flag = redisLock.lockWithOutRetry(lockKey, lockValue, expireMillisSeconds);
        if (flag) {
//            lockKeyThreadLocal.set(lockKey);
//            lockValueThreadLocal.set(lockValue);
            try {
                joinPoint.proceed();
                // 避免执行过快， 增加保护时间
//                Thread.sleep(PROTECT_TIME);
            } catch (Throwable throwable) {
                log.error("lockAroundAction failed, errMsg:{}", throwable.getMessage());
            } finally {
//                releaseLock();
            }
        }
    }

    @PreDestroy
    public void destroy() {
//        releaseLock();
    }

    // 释放当前线程持有的锁
    private void releaseLock() {
//
//        String lockKey = lockKeyThreadLocal.get();
//        String lockValue = lockValueThreadLocal.get();
//        if (lockKey != null && lockValue != null) {
//            redisLock.unlock(lockKey, lockValue);
//            lockKeyThreadLocal.remove();
//            lockValueThreadLocal.remove();
//        }
    }
}

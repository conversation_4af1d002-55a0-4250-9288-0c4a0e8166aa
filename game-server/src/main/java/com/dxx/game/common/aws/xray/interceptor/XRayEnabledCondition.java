package com.dxx.game.common.aws.xray.interceptor;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class XRayEnabledCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        // 获取环境变量 AWS_XRAY_ENABLED
        String xrayEnabled = context.getEnvironment().getProperty("AWS_XRAY_ENABLED");

        // 如果环境变量不存在或值为 true，则返回 true
        return xrayEnabled == null || Boolean.parseBoolean(xrayEnabled);
    }
}
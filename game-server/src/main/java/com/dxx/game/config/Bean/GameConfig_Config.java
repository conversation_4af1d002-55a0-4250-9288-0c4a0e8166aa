
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class GameConfig_Config {
    public GameConfig_Config(JsonObject _buf) { 
        ID = _buf.get("ID").getAsInt();
        Value = _buf.get("Value").getAsString();
    }

    public static GameConfig_Config deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.GameConfig_Config(_buf);
    }

    /**
     * ID
     */
    public final int ID;
    /**
     * 值
     */
    public final String Value;

    

    @Override
    public String toString() {
        return "{ "
        + "ID:" + ID + ","
        + "Value:" + Value + ","
        + "}";
    }
}



//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package com.dxx.game.config.Bean;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


public final class IAP_LevelFundReward {
    public IAP_LevelFundReward(JsonObject _buf) { 
        id = _buf.get("id").getAsInt();
        groupId = _buf.get("groupId").getAsInt();
        param = _buf.get("param").getAsString();
        { com.google.gson.JsonArray _json0_ = _buf.get("fundReward").getAsJsonArray(); fundReward = new java.util.ArrayList<String>(_json0_.size()); for(JsonElement _e0 : _json0_) { String _v0;  _v0 = _e0.getAsString();  fundReward.add(_v0); }   }
    }

    public static IAP_LevelFundReward deserialize(JsonObject _buf) {
            return new com.dxx.game.config.Bean.IAP_LevelFundReward(_buf);
    }

    /**
     * id
     */
    public final int id;
    /**
     * 基金奖励组id
     */
    public final int groupId;
    /**
     * 解锁参数
     */
    public final String param;
    /**
     * 基金奖励
     */
    public final java.util.List<String> fundReward;

    

    @Override
    public String toString() {
        return "{ "
        + "id:" + id + ","
        + "groupId:" + groupId + ","
        + "param:" + param + ","
        + "fundReward:" + fundReward + ","
        + "}";
    }
}


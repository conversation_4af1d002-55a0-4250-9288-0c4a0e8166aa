package com.dxx.game.modules.im;

import com.amazonaws.xray.spring.aop.XRayEnabled;
import com.dxx.game.dto.IMProto.MessageType;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lsc
 * @createDate: 2025/4/9
 * @description:
 */
@XRayEnabled
@Slf4j
public class IMGroupIdGenerator {

    public static final int CROSS_SERVER_GROUP_NUM = 10;       // 跨服分组数

    public enum GroupType {
        SERVER, GUILD, CROSS_SERVER, PRIVATE, GLOBAL;

        public MessageType getMessageType() {
            MessageType messageType = null;
            switch (this) {
                case GUILD:
                    messageType = MessageType.CHAT_GUILD;
                    break;
                case PRIVATE:
                    messageType = MessageType.CHAT_PRIVATE;
                    break;
                case CROSS_SERVER:
                    messageType = MessageType.CHAT_CROSS_SERVER;
                    break;
                case SERVER:
                    messageType = MessageType.CHAT_WORLD_SERVER;
                    break;
                case GLOBAL:
                    messageType = MessageType.CHAT_GLOBAL;
                    break;
            }
            return messageType;
        }
    }



    public static String generateGroupId(IMGroupIdGenerator.GroupType groupType, long id) {
        String prefix;

        switch (groupType) {
            case SERVER:
                prefix = "SERVER-";
                break;
            case GUILD:
                prefix = "GUILD-";
                break;
            case CROSS_SERVER:
                prefix = "CROSS_SERVER-";
                break;
            case PRIVATE:
                prefix = "PRIVATE-";
                break;
            case GLOBAL:
                prefix = "GLOBAL-";
                break;
            default:
                throw new IllegalArgumentException("Unknown group type: " + groupType);
        }

        return prefix + id;
    }

    // Parse the GroupID and return its type
    public static IMGroupIdGenerator.GroupType getGroupType(String groupId) {
        if (groupId.startsWith("SERVER-")) {
            return IMGroupIdGenerator.GroupType.SERVER;
        } else if (groupId.startsWith("GUILD-")) {
            return IMGroupIdGenerator.GroupType.GUILD;
        } else if (groupId.startsWith("CROSS_SERVER-")) {
            return IMGroupIdGenerator.GroupType.CROSS_SERVER;
        } else if (groupId.startsWith("PRIVATE-")) {
            return IMGroupIdGenerator.GroupType.PRIVATE;
        } else if (groupId.startsWith("GLOBAL-")) {
            return IMGroupIdGenerator.GroupType.GLOBAL;
        } else {
            throw new IllegalArgumentException("Unknown Group ID prefix: " + groupId);
        }
    }

    public static long parseIdFromGroupId(String groupId) {
        String[] parts = groupId.split("-");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid Group ID format: " + groupId);
        }

        try {
            return Long.parseLong(parts[parts.length - 1]);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid Server/Guild ID in Group ID: " + groupId, e);
        }
    }


    public static long getCrossServerId(long serverId) {
        return (int) Math.ceil((double) serverId / CROSS_SERVER_GROUP_NUM);
    }
}

package com.dxx.game.modules.common.service;

import java.util.List;
import java.util.Map;

public interface CommonService {
	
	/**
	 * redis设置自增ID起始值
	 * @param key
	 * @param value
	 * @return
	 */
	boolean setGenerateIdStarter(String key, long value);
	/**
	 * redis获取自增ID值
	 * @param key
	 * @return
	 */
	long generateId(String key);

	/**
	 * redis自增ID - 根据UserID 分片
	 * @return
	 */
	long generateId(long userId);

	/**
	 * 自增用户操作ID
	 * @param userId
	 * @return
	 */
	long generateUserTransId(long userId);

	/**
	 * 生成userId
	 * @return
	 */
	long generateUserId();

	/**
	 * 获取GameConfig 配置表的值
	 */
	int getGameConfigIntValue(int id);

	/**
	 * GameConfig配置表的值
	 */
	String getGameConfigValue(int id);

	List<Integer> getGameConfigValue(int id, String split);

    List<List<Integer>> getGameConfigValueSplitOneArr(int id, String split);

    Map<Integer, Integer> getGameConfigValueSplitOneMap(int id, String split);
}
